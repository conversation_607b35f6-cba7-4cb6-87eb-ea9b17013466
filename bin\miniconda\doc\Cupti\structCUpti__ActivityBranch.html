<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityBranch Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityBranch Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityBranch" -->The activity record for source level result branch. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html#a48e08fd6eff88ab2eb3b15a70005951">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html#6b1f5438cb8a3eebbc10cc4a74fa9292">diverged</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html#ee5c8a83a5913a4205adecb02758433f">executed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html#ce347e7bdb26e1777e6cb9b5cabdeb49">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html#652f7b116d8738a0f72e9daed29aef95">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html#6c15eb8df540cb08dee0a3b044b58a97">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html#af46d0ef2afa4ec8903314ec095ce2b2">threadsExecuted</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record the locations of the branches in the source (CUPTI_ACTIVITY_KIND_BRANCH). Branch activities are now reported using the <a class="el" href="structCUpti__ActivityBranch2.html" title="The activity record for source level result branch.">CUpti_ActivityBranch2</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="a48e08fd6eff88ab2eb3b15a70005951"></a><!-- doxytag: member="CUpti_ActivityBranch::correlationId" ref="a48e08fd6eff88ab2eb3b15a70005951" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch.html#a48e08fd6eff88ab2eb3b15a70005951">CUpti_ActivityBranch::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="6b1f5438cb8a3eebbc10cc4a74fa9292"></a><!-- doxytag: member="CUpti_ActivityBranch::diverged" ref="6b1f5438cb8a3eebbc10cc4a74fa9292" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch.html#6b1f5438cb8a3eebbc10cc4a74fa9292">CUpti_ActivityBranch::diverged</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of times this branch diverged 
</div>
</div><p>
<a class="anchor" name="ee5c8a83a5913a4205adecb02758433f"></a><!-- doxytag: member="CUpti_ActivityBranch::executed" ref="ee5c8a83a5913a4205adecb02758433f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch.html#ee5c8a83a5913a4205adecb02758433f">CUpti_ActivityBranch::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of times this instruction was executed per warp. It will be incremented regardless of predicate or condition code. 
</div>
</div><p>
<a class="anchor" name="ce347e7bdb26e1777e6cb9b5cabdeb49"></a><!-- doxytag: member="CUpti_ActivityBranch::kind" ref="ce347e7bdb26e1777e6cb9b5cabdeb49" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityBranch.html#ce347e7bdb26e1777e6cb9b5cabdeb49">CUpti_ActivityBranch::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_BRANCH. 
</div>
</div><p>
<a class="anchor" name="652f7b116d8738a0f72e9daed29aef95"></a><!-- doxytag: member="CUpti_ActivityBranch::pcOffset" ref="652f7b116d8738a0f72e9daed29aef95" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch.html#652f7b116d8738a0f72e9daed29aef95">CUpti_ActivityBranch::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the branch. 
</div>
</div><p>
<a class="anchor" name="6c15eb8df540cb08dee0a3b044b58a97"></a><!-- doxytag: member="CUpti_ActivityBranch::sourceLocatorId" ref="6c15eb8df540cb08dee0a3b044b58a97" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch.html#6c15eb8df540cb08dee0a3b044b58a97">CUpti_ActivityBranch::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="af46d0ef2afa4ec8903314ec095ce2b2"></a><!-- doxytag: member="CUpti_ActivityBranch::threadsExecuted" ref="af46d0ef2afa4ec8903314ec095ce2b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityBranch.html#af46d0ef2afa4ec8903314ec095ce2b2">CUpti_ActivityBranch::threadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
