<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityFunction Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityFunction Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityFunction" -->The activity record for global/device functions.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityFunction.html#7b3ccbadecf78f48a31e43adbbfc40a8">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityFunction.html#60f1d14ce36ad6cce3e2933c90a9ae72">functionIndex</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityFunction.html#0289e0d21e2294f7fe52a67c6ec4c8a4">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityFunction.html#45a77b1e019306bcbc46e87afd87ee20">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityFunction.html#c710d09404423ecac58ced21e0c51479">moduleId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityFunction.html#39f0ffa701450d3efd386c78d58edf10">name</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records function name and corresponding module information. (CUPTI_ACTIVITY_KIND_FUNCTION). <hr><h2>Field Documentation</h2>
<a class="anchor" name="7b3ccbadecf78f48a31e43adbbfc40a8"></a><!-- doxytag: member="CUpti_ActivityFunction::contextId" ref="7b3ccbadecf78f48a31e43adbbfc40a8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityFunction.html#7b3ccbadecf78f48a31e43adbbfc40a8">CUpti_ActivityFunction::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the function is launched. 
</div>
</div><p>
<a class="anchor" name="60f1d14ce36ad6cce3e2933c90a9ae72"></a><!-- doxytag: member="CUpti_ActivityFunction::functionIndex" ref="60f1d14ce36ad6cce3e2933c90a9ae72" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityFunction.html#60f1d14ce36ad6cce3e2933c90a9ae72">CUpti_ActivityFunction::functionIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The function's unique symbol index in the module. 
</div>
</div><p>
<a class="anchor" name="0289e0d21e2294f7fe52a67c6ec4c8a4"></a><!-- doxytag: member="CUpti_ActivityFunction::id" ref="0289e0d21e2294f7fe52a67c6ec4c8a4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityFunction.html#0289e0d21e2294f7fe52a67c6ec4c8a4">CUpti_ActivityFunction::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ID to uniquely identify the record 
</div>
</div><p>
<a class="anchor" name="45a77b1e019306bcbc46e87afd87ee20"></a><!-- doxytag: member="CUpti_ActivityFunction::kind" ref="45a77b1e019306bcbc46e87afd87ee20" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityFunction.html#45a77b1e019306bcbc46e87afd87ee20">CUpti_ActivityFunction::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_FUNCTION. 
</div>
</div><p>
<a class="anchor" name="c710d09404423ecac58ced21e0c51479"></a><!-- doxytag: member="CUpti_ActivityFunction::moduleId" ref="c710d09404423ecac58ced21e0c51479" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityFunction.html#c710d09404423ecac58ced21e0c51479">CUpti_ActivityFunction::moduleId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The module ID in which this global/device function is present. 
</div>
</div><p>
<a class="anchor" name="39f0ffa701450d3efd386c78d58edf10"></a><!-- doxytag: member="CUpti_ActivityFunction::name" ref="39f0ffa701450d3efd386c78d58edf10" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityFunction.html#39f0ffa701450d3efd386c78d58edf10">CUpti_ActivityFunction::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the function. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
