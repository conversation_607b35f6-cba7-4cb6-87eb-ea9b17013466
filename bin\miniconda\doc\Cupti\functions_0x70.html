<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li class="current"><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_p">- p -</a></h3><ul>
<li>pAccessPolicyWindow
: <a class="el" href="structCUpti__ActivityKernel6.html#ff1fdd768e3fb91fff3badefe7610190">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#baeaa887ff076807205823fe4e87c6b4">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#6e1f1e578bb63d068c7ba37641c08ed5">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#342ccf5385daa6c45083cc1e02de6393">CUpti_ActivityKernel8</a>
<li>pad
: <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#7be4f235246eebb9adb5ba59d40fda00">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#c410295b3b8cf87493369e321c9fd72a">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#2db3f914716449a1b321511903e519cc">CUpti_ActivityInstantaneousMetric</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#6de13d072f5acb585184a3d1fea1ab97">CUpti_ActivityInstantaneousMetricInstance</a>
, <a class="el" href="structCUpti__ActivityBranch2.html#e55573635b20e8dd1b601b8be09fd045">CUpti_ActivityBranch2</a>
, <a class="el" href="structCUpti__PCSamplingPCData.html#41663811d199caf6f52cc9b91092651b">CUpti_PCSamplingPCData</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#866a54d44c9c5fb25df9da5e51052843">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#24798611cc00faa4402b71e7d1da0c47">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#3de2e15af1811460186e73e321096ec7">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#847820efa98a41794b7c8e14eea7f47a">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#377cc6eb746e2ea942de3a07cddaacf2">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#01abbe9d8286128d05007fee7868bc33">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityMarker2.html#5e09a5bb7d2c0c62557ce7d56deddd09">CUpti_ActivityMarker2</a>
, <a class="el" href="structCUpti__ActivityKernel.html#3619848c88e34930c0c92c1bbb2109aa">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#2954814304a5a6cf66eb0684d5d8f0b2">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#346aff93788fecb31aad5d197c23e1ee">CUpti_ActivityUnifiedMemoryCounter</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#27c8d2dec0c353073d65b938c73c6875">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityPreemption.html#10ad616ac9340294182ea6d6f72ed1a7">CUpti_ActivityPreemption</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#771450d4e5cf20bffb0c02f7aa67eef1">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivityModule.html#a5b435c84b08be50d7fe5edf4b321cf0">CUpti_ActivityModule</a>
, <a class="el" href="structCUpti__ActivityEventInstance.html#3fd2aaa5987d29ebfeb9729bfaadd70c">CUpti_ActivityEventInstance</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#2581ac8c7f8d2d97f64eace29f888662">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__ActivityCudaEvent.html#e36eca2cf386fc46eddf320fe2e93d8b">CUpti_ActivityCudaEvent</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#52190fa815bdc469582feb58d5f2fa01">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMetric.html#71961e01fc1fae63a1be56139994926f">CUpti_ActivityMetric</a>
, <a class="el" href="structCUpti__ActivityInstructionCorrelation.html#ab1f17b3c08d181f5ad0d731dd5c8fc6">CUpti_ActivityInstructionCorrelation</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#15fd490db7d518b37bacf60d24b8a41f">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityMetricInstance.html#8d703e678b44bb5501939cbb35f1e1ad">CUpti_ActivityMetricInstance</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#e7330e44eea2b8aed71b1334884a3323">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEventInstance.html#6da0255bb1ce66108b5e23bbbb744b8b">CUpti_ActivityInstantaneousEventInstance</a>
<li>pad0
: <a class="el" href="structCUpti__ActivityPcie.html#24ad7e574c8887adc89d36b14db1b949">CUpti_ActivityPcie</a>
<li>pad1
: <a class="el" href="structCUpti__ActivityOpenAccData.html#70b525b93b3830f1ba1fdbb23f2bd99e">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#1eeb9352d984ac2415ce27f8b05b46cf">CUpti_ActivityOpenAccLaunch</a>
<li>pad2
: <a class="el" href="structCUpti__ActivityMemcpy5.html#f86a13a423086976dbd2b5bdbb200f16">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#ad64c4f5b576f7a813b2216795d4fb18">CUpti_ActivityMemset4</a>
<li>padding
: <a class="el" href="structCUpti__ActivityMemcpy4.html#523c2e4d5e5fd50ea2ce7b8ec24d89ff">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#1bd1c9c2c8f217040878dbd0dcbd39fa">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#994eb21ab711531996007cda8a928276">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#d006e56fc6a027ab93154703d2f5ef6a">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#83be78d563ab6303c4714537c99cb472">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#eaaaf2c9006e5559a7aac13dd84c0486">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#71fcb028839bad4c9981a3cc5e350b3a">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#7a2b34a8e0f3871b7896292ae88a4e0e">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#a474a5f04c0ccf8a82f37029faa171c5">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityJit.html#6f4a9f5127664c72c113a1365666f156">CUpti_ActivityJit</a>
<li>parentBlockX
: <a class="el" href="structCUpti__ActivityCdpKernel.html#c4be3006104dc007653df49faed7224d">CUpti_ActivityCdpKernel</a>
<li>parentBlockY
: <a class="el" href="structCUpti__ActivityCdpKernel.html#891d2fe6af85cffe78e1f90d2e280f1f">CUpti_ActivityCdpKernel</a>
<li>parentBlockZ
: <a class="el" href="structCUpti__ActivityCdpKernel.html#f4679f4685f646a64be682d90bb36331">CUpti_ActivityCdpKernel</a>
<li>parentConstruct
: <a class="el" href="structCUpti__ActivityOpenAcc.html#86449c5baf4c4eda27860511e3b909c8">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#68a68ffc7cfe0760a9c2f68a8982ee8f">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#3d6a65793fd64b90bf620bfb8d66ebf5">CUpti_ActivityOpenAccOther</a>
<li>parentGridId
: <a class="el" href="structCUpti__ActivityCdpKernel.html#dcc5b312ecc3b0c7ba04a439704fd112">CUpti_ActivityCdpKernel</a>
<li>partitionedGlobalCacheExecuted
: <a class="el" href="structCUpti__ActivityKernel3.html#9e94fd60a71ec506777ff5dc1efe1112">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#28fd56a6fec868f81723c3d07ca502e7">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#b0851f858df09cb67f04e9e962fd18c9">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#1914bb9c82257e19a7cc627f5c413799">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#0098287b94ed1b7b430a21302ecd3d6d">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#600b93250df7721993f66b45b8d67271">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#d38470cc9c074c4f8f99473d968d3f44">CUpti_ActivityKernel9</a>
<li>partitionedGlobalCacheRequested
: <a class="el" href="structCUpti__ActivityKernel3.html#c486131d9d49586aa959013eae2901f9">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#7e8f55639860b40dcd53249500dabd96">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#5719804df55222ff1c9430903b94165f">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#a96e3194cd271a1af3658c3bc0921db3">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#056376e67dfebdea78793f1a35b89fa2">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#9feb7964a4dfd1b9ec2bd124f4aa5f50">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#067f0363d37d4997d5a3695f9ed383d9">CUpti_ActivityKernel9</a>
<li>passIndex
: <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#d6fb9a2e13df9215871bf98ab2a3eb6c">CUpti_Profiler_SetConfig_Params</a>
, <a class="el" href="structCUpti__Profiler__EndPass__Params.html#bd1d93bc5460ad132990adefda6cfcf2">CUpti_Profiler_EndPass_Params</a>
<li>payload
: <a class="el" href="structCUpti__ActivityMarkerData.html#6173403e530ddffdc319bec3afe636bd">CUpti_ActivityMarkerData</a>
<li>payloadKind
: <a class="el" href="structCUpti__ActivityMarkerData.html#c2a433aee66351fb5cddcd19ef7583d6">CUpti_ActivityMarkerData</a>
<li>pBufferInfoData
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#5b7c261e08f1600a008eef46023cb658">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
<li>PC
: <a class="el" href="structCUpti__ActivityMemory2.html#b78ee1d974b686cd6e0316cd0470117c">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#be95bb903c9c74771055bf4a09870d4c">CUpti_ActivityMemory3</a>
<li>pcieGeneration
: <a class="el" href="structCUpti__ActivityPcie.html#c4f652d71bbefe5b764c33ffa508b3d0">CUpti_ActivityPcie</a>
<li>pcieLinkGen
: <a class="el" href="structCUpti__ActivityEnvironment.html#65551f5708666db35d3fda6f851865b5">CUpti_ActivityEnvironment</a>
<li>pcieLinkWidth
: <a class="el" href="structCUpti__ActivityEnvironment.html#dabf37883d378b25c8a56c1c765c98c9">CUpti_ActivityEnvironment</a>
<li>pcOffset
: <a class="el" href="structCUpti__ActivityGlobalAccess.html#3b8857002223eb82ea24ac13fccd170c">CUpti_ActivityGlobalAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#b2275cc0d73ea69d2409fe0d7747c3ec">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#bd851c0443368383e9d86360c29b3941">CUpti_ActivityGlobalAccess3</a>
, <a class="el" href="structCUpti__ActivityBranch.html#652f7b116d8738a0f72e9daed29aef95">CUpti_ActivityBranch</a>
, <a class="el" href="structCUpti__ActivityBranch2.html#57817347cd8d51dad49ce547077976b1">CUpti_ActivityBranch2</a>
, <a class="el" href="structCUpti__ActivityInstructionCorrelation.html#37aa4891cd88e675d9578bec5ad7ee73">CUpti_ActivityInstructionCorrelation</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#c01976cf9f0805878aaa4ab4a85e638a">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivityPCSampling.html#637d4c5c9579566fecb98dcf8b2d3b91">CUpti_ActivityPCSampling</a>
, <a class="el" href="structCUpti__ActivityPCSampling2.html#16fffe727d36705fc60c9766ea32bbe2">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#f09b061063d1c9e7b4a989f7ef9c04ad">CUpti_ActivityPCSampling3</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#f9450a9c16d9c545a1cdb3b5909ab542">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__PCSamplingPCData.html#a40b1a291c8d4dc774cac92657ffdd2b">CUpti_PCSamplingPCData</a>
, <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#9713b8a495f78284612c1f3403a8bd39">CUpti_GetSassToSourceCorrelationParams</a>
<li>pConfig
: <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#c28aecfebb897a9ef4a7ae4599af8064">CUpti_Profiler_SetConfig_Params</a>
<li>pCounterAvailabilityImage
: <a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#ffdbb99799566a7989e07fa4bf2c19be">CUpti_Profiler_GetCounterAvailability_Params</a>
<li>pCounterDataFilePath
: <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#7061112a59b3ec59c3f60261f41fb0f0">CUpti_Profiler_BeginSession_Params</a>
<li>pCounterDataImage
: <a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#4b9e1301f3ea3313ba96a5b39eff7386">CUpti_Profiler_CounterDataImage_Initialize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#d0d7ab35a470595e78208e014c9ebcf9">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#6cfc69416dff06c5898fdcc4c716a4b3">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a>
, <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#b5502318f4afd407f15a800cece9f030">CUpti_Profiler_BeginSession_Params</a>
<li>pCounterDataPrefix
: <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#8dfccc885af68268033d71926c37c1cd">CUpti_Profiler_CounterDataImageOptions</a>
<li>pCounterDataScratchBuffer
: <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#330585a7b6cef3912946bdc21cfaf922">CUpti_Profiler_BeginSession_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#0287d7929c16694c07d3a4e9c34c1e6a">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a>
<li>PcSampDataBuffer
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#17737240fde681d90ca46f3e2162a279">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a>
<li>pcSamplingData
: <a class="el" href="structCUpti__PCSamplingGetDataParams.html#74fed7e7f23a6eb7300ae80de927140c">CUpti_PCSamplingGetDataParams</a>
<li>pcSamplingStallReasonIndex
: <a class="el" href="structCUpti__PCSamplingStallReason.html#c11d563facfd8d0bb4d05f08d5450bd3">CUpti_PCSamplingStallReason</a>
<li>pCubin
: <a class="el" href="structCUpti__ModuleResourceData.html#72a92bfe2127a8b0b245b41f5a85e9ff">CUpti_ModuleResourceData</a>
<li>peerDev
: <a class="el" href="structCUpti__ActivityPcie.html#ec71be9d1b09221e84a85f7c01d0eeca">CUpti_ActivityPcie</a>
<li>physicalNvLinkCount
: <a class="el" href="structCUpti__ActivityNvLink3.html#e4c5f89a7da90c4bf44a35d8d2e613df">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink.html#6f5de984955775d5bb188e09e698784c">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#7e41e33987558b0c22d6d02d30bc5e6e">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#74731eaf480ee7cba996763720d1e469">CUpti_ActivityNvLink4</a>
<li>pid
: <a class="el" href="structCUpti__ActivityAutoBoostState.html#fe424a40cbe3ec6053530532d088b4d4">CUpti_ActivityAutoBoostState</a>
<li>pOptions
: <a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#85695d03e22a0a634575d04d5d2b4556">CUpti_Profiler_CounterDataImage_Initialize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#4c61d978d675f51e8094818a2cf026e0">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a>
<li>portDev0
: <a class="el" href="structCUpti__ActivityNvLink.html#896ce20863bca39ce8856cab1d6404f0">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#cebbaa71fc8a2210aeb76b31f00b9bb5">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#2fdff4e57250cecc13c888d7e3cbeacd">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#e2000b4e43a642fdf1f27ae31d0c17da">CUpti_ActivityNvLink4</a>
<li>portDev1
: <a class="el" href="structCUpti__ActivityNvLink4.html#cf91d7d9373ae84ad440401428f3156e">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#6da24893dd90a0883af0ef2f22d9aa50">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink.html#e90506328344c02dfa1ff6eb982285f4">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#4a69b1b6b568c83d4f41b765c3d5dd72">CUpti_ActivityNvLink2</a>
<li>power
: <a class="el" href="structCUpti__ActivityEnvironment.html#6975324c822504d779a6f14a0888933f">CUpti_ActivityEnvironment</a>
<li>powerLimit
: <a class="el" href="structCUpti__ActivityEnvironment.html#aec43cd954ae85b91efb47ce5780e1bb">CUpti_ActivityEnvironment</a>
<li>pPcData
: <a class="el" href="structCUpti__PCSamplingData.html#82d7bab4f2b8654b76c0be941e2c0dcf">CUpti_PCSamplingData</a>
<li>pPCSamplingConfigurationInfo
: <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#bfd81ba5c146175f45f7e8c14457fcd6">CUpti_PCSamplingConfigurationInfoParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#0d43759457b95653ca345958d488107b">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#780bcd388e5f1da82df2bb60a001724f">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
<li>pPcSamplingStallReasons
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#885d3688c9b0d0ae5d168d866d8db2ce">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#5e2fc251c5a6d8f8a4cf665a447350e1">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a>
<li>pPriv
: <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#c923db279c465a5af1b4c59b41b60490">NV::Cupti::Checkpoint::CUpti_Checkpoint</a>
, <a class="el" href="structCUpti__Profiler__Initialize__Params.html#e821bccc5fa5dec77df13b7817ea8896">CUpti_Profiler_Initialize_Params</a>
, <a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#a222659b0936959904f70e3249aafb81">CUpti_Profiler_GetCounterAvailability_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#59aa3633a09400518d6f9336bab2b8a8">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a>
, <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#a26c48169e271b780d7628cc31eadbc9">CUpti_Profiler_BeginSession_Params</a>
, <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#95df11cfcbbbd1b16c0c0af4d7c491ce">CUpti_Profiler_SetConfig_Params</a>
, <a class="el" href="structCUpti__PCSamplingGetDataParams.html#1ea7a8f82c08de9c3d7a5f0ca63b40db">CUpti_PCSamplingGetDataParams</a>
, <a class="el" href="structCUpti__Profiler__BeginPass__Params.html#e8e8c7ddec9985718559340d9dde9d78">CUpti_Profiler_BeginPass_Params</a>
, <a class="el" href="structCUpti__PCSamplingStartParams.html#089850ef9471a42eb353164510e65729">CUpti_PCSamplingStartParams</a>
, <a class="el" href="structCUpti__Profiler__EnableProfiling__Params.html#1d87fff637ec3cb5fe449a2fdfd602cc">CUpti_Profiler_EnableProfiling_Params</a>
, <a class="el" href="structCUpti__PCSamplingDisableParams.html#a2efa349fb4f0cf9cdba86bd8296fb75">CUpti_PCSamplingDisableParams</a>
, <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#fcd7425b7be01cd5c140a1aac6f07eeb">CUpti_PCSamplingGetStallReasonsParams</a>
, <a class="el" href="structCUpti__PCSamplingStopParams.html#4f95feca7d0b1dace8aa091e6ea3deb7">CUpti_PCSamplingStopParams</a>
, <a class="el" href="structCUpti__PCSamplingEnableParams.html#a06792e6fb766237985fc7b0e4f23f8e">CUpti_PCSamplingEnableParams</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#fb9de5ab2e7fe1bef000b829e36963ae">CUpti_Profiler_CounterDataImage_Initialize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#49561f9b246c682ca7ab8c20e2f55d57">CUpti_Profiler_CounterDataImageOptions</a>
, <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#f4cadb3a732398d544e27ae6a039c296">CUpti_PCSamplingGetNumStallReasonsParams</a>
, <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#738bd9fbcf869e4cdf96cd4516739bf8">CUpti_PCSamplingConfigurationInfoParams</a>
, <a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#ad37da287c2ebf6bedaa25659a04ae36">CUpti_Profiler_IsPassCollected_Params</a>
, <a class="el" href="structCUpti__Profiler__UnsetConfig__Params.html#fe155f755a20fd5af98602029ea77736">CUpti_Profiler_UnsetConfig_Params</a>
, <a class="el" href="structCUpti__Profiler__DeInitialize__Params.html#89e03b89d5b592a5ee202fd7955f7bf5">CUpti_Profiler_DeInitialize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#7733fe90bba9bffba109b23dc621831e">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a>
, <a class="el" href="structCUpti__Profiler__EndSession__Params.html#4b52c37df54595c8d0602658dd10d2d3">CUpti_Profiler_EndSession_Params</a>
, <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#43b73ef08bbba02af58fd287d017d4b9">CUpti_Profiler_DeviceSupported_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#4485c5c0d54a45cbc1f1b4756e55eadf">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a>
, <a class="el" href="structCUpti__Profiler__DisableProfiling__Params.html#734601171bc8c6057566f2bb8cd0719d">CUpti_Profiler_DisableProfiling_Params</a>
, <a class="el" href="structCUpti__Profiler__EndPass__Params.html#fbda0269ab05f8a5698495f842c24d0d">CUpti_Profiler_EndPass_Params</a>
, <a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#ea3f2437a4758ec15d67e48be03679fa">CUpti_Profiler_FlushCounterData_Params</a>
<li>preemptionKind
: <a class="el" href="structCUpti__ActivityPreemption.html#cae1214e9afa9408296b61d1057053ed">CUpti_ActivityPreemption</a>
<li>priority
: <a class="el" href="structCUpti__ActivityStream.html#9b1ab26578fb807aa4df4bc38e3db251">CUpti_ActivityStream</a>
<li>processId
: <a class="el" href="structCUpti__ActivityMemory2.html#48458c3eddcfaaeea2c3ab45bbbb1092">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#2e18f2211c242e91197e027b29f0359f">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivityMemory.html#57c6f4268958946c3384b43deab7f88f">CUpti_ActivityMemory</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#6a0a59696c57b1094892ba632b14b534">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#4f0f99e31f2312ddd9eac5e9c97bf16a">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#7c1777244ba2c452f778dfcbc44d7e95">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityAPI.html#e6ecbca91b11a92aee9bececae95f84b">CUpti_ActivityAPI</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#5380e7b6529aaaef5314dbe0113c3c28">CUpti_ActivityUnifiedMemoryCounter</a>
, <a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#513cf3ab1a411930759ba371c7416d6a">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#0a0b49b0018469f8e304a58f14423b7d">CUpti_ActivityMemory2</a>
<li>pSamplingData
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#74c6ec18e6b9c86469bb1f3b37aeb567">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#6c0e44aaa78a597f5aca381ff8fb7535">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
<li>pt
: <a class="el" href="unionCUpti__ActivityObjectKindId.html#978e16ae16b0e7dae8bbb26fdafca938">CUpti_ActivityObjectKindId</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
