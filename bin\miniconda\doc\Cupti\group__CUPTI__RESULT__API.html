<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Result Codes</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Result Codes</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871460bd0257372573920d9bb2c802ce3b71">CUPTI_SUCCESS</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714d4dc6d34f22e4adc9b129df68b7bae63">CUPTI_ERROR_INVALID_PARAMETER</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714d4e3edbcbf87a0e56a8419aa4d5a056c">CUPTI_ERROR_INVALID_DEVICE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887144693d88f65fa6efcd21a8259ec3e8c29">CUPTI_ERROR_INVALID_CONTEXT</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714226fc11ce118a08b207666af282aa096">CUPTI_ERROR_INVALID_EVENT_DOMAIN_ID</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887149da48ef2a261b2c3047603acc8d5c548">CUPTI_ERROR_INVALID_EVENT_ID</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871403f2933f692108a1ad544936572cbdca">CUPTI_ERROR_INVALID_EVENT_NAME</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714fdf49c2358b9da99aaad9f932fde2b25">CUPTI_ERROR_INVALID_OPERATION</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887143026007911417005c6e8195cecbb982a">CUPTI_ERROR_OUT_OF_MEMORY</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714323402d9af39654ac88a83ec4a9c1bd6">CUPTI_ERROR_HARDWARE</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714004b896abf14bd89619e48db8d7f45b5">CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887149266804628c7702b3531d435610f154a">CUPTI_ERROR_API_NOT_IMPLEMENTED</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714a6d97464748158d2c5e45eeac821d1da">CUPTI_ERROR_MAX_LIMIT_REACHED</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887143c7fb2db82ff0f7a206c54b2dd387fa0">CUPTI_ERROR_NOT_READY</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714e513a0f34c398fe4c4416b8e673e0721">CUPTI_ERROR_NOT_COMPATIBLE</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887146eacb4cecd3e569b080a94fe5520e111">CUPTI_ERROR_NOT_INITIALIZED</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714d00647ff8e3dd14a08a452873954c02b">CUPTI_ERROR_INVALID_METRIC_ID</a> =  16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887147a2c57f2bff34bfe66e570dedcea724b">CUPTI_ERROR_INVALID_METRIC_NAME</a> =  17, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714e267e4096cca7c586fcd650920c61389">CUPTI_ERROR_QUEUE_EMPTY</a> =  18, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714f7e3481f8285b6dc0f4e57f61636a728">CUPTI_ERROR_INVALID_HANDLE</a> =  19, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714b9e1cbc9a6eec9ee2cee5e5e7634bdd8">CUPTI_ERROR_INVALID_STREAM</a> =  20, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714b80850b04865d344e1cb74c6e5f4fa89">CUPTI_ERROR_INVALID_KIND</a> =  21, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887148a86d5e5179c2301f07175ed1fc48f53">CUPTI_ERROR_INVALID_EVENT_VALUE</a> =  22, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714220d3758190ac7ee209f87806e4d1190">CUPTI_ERROR_DISABLED</a> =  23, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887149521d0be61abb1275a1084934e4f439b">CUPTI_ERROR_INVALID_MODULE</a> =  24, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714219f355b450d885882913616e209bd13">CUPTI_ERROR_INVALID_METRIC_VALUE</a> =  25, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714278d02b6dfb6149a194d52b21b7d4b51">CUPTI_ERROR_HARDWARE_BUSY</a> =  26, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871464bccf609555971c74dcdfde280d1ec5">CUPTI_ERROR_NOT_SUPPORTED</a> =  27, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887140552463887f8a8ee360a0a6266cb38d4">CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED</a> =  28, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887149bdac8360c3201c14a105ae7ebc1fc60">CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_DEVICE</a> =  29, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871487c48905f22aacfce2952766295e9012">CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_NON_P2P_DEVICES</a> =  30, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887145ec165813586cf953b594a771e9731af">CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_WITH_MPS</a> =  31, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714e37e4aa9b8df0783da522633e60aadde">CUPTI_ERROR_CDP_TRACING_NOT_SUPPORTED</a> =  32, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714b8c2ebd3c7127dd3ccdc79bd7a1e6667">CUPTI_ERROR_VIRTUALIZED_DEVICE_NOT_SUPPORTED</a> =  33, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871498cc4acf3321aed42fb903ce5a3bf442">CUPTI_ERROR_CUDA_COMPILER_NOT_COMPATIBLE</a> =  34, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714bd91e10d1fe9756753603b1409bf1925">CUPTI_ERROR_INSUFFICIENT_PRIVILEGES</a> =  35, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887145e147bacdf9e74450a51473282c0544a">CUPTI_ERROR_OLD_PROFILER_API_INITIALIZED</a> =  36, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871432d7813e01513b9c11b3ab379b59e368">CUPTI_ERROR_OPENACC_UNDEFINED_ROUTINE</a> =  37, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714bd59b3c4fe91d2a294495953ded7cbfd">CUPTI_ERROR_LEGACY_PROFILER_NOT_SUPPORTED</a> =  38, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871483a17b153be4fcfeec47702b464c10ae">CUPTI_ERROR_MULTIPLE_SUBSCRIBERS_NOT_SUPPORTED</a> =  39, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887145d6870795eeefd97df1ba7028bd1c510">CUPTI_ERROR_VIRTUALIZED_DEVICE_INSUFFICIENT_PRIVILEGES</a> =  40, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871427c8f73606b8c590313b99d4d6a37361">CUPTI_ERROR_CONFIDENTIAL_COMPUTING_NOT_SUPPORTED</a> =  41, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c887140c33e0b62d6e3b8952e527446073df98">CUPTI_ERROR_CMP_DEVICE_NOT_SUPPORTED</a> =  42, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714f9d2ec11d1e9023750bb9f03259da407">CUPTI_ERROR_UNKNOWN</a> =  999
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">CUPTI result codes.  <a href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__RESULT__API.html#g6d5275dd8f127592c1fe3033bb73a751">cuptiGetResultString</a> (<a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> result, const char **str)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the descriptive string for a CUptiResult.  <a href="#g6d5275dd8f127592c1fe3033bb73a751"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Error and result codes returned by CUPTI functions. <hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g8c54bf95108e67d858f37fcf76c88714"></a><!-- doxytag: member="cupti_result.h::CUptiResult" ref="g8c54bf95108e67d858f37fcf76c88714" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Error and result codes returned by CUPTI functions. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871460bd0257372573920d9bb2c802ce3b71"></a><!-- doxytag: member="CUPTI_SUCCESS" ref="gg8c54bf95108e67d858f37fcf76c8871460bd0257372573920d9bb2c802ce3b71" args="" -->CUPTI_SUCCESS</em>&nbsp;</td><td>
No error. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714d4dc6d34f22e4adc9b129df68b7bae63"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_PARAMETER" ref="gg8c54bf95108e67d858f37fcf76c88714d4dc6d34f22e4adc9b129df68b7bae63" args="" -->CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>
One or more of the parameters is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714d4e3edbcbf87a0e56a8419aa4d5a056c"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_DEVICE" ref="gg8c54bf95108e67d858f37fcf76c88714d4e3edbcbf87a0e56a8419aa4d5a056c" args="" -->CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td>
The device does not correspond to a valid CUDA device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887144693d88f65fa6efcd21a8259ec3e8c29"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_CONTEXT" ref="gg8c54bf95108e67d858f37fcf76c887144693d88f65fa6efcd21a8259ec3e8c29" args="" -->CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td>
The context is NULL or not valid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714226fc11ce118a08b207666af282aa096"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_EVENT_DOMAIN_ID" ref="gg8c54bf95108e67d858f37fcf76c88714226fc11ce118a08b207666af282aa096" args="" -->CUPTI_ERROR_INVALID_EVENT_DOMAIN_ID</em>&nbsp;</td><td>
The event domain id is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887149da48ef2a261b2c3047603acc8d5c548"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_EVENT_ID" ref="gg8c54bf95108e67d858f37fcf76c887149da48ef2a261b2c3047603acc8d5c548" args="" -->CUPTI_ERROR_INVALID_EVENT_ID</em>&nbsp;</td><td>
The event id is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871403f2933f692108a1ad544936572cbdca"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_EVENT_NAME" ref="gg8c54bf95108e67d858f37fcf76c8871403f2933f692108a1ad544936572cbdca" args="" -->CUPTI_ERROR_INVALID_EVENT_NAME</em>&nbsp;</td><td>
The event name is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714fdf49c2358b9da99aaad9f932fde2b25"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_OPERATION" ref="gg8c54bf95108e67d858f37fcf76c88714fdf49c2358b9da99aaad9f932fde2b25" args="" -->CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>
The current operation cannot be performed due to dependency on other factors. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887143026007911417005c6e8195cecbb982a"></a><!-- doxytag: member="CUPTI_ERROR_OUT_OF_MEMORY" ref="gg8c54bf95108e67d858f37fcf76c887143026007911417005c6e8195cecbb982a" args="" -->CUPTI_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td>
Unable to allocate enough memory to perform the requested operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714323402d9af39654ac88a83ec4a9c1bd6"></a><!-- doxytag: member="CUPTI_ERROR_HARDWARE" ref="gg8c54bf95108e67d858f37fcf76c88714323402d9af39654ac88a83ec4a9c1bd6" args="" -->CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td>
An error occurred on the performance monitoring hardware. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714004b896abf14bd89619e48db8d7f45b5"></a><!-- doxytag: member="CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT" ref="gg8c54bf95108e67d858f37fcf76c88714004b896abf14bd89619e48db8d7f45b5" args="" -->CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>
The output buffer size is not sufficient to return all requested data. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887149266804628c7702b3531d435610f154a"></a><!-- doxytag: member="CUPTI_ERROR_API_NOT_IMPLEMENTED" ref="gg8c54bf95108e67d858f37fcf76c887149266804628c7702b3531d435610f154a" args="" -->CUPTI_ERROR_API_NOT_IMPLEMENTED</em>&nbsp;</td><td>
API is not implemented. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714a6d97464748158d2c5e45eeac821d1da"></a><!-- doxytag: member="CUPTI_ERROR_MAX_LIMIT_REACHED" ref="gg8c54bf95108e67d858f37fcf76c88714a6d97464748158d2c5e45eeac821d1da" args="" -->CUPTI_ERROR_MAX_LIMIT_REACHED</em>&nbsp;</td><td>
The maximum limit is reached. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887143c7fb2db82ff0f7a206c54b2dd387fa0"></a><!-- doxytag: member="CUPTI_ERROR_NOT_READY" ref="gg8c54bf95108e67d858f37fcf76c887143c7fb2db82ff0f7a206c54b2dd387fa0" args="" -->CUPTI_ERROR_NOT_READY</em>&nbsp;</td><td>
The object is not yet ready to perform the requested operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714e513a0f34c398fe4c4416b8e673e0721"></a><!-- doxytag: member="CUPTI_ERROR_NOT_COMPATIBLE" ref="gg8c54bf95108e67d858f37fcf76c88714e513a0f34c398fe4c4416b8e673e0721" args="" -->CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>
The current operation is not compatible with the current state of the object </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887146eacb4cecd3e569b080a94fe5520e111"></a><!-- doxytag: member="CUPTI_ERROR_NOT_INITIALIZED" ref="gg8c54bf95108e67d858f37fcf76c887146eacb4cecd3e569b080a94fe5520e111" args="" -->CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>
CUPTI is unable to initialize its connection to the CUDA driver. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714d00647ff8e3dd14a08a452873954c02b"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_METRIC_ID" ref="gg8c54bf95108e67d858f37fcf76c88714d00647ff8e3dd14a08a452873954c02b" args="" -->CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td>
The metric id is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887147a2c57f2bff34bfe66e570dedcea724b"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_METRIC_NAME" ref="gg8c54bf95108e67d858f37fcf76c887147a2c57f2bff34bfe66e570dedcea724b" args="" -->CUPTI_ERROR_INVALID_METRIC_NAME</em>&nbsp;</td><td>
The metric name is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714e267e4096cca7c586fcd650920c61389"></a><!-- doxytag: member="CUPTI_ERROR_QUEUE_EMPTY" ref="gg8c54bf95108e67d858f37fcf76c88714e267e4096cca7c586fcd650920c61389" args="" -->CUPTI_ERROR_QUEUE_EMPTY</em>&nbsp;</td><td>
The queue is empty. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714f7e3481f8285b6dc0f4e57f61636a728"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_HANDLE" ref="gg8c54bf95108e67d858f37fcf76c88714f7e3481f8285b6dc0f4e57f61636a728" args="" -->CUPTI_ERROR_INVALID_HANDLE</em>&nbsp;</td><td>
Invalid handle (internal?). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714b9e1cbc9a6eec9ee2cee5e5e7634bdd8"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_STREAM" ref="gg8c54bf95108e67d858f37fcf76c88714b9e1cbc9a6eec9ee2cee5e5e7634bdd8" args="" -->CUPTI_ERROR_INVALID_STREAM</em>&nbsp;</td><td>
Invalid stream. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714b80850b04865d344e1cb74c6e5f4fa89"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_KIND" ref="gg8c54bf95108e67d858f37fcf76c88714b80850b04865d344e1cb74c6e5f4fa89" args="" -->CUPTI_ERROR_INVALID_KIND</em>&nbsp;</td><td>
Invalid kind. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887148a86d5e5179c2301f07175ed1fc48f53"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_EVENT_VALUE" ref="gg8c54bf95108e67d858f37fcf76c887148a86d5e5179c2301f07175ed1fc48f53" args="" -->CUPTI_ERROR_INVALID_EVENT_VALUE</em>&nbsp;</td><td>
Invalid event value. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714220d3758190ac7ee209f87806e4d1190"></a><!-- doxytag: member="CUPTI_ERROR_DISABLED" ref="gg8c54bf95108e67d858f37fcf76c88714220d3758190ac7ee209f87806e4d1190" args="" -->CUPTI_ERROR_DISABLED</em>&nbsp;</td><td>
CUPTI is disabled due to conflicts with other enabled profilers </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887149521d0be61abb1275a1084934e4f439b"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_MODULE" ref="gg8c54bf95108e67d858f37fcf76c887149521d0be61abb1275a1084934e4f439b" args="" -->CUPTI_ERROR_INVALID_MODULE</em>&nbsp;</td><td>
Invalid module. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714219f355b450d885882913616e209bd13"></a><!-- doxytag: member="CUPTI_ERROR_INVALID_METRIC_VALUE" ref="gg8c54bf95108e67d858f37fcf76c88714219f355b450d885882913616e209bd13" args="" -->CUPTI_ERROR_INVALID_METRIC_VALUE</em>&nbsp;</td><td>
Invalid metric value. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714278d02b6dfb6149a194d52b21b7d4b51"></a><!-- doxytag: member="CUPTI_ERROR_HARDWARE_BUSY" ref="gg8c54bf95108e67d858f37fcf76c88714278d02b6dfb6149a194d52b21b7d4b51" args="" -->CUPTI_ERROR_HARDWARE_BUSY</em>&nbsp;</td><td>
The performance monitoring hardware is in use by other client. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871464bccf609555971c74dcdfde280d1ec5"></a><!-- doxytag: member="CUPTI_ERROR_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c8871464bccf609555971c74dcdfde280d1ec5" args="" -->CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>
The attempted operation is not supported on the current system or device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887140552463887f8a8ee360a0a6266cb38d4"></a><!-- doxytag: member="CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c887140552463887f8a8ee360a0a6266cb38d4" args="" -->CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED</em>&nbsp;</td><td>
Unified memory profiling is not supported on the system. Potential reason could be unsupported OS or architecture. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887149bdac8360c3201c14a105ae7ebc1fc60"></a><!-- doxytag: member="CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_DEVICE" ref="gg8c54bf95108e67d858f37fcf76c887149bdac8360c3201c14a105ae7ebc1fc60" args="" -->CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_DEVICE</em>&nbsp;</td><td>
Unified memory profiling is not supported on the device </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871487c48905f22aacfce2952766295e9012"></a><!-- doxytag: member="CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_NON_P2P_DEVICES" ref="gg8c54bf95108e67d858f37fcf76c8871487c48905f22aacfce2952766295e9012" args="" -->CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_NON_P2P_DEVICES</em>&nbsp;</td><td>
Unified memory profiling is not supported on a multi-GPU configuration without P2P support between any pair of devices </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887145ec165813586cf953b594a771e9731af"></a><!-- doxytag: member="CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_WITH_MPS" ref="gg8c54bf95108e67d858f37fcf76c887145ec165813586cf953b594a771e9731af" args="" -->CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_WITH_MPS</em>&nbsp;</td><td>
Unified memory profiling is not supported under the Multi-Process Service (MPS) environment. CUDA 7.5 removes this restriction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714e37e4aa9b8df0783da522633e60aadde"></a><!-- doxytag: member="CUPTI_ERROR_CDP_TRACING_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c88714e37e4aa9b8df0783da522633e60aadde" args="" -->CUPTI_ERROR_CDP_TRACING_NOT_SUPPORTED</em>&nbsp;</td><td>
In CUDA 9.0, devices with compute capability 7.0 don't support CDP tracing </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714b8c2ebd3c7127dd3ccdc79bd7a1e6667"></a><!-- doxytag: member="CUPTI_ERROR_VIRTUALIZED_DEVICE_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c88714b8c2ebd3c7127dd3ccdc79bd7a1e6667" args="" -->CUPTI_ERROR_VIRTUALIZED_DEVICE_NOT_SUPPORTED</em>&nbsp;</td><td>
Profiling on virtualized GPU is not supported. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871498cc4acf3321aed42fb903ce5a3bf442"></a><!-- doxytag: member="CUPTI_ERROR_CUDA_COMPILER_NOT_COMPATIBLE" ref="gg8c54bf95108e67d858f37fcf76c8871498cc4acf3321aed42fb903ce5a3bf442" args="" -->CUPTI_ERROR_CUDA_COMPILER_NOT_COMPATIBLE</em>&nbsp;</td><td>
Profiling results might be incorrect for CUDA applications compiled with nvcc version older than 9.0 for devices with compute capability 6.0 and 6.1. Profiling session will continue and CUPTI will notify it using this error code. User is advised to recompile the application code with nvcc version 9.0 or later. Ignore this warning if code is already compiled with the recommended nvcc version. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714bd91e10d1fe9756753603b1409bf1925"></a><!-- doxytag: member="CUPTI_ERROR_INSUFFICIENT_PRIVILEGES" ref="gg8c54bf95108e67d858f37fcf76c88714bd91e10d1fe9756753603b1409bf1925" args="" -->CUPTI_ERROR_INSUFFICIENT_PRIVILEGES</em>&nbsp;</td><td>
User doesn't have sufficient privileges which are required to start the profiling session. One possible reason for this may be that the NVIDIA driver or your system administrator may have restricted access to the NVIDIA GPU performance counters. To learn how to resolve this issue and find more information, please visit <a href="https://developer.nvidia.com/CUPTI_ERROR_INSUFFICIENT_PRIVILEGES">https://developer.nvidia.com/CUPTI_ERROR_INSUFFICIENT_PRIVILEGES</a> </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887145e147bacdf9e74450a51473282c0544a"></a><!-- doxytag: member="CUPTI_ERROR_OLD_PROFILER_API_INITIALIZED" ref="gg8c54bf95108e67d858f37fcf76c887145e147bacdf9e74450a51473282c0544a" args="" -->CUPTI_ERROR_OLD_PROFILER_API_INITIALIZED</em>&nbsp;</td><td>
Legacy CUPTI Profiling API i.e. event API from the header cupti_events.h and metric API from the header cupti_metrics.h are not compatible with the Profiling API in the header cupti_profiler_target.h and Perfworks metrics API in the headers nvperf_host.h and nvperf_target.h. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871432d7813e01513b9c11b3ab379b59e368"></a><!-- doxytag: member="CUPTI_ERROR_OPENACC_UNDEFINED_ROUTINE" ref="gg8c54bf95108e67d858f37fcf76c8871432d7813e01513b9c11b3ab379b59e368" args="" -->CUPTI_ERROR_OPENACC_UNDEFINED_ROUTINE</em>&nbsp;</td><td>
Missing definition of the OpenACC API routine in the linked OpenACC library.<p>
One possible reason is that OpenACC library is linked statically in the user application, which might not have the definition of all the OpenACC API routines needed for the OpenACC profiling, as compiler might ignore definitions for the functions not used in the application. This issue can be mitigated by linking the OpenACC library dynamically. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714bd59b3c4fe91d2a294495953ded7cbfd"></a><!-- doxytag: member="CUPTI_ERROR_LEGACY_PROFILER_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c88714bd59b3c4fe91d2a294495953ded7cbfd" args="" -->CUPTI_ERROR_LEGACY_PROFILER_NOT_SUPPORTED</em>&nbsp;</td><td>
Legacy CUPTI Profiling API i.e. event API from the header cupti_events.h and metric API from the header cupti_metrics.h are not supported on devices with compute capability 7.5 and higher (i.e. Turing and later GPU architectures). These API will be deprecated in a future CUDA release. These are replaced by Profiling API in the header cupti_profiler_target.h and Perfworks metrics API in the headers nvperf_host.h and nvperf_target.h. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871483a17b153be4fcfeec47702b464c10ae"></a><!-- doxytag: member="CUPTI_ERROR_MULTIPLE_SUBSCRIBERS_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c8871483a17b153be4fcfeec47702b464c10ae" args="" -->CUPTI_ERROR_MULTIPLE_SUBSCRIBERS_NOT_SUPPORTED</em>&nbsp;</td><td>
CUPTI doesn't allow multiple callback subscribers. Only a single subscriber can be registered at a time. Same error code is used when application is launched using NVIDIA tools like nvprof, Visual Profiler, Nsight Systems, Nsight Compute, cuda-gdb and cuda-memcheck. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887145d6870795eeefd97df1ba7028bd1c510"></a><!-- doxytag: member="CUPTI_ERROR_VIRTUALIZED_DEVICE_INSUFFICIENT_PRIVILEGES" ref="gg8c54bf95108e67d858f37fcf76c887145d6870795eeefd97df1ba7028bd1c510" args="" -->CUPTI_ERROR_VIRTUALIZED_DEVICE_INSUFFICIENT_PRIVILEGES</em>&nbsp;</td><td>
Profiling on virtualized GPU is not allowed by hypervisor. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c8871427c8f73606b8c590313b99d4d6a37361"></a><!-- doxytag: member="CUPTI_ERROR_CONFIDENTIAL_COMPUTING_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c8871427c8f73606b8c590313b99d4d6a37361" args="" -->CUPTI_ERROR_CONFIDENTIAL_COMPUTING_NOT_SUPPORTED</em>&nbsp;</td><td>
Profiling and tracing are not allowed when confidential computing mode is enabled. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c887140c33e0b62d6e3b8952e527446073df98"></a><!-- doxytag: member="CUPTI_ERROR_CMP_DEVICE_NOT_SUPPORTED" ref="gg8c54bf95108e67d858f37fcf76c887140c33e0b62d6e3b8952e527446073df98" args="" -->CUPTI_ERROR_CMP_DEVICE_NOT_SUPPORTED</em>&nbsp;</td><td>
CUPTI does not support NVIDIA Crypto Mining Processors (CMP). For more information, please visit <a href="https://developer.nvidia.com/ERR_NVCMPGPU">https://developer.nvidia.com/ERR_NVCMPGPU</a> </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c54bf95108e67d858f37fcf76c88714f9d2ec11d1e9023750bb9f03259da407"></a><!-- doxytag: member="CUPTI_ERROR_UNKNOWN" ref="gg8c54bf95108e67d858f37fcf76c88714f9d2ec11d1e9023750bb9f03259da407" args="" -->CUPTI_ERROR_UNKNOWN</em>&nbsp;</td><td>
An unknown internal error has occurred. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g6d5275dd8f127592c1fe3033bb73a751"></a><!-- doxytag: member="cupti_result.h::cuptiGetResultString" ref="g6d5275dd8f127592c1fe3033bb73a751" args="(CUptiResult result, const char **str)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetResultString           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td>
          <td class="paramname"> <em>result</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char **&nbsp;</td>
          <td class="paramname"> <em>str</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Return the descriptive string for a CUptiResult in <code>*str</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>result</em>&nbsp;</td><td>The result to get the string for </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>str</em>&nbsp;</td><td>Returns the string</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>str</code> is NULL or <code>result</code> is not a valid CUptiResult </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
