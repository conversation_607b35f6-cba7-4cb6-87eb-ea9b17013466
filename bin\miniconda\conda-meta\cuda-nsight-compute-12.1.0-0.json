{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["nsight-compute >=2023.1.0.15"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nsight-compute-12.1.0-0", "features": "", "files": [], "fn": "cuda-nsight-compute-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nsight-compute-12.1.0-0", "type": 1}, "md5": "78d93b6cdf204329d4ff6352d803b96a", "name": "cuda-nsight-compute", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nsight-compute-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1401, "subdir": "win-64", "timestamp": 1677129996000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-nsight-compute-12.1.0-0.tar.bz2", "version": "12.1.0"}