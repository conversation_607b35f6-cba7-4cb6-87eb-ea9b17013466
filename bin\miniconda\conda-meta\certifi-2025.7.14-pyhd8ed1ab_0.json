{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\certifi-2025.7.14-pyhd8ed1ab_0", "features": "", "files": ["Lib/site-packages/certifi-2025.7.14.dist-info/INSTALLER", "Lib/site-packages/certifi-2025.7.14.dist-info/LICENSE", "Lib/site-packages/certifi-2025.7.14.dist-info/METADATA", "Lib/site-packages/certifi-2025.7.14.dist-info/RECORD", "Lib/site-packages/certifi-2025.7.14.dist-info/REQUESTED", "Lib/site-packages/certifi-2025.7.14.dist-info/WHEEL", "Lib/site-packages/certifi-2025.7.14.dist-info/direct_url.json", "Lib/site-packages/certifi-2025.7.14.dist-info/top_level.txt", "Lib/site-packages/certifi/__init__.py", "Lib/site-packages/certifi/__main__.py", "Lib/site-packages/certifi/cacert.pem", "Lib/site-packages/certifi/core.py", "Lib/site-packages/certifi/py.typed", "Lib/site-packages/certifi/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/certifi/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/certifi/__pycache__/core.cpython-310.pyc"], "fn": "certifi-2025.7.14-pyhd8ed1ab_0.conda", "license": "ISC", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\certifi-2025.7.14-pyhd8ed1ab_0", "type": 1}, "md5": "4c07624f3faefd0bb6659fb7396cfa76", "name": "certifi", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\certifi-2025.7.14-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/certifi-2025.7.14.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/certifi-2025.7.14.dist-info/LICENSE", "path_type": "hardlink", "sha256": "e93716da6b9c0d5a4a1df60fe695b370f0695603d21f6f83f053e42cfc10caf7", "sha256_in_prefix": "e93716da6b9c0d5a4a1df60fe695b370f0695603d21f6f83f053e42cfc10caf7", "size_in_bytes": 989}, {"_path": "site-packages/certifi-2025.7.14.dist-info/METADATA", "path_type": "hardlink", "sha256": "98caea6e0f17d5d0eddfeb25ac352349247e2062842cefe77f65cbba693895f7", "sha256_in_prefix": "98caea6e0f17d5d0eddfeb25ac352349247e2062842cefe77f65cbba693895f7", "size_in_bytes": 2401}, {"_path": "site-packages/certifi-2025.7.14.dist-info/RECORD", "path_type": "hardlink", "sha256": "ccd4f9df035b6cf8e6a54c57c7795ccfe9d931d5fe36d5119ed21715a839ab39", "sha256_in_prefix": "ccd4f9df035b6cf8e6a54c57c7795ccfe9d931d5fe36d5119ed21715a839ab39", "size_in_bytes": 1203}, {"_path": "site-packages/certifi-2025.7.14.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/certifi-2025.7.14.dist-info/WHEEL", "path_type": "hardlink", "sha256": "227f454cdc5e3fad0a9d3906c3bc24ea624f61dfdd4128c46665dd05d30223ef", "sha256_in_prefix": "227f454cdc5e3fad0a9d3906c3bc24ea624f61dfdd4128c46665dd05d30223ef", "size_in_bytes": 91}, {"_path": "site-packages/certifi-2025.7.14.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "81d074e4491c32fa24fbbcc28061537af32e9037212aafe32e508ea79c8b17ef", "sha256_in_prefix": "81d074e4491c32fa24fbbcc28061537af32e9037212aafe32e508ea79c8b17ef", "size_in_bytes": 111}, {"_path": "site-packages/certifi-2025.7.14.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "28cbb8bd409fb232eb90f6d235d81d7a44bea552730402453bffe723c345ebe5", "sha256_in_prefix": "28cbb8bd409fb232eb90f6d235d81d7a44bea552730402453bffe723c345ebe5", "size_in_bytes": 8}, {"_path": "site-packages/certifi/__init__.py", "path_type": "hardlink", "sha256": "76f34351d017a7e873a1871fd3d3d7ccb9872253dfca968140f169e5eb035f2a", "sha256_in_prefix": "76f34351d017a7e873a1871fd3d3d7ccb9872253dfca968140f169e5eb035f2a", "size_in_bytes": 94}, {"_path": "site-packages/certifi/__main__.py", "path_type": "hardlink", "sha256": "c410688fdd394d45812d118034e71fee88ba7beddd30fe1c1281bd3b232cd758", "sha256_in_prefix": "c410688fdd394d45812d118034e71fee88ba7beddd30fe1c1281bd3b232cd758", "size_in_bytes": 243}, {"_path": "site-packages/certifi/cacert.pem", "path_type": "hardlink", "sha256": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "sha256_in_prefix": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "size_in_bytes": 290057}, {"_path": "site-packages/certifi/core.py", "path_type": "hardlink", "sha256": "5c55f2727746e697f7edac9e17c377d8752e0da7ecca191531b3b80403d61dad", "sha256_in_prefix": "5c55f2727746e697f7edac9e17c377d8752e0da7ecca191531b3b80403d61dad", "size_in_bytes": 3394}, {"_path": "site-packages/certifi/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/certifi/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/certifi/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/certifi/__pycache__/core.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "f68ee5038f37620a4fb4cdd8329c9897dce80331db8c94c3ab264a26a8c70a08", "size": 159755, "subdir": "noarch", "timestamp": 1752493370000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda", "version": "2025.7.14"}