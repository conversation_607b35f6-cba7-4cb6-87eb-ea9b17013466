{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\mdurl-0.1.0-py310haa95532_0", "files": ["Lib/site-packages/mdurl-0.1.0.dist-info/INSTALLER", "Lib/site-packages/mdurl-0.1.0.dist-info/LICENSE", "Lib/site-packages/mdurl-0.1.0.dist-info/METADATA", "Lib/site-packages/mdurl-0.1.0.dist-info/RECORD", "Lib/site-packages/mdurl-0.1.0.dist-info/REQUESTED", "Lib/site-packages/mdurl-0.1.0.dist-info/WHEEL", "Lib/site-packages/mdurl-0.1.0.dist-info/direct_url.json", "Lib/site-packages/mdurl/__init__.py", "Lib/site-packages/mdurl/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/mdurl/__pycache__/_decode.cpython-310.pyc", "Lib/site-packages/mdurl/__pycache__/_encode.cpython-310.pyc", "Lib/site-packages/mdurl/__pycache__/_format.cpython-310.pyc", "Lib/site-packages/mdurl/__pycache__/_parse.cpython-310.pyc", "Lib/site-packages/mdurl/__pycache__/_url.cpython-310.pyc", "Lib/site-packages/mdurl/_decode.py", "Lib/site-packages/mdurl/_encode.py", "Lib/site-packages/mdurl/_format.py", "Lib/site-packages/mdurl/_parse.py", "Lib/site-packages/mdurl/_url.py", "Lib/site-packages/mdurl/py.typed"], "fn": "mdurl-0.1.0-py310haa95532_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\mdurl-0.1.0-py310haa95532_0", "type": 1}, "md5": "bfee2c550708d9897e1a490512bc5640", "name": "mdurl", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\mdurl-0.1.0-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::mdurl==0.1.0=py310haa95532_0[md5=bfee2c550708d9897e1a490512bc5640]", "sha256": "5af13dd075edfdda5edef0b702b8bda858a25ba7d6e7dcbed10d3b949cd2a194", "size": 19106, "subdir": "win-64", "timestamp": 1659716154000, "url": "https://repo.anaconda.com/pkgs/main/win-64/mdurl-0.1.0-py310haa95532_0.conda", "version": "0.1.0"}