<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_SetConfig_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_SetConfig_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_SetConfig_Params" -->Params for cuptiProfilerSetConfig.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="b573048f9dfb3f88eecff556c5267fac"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::configSize" ref="b573048f9dfb3f88eecff556c5267fac" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#b573048f9dfb3f88eecff556c5267fac">configSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] size of config <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="cdc97ff4f8a09a0ee0fbada64fa9814f"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::ctx" ref="cdc97ff4f8a09a0ee0fbada64fa9814f" args="" -->
CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#cdc97ff4f8a09a0ee0fbada64fa9814f">ctx</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] if NULL, the current CUcontext is used <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="a917987772c88234c6ffc47c408aeb56"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::minNestingLevel" ref="a917987772c88234c6ffc47c408aeb56" args="" -->
uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#a917987772c88234c6ffc47c408aeb56">minNestingLevel</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] the lowest nesting level to be profiled; must be &gt;= 1 <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="2c0ba956bca45b7407bd0a7eef8dc607"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::numNestingLevels" ref="2c0ba956bca45b7407bd0a7eef8dc607" args="" -->
uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#2c0ba956bca45b7407bd0a7eef8dc607">numNestingLevels</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] the number of nesting levels to profile; must be &gt;= 1 <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="d6fb9a2e13df9215871bf98ab2a3eb6c"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::passIndex" ref="d6fb9a2e13df9215871bf98ab2a3eb6c" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#d6fb9a2e13df9215871bf98ab2a3eb6c">passIndex</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Set this to zero for in-app replay; set this to the output of EndPass() for application replay <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="c28aecfebb897a9ef4a7ae4599af8064"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::pConfig" ref="c28aecfebb897a9ef4a7ae4599af8064" args="" -->
const uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#c28aecfebb897a9ef4a7ae4599af8064">pConfig</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Config created by NVPW_RawMetricsConfig_GetConfigImage(). Must be align(8). <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="95df11cfcbbbd1b16c0c0af4d7c491ce"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::pPriv" ref="95df11cfcbbbd1b16c0c0af4d7c491ce" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#95df11cfcbbbd1b16c0c0af4d7c491ce">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="44687aab18b99379dab6e57fec3e13f2"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::structSize" ref="44687aab18b99379dab6e57fec3e13f2" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#44687aab18b99379dab6e57fec3e13f2">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_SetConfig_Params_STRUCT_SIZE <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="91c1e47aa6642ff97555a5619c6a6ba3"></a><!-- doxytag: member="CUpti_Profiler_SetConfig_Params::targetNestingLevel" ref="91c1e47aa6642ff97555a5619c6a6ba3" args="" -->
uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html#91c1e47aa6642ff97555a5619c6a6ba3">targetNestingLevel</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Set this to minNestingLevel for in-app replay; set this to the output of EndPass() for application <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
