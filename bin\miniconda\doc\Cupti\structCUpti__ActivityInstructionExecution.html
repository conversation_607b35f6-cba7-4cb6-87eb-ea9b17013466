<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityInstructionExecution Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityInstructionExecution Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityInstructionExecution" -->The activity record for source-level instruction execution.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#59b7a22a0e3c059a55940329ce6258e5">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#1714f6cfcaa3402a285707591252aeef">executed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#9854a67ec5fd25c9d3157e8804af1466">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#64c38ef04b383c6c69e518a99e60a161">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#88f941cf79aa23bafe27fe96700dd37d">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#1d8a8b6191d0fe41e1231def4a86feef">notPredOffThreadsExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#2954814304a5a6cf66eb0684d5d8f0b2">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#c01976cf9f0805878aaa4ab4a85e638a">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#2dbd792bc93cbd93ef8cd552bbe067a4">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html#f6ec4bbd487d71c1769f46fc8f1f8c02">threadsExecuted</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records result for source level instruction execution. (CUPTI_ACTIVITY_KIND_INSTRUCTION_EXECUTION). <hr><h2>Field Documentation</h2>
<a class="anchor" name="59b7a22a0e3c059a55940329ce6258e5"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::correlationId" ref="59b7a22a0e3c059a55940329ce6258e5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#59b7a22a0e3c059a55940329ce6258e5">CUpti_ActivityInstructionExecution::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="1714f6cfcaa3402a285707591252aeef"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::executed" ref="1714f6cfcaa3402a285707591252aeef" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#1714f6cfcaa3402a285707591252aeef">CUpti_ActivityInstructionExecution::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of times this instruction was executed per warp. It will be incremented regardless of predicate or condition code. 
</div>
</div><p>
<a class="anchor" name="9854a67ec5fd25c9d3157e8804af1466"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::flags" ref="9854a67ec5fd25c9d3157e8804af1466" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityInstructionExecution.html#9854a67ec5fd25c9d3157e8804af1466">CUpti_ActivityInstructionExecution::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this instruction execution. 
</div>
</div><p>
<a class="anchor" name="64c38ef04b383c6c69e518a99e60a161"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::functionId" ref="64c38ef04b383c6c69e518a99e60a161" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#64c38ef04b383c6c69e518a99e60a161">CUpti_ActivityInstructionExecution::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="88f941cf79aa23bafe27fe96700dd37d"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::kind" ref="88f941cf79aa23bafe27fe96700dd37d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityInstructionExecution.html#88f941cf79aa23bafe27fe96700dd37d">CUpti_ActivityInstructionExecution::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_INSTRUCTION_EXECUTION. 
</div>
</div><p>
<a class="anchor" name="1d8a8b6191d0fe41e1231def4a86feef"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::notPredOffThreadsExecuted" ref="1d8a8b6191d0fe41e1231def4a86feef" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#1d8a8b6191d0fe41e1231def4a86feef">CUpti_ActivityInstructionExecution::notPredOffThreadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction with predicate and condition code evaluating to true. 
</div>
</div><p>
<a class="anchor" name="2954814304a5a6cf66eb0684d5d8f0b2"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::pad" ref="2954814304a5a6cf66eb0684d5d8f0b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#2954814304a5a6cf66eb0684d5d8f0b2">CUpti_ActivityInstructionExecution::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="c01976cf9f0805878aaa4ab4a85e638a"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::pcOffset" ref="c01976cf9f0805878aaa4ab4a85e638a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#c01976cf9f0805878aaa4ab4a85e638a">CUpti_ActivityInstructionExecution::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the instruction. 
</div>
</div><p>
<a class="anchor" name="2dbd792bc93cbd93ef8cd552bbe067a4"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::sourceLocatorId" ref="2dbd792bc93cbd93ef8cd552bbe067a4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#2dbd792bc93cbd93ef8cd552bbe067a4">CUpti_ActivityInstructionExecution::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="f6ec4bbd487d71c1769f46fc8f1f8c02"></a><!-- doxytag: member="CUpti_ActivityInstructionExecution::threadsExecuted" ref="f6ec4bbd487d71c1769f46fc8f1f8c02" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityInstructionExecution.html#f6ec4bbd487d71c1769f46fc8f1f8c02">CUpti_ActivityInstructionExecution::threadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction, regardless of predicate or condition code. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
