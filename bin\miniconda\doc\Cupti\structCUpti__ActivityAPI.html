<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityAPI Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityAPI Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityAPI" -->The activity record for a driver or runtime API invocation.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#c353a4397e623f75a96124d937be29cd">cbid</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#31ebcf7b922b23850c6c85a9d5157b0d">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#bd3d87d60799bd4f2c9b4c87fea94065">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#183b6eab183ffaca690d4ef96e536489">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#e6ecbca91b11a92aee9bececae95f84b">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#096f5b78b6049d16812114f6af721b72">returnValue</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#390fc4344da529663bfc37745eca0d54">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html#d78f64c5ac657f33face821f25b5d9c1">threadId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents an invocation of a driver or runtime API (CUPTI_ACTIVITY_KIND_DRIVER and CUPTI_ACTIVITY_KIND_RUNTIME). <hr><h2>Field Documentation</h2>
<a class="anchor" name="c353a4397e623f75a96124d937be29cd"></a><!-- doxytag: member="CUpti_ActivityAPI::cbid" ref="c353a4397e623f75a96124d937be29cd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a> <a class="el" href="structCUpti__ActivityAPI.html#c353a4397e623f75a96124d937be29cd">CUpti_ActivityAPI::cbid</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the driver or runtime function. 
</div>
</div><p>
<a class="anchor" name="31ebcf7b922b23850c6c85a9d5157b0d"></a><!-- doxytag: member="CUpti_ActivityAPI::correlationId" ref="31ebcf7b922b23850c6c85a9d5157b0d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityAPI.html#31ebcf7b922b23850c6c85a9d5157b0d">CUpti_ActivityAPI::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the driver or runtime CUDA function. Each function invocation is assigned a unique correlation ID that is identical to the correlation ID in the memcpy, memset, or kernel activity record that is associated with this function. 
</div>
</div><p>
<a class="anchor" name="bd3d87d60799bd4f2c9b4c87fea94065"></a><!-- doxytag: member="CUpti_ActivityAPI::end" ref="bd3d87d60799bd4f2c9b4c87fea94065" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityAPI.html#bd3d87d60799bd4f2c9b4c87fea94065">CUpti_ActivityAPI::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the function, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the function. 
</div>
</div><p>
<a class="anchor" name="183b6eab183ffaca690d4ef96e536489"></a><!-- doxytag: member="CUpti_ActivityAPI::kind" ref="183b6eab183ffaca690d4ef96e536489" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityAPI.html#183b6eab183ffaca690d4ef96e536489">CUpti_ActivityAPI::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_DRIVER, CUPTI_ACTIVITY_KIND_RUNTIME, or CUPTI_ACTIVITY_KIND_INTERNAL_LAUNCH_API. 
</div>
</div><p>
<a class="anchor" name="e6ecbca91b11a92aee9bececae95f84b"></a><!-- doxytag: member="CUpti_ActivityAPI::processId" ref="e6ecbca91b11a92aee9bececae95f84b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityAPI.html#e6ecbca91b11a92aee9bececae95f84b">CUpti_ActivityAPI::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process where the driver or runtime CUDA function is executing. 
</div>
</div><p>
<a class="anchor" name="096f5b78b6049d16812114f6af721b72"></a><!-- doxytag: member="CUpti_ActivityAPI::returnValue" ref="096f5b78b6049d16812114f6af721b72" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityAPI.html#096f5b78b6049d16812114f6af721b72">CUpti_ActivityAPI::returnValue</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The return value for the function. For a CUDA driver function with will be a CUresult value, and for a CUDA runtime function this will be a cudaError_t value. 
</div>
</div><p>
<a class="anchor" name="390fc4344da529663bfc37745eca0d54"></a><!-- doxytag: member="CUpti_ActivityAPI::start" ref="390fc4344da529663bfc37745eca0d54" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityAPI.html#390fc4344da529663bfc37745eca0d54">CUpti_ActivityAPI::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the function, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the function. 
</div>
</div><p>
<a class="anchor" name="d78f64c5ac657f33face821f25b5d9c1"></a><!-- doxytag: member="CUpti_ActivityAPI::threadId" ref="d78f64c5ac657f33face821f25b5d9c1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityAPI.html#d78f64c5ac657f33face821f25b5d9c1">CUpti_ActivityAPI::threadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the thread where the driver or runtime CUDA function is executing. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
