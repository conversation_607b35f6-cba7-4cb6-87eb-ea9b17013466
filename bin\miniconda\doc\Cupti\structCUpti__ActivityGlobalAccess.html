<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityGlobalAccess Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityGlobalAccess Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityGlobalAccess" -->The activity record for source-level global access. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#49fb23371936b61f7c01c43b9a711aa4">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#3e39938b2ade61bddf5f7091762acec2">executed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#a1e24ad05441330453e90245e2dc64fa">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#c1f63fb2ab0f3cea06d73ca8b731a3b1">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#d4727bd5b04cb6fedf75982de05554b9">l2_transactions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#3b8857002223eb82ea24ac13fccd170c">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#bf3e6e00df66291d88018d6d8c54e0e1">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html#59c2b836e5a884774d81193053f9b7b7">threadsExecuted</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records the locations of the global accesses in the source (CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS). Global access activities are now reported using the <a class="el" href="structCUpti__ActivityGlobalAccess3.html" title="The activity record for source-level global access.">CUpti_ActivityGlobalAccess3</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="49fb23371936b61f7c01c43b9a711aa4"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::correlationId" ref="49fb23371936b61f7c01c43b9a711aa4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess.html#49fb23371936b61f7c01c43b9a711aa4">CUpti_ActivityGlobalAccess::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="3e39938b2ade61bddf5f7091762acec2"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::executed" ref="3e39938b2ade61bddf5f7091762acec2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess.html#3e39938b2ade61bddf5f7091762acec2">CUpti_ActivityGlobalAccess::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of times this instruction was executed per warp. It will be incremented when at least one of thread among warp is active with predicate and condition code evaluating to true. 
</div>
</div><p>
<a class="anchor" name="a1e24ad05441330453e90245e2dc64fa"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::flags" ref="a1e24ad05441330453e90245e2dc64fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityGlobalAccess.html#a1e24ad05441330453e90245e2dc64fa">CUpti_ActivityGlobalAccess::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this global access. 
</div>
</div><p>
<a class="anchor" name="c1f63fb2ab0f3cea06d73ca8b731a3b1"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::kind" ref="c1f63fb2ab0f3cea06d73ca8b731a3b1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityGlobalAccess.html#c1f63fb2ab0f3cea06d73ca8b731a3b1">CUpti_ActivityGlobalAccess::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS. 
</div>
</div><p>
<a class="anchor" name="d4727bd5b04cb6fedf75982de05554b9"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::l2_transactions" ref="d4727bd5b04cb6fedf75982de05554b9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess.html#d4727bd5b04cb6fedf75982de05554b9">CUpti_ActivityGlobalAccess::l2_transactions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total number of 32 bytes transactions to L2 cache generated by this access 
</div>
</div><p>
<a class="anchor" name="3b8857002223eb82ea24ac13fccd170c"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::pcOffset" ref="3b8857002223eb82ea24ac13fccd170c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess.html#3b8857002223eb82ea24ac13fccd170c">CUpti_ActivityGlobalAccess::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the access. 
</div>
</div><p>
<a class="anchor" name="bf3e6e00df66291d88018d6d8c54e0e1"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::sourceLocatorId" ref="bf3e6e00df66291d88018d6d8c54e0e1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess.html#bf3e6e00df66291d88018d6d8c54e0e1">CUpti_ActivityGlobalAccess::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="59c2b836e5a884774d81193053f9b7b7"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess::threadsExecuted" ref="59c2b836e5a884774d81193053f9b7b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess.html#59c2b836e5a884774d81193053f9b7b7">CUpti_ActivityGlobalAccess::threadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction with predicate and condition code evaluating to true. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
