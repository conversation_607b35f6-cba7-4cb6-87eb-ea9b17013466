{"arch": "x86_64", "build": "h6c2663c_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\nlohmann_json-3.11.2-h6c2663c_0", "files": ["Library/include/nlohmann/adl_serializer.hpp", "Library/include/nlohmann/byte_container_with_subtype.hpp", "Library/include/nlohmann/detail/abi_macros.hpp", "Library/include/nlohmann/detail/conversions/from_json.hpp", "Library/include/nlohmann/detail/conversions/to_chars.hpp", "Library/include/nlohmann/detail/conversions/to_json.hpp", "Library/include/nlohmann/detail/exceptions.hpp", "Library/include/nlohmann/detail/hash.hpp", "Library/include/nlohmann/detail/input/binary_reader.hpp", "Library/include/nlohmann/detail/input/input_adapters.hpp", "Library/include/nlohmann/detail/input/json_sax.hpp", "Library/include/nlohmann/detail/input/lexer.hpp", "Library/include/nlohmann/detail/input/parser.hpp", "Library/include/nlohmann/detail/input/position_t.hpp", "Library/include/nlohmann/detail/iterators/internal_iterator.hpp", "Library/include/nlohmann/detail/iterators/iter_impl.hpp", "Library/include/nlohmann/detail/iterators/iteration_proxy.hpp", "Library/include/nlohmann/detail/iterators/iterator_traits.hpp", "Library/include/nlohmann/detail/iterators/json_reverse_iterator.hpp", "Library/include/nlohmann/detail/iterators/primitive_iterator.hpp", "Library/include/nlohmann/detail/json_pointer.hpp", "Library/include/nlohmann/detail/json_ref.hpp", "Library/include/nlohmann/detail/macro_scope.hpp", "Library/include/nlohmann/detail/macro_unscope.hpp", "Library/include/nlohmann/detail/meta/call_std/begin.hpp", "Library/include/nlohmann/detail/meta/call_std/end.hpp", "Library/include/nlohmann/detail/meta/cpp_future.hpp", "Library/include/nlohmann/detail/meta/detected.hpp", "Library/include/nlohmann/detail/meta/identity_tag.hpp", "Library/include/nlohmann/detail/meta/is_sax.hpp", "Library/include/nlohmann/detail/meta/std_fs.hpp", "Library/include/nlohmann/detail/meta/type_traits.hpp", "Library/include/nlohmann/detail/meta/void_t.hpp", "Library/include/nlohmann/detail/output/binary_writer.hpp", "Library/include/nlohmann/detail/output/output_adapters.hpp", "Library/include/nlohmann/detail/output/serializer.hpp", "Library/include/nlohmann/detail/string_concat.hpp", "Library/include/nlohmann/detail/string_escape.hpp", "Library/include/nlohmann/detail/value_t.hpp", "Library/include/nlohmann/json.hpp", "Library/include/nlohmann/json_fwd.hpp", "Library/include/nlohmann/ordered_map.hpp", "Library/include/nlohmann/thirdparty/hedley/hedley.hpp", "Library/include/nlohmann/thirdparty/hedley/hedley_undef.hpp", "Library/include/test_data.hpp", "Library/nlohmann_json.natvis", "Library/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake", "Library/share/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake", "Library/share/cmake/nlohmann_json/nlohmann_jsonTargets.cmake", "Library/share/pkgconfig/nlo<PERSON>_json.pc"], "fn": "n<PERSON>hmann_json-3.11.2-h6c2663c_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\nlohmann_json-3.11.2-h6c2663c_0", "type": 1}, "md5": "7533a3f9925b9e46ac0c258be87c2877", "name": "n<PERSON><PERSON>_json", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\nlohmann_json-3.11.2-h6c2663c_0.conda", "paths_data": {"paths": [{"_path": "Library/share/pkgconfig/nlo<PERSON>_json.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_b12svzu_88/croot/n<PERSON><PERSON>_json_1680083453514/_h_env", "sha256": "9a7330237cdc9cb72de838e7bf612cd28b2c3786b8806b2c6ad0a0faa1ecfddf", "sha256_in_prefix": "faf656011bc46179e68ba29a592fde9939d89b044aa8661b6f6d6603f432615c", "size_in_bytes": 160}], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::n<PERSON><PERSON>_json==3.11.2=h6c2663c_0[md5=7533a3f9925b9e46ac0c258be87c2877]", "sha256": "c7981dedae2f3d7734a062f4e07e08a060d462f72c17778df206989a3db42446", "size": 128841, "subdir": "win-64", "timestamp": 1680083671000, "url": "https://repo.anaconda.com/pkgs/main/win-64/nlo<PERSON>_json-3.11.2-h6c2663c_0.conda", "version": "3.11.2"}