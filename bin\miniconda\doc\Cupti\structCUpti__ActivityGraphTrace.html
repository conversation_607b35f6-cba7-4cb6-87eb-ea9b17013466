<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityGraphTrace Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityGraphTrace Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityGraphTrace" -->The activity record for trace of graph execution.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#e434e3b6d0b445397268c8acfb4dbad2">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#8e38e9e4c87cbbc5d0bd13280eb8b7d9">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#fd996abea9f17f3876fecb960f60375b">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#969aec6a37b49295f16a8ffc46f00ef5">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#5feaf917b7be98ec66c54d04c3da83ad">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#c389136326aae4bb3f1e0343c42ea5c8">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#68d4a64ceadf1a6234b79e8c6238c677">reserved</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#63d4edb284a3f1986251988d26ece452">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html#bc75567b79a8f3658387a157d68f3782">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents execution for a graph without giving visibility about the execution of its nodes. This is intended to reduce overheads in tracing each node. The activity kind is CUPTI_ACTIVITY_KIND_GRAPH_TRACE <hr><h2>Field Documentation</h2>
<a class="anchor" name="e434e3b6d0b445397268c8acfb4dbad2"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::contextId" ref="e434e3b6d0b445397268c8acfb4dbad2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGraphTrace.html#e434e3b6d0b445397268c8acfb4dbad2">CUpti_ActivityGraphTrace::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the graph is being launched. 
</div>
</div><p>
<a class="anchor" name="8e38e9e4c87cbbc5d0bd13280eb8b7d9"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::correlationId" ref="8e38e9e4c87cbbc5d0bd13280eb8b7d9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGraphTrace.html#8e38e9e4c87cbbc5d0bd13280eb8b7d9">CUpti_ActivityGraphTrace::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the graph launch. Each graph launch is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the graph. 
</div>
</div><p>
<a class="anchor" name="fd996abea9f17f3876fecb960f60375b"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::deviceId" ref="fd996abea9f17f3876fecb960f60375b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGraphTrace.html#fd996abea9f17f3876fecb960f60375b">CUpti_ActivityGraphTrace::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the graph execution is occurring. 
</div>
</div><p>
<a class="anchor" name="969aec6a37b49295f16a8ffc46f00ef5"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::end" ref="969aec6a37b49295f16a8ffc46f00ef5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGraphTrace.html#969aec6a37b49295f16a8ffc46f00ef5">CUpti_ActivityGraphTrace::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the graph execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the graph. 
</div>
</div><p>
<a class="anchor" name="5feaf917b7be98ec66c54d04c3da83ad"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::graphId" ref="5feaf917b7be98ec66c54d04c3da83ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGraphTrace.html#5feaf917b7be98ec66c54d04c3da83ad">CUpti_ActivityGraphTrace::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that is launched. 
</div>
</div><p>
<a class="anchor" name="c389136326aae4bb3f1e0343c42ea5c8"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::kind" ref="c389136326aae4bb3f1e0343c42ea5c8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityGraphTrace.html#c389136326aae4bb3f1e0343c42ea5c8">CUpti_ActivityGraphTrace::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_GRAPH_TRACE 
</div>
</div><p>
<a class="anchor" name="68d4a64ceadf1a6234b79e8c6238c677"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::reserved" ref="68d4a64ceadf1a6234b79e8c6238c677" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityGraphTrace.html#68d4a64ceadf1a6234b79e8c6238c677">CUpti_ActivityGraphTrace::reserved</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This field is reserved for internal use 
</div>
</div><p>
<a class="anchor" name="63d4edb284a3f1986251988d26ece452"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::start" ref="63d4edb284a3f1986251988d26ece452" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGraphTrace.html#63d4edb284a3f1986251988d26ece452">CUpti_ActivityGraphTrace::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the graph execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the graph. 
</div>
</div><p>
<a class="anchor" name="bc75567b79a8f3658387a157d68f3782"></a><!-- doxytag: member="CUpti_ActivityGraphTrace::streamId" ref="bc75567b79a8f3658387a157d68f3782" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGraphTrace.html#bc75567b79a8f3658387a157d68f3782">CUpti_ActivityGraphTrace::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the graph is being launched. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
