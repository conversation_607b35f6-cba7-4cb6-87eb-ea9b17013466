######################################################################
#
# group: undefined
# id: e68f8226-5994-4d5f-b875-111375834ffb
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/15/2025, 2:25:49 PM (1744752349374)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: e68f8226-5994-4d5f-b875-111375834ffb
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/15/2025, 2:25:49 PM (1744752349657)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 985be1bd-fc27-4e83-87e7-faf618dc31f5
# index: 0
# cmd: dir
# timestamp: 4/15/2025, 2:25:50 PM (1744752350943)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  994,959,134,720 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: cead0aee-5cc1-4ec8-ab1c-a075889b9b96
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/15/2025, 2:25:51 PM (1744752351691)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: b9a9a1f4-e131-4a8e-b445-58359eb6a635
# index: 0
# cmd: dir
# timestamp: 4/15/2025, 2:25:52 PM (1744752352838)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  994,958,557,184 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: f85107f5-b287-4efa-a89b-e20283b3d219
# index: 0
# cmd: dir
# timestamp: 4/15/2025, 2:26:14 PM (1744752374946)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  994,956,333,056 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: ef6718e7-5113-450c-b5d5-761afdd7be52
# index: 0
# cmd: start /wait installer.exe /InstallationType=JustMe /RegisterPython=0 /S /D=C:\pinokio\bin\miniconda
# timestamp: 4/15/2025, 2:26:55 PM (1744752415578)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>start /wait installer.exe /InstallationType=JustMe /RegisterPython=0 /S /D=C:\pinokio\bin\miniconda
Welcome to Miniconda3 py310_25.1.1-2

By continuing this installation you are accepting this license agreement:
C:\pinokio\bin\miniconda\EULA.txt
Please run the installer in GUI mode to read the details.

Miniconda3 will now be installed into this location:
C:\pinokio\bin\miniconda

Unpacking payload...
Setting up the package cache...
Setting up the base environment...
Installing packages for base, creating shortcuts if necessary...
Initializing conda directories...
Running post install...
Done!

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 59595717-8e5b-4dde-8eb9-f297970c7765
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:27:02 PM (1744752422114)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 82 (63.2 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 3 (102 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: e4775b95-d447-4ba0-8528-34c8b1ba3661
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge sqlite=3.47.2
# timestamp: 4/15/2025, 2:27:37 PM (1744752457452)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge sqlite=3.47.2
Retrieving notices: done
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done


==> WARNING: A newer version of conda exists. <==
    current version: 25.1.1
    latest version: 25.3.1

Please update conda by running

    $ conda update -n base -c defaults conda



## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - sqlite=3.47.2


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    ca-certificates-2025.1.31  |       h56e8100_0         155 KB  conda-forge
    conda-25.3.1               |  py310h5588dad_1         891 KB  conda-forge
    libsqlite-3.47.2           |       h67fdade_0         870 KB  conda-forge
    openssl-3.5.0              |       ha4e3fda_0         8.6 MB  conda-forge
    python_abi-3.10            |          2_cp310           4 KB  conda-forge
    sqlite-3.47.2              |       h2466b09_0         894 KB  conda-forge
    ucrt-10.0.22621.0          |       h57928b3_1         547 KB  conda-forge
    vc14_runtime-14.42.34438   |      hfd919c2_26         733 KB  conda-forge
    vs2015_runtime-14.42.34438 |      h7142326_26          17 KB  conda-forge
    ------------------------------------------------------------
                                           Total:        12.6 MB

The following NEW packages will be INSTALLED:

  libsqlite          conda-forge/win-64::libsqlite-3.47.2-h67fdade_0 
  python_abi         conda-forge/win-64::python_abi-3.10-2_cp310 
  ucrt               conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1 
  vc14_runtime       conda-forge/win-64::vc14_runtime-14.42.34438-hfd919c2_26 

The following packages will be UPDATED:

  ca-certificates    pkgs/main::ca-certificates-2024.12.31~ --> conda-forge::ca-certificates-2025.1.31-h56e8100_0 
  conda              pkgs/main::conda-25.1.1-py310haa95532~ --> conda-forge::conda-25.3.1-py310h5588dad_1 
  openssl              pkgs/main::openssl-3.0.15-h827c3e9_0 --> conda-forge::openssl-3.5.0-ha4e3fda_0 
  sqlite                pkgs/main::sqlite-3.45.3-h2bbff1b_0 --> conda-forge::sqlite-3.47.2-h2466b09_0 
  vs2015_runtime     pkgs/main::vs2015_runtime-14.42.34433~ --> conda-forge::vs2015_runtime-14.42.34438-h7142326_26 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done 
 
(base) C:\pinokio\bin> 
 
 
 

######################################################################
#
# group: undefined
# id: 8e7e9a44-80fd-4195-a6e7-30ef08b3e9db
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/15/2025, 2:27:41 PM (1744752461374)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1  
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0  
conda-package-handling    2.4.0           py310haa95532_0  
conda-package-streaming   0.11.0          py310haa95532_0  
cpp-expected              1.1.0                h214f63a_0  
cryptography              43.0.3          py310hbd6ee87_1  
distro                    1.9.0           py310haa95532_0  
fmt                       9.1.0                h6d14046_1  
frozendict                2.4.2           py310h2bbff1b_0  
idna                      3.7             py310haa95532_0  
jsonpatch                 1.33            py310haa95532_1  
jsonpointer               2.1                pyhd3eb1b0_0  
libarchive                3.7.7                h9243413_0  
libcurl                   8.11.1               haff574d_0  
libffi                    3.4.4                hd77b12b_1  
libiconv                  1.16                 h2bbff1b_3  
libmamba                  2.0.5                hcd6fe79_1  
libmambapy                2.0.5           py310h214f63a_1  
libsolv                   0.7.30               hf2fb9eb_1  
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0  
libxml2                   2.13.5               h24da03e_0  
lz4-c                     1.9.4                h2bbff1b_1  
markdown-it-py            2.2.0           py310haa95532_1  
mdurl                     0.1.0           py310haa95532_0  
menuinst                  2.2.0           py310h5da7b33_1  
nlohmann_json             3.11.2               h6c2663c_0  
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0  
pcre2                     10.42                h0ff8eda_1  
pip                       25.0            py310haa95532_0  
platformdirs              3.10.0          py310haa95532_0  
pluggy                    1.5.0           py310haa95532_0  
pybind11-abi              5                    hd3eb1b0_0  
pycosat                   0.6.6           py310h827c3e9_2  
pycparser                 2.21               pyhd3eb1b0_0  
pydantic                  2.10.3          py310haa95532_0  
pydantic-core             2.27.1          py310h636fa0f_0  
pygments                  2.15.1          py310haa95532_1  
pysocks                   1.7.1           py310haa95532_0  
python                    3.10.16              h4607a30_1  
python_abi                3.10                    2_cp310    conda-forge
reproc                    14.2.4               hd77b12b_2  
reproc-cpp                14.2.4               hd77b12b_2  
requests                  2.32.3          py310haa95532_1  
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h8cc25b3_1
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 6850c43c-d3d6-45bf-9bee-76a2663eebff
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:27:45 PM (1744752465609)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 9 (12.6 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 7 (47.4 MB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8f07c4ba-6268-4a1a-a316-0efbe5a63b8c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install --no-shortcuts -y -c conda-forge git git-lfs
# timestamp: 4/15/2025, 2:28:49 PM (1744752529793)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install --no-shortcuts -y -c conda-forge git git-lfs
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - git
    - git-lfs


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    git-2.49.0                 |       h57928b3_0       121.3 MB  conda-forge
    git-lfs-3.6.1              |       h86e1c39_0         3.8 MB  conda-forge
    ------------------------------------------------------------
                                           Total:       125.1 MB

The following NEW packages will be INSTALLED:

  git                conda-forge/win-64::git-2.49.0-h57928b3_0 
  git-lfs            conda-forge/win-64::git-lfs-3.6.1-h86e1c39_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8b2a7cef-cd4d-4515-84f3-fef287b8581b
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:28:53 PM (1744752533970)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 2 (125.1 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 5b3bd7cb-a0a6-4198-b3cd-d318e1df0f5e
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge 7zip
# timestamp: 4/15/2025, 2:29:21 PM (1744752561212)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge 7zip
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - 7zip


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    7zip-24.08                 |       hc790b64_0         1.1 MB  conda-forge
    ------------------------------------------------------------
                                           Total:         1.1 MB

The following NEW packages will be INSTALLED:

  7zip               conda-forge/win-64::7zip-24.08-hc790b64_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 960c58a3-c509-4652-8776-076874e6cf2c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:29:25 PM (1744752565310)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 1 (1.1 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: d8ae8d07-2d8a-4584-8e3f-d6deb3fe5c70
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y nodejs pnpm -c conda-forge
# timestamp: 4/15/2025, 2:29:59 PM (1744752599757)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y nodejs pnpm -c conda-forge
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - nodejs
    - pnpm


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    nodejs-22.13.0             |       hfeaa22a_0        25.0 MB  conda-forge
    pnpm-10.8.1                |       haa868a1_0         3.4 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        28.4 MB

The following NEW packages will be INSTALLED:

  nodejs             conda-forge/win-64::nodejs-22.13.0-hfeaa22a_0 
  pnpm               conda-forge/win-64::pnpm-10.8.1-haa868a1_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 37f16615-9af2-4f73-b3cd-81f9f091657a
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:30:03 PM (1744752603940)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 2 (28.4 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: f66586ab-cf39-45ba-b2df-dac060d904a9
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge ffmpeg
# timestamp: 4/15/2025, 2:30:37 PM (1744752637660)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge ffmpeg
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - ffmpeg


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    ffmpeg-4.3.1               |       ha925a31_0        26.2 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        26.2 MB

The following NEW packages will be INSTALLED:

  ffmpeg             conda-forge/win-64::ffmpeg-4.3.1-ha925a31_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 114b6eaa-78ba-4ec3-aec4-88beecea58ed
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:40:33 PM (1744753233212)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 1 (26.2 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 19f1b023-c4a8-4956-927f-65d316ce6a9f
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cudnn libzlib-wapi -c conda-forge
# timestamp: 4/15/2025, 2:44:35 PM (1744753475289)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cudnn libzlib-wapi -c conda-forge
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - cudnn
    - libzlib-wapi


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    cuda-nvrtc-12.8.93         |       he0c23c2_1        53.9 MB  conda-forge
    cuda-version-12.8          |       h5d125a7_3          21 KB  conda-forge
    cudnn-9.8.0.87             |       h1361d0a_1       450.1 MB  conda-forge
    libcublas-12.8.4.1         |       he0c23c2_1       443.2 MB  conda-forge
    libzlib-1.2.13             |       h2466b09_6          55 KB  conda-forge
    libzlib-wapi-1.2.13        |       h2466b09_6          55 KB  conda-forge
    zlib-1.2.13                |       h2466b09_6         105 KB  conda-forge
    ------------------------------------------------------------
                                           Total:       947.5 MB

The following NEW packages will be INSTALLED:

  cuda-nvrtc         conda-forge/win-64::cuda-nvrtc-12.8.93-he0c23c2_1 
  cuda-version       conda-forge/noarch::cuda-version-12.8-h5d125a7_3 
  cudnn              conda-forge/win-64::cudnn-9.8.0.87-h1361d0a_1 
  libcublas          conda-forge/win-64::libcublas-12.8.4.1-he0c23c2_1 
  libzlib            conda-forge/win-64::libzlib-1.2.13-h2466b09_6 
  libzlib-wapi       conda-forge/win-64::libzlib-wapi-1.2.13-h2466b09_6 

The following packages will be UPDATED:

  zlib                    pkgs/main::zlib-1.2.13-h8cc25b3_1 --> conda-forge::zlib-1.2.13-h2466b09_6 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: / "By downloading and using the cuDNN conda packages, you accept the terms and conditions of the NVIDIA cuDNN EULA - https://docs.nvidia.com/deeplearning/cudnn/sla/index.html" 

done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 169b0486-99d0-4765-a8b6-f5fc18677cf3
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:44:39 PM (1744753479880)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 7 (947.5 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 2 (736 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 0eab47e6-04b0-498b-83ec-46942054acae
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cuda -c nvidia/label/cuda-12.1.0
# timestamp: 4/15/2025, 2:50:47 PM (1744753847593)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cuda -c nvidia/label/cuda-12.1.0
Channels:
 - nvidia/label/cuda-12.1.0
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - cuda


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    cuda-12.1.0                |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-cccl-12.1.55          |                0         1.2 MB  nvidia/label/cuda-12.1.0
    cuda-command-line-tools-12.1.0|                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-compiler-12.1.0       |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-cudart-12.1.55        |                0         965 KB  nvidia/label/cuda-12.1.0
    cuda-cudart-dev-12.1.55    |                0         547 KB  nvidia/label/cuda-12.1.0
    cuda-cuobjdump-12.1.55     |                0         3.7 MB  nvidia/label/cuda-12.1.0
    cuda-cupti-12.1.62         |                0        11.6 MB  nvidia/label/cuda-12.1.0
    cuda-cuxxfilt-12.1.55      |                0         163 KB  nvidia/label/cuda-12.1.0
    cuda-demo-suite-12.1.55    |                0         4.7 MB  nvidia/label/cuda-12.1.0
    cuda-documentation-12.1.55 |                0          89 KB  nvidia/label/cuda-12.1.0
    cuda-libraries-12.1.0      |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-libraries-dev-12.1.0  |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-nsight-compute-12.1.0 |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-nvcc-12.1.66          |                0        55.3 MB  nvidia/label/cuda-12.1.0
    cuda-nvdisasm-12.1.55      |                0        48.0 MB  nvidia/label/cuda-12.1.0
    cuda-nvml-dev-12.1.55      |                0          88 KB  nvidia/label/cuda-12.1.0
    cuda-nvprof-12.1.55        |                0         1.6 MB  nvidia/label/cuda-12.1.0
    cuda-nvprune-12.1.55       |                0         150 KB  nvidia/label/cuda-12.1.0
    cuda-nvrtc-dev-12.1.55     |                0        16.5 MB  nvidia/label/cuda-12.1.0
    cuda-nvtx-12.1.66          |                0          41 KB  nvidia/label/cuda-12.1.0
    cuda-nvvp-12.1.55          |                0       113.6 MB  nvidia/label/cuda-12.1.0
    cuda-opencl-12.1.56        |                0          10 KB  nvidia/label/cuda-12.1.0
    cuda-opencl-dev-12.1.56    |                0          60 KB  nvidia/label/cuda-12.1.0
    cuda-profiler-api-12.1.55  |                0          18 KB  nvidia/label/cuda-12.1.0
    cuda-runtime-12.1.0        |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-sanitizer-api-12.1.55 |                0        12.9 MB  nvidia/label/cuda-12.1.0
    cuda-toolkit-12.1.0        |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-tools-12.1.0          |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-visual-tools-12.1.0   |                0           1 KB  nvidia/label/cuda-12.1.0
    libcublas-dev-12.1.0.26    |                0       348.3 MB  nvidia/label/cuda-12.1.0
    libcufft-11.0.2.4          |                0           6 KB  nvidia/label/cuda-12.1.0
    libcufft-dev-11.0.2.4      |                0       102.6 MB  nvidia/label/cuda-12.1.0
    libcurand-10.3.2.56        |                0           3 KB  nvidia/label/cuda-12.1.0
    libcurand-dev-10.3.2.56    |                0        50.0 MB  nvidia/label/cuda-12.1.0
    libcusolver-11.4.4.55      |                0          30 KB  nvidia/label/cuda-12.1.0
    libcusolver-dev-11.4.4.55  |                0        95.7 MB  nvidia/label/cuda-12.1.0
    libcusparse-12.0.2.55      |                0          12 KB  nvidia/label/cuda-12.1.0
    libcusparse-dev-12.0.2.55  |                0       162.5 MB  nvidia/label/cuda-12.1.0
    libnpp-12.0.2.50           |                0         305 KB  nvidia/label/cuda-12.1.0
    libnpp-dev-12.0.2.50       |                0       135.6 MB  nvidia/label/cuda-12.1.0
    libnvjitlink-12.1.55       |                0        67.3 MB  nvidia/label/cuda-12.1.0
    libnvjitlink-dev-12.1.55   |                0        13.8 MB  nvidia/label/cuda-12.1.0
    libnvjpeg-12.1.0.39        |                0           5 KB  nvidia/label/cuda-12.1.0
    libnvjpeg-dev-12.1.0.39    |                0         2.0 MB  nvidia/label/cuda-12.1.0
    libnvvm-samples-12.1.55    |                0          32 KB  nvidia/label/cuda-12.1.0
    nsight-compute-2023.1.0.15 |                0       601.8 MB  nvidia/label/cuda-12.1.0
    ------------------------------------------------------------
                                           Total:        1.81 GB

The following NEW packages will be INSTALLED:

  cuda               nvidia/label/cuda-12.1.0/win-64::cuda-12.1.0-0
  cuda-cccl          nvidia/label/cuda-12.1.0/win-64::cuda-cccl-12.1.55-0
  cuda-command-line~ nvidia/label/cuda-12.1.0/win-64::cuda-command-line-tools-12.1.0-0
  cuda-compiler      nvidia/label/cuda-12.1.0/win-64::cuda-compiler-12.1.0-0
  cuda-cudart        nvidia/label/cuda-12.1.0/win-64::cuda-cudart-12.1.55-0
  cuda-cudart-dev    nvidia/label/cuda-12.1.0/win-64::cuda-cudart-dev-12.1.55-0
  cuda-cuobjdump     nvidia/label/cuda-12.1.0/win-64::cuda-cuobjdump-12.1.55-0
  cuda-cupti         nvidia/label/cuda-12.1.0/win-64::cuda-cupti-12.1.62-0
  cuda-cuxxfilt      nvidia/label/cuda-12.1.0/win-64::cuda-cuxxfilt-12.1.55-0
  cuda-demo-suite    nvidia/label/cuda-12.1.0/win-64::cuda-demo-suite-12.1.55-0
  cuda-documentation nvidia/label/cuda-12.1.0/win-64::cuda-documentation-12.1.55-0
  cuda-libraries     nvidia/label/cuda-12.1.0/win-64::cuda-libraries-12.1.0-0
  cuda-libraries-dev nvidia/label/cuda-12.1.0/win-64::cuda-libraries-dev-12.1.0-0
  cuda-nsight-compu~ nvidia/label/cuda-12.1.0/win-64::cuda-nsight-compute-12.1.0-0
  cuda-nvcc          nvidia/label/cuda-12.1.0/win-64::cuda-nvcc-12.1.66-0
  cuda-nvdisasm      nvidia/label/cuda-12.1.0/win-64::cuda-nvdisasm-12.1.55-0
  cuda-nvml-dev      nvidia/label/cuda-12.1.0/win-64::cuda-nvml-dev-12.1.55-0
  cuda-nvprof        nvidia/label/cuda-12.1.0/win-64::cuda-nvprof-12.1.55-0
  cuda-nvprune       nvidia/label/cuda-12.1.0/win-64::cuda-nvprune-12.1.55-0
  cuda-nvrtc-dev     nvidia/label/cuda-12.1.0/win-64::cuda-nvrtc-dev-12.1.55-0
  cuda-nvtx          nvidia/label/cuda-12.1.0/win-64::cuda-nvtx-12.1.66-0
  cuda-nvvp          nvidia/label/cuda-12.1.0/win-64::cuda-nvvp-12.1.55-0
  cuda-opencl        nvidia/label/cuda-12.1.0/win-64::cuda-opencl-12.1.56-0
  cuda-opencl-dev    nvidia/label/cuda-12.1.0/win-64::cuda-opencl-dev-12.1.56-0
  cuda-profiler-api  nvidia/label/cuda-12.1.0/win-64::cuda-profiler-api-12.1.55-0
  cuda-runtime       nvidia/label/cuda-12.1.0/win-64::cuda-runtime-12.1.0-0
  cuda-sanitizer-api nvidia/label/cuda-12.1.0/win-64::cuda-sanitizer-api-12.1.55-0
  cuda-toolkit       nvidia/label/cuda-12.1.0/win-64::cuda-toolkit-12.1.0-0
  cuda-tools         nvidia/label/cuda-12.1.0/win-64::cuda-tools-12.1.0-0
  cuda-visual-tools  nvidia/label/cuda-12.1.0/win-64::cuda-visual-tools-12.1.0-0
  libcublas-dev      nvidia/label/cuda-12.1.0/win-64::libcublas-dev-12.1.0.26-0
  libcufft           nvidia/label/cuda-12.1.0/win-64::libcufft-11.0.2.4-0
  libcufft-dev       nvidia/label/cuda-12.1.0/win-64::libcufft-dev-11.0.2.4-0
  libcurand          nvidia/label/cuda-12.1.0/win-64::libcurand-10.3.2.56-0
  libcurand-dev      nvidia/label/cuda-12.1.0/win-64::libcurand-dev-10.3.2.56-0
  libcusolver        nvidia/label/cuda-12.1.0/win-64::libcusolver-11.4.4.55-0
  libcusolver-dev    nvidia/label/cuda-12.1.0/win-64::libcusolver-dev-11.4.4.55-0
  libcusparse        nvidia/label/cuda-12.1.0/win-64::libcusparse-12.0.2.55-0
  libcusparse-dev    nvidia/label/cuda-12.1.0/win-64::libcusparse-dev-12.0.2.55-0
  libnpp             nvidia/label/cuda-12.1.0/win-64::libnpp-12.0.2.50-0
  libnpp-dev         nvidia/label/cuda-12.1.0/win-64::libnpp-dev-12.0.2.50-0
  libnvjitlink       nvidia/label/cuda-12.1.0/win-64::libnvjitlink-12.1.55-0
  libnvjitlink-dev   nvidia/label/cuda-12.1.0/win-64::libnvjitlink-dev-12.1.55-0
  libnvjpeg          nvidia/label/cuda-12.1.0/win-64::libnvjpeg-12.1.0.39-0
  libnvjpeg-dev      nvidia/label/cuda-12.1.0/win-64::libnvjpeg-dev-12.1.0.39-0
  libnvvm-samples    nvidia/label/cuda-12.1.0/win-64::libnvvm-samples-12.1.55-0
  nsight-compute     nvidia/label/cuda-12.1.0/win-64::nsight-compute-2023.1.0.15-0



Downloading and Extracting Packages:

Preparing transaction: done 
Verifying transaction: done 
Executing transaction: done 
 
(base) C:\pinokio\bin>
 
 
 
 
 
 
 
 
 
 
 
 
 
 

 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

 
 
 
 
 
 
 
 
 

######################################################################
#
# group: undefined
# id: 5e87bc2a-b759-4c0c-aaf5-9d97e63b793c
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/pinokiocomputer/python py
# timestamp: 4/15/2025, 2:50:52 PM (1744753852470)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/pinokiocomputer/python py
Cloning into 'py'...
remote: Enumerating objects: 47, done.
remote: Counting objects: 100% (47/47), done.
remote: Compressing objects: 100% (34/34), done.
remote: Total 47 (delta 13), reused 46 (delta 12), pack-reused 0 (from 0)
Receiving objects: 100% (47/47), 12.83 KiB | 453.00 KiB/s, done.
Resolving deltas: 100% (13/13), done.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8d913a2a-1046-4e38-84dd-a868964df4ba
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && python -m venv C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\deactivate && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && pip install -r requirements.txt
# timestamp: 4/15/2025, 2:51:16 PM (1744753876969)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin\py>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && python -m venv C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\deactivate && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && pip install -r requirements.txt
Collecting importlib_metadata
  Downloading importlib_metadata-8.6.1-py3-none-any.whl (26 kB)
Collecting fastapi
  Downloading fastapi-0.115.12-py3-none-any.whl (95 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 95.2/95.2 kB 5.3 MB/s eta 0:00:00
Collecting uvicorn[standard]
  Downloading uvicorn-0.34.1-py3-none-any.whl (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.4/62.4 kB 3.5 MB/s eta 0:00:00
Collecting zipp>=3.20
  Downloading zipp-3.21.0-py3-none-any.whl (9.6 kB)
Collecting starlette<0.47.0,>=0.40.0
  Downloading starlette-0.46.2-py3-none-any.whl (72 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 72.0/72.0 kB 3.9 MB/s eta 0:00:00
Collecting pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4
  Downloading pydantic-2.11.3-py3-none-any.whl (443 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 443.6/443.6 kB 5.6 MB/s eta 0:00:00
Collecting typing-extensions>=4.8.0
  Downloading typing_extensions-4.13.2-py3-none-any.whl (45 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 45.8/45.8 kB 2.2 MB/s eta 0:00:00
Collecting h11>=0.8
  Downloading h11-0.14.0-py3-none-any.whl (58 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 58.3/58.3 kB ? eta 0:00:00
Collecting click>=7.0
  Downloading click-8.1.8-py3-none-any.whl (98 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 98.2/98.2 kB 5.5 MB/s eta 0:00:00
Collecting httptools>=0.6.3
  Downloading httptools-0.6.4-cp310-cp310-win_amd64.whl (88 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 88.3/88.3 kB 5.2 MB/s eta 0:00:00
Collecting python-dotenv>=0.13
  Downloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)
Collecting websockets>=10.4
  Downloading websockets-15.0.1-cp310-cp310-win_amd64.whl (176 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 176.8/176.8 kB 3.5 MB/s eta 0:00:00
Collecting pyyaml>=5.1
  Downloading PyYAML-6.0.2-cp310-cp310-win_amd64.whl (161 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 161.8/161.8 kB 4.9 MB/s eta 0:00:00
Collecting colorama>=0.4
  Downloading colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Collecting watchfiles>=0.13
  Downloading watchfiles-1.0.5-cp310-cp310-win_amd64.whl (291 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 291.5/291.5 kB 4.5 MB/s eta 0:00:00
Collecting typing-inspection>=0.4.0
  Downloading typing_inspection-0.4.0-py3-none-any.whl (14 kB)
Collecting pydantic-core==2.33.1
  Downloading pydantic_core-2.33.1-cp310-cp310-win_amd64.whl (2.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 5.4 MB/s eta 0:00:00
Collecting annotated-types>=0.6.0
  Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Collecting anyio<5,>=3.6.2
  Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100.9/100.9 kB 6.0 MB/s eta 0:00:00
Collecting exceptiongroup>=1.0.2
  Downloading exceptiongroup-1.2.2-py3-none-any.whl (16 kB)
Collecting idna>=2.8
  Downloading idna-3.10-py3-none-any.whl (70 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 70.4/70.4 kB 4.0 MB/s eta 0:00:00
Collecting sniffio>=1.1
  Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Installing collected packages: zipp, websockets, typing-extensions, sniffio, pyyaml, python-dotenv, idna, httptools, h11, exceptiongroup, colorama, annotated-types, typing-inspection, pydantic-core, importlib_metadata, click, anyio, watchfiles, uvicorn, starlette, pydantic, fastapi
Successfully installed annotated-types-0.7.0 anyio-4.9.0 click-8.1.8 colorama-0.4.6 exceptiongroup-1.2.2 fastapi-0.115.12 h11-0.14.0 httptools-0.6.4 idna-3.10 importlib_metadata-8.6.1 pydantic-2.11.3 pydantic-core-2.33.1 python-dotenv-1.1.0 pyyaml-6.0.2 sniffio-1.3.1 starlette-0.46.2 typing-extensions-4.13.2 typing-inspection-0.4.0 uvicorn-0.34.1 watchfiles-1.0.5 websockets-15.0.1 zipp-3.21.0     

[notice] A new release of pip is available: 23.0.1 -> 25.0.1
[notice] To update, run: python.exe -m pip install --upgrade pip

(env) (base) C:\pinokio\bin\py>

######################################################################
#
# group: undefined
# id: c07f325a-facc-45ea-8922-b2899b8a7df3
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && npm init -y playwright@latest -- --quiet
# timestamp: 4/15/2025, 2:53:34 PM (1744754014864)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin\playwright>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && npm init -y playwright@latest -- --quiet

> npx
> create-playwright --quiet

Getting started with writing end-to-end tests with Playwright:
Initializing project in '.'
Initializing NPM project (npm init -y)…
Wrote to C:\pinokio\bin\playwright\package.json:

{
  "name": "playwright",
  "version": "1.0.0",
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "description": ""
}



Installing Playwright Test (npm install --save-dev @playwright/test)…

added 3 packages, and audited 4 packages in 2s

found 0 vulnerabilities
Installing Types (npm install --save-dev @types/node)…

added 2 packages, and audited 6 packages in 821ms

found 0 vulnerabilities
Writing playwright.config.ts.
Writing tests\example.spec.ts.
Writing tests-examples\demo-todo-app.spec.ts.
Writing package.json.
Downloading browsers (npx playwright install)…
Downloading Chromium 134.0.6998.35 (playwright build v1161) from https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1161/chromium-win64.zip
141.8 MiB [====================] 100% 0.0s
Chromium 134.0.6998.35 (playwright build v1161) downloaded to C:\pinokio\bin\playwright\browsers\chromium-1161
Downloading Chromium Headless Shell 134.0.6998.35 (playwright build v1161) from https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1161/chromium-headless-shell-win64.zip
87.8 MiB [====================] 100% 0.0s
Chromium Headless Shell 134.0.6998.35 (playwright build v1161) downloaded to C:\pinokio\bin\playwright\browsers\chromium_headless_shell-1161
Downloading Firefox 135.0 (playwright build v1475) from https://cdn.playwright.dev/dbazure/download/playwright/builds/firefox/1475/firefox-win64.zip
91.5 MiB [====================] 100% 0.0s
Firefox 135.0 (playwright build v1475) downloaded to C:\pinokio\bin\playwright\browsers\firefox-1475
Downloading Webkit 18.4 (playwright build v2140) from https://cdn.playwright.dev/dbazure/download/playwright/builds/webkit/2140/webkit-win64.zip
52.8 MiB [====================] 100% 0.0s
Webkit 18.4 (playwright build v2140) downloaded to C:\pinokio\bin\playwright\browsers\webkit-2140
Downloading FFMPEG playwright build v1011 from https://cdn.playwright.dev/dbazure/download/playwright/builds/ffmpeg/1011/ffmpeg-win64.zip
1.3 MiB [====================] 100% 0.0s
FFMPEG playwright build v1011 downloaded to C:\pinokio\bin\playwright\browsers\ffmpeg-1011
Downloading Winldd playwright build v1007 from https://cdn.playwright.dev/dbazure/download/playwright/builds/winldd/1007/winldd-win64.zip
0.1 MiB [====================] 100% 0.0s
Winldd playwright build v1007 downloaded to C:\pinokio\bin\playwright\browsers\winldd-1007
✔ Success! Created a Playwright Test project at C:\pinokio\bin\playwright

Inside that directory, you can run several commands:

  npx playwright test
    Runs the end-to-end tests.

  npx playwright test --ui
    Starts the interactive UI mode.

  npx playwright test --project=chromium
    Runs the tests only on Desktop Chrome.

  npx playwright test example
    Runs the tests in a specific file.

  npx playwright test --debug
    Runs the tests in debug mode.

  npx playwright codegen
    Auto generate tests with Codegen.

We suggest that you begin by typing:

    npx playwright test

And check out the following files:
  - .\tests\example.spec.ts - Example end-to-end test
  - .\tests-examples\demo-todo-app.spec.ts - Demo Todo App end-to-end tests
  - .\playwright.config.ts - Playwright Test configuration

Visit https://playwright.dev/docs/intro for more information. ✨

Happy hacking! 🎭

(base) C:\pinokio\bin\playwright>

######################################################################
#
# group: undefined
# id: 0095ab04-0583-41b4-83ab-d1e923e5f4b0
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:53:39 PM (1744754019809)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 47 (1.81 GB) tarball(s).
Will remove 1 index cache(s).
Will remove 10 (40 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: b78b4db9-a2e3-478e-ac1b-58011f04c523
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge huggingface_hub
# timestamp: 4/15/2025, 2:54:11 PM (1744754051099)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge huggingface_hub
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - huggingface_hub


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    filelock-3.18.0            |     pyhd8ed1ab_0          17 KB  conda-forge
    fsspec-2025.3.2            |     pyhd8ed1ab_0         139 KB  conda-forge
    huggingface_hub-0.30.2     |     pyhd8ed1ab_0         290 KB  conda-forge
    pyyaml-6.0.2               |  py310h38315fa_2         154 KB  conda-forge
    yaml-0.2.5                 |       h8ffe710_2          62 KB  conda-forge
    ------------------------------------------------------------
                                           Total:         662 KB

The following NEW packages will be INSTALLED:

  filelock           conda-forge/noarch::filelock-3.18.0-pyhd8ed1ab_0 
  fsspec             conda-forge/noarch::fsspec-2025.3.2-pyhd8ed1ab_0 
  huggingface_hub    conda-forge/noarch::huggingface_hub-0.30.2-pyhd8ed1ab_0 
  pyyaml             conda-forge/win-64::pyyaml-6.0.2-py310h38315fa_2 
  yaml               conda-forge/win-64::yaml-0.2.5-h8ffe710_2 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: afacf050-32d6-4fc9-a577-3b3d0e0208e4
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 4/15/2025, 2:54:15 PM (1744754055297)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 5 (662 KB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: c8ea7b98-883a-4ed1-83d7-8e2909bc71e3
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge uv
# timestamp: 4/15/2025, 2:54:44 PM (1744754084767)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge uv
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - uv


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    uv-0.6.14                  |       ha08ef0e_0        12.1 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        12.1 MB

The following NEW packages will be INSTALLED:

  uv                 conda-forge/win-64::uv-0.6.14-ha08ef0e_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: a43e6dce-5990-4663-8f89-f18ebc6f3326
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/15/2025, 2:54:48 PM (1744754088759)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1  
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0  
conda-package-handling    2.4.0           py310haa95532_0  
conda-package-streaming   0.11.0          py310haa95532_0  
cpp-expected              1.1.0                h214f63a_0  
cryptography              43.0.3          py310hbd6ee87_1  
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0  
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1  
frozendict                2.4.2           py310h2bbff1b_0  
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: d97d876a-c46b-4a86-b049-fd6ad2f33215
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/15/2025, 2:54:53 PM (1744754093488)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1  
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0  
conda-package-handling    2.4.0           py310haa95532_0  
conda-package-streaming   0.11.0          py310haa95532_0  
cpp-expected              1.1.0                h214f63a_0  
cryptography              43.0.3          py310hbd6ee87_1  
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0  
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1  
frozendict                2.4.2           py310h2bbff1b_0  
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: cde15c3e-6a98-4840-b9a7-44de0b3bc11a
# index: 1
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/15/2025, 2:54:54 PM (1744754094652)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 6162f66a-78af-47f3-ab70-dbd3d2f9bbb5
# index: 1
# cmd: dir
# timestamp: 4/15/2025, 2:55:00 PM (1744754100391)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  979,090,280,448 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: d6a38a80-c9d2-46cc-a2bc-92d3dc98df58
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
# timestamp: 4/15/2025, 2:55:04 PM (1744754104505)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
Cloning into 'facefusion-pinokio.git'...
remote: Enumerating objects: 434, done.
remote: Counting objects: 100% (192/192), done.
remote: Compressing objects: 100% (61/61), done.
remote: Total 434 (delta 140), reused 135 (delta 131), pack-reused 242 (from 2)
Receiving objects: 100% (434/434), 321.30 KiB | 2.11 MiB/s, done.
Resolving deltas: 100% (271/271), done.

(base) C:\pinokio\api>

######################################################################
#
# group: undefined
# id: 4f69d5d7-42f1-4740-8e7d-e69fc5b4edb5
# index: 1
# cmd: dir
# timestamp: 4/15/2025, 2:55:12 PM (1744754112169)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  979,087,998,976 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: b2b9133c-9be5-4f10-ac25-bff049b4ad6e
# index: 1
# cmd: dir
# timestamp: 4/15/2025, 3:13:41 PM (1744755221018)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  968,612,413,440 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: e51a58f4-b942-404f-92e1-72b6367ff3e2
# index: 1
# cmd: dir
# timestamp: 4/15/2025, 3:18:46 PM (1744755526834)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  968,583,667,712 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 4bdc6781-e64e-4f0d-ba8d-a09dbe06e3b3
# index: 2
# cmd: dir
# timestamp: 4/17/2025, 3:30:36 AM (1744885836587)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,240,003,895,296 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: df8a6846-f519-436c-a25f-78c8ff481c47
# index: 1
# cmd: dir
# timestamp: 4/17/2025, 3:30:44 AM (1744885844789)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,240,003,387,392 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 40ca7aa1-c380-4d91-950f-460acd2d839d
# index: 2
# cmd: dir
# timestamp: 4/17/2025, 3:46:41 AM (1744886801683)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,239,977,390,080 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 0f37ae3e-5a51-4016-a015-d7e02e3ed950
# index: 1
# cmd: dir
# timestamp: 4/17/2025, 6:09:11 AM (1744895351340)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,840,744,960 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: fdbd7c42-0faa-432a-9af0-a217580e0c19
# index: 2
# cmd: dir
# timestamp: 4/17/2025, 6:14:39 AM (1744895679458)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,840,921,088 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 38e8f73e-318e-4046-8b56-9cd090015bd7
# index: 0
# cmd: undefined
# timestamp: 4/17/2025, 6:14:40 AM (1744895680718)



######################################################################
#
# group: undefined
# id: b121f377-db18-40e5-bf30-0ef20ea2f888
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 4/17/2025, 6:14:51 AM (1744895691650)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_22154.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_22154.txt
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_28330.txt.
Failed to run 'conda activate base'.
C:\pinokio\cache\TEMP\__conda_tmp_28330.txt
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_SHLVL=0
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_8072=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin\;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs\;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=$P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\Scripts\python
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 154e0b66-4de3-4871-b9a2-79a8f04c5378
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/17/2025, 6:14:55 AM (1744895695530)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_2095.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_2095.txt
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_1654.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_1654.txt
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0
conda-anaconda-tos        0.1.2           py310haa95532_0
conda-content-trust       0.2.0           py310haa95532_1
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0
conda-package-handling    2.4.0           py310haa95532_0
conda-package-streaming   0.11.0          py310haa95532_0
cpp-expected              1.1.0                h214f63a_0
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 08dbf736-f9d6-4ef4-97b3-775416a643b3
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/17/2025, 6:14:56 AM (1744895696669)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 2bec61c3-dd10-4ace-b4ca-36f48356d487
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 6:14:57 AM (1744895697805)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,844,455,936 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: e936a382-a622-475f-bba3-c65c95945bd7
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 6:15:03 AM (1744895703604)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,843,534,336 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 0c21d2bf-1cf3-4e28-9517-6c880dd6c8a6
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 4/17/2025, 6:16:21 AM (1744895781863)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_6471.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_6471.txt
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_31341.txt.
Failed to run 'conda activate base'.
C:\pinokio\cache\TEMP\__conda_tmp_31341.txt
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_SHLVL=0
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_8072=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin\;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs\;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=$P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\Scripts\python
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: d63171ef-a608-4a47-9699-0364a055daaf
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/17/2025, 6:16:22 AM (1744895782966)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_2389.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_2389.txt
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_3797.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_3797.txt
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0
conda-anaconda-tos        0.1.2           py310haa95532_0
conda-content-trust       0.2.0           py310haa95532_1
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0
conda-package-handling    2.4.0           py310haa95532_0
conda-package-streaming   0.11.0          py310haa95532_0
cpp-expected              1.1.0                h214f63a_0
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 204975b2-bcbd-491e-beb3-81d94a26dae8
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/17/2025, 6:16:24 AM (1744895784118)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 281898bf-e5d7-4552-a916-ee1e8df10970
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 6:16:25 AM (1744895785254)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,840,712,192 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 8c4153a4-4e1d-4c6a-a675-45870bb4c77a
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 6:16:27 AM (1744895787200)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,839,909,376 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 3f4053ce-d73d-41a4-8bde-65aa2d068d4a
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 6:17:33 AM (1744895853479)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,840,388,608 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: c4e16211-f242-4240-b34c-17406f09def7
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 6:17:34 AM (1744895854861)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,838,840,320 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 0e996702-03ff-4337-a00e-e05d84c63566
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 4/17/2025, 12:10:45 PM (1744917045326)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
CONDA_SHLVL=1
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_8072=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda;C:\pinokio\bin\miniconda\Library\mingw64\bin;C:\pinokio\bin\miniconda\Library\mingw-w64\bin;C:\pinokio\bin\miniconda\Library\usr\bin;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=(base) $P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\pinokio\bin\miniconda\Library\ssl\certs
SSL_CERT_FILE=C:\pinokio\bin\miniconda\Library\ssl\cacert.pem
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed
__CONDA_OPENSSL_CERT_DIR_SET=1
__CONDA_OPENSSL_CERT_FILE_SET=1

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 6bddbfbb-38af-486b-8127-b21c3fcc00c8
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/17/2025, 12:10:46 PM (1744917046437)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1  
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0  
conda-package-handling    2.4.0           py310haa95532_0  
conda-package-streaming   0.11.0          py310haa95532_0  
cpp-expected              1.1.0                h214f63a_0  
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 50c6abe0-c4b0-41b7-b178-b629046046da
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/17/2025, 12:10:47 PM (1744917047586)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 204f7ae7-ca1f-44b3-a4ce-f6fa5c0b2509
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 12:10:48 PM (1744917048719)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,945,704,960 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 2c77d2e0-d511-4c20-bc84-32d05393ca9a
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 12:10:54 PM (1744917054241)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,944,742,400 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: dbd2dd10-da6f-4e4d-ba74-1f28ab2d6495
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 4/17/2025, 12:12:03 PM (1744917123617)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_22960.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_22960.txt
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_17718.txt.
Failed to run 'conda activate base'.
C:\pinokio\cache\TEMP\__conda_tmp_17718.txt
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_SHLVL=0
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_8072=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin\;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs\;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=$P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\Scripts\python
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: e2daabc1-44a8-4fa3-ae75-a4317c389af8
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/17/2025, 12:12:04 PM (1744917124700)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_6547.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_6547.txt
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_5264.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_5264.txt
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0
conda-anaconda-tos        0.1.2           py310haa95532_0
conda-content-trust       0.2.0           py310haa95532_1
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0
conda-package-handling    2.4.0           py310haa95532_0
conda-package-streaming   0.11.0          py310haa95532_0
cpp-expected              1.1.0                h214f63a_0
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: ac35e950-c58d-4ad5-966b-efca1984ab82
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/17/2025, 12:12:05 PM (1744917125837)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: edf2da5f-693f-46ec-b49f-8d2cecffef2b
# index: 0
# cmd: dir
# timestamp: 4/17/2025, 12:12:06 PM (1744917126976)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,939,876,352 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 0b5a96ac-af38-4bad-8514-214e9cf1248d
# index: 1
# cmd: dir
# timestamp: 4/17/2025, 12:12:08 PM (1744917128026)

Microsoft Windows [Version 10.0.22631.4890]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,224,939,020,288 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: eda1fe85-c639-4bd7-a53f-3304af68cfe2
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 4/18/2025, 5:42:51 AM (1744980171236)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
CONDA_SHLVL=1
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_9812=1
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda;C:\pinokio\bin\miniconda\Library\mingw64\bin;C:\pinokio\bin\miniconda\Library\mingw-w64\bin;C:\pinokio\bin\miniconda\Library\usr\bin;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=(base) $P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\pinokio\bin\miniconda\Library\ssl\certs
SSL_CERT_FILE=C:\pinokio\bin\miniconda\Library\ssl\cacert.pem
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed
__CONDA_OPENSSL_CERT_DIR_SET=1
__CONDA_OPENSSL_CERT_FILE_SET=1

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 14b29361-552d-4c1b-9307-5db54d001840
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/18/2025, 5:42:55 AM (1744980175697)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1  
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0  
conda-package-handling    2.4.0           py310haa95532_0  
conda-package-streaming   0.11.0          py310haa95532_0  
cpp-expected              1.1.0                h214f63a_0  
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: c35915ae-cac7-40df-ad3d-b8f97e04874c
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/18/2025, 5:42:56 AM (1744980176884)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: c6af966b-531b-4bb5-92e6-fe78638ac929
# index: 0
# cmd: dir
# timestamp: 4/18/2025, 5:42:58 AM (1744980178074)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,223,325,011,968 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 0e0b758d-53e0-4136-b9c5-9c45862356df
# index: 0
# cmd: dir
# timestamp: 4/18/2025, 5:43:01 AM (1744980181742)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,223,324,012,544 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: ab301c52-aa72-4267-af61-dd0d3cbb1918
# index: 1
# cmd: dir
# timestamp: 4/20/2025, 5:51:59 PM (1745196719862)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,237,007,970,304 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 2bcb6643-06c2-40ea-a458-f53a0b75eff0
# index: 0
# cmd: dir
# timestamp: 4/20/2025, 5:52:07 PM (1745196727988)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,237,006,905,344 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 56089655-b58f-4c04-8d4e-eedffe1044ea
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 4/24/2025, 5:16:48 PM (1745540208457)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
CONDA_SHLVL=1
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_3204=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda;C:\pinokio\bin\miniconda\Library\mingw64\bin;C:\pinokio\bin\miniconda\Library\mingw-w64\bin;C:\pinokio\bin\miniconda\Library\usr\bin;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=(base) $P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\pinokio\bin\miniconda\Library\ssl\certs
SSL_CERT_FILE=C:\pinokio\bin\miniconda\Library\ssl\cacert.pem
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed
__CONDA_OPENSSL_CERT_DIR_SET=1
__CONDA_OPENSSL_CERT_FILE_SET=1

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 14b4f079-0e2c-4ee0-a655-71b17b44de80
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 4/24/2025, 5:16:52 PM (1745540212547)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_9654.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_9654.txt
C:\pinokio\cache\TEMP\__conda_tmp_10984.txt
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0
conda-package-handling    2.4.0           py310haa95532_0
conda-package-streaming   0.11.0          py310haa95532_0
cpp-expected              1.1.0                h214f63a_0
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 3f5dd669-579e-4561-8143-353cbc9d945b
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 4/24/2025, 5:16:53 PM (1745540213686)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: fd7a7602-6229-4907-860d-1a00842fd733
# index: 0
# cmd: dir
# timestamp: 4/24/2025, 5:16:54 PM (1745540214827)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,192,659,722,240 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: a9c43292-af11-4fc4-b30c-b5616881d7f8
# index: 0
# cmd: dir
# timestamp: 4/24/2025, 5:17:01 PM (1745540221988)

Microsoft Windows [Version 10.0.22631.5039]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,192,658,665,472 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 61d7eba0-0bc7-4d63-804d-ff958d8006d4
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 5/23/2025, 12:26:38 AM (1747985198990)

Microsoft Windows [Version 10.0.22631.5189]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
CONDA_SHLVL=1
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_2332=1
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda;C:\pinokio\bin\miniconda\Library\mingw64\bin;C:\pinokio\bin\miniconda\Library\mingw-w64\bin;C:\pinokio\bin\miniconda\Library\usr\bin;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=(base) $P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\pinokio\bin\miniconda\Library\ssl\certs
SSL_CERT_FILE=C:\pinokio\bin\miniconda\Library\ssl\cacert.pem
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed
__CONDA_OPENSSL_CERT_DIR_SET=1
__CONDA_OPENSSL_CERT_FILE_SET=1

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: cbc2f4df-4e41-4493-9911-511ad1af00a9
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 5/23/2025, 12:26:43 AM (1747985203205)

Microsoft Windows [Version 10.0.22631.5189]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_31342.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_31342.txt
C:\pinokio\cache\TEMP\__conda_tmp_12946.txt
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0
conda-package-handling    2.4.0           py310haa95532_0
conda-package-streaming   0.11.0          py310haa95532_0
cpp-expected              1.1.0                h214f63a_0
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 830c9a7e-4e33-4a85-98d8-e9e2ecc42094
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 5/23/2025, 12:26:44 AM (1747985204344)

Microsoft Windows [Version 10.0.22631.5189]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 3ed85763-cefa-4c93-a228-7aa67444efb5
# index: 0
# cmd: dir
# timestamp: 5/23/2025, 12:26:45 AM (1747985205483)

Microsoft Windows [Version 10.0.22631.5189]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,142,075,359,232 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: cca16430-a077-40d7-bb76-c906c126ddd1
# index: 0
# cmd: dir
# timestamp: 5/23/2025, 12:27:03 AM (1747985223971)

Microsoft Windows [Version 10.0.22631.5189]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  1,142,075,879,424 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 5cad5f86-bd82-498e-94be-4cdb72241ece
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 7/16/2025, 7:17:58 AM (1752675478740)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
CONDA_SHLVL=1
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_7804=1
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda;C:\pinokio\bin\miniconda\Library\mingw64\bin;C:\pinokio\bin\miniconda\Library\mingw-w64\bin;C:\pinokio\bin\miniconda\Library\usr\bin;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=(base) $P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\pinokio\bin\miniconda\Library\ssl\certs
SSL_CERT_FILE=C:\pinokio\bin\miniconda\Library\ssl\cacert.pem
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed
__CONDA_OPENSSL_CERT_DIR_SET=1
__CONDA_OPENSSL_CERT_FILE_SET=1

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: d15f0b61-38ba-419f-a063-5ad1a2e15d4f
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/16/2025, 7:18:04 AM (1752675484277)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1  
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0  
conda-package-handling    2.4.0           py310haa95532_0  
conda-package-streaming   0.11.0          py310haa95532_0  
cpp-expected              1.1.0                h214f63a_0  
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8f92d5de-25fa-4bbb-96d2-19f10675f56a
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/16/2025, 7:18:05 AM (1752675485549)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 02d8e1c1-1dc6-4b5e-af17-e0a23c154c07
# index: 0
# cmd: dir
# timestamp: 7/16/2025, 7:18:06 AM (1752675486764)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  989,897,277,440 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 7d7e1307-67bd-44d8-84be-5753405c63d2
# index: 0
# cmd: dir
# timestamp: 7/16/2025, 7:18:30 AM (1752675510411)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  989,895,974,912 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: e8d3858b-b1cf-464b-aa50-5e3bc59b95a4
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
# timestamp: 7/17/2025, 3:57:55 PM (1752793075548)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && set
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=133579645654484976
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramW6432=C:\Program Files\Common Files
COMPUTERNAME=CONQUEST
ComSpec=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
CONDA_SHLVL=1
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_7804=1
GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\OneDrive\Documents\Local Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
GROQ_API_KEY=********************************************************
HOMEDRIVE=C:
HOMEPATH=\Users\ullin
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\CONQUEST
MSMPI_BENCHMARKS=C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN=C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS=24
OculusBase=C:\Program Files\Oculus\
OneDrive=C:\Users\<USER>\OneDrive
OneDriveConsumer=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
Path=C:\pinokio\bin\miniconda;C:\pinokio\bin\miniconda\Library\mingw64\bin;C:\pinokio\bin\miniconda\Library\mingw-w64\bin;C:\pinokio\bin\miniconda\Library\usr\bin;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PLAYWRIGHT_BROWSERS_PATH=C:\pinokio\bin\playwright\browsers
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
PROMPT=(base) $P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\pinokio\bin\miniconda\Library\ssl\certs
SSL_CERT_FILE=C:\pinokio\bin\miniconda\Library\ssl\cacert.pem
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\pinokio\cache\TEMP
TMP=C:\pinokio\cache\TMP
USERDOMAIN=CONQUEST
USERDOMAIN_ROAMINGPROFILE=CONQUEST
USERNAME=ullin
USERPROFILE=C:\Users\<USER>\Program Files\Oracle\VirtualBox\
windir=C:\Windows
CMAKE_OBJECT_PATH_MAX=1024
PYTORCH_ENABLE_MPS_FALLBACK=1
TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD=1
HOMEBREW_CACHE=C:\pinokio\bin\homebrew\cache
XDG_CACHE_HOME=C:\pinokio\cache\XDG_CACHE_HOME
PIP_CACHE_DIR=C:\pinokio\cache\PIP_CACHE_DIR
UV_CACHE_DIR=C:\pinokio\cache\UV_CACHE_DIR
PIP_TMPDIR=C:\pinokio\cache\PIP_TMPDIR
TMPDIR=C:\pinokio\cache\TMPDIR
XDG_DATA_HOME=C:\pinokio\cache\XDG_DATA_HOME
XDG_CONFIG_HOME=C:\pinokio\cache\XDG_CONFIG_HOME
XDG_STATE_HOME=C:\pinokio\cache\XDG_STATE_HOME
PIP_CONFIG_FILE=C:\pinokio\pipconfig
CONDARC=C:\pinokio\condarc
PS1=<<PINOKIO_SHELL>>
GRADIO_ANALYTICS_ENABLED=False
GRADIO_ALLOWED_PATHS=C:\pinokio
PINOKIO_SHARE_VAR=url
PINOKIO_SHARE_CLOUDFLARE=false
PINOKIO_SCRIPT_DEFAULT=true
PINOKIO_DRIVE=C:\pinokio\drive
GRADIO_TEMP_DIR=C:\pinokio\cache\GRADIO_TEMP_DIR
HF_HOME=C:\pinokio\cache\HF_HOME
TORCH_HOME=C:\pinokio\cache\TORCH_HOME
PINOKIO_SHARE_LOCAL=false
HOMEBREW_PREFIX=C:\pinokio\bin\homebrew
HOMEBREW_CELLAR=C:\pinokio\bin\homebrew\Cellar
HOMEBREW_REPOSITORY=C:\pinokio\bin\homebrew
CONDA_PREFIX=C:\pinokio\bin\miniconda
CONDA_ENVS_PATH=C:\pinokio\bin\miniconda\envs
CONDA_PKGS_DIRS=C:\pinokio\bin\miniconda\pkgs
PYTHON=C:\pinokio\bin\miniconda\python
CONDA_BAT=C:\pinokio\bin\miniconda\condabin\conda.bat
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
CUDA_HOME=C:\pinokio\bin\miniconda
GIT_CONFIG_GLOBAL=C:\pinokio\gitconfig
CONDA_AUTO_ACTIVATE_BASE=false
PYTHONNOUSERSITE=1
PIP_REQUIRE_VIRTUALENV=true
UV_PYTHON_PREFERENCE=only-managed
__CONDA_OPENSSL_CERT_DIR_SET=1
__CONDA_OPENSSL_CERT_FILE_SET=1

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: cec621a5-2c4c-4bf8-94ee-54f3b9f1f075
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/17/2025, 3:58:00 PM (1752793080586)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_3202.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_3202.txt
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                    Version                   Build  Channel
7zip                      24.08                hc790b64_0    conda-forge
anaconda-anon-usage       0.5.0           py310hfc23b7f_100  
anaconda_powershell_prompt 1.1.0                haa95532_0  
anaconda_prompt           1.1.0                haa95532_0  
annotated-types           0.6.0           py310haa95532_0  
archspec                  0.2.3              pyhd3eb1b0_0  
boltons                   24.1.0          py310haa95532_0  
brotli-python             1.0.9           py310h5da7b33_9  
bzip2                     1.0.8                h2bbff1b_6  
ca-certificates           2025.1.31            h56e8100_0    conda-forge
certifi                   2025.1.31       py310haa95532_0  
cffi                      1.17.1          py310h827c3e9_1  
charset-normalizer        3.3.2              pyhd3eb1b0_0  
colorama                  0.4.6           py310haa95532_0  
conda                     25.3.1          py310h5588dad_1    conda-forge
conda-anaconda-telemetry  0.1.2           py310haa95532_0  
conda-anaconda-tos        0.1.2           py310haa95532_0  
conda-content-trust       0.2.0           py310haa95532_1  
conda-libmamba-solver     25.1.1             pyhd3eb1b0_0
conda-package-handling    2.4.0           py310haa95532_0
conda-package-streaming   0.11.0          py310haa95532_0
cpp-expected              1.1.0                h214f63a_0
cryptography              43.0.3          py310hbd6ee87_1
cuda                      12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cccl                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-command-line-tools   12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-compiler             12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-cudart               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cudart-dev           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cuobjdump            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-cupti                12.1.62                       0    nvidia/label/cuda-12.1.0
cuda-cuxxfilt             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-demo-suite           12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-documentation        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-libraries            12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-libraries-dev        12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nsight-compute       12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-nvcc                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvdisasm             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvml-dev             12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprof               12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvprune              12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvrtc                12.8.93              he0c23c2_1    conda-forge
cuda-nvrtc-dev            12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-nvtx                 12.1.66                       0    nvidia/label/cuda-12.1.0
cuda-nvvp                 12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-opencl               12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-opencl-dev           12.1.56                       0    nvidia/label/cuda-12.1.0
cuda-profiler-api         12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-runtime              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-sanitizer-api        12.1.55                       0    nvidia/label/cuda-12.1.0
cuda-toolkit              12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-tools                12.1.0                        0    nvidia/label/cuda-12.1.0
cuda-version              12.8                 h5d125a7_3    conda-forge
cuda-visual-tools         12.1.0                        0    nvidia/label/cuda-12.1.0
cudnn                     9.8.0.87             h1361d0a_1    conda-forge
distro                    1.9.0           py310haa95532_0
ffmpeg                    4.3.1                ha925a31_0    conda-forge
filelock                  3.18.0             pyhd8ed1ab_0    conda-forge
fmt                       9.1.0                h6d14046_1
frozendict                2.4.2           py310h2bbff1b_0
fsspec                    2025.3.2           pyhd8ed1ab_0    conda-forge
git                       2.49.0               h57928b3_0    conda-forge
git-lfs                   3.6.1                h86e1c39_0    conda-forge
huggingface_hub           0.30.2             pyhd8ed1ab_0    conda-forge
idna                      3.7             py310haa95532_0
jsonpatch                 1.33            py310haa95532_1
jsonpointer               2.1                pyhd3eb1b0_0
libarchive                3.7.7                h9243413_0
libcublas                 12.8.4.1             he0c23c2_1    conda-forge
libcublas-dev             12.1.0.26                     0    nvidia/label/cuda-12.1.0
libcufft                  11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcufft-dev              11.0.2.4                      0    nvidia/label/cuda-12.1.0
libcurand                 10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurand-dev             10.3.2.56                     0    nvidia/label/cuda-12.1.0
libcurl                   8.11.1               haff574d_0
libcusolver               11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusolver-dev           11.4.4.55                     0    nvidia/label/cuda-12.1.0
libcusparse               12.0.2.55                     0    nvidia/label/cuda-12.1.0
libcusparse-dev           12.0.2.55                     0    nvidia/label/cuda-12.1.0
libffi                    3.4.4                hd77b12b_1
libiconv                  1.16                 h2bbff1b_3
libmamba                  2.0.5                hcd6fe79_1
libmambapy                2.0.5           py310h214f63a_1
libnpp                    12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnpp-dev                12.0.2.50                     0    nvidia/label/cuda-12.1.0
libnvjitlink              12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjitlink-dev          12.1.55                       0    nvidia/label/cuda-12.1.0
libnvjpeg                 12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvjpeg-dev             12.1.0.39                     0    nvidia/label/cuda-12.1.0
libnvvm-samples           12.1.55                       0    nvidia/label/cuda-12.1.0
libsolv                   0.7.30               hf2fb9eb_1
libsqlite                 3.47.2               h67fdade_0    conda-forge
libssh2                   1.11.1               h2addb87_0
libxml2                   2.13.5               h24da03e_0
libzlib                   1.2.13               h2466b09_6    conda-forge
libzlib-wapi              1.2.13               h2466b09_6    conda-forge
lz4-c                     1.9.4                h2bbff1b_1
markdown-it-py            2.2.0           py310haa95532_1
mdurl                     0.1.0           py310haa95532_0
menuinst                  2.2.0           py310h5da7b33_1
nlohmann_json             3.11.2               h6c2663c_0
nodejs                    22.13.0              hfeaa22a_0    conda-forge
nsight-compute            2023.1.0.15                   0    nvidia/label/cuda-12.1.0
openssl                   3.5.0                ha4e3fda_0    conda-forge
packaging                 24.2            py310haa95532_0
pcre2                     10.42                h0ff8eda_1
pip                       25.0            py310haa95532_0
platformdirs              3.10.0          py310haa95532_0
pluggy                    1.5.0           py310haa95532_0
pnpm                      10.8.1               haa868a1_0    conda-forge
pybind11-abi              5                    hd3eb1b0_0
pycosat                   0.6.6           py310h827c3e9_2
pycparser                 2.21               pyhd3eb1b0_0
pydantic                  2.10.3          py310haa95532_0
pydantic-core             2.27.1          py310h636fa0f_0
pygments                  2.15.1          py310haa95532_1
pysocks                   1.7.1           py310haa95532_0
python                    3.10.16              h4607a30_1
python_abi                3.10                    2_cp310    conda-forge
pyyaml                    6.0.2           py310h38315fa_2    conda-forge
reproc                    14.2.4               hd77b12b_2
reproc-cpp                14.2.4               hd77b12b_2
requests                  2.32.3          py310haa95532_1
rich                      13.9.4          py310haa95532_0
ruamel.yaml               0.18.6          py310h827c3e9_0
ruamel.yaml.clib          0.2.8           py310h827c3e9_0
setuptools                75.8.0          py310haa95532_0
simdjson                  3.10.1               h214f63a_0
spdlog                    1.11.0               h59b6b97_0
sqlite                    3.47.2               h2466b09_0    conda-forge
tk                        8.6.14               h0416ee5_0
tqdm                      4.67.1          py310h9909e9c_0
truststore                0.10.0          py310haa95532_0
typing-extensions         4.12.2          py310haa95532_0
typing_extensions         4.12.2          py310haa95532_0
tzdata                    2025a                h04d1e81_0
ucrt                      10.0.22621.0         h57928b3_1    conda-forge
urllib3                   2.3.0           py310haa95532_0
uv                        0.6.14               ha08ef0e_0    conda-forge
vc                        14.42                haa95532_4
vc14_runtime              14.42.34438         hfd919c2_26    conda-forge
vs2015_runtime            14.42.34438         h7142326_26    conda-forge
wheel                     0.45.1          py310haa95532_0
win_inet_pton             1.1.0           py310haa95532_0
xz                        5.4.6                h8cc25b3_1
yaml                      0.2.5                h8ffe710_2    conda-forge
yaml-cpp                  0.8.0                hd77b12b_1
zlib                      1.2.13               h2466b09_6    conda-forge
zstandard                 0.23.0          py310h4fc1ca9_1
zstd                      1.5.6                h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9d50da4d-9749-4e4b-9bde-eeb1a30c76ed
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/17/2025, 3:58:01 PM (1752793081761)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8ab1f3c7-d7d9-4771-b01d-da0f9988e4dc
# index: 0
# cmd: dir
# timestamp: 7/17/2025, 3:58:03 PM (1752793083040)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  891,065,188,352 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 2eed05ff-60d5-4944-9a0c-5f8ca5115174
# index: 0
# cmd: dir
# timestamp: 7/17/2025, 3:58:34 PM (1752793114620)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  891,062,755,328 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: e7f99c02-6e87-407a-9302-e244de5a0b99
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:31:43 PM (1753252303866)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 320f8db9-a44d-4704-98f1-3a875f85cc49
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:31:45 PM (1753252305080)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 223d0149-2585-40e3-bb2f-acf8a5febd69
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:31:46 PM (1753252306251)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 955eccf0-2cf2-4ee3-a7c6-c556c8834c09
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:31:47 PM (1753252307459)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: be8b0162-88d5-4390-acbb-f38c9418147c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:31:48 PM (1753252308648)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9efc2418-021e-4560-9e37-bbb5bce0aaf6
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/22/2025, 11:31:49 PM (1753252309858)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 3f019f6b-5d10-4978-8ae6-3f6391f7300a
# index: 0
# cmd: dir
# timestamp: 7/22/2025, 11:31:51 PM (1753252311080)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  955,928,223,744 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 1ea75fe6-cb71-4f84-840a-8cb1ec58739a
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:32:14 PM (1753252334330)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: d9838245-20d2-4be2-a978-1abb87c54b47
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:32:15 PM (1753252335545)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9cc0f7be-ac1e-434b-bab0-9009010aba76
# index: 0
# cmd: undefined
# timestamp: 7/22/2025, 11:32:15 PM (1753252335929)



######################################################################
#
# group: undefined
# id: 9cc0f7be-ac1e-434b-bab0-9009010aba76
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:32:16 PM (1753252336771)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: b03baba5-228a-449f-ae69-f11ad824e631
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:32:17 PM (1753252337979)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 31b63274-bebf-4b0f-b113-c5fed5351c33
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:32:19 PM (1753252339170)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: e8b3ef00-f53c-4d11-8720-aa3e5491ea04
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/22/2025, 11:32:20 PM (1753252340386)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 1cde94f7-3e07-44a6-b717-f73eb79227dc
# index: 0
# cmd: dir
# timestamp: 7/22/2025, 11:32:21 PM (1753252341593)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  955,864,236,032 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: e5ef80c5-c75c-41a5-95db-28acbe8ed046
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:34:52 PM (1753252492617)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 88fa8d8d-dd5f-45e4-a078-f24b833e554a
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:34:53 PM (1753252493836)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: a2edfb5d-5ddc-4db4-9505-809ab853f52e
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:34:55 PM (1753252495035)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9e8dec7e-77c5-4a69-ba51-0272ac468f34
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:34:56 PM (1753252496234)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 6cca580e-00f1-49f2-b79c-809379504fde
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/22/2025, 11:34:57 PM (1753252497448)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 04d6dac3-df4a-4ff1-ab62-d452675f1082
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/22/2025, 11:34:58 PM (1753252498658)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 24f7bcd5-5ab2-4927-b2fc-dfe34a444b10
# index: 0
# cmd: dir
# timestamp: 7/22/2025, 11:34:59 PM (1753252499851)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

04/15/2025  02:25 PM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
03/31/2025  10:52 PM           129,690 chrome_100_percent.pak
03/31/2025  10:52 PM           179,971 chrome_200_percent.pak
03/31/2025  10:52 PM         4,891,080 d3dcompiler_47.dll
03/31/2025  10:52 PM         2,862,080 ffmpeg.dll
03/31/2025  10:52 PM        10,541,296 icudtl.dat
03/31/2025  10:52 PM           479,232 libEGL.dll
03/31/2025  10:52 PM         7,514,112 libGLESv2.dll
03/31/2025  10:52 PM             1,096 LICENSE.electron.txt
03/31/2025  10:52 PM         6,766,160 LICENSES.chromium.html
04/15/2025  02:25 PM    <DIR>          locales
03/31/2025  10:52 PM       162,031,104 Pinokio.exe
04/15/2025  02:25 PM    <DIR>          resources
03/31/2025  10:52 PM         5,430,320 resources.pak
03/31/2025  10:52 PM           162,352 snapshot_blob.bin
03/31/2025  10:52 PM           129,959 Uninstall Pinokio.exe
03/31/2025  10:52 PM           476,792 v8_context_snapshot.bin
03/31/2025  10:52 PM         5,209,088 vk_swiftshader.dll
03/31/2025  10:52 PM               106 vk_swiftshader_icd.json
03/31/2025  10:52 PM           920,576 vulkan-1.dll
              17 File(s)    207,725,014 bytes
               4 Dir(s)  955,585,196,032 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 61bf1307-0999-406d-88d6-9c3de89450dd
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:16:05 AM (1753254965927)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: d4e296e8-5647-49b7-86bf-f0111ffe06d9
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:16:07 AM (1753254967159)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: ba09c2d6-585f-4741-8909-3736e15839e9
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:16:08 AM (1753254968386)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 87b3b811-a178-4be1-8218-1632fb7eee80
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:16:09 AM (1753254969618)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: ac3bf51c-fab3-41cc-81f0-5dfd5ceb737a
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:16:10 AM (1753254970849)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
'conda_hook' is not recognized as an internal or external command,
operable program or batch file.

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: a4b11cae-f7ef-4277-a154-0ade9f71e1dd
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/23/2025, 12:16:12 AM (1753254972050)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 4763a743-49a6-4e4d-91ed-88331e635902
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 12:16:13 AM (1753254973271)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  952,089,395,200 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: c7a0bc42-7489-43b1-9464-b3c76399b8e3
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 12:16:39 AM (1753254999368)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  952,035,184,640 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 79c4c40d-4b42-40b6-9442-4e924c0085f2
# index: 0
# cmd: start /wait installer.exe /InstallationType=JustMe /RegisterPython=0 /S /D=C:\pinokio\bin\miniconda
# timestamp: 7/23/2025, 12:17:26 AM (1753255046292)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>start /wait installer.exe /InstallationType=JustMe /RegisterPython=0 /S /D=C:\pinokio\bin\miniconda
Welcome to Miniconda3 py310_25.1.1-2

By continuing this installation you are accepting this license agreement:
C:\pinokio\bin\miniconda\EULA.txt
Please run the installer in GUI mode to read the details.

Miniconda3 will now be installed into this location:
C:\pinokio\bin\miniconda

Unpacking payload...
Setting up the package cache...
Setting up the base environment...
Installing packages for base, creating shortcuts if necessary...
Initializing conda directories...
Running post install...
Done!

C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 8fb700cc-b6a1-47c3-b8ea-d210e8ab7123
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:17:32 AM (1753255052534)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 82 (63.2 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 3 (102 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: af9ae569-77c6-4f40-9bf6-804b74b1714b
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge sqlite=3.47.2
# timestamp: 7/23/2025, 12:19:21 AM (1753255161288)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge sqlite=3.47.2
Retrieving notices: done
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done


==> WARNING: A newer version of conda exists. <==
    current version: 25.1.1
    latest version: 25.5.1

Please update conda by running

    $ conda update -n base -c defaults conda



## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - sqlite=3.47.2


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    ca-certificates-2025.7.14  |       h4c7d964_0         152 KB  conda-forge
    certifi-2025.7.14          |     pyhd8ed1ab_0         156 KB  conda-forge
    conda-25.5.1               |  py310h5588dad_0         924 KB  conda-forge
    libsqlite-3.47.2           |       h67fdade_0         870 KB  conda-forge
    openssl-3.5.1              |       h725018a_0         8.9 MB  conda-forge
    python_abi-3.10            |          2_cp310           4 KB  conda-forge
    sqlite-3.47.2              |       h2466b09_0         894 KB  conda-forge
    ucrt-10.0.22621.0          |       h57928b3_1         547 KB  conda-forge
    vc14_runtime-14.44.35208   |      h818238b_30         737 KB  conda-forge
    vs2015_runtime-14.44.35208 |      h38c0c73_30          17 KB  conda-forge
    ------------------------------------------------------------
                                           Total:        13.1 MB

The following NEW packages will be INSTALLED:

  libsqlite          conda-forge/win-64::libsqlite-3.47.2-h67fdade_0
  python_abi         conda-forge/win-64::python_abi-3.10-2_cp310
  ucrt               conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1
  vc14_runtime       conda-forge/win-64::vc14_runtime-14.44.35208-h818238b_30

The following packages will be UPDATED:

  ca-certificates    pkgs/main/win-64::ca-certificates-202~ --> conda-forge/noarch::ca-certificates-2025.7.14-h4c7d964_0
  certifi            pkgs/main/win-64::certifi-2025.1.31-p~ --> conda-forge/noarch::certifi-2025.7.14-pyhd8ed1ab_0
  conda              pkgs/main::conda-25.1.1-py310haa95532~ --> conda-forge::conda-25.5.1-py310h5588dad_0
  openssl              pkgs/main::openssl-3.0.15-h827c3e9_0 --> conda-forge::openssl-3.5.1-h725018a_0
  sqlite                pkgs/main::sqlite-3.45.3-h2bbff1b_0 --> conda-forge::sqlite-3.47.2-h2466b09_0
  vs2015_runtime     pkgs/main::vs2015_runtime-14.42.34433~ --> conda-forge::vs2015_runtime-14.44.35208-h38c0c73_30



Downloading and Extracting Packages:

Preparing transaction: done 
Verifying transaction: done 
Executing transaction: done 
 
(base) C:\pinokio\bin> 
 
 
 
 

######################################################################
#
# group: undefined
# id: b0ba1e86-dfd9-4eb7-8b9d-0f2f90cdb1b6
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:19:25 AM (1753255165138)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                        Version          Build              Channel
anaconda-anon-usage           0.5.0            py310hfc23b7f_100
anaconda_powershell_prompt    1.1.0            haa95532_0
anaconda_prompt               1.1.0            haa95532_0
annotated-types               0.6.0            py310haa95532_0
archspec                      0.2.3            pyhd3eb1b0_0
boltons                       24.1.0           py310haa95532_0
brotli-python                 1.0.9            py310h5da7b33_9
bzip2                         1.0.8            h2bbff1b_6
ca-certificates               2025.7.14        h4c7d964_0         conda-forge
certifi                       2025.7.14        pyhd8ed1ab_0       conda-forge
cffi                          1.17.1           py310h827c3e9_1
charset-normalizer            3.3.2            pyhd3eb1b0_0
colorama                      0.4.6            py310haa95532_0
conda                         25.5.1           py310h5588dad_0    conda-forge
conda-anaconda-telemetry      0.1.2            py310haa95532_0
conda-anaconda-tos            0.1.2            py310haa95532_0
conda-content-trust           0.2.0            py310haa95532_1
conda-libmamba-solver         25.1.1           pyhd3eb1b0_0
conda-package-handling        2.4.0            py310haa95532_0
conda-package-streaming       0.11.0           py310haa95532_0
cpp-expected                  1.1.0            h214f63a_0
cryptography                  43.0.3           py310hbd6ee87_1
distro                        1.9.0            py310haa95532_0
fmt                           9.1.0            h6d14046_1
frozendict                    2.4.2            py310h2bbff1b_0
idna                          3.7              py310haa95532_0
jsonpatch                     1.33             py310haa95532_1
jsonpointer                   2.1              pyhd3eb1b0_0
libarchive                    3.7.7            h9243413_0
libcurl                       8.11.1           haff574d_0
libffi                        3.4.4            hd77b12b_1
libiconv                      1.16             h2bbff1b_3
libmamba                      2.0.5            hcd6fe79_1
libmambapy                    2.0.5            py310h214f63a_1
libsolv                       0.7.30           hf2fb9eb_1
libsqlite                     3.47.2           h67fdade_0         conda-forge
libssh2                       1.11.1           h2addb87_0
libxml2                       2.13.5           h24da03e_0
lz4-c                         1.9.4            h2bbff1b_1
markdown-it-py                2.2.0            py310haa95532_1
mdurl                         0.1.0            py310haa95532_0
menuinst                      2.2.0            py310h5da7b33_1
nlohmann_json                 3.11.2           h6c2663c_0
openssl                       3.5.1            h725018a_0         conda-forge
packaging                     24.2             py310haa95532_0
pcre2                         10.42            h0ff8eda_1
pip                           25.0             py310haa95532_0
platformdirs                  3.10.0           py310haa95532_0
pluggy                        1.5.0            py310haa95532_0
pybind11-abi                  5                hd3eb1b0_0
pycosat                       0.6.6            py310h827c3e9_2
pycparser                     2.21             pyhd3eb1b0_0
pydantic                      2.10.3           py310haa95532_0
pydantic-core                 2.27.1           py310h636fa0f_0
pygments                      2.15.1           py310haa95532_1
pysocks                       1.7.1            py310haa95532_0
python                        3.10.16          h4607a30_1
python_abi                    3.10             2_cp310            conda-forge
reproc                        14.2.4           hd77b12b_2
reproc-cpp                    14.2.4           hd77b12b_2
requests                      2.32.3           py310haa95532_1
rich                          13.9.4           py310haa95532_0
ruamel.yaml                   0.18.6           py310h827c3e9_0
ruamel.yaml.clib              0.2.8            py310h827c3e9_0
setuptools                    75.8.0           py310haa95532_0
simdjson                      3.10.1           h214f63a_0
spdlog                        1.11.0           h59b6b97_0
sqlite                        3.47.2           h2466b09_0         conda-forge
tk                            8.6.14           h0416ee5_0
tqdm                          4.67.1           py310h9909e9c_0
truststore                    0.10.0           py310haa95532_0
typing-extensions             4.12.2           py310haa95532_0
typing_extensions             4.12.2           py310haa95532_0
tzdata                        2025a            h04d1e81_0
ucrt                          10.0.22621.0     h57928b3_1         conda-forge
urllib3                       2.3.0            py310haa95532_0
vc                            14.42            haa95532_4
vc14_runtime                  14.44.35208      h818238b_30        conda-forge
vs2015_runtime                14.44.35208      h38c0c73_30        conda-forge
wheel                         0.45.1           py310haa95532_0
win_inet_pton                 1.1.0            py310haa95532_0
xz                            5.4.6            h8cc25b3_1
yaml-cpp                      0.8.0            hd77b12b_1
zlib                          1.2.13           h8cc25b3_1
zstandard                     0.23.0           py310h4fc1ca9_1
zstd                          1.5.6            h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 95e05ba5-93f9-4062-bed6-ff73d9a83c3b
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:19:29 AM (1753255169377)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 10 (13.1 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 8 (47.8 MB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 5095ba12-4f1d-4154-bc65-c3b348506677
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install --no-shortcuts -y -c conda-forge git git-lfs
# timestamp: 7/23/2025, 12:22:51 AM (1753255371183)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install --no-shortcuts -y -c conda-forge git git-lfs
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - git
    - git-lfs


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    git-2.49.0                 |       h57928b3_2       121.6 MB  conda-forge
    git-lfs-3.7.0              |       h86e1c39_0         3.9 MB  conda-forge
    ------------------------------------------------------------
                                           Total:       125.5 MB

The following NEW packages will be INSTALLED:

  git                conda-forge/win-64::git-2.49.0-h57928b3_2 
  git-lfs            conda-forge/win-64::git-lfs-3.7.0-h86e1c39_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: c755d9a2-429f-4d23-9d38-7f5a43a00a24
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:22:55 AM (1753255375247)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 2 (125.5 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 65f5f60f-b510-4aee-8172-458abbaa3f3f
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge 7zip
# timestamp: 7/23/2025, 12:23:50 AM (1753255430768)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge 7zip
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - 7zip


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    7zip-24.08                 |       h49e36cd_1         1.1 MB  conda-forge
    ------------------------------------------------------------
                                           Total:         1.1 MB

The following NEW packages will be INSTALLED:

  7zip               conda-forge/win-64::7zip-24.08-h49e36cd_1 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 9b0e0deb-e1c7-424d-b9fb-cd7991b26cec
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:23:54 AM (1753255434815)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 1 (1.1 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: b371c56f-0f3b-4355-b3dc-5739334392f7
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y nodejs pnpm -c conda-forge
# timestamp: 7/23/2025, 12:24:43 AM (1753255483077)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y nodejs pnpm -c conda-forge
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - nodejs
    - pnpm


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    nodejs-24.4.1              |       he453025_0        28.6 MB  conda-forge
    pnpm-10.13.1               |       h785286a_1         3.1 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        31.7 MB

The following NEW packages will be INSTALLED:

  nodejs             conda-forge/win-64::nodejs-24.4.1-he453025_0 
  pnpm               conda-forge/win-64::pnpm-10.13.1-h785286a_1 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: fd38b002-3fec-4bc1-9d9e-c224ed5f1118
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:24:47 AM (1753255487230)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 2 (31.7 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: a37f63c3-1816-4238-ae58-c17c04211a8c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge ffmpeg
# timestamp: 7/23/2025, 12:25:43 AM (1753255543759)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge ffmpeg
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - ffmpeg


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    ffmpeg-4.3.1               |       ha925a31_0        26.2 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        26.2 MB

The following NEW packages will be INSTALLED:

  ffmpeg             conda-forge/win-64::ffmpeg-4.3.1-ha925a31_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 74799d54-4dc1-416d-9dac-6c928928781c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:25:48 AM (1753255548108)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 1 (26.2 MB) tarball(s).
Will remove 1 index cache(s).
There are no unused package(s) to remove.
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 5add2db9-b531-48ce-b25b-d41f289dac83
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cudnn libzlib-wapi -c conda-forge
# timestamp: 7/23/2025, 12:31:42 AM (1753255902455)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cudnn libzlib-wapi -c conda-forge
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - cudnn
    - libzlib-wapi


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    cuda-nvrtc-12.9.86         |       he0c23c2_0        55.8 MB  conda-forge
    cuda-version-12.9          |       h4f385c5_3          21 KB  conda-forge
    cudnn-9.10.1.4             |       h1361d0a_0          19 KB  conda-forge
    libcublas-12.9.1.4         |       he0c23c2_0       439.9 MB  conda-forge
    libcudnn-9.10.1.4          |       hffc9a7f_0       486.2 MB  conda-forge
    libcudnn-dev-9.10.1.4      |       hffc9a7f_0         151 KB  conda-forge
    libzlib-1.2.13             |       h2466b09_6          55 KB  conda-forge
    libzlib-wapi-1.2.13        |       h2466b09_6          55 KB  conda-forge
    zlib-1.2.13                |       h2466b09_6         105 KB  conda-forge
    ------------------------------------------------------------
                                           Total:       982.4 MB

The following NEW packages will be INSTALLED:

  cuda-nvrtc         conda-forge/win-64::cuda-nvrtc-12.9.86-he0c23c2_0 
  cuda-version       conda-forge/noarch::cuda-version-12.9-h4f385c5_3 
  cudnn              conda-forge/win-64::cudnn-9.10.1.4-h1361d0a_0 
  libcublas          conda-forge/win-64::libcublas-12.9.1.4-he0c23c2_0 
  libcudnn           conda-forge/win-64::libcudnn-9.10.1.4-hffc9a7f_0 
  libcudnn-dev       conda-forge/win-64::libcudnn-dev-9.10.1.4-hffc9a7f_0 
  libzlib            conda-forge/win-64::libzlib-1.2.13-h2466b09_6 
  libzlib-wapi       conda-forge/win-64::libzlib-wapi-1.2.13-h2466b09_6 

The following packages will be UPDATED:

  zlib                    pkgs/main::zlib-1.2.13-h8cc25b3_1 --> conda-forge::zlib-1.2.13-h2466b09_6



Downloading and Extracting Packages:

Preparing transaction: done 
Verifying transaction: done 
Executing transaction: done 
 
(base) C:\pinokio\bin> 
 
 
 

######################################################################
#
# group: undefined
# id: dfdcf8a6-b011-410b-a96e-ea6d3bfba862
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:31:46 AM (1753255906719)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 9 (982.4 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 3 (810 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: f52fd95c-8e03-4672-8951-83029b30a26c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cuda -c nvidia/label/cuda-12.1.0
# timestamp: 7/23/2025, 12:51:26 AM (1753257086851)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y cuda -c nvidia/label/cuda-12.1.0
Channels:
 - nvidia/label/cuda-12.1.0
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - cuda


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    cuda-12.1.0                |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-cccl-12.1.55          |                0         1.2 MB  nvidia/label/cuda-12.1.0
    cuda-command-line-tools-12.1.0|                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-compiler-12.1.0       |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-cudart-12.1.55        |                0         965 KB  nvidia/label/cuda-12.1.0
    cuda-cudart-dev-12.1.55    |                0         547 KB  nvidia/label/cuda-12.1.0
    cuda-cuobjdump-12.1.55     |                0         3.7 MB  nvidia/label/cuda-12.1.0
    cuda-cupti-12.1.62         |                0        11.6 MB  nvidia/label/cuda-12.1.0
    cuda-cuxxfilt-12.1.55      |                0         163 KB  nvidia/label/cuda-12.1.0
    cuda-demo-suite-12.1.55    |                0         4.7 MB  nvidia/label/cuda-12.1.0
    cuda-documentation-12.1.55 |                0          89 KB  nvidia/label/cuda-12.1.0
    cuda-libraries-12.1.0      |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-libraries-dev-12.1.0  |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-nsight-compute-12.1.0 |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-nvcc-12.1.66          |                0        55.3 MB  nvidia/label/cuda-12.1.0
    cuda-nvdisasm-12.1.55      |                0        48.0 MB  nvidia/label/cuda-12.1.0
    cuda-nvml-dev-12.1.55      |                0          88 KB  nvidia/label/cuda-12.1.0
    cuda-nvprof-12.1.55        |                0         1.6 MB  nvidia/label/cuda-12.1.0
    cuda-nvprune-12.1.55       |                0         150 KB  nvidia/label/cuda-12.1.0
    cuda-nvrtc-dev-12.1.55     |                0        16.5 MB  nvidia/label/cuda-12.1.0
    cuda-nvtx-12.1.66          |                0          41 KB  nvidia/label/cuda-12.1.0
    cuda-nvvp-12.1.55          |                0       113.6 MB  nvidia/label/cuda-12.1.0
    cuda-opencl-12.1.56        |                0          10 KB  nvidia/label/cuda-12.1.0
    cuda-opencl-dev-12.1.56    |                0          60 KB  nvidia/label/cuda-12.1.0
    cuda-profiler-api-12.1.55  |                0          18 KB  nvidia/label/cuda-12.1.0
    cuda-runtime-12.1.0        |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-sanitizer-api-12.1.55 |                0        12.9 MB  nvidia/label/cuda-12.1.0
    cuda-toolkit-12.1.0        |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-tools-12.1.0          |                0           1 KB  nvidia/label/cuda-12.1.0
    cuda-visual-tools-12.1.0   |                0           1 KB  nvidia/label/cuda-12.1.0
    libcublas-dev-12.1.0.26    |                0       348.3 MB  nvidia/label/cuda-12.1.0
    libcufft-11.0.2.4          |                0           6 KB  nvidia/label/cuda-12.1.0
    libcufft-dev-11.0.2.4      |                0       102.6 MB  nvidia/label/cuda-12.1.0
    libcurand-10.3.2.56        |                0           3 KB  nvidia/label/cuda-12.1.0
    libcurand-dev-10.3.2.56    |                0        50.0 MB  nvidia/label/cuda-12.1.0
    libcusolver-11.4.4.55      |                0          30 KB  nvidia/label/cuda-12.1.0
    libcusolver-dev-11.4.4.55  |                0        95.7 MB  nvidia/label/cuda-12.1.0
    libcusparse-12.0.2.55      |                0          12 KB  nvidia/label/cuda-12.1.0
    libcusparse-dev-12.0.2.55  |                0       162.5 MB  nvidia/label/cuda-12.1.0
    libnpp-12.0.2.50           |                0         305 KB  nvidia/label/cuda-12.1.0
    libnpp-dev-12.0.2.50       |                0       135.6 MB  nvidia/label/cuda-12.1.0
    libnvjitlink-12.1.55       |                0        67.3 MB  nvidia/label/cuda-12.1.0
    libnvjitlink-dev-12.1.55   |                0        13.8 MB  nvidia/label/cuda-12.1.0
    libnvjpeg-12.1.0.39        |                0           5 KB  nvidia/label/cuda-12.1.0
    libnvjpeg-dev-12.1.0.39    |                0         2.0 MB  nvidia/label/cuda-12.1.0
    libnvvm-samples-12.1.55    |                0          32 KB  nvidia/label/cuda-12.1.0
    nsight-compute-2023.1.0.15 |                0       601.8 MB  nvidia/label/cuda-12.1.0
    ------------------------------------------------------------
                                           Total:        1.81 GB

The following NEW packages will be INSTALLED:

  cuda               nvidia/label/cuda-12.1.0/win-64::cuda-12.1.0-0
  cuda-cccl          nvidia/label/cuda-12.1.0/win-64::cuda-cccl-12.1.55-0
  cuda-command-line~ nvidia/label/cuda-12.1.0/win-64::cuda-command-line-tools-12.1.0-0
  cuda-compiler      nvidia/label/cuda-12.1.0/win-64::cuda-compiler-12.1.0-0
  cuda-cudart        nvidia/label/cuda-12.1.0/win-64::cuda-cudart-12.1.55-0
  cuda-cudart-dev    nvidia/label/cuda-12.1.0/win-64::cuda-cudart-dev-12.1.55-0
  cuda-cuobjdump     nvidia/label/cuda-12.1.0/win-64::cuda-cuobjdump-12.1.55-0
  cuda-cupti         nvidia/label/cuda-12.1.0/win-64::cuda-cupti-12.1.62-0
  cuda-cuxxfilt      nvidia/label/cuda-12.1.0/win-64::cuda-cuxxfilt-12.1.55-0
  cuda-demo-suite    nvidia/label/cuda-12.1.0/win-64::cuda-demo-suite-12.1.55-0
  cuda-documentation nvidia/label/cuda-12.1.0/win-64::cuda-documentation-12.1.55-0
  cuda-libraries     nvidia/label/cuda-12.1.0/win-64::cuda-libraries-12.1.0-0
  cuda-libraries-dev nvidia/label/cuda-12.1.0/win-64::cuda-libraries-dev-12.1.0-0
  cuda-nsight-compu~ nvidia/label/cuda-12.1.0/win-64::cuda-nsight-compute-12.1.0-0
  cuda-nvcc          nvidia/label/cuda-12.1.0/win-64::cuda-nvcc-12.1.66-0
  cuda-nvdisasm      nvidia/label/cuda-12.1.0/win-64::cuda-nvdisasm-12.1.55-0
  cuda-nvml-dev      nvidia/label/cuda-12.1.0/win-64::cuda-nvml-dev-12.1.55-0
  cuda-nvprof        nvidia/label/cuda-12.1.0/win-64::cuda-nvprof-12.1.55-0
  cuda-nvprune       nvidia/label/cuda-12.1.0/win-64::cuda-nvprune-12.1.55-0
  cuda-nvrtc-dev     nvidia/label/cuda-12.1.0/win-64::cuda-nvrtc-dev-12.1.55-0
  cuda-nvtx          nvidia/label/cuda-12.1.0/win-64::cuda-nvtx-12.1.66-0
  cuda-nvvp          nvidia/label/cuda-12.1.0/win-64::cuda-nvvp-12.1.55-0
  cuda-opencl        nvidia/label/cuda-12.1.0/win-64::cuda-opencl-12.1.56-0
  cuda-opencl-dev    nvidia/label/cuda-12.1.0/win-64::cuda-opencl-dev-12.1.56-0
  cuda-profiler-api  nvidia/label/cuda-12.1.0/win-64::cuda-profiler-api-12.1.55-0
  cuda-runtime       nvidia/label/cuda-12.1.0/win-64::cuda-runtime-12.1.0-0
  cuda-sanitizer-api nvidia/label/cuda-12.1.0/win-64::cuda-sanitizer-api-12.1.55-0
  cuda-toolkit       nvidia/label/cuda-12.1.0/win-64::cuda-toolkit-12.1.0-0
  cuda-tools         nvidia/label/cuda-12.1.0/win-64::cuda-tools-12.1.0-0
  cuda-visual-tools  nvidia/label/cuda-12.1.0/win-64::cuda-visual-tools-12.1.0-0
  libcublas-dev      nvidia/label/cuda-12.1.0/win-64::libcublas-dev-12.1.0.26-0
  libcufft           nvidia/label/cuda-12.1.0/win-64::libcufft-11.0.2.4-0
  libcufft-dev       nvidia/label/cuda-12.1.0/win-64::libcufft-dev-11.0.2.4-0
  libcurand          nvidia/label/cuda-12.1.0/win-64::libcurand-10.3.2.56-0
  libcurand-dev      nvidia/label/cuda-12.1.0/win-64::libcurand-dev-10.3.2.56-0
  libcusolver        nvidia/label/cuda-12.1.0/win-64::libcusolver-11.4.4.55-0
  libcusolver-dev    nvidia/label/cuda-12.1.0/win-64::libcusolver-dev-11.4.4.55-0
  libcusparse        nvidia/label/cuda-12.1.0/win-64::libcusparse-12.0.2.55-0
  libcusparse-dev    nvidia/label/cuda-12.1.0/win-64::libcusparse-dev-12.0.2.55-0
  libnpp             nvidia/label/cuda-12.1.0/win-64::libnpp-12.0.2.50-0
  libnpp-dev         nvidia/label/cuda-12.1.0/win-64::libnpp-dev-12.0.2.50-0
  libnvjitlink       nvidia/label/cuda-12.1.0/win-64::libnvjitlink-12.1.55-0
  libnvjitlink-dev   nvidia/label/cuda-12.1.0/win-64::libnvjitlink-dev-12.1.55-0
  libnvjpeg          nvidia/label/cuda-12.1.0/win-64::libnvjpeg-12.1.0.39-0
  libnvjpeg-dev      nvidia/label/cuda-12.1.0/win-64::libnvjpeg-dev-12.1.0.39-0
  libnvvm-samples    nvidia/label/cuda-12.1.0/win-64::libnvvm-samples-12.1.55-0
  nsight-compute     nvidia/label/cuda-12.1.0/win-64::nsight-compute-2023.1.0.15-0



Downloading and Extracting Packages:

Preparing transaction: done 
Verifying transaction: done 
Executing transaction: done 
 
(base) C:\pinokio\bin> 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


######################################################################
#
# group: undefined
# id: 667c4561-6759-47b4-b4df-68eda243e411
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/pinokiocomputer/python py
# timestamp: 7/23/2025, 12:51:31 AM (1753257091283)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/pinokiocomputer/python py
Cloning into 'py'...
remote: Enumerating objects: 54, done.
remote: Counting objects: 100% (54/54), done.
remote: Compressing objects: 100% (38/38), done.
remote: Total 54 (delta 16), reused 53 (delta 15), pack-reused 0 (from 0)
Receiving objects: 100% (54/54), 14.65 KiB | 416.00 KiB/s, done.
Resolving deltas: 100% (16/16), done.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: f1c9807b-a1a5-4caa-8fef-edec4c19e234
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && python -m venv C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\deactivate && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && pip install -r requirements.txt
# timestamp: 7/23/2025, 12:51:50 AM (1753257110830)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin\py>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && python -m venv C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && C:\pinokio\bin\py\env\Scripts\deactivate && C:\pinokio\bin\py\env\Scripts\activate C:\pinokio\bin\py\env && pip install -r requirements.txt
Collecting importlib_metadata
  Downloading importlib_metadata-8.7.0-py3-none-any.whl (27 kB)
Collecting fastapi
  Downloading fastapi-0.116.1-py3-none-any.whl (95 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 95.6/95.6 kB 5.7 MB/s eta 0:00:00
Collecting uvicorn[standard]
  Downloading uvicorn-0.35.0-py3-none-any.whl (66 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 66.4/66.4 kB ? eta 0:00:00
Collecting zipp>=3.20
  Downloading zipp-3.23.0-py3-none-any.whl (10 kB)
Collecting pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4
  Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 444.8/444.8 kB 5.6 MB/s eta 0:00:00
Collecting starlette<0.48.0,>=0.40.0
  Downloading starlette-0.47.2-py3-none-any.whl (72 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.0/73.0 kB ? eta 0:00:00
Collecting typing-extensions>=4.8.0
  Downloading typing_extensions-4.14.1-py3-none-any.whl (43 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.9/43.9 kB ? eta 0:00:00
Collecting h11>=0.8
  Downloading h11-0.16.0-py3-none-any.whl (37 kB)
Collecting click>=7.0
  Downloading click-8.2.1-py3-none-any.whl (102 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 102.2/102.2 kB 6.1 MB/s eta 0:00:00
Collecting pyyaml>=5.1
  Using cached PyYAML-6.0.2-cp310-cp310-win_amd64.whl (161 kB)
Collecting httptools>=0.6.3
  Using cached httptools-0.6.4-cp310-cp310-win_amd64.whl (88 kB)
Collecting python-dotenv>=0.13
  Downloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)
Collecting watchfiles>=0.13
  Downloading watchfiles-1.1.0-cp310-cp310-win_amd64.whl (292 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 292.2/292.2 kB 17.6 MB/s eta 0:00:00
Collecting colorama>=0.4
  Using cached colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Collecting websockets>=10.4
  Using cached websockets-15.0.1-cp310-cp310-win_amd64.whl (176 kB)
Collecting typing-inspection>=0.4.0
  Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Collecting pydantic-core==2.33.2
  Downloading pydantic_core-2.33.2-cp310-cp310-win_amd64.whl (2.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 5.4 MB/s eta 0:00:00
Collecting annotated-types>=0.6.0
  Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)
Collecting anyio<5,>=3.6.2
  Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Collecting exceptiongroup>=1.0.2
  Downloading exceptiongroup-1.3.0-py3-none-any.whl (16 kB)
Collecting sniffio>=1.1
  Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Collecting idna>=2.8
  Using cached idna-3.10-py3-none-any.whl (70 kB)
Installing collected packages: zipp, websockets, typing-extensions, sniffio, pyyaml, python-dotenv, idna, httptools, h11, colorama, annotated-types, typing-inspection, pydantic-core, importlib_metadata, exceptiongroup, click, uvicorn, pydantic, anyio, watchfiles, starlette, fastapi
Successfully installed annotated-types-0.7.0 anyio-4.9.0 click-8.2.1 colorama-0.4.6 exceptiongroup-1.3.0 fastapi-0.116.1 h11-0.16.0 httptools-0.6.4 idna-3.10 importlib_metadata-8.7.0 pydantic-2.11.7 pydantic-core-2.33.2 python-dotenv-1.1.1 pyyaml-6.0.2 sniffio-1.3.1 starlette-0.47.2 typing-extensions-4.14.1 typing-inspection-0.4.1 uvicorn-0.35.0 watchfiles-1.1.0 websockets-15.0.1 zipp-3.23.0      

[notice] A new release of pip is available: 23.0.1 -> 25.1.1
[notice] To update, run: python.exe -m pip install --upgrade pip

(env) (base) C:\pinokio\bin\py>

######################################################################
#
# group: undefined
# id: 82853629-2d18-457b-8058-6fbfc92090e1
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:52:12 AM (1753257132285)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 47 (1.81 GB) tarball(s).
Will remove 1 index cache(s).
Will remove 10 (40 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 6886caaa-fdf8-4a76-9c08-ed6b0bd89c51
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge huggingface_hub
# timestamp: 7/23/2025, 12:52:51 AM (1753257171812)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge huggingface_hub
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - huggingface_hub


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    _python_abi3_support-1.0   |       hd8ed1ab_2           8 KB  conda-forge
    cpython-3.10.18            |  py310hd8ed1ab_0          49 KB  conda-forge
    filelock-3.18.0            |     pyhd8ed1ab_0          17 KB  conda-forge
    fsspec-2025.7.0            |     pyhd8ed1ab_0         142 KB  conda-forge
    hf-xet-1.1.5               |   py39h17685eb_3         2.3 MB  conda-forge
    huggingface_hub-0.33.4     |     pyhd8ed1ab_0         311 KB  conda-forge
    python-gil-3.10.18         |       hd8ed1ab_0          49 KB  conda-forge
    pyyaml-6.0.2               |  py310h38315fa_2         154 KB  conda-forge
    yaml-0.2.5                 |       h8ffe710_2          62 KB  conda-forge
    ------------------------------------------------------------
                                           Total:         3.1 MB

The following NEW packages will be INSTALLED:

  _python_abi3_supp~ conda-forge/noarch::_python_abi3_support-1.0-hd8ed1ab_2 
  cpython            conda-forge/noarch::cpython-3.10.18-py310hd8ed1ab_0 
  filelock           conda-forge/noarch::filelock-3.18.0-pyhd8ed1ab_0 
  fsspec             conda-forge/noarch::fsspec-2025.7.0-pyhd8ed1ab_0 
  hf-xet             conda-forge/win-64::hf-xet-1.1.5-py39h17685eb_3 
  huggingface_hub    conda-forge/noarch::huggingface_hub-0.33.4-pyhd8ed1ab_0 
  python-gil         conda-forge/noarch::python-gil-3.10.18-hd8ed1ab_0 
  pyyaml             conda-forge/win-64::pyyaml-6.0.2-py310h38315fa_2 
  yaml               conda-forge/win-64::yaml-0.2.5-h8ffe710_2 



Downloading and Extracting Packages:

Preparing transaction: done 
Verifying transaction: done 
Executing transaction: done 
 
(base) C:\pinokio\bin> 
 
 
 

######################################################################
#
# group: undefined
# id: ea08a970-9ad5-4538-b31c-bcea071103f0
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
# timestamp: 7/23/2025, 12:52:56 AM (1753257176012)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda clean -y --all
Will remove 9 (3.1 MB) tarball(s).
Will remove 1 index cache(s).
Will remove 3 (453 KB) package(s).
There are no tempfile(s) to remove.
There are no logfile(s) to remove.

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 905c739b-acc2-4fd6-b165-357d18cdd5cf
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge uv
# timestamp: 7/23/2025, 12:53:46 AM (1753257226881)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda install -y -c conda-forge uv
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: C:\pinokio\bin\miniconda

  added / updated specs:
    - uv


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    uv-0.8.2                   |       h579f82e_0        14.8 MB  conda-forge
    ------------------------------------------------------------
                                           Total:        14.8 MB

The following NEW packages will be INSTALLED:

  uv                 conda-forge/win-64::uv-0.8.2-h579f82e_0 



Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 497df442-0c9f-4de4-823d-45468b14619c
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:53:50 AM (1753257230842)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                        Version          Build              Channel
7zip                          24.08            h49e36cd_1         conda-forge
_python_abi3_support          1.0              hd8ed1ab_2         conda-forge
anaconda-anon-usage           0.5.0            py310hfc23b7f_100
anaconda_powershell_prompt    1.1.0            haa95532_0
anaconda_prompt               1.1.0            haa95532_0
annotated-types               0.6.0            py310haa95532_0
archspec                      0.2.3            pyhd3eb1b0_0
boltons                       24.1.0           py310haa95532_0
brotli-python                 1.0.9            py310h5da7b33_9
bzip2                         1.0.8            h2bbff1b_6
ca-certificates               2025.7.14        h4c7d964_0         conda-forge
certifi                       2025.7.14        pyhd8ed1ab_0       conda-forge
cffi                          1.17.1           py310h827c3e9_1
charset-normalizer            3.3.2            pyhd3eb1b0_0
colorama                      0.4.6            py310haa95532_0
conda                         25.5.1           py310h5588dad_0    conda-forge
conda-anaconda-telemetry      0.1.2            py310haa95532_0
conda-anaconda-tos            0.1.2            py310haa95532_0
conda-content-trust           0.2.0            py310haa95532_1
conda-libmamba-solver         25.1.1           pyhd3eb1b0_0
conda-package-handling        2.4.0            py310haa95532_0
conda-package-streaming       0.11.0           py310haa95532_0
cpp-expected                  1.1.0            h214f63a_0
cpython                       3.10.18          py310hd8ed1ab_0    conda-forge
cryptography                  43.0.3           py310hbd6ee87_1
cuda                          12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cccl                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-command-line-tools       12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-compiler                 12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cudart                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cudart-dev               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cuobjdump                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cupti                    12.1.62          0                  nvidia/label/cuda-12.1.0
cuda-cuxxfilt                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-demo-suite               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-documentation            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-libraries                12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-libraries-dev            12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nsight-compute           12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nvcc                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvdisasm                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvml-dev                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprof                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprune                  12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvrtc                    12.9.86          he0c23c2_0         conda-forge
cuda-nvrtc-dev                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvtx                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvvp                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-opencl                   12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-opencl-dev               12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-profiler-api             12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-runtime                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-sanitizer-api            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-toolkit                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-tools                    12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-version                  12.9             h4f385c5_3         conda-forge
cuda-visual-tools             12.1.0           0                  nvidia/label/cuda-12.1.0
cudnn                         9.10.1.4         h1361d0a_0         conda-forge
distro                        1.9.0            py310haa95532_0
ffmpeg                        4.3.1            ha925a31_0         conda-forge
filelock                      3.18.0           pyhd8ed1ab_0       conda-forge
fmt                           9.1.0            h6d14046_1
frozendict                    2.4.2            py310h2bbff1b_0
fsspec                        2025.7.0         pyhd8ed1ab_0       conda-forge
git                           2.49.0           h57928b3_2         conda-forge
git-lfs                       3.7.0            h86e1c39_0         conda-forge
hf-xet                        1.1.5            py39h17685eb_3     conda-forge
huggingface_hub               0.33.4           pyhd8ed1ab_0       conda-forge
idna                          3.7              py310haa95532_0
jsonpatch                     1.33             py310haa95532_1
jsonpointer                   2.1              pyhd3eb1b0_0
libarchive                    3.7.7            h9243413_0
libcublas                     12.9.1.4         he0c23c2_0         conda-forge
libcublas-dev                 12.1.0.26        0                  nvidia/label/cuda-12.1.0
libcudnn                      9.10.1.4         hffc9a7f_0         conda-forge
libcudnn-dev                  9.10.1.4         hffc9a7f_0         conda-forge
libcufft                      11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcufft-dev                  11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcurand                     10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurand-dev                 10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurl                       8.11.1           haff574d_0
libcusolver                   11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusolver-dev               11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusparse                   12.0.2.55        0                  nvidia/label/cuda-12.1.0
libcusparse-dev               12.0.2.55        0                  nvidia/label/cuda-12.1.0
libffi                        3.4.4            hd77b12b_1
libiconv                      1.16             h2bbff1b_3
libmamba                      2.0.5            hcd6fe79_1
libmambapy                    2.0.5            py310h214f63a_1
libnpp                        12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnpp-dev                    12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnvjitlink                  12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjitlink-dev              12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjpeg                     12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvjpeg-dev                 12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvvm-samples               12.1.55          0                  nvidia/label/cuda-12.1.0
libsolv                       0.7.30           hf2fb9eb_1
libsqlite                     3.47.2           h67fdade_0         conda-forge
libssh2                       1.11.1           h2addb87_0
libxml2                       2.13.5           h24da03e_0
libzlib                       1.2.13           h2466b09_6         conda-forge
libzlib-wapi                  1.2.13           h2466b09_6         conda-forge
lz4-c                         1.9.4            h2bbff1b_1
markdown-it-py                2.2.0            py310haa95532_1
mdurl                         0.1.0            py310haa95532_0
menuinst                      2.2.0            py310h5da7b33_1
nlohmann_json                 3.11.2           h6c2663c_0
nodejs                        24.4.1           he453025_0         conda-forge
nsight-compute                2023.1.0.15      0                  nvidia/label/cuda-12.1.0
openssl                       3.5.1            h725018a_0         conda-forge
packaging                     24.2             py310haa95532_0
pcre2                         10.42            h0ff8eda_1
pip                           25.0             py310haa95532_0
platformdirs                  3.10.0           py310haa95532_0
pluggy                        1.5.0            py310haa95532_0
pnpm                          10.13.1          h785286a_1         conda-forge
pybind11-abi                  5                hd3eb1b0_0
pycosat                       0.6.6            py310h827c3e9_2
pycparser                     2.21             pyhd3eb1b0_0
pydantic                      2.10.3           py310haa95532_0
pydantic-core                 2.27.1           py310h636fa0f_0
pygments                      2.15.1           py310haa95532_1
pysocks                       1.7.1            py310haa95532_0
python                        3.10.16          h4607a30_1
python-gil                    3.10.18          hd8ed1ab_0         conda-forge
python_abi                    3.10             2_cp310            conda-forge
pyyaml                        6.0.2            py310h38315fa_2    conda-forge
reproc                        14.2.4           hd77b12b_2
reproc-cpp                    14.2.4           hd77b12b_2
requests                      2.32.3           py310haa95532_1
rich                          13.9.4           py310haa95532_0
ruamel.yaml                   0.18.6           py310h827c3e9_0
ruamel.yaml.clib              0.2.8            py310h827c3e9_0
setuptools                    75.8.0           py310haa95532_0
simdjson                      3.10.1           h214f63a_0
spdlog                        1.11.0           h59b6b97_0
sqlite                        3.47.2           h2466b09_0         conda-forge
tk                            8.6.14           h0416ee5_0
tqdm                          4.67.1           py310h9909e9c_0
truststore                    0.10.0           py310haa95532_0
typing-extensions             4.12.2           py310haa95532_0
typing_extensions             4.12.2           py310haa95532_0
tzdata                        2025a            h04d1e81_0
ucrt                          10.0.22621.0     h57928b3_1         conda-forge
urllib3                       2.3.0            py310haa95532_0
uv                            0.8.2            h579f82e_0         conda-forge
vc                            14.42            haa95532_4
vc14_runtime                  14.44.35208      h818238b_30        conda-forge
vs2015_runtime                14.44.35208      h38c0c73_30        conda-forge
wheel                         0.45.1           py310haa95532_0
win_inet_pton                 1.1.0            py310haa95532_0
xz                            5.4.6            h8cc25b3_1
yaml                          0.2.5            h8ffe710_2         conda-forge
yaml-cpp                      0.8.0            hd77b12b_1
zlib                          1.2.13           h2466b09_6         conda-forge
zstandard                     0.23.0           py310h4fc1ca9_1
zstd                          1.5.6            h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 18b1a4d7-dbd6-4164-a198-c89243539cb1
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# timestamp: 7/23/2025, 12:53:55 AM (1753257235914)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && conda list
# packages in environment at C:\pinokio\bin\miniconda:
#
# Name                        Version          Build              Channel
7zip                          24.08            h49e36cd_1         conda-forge
_python_abi3_support          1.0              hd8ed1ab_2         conda-forge
anaconda-anon-usage           0.5.0            py310hfc23b7f_100
anaconda_powershell_prompt    1.1.0            haa95532_0
anaconda_prompt               1.1.0            haa95532_0
annotated-types               0.6.0            py310haa95532_0
archspec                      0.2.3            pyhd3eb1b0_0
boltons                       24.1.0           py310haa95532_0
brotli-python                 1.0.9            py310h5da7b33_9
bzip2                         1.0.8            h2bbff1b_6
ca-certificates               2025.7.14        h4c7d964_0         conda-forge
certifi                       2025.7.14        pyhd8ed1ab_0       conda-forge
cffi                          1.17.1           py310h827c3e9_1
charset-normalizer            3.3.2            pyhd3eb1b0_0
colorama                      0.4.6            py310haa95532_0
conda                         25.5.1           py310h5588dad_0    conda-forge
conda-anaconda-telemetry      0.1.2            py310haa95532_0
conda-anaconda-tos            0.1.2            py310haa95532_0
conda-content-trust           0.2.0            py310haa95532_1
conda-libmamba-solver         25.1.1           pyhd3eb1b0_0
conda-package-handling        2.4.0            py310haa95532_0
conda-package-streaming       0.11.0           py310haa95532_0
cpp-expected                  1.1.0            h214f63a_0
cpython                       3.10.18          py310hd8ed1ab_0    conda-forge
cryptography                  43.0.3           py310hbd6ee87_1
cuda                          12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cccl                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-command-line-tools       12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-compiler                 12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-cudart                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cudart-dev               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cuobjdump                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-cupti                    12.1.62          0                  nvidia/label/cuda-12.1.0
cuda-cuxxfilt                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-demo-suite               12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-documentation            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-libraries                12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-libraries-dev            12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nsight-compute           12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-nvcc                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvdisasm                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvml-dev                 12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprof                   12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvprune                  12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvrtc                    12.9.86          he0c23c2_0         conda-forge
cuda-nvrtc-dev                12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-nvtx                     12.1.66          0                  nvidia/label/cuda-12.1.0
cuda-nvvp                     12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-opencl                   12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-opencl-dev               12.1.56          0                  nvidia/label/cuda-12.1.0
cuda-profiler-api             12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-runtime                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-sanitizer-api            12.1.55          0                  nvidia/label/cuda-12.1.0
cuda-toolkit                  12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-tools                    12.1.0           0                  nvidia/label/cuda-12.1.0
cuda-version                  12.9             h4f385c5_3         conda-forge
cuda-visual-tools             12.1.0           0                  nvidia/label/cuda-12.1.0
cudnn                         9.10.1.4         h1361d0a_0         conda-forge
distro                        1.9.0            py310haa95532_0
ffmpeg                        4.3.1            ha925a31_0         conda-forge
filelock                      3.18.0           pyhd8ed1ab_0       conda-forge
fmt                           9.1.0            h6d14046_1
frozendict                    2.4.2            py310h2bbff1b_0
fsspec                        2025.7.0         pyhd8ed1ab_0       conda-forge
git                           2.49.0           h57928b3_2         conda-forge
git-lfs                       3.7.0            h86e1c39_0         conda-forge
hf-xet                        1.1.5            py39h17685eb_3     conda-forge
huggingface_hub               0.33.4           pyhd8ed1ab_0       conda-forge
idna                          3.7              py310haa95532_0
jsonpatch                     1.33             py310haa95532_1
jsonpointer                   2.1              pyhd3eb1b0_0
libarchive                    3.7.7            h9243413_0
libcublas                     12.9.1.4         he0c23c2_0         conda-forge
libcublas-dev                 12.1.0.26        0                  nvidia/label/cuda-12.1.0
libcudnn                      9.10.1.4         hffc9a7f_0         conda-forge
libcudnn-dev                  9.10.1.4         hffc9a7f_0         conda-forge
libcufft                      11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcufft-dev                  11.0.2.4         0                  nvidia/label/cuda-12.1.0
libcurand                     10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurand-dev                 10.3.2.56        0                  nvidia/label/cuda-12.1.0
libcurl                       8.11.1           haff574d_0
libcusolver                   11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusolver-dev               11.4.4.55        0                  nvidia/label/cuda-12.1.0
libcusparse                   12.0.2.55        0                  nvidia/label/cuda-12.1.0
libcusparse-dev               12.0.2.55        0                  nvidia/label/cuda-12.1.0
libffi                        3.4.4            hd77b12b_1
libiconv                      1.16             h2bbff1b_3
libmamba                      2.0.5            hcd6fe79_1
libmambapy                    2.0.5            py310h214f63a_1
libnpp                        12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnpp-dev                    12.0.2.50        0                  nvidia/label/cuda-12.1.0
libnvjitlink                  12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjitlink-dev              12.1.55          0                  nvidia/label/cuda-12.1.0
libnvjpeg                     12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvjpeg-dev                 12.1.0.39        0                  nvidia/label/cuda-12.1.0
libnvvm-samples               12.1.55          0                  nvidia/label/cuda-12.1.0
libsolv                       0.7.30           hf2fb9eb_1
libsqlite                     3.47.2           h67fdade_0         conda-forge
libssh2                       1.11.1           h2addb87_0
libxml2                       2.13.5           h24da03e_0
libzlib                       1.2.13           h2466b09_6         conda-forge
libzlib-wapi                  1.2.13           h2466b09_6         conda-forge
lz4-c                         1.9.4            h2bbff1b_1
markdown-it-py                2.2.0            py310haa95532_1
mdurl                         0.1.0            py310haa95532_0
menuinst                      2.2.0            py310h5da7b33_1
nlohmann_json                 3.11.2           h6c2663c_0
nodejs                        24.4.1           he453025_0         conda-forge
nsight-compute                2023.1.0.15      0                  nvidia/label/cuda-12.1.0
openssl                       3.5.1            h725018a_0         conda-forge
packaging                     24.2             py310haa95532_0
pcre2                         10.42            h0ff8eda_1
pip                           25.0             py310haa95532_0
platformdirs                  3.10.0           py310haa95532_0
pluggy                        1.5.0            py310haa95532_0
pnpm                          10.13.1          h785286a_1         conda-forge
pybind11-abi                  5                hd3eb1b0_0
pycosat                       0.6.6            py310h827c3e9_2
pycparser                     2.21             pyhd3eb1b0_0
pydantic                      2.10.3           py310haa95532_0
pydantic-core                 2.27.1           py310h636fa0f_0
pygments                      2.15.1           py310haa95532_1
pysocks                       1.7.1            py310haa95532_0
python                        3.10.16          h4607a30_1
python-gil                    3.10.18          hd8ed1ab_0         conda-forge
python_abi                    3.10             2_cp310            conda-forge
pyyaml                        6.0.2            py310h38315fa_2    conda-forge
reproc                        14.2.4           hd77b12b_2
reproc-cpp                    14.2.4           hd77b12b_2
requests                      2.32.3           py310haa95532_1
rich                          13.9.4           py310haa95532_0
ruamel.yaml                   0.18.6           py310h827c3e9_0
ruamel.yaml.clib              0.2.8            py310h827c3e9_0
setuptools                    75.8.0           py310haa95532_0
simdjson                      3.10.1           h214f63a_0
spdlog                        1.11.0           h59b6b97_0
sqlite                        3.47.2           h2466b09_0         conda-forge
tk                            8.6.14           h0416ee5_0
tqdm                          4.67.1           py310h9909e9c_0
truststore                    0.10.0           py310haa95532_0
typing-extensions             4.12.2           py310haa95532_0
typing_extensions             4.12.2           py310haa95532_0
tzdata                        2025a            h04d1e81_0
ucrt                          10.0.22621.0     h57928b3_1         conda-forge
urllib3                       2.3.0            py310haa95532_0
uv                            0.8.2            h579f82e_0         conda-forge
vc                            14.42            haa95532_4
vc14_runtime                  14.44.35208      h818238b_30        conda-forge
vs2015_runtime                14.44.35208      h38c0c73_30        conda-forge
wheel                         0.45.1           py310haa95532_0
win_inet_pton                 1.1.0            py310haa95532_0
xz                            5.4.6            h8cc25b3_1
yaml                          0.2.5            h8ffe710_2         conda-forge
yaml-cpp                      0.8.0            hd77b12b_1
zlib                          1.2.13           h2466b09_6         conda-forge
zstandard                     0.23.0           py310h4fc1ca9_1
zstd                          1.5.6            h8880b57_0

(base) C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 742c9e15-dae0-4c8e-8212-f8ee3ef3eb54
# index: 0
# cmd: reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled
# timestamp: 7/23/2025, 12:53:57 AM (1753257237163)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\bin>reg query HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem
    LongPathsEnabled    REG_DWORD    0x1


C:\pinokio\bin>

######################################################################
#
# group: undefined
# id: 83c391b7-0c7c-41cf-9840-40190a6cffc7
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 12:54:01 AM (1753257241980)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  956,191,059,968 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 0084218e-0d4f-4d39-8b85-9b66da9782be
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
# timestamp: 7/23/2025, 12:54:06 AM (1753257246047)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
Cloning into 'facefusion-pinokio.git'...
remote: Enumerating objects: 453, done.
remote: Counting objects: 100% (213/213), done.
remote: Compressing objects: 100% (78/78), done.
remote: Total 453 (delta 153), reused 144 (delta 135), pack-reused 240 (from 2)
Receiving objects: 100% (453/453), 324.69 KiB | 2.15 MiB/s, done.
Resolving deltas: 100% (283/283), done.

(base) C:\pinokio\api>

######################################################################
#
# group: undefined
# id: 4da74a5a-a9ba-453a-aab5-40bbe9418a50
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:36:09 PM (1753302969160)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,879,149,568 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 9a8f12a5-b4f9-43fa-a00b-1faf77bed664
# index: 0
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
# timestamp: 7/23/2025, 1:36:13 PM (1753302973941)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion-pinokio.git facefusion-pinokio.git
fatal: destination path 'facefusion-pinokio.git' already exists and is not an empty directory.

(base) C:\pinokio\api>

######################################################################
#
# group: undefined
# id: 7caf1ff0-be81-48dd-8724-0e6c6e2fcef0
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:37:07 PM (1753303027782)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,880,841,216 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: fe6f7ec4-dbdd-407f-bbad-a26be69034f4
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:48:37 PM (1753303717663)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,371,581,440 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 9e435956-cba7-4fc6-8217-6529fdfaa18d
# index: 1
# cmd: dir
# timestamp: 7/23/2025, 1:48:44 PM (1753303724334)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,371,515,904 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 741fecd9-5288-4ad5-a8ba-3d11d946fca7
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:48:58 PM (1753303738653)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,371,253,760 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 01ec15a6-e559-46ca-8088-ee6e9adce798
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:49:09 PM (1753303749739)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,365,232,640 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: f7ad901a-d3b5-4fb1-94eb-180148cf74ba
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:49:16 PM (1753303756013)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,348,189,184 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 2259bf72-57a3-4542-93c0-fed532f2cfff
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:49:38 PM (1753303778856)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,408,646,144 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: d1c395d9-bb68-494b-aa3c-46771de9b9f5
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:49:48 PM (1753303788952)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,408,613,376 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 3e00a76c-b46a-4e5c-85b2-2e8c60c9a7e9
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 1:51:11 PM (1753303871619)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  955,343,867,904 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 69176ee0-d680-423c-bc72-1842b39c73d3
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 2:17:40 PM (1753305460363)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  945,871,519,744 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 1c399482-933d-4846-9b5b-e183796034d3
# index: 1
# cmd: dir
# timestamp: 7/23/2025, 2:17:40 PM (1753305460655)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  945,871,519,744 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: a392b4f5-6ca2-41ce-a89d-793d449a53ac
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 2:27:47 PM (1753306067921)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  945,071,460,352 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

######################################################################
#
# group: undefined
# id: 56814531-c07b-44b3-9042-5fd0b763e38e
# index: 0
# cmd: dir
# timestamp: 7/23/2025, 2:37:05 PM (1753306625397)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\AppData\Local\Programs\Pinokio>dir
 Volume in drive C is Windows
 Volume Serial Number is 1624-1F42

 Directory of C:\Users\<USER>\AppData\Local\Programs\Pinokio

07/23/2025  12:16 AM    <DIR>          .
04/15/2025  02:25 PM    <DIR>          ..
06/09/2025  07:25 AM           129,690 chrome_100_percent.pak
06/09/2025  07:25 AM           179,971 chrome_200_percent.pak
06/09/2025  07:25 AM         4,891,080 d3dcompiler_47.dll
06/09/2025  07:25 AM         2,862,080 ffmpeg.dll
06/09/2025  07:25 AM        10,541,296 icudtl.dat
06/09/2025  07:25 AM           479,232 libEGL.dll
06/09/2025  07:25 AM         7,514,112 libGLESv2.dll
06/09/2025  07:25 AM             1,096 LICENSE.electron.txt
06/09/2025  07:25 AM         6,766,160 LICENSES.chromium.html
06/09/2025  07:25 AM    <DIR>          locales
06/09/2025  07:25 AM       162,031,104 Pinokio.exe
06/09/2025  07:25 AM    <DIR>          resources
06/09/2025  07:25 AM         5,430,320 resources.pak
06/09/2025  07:25 AM           162,352 snapshot_blob.bin
06/09/2025  07:25 AM           129,961 Uninstall Pinokio.exe
06/09/2025  07:25 AM           476,792 v8_context_snapshot.bin
06/09/2025  07:25 AM         5,209,088 vk_swiftshader.dll
06/09/2025  07:25 AM               106 vk_swiftshader_icd.json
06/09/2025  07:25 AM           920,576 vulkan-1.dll
              17 File(s)    207,725,016 bytes
               4 Dir(s)  943,604,871,168 bytes free

C:\Users\<USER>\AppData\Local\Programs\Pinokio>

