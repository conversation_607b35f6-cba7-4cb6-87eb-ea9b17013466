<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>SanitizerApi: Sanitizer Stream API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Sanitizer Stream API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__STREAM__API.html#g3b54bc99472229c5f4928bb37382295a">sanitizerGetStream</a> (Sanitizer_StreamHandle hStream, CUstream *stream)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Retrieve a CUstream handle from a Sanitizer_StreamHandle handle.  <a href="#g3b54bc99472229c5f4928bb37382295a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__STREAM__API.html#g71ecc9d6e3d2ab5bccd4b6448bbe558a">sanitizerGetStreamHandle</a> (CUcontext ctx, CUstream stream, Sanitizer_StreamHandle *hStream)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Retrieve a Sanitizer_StreamHandle handle from a CUstream handle.  <a href="#g71ecc9d6e3d2ab5bccd4b6448bbe558a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__STREAM__API.html#gc6919d046ea3351326fce607daa19a6c">sanitizerStreamSynchronize</a> (Sanitizer_StreamHandle stream)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Synchronize a given stream.  <a href="#gc6919d046ea3351326fce607daa19a6c"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the Sanitizer Stream API. <hr><h2>Function Documentation</h2>
<a class="anchor" name="g3b54bc99472229c5f4928bb37382295a"></a><!-- doxytag: member="sanitizer_stream.h::sanitizerGetStream" ref="g3b54bc99472229c5f4928bb37382295a" args="(Sanitizer_StreamHandle hStream, CUstream *stream)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetStream           </td>
          <td>(</td>
          <td class="paramtype">Sanitizer_StreamHandle&nbsp;</td>
          <td class="paramname"> <em>hStream</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUstream *&nbsp;</td>
          <td class="paramname"> <em>stream</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>hStream</em>&nbsp;</td><td>Sanitizer Stream handle. </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>stream</em>&nbsp;</td><td>Output CUstream handle.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>hStream</code> is not a valid Sanitizer stream handle or if <code>stream</code> is NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g71ecc9d6e3d2ab5bccd4b6448bbe558a"></a><!-- doxytag: member="sanitizer_stream.h::sanitizerGetStreamHandle" ref="g71ecc9d6e3d2ab5bccd4b6448bbe558a" args="(CUcontext ctx, CUstream stream, Sanitizer_StreamHandle *hStream)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetStreamHandle           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUstream&nbsp;</td>
          <td class="paramname"> <em>stream</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Sanitizer_StreamHandle *&nbsp;</td>
          <td class="paramname"> <em>hStream</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>ctx</em>&nbsp;</td><td>Context owning the stream. If NULL, the current context will be used. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>stream</em>&nbsp;</td><td>CUstream handle. If NULL, the NULL stream will be used. </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>hStream</em>&nbsp;</td><td>Output Sanitizer Stream handle.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>stream</code> is not a valid CUstream handle or if <code>hStream</code> is NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc6919d046ea3351326fce607daa19a6c"></a><!-- doxytag: member="sanitizer_stream.h::sanitizerStreamSynchronize" ref="gc6919d046ea3351326fce607daa19a6c" args="(Sanitizer_StreamHandle stream)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerStreamSynchronize           </td>
          <td>(</td>
          <td class="paramtype">Sanitizer_StreamHandle&nbsp;</td>
          <td class="paramname"> <em>stream</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaStreamSynchronize that can be called with a sanitizer stream handle <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>stream</em>&nbsp;</td><td>Stream handle. If NULL, the NULL stream will be used. </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Tue Jan 24 19:43:08 2023 for SanitizerApi by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
