{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-opencl-12.1.56-0", "features": "", "files": ["lib/Win32/OpenCL.lib", "lib/x64/OpenCL.lib"], "fn": "cuda-opencl-12.1.56-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-opencl-12.1.56-0", "type": 1}, "md5": "115146d07845cdd0b600d31810c707d6", "name": "cuda-opencl", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-opencl-12.1.56-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/Win32/OpenCL.lib", "path_type": "hardlink", "sha256": "14b401d97608ae1bea90aa27edc64d89f3090e62a21b82a07e510c98e40d6f80", "sha256_in_prefix": "14b401d97608ae1bea90aa27edc64d89f3090e62a21b82a07e510c98e40d6f80", "size_in_bytes": 31126}, {"_path": "lib/x64/OpenCL.lib", "path_type": "hardlink", "sha256": "eebe9c651ef88344afc7c498adf4ad1d55d0ccbbcc44c7fc85ea61712d69d1d2", "sha256_in_prefix": "eebe9c651ef88344afc7c498adf4ad1d55d0ccbbcc44c7fc85ea61712d69d1d2", "size_in_bytes": 28824}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 10612, "subdir": "win-64", "timestamp": 1674711714000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-opencl-12.1.56-0.tar.bz2", "version": "12.1.56"}