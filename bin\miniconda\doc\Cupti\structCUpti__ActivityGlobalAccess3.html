<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityGlobalAccess3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityGlobalAccess3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityGlobalAccess3" -->The activity record for source-level global access.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#c778fe944693e558ad06264d94352701">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#11e235e20debbe9f55ee838c5b551769">executed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#082f16d4239b2b7d929476fd6f159f3d">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#3ee4d94d0124d3aa683bcadf0af101c0">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#07f22bc6656ce9a3c8893a370504640d">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#8d62607220bc310fbffeba2180005f80">l2_transactions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#bd851c0443368383e9d86360c29b3941">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#12392c85bce65a1b3d93b93282909e1e">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#097d3828fdde7d24df95ca676a16f221">theoreticalL2Transactions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html#7d06f4a8c882ef0591501cf7488dff0d">threadsExecuted</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records the locations of the global accesses in the source (CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS). <hr><h2>Field Documentation</h2>
<a class="anchor" name="c778fe944693e558ad06264d94352701"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::correlationId" ref="c778fe944693e558ad06264d94352701" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#c778fe944693e558ad06264d94352701">CUpti_ActivityGlobalAccess3::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="11e235e20debbe9f55ee838c5b551769"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::executed" ref="11e235e20debbe9f55ee838c5b551769" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#11e235e20debbe9f55ee838c5b551769">CUpti_ActivityGlobalAccess3::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of times this instruction was executed per warp. It will be incremented when at least one of thread among warp is active with predicate and condition code evaluating to true. 
</div>
</div><p>
<a class="anchor" name="082f16d4239b2b7d929476fd6f159f3d"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::flags" ref="082f16d4239b2b7d929476fd6f159f3d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityGlobalAccess3.html#082f16d4239b2b7d929476fd6f159f3d">CUpti_ActivityGlobalAccess3::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this global access. 
</div>
</div><p>
<a class="anchor" name="3ee4d94d0124d3aa683bcadf0af101c0"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::functionId" ref="3ee4d94d0124d3aa683bcadf0af101c0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#3ee4d94d0124d3aa683bcadf0af101c0">CUpti_ActivityGlobalAccess3::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="07f22bc6656ce9a3c8893a370504640d"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::kind" ref="07f22bc6656ce9a3c8893a370504640d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityGlobalAccess3.html#07f22bc6656ce9a3c8893a370504640d">CUpti_ActivityGlobalAccess3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS. 
</div>
</div><p>
<a class="anchor" name="8d62607220bc310fbffeba2180005f80"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::l2_transactions" ref="8d62607220bc310fbffeba2180005f80" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#8d62607220bc310fbffeba2180005f80">CUpti_ActivityGlobalAccess3::l2_transactions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total number of 32 bytes transactions to L2 cache generated by this access 
</div>
</div><p>
<a class="anchor" name="bd851c0443368383e9d86360c29b3941"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::pcOffset" ref="bd851c0443368383e9d86360c29b3941" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#bd851c0443368383e9d86360c29b3941">CUpti_ActivityGlobalAccess3::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the access. 
</div>
</div><p>
<a class="anchor" name="12392c85bce65a1b3d93b93282909e1e"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::sourceLocatorId" ref="12392c85bce65a1b3d93b93282909e1e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#12392c85bce65a1b3d93b93282909e1e">CUpti_ActivityGlobalAccess3::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="097d3828fdde7d24df95ca676a16f221"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::theoreticalL2Transactions" ref="097d3828fdde7d24df95ca676a16f221" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#097d3828fdde7d24df95ca676a16f221">CUpti_ActivityGlobalAccess3::theoreticalL2Transactions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The minimum number of L2 transactions possible based on the access pattern. 
</div>
</div><p>
<a class="anchor" name="7d06f4a8c882ef0591501cf7488dff0d"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess3::threadsExecuted" ref="7d06f4a8c882ef0591501cf7488dff0d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess3.html#7d06f4a8c882ef0591501cf7488dff0d">CUpti_ActivityGlobalAccess3::threadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction with predicate and condition code evaluating to true. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
