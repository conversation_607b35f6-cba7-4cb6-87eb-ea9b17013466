<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityOpenMp Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityOpenMp Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityOpenMp" -->The base activity record for OpenMp records.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#db6237b2a554d4ad52a6744de746c0db">cuProcessId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#6aa63fab93c46b7be6ecd1dca36256c8">cuThreadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#9516c0145f454bd6852bc6ed13c4fd77">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUpti_OpenMpEventKind&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#4387c46cde06fbb6142b52a2940da8f2">eventKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#9b7ba8776063f211e28cf3c8e053154f">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#0b5dd126b68af77c482b205f58b14889">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#d2953d5453b295c412038ec775c26eea">threadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html#97f91d0a3b0a3b9ffc07aa3ae4d52bac">version</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3" title="The kinds of activity records.">CUpti_ActivityKind</a> </dd></dl>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="db6237b2a554d4ad52a6744de746c0db"></a><!-- doxytag: member="CUpti_ActivityOpenMp::cuProcessId" ref="db6237b2a554d4ad52a6744de746c0db" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenMp.html#db6237b2a554d4ad52a6744de746c0db">CUpti_ActivityOpenMp::cuProcessId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process where the OpenMP activity is executing. 
</div>
</div><p>
<a class="anchor" name="6aa63fab93c46b7be6ecd1dca36256c8"></a><!-- doxytag: member="CUpti_ActivityOpenMp::cuThreadId" ref="6aa63fab93c46b7be6ecd1dca36256c8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenMp.html#6aa63fab93c46b7be6ecd1dca36256c8">CUpti_ActivityOpenMp::cuThreadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the thread where the OpenMP activity is executing. 
</div>
</div><p>
<a class="anchor" name="9516c0145f454bd6852bc6ed13c4fd77"></a><!-- doxytag: member="CUpti_ActivityOpenMp::end" ref="9516c0145f454bd6852bc6ed13c4fd77" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenMp.html#9516c0145f454bd6852bc6ed13c4fd77">CUpti_ActivityOpenMp::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI end timestamp 
</div>
</div><p>
<a class="anchor" name="4387c46cde06fbb6142b52a2940da8f2"></a><!-- doxytag: member="CUpti_ActivityOpenMp::eventKind" ref="4387c46cde06fbb6142b52a2940da8f2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUpti_OpenMpEventKind <a class="el" href="structCUpti__ActivityOpenMp.html#4387c46cde06fbb6142b52a2940da8f2">CUpti_ActivityOpenMp::eventKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenMP event kind (<dl class="see" compact><dt><b>See also:</b></dt><dd>CUpti_OpenMpEventKind) </dd></dl>

</div>
</div><p>
<a class="anchor" name="9b7ba8776063f211e28cf3c8e053154f"></a><!-- doxytag: member="CUpti_ActivityOpenMp::kind" ref="9b7ba8776063f211e28cf3c8e053154f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityOpenMp.html#9b7ba8776063f211e28cf3c8e053154f">CUpti_ActivityOpenMp::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of this activity. 
</div>
</div><p>
<a class="anchor" name="0b5dd126b68af77c482b205f58b14889"></a><!-- doxytag: member="CUpti_ActivityOpenMp::start" ref="0b5dd126b68af77c482b205f58b14889" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenMp.html#0b5dd126b68af77c482b205f58b14889">CUpti_ActivityOpenMp::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI start timestamp 
</div>
</div><p>
<a class="anchor" name="d2953d5453b295c412038ec775c26eea"></a><!-- doxytag: member="CUpti_ActivityOpenMp::threadId" ref="d2953d5453b295c412038ec775c26eea" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenMp.html#d2953d5453b295c412038ec775c26eea">CUpti_ActivityOpenMp::threadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ThreadId 
</div>
</div><p>
<a class="anchor" name="97f91d0a3b0a3b9ffc07aa3ae4d52bac"></a><!-- doxytag: member="CUpti_ActivityOpenMp::version" ref="97f91d0a3b0a3b9ffc07aa3ae4d52bac" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenMp.html#97f91d0a3b0a3b9ffc07aa3ae4d52bac">CUpti_ActivityOpenMp::version</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Version number 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
