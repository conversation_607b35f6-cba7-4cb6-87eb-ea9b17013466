<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_CounterDataImageOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_CounterDataImageOptions Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_CounterDataImageOptions" -->Input parameter to define the counterDataImage.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="0b22a7b53ab96aaa4a45f719d3b5f2b2"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImageOptions::counterDataPrefixSize" ref="0b22a7b53ab96aaa4a45f719d3b5f2b2" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#0b22a7b53ab96aaa4a45f719d3b5f2b2">counterDataPrefixSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Size of CounterDataPrefix generated from NVPW_CounterDataBuilder_GetCounterDataPrefix(). <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="de01ce44b90670ee04849bd593e833d6"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImageOptions::maxNumRanges" ref="de01ce44b90670ee04849bd593e833d6" args="" -->
uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#de01ce44b90670ee04849bd593e833d6">maxNumRanges</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Maximum number of ranges that can be profiled <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="41e525282b7eaec90f0532baeba403e5"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImageOptions::maxNumRangeTreeNodes" ref="41e525282b7eaec90f0532baeba403e5" args="" -->
uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#41e525282b7eaec90f0532baeba403e5">maxNumRangeTreeNodes</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Maximum number of RangeTree nodes; must be &gt;= maxNumRanges <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="8fae036081a86220be88f2eff60941aa"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImageOptions::maxRangeNameLength" ref="8fae036081a86220be88f2eff60941aa" args="" -->
uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#8fae036081a86220be88f2eff60941aa">maxRangeNameLength</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Maximum string length of each RangeName, including the trailing NULL character <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#8dfccc885af68268033d71926c37c1cd">pCounterDataPrefix</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="49561f9b246c682ca7ab8c20e2f55d57"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImageOptions::pPriv" ref="49561f9b246c682ca7ab8c20e2f55d57" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#49561f9b246c682ca7ab8c20e2f55d57">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="005ccabc523d2e4af4104ab0894aff02"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImageOptions::structSize" ref="005ccabc523d2e4af4104ab0894aff02" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#005ccabc523d2e4af4104ab0894aff02">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_CounterDataImageOptions_Params_STRUCT_SIZE <br></td></tr>
</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="8dfccc885af68268033d71926c37c1cd"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImageOptions::pCounterDataPrefix" ref="8dfccc885af68268033d71926c37c1cd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint8_t* <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#8dfccc885af68268033d71926c37c1cd">CUpti_Profiler_CounterDataImageOptions::pCounterDataPrefix</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[in] Address of CounterDataPrefix generated from NVPW_CounterDataBuilder_GetCounterDataPrefix(). Must be align(8). 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
