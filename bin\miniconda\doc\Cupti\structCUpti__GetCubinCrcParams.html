<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_GetCubinCrcParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_GetCubinCrcParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_GetCubinCrcParams" -->Params for cuptiGetCubinCrc.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetCubinCrcParams.html#795bf869a594cf619d53cb0f1745da33">cubin</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetCubinCrcParams.html#5a4ba4f434ae84142462969517c79393">cubinCrc</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetCubinCrcParams.html#7b94af2b1abe10da1d495b242e645a65">cubinSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetCubinCrcParams.html#0b8a3127ca1a3e60013963f3205f6b83">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="795bf869a594cf619d53cb0f1745da33"></a><!-- doxytag: member="CUpti_GetCubinCrcParams::cubin" ref="795bf869a594cf619d53cb0f1745da33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="structCUpti__GetCubinCrcParams.html#795bf869a594cf619d53cb0f1745da33">CUpti_GetCubinCrcParams::cubin</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Pointer to cubin binary 
</div>
</div><p>
<a class="anchor" name="5a4ba4f434ae84142462969517c79393"></a><!-- doxytag: member="CUpti_GetCubinCrcParams::cubinCrc" ref="5a4ba4f434ae84142462969517c79393" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__GetCubinCrcParams.html#5a4ba4f434ae84142462969517c79393">CUpti_GetCubinCrcParams::cubinCrc</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Computed CRC will be stored in it. 
</div>
</div><p>
<a class="anchor" name="7b94af2b1abe10da1d495b242e645a65"></a><!-- doxytag: member="CUpti_GetCubinCrcParams::cubinSize" ref="7b94af2b1abe10da1d495b242e645a65" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__GetCubinCrcParams.html#7b94af2b1abe10da1d495b242e645a65">CUpti_GetCubinCrcParams::cubinSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of cubin binary. 
</div>
</div><p>
<a class="anchor" name="0b8a3127ca1a3e60013963f3205f6b83"></a><!-- doxytag: member="CUpti_GetCubinCrcParams::size" ref="0b8a3127ca1a3e60013963f3205f6b83" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__GetCubinCrcParams.html#0b8a3127ca1a3e60013963f3205f6b83">CUpti_GetCubinCrcParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of configuration structure. CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
