{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvml-dev-12.1.55-0", "features": "", "files": ["LICENSE", "include/nvml.h", "lib/x64/nvml.lib", "nvml/example/README.txt", "nvml/example/example.c", "nvml/example/example.sln", "nvml/example/example.vcxproj", "nvml/example/supportedVgpus.c", "nvml/example/supportedVgpus.sln", "nvml/example/supportedVgpus.vcxproj"], "fn": "cuda-nvml-dev-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvml-dev-12.1.55-0", "type": 1}, "md5": "66ae7fb2d225afecf132a51639eeb010", "name": "cuda-nvml-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvml-dev-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "include/nvml.h", "path_type": "hardlink", "sha256": "8d25e42d29fbf3bca28affa367a8adbca88c9b625e00f3ba3dbbb807c8a836a0", "sha256_in_prefix": "8d25e42d29fbf3bca28affa367a8adbca88c9b625e00f3ba3dbbb807c8a836a0", "size_in_bytes": 502957}, {"_path": "lib/x64/nvml.lib", "path_type": "hardlink", "sha256": "587c566107d38aec67f8b13c12b02e8802a9fd49f1815dbf110b8ecc2428e9b1", "sha256_in_prefix": "587c566107d38aec67f8b13c12b02e8802a9fd49f1815dbf110b8ecc2428e9b1", "size_in_bytes": 83052}, {"_path": "nvml/example/README.txt", "path_type": "hardlink", "sha256": "0d753db603fa13239cdda912436f336e83287322bd4152f5fad5fe2e5c02fd7d", "sha256_in_prefix": "0d753db603fa13239cdda912436f336e83287322bd4152f5fad5fe2e5c02fd7d", "size_in_bytes": 632}, {"_path": "nvml/example/example.c", "path_type": "hardlink", "sha256": "20304af3ff5b73c1d7d3e30f753459df3987b2580c75966fdd748f0d4932bb03", "sha256_in_prefix": "20304af3ff5b73c1d7d3e30f753459df3987b2580c75966fdd748f0d4932bb03", "size_in_bytes": 8099}, {"_path": "nvml/example/example.sln", "path_type": "hardlink", "sha256": "488fbe47b627735f810adf5837f0859060fb84ead6852b853b3b24b99106c5dd", "sha256_in_prefix": "488fbe47b627735f810adf5837f0859060fb84ead6852b853b3b24b99106c5dd", "size_in_bytes": 963}, {"_path": "nvml/example/example.vcxproj", "path_type": "hardlink", "sha256": "9de8dd7d366a37b04d267488473a0e362ed66c21273610aa6c7e21c51e0e5604", "sha256_in_prefix": "9de8dd7d366a37b04d267488473a0e362ed66c21273610aa6c7e21c51e0e5604", "size_in_bytes": 7072}, {"_path": "nvml/example/supportedVgpus.c", "path_type": "hardlink", "sha256": "60f9877b6059fec89396a41a5b6ce200612b8814d30f104b402fe6a7a50604c9", "sha256_in_prefix": "60f9877b6059fec89396a41a5b6ce200612b8814d30f104b402fe6a7a50604c9", "size_in_bytes": 7114}, {"_path": "nvml/example/supportedVgpus.sln", "path_type": "hardlink", "sha256": "ca244679f78e4ea346456ad302ca14896024b306ebe5e3e4f3379b8261692135", "sha256_in_prefix": "ca244679f78e4ea346456ad302ca14896024b306ebe5e3e4f3379b8261692135", "size_in_bytes": 947}, {"_path": "nvml/example/supportedVgpus.vcxproj", "path_type": "hardlink", "sha256": "013d01135f8ad348d61b8c0234cfde02be141810b7c458aa178b6ce3f7a93358", "sha256_in_prefix": "013d01135f8ad348d61b8c0234cfde02be141810b7c458aa178b6ce3f7a93358", "size_in_bytes": 5339}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 89996, "subdir": "win-64", "timestamp": 1674623730000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-nvml-dev-12.1.55-0.tar.bz2", "version": "12.1.55"}