{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-opencl >=12.1.56"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-opencl-dev-12.1.56-0", "features": "", "files": ["include/CL/cl.h", "include/CL/cl.hpp", "include/CL/cl_d3d10.h", "include/CL/cl_d3d10_ext.h", "include/CL/cl_d3d11.h", "include/CL/cl_d3d11_ext.h", "include/CL/cl_d3d9_ext.h", "include/CL/cl_dx9_media_sharing.h", "include/CL/cl_egl.h", "include/CL/cl_ext.h", "include/CL/cl_gl.h", "include/CL/cl_gl_ext.h", "include/CL/cl_platform.h", "include/CL/opencl.h"], "fn": "cuda-opencl-dev-12.1.56-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-opencl-dev-12.1.56-0", "type": 1}, "md5": "129f3035542f8813b8a6c014a7886141", "name": "cuda-opencl-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-opencl-dev-12.1.56-0.tar.bz2", "paths_data": {"paths": [{"_path": "include/CL/cl.h", "path_type": "hardlink", "sha256": "b47a79636c77e46b3ace8409ac8ed940900cc586ff600044f74fbd6d1750dd46", "sha256_in_prefix": "b47a79636c77e46b3ace8409ac8ed940900cc586ff600044f74fbd6d1750dd46", "size_in_bytes": 85901}, {"_path": "include/CL/cl.hpp", "path_type": "hardlink", "sha256": "2f9be4e0fd916dbb1a6f991836157f3d62037d4e619cad638e73cfd0db804bc8", "sha256_in_prefix": "2f9be4e0fd916dbb1a6f991836157f3d62037d4e619cad638e73cfd0db804bc8", "size_in_bytes": 293670}, {"_path": "include/CL/cl_d3d10.h", "path_type": "hardlink", "sha256": "3a8807b929931e76e458260e1a1639cef5f7631f57dcdacc3f3e9470fa657682", "sha256_in_prefix": "3a8807b929931e76e458260e1a1639cef5f7631f57dcdacc3f3e9470fa657682", "size_in_bytes": 5330}, {"_path": "include/CL/cl_d3d10_ext.h", "path_type": "hardlink", "sha256": "aa02353d5842eb1651a549823b1faaf797e147537816d9332d0695ee9b2e632e", "sha256_in_prefix": "aa02353d5842eb1651a549823b1faaf797e147537816d9332d0695ee9b2e632e", "size_in_bytes": 4724}, {"_path": "include/CL/cl_d3d11.h", "path_type": "hardlink", "sha256": "11b49586e6aaf72b4674ec2dc61730035b353c9e40e229f4ec096e0b74b0270c", "sha256_in_prefix": "11b49586e6aaf72b4674ec2dc61730035b353c9e40e229f4ec096e0b74b0270c", "size_in_bytes": 5364}, {"_path": "include/CL/cl_d3d11_ext.h", "path_type": "hardlink", "sha256": "616971611bd6a7677da2a624cb888952ce80a228ddcef04f44d87f6cf73bc304", "sha256_in_prefix": "616971611bd6a7677da2a624cb888952ce80a228ddcef04f44d87f6cf73bc304", "size_in_bytes": 4724}, {"_path": "include/CL/cl_d3d9_ext.h", "path_type": "hardlink", "sha256": "e6d72271ebd002dda8598723910c666962d4c561da560d18164f7c9d40f03cae", "sha256_in_prefix": "e6d72271ebd002dda8598723910c666962d4c561da560d18164f7c9d40f03cae", "size_in_bytes": 5635}, {"_path": "include/CL/cl_dx9_media_sharing.h", "path_type": "hardlink", "sha256": "16f1f53acd833fd0ef8966b23feb6323d0eea19973e5d8d445307a43441f7a09", "sha256_in_prefix": "16f1f53acd833fd0ef8966b23feb6323d0eea19973e5d8d445307a43441f7a09", "size_in_bytes": 10408}, {"_path": "include/CL/cl_egl.h", "path_type": "hardlink", "sha256": "528e745f92cc906a5213e23ea77e20328d94b2880faa8af6b008d361e222d53b", "sha256_in_prefix": "528e745f92cc906a5213e23ea77e20328d94b2880faa8af6b008d361e222d53b", "size_in_bytes": 4554}, {"_path": "include/CL/cl_ext.h", "path_type": "hardlink", "sha256": "d00140ab0dcf4a6b596a32ee848b22dc5b502f5982ec28703183c32a78a24ccf", "sha256_in_prefix": "d00140ab0dcf4a6b596a32ee848b22dc5b502f5982ec28703183c32a78a24ccf", "size_in_bytes": 100069}, {"_path": "include/CL/cl_gl.h", "path_type": "hardlink", "sha256": "2a04ea25aa11af83cd34b48e253902751b7009de1d1a29077901a35c23e39709", "sha256_in_prefix": "2a04ea25aa11af83cd34b48e253902751b7009de1d1a29077901a35c23e39709", "size_in_bytes": 7704}, {"_path": "include/CL/cl_gl_ext.h", "path_type": "hardlink", "sha256": "6dce50f64812e0f5cd6954fea51bcdc519cc95b7fab38804b9a499bd96b18ca8", "sha256_in_prefix": "6dce50f64812e0f5cd6954fea51bcdc519cc95b7fab38804b9a499bd96b18ca8", "size_in_bytes": 923}, {"_path": "include/CL/cl_platform.h", "path_type": "hardlink", "sha256": "c00f59d044a2edc8d36f28ae7d9de0770286c605f32985353bd11d0ee6ff96b8", "sha256_in_prefix": "c00f59d044a2edc8d36f28ae7d9de0770286c605f32985353bd11d0ee6ff96b8", "size_in_bytes": 47134}, {"_path": "include/CL/opencl.h", "path_type": "hardlink", "sha256": "669ce5ad35f253944a11e60b775fadfc5b2ba7a5f0955210fd3534c3e648b143", "sha256_in_prefix": "669ce5ad35f253944a11e60b775fadfc5b2ba7a5f0955210fd3534c3e648b143", "size_in_bytes": 1002}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 61916, "subdir": "win-64", "timestamp": 1674711715000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-opencl-dev-12.1.56-0.tar.bz2", "version": "12.1.56"}