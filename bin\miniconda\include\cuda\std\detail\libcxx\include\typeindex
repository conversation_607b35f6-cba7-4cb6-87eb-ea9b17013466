// -*- C++ -*-
//===-------------------------- typeindex ---------------------------------===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _LIBCUDACXX_TYPEINDEX
#define _LIBCUDACXX_TYPEINDEX

/*

    typeindex synopsis

namespace std
{

class type_index
{
public:
    type_index(const type_info& rhs) noexcept;

    bool operator==(const type_index& rhs) const noexcept;
    bool operator!=(const type_index& rhs) const noexcept;
    bool operator< (const type_index& rhs) const noexcept;
    bool operator<=(const type_index& rhs) const noexcept;
    bool operator> (const type_index& rhs) const noexcept;
    bool operator>=(const type_index& rhs) const noexcept;

    size_t hash_code() const noexcept;
    const char* name() const noexcept;
};

template <>
struct hash<type_index>
    : public unary_function<type_index, size_t>
{
    size_t operator()(type_index index) const noexcept;
};

}  // std

*/

#include <__config>
#include <typeinfo>
#include <__functional_base>

#if defined(_LIBCUDACXX_USE_PRAGMA_GCC_SYSTEM_HEADER)
#pragma GCC system_header
#endif

_LIBCUDACXX_BEGIN_NAMESPACE_STD

class _LIBCUDACXX_TEMPLATE_VIS type_index
{
    const type_info* __t_;
public:
    _LIBCUDACXX_INLINE_VISIBILITY
    type_index(const type_info& __y) _NOEXCEPT : __t_(&__y) {}

    _LIBCUDACXX_INLINE_VISIBILITY
    bool operator==(const type_index& __y) const _NOEXCEPT
        {return *__t_ == *__y.__t_;}
    _LIBCUDACXX_INLINE_VISIBILITY
    bool operator!=(const type_index& __y) const _NOEXCEPT
        {return *__t_ != *__y.__t_;}
    _LIBCUDACXX_INLINE_VISIBILITY
    bool operator< (const type_index& __y) const _NOEXCEPT
        {return  __t_->before(*__y.__t_);}
    _LIBCUDACXX_INLINE_VISIBILITY
    bool operator<=(const type_index& __y) const _NOEXCEPT
        {return !__y.__t_->before(*__t_);}
    _LIBCUDACXX_INLINE_VISIBILITY
    bool operator> (const type_index& __y) const _NOEXCEPT
        {return  __y.__t_->before(*__t_);}
    _LIBCUDACXX_INLINE_VISIBILITY
    bool operator>=(const type_index& __y) const _NOEXCEPT
        {return !__t_->before(*__y.__t_);}

    _LIBCUDACXX_INLINE_VISIBILITY
    size_t hash_code() const _NOEXCEPT {return __t_->hash_code();}
    _LIBCUDACXX_INLINE_VISIBILITY
    const char* name() const _NOEXCEPT {return __t_->name();}
};

template <class _Tp> struct _LIBCUDACXX_TEMPLATE_VIS hash;

template <>
struct _LIBCUDACXX_TEMPLATE_VIS hash<type_index>
    : public unary_function<type_index, size_t>
{
    _LIBCUDACXX_INLINE_VISIBILITY
    size_t operator()(type_index __index) const _NOEXCEPT
        {return __index.hash_code();}
};

_LIBCUDACXX_END_NAMESPACE_STD

#endif  // _LIBCUDACXX_TYPEINDEX
