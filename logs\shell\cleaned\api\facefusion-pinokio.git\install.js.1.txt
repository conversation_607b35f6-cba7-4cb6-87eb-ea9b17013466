######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: a9c67c6d-79ee-41cf-ae3c-6d06be90ed2b
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion --branch 3.3.2 --single-branch
# timestamp: 7/23/2025, 2:17:45 PM (1753305465835)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api\facefusion-pinokio.git>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion --branch 3.3.2 --single-branch
fatal: destination path 'facefusion' already exists and is not an empty directory.

(base) C:\pinokio\api\facefusion-pinokio.git>

######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 82d2c3c7-7420-4990-addd-7f88a58431cd
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install conda-forge::ffmpeg=7.0.2 conda-forge::libvorbis=1.3.7 --yes
# timestamp: 7/23/2025, 2:18:20 PM (1753305500425)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api\facefusion-pinokio.git>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install conda-forge::ffmpeg=7.0.2 conda-forge::libvorbis=1.3.7 --yes
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_5301.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_5301.txt
The process cannot access the file because it is being used by another process.
The system cannot find the file C:\pinokio\cache\TEMP\__conda_tmp_4259.txt.
Failed to run 'conda deactivate'.
C:\pinokio\cache\TEMP\__conda_tmp_4259.txt
WARNING: overwriting environment variables set in the machine
overwriting variable ['PATH']
Channels:
 - conda-forge
 - defaults
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

# All requested packages already installed.

Did not find path entry C:\pinokio\api\facefusion-pinokio.git\.env\bin


(C:\pinokio\api\facefusion-pinokio.git\.env) C:\pinokio\api\facefusion-pinokio.git>

######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 0a8db740-9710-4752-918c-b807ed311cec
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
# timestamp: 7/23/2025, 2:18:40 PM (1753305520393)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api\facefusion-pinokio.git>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
WARNING: overwriting environment variables set in the machine
overwriting variable ['PATH']
Channels:
 - conda-forge
 - defaults
 - nvidia/label/cudnn-9.10.0
 - nvidia/label/cuda-12.9.1
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

# All requested packages already installed.

Did not find path entry C:\pinokio\api\facefusion-pinokio.git\.env\bin


(C:\pinokio\api\facefusion-pinokio.git\.env) C:\pinokio\api\facefusion-pinokio.git>

######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 4cf9b268-6472-493c-9fb6-15d071ac7087
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install tensorrt==********** --extra-index-url https://pypi.nvidia.com
# timestamp: 7/23/2025, 2:18:53 PM (1753305533410)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api\facefusion-pinokio.git>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install tensorrt==********** --extra-index-url https://pypi.nvidia.com
WARNING: overwriting environment variables set in the machine
overwriting variable ['PATH']
Looking in indexes: https://pypi.org/simple, https://pypi.nvidia.com
Requirement already satisfied: tensorrt==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (**********)
Requirement already satisfied: tensorrt_cu12==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt==**********) (**********)
Requirement already satisfied: tensorrt_cu12_libs==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt_cu12==**********->tensorrt==**********) (**********)
Requirement already satisfied: tensorrt_cu12_bindings==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt_cu12==**********->tensorrt==**********) (**********)
Requirement already satisfied: nvidia-cuda-runtime-cu12 in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt_cu12_libs==**********->tensorrt_cu12==**********->tensorrt==**********) (12.9.79)

(C:\pinokio\api\facefusion-pinokio.git\.env) C:\pinokio\api\facefusion-pinokio.git>

######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 239c5871-4ee3-4265-bd6f-52b6b8c9bbe2
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
# timestamp: 7/23/2025, 2:19:53 PM (1753305593200)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api\facefusion-pinokio.git>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
WARNING: overwriting environment variables set in the machine
overwriting variable ['PATH']
Channels:
 - conda-forge
 - defaults
 - nvidia/label/cuda-12.9.1
 - nvidia/label/cudnn-9.10.0
Platform: win-64
Collecting package metadata (repodata.json): done
Solving environment: done

# All requested packages already installed.

Did not find path entry C:\pinokio\api\facefusion-pinokio.git\.env\bin


(C:\pinokio\api\facefusion-pinokio.git\.env) C:\pinokio\api\facefusion-pinokio.git>

######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 0ab8cd9b-56b9-47c3-a629-6fee7a8f4789
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install tensorrt==********** --extra-index-url https://pypi.nvidia.com
# timestamp: 7/23/2025, 2:19:58 PM (1753305598198)

Microsoft Windows [Version 10.0.22631.5472]
(c) Microsoft Corporation. All rights reserved.

C:\pinokio\api\facefusion-pinokio.git>conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install tensorrt==********** --extra-index-url https://pypi.nvidia.com
WARNING: overwriting environment variables set in the machine
overwriting variable ['PATH']
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Looking in indexes: https://pypi.org/simple, https://pypi.nvidia.com
Requirement already satisfied: tensorrt==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (**********)
Requirement already satisfied: tensorrt_cu12==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt==**********) (**********)
Requirement already satisfied: tensorrt_cu12_libs==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt_cu12==**********->tensorrt==**********) (**********)
Requirement already satisfied: tensorrt_cu12_bindings==********** in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt_cu12==**********->tensorrt==**********) (**********)
Requirement already satisfied: nvidia-cuda-runtime-cu12 in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages (from tensorrt_cu12_libs==**********->tensorrt_cu12==**********->tensorrt==**********) (12.9.79)
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)

(C:\pinokio\api\facefusion-pinokio.git\.env) C:\pinokio\api\facefusion-pinokio.git>

######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: b10e49d4-37cd-42b5-8643-776489d98fb7
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && python install.py --onnxruntime cuda
# timestamp: 7/23/2025, 2:24:40 PM (1753305880667)

  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting h11>=0.16 (from httpcore==1.*->httpx<1.0,>=0.24.1->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting python-dateutil>=2.8.2 (from pandas<3.0,>=1.0->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting pytz>=2020.1 (from pandas<3.0,>=1.0->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas<3.0,>=1.0->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting annotated-types>=0.6.0 (from pydantic<2.12,>=2.0->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic<2.12,>=2.0->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached pydantic_core-2.33.2-cp310-cp310-win_amd64.whl.metadata (6.9 kB)
Collecting typing-inspection>=0.4.0 (from pydantic<2.12,>=2.0->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Collecting click>=8.0.0 (from typer<1.0,>=0.12->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting shellingham>=1.3.0 (from typer<1.0,>=0.12->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)
Collecting rich>=10.11.0 (from typer<1.0,>=0.12->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached rich-14.0.0-py3-none-any.whl.metadata (18 kB)
Collecting colorama (from click>=8.0.0->typer<1.0,>=0.12->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached colorama-0.4.6-py2.py3-none-any.whl.metadata (17 kB)
Collecting filelock (from huggingface-hub>=0.28.1->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting requests (from huggingface-hub>=0.28.1->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
Collecting tqdm>=4.42.1 (from huggingface-hub>=0.28.1->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting six>=1.5 (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting markdown-it-py>=2.2.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached pygments-2.19.2-py3-none-any.whl.metadata (2.5 kB)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting charset_normalizer<4,>=2 (from requests->huggingface-hub>=0.28.1->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached charset_normalizer-3.4.2-cp310-cp310-win_amd64.whl.metadata (36 kB)
Collecting urllib3<3,>=1.21.1 (from requests->huggingface-hub>=0.28.1->gradio<6.0,>=4.0->gradio-rangeslider==0.0.8)
  Using cached urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
Using cached gradio_rangeslider-0.0.8-py3-none-any.whl (1.2 MB)
Using cached gradio-5.38.1-py3-none-any.whl (59.5 MB)
Using cached gradio_client-1.11.0-py3-none-any.whl (324 kB)
Using cached aiofiles-24.1.0-py3-none-any.whl (15 kB)
Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Using cached fastapi-0.116.1-py3-none-any.whl (95 kB)
Using cached groovy-0.1.2-py3-none-any.whl (14 kB)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Using cached jinja2-3.1.6-py3-none-any.whl (134 kB)
Using cached MarkupSafe-3.0.2-cp310-cp310-win_amd64.whl (15 kB)
Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl (12.9 MB)
Using cached orjson-3.11.0-cp310-cp310-win_amd64.whl (129 kB)
Using cached pandas-2.3.1-cp310-cp310-win_amd64.whl (11.3 MB)
Using cached pillow-11.3.0-cp310-cp310-win_amd64.whl (7.0 MB)
Using cached pydantic-2.11.7-py3-none-any.whl (444 kB)
Using cached pydantic_core-2.33.2-cp310-cp310-win_amd64.whl (2.0 MB)
Using cached PyYAML-6.0.2-cp310-cp310-win_amd64.whl (161 kB)
Using cached safehttpx-0.1.6-py3-none-any.whl (8.7 kB)
Using cached semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)
Using cached starlette-0.47.2-py3-none-any.whl (72 kB)
Using cached tomlkit-0.13.3-py3-none-any.whl (38 kB)
Using cached typer-0.16.0-py3-none-any.whl (46 kB)
Using cached typing_extensions-4.14.1-py3-none-any.whl (43 kB)
Using cached websockets-15.0.1-cp310-cp310-win_amd64.whl (176 kB)
Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)
Using cached Brotli-1.1.0-cp310-cp310-win_amd64.whl (357 kB)
Using cached click-8.2.1-py3-none-any.whl (102 kB)
Using cached exceptiongroup-1.3.0-py3-none-any.whl (16 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Using cached huggingface_hub-0.33.4-py3-none-any.whl (515 kB)
Using cached fsspec-2025.7.0-py3-none-any.whl (199 kB)
Using cached idna-3.10-py3-none-any.whl (70 kB)
Using cached packaging-25.0-py3-none-any.whl (66 kB)
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Using cached python_multipart-0.0.20-py3-none-any.whl (24 kB)
Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)
Using cached rich-14.0.0-py3-none-any.whl (243 kB)
Using cached pygments-2.19.2-py3-none-any.whl (1.2 MB)
Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Using cached ruff-0.12.4-py3-none-win_amd64.whl (11.2 MB)
Using cached shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)
Using cached typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Using cached uvicorn-0.35.0-py3-none-any.whl (66 kB)
Using cached certifi-2025.7.14-py3-none-any.whl (162 kB)
Using cached colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Using cached ffmpy-0.6.1-py3-none-any.whl (5.5 kB)
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached pydub-0.25.1-py2.py3-none-any.whl (32 kB)
Using cached requests-2.32.4-py3-none-any.whl (64 kB)
Using cached charset_normalizer-3.4.2-cp310-cp310-win_amd64.whl (105 kB)
Using cached urllib3-2.5.0-py3-none-any.whl (129 kB)
WARNING: Ignoring invalid distribution -sspec (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: pytz, pydub, brotli, websockets, urllib3, tzdata, typing-extensions, tomlkit, sniffio, six, shellingham, semantic-version, ruff, pyyaml, python-multipart, pygments, pillow, packaging, orjson, numpy, mdurl, markupsafe, idna, h11, groovy, fsspec, filelock, ffmpy, colorama, charset_normalizer, certifi, annotated-types, aiofiles, typing-inspection, tqdm, requests, python-dateutil, pydantic-core, markdown-it-py, jinja2, httpcore, exceptiongroup, click, uvicorn, rich, pydantic, pandas, huggingface-hub, anyio, typer, starlette, httpx, safehttpx, gradio-client, fastapi, gradio, gradio-rangeslider
  Attempting uninstall: pytz
    Found existing installation: pytz 2025.2
    Uninstalling pytz-2025.2:
      Successfully uninstalled pytz-2025.2
  Attempting uninstall: pydub
    Found existing installation: pydub 0.25.1
    Uninstalling pydub-0.25.1:
      Successfully uninstalled pydub-0.25.1
  Attempting uninstall: brotli
    Found existing installation: Brotli 1.1.0
    Uninstalling Brotli-1.1.0:
      Successfully uninstalled Brotli-1.1.0
  Attempting uninstall: websockets
    Found existing installation: websockets 15.0.1
    Uninstalling websockets-15.0.1:
      Successfully uninstalled websockets-15.0.1
  Attempting uninstall: urllib3
    Found existing installation: urllib3 2.5.0
    Uninstalling urllib3-2.5.0:
      Successfully uninstalled urllib3-2.5.0
  Attempting uninstall: tzdata
    Found existing installation: tzdata 2025.2
    Uninstalling tzdata-2025.2:
      Successfully uninstalled tzdata-2025.2
  Attempting uninstall: typing-extensions
    Found existing installation: typing_extensions 4.14.1
    Uninstalling typing_extensions-4.14.1:
      Successfully uninstalled typing_extensions-4.14.1
  Attempting uninstall: tomlkit
    Found existing installation: tomlkit 0.13.3
    Uninstalling tomlkit-0.13.3:
      Successfully uninstalled tomlkit-0.13.3
  Attempting uninstall: sniffio
    Found existing installation: sniffio 1.3.1
    Uninstalling sniffio-1.3.1:
      Successfully uninstalled sniffio-1.3.1
  Attempting uninstall: six
    Found existing installation: six 1.17.0
    Uninstalling six-1.17.0:
      Successfully uninstalled six-1.17.0
  Attempting uninstall: shellingham
    Found existing installation: shellingham 1.5.4
    Uninstalling shellingham-1.5.4:
      Successfully uninstalled shellingham-1.5.4
  Attempting uninstall: semantic-version
    Found existing installation: semantic-version 2.10.0
    Uninstalling semantic-version-2.10.0:
      Successfully uninstalled semantic-version-2.10.0
  Attempting uninstall: ruff
    Found existing installation: ruff 0.12.4
    Uninstalling ruff-0.12.4:
      Successfully uninstalled ruff-0.12.4
  Attempting uninstall: pyyaml
    Found existing installation: PyYAML 6.0.2
    Uninstalling PyYAML-6.0.2:
      Successfully uninstalled PyYAML-6.0.2
  Attempting uninstall: python-multipart
    Found existing installation: python-multipart 0.0.20
    Uninstalling python-multipart-0.0.20:
      Successfully uninstalled python-multipart-0.0.20
  Attempting uninstall: pygments
    Found existing installation: Pygments 2.19.2
    Uninstalling Pygments-2.19.2:
      Successfully uninstalled Pygments-2.19.2
  Attempting uninstall: pillow
    Found existing installation: pillow 11.3.0
    Uninstalling pillow-11.3.0:
      Successfully uninstalled pillow-11.3.0
  Attempting uninstall: packaging
    Found existing installation: packaging 25.0
    Uninstalling packaging-25.0:
      Successfully uninstalled packaging-25.0
  Attempting uninstall: orjson
    Found existing installation: orjson 3.11.0
    Uninstalling orjson-3.11.0:
      Successfully uninstalled orjson-3.11.0
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.6
    Uninstalling numpy-2.2.6:
      Successfully uninstalled numpy-2.2.6
  Attempting uninstall: mdurl
    Found existing installation: mdurl 0.1.2
    Uninstalling mdurl-0.1.2:
      Successfully uninstalled mdurl-0.1.2
  Attempting uninstall: markupsafe
    Found existing installation: MarkupSafe 3.0.2
    Uninstalling MarkupSafe-3.0.2:
      Successfully uninstalled MarkupSafe-3.0.2
  Attempting uninstall: idna
    Found existing installation: idna 3.10
    Uninstalling idna-3.10:
      Successfully uninstalled idna-3.10
  Attempting uninstall: h11
    Found existing installation: h11 0.16.0
    Uninstalling h11-0.16.0:
      Successfully uninstalled h11-0.16.0
  Attempting uninstall: groovy
    Found existing installation: groovy 0.1.2
    Uninstalling groovy-0.1.2:
      Successfully uninstalled groovy-0.1.2
  Attempting uninstall: fsspec
    Found existing installation: fsspec 2025.7.0
    Uninstalling fsspec-2025.7.0:
      Successfully uninstalled fsspec-2025.7.0
  Attempting uninstall: filelock
    Found existing installation: filelock 3.18.0
    Uninstalling filelock-3.18.0:
      Successfully uninstalled filelock-3.18.0
  Attempting uninstall: ffmpy
    Found existing installation: ffmpy 0.6.1
    Uninstalling ffmpy-0.6.1:
      Successfully uninstalled ffmpy-0.6.1
  Attempting uninstall: colorama
    Found existing installation: colorama 0.4.6
    Uninstalling colorama-0.4.6:
      Successfully uninstalled colorama-0.4.6
  Attempting uninstall: charset_normalizer
    Found existing installation: charset-normalizer 3.4.2
    Uninstalling charset-normalizer-3.4.2:
      Successfully uninstalled charset-normalizer-3.4.2
  Attempting uninstall: certifi
    Found existing installation: certifi 2025.7.14
    Uninstalling certifi-2025.7.14:
      Successfully uninstalled certifi-2025.7.14
  Attempting uninstall: annotated-types
    Found existing installation: annotated-types 0.7.0
    Uninstalling annotated-types-0.7.0:
      Successfully uninstalled annotated-types-0.7.0
  Attempting uninstall: aiofiles
    Found existing installation: aiofiles 24.1.0
    Uninstalling aiofiles-24.1.0:
      Successfully uninstalled aiofiles-24.1.0
  Attempting uninstall: typing-inspection
    Found existing installation: typing-inspection 0.4.1
    Uninstalling typing-inspection-0.4.1:
      Successfully uninstalled typing-inspection-0.4.1
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.67.1
    Uninstalling tqdm-4.67.1:
      Successfully uninstalled tqdm-4.67.1
  Attempting uninstall: requests
    Found existing installation: requests 2.32.4
    Uninstalling requests-2.32.4:
      Successfully uninstalled requests-2.32.4
  Attempting uninstall: python-dateutil
    Found existing installation: python-dateutil 2.9.0.post0
    Uninstalling python-dateutil-2.9.0.post0:
      Successfully uninstalled python-dateutil-2.9.0.post0
  Attempting uninstall: pydantic-core
    Found existing installation: pydantic_core 2.33.2
    Uninstalling pydantic_core-2.33.2:
      Successfully uninstalled pydantic_core-2.33.2
  Attempting uninstall: markdown-it-py
    Found existing installation: markdown-it-py 3.0.0
    Uninstalling markdown-it-py-3.0.0:
      Successfully uninstalled markdown-it-py-3.0.0
  Attempting uninstall: jinja2
    Found existing installation: Jinja2 3.1.6
    Uninstalling Jinja2-3.1.6:
      Successfully uninstalled Jinja2-3.1.6
  Attempting uninstall: httpcore
    Found existing installation: httpcore 1.0.9
    Uninstalling httpcore-1.0.9:
      Successfully uninstalled httpcore-1.0.9
  Attempting uninstall: exceptiongroup
    Found existing installation: exceptiongroup 1.3.0
    Uninstalling exceptiongroup-1.3.0:
      Successfully uninstalled exceptiongroup-1.3.0
  Attempting uninstall: click
    Found existing installation: click 8.2.1
    Uninstalling click-8.2.1:
      Successfully uninstalled click-8.2.1
  Attempting uninstall: uvicorn
    Found existing installation: uvicorn 0.35.0
    Uninstalling uvicorn-0.35.0:
      Successfully uninstalled uvicorn-0.35.0
  Attempting uninstall: rich
    Found existing installation: rich 14.0.0
    Uninstalling rich-14.0.0:
      Successfully uninstalled rich-14.0.0
  Attempting uninstall: pydantic
    Found existing installation: pydantic 2.11.7
    Uninstalling pydantic-2.11.7:
      Successfully uninstalled pydantic-2.11.7
  Attempting uninstall: pandas
    Found existing installation: pandas 2.3.1
    Uninstalling pandas-2.3.1:
      Successfully uninstalled pandas-2.3.1
  Attempting uninstall: huggingface-hub
    Found existing installation: huggingface-hub 0.33.4
    Uninstalling huggingface-hub-0.33.4:
      Successfully uninstalled huggingface-hub-0.33.4
  Attempting uninstall: anyio
    Found existing installation: anyio 4.9.0
    Uninstalling anyio-4.9.0:
      Successfully uninstalled anyio-4.9.0
  Attempting uninstall: typer
    Found existing installation: typer 0.16.0
    Uninstalling typer-0.16.0:
      Successfully uninstalled typer-0.16.0
  Attempting uninstall: starlette
    Found existing installation: starlette 0.47.2
    Uninstalling starlette-0.47.2:
      Successfully uninstalled starlette-0.47.2
  Attempting uninstall: httpx
    Found existing installation: httpx 0.28.1
    Uninstalling httpx-0.28.1:
      Successfully uninstalled httpx-0.28.1
  Attempting uninstall: safehttpx
    Found existing installation: safehttpx 0.1.6
    Uninstalling safehttpx-0.1.6:
      Successfully uninstalled safehttpx-0.1.6
  Attempting uninstall: gradio-client
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╸━━━━ 51/57 [httpx]    WARNING: No metadata found in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages
    Found existing installation: gradio-client 1.8.0
    Can't uninstall 'gradio-client'. No files were found to uninstall.
  Attempting uninstall: fastapi
    Found existing installation: fastapi 0.116.1
    Uninstalling fastapi-0.116.1:
      Successfully uninstalled fastapi-0.116.1
  Attempting uninstall: gradio
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╸━━ 54/57 [fastapi]    WARNING: No metadata found in c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages
    Found existing installation: gradio 5.25.2
    Can't uninstall 'gradio'. No files were found to uninstall.
  Attempting uninstall: gradio-rangeslider
    Found existing installation: gradio_rangeslider 0.0.8
    Uninstalling gradio_rangeslider-0.0.8:
      Successfully uninstalled gradio_rangeslider-0.0.8
Successfully installed aiofiles-24.1.0 annotated-types-0.7.0 anyio-4.9.0 brotli-1.1.0 certifi-2025.7.14 charset_normalizer-3.4.2 click-8.2.1 colorama-0.4.6 exceptiongroup-1.3.0 fastapi-0.116.1 ffmpy-0.6.1 filelock-3.18.0 fsspec-2025.7.0 gradio-5.38.1 gradio-client-1.11.0 gradio-rangeslider-0.0.8 groovy-0.1.2 h11-0.16.0 httpcore-1.0.9 httpx-0.28.1 huggingface-hub-0.33.4 idna-3.10 jinja2-3.1.6 markdown-it-py-3.0.0 markupsafe-3.0.2 mdurl-0.1.2 numpy-2.2.6 orjson-3.11.0 packaging-25.0 pandas-2.3.1 pillow-11.3.0 pydantic-2.11.7 pydantic-core-2.33.2 pydub-0.25.1 pygments-2.19.2 python-dateutil-2.9.0.post0 python-multipart-0.0.20 pytz-2025.2 pyyaml-6.0.2 requests-2.32.4 rich-14.0.0 ruff-0.12.4 safehttpx-0.1.6 semantic-version-2.10.0 shellingham-1.5.4 six-1.17.0 sniffio-1.3.1 starlette-0.47.2 tomlkit-0.13.3 tqdm-4.67.1 typer-0.16.0 typing-extensions-4.14.1 typing-inspection-0.4.1 tzdata-2025.2 urllib3-2.5.0 uvicorn-0.35.0 websockets-15.0.1
WARNING: Ignoring invalid distribution -hellingham (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Collecting gradio==5.25.2
  Using cached gradio-5.25.2-py3-none-any.whl.metadata (16 kB)
Collecting aiofiles<25.0,>=22.0 (from gradio==5.25.2)
  Using cached aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)
Collecting anyio<5.0,>=3.0 (from gradio==5.25.2)
  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting fastapi<1.0,>=0.115.2 (from gradio==5.25.2)
  Using cached fastapi-0.116.1-py3-none-any.whl.metadata (28 kB)
Collecting ffmpy (from gradio==5.25.2)
  Using cached ffmpy-0.6.1-py3-none-any.whl.metadata (2.9 kB)
Collecting gradio-client==1.8.0 (from gradio==5.25.2)
  Using cached gradio_client-1.8.0-py3-none-any.whl.metadata (7.1 kB)
Collecting groovy~=0.1 (from gradio==5.25.2)
  Using cached groovy-0.1.2-py3-none-any.whl.metadata (6.1 kB)
Collecting httpx>=0.24.1 (from gradio==5.25.2)
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting huggingface-hub>=0.28.1 (from gradio==5.25.2)
  Using cached huggingface_hub-0.33.4-py3-none-any.whl.metadata (14 kB)
Collecting jinja2<4.0 (from gradio==5.25.2)
  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting markupsafe<4.0,>=2.0 (from gradio==5.25.2)
  Using cached MarkupSafe-3.0.2-cp310-cp310-win_amd64.whl.metadata (4.1 kB)
Collecting numpy<3.0,>=1.0 (from gradio==5.25.2)
  Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl.metadata (60 kB)
Collecting orjson~=3.0 (from gradio==5.25.2)
  Using cached orjson-3.11.0-cp310-cp310-win_amd64.whl.metadata (43 kB)
Collecting packaging (from gradio==5.25.2)
  Using cached packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting pandas<3.0,>=1.0 (from gradio==5.25.2)
  Using cached pandas-2.3.1-cp310-cp310-win_amd64.whl.metadata (19 kB)
Collecting pillow<12.0,>=8.0 (from gradio==5.25.2)
  Using cached pillow-11.3.0-cp310-cp310-win_amd64.whl.metadata (9.2 kB)
Collecting pydantic<2.12,>=2.0 (from gradio==5.25.2)
  Using cached pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Collecting pydub (from gradio==5.25.2)
  Using cached pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting python-multipart>=0.0.18 (from gradio==5.25.2)
  Using cached python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)
Collecting pyyaml<7.0,>=5.0 (from gradio==5.25.2)
  Using cached PyYAML-6.0.2-cp310-cp310-win_amd64.whl.metadata (2.1 kB)
Collecting ruff>=0.9.3 (from gradio==5.25.2)
  Using cached ruff-0.12.4-py3-none-win_amd64.whl.metadata (26 kB)
Collecting safehttpx<0.2.0,>=0.1.6 (from gradio==5.25.2)
  Using cached safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)
Collecting semantic-version~=2.0 (from gradio==5.25.2)
  Using cached semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)
Collecting starlette<1.0,>=0.40.0 (from gradio==5.25.2)
  Using cached starlette-0.47.2-py3-none-any.whl.metadata (6.2 kB)
Collecting tomlkit<0.14.0,>=0.12.0 (from gradio==5.25.2)
  Using cached tomlkit-0.13.3-py3-none-any.whl.metadata (2.8 kB)
Collecting typer<1.0,>=0.12 (from gradio==5.25.2)
  Using cached typer-0.16.0-py3-none-any.whl.metadata (15 kB)
Collecting typing-extensions~=4.0 (from gradio==5.25.2)
  Using cached typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)
Collecting uvicorn>=0.14.0 (from gradio==5.25.2)
  Using cached uvicorn-0.35.0-py3-none-any.whl.metadata (6.5 kB)
Collecting fsspec (from gradio-client==1.8.0->gradio==5.25.2)
  Using cached fsspec-2025.7.0-py3-none-any.whl.metadata (12 kB)
Collecting websockets<16.0,>=10.0 (from gradio-client==1.8.0->gradio==5.25.2)
  Using cached websockets-15.0.1-cp310-cp310-win_amd64.whl.metadata (7.0 kB)
Collecting exceptiongroup>=1.0.2 (from anyio<5.0,>=3.0->gradio==5.25.2)
  Using cached exceptiongroup-1.3.0-py3-none-any.whl.metadata (6.7 kB)
Collecting idna>=2.8 (from anyio<5.0,>=3.0->gradio==5.25.2)
  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)
Collecting sniffio>=1.1 (from anyio<5.0,>=3.0->gradio==5.25.2)
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting python-dateutil>=2.8.2 (from pandas<3.0,>=1.0->gradio==5.25.2)
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting pytz>=2020.1 (from pandas<3.0,>=1.0->gradio==5.25.2)
  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas<3.0,>=1.0->gradio==5.25.2)
  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting annotated-types>=0.6.0 (from pydantic<2.12,>=2.0->gradio==5.25.2)
  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic<2.12,>=2.0->gradio==5.25.2)
  Using cached pydantic_core-2.33.2-cp310-cp310-win_amd64.whl.metadata (6.9 kB)
Collecting typing-inspection>=0.4.0 (from pydantic<2.12,>=2.0->gradio==5.25.2)
  Using cached typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Collecting click>=8.0.0 (from typer<1.0,>=0.12->gradio==5.25.2)
  Using cached click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting shellingham>=1.3.0 (from typer<1.0,>=0.12->gradio==5.25.2)
  Using cached shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)
Collecting rich>=10.11.0 (from typer<1.0,>=0.12->gradio==5.25.2)
  Using cached rich-14.0.0-py3-none-any.whl.metadata (18 kB)
Collecting colorama (from click>=8.0.0->typer<1.0,>=0.12->gradio==5.25.2)
  Using cached colorama-0.4.6-py2.py3-none-any.whl.metadata (17 kB)
Collecting certifi (from httpx>=0.24.1->gradio==5.25.2)
  Using cached certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
Collecting httpcore==1.* (from httpx>=0.24.1->gradio==5.25.2)
  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting h11>=0.16 (from httpcore==1.*->httpx>=0.24.1->gradio==5.25.2)
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting filelock (from huggingface-hub>=0.28.1->gradio==5.25.2)
  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting requests (from huggingface-hub>=0.28.1->gradio==5.25.2)
  Using cached requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
Collecting tqdm>=4.42.1 (from huggingface-hub>=0.28.1->gradio==5.25.2)
  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting six>=1.5 (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio==5.25.2)
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting markdown-it-py>=2.2.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio==5.25.2)
  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio==5.25.2)
  Using cached pygments-2.19.2-py3-none-any.whl.metadata (2.5 kB)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio==5.25.2)
  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting charset_normalizer<4,>=2 (from requests->huggingface-hub>=0.28.1->gradio==5.25.2)
  Using cached charset_normalizer-3.4.2-cp310-cp310-win_amd64.whl.metadata (36 kB)
Collecting urllib3<3,>=1.21.1 (from requests->huggingface-hub>=0.28.1->gradio==5.25.2)
  Using cached urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
Using cached gradio-5.25.2-py3-none-any.whl (46.9 MB)
Using cached gradio_client-1.8.0-py3-none-any.whl (322 kB)
Using cached aiofiles-24.1.0-py3-none-any.whl (15 kB)
Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Using cached fastapi-0.116.1-py3-none-any.whl (95 kB)
Using cached groovy-0.1.2-py3-none-any.whl (14 kB)
Using cached jinja2-3.1.6-py3-none-any.whl (134 kB)
Using cached MarkupSafe-3.0.2-cp310-cp310-win_amd64.whl (15 kB)
Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl (12.9 MB)
Using cached orjson-3.11.0-cp310-cp310-win_amd64.whl (129 kB)
Using cached pandas-2.3.1-cp310-cp310-win_amd64.whl (11.3 MB)
Using cached pillow-11.3.0-cp310-cp310-win_amd64.whl (7.0 MB)
Using cached pydantic-2.11.7-py3-none-any.whl (444 kB)
Using cached pydantic_core-2.33.2-cp310-cp310-win_amd64.whl (2.0 MB)
Using cached PyYAML-6.0.2-cp310-cp310-win_amd64.whl (161 kB)
Using cached safehttpx-0.1.6-py3-none-any.whl (8.7 kB)
Using cached semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)
Using cached starlette-0.47.2-py3-none-any.whl (72 kB)
Using cached tomlkit-0.13.3-py3-none-any.whl (38 kB)
Using cached typer-0.16.0-py3-none-any.whl (46 kB)
Using cached typing_extensions-4.14.1-py3-none-any.whl (43 kB)
Using cached websockets-15.0.1-cp310-cp310-win_amd64.whl (176 kB)
Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)
Using cached click-8.2.1-py3-none-any.whl (102 kB)
Using cached exceptiongroup-1.3.0-py3-none-any.whl (16 kB)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Using cached huggingface_hub-0.33.4-py3-none-any.whl (515 kB)
Using cached fsspec-2025.7.0-py3-none-any.whl (199 kB)
Using cached idna-3.10-py3-none-any.whl (70 kB)
Using cached packaging-25.0-py3-none-any.whl (66 kB)
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Using cached python_multipart-0.0.20-py3-none-any.whl (24 kB)
Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)
Using cached rich-14.0.0-py3-none-any.whl (243 kB)
Using cached pygments-2.19.2-py3-none-any.whl (1.2 MB)
Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Using cached ruff-0.12.4-py3-none-win_amd64.whl (11.2 MB)
Using cached shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)
Using cached typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Using cached uvicorn-0.35.0-py3-none-any.whl (66 kB)
Using cached certifi-2025.7.14-py3-none-any.whl (162 kB)
Using cached colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Using cached ffmpy-0.6.1-py3-none-any.whl (5.5 kB)
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached pydub-0.25.1-py2.py3-none-any.whl (32 kB)
Using cached requests-2.32.4-py3-none-any.whl (64 kB)
Using cached charset_normalizer-3.4.2-cp310-cp310-win_amd64.whl (105 kB)
Using cached urllib3-2.5.0-py3-none-any.whl (129 kB)
WARNING: Ignoring invalid distribution -hellingham (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: pytz, pydub, websockets, urllib3, tzdata, typing-extensions, tomlkit, sniffio, six, shellingham, semantic-version, ruff, pyyaml, python-multipart, pygments, pillow, packaging, orjson, numpy, mdurl, markupsafe, idna, h11, groovy, fsspec, filelock, ffmpy, colorama, charset_normalizer, certifi, annotated-types, aiofiles, typing-inspection, tqdm, requests, python-dateutil, pydantic-core, markdown-it-py, jinja2, httpcore, exceptiongroup, click, uvicorn, rich, pydantic, pandas, huggingface-hub, anyio, typer, starlette, httpx, safehttpx, gradio-client, fastapi, gradio
  Attempting uninstall: pytz
    Found existing installation: pytz 2025.2
    Uninstalling pytz-2025.2:
      Successfully uninstalled pytz-2025.2
  Attempting uninstall: pydub
    Found existing installation: pydub 0.25.1
    Uninstalling pydub-0.25.1:
      Successfully uninstalled pydub-0.25.1
  Attempting uninstall: websockets
    Found existing installation: websockets 15.0.1
    Uninstalling websockets-15.0.1:
      Successfully uninstalled websockets-15.0.1
  Attempting uninstall: urllib3
    Found existing installation: urllib3 2.5.0
    Uninstalling urllib3-2.5.0:
      Successfully uninstalled urllib3-2.5.0
  Attempting uninstall: tzdata
    Found existing installation: tzdata 2025.2
    Uninstalling tzdata-2025.2:
      Successfully uninstalled tzdata-2025.2
  Attempting uninstall: typing-extensions
    Found existing installation: typing_extensions 4.14.1
    Uninstalling typing_extensions-4.14.1:
      Successfully uninstalled typing_extensions-4.14.1
  Attempting uninstall: tomlkit
    Found existing installation: tomlkit 0.13.3
    Uninstalling tomlkit-0.13.3:
      Successfully uninstalled tomlkit-0.13.3
  Attempting uninstall: sniffio
    Found existing installation: sniffio 1.3.1
    Uninstalling sniffio-1.3.1:
      Successfully uninstalled sniffio-1.3.1
  Attempting uninstall: six
    Found existing installation: six 1.17.0
    Uninstalling six-1.17.0:
      Successfully uninstalled six-1.17.0
  Attempting uninstall: shellingham
   ━━━━━╸━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━  8/55 [six]    WARNING: Ignoring invalid distribution -hellingham (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
    Found existing installation: shellingham 1.5.4
    Uninstalling shellingham-1.5.4:
      Successfully uninstalled shellingham-1.5.4
  Attempting uninstall: semantic-version
    Found existing installation: semantic-version 2.10.0
    Uninstalling semantic-version-2.10.0:
      Successfully uninstalled semantic-version-2.10.0
  Attempting uninstall: ruff
    Found existing installation: ruff 0.12.4
    Uninstalling ruff-0.12.4:
      Successfully uninstalled ruff-0.12.4
  Attempting uninstall: pyyaml
    Found existing installation: PyYAML 6.0.2
    Uninstalling PyYAML-6.0.2:
      Successfully uninstalled PyYAML-6.0.2
  Attempting uninstall: python-multipart
    Found existing installation: python-multipart 0.0.20
    Uninstalling python-multipart-0.0.20:
      Successfully uninstalled python-multipart-0.0.20
  Attempting uninstall: pygments
    Found existing installation: Pygments 2.19.2
    Uninstalling Pygments-2.19.2:
      Successfully uninstalled Pygments-2.19.2
  Attempting uninstall: pillow
    Found existing installation: pillow 11.3.0
    Uninstalling pillow-11.3.0:
      Successfully uninstalled pillow-11.3.0
  Attempting uninstall: packaging
    Found existing installation: packaging 25.0
    Uninstalling packaging-25.0:
      Successfully uninstalled packaging-25.0
  Attempting uninstall: orjson
    Found existing installation: orjson 3.11.0
    Uninstalling orjson-3.11.0:
      Successfully uninstalled orjson-3.11.0
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.6
    Uninstalling numpy-2.2.6:
      Successfully uninstalled numpy-2.2.6
  Attempting uninstall: mdurl
    Found existing installation: mdurl 0.1.2
    Uninstalling mdurl-0.1.2:
      Successfully uninstalled mdurl-0.1.2
  Attempting uninstall: markupsafe
    Found existing installation: MarkupSafe 3.0.2
    Uninstalling MarkupSafe-3.0.2:
      Successfully uninstalled MarkupSafe-3.0.2
  Attempting uninstall: idna
    Found existing installation: idna 3.10
    Uninstalling idna-3.10:
      Successfully uninstalled idna-3.10
  Attempting uninstall: h11
    Found existing installation: h11 0.16.0
    Uninstalling h11-0.16.0:
      Successfully uninstalled h11-0.16.0
  Attempting uninstall: groovy
    Found existing installation: groovy 0.1.2
    Uninstalling groovy-0.1.2:
      Successfully uninstalled groovy-0.1.2
  Attempting uninstall: fsspec
    Found existing installation: fsspec 2025.7.0
    Uninstalling fsspec-2025.7.0:
      Successfully uninstalled fsspec-2025.7.0
  Attempting uninstall: filelock
    Found existing installation: filelock 3.18.0
    Uninstalling filelock-3.18.0:
      Successfully uninstalled filelock-3.18.0
  Attempting uninstall: ffmpy
    Found existing installation: ffmpy 0.6.1
    Uninstalling ffmpy-0.6.1:
      Successfully uninstalled ffmpy-0.6.1
  Attempting uninstall: colorama
    Found existing installation: colorama 0.4.6
    Uninstalling colorama-0.4.6:
      Successfully uninstalled colorama-0.4.6
  Attempting uninstall: charset_normalizer
    Found existing installation: charset-normalizer 3.4.2
    Uninstalling charset-normalizer-3.4.2:
      Successfully uninstalled charset-normalizer-3.4.2
  Attempting uninstall: certifi
    Found existing installation: certifi 2025.7.14
    Uninstalling certifi-2025.7.14:
      Successfully uninstalled certifi-2025.7.14
  Attempting uninstall: annotated-types
    Found existing installation: annotated-types 0.7.0
    Uninstalling annotated-types-0.7.0:
      Successfully uninstalled annotated-types-0.7.0
  Attempting uninstall: aiofiles
    Found existing installation: aiofiles 24.1.0
    Uninstalling aiofiles-24.1.0:
      Successfully uninstalled aiofiles-24.1.0
  Attempting uninstall: typing-inspection
    Found existing installation: typing-inspection 0.4.1
    Uninstalling typing-inspection-0.4.1:
      Successfully uninstalled typing-inspection-0.4.1
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.67.1
    Uninstalling tqdm-4.67.1:
      Successfully uninstalled tqdm-4.67.1
  Attempting uninstall: requests
    Found existing installation: requests 2.32.4
    Uninstalling requests-2.32.4:
      Successfully uninstalled requests-2.32.4
  Attempting uninstall: python-dateutil
    Found existing installation: python-dateutil 2.9.0.post0
    Uninstalling python-dateutil-2.9.0.post0:
      Successfully uninstalled python-dateutil-2.9.0.post0
  Attempting uninstall: pydantic-core
    Found existing installation: pydantic_core 2.33.2
    Uninstalling pydantic_core-2.33.2:
      Successfully uninstalled pydantic_core-2.33.2
  Attempting uninstall: markdown-it-py
    Found existing installation: markdown-it-py 3.0.0
    Uninstalling markdown-it-py-3.0.0:
      Successfully uninstalled markdown-it-py-3.0.0
  Attempting uninstall: jinja2
    Found existing installation: Jinja2 3.1.6
    Uninstalling Jinja2-3.1.6:
      Successfully uninstalled Jinja2-3.1.6
  Attempting uninstall: httpcore
    Found existing installation: httpcore 1.0.9
    Uninstalling httpcore-1.0.9:
      Successfully uninstalled httpcore-1.0.9
  Attempting uninstall: exceptiongroup
    Found existing installation: exceptiongroup 1.3.0
    Uninstalling exceptiongroup-1.3.0:
      Successfully uninstalled exceptiongroup-1.3.0
  Attempting uninstall: click
    Found existing installation: click 8.2.1
    Uninstalling click-8.2.1:
      Successfully uninstalled click-8.2.1
  Attempting uninstall: uvicorn
    Found existing installation: uvicorn 0.35.0
    Uninstalling uvicorn-0.35.0:
      Successfully uninstalled uvicorn-0.35.0
  Attempting uninstall: rich
    Found existing installation: rich 14.0.0
    Uninstalling rich-14.0.0:
      Successfully uninstalled rich-14.0.0
  Attempting uninstall: pydantic
    Found existing installation: pydantic 2.11.7
    Uninstalling pydantic-2.11.7:
      Successfully uninstalled pydantic-2.11.7
  Attempting uninstall: pandas
    Found existing installation: pandas 2.3.1
    Uninstalling pandas-2.3.1:
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╸━━━━━━━ 45/55 [pandas]ERROR: Could not install packages due to an OSError: [('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\__pycache__\\test_map.cpython-310.pyc.2041328490928', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\indexes\\categorical\\__pycache__\\test_map.cpython-310.pyc.2041328490928', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\indexes\\\\categorical\\\\__pycache__\\\\test_map.cpython-310.pyc.2041328490928'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\__pycache__\\test_value_counts.cpython-310.pyc.2041328491504', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\indexes\\datetimelike_\\__pycache__\\test_value_counts.cpython-310.pyc.2041328491504', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\indexes\\\\datetimelike_\\\\__pycache__\\\\test_value_counts.cpython-310.pyc.2041328491504'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\__pycache__\\test_sorting.cpython-310.pyc.2041202943792', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\indexes\\multi\\__pycache__\\test_sorting.cpython-310.pyc.2041202943792', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\indexes\\\\multi\\\\__pycache__\\\\test_sorting.cpython-310.pyc.2041202943792'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\indexes\\period\\__pycache__\\test_setops.cpython-310.pyc.2041202948976', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\indexes\\period\\__pycache__\\test_setops.cpython-310.pyc.2041202948976', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\indexes\\\\period\\\\__pycache__\\\\test_setops.cpython-310.pyc.2041202948976'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\__pycache__\\test_timedelta_range.cpython-310.pyc.2041281449008', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\indexes\\timedeltas\\__pycache__\\test_timedelta_range.cpython-310.pyc.2041281449008', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\indexes\\\\timedeltas\\\\__pycache__\\\\test_timedelta_range.cpython-310.pyc.2041281449008'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\indexing\\interval\\__pycache__\\test_interval_new.cpython-310.pyc', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\indexing\\interval\\__pycache__\\test_interval_new.cpython-310.pyc', "[Errno 13] Permission denied: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\indexing\\\\interval\\\\__pycache__\\\\test_interval_new.cpython-310.pyc'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\internals\\__pycache__\\test_internals.cpython-310.pyc.2041330206128', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\internals\\__pycache__\\test_internals.cpython-310.pyc.2041330206128', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\internals\\\\__pycache__\\\\test_internals.cpython-310.pyc.2041330206128'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\io\\formats\\style\\__pycache__\\test_to_string.cpython-310.pyc.2041281454000', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\io\\formats\\style\\__pycache__\\test_to_string.cpython-310.pyc.2041281454000', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\io\\\\formats\\\\style\\\\__pycache__\\\\test_to_string.cpython-310.pyc.2041281454000'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\io\\parser\\common\\__pycache__\\test_read_errors.cpython-310.pyc.2041281457072', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\io\\parser\\common\\__pycache__\\test_read_errors.cpython-310.pyc.2041281457072', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\io\\\\parser\\\\common\\\\__pycache__\\\\test_read_errors.cpython-310.pyc.2041281457072'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\io\\pytables\\__pycache__\\test_time_series.cpython-310.pyc.2041281461680', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\io\\pytables\\__pycache__\\test_time_series.cpython-310.pyc.2041281461680', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\io\\\\pytables\\\\__pycache__\\\\test_time_series.cpython-310.pyc.2041281461680'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\io\\xml\\__pycache__\\test_to_xml.cpython-310.pyc.2041203201584', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\io\\xml\\__pycache__\\test_to_xml.cpython-310.pyc.2041203201584', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\io\\\\xml\\\\__pycache__\\\\test_to_xml.cpython-310.pyc.2041203201584'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\reshape\\__pycache__\\test_util.cpython-310.pyc.2041335578128', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\reshape\\__pycache__\\test_util.cpython-310.pyc.2041335578128', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\reshape\\\\__pycache__\\\\test_util.cpython-310.pyc.2041335578128'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\__pycache__\\test_tz_localize.cpython-310.pyc.2041281915056', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\scalar\\timestamp\\methods\\__pycache__\\test_tz_localize.cpython-310.pyc.2041281915056', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\scalar\\\\timestamp\\\\methods\\\\__pycache__\\\\test_tz_localize.cpython-310.pyc.2041281915056'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\__pycache__\\test_timezones.cpython-310.pyc.2041281914672', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\scalar\\timestamp\\__pycache__\\test_timezones.cpython-310.pyc.2041281914672', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\scalar\\\\timestamp\\\\__pycache__\\\\test_timezones.cpython-310.pyc.2041281914672'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\series\\__pycache__\\test_ufunc.cpython-310.pyc.2041281973744', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\series\\__pycache__\\test_ufunc.cpython-310.pyc.2041281973744', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\series\\\\__pycache__\\\\test_ufunc.cpython-310.pyc.2041281973744'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\strings\\__pycache__\\test_string_array.cpython-310.pyc.2041281985184', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\strings\\__pycache__\\test_string_array.cpython-310.pyc.2041281985184', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\strings\\\\__pycache__\\\\test_string_array.cpython-310.pyc.2041281985184'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tests\\tseries\\holiday\\__pycache__\\test_observance.cpython-310.pyc.2041281797488', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tests\\tseries\\holiday\\__pycache__\\test_observance.cpython-310.pyc.2041281797488', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tests\\\\tseries\\\\holiday\\\\__pycache__\\\\test_observance.cpython-310.pyc.2041281797488'"), ('c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas\\tseries\\__pycache__\\offsets.cpython-310.pyc.2041331757296', 'C:\\pinokio\\api\\facefusion-pinokio.git\\.env\\Lib\\site-packages\\~andas\\tseries\\__pycache__\\offsets.cpython-310.pyc.2041331757296', "[Errno 2] No such file or directory: 'c:\\\\pinokio\\\\api\\\\facefusion-pinokio.git\\\\.env\\\\lib\\\\site-packages\\\\pandas\\\\tseries\\\\__pycache__\\\\offsets.cpython-310.pyc.2041331757296'")]

WARNING: Ignoring invalid distribution -tarlette (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Collecting numpy==2.2.4
  Using cached numpy-2.2.4-cp310-cp310-win_amd64.whl.metadata (60 kB)
Using cached numpy-2.2.4-cp310-cp310-win_amd64.whl (12.9 MB)
WARNING: Error parsing dependencies of gradio-client: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\gradio_client-1.11.0.dist-info\\METADATA'
WARNING: Error parsing dependencies of pandas: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas-2.3.1.dist-info\\METADATA'
WARNING: Ignoring invalid distribution -tarlette (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: numpy
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.6
    Uninstalling numpy-2.2.6:
      Successfully uninstalled numpy-2.2.6
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
gradio 5.38.1 requires gradio-client==1.11.0, which is not installed.
gradio 5.38.1 requires pandas<3.0,>=1.0, which is not installed.
Successfully installed numpy-2.2.4
Collecting onnx==1.17.0
  Using cached onnx-1.17.0-cp310-cp310-win_amd64.whl.metadata (16 kB)
Collecting numpy>=1.20 (from onnx==1.17.0)
  Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl.metadata (60 kB)
Collecting protobuf>=3.20.2 (from onnx==1.17.0)
  Downloading protobuf-6.31.1-cp310-abi3-win_amd64.whl.metadata (593 bytes)
Using cached onnx-1.17.0-cp310-cp310-win_amd64.whl (14.5 MB)
Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl (12.9 MB)
Downloading protobuf-6.31.1-cp310-abi3-win_amd64.whl (435 kB)
WARNING: Error parsing dependencies of pandas: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas-2.3.1.dist-info\\METADATA'
Installing collected packages: protobuf, numpy, onnx
  Attempting uninstall: protobuf
    Found existing installation: protobuf 6.31.1
    Uninstalling protobuf-6.31.1:
      Successfully uninstalled protobuf-6.31.1
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.4
    Uninstalling numpy-2.2.4:
      Successfully uninstalled numpy-2.2.4
  Attempting uninstall: onnx
    Found existing installation: onnx 1.17.0
    Uninstalling onnx-1.17.0:
      Successfully uninstalled onnx-1.17.0
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
gradio 5.25.2 requires pandas<3.0,>=1.0, which is not installed.
Successfully installed numpy-2.2.6 onnx-1.17.0 protobuf-6.31.1
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Collecting opencv-python==*********
  Using cached opencv_python-*********-cp37-abi3-win_amd64.whl.metadata (20 kB)
Collecting numpy>=1.21.2 (from opencv-python==*********)
  Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl.metadata (60 kB)
Using cached opencv_python-*********-cp37-abi3-win_amd64.whl (39.5 MB)
Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl (12.9 MB)
WARNING: Error parsing dependencies of pandas: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas-2.3.1.dist-info\\METADATA'
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: numpy, opencv-python
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.6
    Uninstalling numpy-2.2.6:
      Successfully uninstalled numpy-2.2.6
  Attempting uninstall: opencv-python
    Found existing installation: opencv-python *********
    Uninstalling opencv-python-*********:
      Successfully uninstalled opencv-python-*********
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
gradio 5.25.2 requires pandas<3.0,>=1.0, which is not installed.
Successfully installed numpy-2.2.6 opencv-python-*********
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Collecting psutil==7.0.0
  Using cached psutil-7.0.0-cp37-abi3-win_amd64.whl.metadata (23 kB)
Using cached psutil-7.0.0-cp37-abi3-win_amd64.whl (244 kB)
WARNING: Error parsing dependencies of pandas: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas-2.3.1.dist-info\\METADATA'
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: psutil
  Attempting uninstall: psutil
    Found existing installation: psutil 7.0.0
    Uninstalling psutil-7.0.0:
      Successfully uninstalled psutil-7.0.0
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Successfully installed psutil-7.0.0
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Collecting tqdm==4.67.1
  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting colorama (from tqdm==4.67.1)
  Using cached colorama-0.4.6-py2.py3-none-any.whl.metadata (17 kB)
Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)
Using cached colorama-0.4.6-py2.py3-none-any.whl (25 kB)
WARNING: Error parsing dependencies of pandas: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas-2.3.1.dist-info\\METADATA'
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: colorama, tqdm
  Attempting uninstall: colorama
    Found existing installation: colorama 0.4.6
    Uninstalling colorama-0.4.6:
      Successfully uninstalled colorama-0.4.6
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.67.1
    Uninstalling tqdm-4.67.1:
      Successfully uninstalled tqdm-4.67.1
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Successfully installed colorama-0.4.6 tqdm-4.67.1
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Collecting scipy==1.15.2
  Using cached scipy-1.15.2-cp310-cp310-win_amd64.whl.metadata (60 kB)
Collecting numpy<2.5,>=1.23.5 (from scipy==1.15.2)
  Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl.metadata (60 kB)
Downloading scipy-1.15.2-cp310-cp310-win_amd64.whl (41.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 41.2/41.2 MB 1.9 MB/s eta 0:00:00
Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl (12.9 MB)
WARNING: Error parsing dependencies of pandas: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas-2.3.1.dist-info\\METADATA'
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: numpy, scipy
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.6
    Uninstalling numpy-2.2.6:
      Successfully uninstalled numpy-2.2.6
  Attempting uninstall: scipy
    Found existing installation: scipy 1.15.2
    Uninstalling scipy-1.15.2:
      Successfully uninstalled scipy-1.15.2
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
gradio 5.25.2 requires pandas<3.0,>=1.0, which is not installed.
Successfully installed numpy-2.2.6 scipy-1.15.2
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Collecting onnxruntime-gpu==1.22.0
  Downloading onnxruntime_gpu-1.22.0-cp310-cp310-win_amd64.whl.metadata (5.1 kB)
Collecting coloredlogs (from onnxruntime-gpu==1.22.0)
  Using cached coloredlogs-15.0.1-py2.py3-none-any.whl.metadata (12 kB)
Collecting flatbuffers (from onnxruntime-gpu==1.22.0)
  Using cached flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)
Collecting numpy>=1.21.6 (from onnxruntime-gpu==1.22.0)
  Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl.metadata (60 kB)
Collecting packaging (from onnxruntime-gpu==1.22.0)
  Using cached packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting protobuf (from onnxruntime-gpu==1.22.0)
  Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl.metadata (593 bytes)
Collecting sympy (from onnxruntime-gpu==1.22.0)
  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)
Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime-gpu==1.22.0)
  Using cached humanfriendly-10.0-py2.py3-none-any.whl.metadata (9.2 kB)
Collecting pyreadline3 (from humanfriendly>=9.1->coloredlogs->onnxruntime-gpu==1.22.0)
  Using cached pyreadline3-3.5.4-py3-none-any.whl.metadata (4.7 kB)
Collecting mpmath<1.4,>=1.1.0 (from sympy->onnxruntime-gpu==1.22.0)
  Using cached mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)
Downloading onnxruntime_gpu-1.22.0-cp310-cp310-win_amd64.whl (214.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 214.9/214.9 MB 2.6 MB/s eta 0:00:00
Using cached numpy-2.2.6-cp310-cp310-win_amd64.whl (12.9 MB)
Using cached coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)
Using cached humanfriendly-10.0-py2.py3-none-any.whl (86 kB)
Using cached flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)
Using cached packaging-25.0-py3-none-any.whl (66 kB)
Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl (435 kB)
Using cached pyreadline3-3.5.4-py3-none-any.whl (83 kB)
Using cached sympy-1.14.0-py3-none-any.whl (6.3 MB)
Using cached mpmath-1.3.0-py3-none-any.whl (536 kB)
WARNING: Error parsing dependencies of pandas: [Errno 2] No such file or directory: 'c:\\pinokio\\api\\facefusion-pinokio.git\\.env\\lib\\site-packages\\pandas-2.3.1.dist-info\\METADATA'
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
Installing collected packages: mpmath, flatbuffers, sympy, pyreadline3, protobuf, packaging, numpy, humanfriendly, coloredlogs, onnxruntime-gpu
  Attempting uninstall: mpmath
    Found existing installation: mpmath 1.3.0
    Uninstalling mpmath-1.3.0:
      Successfully uninstalled mpmath-1.3.0
  Attempting uninstall: flatbuffers
    Found existing installation: flatbuffers 25.2.10
    Uninstalling flatbuffers-25.2.10:
      Successfully uninstalled flatbuffers-25.2.10
  Attempting uninstall: sympy
    Found existing installation: sympy 1.14.0
    Uninstalling sympy-1.14.0:
      Successfully uninstalled sympy-1.14.0
  Attempting uninstall: pyreadline3
    Found existing installation: pyreadline3 3.5.4
    Uninstalling pyreadline3-3.5.4:
      Successfully uninstalled pyreadline3-3.5.4
  Attempting uninstall: protobuf
    Found existing installation: protobuf 6.31.1
    Uninstalling protobuf-6.31.1:
      Successfully uninstalled protobuf-6.31.1
  Attempting uninstall: packaging
    Found existing installation: packaging 25.0
    Uninstalling packaging-25.0:
      Successfully uninstalled packaging-25.0
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.6
    Uninstalling numpy-2.2.6:
      Successfully uninstalled numpy-2.2.6
  Attempting uninstall: humanfriendly
    Found existing installation: humanfriendly 10.0
    Uninstalling humanfriendly-10.0:
      Successfully uninstalled humanfriendly-10.0
  Attempting uninstall: coloredlogs
    Found existing installation: coloredlogs 15.0.1
    Uninstalling coloredlogs-15.0.1:
      Successfully uninstalled coloredlogs-15.0.1
  Attempting uninstall: onnxruntime-gpu
    Found existing installation: onnxruntime-gpu 1.22.0
    Uninstalling onnxruntime-gpu-1.22.0:
      Successfully uninstalled onnxruntime-gpu-1.22.0
WARNING: Ignoring invalid distribution -umpy (c:\pinokio\api\facefusion-pinokio.git\.env\lib\site-packages)
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.
gradio 5.25.2 requires pandas<3.0,>=1.0, which is not installed.
Successfully installed coloredlogs-15.0.1 flatbuffers-25.2.10 humanfriendly-10.0 mpmath-1.3.0 numpy-2.2.6 onnxruntime-gpu-1.22.0 packaging-25.0 protobuf-6.31.1 pyreadline3-3.5.4 sympy-1.14.0
To make your changes take effect please reactivate your environment

(C:\pinokio\api\facefusion-pinokio.git\.env) C:\pinokio\api\facefusion-pinokio.git\facefusion>

