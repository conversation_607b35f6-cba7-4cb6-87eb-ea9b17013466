{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-libraries >=12.1.0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-runtime-12.1.0-0", "features": "", "files": [], "fn": "cuda-runtime-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-runtime-12.1.0-0", "type": 1}, "md5": "fa8278d90eeeb45ef08cc359bb76028c", "name": "cuda-runtime", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-runtime-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1392, "subdir": "win-64", "timestamp": 1677130036000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-runtime-12.1.0-0.tar.bz2", "version": "12.1.0"}