//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_BIT
#define _CUDA_BIT

#include "detail/__config"

#include "detail/__pragma_push"

#include "cstdint"
#include "limits"
#include "type_traits"
#include "detail/libcxx/include/bit"

#include "detail/__pragma_pop"

#endif //_CUDA_BIT
