{"build": "h4f385c5_3", "build_number": 3, "channel": "https://conda.anaconda.org/conda-forge", "constrains": ["cudatoolkit 12.9|12.9.*", "__cuda >=12"], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-version-12.9-h4f385c5_3", "features": "", "files": [], "fn": "cuda-version-12.9-h4f385c5_3.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-version-12.9-h4f385c5_3", "type": 1}, "md5": "b6d5d7f1c171cbd228ea06b556cfa859", "name": "cuda-version", "noarch": "generic", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-version-12.9-h4f385c5_3.conda", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "5f5f428031933f117ff9f7fcc650e6ea1b3fef5936cf84aa24af79167513b656", "size": 21578, "subdir": "noarch", "timestamp": 1746134436000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda", "version": "12.9"}