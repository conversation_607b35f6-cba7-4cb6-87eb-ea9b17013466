<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params" -->Params for cuptiProfilerCounterDataImageInitializeScratchBuffer.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="c5761b37242e63389003fdd4ce1281f3"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params::counterDataImageSize" ref="c5761b37242e63389003fdd4ce1281f3" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#c5761b37242e63389003fdd4ce1281f3">counterDataImageSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] size calculated from cuptiProfilerCounterDataImageCalculateSize <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="837ad10a6070eca5355dd24d53a4bba9"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params::counterDataScratchBufferSize" ref="837ad10a6070eca5355dd24d53a4bba9" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#837ad10a6070eca5355dd24d53a4bba9">counterDataScratchBufferSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] size calculated using cuptiProfilerCounterDataImageCalculateScratchBufferSize <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="6cfc69416dff06c5898fdcc4c716a4b3"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params::pCounterDataImage" ref="6cfc69416dff06c5898fdcc4c716a4b3" args="" -->
uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#6cfc69416dff06c5898fdcc4c716a4b3">pCounterDataImage</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="0287d7929c16694c07d3a4e9c34c1e6a"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params::pCounterDataScratchBuffer" ref="0287d7929c16694c07d3a4e9c34c1e6a" args="" -->
uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#0287d7929c16694c07d3a4e9c34c1e6a">pCounterDataScratchBuffer</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] the scratch buffer to be initialized. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="7733fe90bba9bffba109b23dc621831e"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params::pPriv" ref="7733fe90bba9bffba109b23dc621831e" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#7733fe90bba9bffba109b23dc621831e">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="4605887493f255b93dd5ef346169d53d"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params::structSize" ref="4605887493f255b93dd5ef346169d53d" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#4605887493f255b93dd5ef346169d53d">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params_STRUCT_SIZE <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
