{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvprune-12.1.55-0", "features": "", "files": ["LICENSE", "bin/nvprune.exe"], "fn": "cuda-nvprune-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvprune-12.1.55-0", "type": 1}, "md5": "a9a8fc80875cf0a9c90b2272623c88c2", "name": "cuda-nvprune", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvprune-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "bin/nvprune.exe", "path_type": "hardlink", "sha256": "95cfaa145d698f3b334ce1a1fd12fc11343f87d1779a9eec8f758be1b32fe419", "sha256_in_prefix": "95cfaa145d698f3b334ce1a1fd12fc11343f87d1779a9eec8f758be1b32fe419", "size_in_bytes": 252928}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 153739, "subdir": "win-64", "timestamp": 1674622573000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-nvprune-12.1.55-0.tar.bz2", "version": "12.1.55"}