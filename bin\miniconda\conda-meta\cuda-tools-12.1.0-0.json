{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-command-line-tools >=12.1.0", "cuda-visual-tools >=12.1.0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-tools-12.1.0-0", "features": "", "files": [], "fn": "cuda-tools-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-tools-12.1.0-0", "type": 1}, "md5": "41d1c06f13889420690b5c09e4857a6f", "name": "cuda-tools", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-tools-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1408, "subdir": "win-64", "timestamp": 1677130062000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-tools-12.1.0-0.tar.bz2", "version": "12.1.0"}