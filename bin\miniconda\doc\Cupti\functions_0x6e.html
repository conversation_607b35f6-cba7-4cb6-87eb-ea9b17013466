<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li class="current"><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_n">- n -</a></h3><ul>
<li>name
: <a class="el" href="structCUpti__ActivityMemory.html#f6a4df9e6e990204a80a7fce37d95ac2">CUpti_ActivityMemory</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#1ce4277d58693c0bbe8b63f9a8d34e25">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityKernel.html#2ed011696782a9fb7f07279dfbfe9751">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#02898b016ca764a8fb5b10102de544e7">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#bbd6a67338f1a93a3977b0133ba03d7f">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#4e1a2f98b3ba230045e977be43e3c95a">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#e5958d24f048e60c4e740be144fe409c">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityName.html#fe9c75229f1fb6a067be030945f73bd6">CUpti_ActivityName</a>
, <a class="el" href="structCUpti__ActivityMarker.html#c3967aa4171fa1e30be9c31f16bd0369">CUpti_ActivityMarker</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#79a76e374b09d20bfdf2474c6f9fa6b3">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#e64321d051a4d89c07c7c9235463e18b">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityMarker2.html#b7a66939efa7a3418b82f73d570d6547">CUpti_ActivityMarker2</a>
, <a class="el" href="structCUpti__ActivityFunction.html#39f0ffa701450d3efd386c78d58edf10">CUpti_ActivityFunction</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#74adea6e1869f5acdf9e87d3fc36c38c">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#2a52532acd95190824fed56f76889853">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#74d99310843f770590957310c1119031">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#e2fa5eeb7e2b97e535cb744a5a82c8e7">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#9300af95de27b8b6e3f57fd47b29f4c3">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#3985681a4104f3d7b5e9c91330643612">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityDevice.html#634327dd4c2979d083e97b5d701a4245">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#4b05a23714cb721352d4466f45eeb7d5">CUpti_ActivityDevice2</a>
<li>node
: <a class="el" href="structCUpti__GraphData.html#432140988d6d3883d41a438a3b5906fa">CUpti_GraphData</a>
<li>nodeType
: <a class="el" href="structCUpti__GraphData.html#8d6e9993380ae829ad380d1549c85889">CUpti_GraphData</a>
<li>nonUsrKernelsTotalSamples
: <a class="el" href="structCUpti__PCSamplingData.html#af69505071b7815e9d9a1e9f75bcbd44">CUpti_PCSamplingData</a>
<li>notPredOffThreadsExecuted
: <a class="el" href="structCUpti__ActivityInstructionExecution.html#1d8a8b6191d0fe41e1231def4a86feef">CUpti_ActivityInstructionExecution</a>
<li>nullStreamId
: <a class="el" href="structCUpti__ActivityContext.html#d35227038d80fe0275ba23e17561af62">CUpti_ActivityContext</a>
<li>numAttributes
: <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#6b007258d8944934683cff267ca212df">CUpti_PCSamplingConfigurationInfoParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#6093fbb19d3a729a999adabc05181b87">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#a5938dd3236f8326d2aaec8ea92b80ee">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
<li>numberOfBuffers
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#c29d94c02d27b7e7564512afbb89776b">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a>
<li>numEventGroups
: <a class="el" href="structCUpti__EventGroupSet.html#18b3e9aa81b673c5b6c2f74a6203e3bb">CUpti_EventGroupSet</a>
<li>numGangs
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#9111d35a7b7a041d57d6261ecb97db86">CUpti_ActivityOpenAccLaunch</a>
<li>numMemcpyEngines
: <a class="el" href="structCUpti__ActivityDevice.html#5793ec045865bde52ec2b6b20aa8ae3e">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#1558e53e287f66841dcfe1879b137251">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#e3b6102eba7bedad732e2ae9466d69de">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#d681fcc27d1b233e67ff24f75b30495d">CUpti_ActivityDevice4</a>
<li>numMergedBuffer
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#e079332020b290e992840b0f60b00cd0">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a>
<li>numMultiprocessors
: <a class="el" href="structCUpti__ActivityDevice.html#0a8d0f23f95aa19c84950a121bdc6230">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#1b8d2d2350ef003289b157051f5055a5">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#3a41a14294f152f369456a5c29a386e6">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#4f6e49c188a1615bf416670956a570c7">CUpti_ActivityDevice2</a>
<li>numNestingLevels
: <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#2c0ba956bca45b7407bd0a7eef8dc607">CUpti_Profiler_SetConfig_Params</a>
<li>numRangesDropped
: <a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#c2a4281719c88dcdf4f105d516ab0030">CUpti_Profiler_IsPassCollected_Params</a>
, <a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#ea9923fecff1ec2eec224cd8516cd474">CUpti_Profiler_FlushCounterData_Params</a>
<li>numSelectedStallReasons
: <a class="el" href="structBufferInfo.html#88792b744655eb9d28401ccc5e0e4edd">BufferInfo</a>
<li>numSets
: <a class="el" href="structCUpti__EventGroupSets.html#a87e3a2fcaee6dc7ed18a6c40a79f831">CUpti_EventGroupSets</a>
<li>numStallReasons
: <a class="el" href="structPcSamplingStallReasons.html#bef119b31f4c7d9f3fc14c7988703326">PcSamplingStallReasons</a>
, <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#cc76037484523a10b10a75b634926eaa">CUpti_PCSamplingGetStallReasonsParams</a>
, <a class="el" href="structBufferInfo.html#f7cae81afad438c1f2d4921da89432e3">BufferInfo</a>
, <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#9f4cd2aac4b681aa3d3a9641fc916355">CUpti_PCSamplingGetNumStallReasonsParams</a>
<li>numThreadsPerWarp
: <a class="el" href="structCUpti__ActivityDevice4.html#35d812373cb37ff3f1c2b28a1bfc202f">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#9a8755f3467aa55414dbe7b0ca4a95f7">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#3b1098a032c9df1607810bfc62d5d90b">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice.html#c9a763efd5c2f3d250a615935484ece3">CUpti_ActivityDevice</a>
<li>numTraceBytesDropped
: <a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#a259efdbb47b23017ba4d55b8c772ec1">CUpti_Profiler_IsPassCollected_Params</a>
, <a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#ed6bc209584c89a19ca2dab4929da3eb">CUpti_Profiler_FlushCounterData_Params</a>
<li>numWorkers
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#245c5b442944988d46429160953a699f">CUpti_ActivityOpenAccLaunch</a>
<li>nvlinkVersion
: <a class="el" href="structCUpti__ActivityNvLink4.html#c7984f64edaac13a46169e6703c72746">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#6a1b2ad0944e964b9aa86ac339407ed5">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#9ac440e343e09e2a4fb211e9a2b4c11a">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink.html#f5dca02236bf7fbe1369d820899a5d75">CUpti_ActivityNvLink</a>
<li>nvswitchConnected
: <a class="el" href="structCUpti__ActivityNvLink4.html#945941009ff11011bccd4abfddbb2724">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#29c22f74e9b4da2c357246f5350e7ab3">CUpti_ActivityNvLink3</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
