<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li class="current"><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_f">- f -</a></h3><ul>
<li>fanSpeed
: <a class="el" href="structCUpti__ActivityEnvironment.html#5941e586a8b85e49dcd20212df44c82d">CUpti_ActivityEnvironment</a>
<li>fileHandler
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#ee70dc881868b513d73c1bc6daa376a5">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#3da28dd6faa77904a1dee9608a3296e8">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#f34d532700a114abc5360396d1bd384c">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a>
<li>fileName
: <a class="el" href="structCUpti__ActivitySourceLocator.html#0d08babf85a0fd2fc0b4a9ac5dd365fe">CUpti_ActivitySourceLocator</a>
, <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#5fd74201d9184aa6d7e482f22ba6cd33">CUpti_GetSassToSourceCorrelationParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#696ede0d98ac0e500a4a74bbdc717de3">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a>
<li>flag
: <a class="el" href="structCUpti__ActivityStream.html#adb997115ccd43b2fa7815e25e620465">CUpti_ActivityStream</a>
, <a class="el" href="structCUpti__ActivityNvLink.html#a51fecf57e08c0ea0f6894dcdf921a3f">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#f217b34a45bda6725821b44bf5c2c035">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#fc057d60b7056996eb327282cbbcd42c">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#41e344a24884357191daf2726c840681">CUpti_ActivityNvLink4</a>
<li>flags
: <a class="el" href="structCUpti__ActivityMemcpy5.html#818723ed5e9038d1c1540f53068bbb29">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#54677f82410f5e3cb57abbf21397e7c9">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#9bb3a90c90223464d90e1c3e53ea8f0b">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#99a3ffbf3db91324072d87d855c3cf2a">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityDeviceAttribute.html#296b9b6fed50288470026727b630e9c3">CUpti_ActivityDeviceAttribute</a>
, <a class="el" href="structCUpti__ActivityMarker.html#2b83a27fe4d92c89b951f6d004b84b8f">CUpti_ActivityMarker</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#01c6ab05b31cbffe53f0e1dfe93c25d6">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMarker2.html#d2f907bc6015b0c0053dab9b743f7ce7">CUpti_ActivityMarker2</a>
, <a class="el" href="structCUpti__ActivityMarkerData.html#689fdd97518cec0e8f3cbd6d0eff90ce">CUpti_ActivityMarkerData</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#7d7feac72f3c890e69654c08e1d1ca85">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#9854a67ec5fd25c9d3157e8804af1466">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivityPCSampling.html#c2c84ca7f56ad78f3641942a1991ef5c">CUpti_ActivityPCSampling</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#576bee56bea2b04feb7a40e55b334465">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityPCSampling2.html#48b54f9ebe412db579a739569fb51f1c">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#db195817dc4a6192c48a74202baeb44f">CUpti_ActivityPCSampling3</a>
, <a class="el" href="structCUpti__ActivityMemset.html#367e3d07412add37fa2a20e74a05e8e7">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#3095c670ddd8889a411179ffd7e3954a">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#cd28ed15e564582a3b58d0ef614eb950">CUpti_ActivityInstantaneousMetricInstance</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#64d92d1f3a816f6e4c8194580b65c7cc">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#f97b22e7b2aceb126e32d09e83fd318b">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityInstructionCorrelation.html#54daa0e83bf68b4246f0a506076c219c">CUpti_ActivityInstructionCorrelation</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#5998404f431736262d803dfe7e582de2">CUpti_ActivityInstantaneousMetric</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#9972f3b0dcf3e7c45e288dcc378bb938">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#37150e683fdc637c337a63870fb480e9">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityMetric.html#d7a8b000ee8532c01c863b93618f7105">CUpti_ActivityMetric</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#02f04d50706dc986ff487572e28396b2">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMetricInstance.html#925a1b4effa4f263bb2cd4ed21324361">CUpti_ActivityMetricInstance</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess.html#a1e24ad05441330453e90245e2dc64fa">CUpti_ActivityGlobalAccess</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#91e830d5ea735c4a0905f69e37d764d3">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#a937cff74ff532080cb0d3c585641836">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#082f16d4239b2b7d929476fd6f159f3d">CUpti_ActivityGlobalAccess3</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#7fa84a0116642c7960a8cf4066daefd5">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityDevice.html#de7e83e5e5b6391e5cc5f0fbd165a19c">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#79551d9408dd17a6c54668be2eeb2635">CUpti_ActivityDevice2</a>
<li>freePC
: <a class="el" href="structCUpti__ActivityMemory.html#e8d5833b4227fe9350cb35e0176c1f99">CUpti_ActivityMemory</a>
<li>funcEndLineNo
: <a class="el" href="structCUpti__ActivityOpenAcc.html#237d817dec1fbda3ade3e62dc26d6437">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#005016da79873557a652c47c19eb0fba">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#49e27bd7d04829bebab835603cf18d60">CUpti_ActivityOpenAccOther</a>
<li>funcLineNo
: <a class="el" href="structCUpti__ActivityOpenAccOther.html#2dae9dea02831e90bd3d29e8b90f37ad">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#c5d4a85a1757ed1598cf5a0a72740289">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#d6cbaf987363d22784d919a8a639b1f7">CUpti_ActivityOpenAcc</a>
<li>funcName
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#3ca77809b50823e4aab22f7d29e68093">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#2707237f98dd557b8c97a4cd15a5b8c8">CUpti_ActivityOpenAccOther</a>
<li>functionId
: <a class="el" href="structCUpti__ActivityPCSampling.html#6a9defe410cea946cccbfdb74b481e76">CUpti_ActivityPCSampling</a>
, <a class="el" href="structCUpti__ActivityInstructionCorrelation.html#979332452771c51fc42bb4f6c6d82f7a">CUpti_ActivityInstructionCorrelation</a>
, <a class="el" href="structCUpti__ActivityPCSampling2.html#a79550dec752213b2af90dba0d3e72a8">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__ActivityBranch2.html#3ec1e59a986523300bc8577140a9e9c5">CUpti_ActivityBranch2</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#64c38ef04b383c6c69e518a99e60a161">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#5a984df75a3ac618198eaed58b8e94de">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#26050e961db8caf048c51e1c34f5b972">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#3ee4d94d0124d3aa683bcadf0af101c0">CUpti_ActivityGlobalAccess3</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#ec2ac604ceefb2fa93022e578c786396">CUpti_ActivityPCSampling3</a>
<li>functionIndex
: <a class="el" href="structCUpti__ActivityFunction.html#60f1d14ce36ad6cce3e2933c90a9ae72">CUpti_ActivityFunction</a>
, <a class="el" href="structCUpti__PCSamplingPCData.html#f15888e0f326e1281d22f9c2ce83a4db">CUpti_PCSamplingPCData</a>
<li>functionName
: <a class="el" href="structCUpti__CallbackData.html#45423011db4b6b3078f96380e7c43076">CUpti_CallbackData</a>
, <a class="el" href="structCUpti__PCSamplingPCData.html#cf1a26f89202959daa2566316f6bad6b">CUpti_PCSamplingPCData</a>
, <a class="el" href="structCUpti__NvtxData.html#29b7697eacc3130d6e789441e880f56b">CUpti_NvtxData</a>
, <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#d381fee6d12b4b5ea4003be6b0acff52">CUpti_GetSassToSourceCorrelationParams</a>
<li>functionParams
: <a class="el" href="structCUpti__CallbackData.html#0498588b68ecc63a07b30d88d1d7171c">CUpti_CallbackData</a>
, <a class="el" href="structCUpti__NvtxData.html#f7e94572b94c6d872c6a402ec026feaa">CUpti_NvtxData</a>
<li>functionReturnValue
: <a class="el" href="structCUpti__NvtxData.html#6ffd67804a43a3e3180b5127621f0214">CUpti_NvtxData</a>
, <a class="el" href="structCUpti__CallbackData.html#659cca9a4d4498b5da937e5a65e0bf13">CUpti_CallbackData</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
