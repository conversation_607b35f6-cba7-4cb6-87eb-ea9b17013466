<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI PC Sampling API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI PC Sampling API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetCubinCrcParams.html">CUpti_GetCubinCrcParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiGetCubinCrc.  <a href="structCUpti__GetCubinCrcParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html">CUpti_GetSassToSourceCorrelationParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiGetSassToSourceCorrelation.  <a href="structCUpti__GetSassToSourceCorrelationParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC sampling configuration information structure.  <a href="structCUpti__PCSamplingConfigurationInfo.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html">CUpti_PCSamplingConfigurationInfoParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC sampling configuration structure.  <a href="structCUpti__PCSamplingConfigurationInfoParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html">CUpti_PCSamplingData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Collected PC Sampling data.  <a href="structCUpti__PCSamplingData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingDisableParams.html">CUpti_PCSamplingDisableParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiPCSamplingDisable.  <a href="structCUpti__PCSamplingDisableParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingEnableParams.html">CUpti_PCSamplingEnableParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiPCSamplingEnable.  <a href="structCUpti__PCSamplingEnableParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetDataParams.html">CUpti_PCSamplingGetDataParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiPCSamplingEnable.  <a href="structCUpti__PCSamplingGetDataParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html">CUpti_PCSamplingGetNumStallReasonsParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiPCSamplingGetNumStallReasons.  <a href="structCUpti__PCSamplingGetNumStallReasonsParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html">CUpti_PCSamplingGetStallReasonsParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiPCSamplingGetStallReasons.  <a href="structCUpti__PCSamplingGetStallReasonsParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html">CUpti_PCSamplingPCData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC Sampling data.  <a href="structCUpti__PCSamplingPCData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingStallReason.html">CUpti_PCSamplingStallReason</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC Sampling stall reasons.  <a href="structCUpti__PCSamplingStallReason.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingStartParams.html">CUpti_PCSamplingStartParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiPCSamplingStart.  <a href="structCUpti__PCSamplingStartParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingStopParams.html">CUpti_PCSamplingStopParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiPCSamplingStop.  <a href="structCUpti__PCSamplingStopParams.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef void(*&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g8eaf1a578c894725602cf455756927ab">CUpti_ComputeCrcCallbackFunc</a> )(const void *cubin, size_t cubinSize, uint64_t *cubinCrc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for callback used by CUPTI to request crc of loaded module.  <a href="#g8eaf1a578c894725602cf455756927ab"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g7fcaa834e5520b282b2a3b1de3f304b4">CUpti_PCSamplingCollectionMode</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg7fcaa834e5520b282b2a3b1de3f304b4e39aa8671291e4089b4881797ea2018b">CUPTI_PC_SAMPLING_COLLECTION_MODE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg7fcaa834e5520b282b2a3b1de3f304b4d11fdae148d59d91408f90b149cc1181">CUPTI_PC_SAMPLING_COLLECTION_MODE_CONTINUOUS</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg7fcaa834e5520b282b2a3b1de3f304b4c3df7d2baba155e561d3cbd3ac1ed2c0">CUPTI_PC_SAMPLING_COLLECTION_MODE_KERNEL_SERIALIZED</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC Sampling collection mode.  <a href="group__CUPTI__PCSAMPLING__API.html#g7fcaa834e5520b282b2a3b1de3f304b4">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g4fe866bd47d8825f4d46cc52c8e29bb5">CUpti_PCSamplingConfigurationAttributeType</a> { , <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb55a3304f055fc43b3f1885f2b5fe3d35c">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_PERIOD</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb56b99e85c0ca755ba6f1e1b68af6de405">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_STALL_REASON</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb58f4358cee727885dde3311a81bb996cc">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SCRATCH_BUFFER_SIZE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5979e3dbbec977c8141ba3e01b8dbf014">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_HARDWARE_BUFFER_SIZE</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5a561bd34aebe51eb26e4695ea0d865bb">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_COLLECTION_MODE</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5c75dea72b5908405f9096dd1ae27366b">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5a19b4a5c74150e029c95a6a320018f6a">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_OUTPUT_DATA_FORMAT</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5cefa869098e32174b5ed1e8dfd96af06">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_DATA_BUFFER</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb567b78d0e6180a9cb3220e49e6b842748">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_WORKER_THREAD_PERIODIC_SLEEP_SPAN</a> =  9
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC Sampling configuration attributes.  <a href="group__CUPTI__PCSAMPLING__API.html#g4fe866bd47d8825f4d46cc52c8e29bb5">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g6959d5af9fa0ce5b41af09276fc190f7">CUpti_PCSamplingOutputDataFormat</a> { , <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg6959d5af9fa0ce5b41af09276fc190f71dfda31478f7233dc0bf091a7e84fdf9">CUPTI_PC_SAMPLING_OUTPUT_DATA_FORMAT_PARSED</a> =  1
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC Sampling output data format.  <a href="group__CUPTI__PCSAMPLING__API.html#g6959d5af9fa0ce5b41af09276fc190f7">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g58d9101ff62121eb735b19167153a4b3">cuptiGetCubinCrc</a> (<a class="el" href="structCUpti__GetCubinCrcParams.html">CUpti_GetCubinCrcParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the CRC of cubin.  <a href="#g58d9101ff62121eb735b19167153a4b3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g35c9023ef22a59235de77b15496814eb">cuptiGetSassToSourceCorrelation</a> (<a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html">CUpti_GetSassToSourceCorrelationParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">SASS to Source correlation.  <a href="#g35c9023ef22a59235de77b15496814eb"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#gcbe97deb69ca2795102902544df571e3">cuptiPCSamplingDisable</a> (<a class="el" href="structCUpti__PCSamplingDisableParams.html">CUpti_PCSamplingDisableParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Disable PC sampling.  <a href="#gcbe97deb69ca2795102902544df571e3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#gf76a8824108dc7517ba80c2e56d46058">cuptiPCSamplingEnable</a> (<a class="el" href="structCUpti__PCSamplingEnableParams.html">CUpti_PCSamplingEnableParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable PC sampling.  <a href="#gf76a8824108dc7517ba80c2e56d46058"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#gda8f3c53c8673e56a689a67cae7e8df2">cuptiPCSamplingGetConfigurationAttribute</a> (<a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html">CUpti_PCSamplingConfigurationInfoParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read PC Sampling configuration attribute.  <a href="#gda8f3c53c8673e56a689a67cae7e8df2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g6a34245ee39a52a31e231845af598e6b">cuptiPCSamplingGetData</a> (<a class="el" href="structCUpti__PCSamplingGetDataParams.html">CUpti_PCSamplingGetDataParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flush GPU PC sampling data periodically.  <a href="#g6a34245ee39a52a31e231845af598e6b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g5f5393a42625671275d30efaf8e25c3b">cuptiPCSamplingGetNumStallReasons</a> (<a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html">CUpti_PCSamplingGetNumStallReasonsParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get PC sampling stall reason count.  <a href="#g5f5393a42625671275d30efaf8e25c3b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g74ff0bc692635200205045f6da595b8d">cuptiPCSamplingGetStallReasons</a> (<a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html">CUpti_PCSamplingGetStallReasonsParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get PC sampling stall reasons.  <a href="#g74ff0bc692635200205045f6da595b8d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#gf3e8842fda99b4c1659c2d2f1c18674e">cuptiPCSamplingSetConfigurationAttribute</a> (<a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html">CUpti_PCSamplingConfigurationInfoParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Write PC Sampling configuration attribute.  <a href="#gf3e8842fda99b4c1659c2d2f1c18674e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g569d99977964b8f7b0f771a153666a6f">cuptiPCSamplingStart</a> (<a class="el" href="structCUpti__PCSamplingStartParams.html">CUpti_PCSamplingStartParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Start PC sampling.  <a href="#g569d99977964b8f7b0f771a153666a6f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g942cf8284013fe62b61d5988e6c1052f">cuptiPCSamplingStop</a> (<a class="el" href="structCUpti__PCSamplingStopParams.html">CUpti_PCSamplingStopParams</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Stop PC sampling.  <a href="#g942cf8284013fe62b61d5988e6c1052f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g675da588e3db103435a3aa360b13c005">cuptiRegisterComputeCrcCallback</a> (<a class="el" href="group__CUPTI__PCSAMPLING__API.html#g8eaf1a578c894725602cf455756927ab">CUpti_ComputeCrcCallbackFunc</a> funcComputeCubinCrc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Register callback function with CUPTI to use your own algorithm to compute cubin crc.  <a href="#g675da588e3db103435a3aa360b13c005"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI PC Sampling API. <hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g8eaf1a578c894725602cf455756927ab"></a><!-- doxytag: member="cupti_pcsampling.h::CUpti_ComputeCrcCallbackFunc" ref="g8eaf1a578c894725602cf455756927ab" args=")(const void *cubin, size_t cubinSize, uint64_t *cubinCrc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void( * <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g8eaf1a578c894725602cf455756927ab">CUpti_ComputeCrcCallbackFunc</a>)(const void *cubin, size_t cubinSize, uint64_t *cubinCrc)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This callback function ask for crc of provided module in function. The provided crc will be stored in PC sampling records i.e. in the field 'cubinCrc' of the PC sampling struct <a class="el" href="structCUpti__PCSamplingPCData.html" title="PC Sampling data.">CUpti_PCSamplingPCData</a>. The CRC is uses during the offline source correlation to uniquely identify the module.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cubin</em>&nbsp;</td><td>The pointer to cubin binary </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cubinSize</em>&nbsp;</td><td>The size of cubin binary. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cubinCrc</em>&nbsp;</td><td>Returns the computed crc of cubin. </td></tr>
  </table>
</dl>

</div>
</div><p>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g7fcaa834e5520b282b2a3b1de3f304b4"></a><!-- doxytag: member="cupti_pcsampling.h::CUpti_PCSamplingCollectionMode" ref="g7fcaa834e5520b282b2a3b1de3f304b4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g7fcaa834e5520b282b2a3b1de3f304b4">CUpti_PCSamplingCollectionMode</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg7fcaa834e5520b282b2a3b1de3f304b4e39aa8671291e4089b4881797ea2018b"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_COLLECTION_MODE_INVALID" ref="gg7fcaa834e5520b282b2a3b1de3f304b4e39aa8671291e4089b4881797ea2018b" args="" -->CUPTI_PC_SAMPLING_COLLECTION_MODE_INVALID</em>&nbsp;</td><td>
INVALID Value </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7fcaa834e5520b282b2a3b1de3f304b4d11fdae148d59d91408f90b149cc1181"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_COLLECTION_MODE_CONTINUOUS" ref="gg7fcaa834e5520b282b2a3b1de3f304b4d11fdae148d59d91408f90b149cc1181" args="" -->CUPTI_PC_SAMPLING_COLLECTION_MODE_CONTINUOUS</em>&nbsp;</td><td>
Continuous mode. Kernels are not serialized in this mode. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7fcaa834e5520b282b2a3b1de3f304b4c3df7d2baba155e561d3cbd3ac1ed2c0"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_COLLECTION_MODE_KERNEL_SERIALIZED" ref="gg7fcaa834e5520b282b2a3b1de3f304b4c3df7d2baba155e561d3cbd3ac1ed2c0" args="" -->CUPTI_PC_SAMPLING_COLLECTION_MODE_KERNEL_SERIALIZED</em>&nbsp;</td><td>
Serialized mode. Kernels are serialized in this mode. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g4fe866bd47d8825f4d46cc52c8e29bb5"></a><!-- doxytag: member="cupti_pcsampling.h::CUpti_PCSamplingConfigurationAttributeType" ref="g4fe866bd47d8825f4d46cc52c8e29bb5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g4fe866bd47d8825f4d46cc52c8e29bb5">CUpti_PCSamplingConfigurationAttributeType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
PC Sampling configuration attribute types. These attributes can be read using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gda8f3c53c8673e56a689a67cae7e8df2">cuptiPCSamplingGetConfigurationAttribute</a> and can be written using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gf3e8842fda99b4c1659c2d2f1c18674e">cuptiPCSamplingSetConfigurationAttribute</a>. Attributes marked [r] can only be read using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gda8f3c53c8673e56a689a67cae7e8df2">cuptiPCSamplingGetConfigurationAttribute</a> [w] can only be written using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gf3e8842fda99b4c1659c2d2f1c18674e">cuptiPCSamplingSetConfigurationAttribute</a> [rw] can be read using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gda8f3c53c8673e56a689a67cae7e8df2">cuptiPCSamplingGetConfigurationAttribute</a> and written using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gf3e8842fda99b4c1659c2d2f1c18674e">cuptiPCSamplingSetConfigurationAttribute</a> <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb55a3304f055fc43b3f1885f2b5fe3d35c"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_PERIOD" ref="gg4fe866bd47d8825f4d46cc52c8e29bb55a3304f055fc43b3f1885f2b5fe3d35c" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_PERIOD</em>&nbsp;</td><td>
[rw] Sampling period for PC Sampling. DEFAULT - CUPTI defined value based on number of SMs Valid values for the sampling periods are between 5 to 31 both inclusive. This will set the sampling period to (2^samplingPeriod) cycles. For e.g. for sampling period = 5 to 31, cycles = 32, 64, 128,..., 2^31 Value is a uint32_t </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb56b99e85c0ca755ba6f1e1b68af6de405"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_STALL_REASON" ref="gg4fe866bd47d8825f4d46cc52c8e29bb56b99e85c0ca755ba6f1e1b68af6de405" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_STALL_REASON</em>&nbsp;</td><td>
[w] Number of stall reasons to collect. DEFAULT - All stall reasons will be collected Value is a size_t [w] Stall reasons to collect DEFAULT - All stall reasons will be collected Input value should be a pointer pointing to array of stall reason indexes containing all the stall reason indexes to collect. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb58f4358cee727885dde3311a81bb996cc"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SCRATCH_BUFFER_SIZE" ref="gg4fe866bd47d8825f4d46cc52c8e29bb58f4358cee727885dde3311a81bb996cc" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SCRATCH_BUFFER_SIZE</em>&nbsp;</td><td>
[rw] Size of SW buffer for raw PC counter data downloaded from HW buffer DEFAULT - 1 MB, which can accommodate approximately 5500 PCs with all stall reasons Approximately it takes 16 Bytes (and some fixed size memory) to accommodate one PC with one stall reason For e.g. 1 PC with 1 stall reason = 32 Bytes 1 PC with 2 stall reason = 48 Bytes 1 PC with 4 stall reason = 96 Bytes Value is a size_t </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb5979e3dbbec977c8141ba3e01b8dbf014"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_HARDWARE_BUFFER_SIZE" ref="gg4fe866bd47d8825f4d46cc52c8e29bb5979e3dbbec977c8141ba3e01b8dbf014" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_HARDWARE_BUFFER_SIZE</em>&nbsp;</td><td>
[rw] Size of HW buffer in bytes DEFAULT - 512 MB If sampling period is too less, HW buffer can overflow and drop PC data Value is a size_t </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb5a561bd34aebe51eb26e4695ea0d865bb"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_COLLECTION_MODE" ref="gg4fe866bd47d8825f4d46cc52c8e29bb5a561bd34aebe51eb26e4695ea0d865bb" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_COLLECTION_MODE</em>&nbsp;</td><td>
[rw] PC Sampling collection mode DEFAULT - CUPTI_PC_SAMPLING_COLLECTION_MODE_CONTINUOUS Input value should be of type <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g7fcaa834e5520b282b2a3b1de3f304b4">CUpti_PCSamplingCollectionMode</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb5c75dea72b5908405f9096dd1ae27366b"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL" ref="gg4fe866bd47d8825f4d46cc52c8e29bb5c75dea72b5908405f9096dd1ae27366b" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL</em>&nbsp;</td><td>
[rw] Control over PC Sampling data collection range Default - 0 1 - Allows user to start and stop PC Sampling using APIs - <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g569d99977964b8f7b0f771a153666a6f">cuptiPCSamplingStart()</a> - Start PC Sampling <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g942cf8284013fe62b61d5988e6c1052f">cuptiPCSamplingStop()</a> - Stop PC Sampling Value is a uint32_t </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb5a19b4a5c74150e029c95a6a320018f6a"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_OUTPUT_DATA_FORMAT" ref="gg4fe866bd47d8825f4d46cc52c8e29bb5a19b4a5c74150e029c95a6a320018f6a" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_OUTPUT_DATA_FORMAT</em>&nbsp;</td><td>
[w] Value for output data format Default - CUPTI_PC_SAMPLING_OUTPUT_DATA_FORMAT_PARSED Input value should be of type <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g6959d5af9fa0ce5b41af09276fc190f7">CUpti_PCSamplingOutputDataFormat</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb5cefa869098e32174b5ed1e8dfd96af06"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_DATA_BUFFER" ref="gg4fe866bd47d8825f4d46cc52c8e29bb5cefa869098e32174b5ed1e8dfd96af06" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_DATA_BUFFER</em>&nbsp;</td><td>
[w] Data buffer to hold collected PC Sampling data PARSED_DATA Default - none. Buffer type is void * which can point to PARSED_DATA Refer <a class="el" href="structCUpti__PCSamplingData.html">CUpti_PCSamplingData</a> for buffer format for PARSED_DATA </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4fe866bd47d8825f4d46cc52c8e29bb567b78d0e6180a9cb3220e49e6b842748"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_WORKER_THREAD_PERIODIC_SLEEP_SPAN" ref="gg4fe866bd47d8825f4d46cc52c8e29bb567b78d0e6180a9cb3220e49e6b842748" args="" -->CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_WORKER_THREAD_PERIODIC_SLEEP_SPAN</em>&nbsp;</td><td>
[rw] Control sleep time of the worker threads created by CUPTI for various PC sampling operations. CUPTI creates multiple worker threads to offload certain operations to these threads. This includes decoding of HW data to the CUPTI PC sampling data and correlating PC data to SASS instructions. CUPTI wakes up these threads periodically. Default - 100 milliseconds. Value is a uint32_t </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6959d5af9fa0ce5b41af09276fc190f7"></a><!-- doxytag: member="cupti_pcsampling.h::CUpti_PCSamplingOutputDataFormat" ref="g6959d5af9fa0ce5b41af09276fc190f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g6959d5af9fa0ce5b41af09276fc190f7">CUpti_PCSamplingOutputDataFormat</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg6959d5af9fa0ce5b41af09276fc190f71dfda31478f7233dc0bf091a7e84fdf9"></a><!-- doxytag: member="CUPTI_PC_SAMPLING_OUTPUT_DATA_FORMAT_PARSED" ref="gg6959d5af9fa0ce5b41af09276fc190f71dfda31478f7233dc0bf091a7e84fdf9" args="" -->CUPTI_PC_SAMPLING_OUTPUT_DATA_FORMAT_PARSED</em>&nbsp;</td><td>
HW buffer data will be parsed during collection of data </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g58d9101ff62121eb735b19167153a4b3"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiGetCubinCrc" ref="g58d9101ff62121eb735b19167153a4b3" args="(CUpti_GetCubinCrcParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetCubinCrc           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__GetCubinCrcParams.html">CUpti_GetCubinCrcParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function returns the CRC of provided cubin binary.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__GetCubinCrcParams.html">CUpti_GetCubinCrcParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if parameter cubin is NULL or provided cubinSize is zero or size field is not set. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g35c9023ef22a59235de77b15496814eb"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiGetSassToSourceCorrelation" ref="g35c9023ef22a59235de77b15496814eb" args="(CUpti_GetSassToSourceCorrelationParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetSassToSourceCorrelation           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html">CUpti_GetSassToSourceCorrelationParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html">CUpti_GetSassToSourceCorrelationParams</a></td></tr>
  </table>
</dl>
It is expected from user to free allocated memory for fileName and dirName after use.<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if either of the parameters cubin or functionName is NULL or cubinSize is zero or size field is not set correctly. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_MODULE</em>&nbsp;</td><td>provided cubin is invalid. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UNKNOWN</em>&nbsp;</td><td>an internal error occurred. This error code is also used for cases when the function is not present in the module. A better error code will be returned in the future release. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gcbe97deb69ca2795102902544df571e3"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingDisable" ref="gcbe97deb69ca2795102902544df571e3" args="(CUpti_PCSamplingDisableParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingDisable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingDisableParams.html">CUpti_PCSamplingDisableParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For application which doesn't destroy the CUDA context explicitly, this API does the PC Sampling tear-down, joins threads and copies PC records in the buffer provided during the PC sampling configuration. PC records which can't be accommodated in the buffer are discarded.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__PCSamplingDisableParams.html">CUpti_PCSamplingDisableParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gf76a8824108dc7517ba80c2e56d46058"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingEnable" ref="gf76a8824108dc7517ba80c2e56d46058" args="(CUpti_PCSamplingEnableParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingEnable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingEnableParams.html">CUpti_PCSamplingEnableParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__PCSamplingEnableParams.html">CUpti_PCSamplingEnableParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gda8f3c53c8673e56a689a67cae7e8df2"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingGetConfigurationAttribute" ref="gda8f3c53c8673e56a689a67cae7e8df2" args="(CUpti_PCSamplingConfigurationInfoParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingGetConfigurationAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html">CUpti_PCSamplingConfigurationInfoParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pParams</em>&nbsp;</td><td>A pointer to <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html">CUpti_PCSamplingConfigurationInfoParams</a> containing PC sampling configuration.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if this API is called with some invalid attribute. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>attrib</code> is not valid or any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>indicates that the <code>value</code> buffer is too small to hold the attribute value </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6a34245ee39a52a31e231845af598e6b"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingGetData" ref="g6a34245ee39a52a31e231845af598e6b" args="(CUpti_PCSamplingGetDataParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingGetData           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingGetDataParams.html">CUpti_PCSamplingGetDataParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flushing of GPU PC Sampling data is required at following point to maintain uniqueness of PCs: For CUPTI_PC_SAMPLING_COLLECTION_MODE_CONTINUOUS, after every module load-unload-load For CUPTI_PC_SAMPLING_COLLECTION_MODE_KERNEL_SERIALIZED, after every kernel ends If configuration option CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL is enabled, then after every range end i.e. <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g942cf8284013fe62b61d5988e6c1052f" title="Stop PC sampling.">cuptiPCSamplingStop()</a> If application is profiled in CUPTI_PC_SAMPLING_COLLECTION_MODE_CONTINUOUS, with disabled CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL, and there is no module unload, user can collect data in two ways: Use <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g6a34245ee39a52a31e231845af598e6b" title="Flush GPU PC sampling data periodically.">cuptiPCSamplingGetData()</a> API periodically Use <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gcbe97deb69ca2795102902544df571e3" title="Disable PC sampling.">cuptiPCSamplingDisable()</a> on application exit and read GPU PC sampling data from sampling data buffer passed during configuration. Note: In case, <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g6a34245ee39a52a31e231845af598e6b" title="Flush GPU PC sampling data periodically.">cuptiPCSamplingGetData()</a> API is not called periodically, then sampling data buffer passed during configuration should be large enough to hold all PCs data. <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g6a34245ee39a52a31e231845af598e6b" title="Flush GPU PC sampling data periodically.">cuptiPCSamplingGetData()</a> API never does device synchronization. It is possible that when the API is called there is some unconsumed data from the HW buffer. In this case CUPTI provides only the data available with it at that moment. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__PCSamplingGetDataParams.html">CUpti_PCSamplingGetDataParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if this API is called without enabling PC sampling. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td>indicates that the HW buffer is full does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g5f5393a42625671275d30efaf8e25c3b"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingGetNumStallReasons" ref="g5f5393a42625671275d30efaf8e25c3b" args="(CUpti_PCSamplingGetNumStallReasonsParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingGetNumStallReasons           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html">CUpti_PCSamplingGetNumStallReasonsParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html">CUpti_PCSamplingGetNumStallReasonsParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g74ff0bc692635200205045f6da595b8d"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingGetStallReasons" ref="g74ff0bc692635200205045f6da595b8d" args="(CUpti_PCSamplingGetStallReasonsParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingGetStallReasons           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html">CUpti_PCSamplingGetStallReasonsParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html">CUpti_PCSamplingGetStallReasonsParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gf3e8842fda99b4c1659c2d2f1c18674e"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingSetConfigurationAttribute" ref="gf3e8842fda99b4c1659c2d2f1c18674e" args="(CUpti_PCSamplingConfigurationInfoParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingSetConfigurationAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html">CUpti_PCSamplingConfigurationInfoParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pParams</em>&nbsp;</td><td>A pointer to <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html">CUpti_PCSamplingConfigurationInfoParams</a> containing PC sampling configuration.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if this API is called with some invalid <code>attrib</code>. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if attribute <code>value</code> is not valid or any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g569d99977964b8f7b0f771a153666a6f"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingStart" ref="g569d99977964b8f7b0f771a153666a6f" args="(CUpti_PCSamplingStartParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingStart           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingStartParams.html">CUpti_PCSamplingStartParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
User can collect PC Sampling data for user-defined range specified by Start/Stop APIs. This API can be used to mark starting of range. Set configuration option CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL to use this API. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__PCSamplingStartParams.html">CUpti_PCSamplingStartParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if this API is called with incorrect PC Sampling configuration. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g942cf8284013fe62b61d5988e6c1052f"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiPCSamplingStop" ref="g942cf8284013fe62b61d5988e6c1052f" args="(CUpti_PCSamplingStopParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiPCSamplingStop           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__PCSamplingStopParams.html">CUpti_PCSamplingStopParams</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
User can collect PC Sampling data for user-defined range specified by Start/Stop APIs. This API can be used to mark end of range. Set configuration option CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL to use this API. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>Refer</em>&nbsp;</td><td><a class="el" href="structCUpti__PCSamplingStopParams.html">CUpti_PCSamplingStopParams</a></td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if this API is called with incorrect PC Sampling configuration. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if any <code>pParams</code> is not valid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>indicates that the system/device does not support the API </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g675da588e3db103435a3aa360b13c005"></a><!-- doxytag: member="cupti_pcsampling.h::cuptiRegisterComputeCrcCallback" ref="g675da588e3db103435a3aa360b13c005" args="(CUpti_ComputeCrcCallbackFunc funcComputeCubinCrc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiRegisterComputeCrcCallback           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g8eaf1a578c894725602cf455756927ab">CUpti_ComputeCrcCallbackFunc</a>&nbsp;</td>
          <td class="paramname"> <em>funcComputeCubinCrc</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function registers a callback function and it gets called from CUPTI when a CUDA module is loaded.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>funcComputeCubinCrc</em>&nbsp;</td><td>callback is invoked when a CUDA module is loaded.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>funcComputeCubinCrc</code> is NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
