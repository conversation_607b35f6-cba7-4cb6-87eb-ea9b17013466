//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_UTILITY
#define _CUDA_UTILITY

#include "type_traits"
#include "version"

#include "detail/__config"

#include "detail/__pragma_push"

#include "detail/libcxx/include/__tuple"
#include "detail/libcxx/include/utility"

#include "detail/__pragma_pop"

#endif //_CUDA_UTILITY


