<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>SanitizerApi: Sanitizer Patching API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Sanitizer Patching API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g4b7e57672cf60e7ae4ac55176d2ab834">SanitizerCallbackBarrier</a> )(void *userdata, uint64_t pc, uint32_t barIndex, uint32_t threadCount, uint32_t flags)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a barrier callback.  <a href="#g4b7e57672cf60e7ae4ac55176d2ab834"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g4aab2ddf5dc879434f2201c1f4da15cb">SanitizerCallbackBlockEnter</a> )(void *userdata, uint64_t pc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a CUDA block enter callback.  <a href="#g4aab2ddf5dc879434f2201c1f4da15cb"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g6d29c0ef356ff8cf83528d6089890e1a">SanitizerCallbackBlockExit</a> )(void *userdata, uint64_t pc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a CUDA block exit callback.  <a href="#g6d29c0ef356ff8cf83528d6089890e1a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gd27afbd55db59626f334bdd6637166ca">SanitizerCallbackCacheControl</a> )(void *userdata, uint64_t pc, void *address, <a class="el" href="group__SANITIZER__PATCHING__API.html#gd3bcfc1a9d22465613ada149c757b5db">Sanitizer_CacheControlInstructionKind</a> kind)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a cache control instruction callback.  <a href="#gd27afbd55db59626f334bdd6637166ca"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g232756bcf8464f0f4807345b3c87ce81">SanitizerCallbackCall</a> )(void *userdata, uint64_t pc, uint64_t targetPc, uint32_t flags)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a function call callback.  <a href="#g232756bcf8464f0f4807345b3c87ce81"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g9cc64b7328eb0f0a1ef870510205fa91">SanitizerCallbackClusterBarrierArrive</a> )(void *userdata, uint64_t pc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a cluster barrier arrive.  <a href="#g9cc64b7328eb0f0a1ef870510205fa91"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gf41edf6c7d09b74218d43e5021bc9687">SanitizerCallbackCudaBarrier</a> )(void *userdata, uint64_t pc, void *barrier, uint32_t kind, uint32_t data)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a CUDA Barrier action callback.  <a href="#gf41edf6c7d09b74218d43e5021bc9687"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g353c3b8eac7e039c38ddc14d730fc495">SanitizerCallbackDeviceSideFree</a> )(void *userdata, uint64_t pc, void *ptr)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a device-side free call.  <a href="#g353c3b8eac7e039c38ddc14d730fc495"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g4d2f17557934af858ce29328fed19e3f">SanitizerCallbackDeviceSideMalloc</a> )(void *userdata, uint64_t pc, void *allocatedPtr, uint64_t allocatedSize)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a device-side malloc call.  <a href="#g4d2f17557934af858ce29328fed19e3f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gad3a4de1d22335b4ff75ad35b053e90e">SanitizerCallbackMatrixMemoryAccess</a> )(void *userdata, uint64_t pc, uint32_t address, uint32_t accessSize, uint32_t flags, uint32_t count, const void *pNewValue)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a matrix shared memory access callback.  <a href="#gad3a4de1d22335b4ff75ad35b053e90e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#ga0d4e0f597f312316ba67da32205bc32">SanitizerCallbackMemcpyAsync</a> )(void *userdata, uint64_t pc, void *src, uint32_t dst, uint32_t accessSize)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a global to shared memory asynchronous copy.  <a href="#ga0d4e0f597f312316ba67da32205bc32"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gbd264aa7fdf8c45f12b58b82d1d981aa">SanitizerCallbackMemoryAccess</a> )(void *userdata, uint64_t pc, void *ptr, uint32_t accessSize, uint32_t flags, const void *newValue)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a memory access callback.  <a href="#gbd264aa7fdf8c45f12b58b82d1d981aa"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g5d226611ac5c197b1dcb78c28a15eb6a">SanitizerCallbackPipelineCommit</a> )(void *userdata, uint64_t pc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a pipeline commit.  <a href="#g5d226611ac5c197b1dcb78c28a15eb6a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g5f8f2bea7cfd5b0b99e9de7ae9010b14">SanitizerCallbackPipelineWait</a> )(void *userdata, uint64_t pc, uint32_t groups)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a pipeline wait.  <a href="#g5f8f2bea7cfd5b0b99e9de7ae9010b14"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gd18d7759b00b28bbbbfb5279261c8cc9">SanitizerCallbackRet</a> )(void *userdata, uint64_t pc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a function return callback.  <a href="#gd18d7759b00b28bbbbfb5279261c8cc9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g7d14e7f12b6f9fd9c5f1f5324b3e9c58">SanitizerCallbackShfl</a> )(void *userdata, uint64_t pc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a shfl callback.  <a href="#g7d14e7f12b6f9fd9c5f1f5324b3e9c58"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gba999e65bfd8fc65771f12a98b6d6de3">SanitizerCallbackSyncwarp</a> )(void *userdata, uint64_t pc, uint32_t mask)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a syncwarp callback.  <a href="#gba999e65bfd8fc65771f12a98b6d6de3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g6047da6fd6a63f97a0d40c3cfd044d27">SanitizerCallbackWarpgroupFence</a> )(void *userdata, uint64_t pc, uint32_t warpMask)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a warpgroup MMA fence.  <a href="#g6047da6fd6a63f97a0d40c3cfd044d27"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g86c587880067e7498f8168c26a49cb47">SanitizerCallbackWarpgroupMMAAsync</a> )(void *userdata, uint64_t pc, uint32_t addressMatrixA, uint32_t sizeMatrixA, uint32_t addressMatrixB, uint32_t sizeMatrixB, uint32_t flags, uint32_t warpMask)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a warpgroup aligned async MMA.  <a href="#g86c587880067e7498f8168c26a49cb47"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g86d8776713ae5a7b693f9b62751a546e">SanitizerCallbackWarpgroupWaitGroup</a> )(void *userdata, uint64_t pc, uint32_t numGroups, uint32_t warpMask)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a warpgroup MMA wait group.  <a href="#g86d8776713ae5a7b693f9b62751a546e"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g22dc34eb2f89b61aa2a8535c8c089209">Sanitizer_BarrierFlags</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg22dc34eb2f89b61aa2a8535c8c0892096af1eab444b00487919dff702c88bbd7">SANITIZER_BARRIER_FLAG_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg22dc34eb2f89b61aa2a8535c8c0892092cf84ebfb48dc201dda3333237e78c22">SANITIZER_BARRIER_FLAG_UNALIGNED_ALLOWED</a> =  0x1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags describing a barrier.  <a href="group__SANITIZER__PATCHING__API.html#g22dc34eb2f89b61aa2a8535c8c089209">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gd3bcfc1a9d22465613ada149c757b5db">Sanitizer_CacheControlInstructionKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#ggd3bcfc1a9d22465613ada149c757b5dbf6206156c9cf5d16175ba7976b55c6ce">SANITIZER_CACHE_CONTROL_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#ggd3bcfc1a9d22465613ada149c757b5db3f9f2ac8fd9ba6921bd6703176a80fe2">SANITIZER_CACHE_CONTROL_L1_PREFETCH</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#ggd3bcfc1a9d22465613ada149c757b5db2503c68525271d06aaf101217ac62d94">SANITIZER_CACHE_CONTROL_L2_PREFETCH</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Cache control action.  <a href="group__SANITIZER__PATCHING__API.html#gd3bcfc1a9d22465613ada149c757b5db">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g2c82c2446853ee79b55d71b098181ca1">Sanitizer_CallFlags</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg2c82c2446853ee79b55d71b098181ca13cf48edeb09a68cb3c0920a21dd09d7b">SANITIZER_CALL_FLAG_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg2c82c2446853ee79b55d71b098181ca1208e029b1ae7767951345e14e60b1c1b">SANITIZER_CALL_FLAG_UNALIGNED_ALLOWED</a> =  0x1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags describing a function call.  <a href="group__SANITIZER__PATCHING__API.html#g2c82c2446853ee79b55d71b098181ca1">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g505b1abbfde6d1d66fba877f5ce9b5f3">Sanitizer_CudaBarrierInstructionKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f3ee9a91faa6945664b0ce03d905120cf4">SANITIZER_CUDA_BARRIER_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f315931890a68eb80129cbfb49a013e8ac">SANITIZER_CUDA_BARRIER_INIT</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f34b3ac93d84739d9751cfa13f24662422">SANITIZER_CUDA_BARRIER_ARRIVE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f3230a6269ef171e6f20849eb9d5e820ac">SANITIZER_CUDA_BARRIER_ARRIVE_DROP</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f351840247bb75a7e9c817d69e5265dbb1">SANITIZER_CUDA_BARRIER_ARRIVE_NOCOMPLETE</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f3382bcc23518ba661fae3cdcf5d6a0dec">SANITIZER_CUDA_BARRIER_ARRIVE_DROP_NOCOMPLETE</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f3e18b477c6970ee40efce04e234dd182a">SANITIZER_CUDA_BARRIER_WAIT</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg505b1abbfde6d1d66fba877f5ce9b5f3658582452af5eb48a6c5bc92f41d15e8">SANITIZER_CUDA_BARRIER_INVALIDATE</a> =  7
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">CUDA Barrier action kind.  <a href="group__SANITIZER__PATCHING__API.html#g505b1abbfde6d1d66fba877f5ce9b5f3">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#ga177905ebbb1a243f0784662d6de9c0e">Sanitizer_DeviceMemoryFlags</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gga177905ebbb1a243f0784662d6de9c0ebe87dde2e3c4474d7c0e3525483ad08c">SANITIZER_MEMORY_DEVICE_FLAG_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gga177905ebbb1a243f0784662d6de9c0e22aa86808c07a6e8243d376e6f972023">SANITIZER_MEMORY_DEVICE_FLAG_READ</a> =  0x1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gga177905ebbb1a243f0784662d6de9c0ea9fcb4800449066a51276f33394b03fe">SANITIZER_MEMORY_DEVICE_FLAG_WRITE</a> =  0x2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gga177905ebbb1a243f0784662d6de9c0ed90430be7f917f6706600b4f27575a79">SANITIZER_MEMORY_DEVICE_FLAG_ATOMSYS</a> =  0x4
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags describing a memory access.  <a href="group__SANITIZER__PATCHING__API.html#ga177905ebbb1a243f0784662d6de9c0e">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g7378dfd4f3c8d5f315e733e0243ae46d">Sanitizer_FunctionLoadedStatus</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg7378dfd4f3c8d5f315e733e0243ae46d4df8ae157dc9366feda9e04ad6730cdb">SANITIZER_FUNCTION_NOT_LOADED</a> =  0x0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg7378dfd4f3c8d5f315e733e0243ae46da09ef7ea3342e8b2e547eff843867b32">SANITIZER_FUNCTION_PARTIALLY_LOADED</a> =  0x1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg7378dfd4f3c8d5f315e733e0243ae46dbdb7aab56b0e4d31a4e202efcdab6f7f">SANITIZER_FUNCTION_LOADED</a> =  0x2
<br>
 }</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g25af2e64f4c5e2a4e012edb4bb991dcf">Sanitizer_InstructionId</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfb792b56a62fc2cab8ce6f05827bee57e">SANITIZER_INSTRUCTION_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf7c59f819d654294b1d03556920b6506a">SANITIZER_INSTRUCTION_BLOCK_ENTER</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfab863a91dcec4dba6d646493730c6dba">SANITIZER_INSTRUCTION_BLOCK_EXIT</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf5406d26c46ab9e70dbe9d5e28bde8b84">SANITIZER_INSTRUCTION_GLOBAL_MEMORY_ACCESS</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf807d409043e80f13b0d8f72db74deca2">SANITIZER_INSTRUCTION_SHARED_MEMORY_ACCESS</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf1de26cc6d4b3c2047c5d367f6feab8b5">SANITIZER_INSTRUCTION_LOCAL_MEMORY_ACCESS</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfc1de637e650a2ffe7873d9773dfe81dd">SANITIZER_INSTRUCTION_BARRIER</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf675ff6968f4b589338b73ca52ff5e51d">SANITIZER_INSTRUCTION_SYNCWARP</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcff7dfa1d13d455871e8cf277e96fa98e0">SANITIZER_INSTRUCTION_SHFL</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfe794dd6b50d2cc90f31cadaf82c83183">SANITIZER_INSTRUCTION_CALL</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf6ffff69ff42f32340d6c1629600fdf68">SANITIZER_INSTRUCTION_RET</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf1d5cb2a1144869418634b105ffea08ce">SANITIZER_INSTRUCTION_DEVICE_SIDE_MALLOC</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf55e73a8a1fdf8f213e67c9e8d776782c">SANITIZER_INSTRUCTION_DEVICE_SIDE_FREE</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfc52324b09329445703e363d369b6360f">SANITIZER_INSTRUCTION_CUDA_BARRIER</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf1ad83fa89c08aadb2a5a91e6b8573736">SANITIZER_INSTRUCTION_MEMCPY_ASYNC</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf4d0f8feda14c6d003e8a1ab086b06b72">SANITIZER_INSTRUCTION_PIPELINE_COMMIT</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf68f0cba8cf1afcba8ff13b2d884ac579">SANITIZER_INSTRUCTION_PIPELINE_WAIT</a> =  16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf8072fa9014f4b425a0efc6f263d36a49">SANITIZER_INSTRUCTION_REMOTE_SHARED_MEMORY_ACCESS</a> =  17, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfb466d2d23df81ff9f9ac0fd2f9ba76bf">SANITIZER_INSTRUCTION_DEVICE_ALIGNED_MALLOC</a> =  18, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf8c9204d0db15113ed8de1102b3e16f3c">SANITIZER_INSTRUCTION_MATRIX_MEMORY_ACCESS</a> =  19, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf914c1d95480474684cb857928d81eb50">SANITIZER_INSTRUCTION_CACHE_CONTROL</a> =  20, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf512c7dfdc0d8dd20b50e4db1c3b88b7d">SANITIZER_INSTRUCTION_CLUSTER_BARRIER_ARRIVE</a> =  21, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfd555a47beed4559a95e78a07b8840bb4">SANITIZER_INSTRUCTION_CLUSTER_BARRIER_WAIT</a> =  22, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf80005ea0a333907b8d13bf0fa0ee72e8">SANITIZER_INSTRUCTION_WARPGROUP_MMA_ASYNC</a> =  23, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcfc2804c794076735eac139081030520f6">SANITIZER_INSTRUCTION_WARPGROUP_WAIT_GROUP</a> =  24, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg25af2e64f4c5e2a4e012edb4bb991dcf2c32690ce36b108e2914b3f6b9a9c961">SANITIZER_INSTRUCTION_WARPGROUP_FENCE</a> =  25
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Instrumentation.  <a href="group__SANITIZER__PATCHING__API.html#g25af2e64f4c5e2a4e012edb4bb991dcf">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gcec4a303dd18b91d32ba1bcabf7296e9">Sanitizer_WarpgroupMMAAsyncFlags</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#ggcec4a303dd18b91d32ba1bcabf7296e97df769f65d0360d5616acd1cd4021f66">SANITIZER_WARPGROUP_MMA_ASYNC_FLAG_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#ggcec4a303dd18b91d32ba1bcabf7296e900d980aadfbc12fabe810564871a1edc">SANITIZER_WARPGROUP_MMA_ASYNC_FLAG_COMMIT_GROUP</a> =  0x1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags describing a warpgroup aligned MMA async.  <a href="group__SANITIZER__PATCHING__API.html#gcec4a303dd18b91d32ba1bcabf7296e9">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg3db35bcf534fa726680c73ba242b2e70b27e6d43015f3cf83c222664f5252b0f">SANITIZER_PATCH_SUCCESS</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__PATCHING__API.html#gg3db35bcf534fa726680c73ba242b2e708d4fce4a175106f313134ef0332f9efc">SANITIZER_PATCH_ERROR</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Sanitizer patch result codes.  <a href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g9d63f4e3663fc4ce4558668afc151a39">sanitizerAddPatches</a> (const void *image, CUcontext ctx)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Load a module containing patches that can be used by the patching API.  <a href="#g9d63f4e3663fc4ce4558668afc151a39"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g04ea679ab0708224d366c48e651e40ef">sanitizerAddPatchesFromFile</a> (const char *filename, CUcontext ctx)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Load a module containing patches that can be used by the patching API.  <a href="#g04ea679ab0708224d366c48e651e40ef"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g8a243dfc09dc2f676114409039a3e80d">sanitizerGetCallbackPcAndSize</a> (CUcontext ctx, const char *deviceCallbackName, uint64_t *pc, uint64_t *size)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get PC and size of a device callback.  <a href="#g8a243dfc09dc2f676114409039a3e80d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#gc22100c2f92a845e6526fb837027222f">sanitizerGetFunctionLoadedStatus</a> (CUfunction func, <a class="el" href="group__SANITIZER__PATCHING__API.html#g7378dfd4f3c8d5f315e733e0243ae46d">Sanitizer_FunctionLoadedStatus</a> *loadingStatus)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the loading status of a function. Requires a driver version &gt;=515.  <a href="#gc22100c2f92a845e6526fb837027222f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g505e9ee66db67d02d15c47b874859545">sanitizerGetFunctionPcAndSize</a> (CUmodule module, const char *functionName, uint64_t *pc, uint64_t *size)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get PC and size of a CUDA function.  <a href="#g505e9ee66db67d02d15c47b874859545"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g4047e8742dc65935c2ab45d19a53b850">sanitizerPatchInstructions</a> (const <a class="el" href="group__SANITIZER__PATCHING__API.html#g25af2e64f4c5e2a4e012edb4bb991dcf">Sanitizer_InstructionId</a> instructionId, CUmodule module, const char *deviceCallbackName)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set instrumentation points and patches to be applied in a module.  <a href="#g4047e8742dc65935c2ab45d19a53b850"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> (CUmodule module)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Perform the actual instrumentation of a module.  <a href="#g2e7500a0e365b2ddf6c273c87a34b22d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g55712541951d15f51e8c294382f91be0">sanitizerSetCallbackData</a> (CUfunction kernel, const void *userdata)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Specifies the user data pointer for callbacks.  <a href="#g55712541951d15f51e8c294382f91be0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#ge0ea84fc8d9b9a2a514826be53ef2b14">sanitizerSetLaunchCallbackData</a> (Sanitizer_LaunchHandle launch, CUfunction kernel, Sanitizer_StreamHandle stream, const void *userdata)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Specifies the user data pointer for callbacks.  <a href="#ge0ea84fc8d9b9a2a514826be53ef2b14"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__PATCHING__API.html#g5ac4eb8e7b484ac3e70c459413791b22">sanitizerUnpatchModule</a> (CUmodule module)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Remove existing instrumentation of a module.  <a href="#g5ac4eb8e7b484ac3e70c459413791b22"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the Sanitizer Patching API. <hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g4b7e57672cf60e7ae4ac55176d2ab834"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackBarrier" ref="g4b7e57672cf60e7ae4ac55176d2ab834" args=")(void *userdata, uint64_t pc, uint32_t barIndex, uint32_t threadCount, uint32_t flags)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g4b7e57672cf60e7ae4ac55176d2ab834">SanitizerCallbackBarrier</a>)(void *userdata, uint64_t pc, uint32_t barIndex, uint32_t threadCount, uint32_t flags)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>barIndex</code> is the barrier index. <code>threadCount</code> is the number of expected threads (must be a multiple of the warp size). <code>flags</code> contains information about the barrier. See Sanitizer_BarrierFlags to interpret this value. 0 means that all threads are participating in the barrier. 
</div>
</div><p>
<a class="anchor" name="g4aab2ddf5dc879434f2201c1f4da15cb"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackBlockEnter" ref="g4aab2ddf5dc879434f2201c1f4da15cb" args=")(void *userdata, uint64_t pc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g4aab2ddf5dc879434f2201c1f4da15cb">SanitizerCallbackBlockEnter</a>)(void *userdata, uint64_t pc)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the entry point of the block 
</div>
</div><p>
<a class="anchor" name="g6d29c0ef356ff8cf83528d6089890e1a"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackBlockExit" ref="g6d29c0ef356ff8cf83528d6089890e1a" args=")(void *userdata, uint64_t pc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g6d29c0ef356ff8cf83528d6089890e1a">SanitizerCallbackBlockExit</a>)(void *userdata, uint64_t pc)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction 
</div>
</div><p>
<a class="anchor" name="gd27afbd55db59626f334bdd6637166ca"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackCacheControl" ref="gd27afbd55db59626f334bdd6637166ca" args=")(void *userdata, uint64_t pc, void *address, Sanitizer_CacheControlInstructionKind kind)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI* <a class="el" href="group__SANITIZER__PATCHING__API.html#gd27afbd55db59626f334bdd6637166ca">SanitizerCallbackCacheControl</a>)(void *userdata, uint64_t pc, void *address, <a class="el" href="group__SANITIZER__PATCHING__API.html#gd3bcfc1a9d22465613ada149c757b5db">Sanitizer_CacheControlInstructionKind</a> kind)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>address</code> is the address of the memory being controlled <code>kind</code> is the type of cache control. See <a class="el" href="group__SANITIZER__PATCHING__API.html#gd3bcfc1a9d22465613ada149c757b5db">Sanitizer_CacheControlInstructionKind</a> 
</div>
</div><p>
<a class="anchor" name="g232756bcf8464f0f4807345b3c87ce81"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackCall" ref="g232756bcf8464f0f4807345b3c87ce81" args=")(void *userdata, uint64_t pc, uint64_t targetPc, uint32_t flags)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g232756bcf8464f0f4807345b3c87ce81">SanitizerCallbackCall</a>)(void *userdata, uint64_t pc, uint64_t targetPc, uint32_t flags)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>targetPc</code> is the PC where the called function is located. <code>flags</code> contains information about the function call. 
</div>
</div><p>
<a class="anchor" name="g9cc64b7328eb0f0a1ef870510205fa91"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackClusterBarrierArrive" ref="g9cc64b7328eb0f0a1ef870510205fa91" args=")(void *userdata, uint64_t pc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g9cc64b7328eb0f0a1ef870510205fa91">SanitizerCallbackClusterBarrierArrive</a>)(void *userdata, uint64_t pc)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Function type for a cluster barrier wait.<p>
This can be generated by a cg::this_cluster().sync() (C++ API), or a barrier.cluster.arrive (PTX API).<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction<p>
This can be generated by a cg::this_cluster().sync() (C++ API), or a barrier.cluster.wait (PTX API).<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction 
</div>
</div><p>
<a class="anchor" name="gf41edf6c7d09b74218d43e5021bc9687"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackCudaBarrier" ref="gf41edf6c7d09b74218d43e5021bc9687" args=")(void *userdata, uint64_t pc, void *barrier, uint32_t kind, uint32_t data)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#gf41edf6c7d09b74218d43e5021bc9687">SanitizerCallbackCudaBarrier</a>)(void *userdata, uint64_t pc, void *barrier, uint32_t kind, uint32_t data)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>barrier</code> Barrier address which can be used as a unique identifier <code>kind</code> Barrier action type. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g505b1abbfde6d1d66fba877f5ce9b5f3">Sanitizer_CudaBarrierInstructionKind</a> <code>data</code> Barrier data. This is specific to each action type, refer to <a class="el" href="group__SANITIZER__PATCHING__API.html#g505b1abbfde6d1d66fba877f5ce9b5f3">Sanitizer_CudaBarrierInstructionKind</a> 
</div>
</div><p>
<a class="anchor" name="g353c3b8eac7e039c38ddc14d730fc495"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackDeviceSideFree" ref="g353c3b8eac7e039c38ddc14d730fc495" args=")(void *userdata, uint64_t pc, void *ptr)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g353c3b8eac7e039c38ddc14d730fc495">SanitizerCallbackDeviceSideFree</a>)(void *userdata, uint64_t pc, void *ptr)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd>This is called prior to the actual call.</dd></dl>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>ptr</code> is the pointer passed to device-side free. 
</div>
</div><p>
<a class="anchor" name="g4d2f17557934af858ce29328fed19e3f"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackDeviceSideMalloc" ref="g4d2f17557934af858ce29328fed19e3f" args=")(void *userdata, uint64_t pc, void *allocatedPtr, uint64_t allocatedSize)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g4d2f17557934af858ce29328fed19e3f">SanitizerCallbackDeviceSideMalloc</a>)(void *userdata, uint64_t pc, void *allocatedPtr, uint64_t allocatedSize)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd>This is called after the call has completed.</dd></dl>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>allocatedPtr</code> is the pointer returned by device-side malloc <code>allocatedSize</code> is the size requested by the user to device-side malloc. 
</div>
</div><p>
<a class="anchor" name="gad3a4de1d22335b4ff75ad35b053e90e"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackMatrixMemoryAccess" ref="gad3a4de1d22335b4ff75ad35b053e90e" args=")(void *userdata, uint64_t pc, uint32_t address, uint32_t accessSize, uint32_t flags, uint32_t count, const void *pNewValue)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI* <a class="el" href="group__SANITIZER__PATCHING__API.html#gad3a4de1d22335b4ff75ad35b053e90e">SanitizerCallbackMatrixMemoryAccess</a>)(void *userdata, uint64_t pc, uint32_t address, uint32_t accessSize, uint32_t flags, uint32_t count, const void *pNewValue)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>address</code> is the address of the shared memory being read or written. This is an offset within the shared memory window <code>accessSize</code> is the size of the access in bytes. Valid value is 16. <code>flags</code> contains information about the type of access. See Sanitizer_DeviceMemoryFlags to interpret this value. <code>count</code> is the number of matrices accessed. <code>newValue</code> is a pointer to the new value being written if the acces is a write. If the access is a read or an atomic, the pointer will be NULL. 
</div>
</div><p>
<a class="anchor" name="ga0d4e0f597f312316ba67da32205bc32"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackMemcpyAsync" ref="ga0d4e0f597f312316ba67da32205bc32" args=")(void *userdata, uint64_t pc, void *src, uint32_t dst, uint32_t accessSize)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#ga0d4e0f597f312316ba67da32205bc32">SanitizerCallbackMemcpyAsync</a>)(void *userdata, uint64_t pc, void *src, uint32_t dst, uint32_t accessSize)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>src</code> is the address of the global memory being read. This can be NULL if src-size is 0. <code>dst</code> is the address of the shared memory being written. This is an offset within the shared memory window <code>accessSize</code> is the size of the access in bytes. Valid values are 4, 8 and 16. 
</div>
</div><p>
<a class="anchor" name="gbd264aa7fdf8c45f12b58b82d1d981aa"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackMemoryAccess" ref="gbd264aa7fdf8c45f12b58b82d1d981aa" args=")(void *userdata, uint64_t pc, void *ptr, uint32_t accessSize, uint32_t flags, const void *newValue)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#gbd264aa7fdf8c45f12b58b82d1d981aa">SanitizerCallbackMemoryAccess</a>)(void *userdata, uint64_t pc, void *ptr, uint32_t accessSize, uint32_t flags, const void *newValue)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>ptr</code> is the address of the memory being accessed. For local or shared memory access, this is the offset within the local or shared memory window. <code>accessSize</code> is the size of the access in bytes. Valid values are 1, 2, 4, 8, and 16. <code>flags</code> contains information about the type of access. See Sanitizer_DeviceMemoryFlags to interpret this value. <code>newValue</code> is a pointer to the new value being written if the acces is a write. If the access is a read or an atomic, the pointer will be NULL. 
</div>
</div><p>
<a class="anchor" name="g5d226611ac5c197b1dcb78c28a15eb6a"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackPipelineCommit" ref="g5d226611ac5c197b1dcb78c28a15eb6a" args=")(void *userdata, uint64_t pc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g5d226611ac5c197b1dcb78c28a15eb6a">SanitizerCallbackPipelineCommit</a>)(void *userdata, uint64_t pc)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This can be generated by a pipeline::producer_commit (C++ API), a pipeline_commit (C API) or a cp.async.commit_group (PTX API).<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction 
</div>
</div><p>
<a class="anchor" name="g5f8f2bea7cfd5b0b99e9de7ae9010b14"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackPipelineWait" ref="g5f8f2bea7cfd5b0b99e9de7ae9010b14" args=")(void *userdata, uint64_t pc, uint32_t groups)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g5f8f2bea7cfd5b0b99e9de7ae9010b14">SanitizerCallbackPipelineWait</a>)(void *userdata, uint64_t pc, uint32_t groups)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This can be generated by a pipeline::consumer_wait (C++ API), a pipeline_wait_prior (C API), cp.async.wait_group or cp.async.wait_all (PTX API).<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>groups</code> is the number of groups the pipeline will wait for. 0 is used to wait for all groups. 
</div>
</div><p>
<a class="anchor" name="gd18d7759b00b28bbbbfb5279261c8cc9"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackRet" ref="gd18d7759b00b28bbbbfb5279261c8cc9" args=")(void *userdata, uint64_t pc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#gd18d7759b00b28bbbbfb5279261c8cc9">SanitizerCallbackRet</a>)(void *userdata, uint64_t pc)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction 
</div>
</div><p>
<a class="anchor" name="g7d14e7f12b6f9fd9c5f1f5324b3e9c58"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackShfl" ref="g7d14e7f12b6f9fd9c5f1f5324b3e9c58" args=")(void *userdata, uint64_t pc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g7d14e7f12b6f9fd9c5f1f5324b3e9c58">SanitizerCallbackShfl</a>)(void *userdata, uint64_t pc)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction 
</div>
</div><p>
<a class="anchor" name="gba999e65bfd8fc65771f12a98b6d6de3"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackSyncwarp" ref="gba999e65bfd8fc65771f12a98b6d6de3" args=")(void *userdata, uint64_t pc, uint32_t mask)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#gba999e65bfd8fc65771f12a98b6d6de3">SanitizerCallbackSyncwarp</a>)(void *userdata, uint64_t pc, uint32_t mask)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>mask</code> is the thread mask passed to __syncwarp(). 
</div>
</div><p>
<a class="anchor" name="g6047da6fd6a63f97a0d40c3cfd044d27"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackWarpgroupFence" ref="g6047da6fd6a63f97a0d40c3cfd044d27" args=")(void *userdata, uint64_t pc, uint32_t warpMask)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g6047da6fd6a63f97a0d40c3cfd044d27">SanitizerCallbackWarpgroupFence</a>)(void *userdata, uint64_t pc, uint32_t warpMask)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This can be generated by a wgmma.fence in PTX.<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a>. <code>pc</code> is the program counter of the patched instructioin. <code>warpMask</code> is a mask of threads that will perform the fence operation. Expected values are either 0x0 or 0xffffffff (full). The value is expected to be the same accross the warpgroup. Other values can be reported but signal a programming error in the target application. 
</div>
</div><p>
<a class="anchor" name="g86c587880067e7498f8168c26a49cb47"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackWarpgroupMMAAsync" ref="g86c587880067e7498f8168c26a49cb47" args=")(void *userdata, uint64_t pc, uint32_t addressMatrixA, uint32_t sizeMatrixA, uint32_t addressMatrixB, uint32_t sizeMatrixB, uint32_t flags, uint32_t warpMask)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g86c587880067e7498f8168c26a49cb47">SanitizerCallbackWarpgroupMMAAsync</a>)(void *userdata, uint64_t pc, uint32_t addressMatrixA, uint32_t sizeMatrixA, uint32_t addressMatrixB, uint32_t sizeMatrixB, uint32_t flags, uint32_t warpMask)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This can be generated by a wgmma.mma_async in PTX.<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> <code>pc</code> is the program counter of the patched instruction <code>addressMatrixA</code> is the address in shared memory of the matrix A being read. This field is only valid if sizeMatrixA is non-zero and warpMask is full. <code>sizeMatrixA</code> is the size of the matrix A in shared memory. A value of 0 means that the matrix A is read from registers instead. <code>addressMatrixB</code> is the address in shared memory of the matrix B being read. This field is only valid if warpMask is full. <code>sizeMatrixB</code> is the size of the matrix B in shared memory. The value will always be non-zero. <code>flags</code> of type Sanitizer_WarpgroupMMAAsyncFlags provide information about the access. These flags are to be taken into account even if the warpMask is zero. <code>warpMask</code> is a mask of threads that will perform the operation and read the operands. Expected values are either 0x0 or 0xffffffff (full). The value is expected to be the same accross the warpgroup. Other values can be reported but signal a programming error in the target application. 
</div>
</div><p>
<a class="anchor" name="g86d8776713ae5a7b693f9b62751a546e"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerCallbackWarpgroupWaitGroup" ref="g86d8776713ae5a7b693f9b62751a546e" args=")(void *userdata, uint64_t pc, uint32_t numGroups, uint32_t warpMask)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>(SANITIZERAPI * <a class="el" href="group__SANITIZER__PATCHING__API.html#g86d8776713ae5a7b693f9b62751a546e">SanitizerCallbackWarpgroupWaitGroup</a>)(void *userdata, uint64_t pc, uint32_t numGroups, uint32_t warpMask)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This can be generated by a wgmma.wait_group in PTX.<p>
<code>userdata</code> is a pointer to user data. See <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a>. <code>pc</code> is the program counter of the patched instructioin. <code>numGroups</code> is the maximum number of group that will be left pending after the operation. A value of zero means that all MMA async of the warpgroup are guaranteed to have completed after the opertion. <code>warpMask</code> is a mask of threads for which the expected values are either 0x0 or 0xffffffff (full). The value is expected to be the same accross the warpgroup. Other values can be reported but signal a programming error in the target application. If the value is valid, the value has no influence on the operation. 
</div>
</div><p>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g22dc34eb2f89b61aa2a8535c8c089209"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_BarrierFlags" ref="g22dc34eb2f89b61aa2a8535c8c089209" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#g22dc34eb2f89b61aa2a8535c8c089209">Sanitizer_BarrierFlags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flags describing a barrier. These values are to be or-combined in the value of <b>flags</b> for a SanitizerCallbackBarrier callback. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg22dc34eb2f89b61aa2a8535c8c0892096af1eab444b00487919dff702c88bbd7"></a><!-- doxytag: member="SANITIZER_BARRIER_FLAG_NONE" ref="gg22dc34eb2f89b61aa2a8535c8c0892096af1eab444b00487919dff702c88bbd7" args="" -->SANITIZER_BARRIER_FLAG_NONE</em>&nbsp;</td><td>
Empty flag. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg22dc34eb2f89b61aa2a8535c8c0892092cf84ebfb48dc201dda3333237e78c22"></a><!-- doxytag: member="SANITIZER_BARRIER_FLAG_UNALIGNED_ALLOWED" ref="gg22dc34eb2f89b61aa2a8535c8c0892092cf84ebfb48dc201dda3333237e78c22" args="" -->SANITIZER_BARRIER_FLAG_UNALIGNED_ALLOWED</em>&nbsp;</td><td>
Specifies that the barrier can be called unaligned. This flag is only valid on SM 7.0 and above. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd3bcfc1a9d22465613ada149c757b5db"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_CacheControlInstructionKind" ref="gd3bcfc1a9d22465613ada149c757b5db" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#gd3bcfc1a9d22465613ada149c757b5db">Sanitizer_CacheControlInstructionKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd3bcfc1a9d22465613ada149c757b5dbf6206156c9cf5d16175ba7976b55c6ce"></a><!-- doxytag: member="SANITIZER_CACHE_CONTROL_INVALID" ref="ggd3bcfc1a9d22465613ada149c757b5dbf6206156c9cf5d16175ba7976b55c6ce" args="" -->SANITIZER_CACHE_CONTROL_INVALID</em>&nbsp;</td><td>
Invalid action ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd3bcfc1a9d22465613ada149c757b5db3f9f2ac8fd9ba6921bd6703176a80fe2"></a><!-- doxytag: member="SANITIZER_CACHE_CONTROL_L1_PREFETCH" ref="ggd3bcfc1a9d22465613ada149c757b5db3f9f2ac8fd9ba6921bd6703176a80fe2" args="" -->SANITIZER_CACHE_CONTROL_L1_PREFETCH</em>&nbsp;</td><td>
Prefetch to L1. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd3bcfc1a9d22465613ada149c757b5db2503c68525271d06aaf101217ac62d94"></a><!-- doxytag: member="SANITIZER_CACHE_CONTROL_L2_PREFETCH" ref="ggd3bcfc1a9d22465613ada149c757b5db2503c68525271d06aaf101217ac62d94" args="" -->SANITIZER_CACHE_CONTROL_L2_PREFETCH</em>&nbsp;</td><td>
Prefetch to L2. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2c82c2446853ee79b55d71b098181ca1"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_CallFlags" ref="g2c82c2446853ee79b55d71b098181ca1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#g2c82c2446853ee79b55d71b098181ca1">Sanitizer_CallFlags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flags describing a function call. These values are to be or-combined in the value of <b>flags</b> for a SanitizerCallbackCall callback. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg2c82c2446853ee79b55d71b098181ca13cf48edeb09a68cb3c0920a21dd09d7b"></a><!-- doxytag: member="SANITIZER_CALL_FLAG_NONE" ref="gg2c82c2446853ee79b55d71b098181ca13cf48edeb09a68cb3c0920a21dd09d7b" args="" -->SANITIZER_CALL_FLAG_NONE</em>&nbsp;</td><td>
Empty flag. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2c82c2446853ee79b55d71b098181ca1208e029b1ae7767951345e14e60b1c1b"></a><!-- doxytag: member="SANITIZER_CALL_FLAG_UNALIGNED_ALLOWED" ref="gg2c82c2446853ee79b55d71b098181ca1208e029b1ae7767951345e14e60b1c1b" args="" -->SANITIZER_CALL_FLAG_UNALIGNED_ALLOWED</em>&nbsp;</td><td>
Specifies that barriers within this function call can be called unaligned. This flag is only valid on SM 7.0 and above. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g505b1abbfde6d1d66fba877f5ce9b5f3"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_CudaBarrierInstructionKind" ref="g505b1abbfde6d1d66fba877f5ce9b5f3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#g505b1abbfde6d1d66fba877f5ce9b5f3">Sanitizer_CudaBarrierInstructionKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer to the CUDA Barrier interface section of the CUDA toolkit documentation for a more extensive description of these actions. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f3ee9a91faa6945664b0ce03d905120cf4"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_INVALID" ref="gg505b1abbfde6d1d66fba877f5ce9b5f3ee9a91faa6945664b0ce03d905120cf4" args="" -->SANITIZER_CUDA_BARRIER_INVALID</em>&nbsp;</td><td>
Invalid action ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f315931890a68eb80129cbfb49a013e8ac"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_INIT" ref="gg505b1abbfde6d1d66fba877f5ce9b5f315931890a68eb80129cbfb49a013e8ac" args="" -->SANITIZER_CUDA_BARRIER_INIT</em>&nbsp;</td><td>
Barrier initialization. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f34b3ac93d84739d9751cfa13f24662422"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_ARRIVE" ref="gg505b1abbfde6d1d66fba877f5ce9b5f34b3ac93d84739d9751cfa13f24662422" args="" -->SANITIZER_CUDA_BARRIER_ARRIVE</em>&nbsp;</td><td>
Barrier arrive operation. On Hopper and newer architectures, barrier data is the count argument to the arrive-on operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f3230a6269ef171e6f20849eb9d5e820ac"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_ARRIVE_DROP" ref="gg505b1abbfde6d1d66fba877f5ce9b5f3230a6269ef171e6f20849eb9d5e820ac" args="" -->SANITIZER_CUDA_BARRIER_ARRIVE_DROP</em>&nbsp;</td><td>
Barrier arrive and drop operation. On Hopper and newer architectures, barrier data is the count argument to the arrive-on operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f351840247bb75a7e9c817d69e5265dbb1"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_ARRIVE_NOCOMPLETE" ref="gg505b1abbfde6d1d66fba877f5ce9b5f351840247bb75a7e9c817d69e5265dbb1" args="" -->SANITIZER_CUDA_BARRIER_ARRIVE_NOCOMPLETE</em>&nbsp;</td><td>
Barrier arrive operation without phase completion. Barrier data is the count argument to the arrive-on operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f3382bcc23518ba661fae3cdcf5d6a0dec"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_ARRIVE_DROP_NOCOMPLETE" ref="gg505b1abbfde6d1d66fba877f5ce9b5f3382bcc23518ba661fae3cdcf5d6a0dec" args="" -->SANITIZER_CUDA_BARRIER_ARRIVE_DROP_NOCOMPLETE</em>&nbsp;</td><td>
Barrier arrive and drop operation without phase completion. Barrier data is the count argument to the arrive-on operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f3e18b477c6970ee40efce04e234dd182a"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_WAIT" ref="gg505b1abbfde6d1d66fba877f5ce9b5f3e18b477c6970ee40efce04e234dd182a" args="" -->SANITIZER_CUDA_BARRIER_WAIT</em>&nbsp;</td><td>
Barrier wait operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg505b1abbfde6d1d66fba877f5ce9b5f3658582452af5eb48a6c5bc92f41d15e8"></a><!-- doxytag: member="SANITIZER_CUDA_BARRIER_INVALIDATE" ref="gg505b1abbfde6d1d66fba877f5ce9b5f3658582452af5eb48a6c5bc92f41d15e8" args="" -->SANITIZER_CUDA_BARRIER_INVALIDATE</em>&nbsp;</td><td>
Barrier invalidation. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="ga177905ebbb1a243f0784662d6de9c0e"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_DeviceMemoryFlags" ref="ga177905ebbb1a243f0784662d6de9c0e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#ga177905ebbb1a243f0784662d6de9c0e">Sanitizer_DeviceMemoryFlags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flags describing a memory access. These values are to be or-combined in the value of <b>flags</b> for a SanitizerCallbackMemoryAccess callback. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gga177905ebbb1a243f0784662d6de9c0ebe87dde2e3c4474d7c0e3525483ad08c"></a><!-- doxytag: member="SANITIZER_MEMORY_DEVICE_FLAG_NONE" ref="gga177905ebbb1a243f0784662d6de9c0ebe87dde2e3c4474d7c0e3525483ad08c" args="" -->SANITIZER_MEMORY_DEVICE_FLAG_NONE</em>&nbsp;</td><td>
Empty flag. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga177905ebbb1a243f0784662d6de9c0e22aa86808c07a6e8243d376e6f972023"></a><!-- doxytag: member="SANITIZER_MEMORY_DEVICE_FLAG_READ" ref="gga177905ebbb1a243f0784662d6de9c0e22aa86808c07a6e8243d376e6f972023" args="" -->SANITIZER_MEMORY_DEVICE_FLAG_READ</em>&nbsp;</td><td>
Specifies that the access is a read. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga177905ebbb1a243f0784662d6de9c0ea9fcb4800449066a51276f33394b03fe"></a><!-- doxytag: member="SANITIZER_MEMORY_DEVICE_FLAG_WRITE" ref="gga177905ebbb1a243f0784662d6de9c0ea9fcb4800449066a51276f33394b03fe" args="" -->SANITIZER_MEMORY_DEVICE_FLAG_WRITE</em>&nbsp;</td><td>
Specifies that the access is a write. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga177905ebbb1a243f0784662d6de9c0ed90430be7f917f6706600b4f27575a79"></a><!-- doxytag: member="SANITIZER_MEMORY_DEVICE_FLAG_ATOMSYS" ref="gga177905ebbb1a243f0784662d6de9c0ed90430be7f917f6706600b4f27575a79" args="" -->SANITIZER_MEMORY_DEVICE_FLAG_ATOMSYS</em>&nbsp;</td><td>
Specifies that the access is a system-scoped atomic. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g7378dfd4f3c8d5f315e733e0243ae46d"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_FunctionLoadedStatus" ref="g7378dfd4f3c8d5f315e733e0243ae46d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#g7378dfd4f3c8d5f315e733e0243ae46d">Sanitizer_FunctionLoadedStatus</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg7378dfd4f3c8d5f315e733e0243ae46d4df8ae157dc9366feda9e04ad6730cdb"></a><!-- doxytag: member="SANITIZER_FUNCTION_NOT_LOADED" ref="gg7378dfd4f3c8d5f315e733e0243ae46d4df8ae157dc9366feda9e04ad6730cdb" args="" -->SANITIZER_FUNCTION_NOT_LOADED</em>&nbsp;</td><td>
The function is not loaded. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7378dfd4f3c8d5f315e733e0243ae46da09ef7ea3342e8b2e547eff843867b32"></a><!-- doxytag: member="SANITIZER_FUNCTION_PARTIALLY_LOADED" ref="gg7378dfd4f3c8d5f315e733e0243ae46da09ef7ea3342e8b2e547eff843867b32" args="" -->SANITIZER_FUNCTION_PARTIALLY_LOADED</em>&nbsp;</td><td>
The function is being loaded. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7378dfd4f3c8d5f315e733e0243ae46dbdb7aab56b0e4d31a4e202efcdab6f7f"></a><!-- doxytag: member="SANITIZER_FUNCTION_LOADED" ref="gg7378dfd4f3c8d5f315e733e0243ae46dbdb7aab56b0e4d31a4e202efcdab6f7f" args="" -->SANITIZER_FUNCTION_LOADED</em>&nbsp;</td><td>
The function is fully loaded. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g25af2e64f4c5e2a4e012edb4bb991dcf"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_InstructionId" ref="g25af2e64f4c5e2a4e012edb4bb991dcf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#g25af2e64f4c5e2a4e012edb4bb991dcf">Sanitizer_InstructionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Instrumentation. Every entry represent an instruction type or a function call where a callback patch can be inserted. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfb792b56a62fc2cab8ce6f05827bee57e"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_INVALID" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfb792b56a62fc2cab8ce6f05827bee57e" args="" -->SANITIZER_INSTRUCTION_INVALID</em>&nbsp;</td><td>
Invalid instruction ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf7c59f819d654294b1d03556920b6506a"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_BLOCK_ENTER" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf7c59f819d654294b1d03556920b6506a" args="" -->SANITIZER_INSTRUCTION_BLOCK_ENTER</em>&nbsp;</td><td>
CUDA block enter. This is called prior to any user code. The type of the callback must be SanitizerCallbackBlockEnter. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfab863a91dcec4dba6d646493730c6dba"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_BLOCK_EXIT" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfab863a91dcec4dba6d646493730c6dba" args="" -->SANITIZER_INSTRUCTION_BLOCK_EXIT</em>&nbsp;</td><td>
CUDA block exit. This is called after all user code has executed. The type of the callback must be SanitizerCallbackBlockExit. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf5406d26c46ab9e70dbe9d5e28bde8b84"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_GLOBAL_MEMORY_ACCESS" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf5406d26c46ab9e70dbe9d5e28bde8b84" args="" -->SANITIZER_INSTRUCTION_GLOBAL_MEMORY_ACCESS</em>&nbsp;</td><td>
Global Memory Access. This can be a store, load or atomic operation. The type of the callback must be SanitizerCallbackMemoryAccess. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf807d409043e80f13b0d8f72db74deca2"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_SHARED_MEMORY_ACCESS" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf807d409043e80f13b0d8f72db74deca2" args="" -->SANITIZER_INSTRUCTION_SHARED_MEMORY_ACCESS</em>&nbsp;</td><td>
Shared Memory Access. This can be a store, load or atomic operation. The type of the callback must be SanitizerCallbackMemoryAccess. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf1de26cc6d4b3c2047c5d367f6feab8b5"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_LOCAL_MEMORY_ACCESS" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf1de26cc6d4b3c2047c5d367f6feab8b5" args="" -->SANITIZER_INSTRUCTION_LOCAL_MEMORY_ACCESS</em>&nbsp;</td><td>
Local Memory Access. This can be a store or load operation. The type of the callback must be SanitizerCallbackMemoryAccess. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfc1de637e650a2ffe7873d9773dfe81dd"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_BARRIER" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfc1de637e650a2ffe7873d9773dfe81dd" args="" -->SANITIZER_INSTRUCTION_BARRIER</em>&nbsp;</td><td>
Barrier. The type of the callback must be SanitizerCallbackBarrier. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf675ff6968f4b589338b73ca52ff5e51d"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_SYNCWARP" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf675ff6968f4b589338b73ca52ff5e51d" args="" -->SANITIZER_INSTRUCTION_SYNCWARP</em>&nbsp;</td><td>
Syncwarp. The type of the callback must be SanitizerCallbackSyncwarp. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcff7dfa1d13d455871e8cf277e96fa98e0"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_SHFL" ref="gg25af2e64f4c5e2a4e012edb4bb991dcff7dfa1d13d455871e8cf277e96fa98e0" args="" -->SANITIZER_INSTRUCTION_SHFL</em>&nbsp;</td><td>
Shfl. The type of the callback must be SanitizerCallbackShfl. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfe794dd6b50d2cc90f31cadaf82c83183"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_CALL" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfe794dd6b50d2cc90f31cadaf82c83183" args="" -->SANITIZER_INSTRUCTION_CALL</em>&nbsp;</td><td>
Function call. The type of the callback must be SanitizerCallbackCall. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf6ffff69ff42f32340d6c1629600fdf68"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_RET" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf6ffff69ff42f32340d6c1629600fdf68" args="" -->SANITIZER_INSTRUCTION_RET</em>&nbsp;</td><td>
Function return. The type of the callback must be SanitizerCallbackRet. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf1d5cb2a1144869418634b105ffea08ce"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_DEVICE_SIDE_MALLOC" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf1d5cb2a1144869418634b105ffea08ce" args="" -->SANITIZER_INSTRUCTION_DEVICE_SIDE_MALLOC</em>&nbsp;</td><td>
Device-side malloc. The type of the callback must be SanitizerCallbackDeviceSideMalloc. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf55e73a8a1fdf8f213e67c9e8d776782c"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_DEVICE_SIDE_FREE" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf55e73a8a1fdf8f213e67c9e8d776782c" args="" -->SANITIZER_INSTRUCTION_DEVICE_SIDE_FREE</em>&nbsp;</td><td>
Device-side free. The type of the callback must be SanitizerCallbackDeviceSideFree. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfc52324b09329445703e363d369b6360f"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_CUDA_BARRIER" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfc52324b09329445703e363d369b6360f" args="" -->SANITIZER_INSTRUCTION_CUDA_BARRIER</em>&nbsp;</td><td>
CUDA Barrier operation. The type of the callback must be SanitizerCallbackCudaBarrier. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf1ad83fa89c08aadb2a5a91e6b8573736"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_MEMCPY_ASYNC" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf1ad83fa89c08aadb2a5a91e6b8573736" args="" -->SANITIZER_INSTRUCTION_MEMCPY_ASYNC</em>&nbsp;</td><td>
Global to shared memory asynchronous copy. The type of the callback must be SanitizerCallbackMemcpyAsync. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf4d0f8feda14c6d003e8a1ab086b06b72"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_PIPELINE_COMMIT" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf4d0f8feda14c6d003e8a1ab086b06b72" args="" -->SANITIZER_INSTRUCTION_PIPELINE_COMMIT</em>&nbsp;</td><td>
Pipeline commit. The type of the callback must be SanitizerCallbackPipelineCommit. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf68f0cba8cf1afcba8ff13b2d884ac579"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_PIPELINE_WAIT" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf68f0cba8cf1afcba8ff13b2d884ac579" args="" -->SANITIZER_INSTRUCTION_PIPELINE_WAIT</em>&nbsp;</td><td>
Pipeline wait. The type of the callback must be SanitizerCallbackPipelineWait. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf8072fa9014f4b425a0efc6f263d36a49"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_REMOTE_SHARED_MEMORY_ACCESS" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf8072fa9014f4b425a0efc6f263d36a49" args="" -->SANITIZER_INSTRUCTION_REMOTE_SHARED_MEMORY_ACCESS</em>&nbsp;</td><td>
Remote Shared Memory Access. This can be a store or load operation. The type of the callback must be SanitizerCallbackMemoryAccess. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfb466d2d23df81ff9f9ac0fd2f9ba76bf"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_DEVICE_ALIGNED_MALLOC" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfb466d2d23df81ff9f9ac0fd2f9ba76bf" args="" -->SANITIZER_INSTRUCTION_DEVICE_ALIGNED_MALLOC</em>&nbsp;</td><td>
Device-side aligned malloc. The type of the callback must be SanitizerCallbackDeviceSideMalloc. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf8c9204d0db15113ed8de1102b3e16f3c"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_MATRIX_MEMORY_ACCESS" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf8c9204d0db15113ed8de1102b3e16f3c" args="" -->SANITIZER_INSTRUCTION_MATRIX_MEMORY_ACCESS</em>&nbsp;</td><td>
Matrix shared memory access. The type of the callback must be SanitizerCallbackMatrixMemoryAccess. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf914c1d95480474684cb857928d81eb50"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_CACHE_CONTROL" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf914c1d95480474684cb857928d81eb50" args="" -->SANITIZER_INSTRUCTION_CACHE_CONTROL</em>&nbsp;</td><td>
Cache control instruction. The type of the callback must be SanitizerCallbackCacheControl. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf512c7dfdc0d8dd20b50e4db1c3b88b7d"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_CLUSTER_BARRIER_ARRIVE" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf512c7dfdc0d8dd20b50e4db1c3b88b7d" args="" -->SANITIZER_INSTRUCTION_CLUSTER_BARRIER_ARRIVE</em>&nbsp;</td><td>
Cluster barrier arrive instruction. The type of the callback must be SanitizerCallbackClusterBarrierArrive. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfd555a47beed4559a95e78a07b8840bb4"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_CLUSTER_BARRIER_WAIT" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfd555a47beed4559a95e78a07b8840bb4" args="" -->SANITIZER_INSTRUCTION_CLUSTER_BARRIER_WAIT</em>&nbsp;</td><td>
Cluster barrier wait instruction. The type of the callback must be SanitizerCallbackClusterBarrierWait. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf80005ea0a333907b8d13bf0fa0ee72e8"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_WARPGROUP_MMA_ASYNC" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf80005ea0a333907b8d13bf0fa0ee72e8" args="" -->SANITIZER_INSTRUCTION_WARPGROUP_MMA_ASYNC</em>&nbsp;</td><td>
Warpgroup aligned async MMA instruction. The type of the callback must be SanitizerCallbackWarpgroupMMAAsync. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcfc2804c794076735eac139081030520f6"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_WARPGROUP_WAIT_GROUP" ref="gg25af2e64f4c5e2a4e012edb4bb991dcfc2804c794076735eac139081030520f6" args="" -->SANITIZER_INSTRUCTION_WARPGROUP_WAIT_GROUP</em>&nbsp;</td><td>
Warpgroup wait MMA group instruction. The type of the callback must be SanitizerCallbackWarpgroupWaitGroup. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg25af2e64f4c5e2a4e012edb4bb991dcf2c32690ce36b108e2914b3f6b9a9c961"></a><!-- doxytag: member="SANITIZER_INSTRUCTION_WARPGROUP_FENCE" ref="gg25af2e64f4c5e2a4e012edb4bb991dcf2c32690ce36b108e2914b3f6b9a9c961" args="" -->SANITIZER_INSTRUCTION_WARPGROUP_FENCE</em>&nbsp;</td><td>
Warpgroup fence instruction. The type of the callback must be SanitizerCallbackWarpgroupFence. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gcec4a303dd18b91d32ba1bcabf7296e9"></a><!-- doxytag: member="sanitizer_patching.h::Sanitizer_WarpgroupMMAAsyncFlags" ref="gcec4a303dd18b91d32ba1bcabf7296e9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#gcec4a303dd18b91d32ba1bcabf7296e9">Sanitizer_WarpgroupMMAAsyncFlags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flags describing a warpgroup aligned MMA async. These values are to be or-combined in the value of <b>flags</b> for a SanitizerCallbackWarpgroupMMAAsync callback. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggcec4a303dd18b91d32ba1bcabf7296e97df769f65d0360d5616acd1cd4021f66"></a><!-- doxytag: member="SANITIZER_WARPGROUP_MMA_ASYNC_FLAG_NONE" ref="ggcec4a303dd18b91d32ba1bcabf7296e97df769f65d0360d5616acd1cd4021f66" args="" -->SANITIZER_WARPGROUP_MMA_ASYNC_FLAG_NONE</em>&nbsp;</td><td>
Empty flag. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcec4a303dd18b91d32ba1bcabf7296e900d980aadfbc12fabe810564871a1edc"></a><!-- doxytag: member="SANITIZER_WARPGROUP_MMA_ASYNC_FLAG_COMMIT_GROUP" ref="ggcec4a303dd18b91d32ba1bcabf7296e900d980aadfbc12fabe810564871a1edc" args="" -->SANITIZER_WARPGROUP_MMA_ASYNC_FLAG_COMMIT_GROUP</em>&nbsp;</td><td>
Specifies that the MMA async delimitates a MMA async group of which it is the last instruction. Please refer to the PTX documentation for wgmma_async.commit_group for more details. This property is valid even if the warpMask is zero. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g3db35bcf534fa726680c73ba242b2e70"></a><!-- doxytag: member="sanitizer_patching.h::SanitizerPatchResult" ref="g3db35bcf534fa726680c73ba242b2e70" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__PATCHING__API.html#g3db35bcf534fa726680c73ba242b2e70">SanitizerPatchResult</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Error and result codes returned by Sanitizer patches. If a patch returns SANITIZER_PATCH_ERROR, the thread will be exited. On Volta and newer architectures, the full warp which the thread belongs to will be exited. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg3db35bcf534fa726680c73ba242b2e70b27e6d43015f3cf83c222664f5252b0f"></a><!-- doxytag: member="SANITIZER_PATCH_SUCCESS" ref="gg3db35bcf534fa726680c73ba242b2e70b27e6d43015f3cf83c222664f5252b0f" args="" -->SANITIZER_PATCH_SUCCESS</em>&nbsp;</td><td>
No error. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3db35bcf534fa726680c73ba242b2e708d4fce4a175106f313134ef0332f9efc"></a><!-- doxytag: member="SANITIZER_PATCH_ERROR" ref="gg3db35bcf534fa726680c73ba242b2e708d4fce4a175106f313134ef0332f9efc" args="" -->SANITIZER_PATCH_ERROR</em>&nbsp;</td><td>
An error was detected in the patch. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g9d63f4e3663fc4ce4558668afc151a39"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerAddPatches" ref="g9d63f4e3663fc4ce4558668afc151a39" args="(const void *image, CUcontext ctx)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerAddPatches           </td>
          <td>(</td>
          <td class="paramtype">const void *&nbsp;</td>
          <td class="paramname"> <em>image</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: an API user must serialize access to sanitizerAddPatchesFromFile, sanitizerAddPatches, sanitizerPatchInstructions, and sanitizerPatchModule. For example if sanitizerAddPatches(image) and sanitizerPatchInstruction(*, *, cbName) are called concurrently and cbName is intended to be found in the loaded image, the results are undefined.<p>
The patches loaded are only valid for the specified CUDA context.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>image</em>&nbsp;</td><td>Pointer to module data to load. This API supports the same module formats as the cuModuleLoadData and cuModuleLoadFatBinary functions from the CUDA driver API. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>ctx</em>&nbsp;</td><td>CUDA context in which to load the patches. If ctx is NULL, the current context will be used.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>image</code> does not point to a valid CUDA module. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g04ea679ab0708224d366c48e651e40ef"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerAddPatchesFromFile" ref="g04ea679ab0708224d366c48e651e40ef" args="(const char *filename, CUcontext ctx)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerAddPatchesFromFile           </td>
          <td>(</td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>filename</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: an API user must serialize access to sanitizerAddPatchesFromFile, sanitizerAddPatches, sanitizerPatchInstructions, and sanitizerPatchModule. For example if sanitizerAddPatchesFromFile(filename) and sanitizerPatchInstruction(*, *, cbName) are called concurrently and cbName is intended to be found in the loaded module, the results are undefined.<p>
The patches loaded are only valid for the specified CUDA context.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>filename</em>&nbsp;</td><td>Path to the module file. This API supports the same module formats as the cuModuleLoad function from the CUDA driver API. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>ctx</em>&nbsp;</td><td>CUDA context in which to load the patches. If ctx is NULL, the current context will be used.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>filename</code> is not a path to a valid CUDA module. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g8a243dfc09dc2f676114409039a3e80d"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerGetCallbackPcAndSize" ref="g8a243dfc09dc2f676114409039a3e80d" args="(CUcontext ctx, const char *deviceCallbackName, uint64_t *pc, uint64_t *size)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetCallbackPcAndSize           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>deviceCallbackName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>pc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>size</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>ctx</em>&nbsp;</td><td>CUDA context in which the patches were loaded. If ctx is NULL, the current context will be used. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>deviceCallbackName</em>&nbsp;</td><td>device function callback name </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>pc</em>&nbsp;</td><td>Callback PC returned </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>size</em>&nbsp;</td><td>Callback size returned</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>deviceCallbackName</code> function cannot be located, if pc is NULL or if size is NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc22100c2f92a845e6526fb837027222f"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerGetFunctionLoadedStatus" ref="gc22100c2f92a845e6526fb837027222f" args="(CUfunction func, Sanitizer_FunctionLoadedStatus *loadingStatus)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetFunctionLoadedStatus           </td>
          <td>(</td>
          <td class="paramtype">CUfunction&nbsp;</td>
          <td class="paramname"> <em>func</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__PATCHING__API.html#g7378dfd4f3c8d5f315e733e0243ae46d">Sanitizer_FunctionLoadedStatus</a> *&nbsp;</td>
          <td class="paramname"> <em>loadingStatus</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>func</em>&nbsp;</td><td>CUDA function for which the loading status is queried. </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>loadingStatus</em>&nbsp;</td><td>Loading status returned</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>func</code> is NULL or if loadingStatus is NULL. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>if the loading status cannot be queried with this driver version. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g505e9ee66db67d02d15c47b874859545"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerGetFunctionPcAndSize" ref="g505e9ee66db67d02d15c47b874859545" args="(CUmodule module, const char *functionName, uint64_t *pc, uint64_t *size)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetFunctionPcAndSize           </td>
          <td>(</td>
          <td class="paramtype">CUmodule&nbsp;</td>
          <td class="paramname"> <em>module</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>functionName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>pc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>size</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>module</em>&nbsp;</td><td>CUDA module containing the function </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>deviceCallbackName</em>&nbsp;</td><td>CUDA function name </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>pc</em>&nbsp;</td><td>Function start program counter (PC) returned </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>size</em>&nbsp;</td><td>Function size in bytes returned</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>functionName</code> function cannot be located, if pc is NULL or if size is NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g4047e8742dc65935c2ab45d19a53b850"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerPatchInstructions" ref="g4047e8742dc65935c2ab45d19a53b850" args="(const Sanitizer_InstructionId instructionId, CUmodule module, const char *deviceCallbackName)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerPatchInstructions           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="group__SANITIZER__PATCHING__API.html#g25af2e64f4c5e2a4e012edb4bb991dcf">Sanitizer_InstructionId</a>&nbsp;</td>
          <td class="paramname"> <em>instructionId</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUmodule&nbsp;</td>
          <td class="paramname"> <em>module</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>deviceCallbackName</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Mark that all instrumentation points matching instructionId are to be patched in order to call the device function identified by deviceCallbackName. It is up to the API client to ensure that this device callback exists and match the correct callback format for this instrumentation point. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: an API user must serialize access to sanitizerAddPatchesFromFile, sanitizerAddPatches, sanitizerPatchInstructions, and sanitizerPatchModule. For example if sanitizerAddPatches(fileName) and sanitizerPatchInstruction(*, *, cbName) are called concurrently and cbName is intended to be found in the loaded module, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>instructionId</em>&nbsp;</td><td>Instrumentation point for which to insert patches </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>module</em>&nbsp;</td><td>CUDA module to instrument </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>deviceCallbackName</em>&nbsp;</td><td>Name of the device function callback that the inserted patch will call at the instrumented points. This function is expected to be found in code previously loaded by sanitizerAddPatchesFromFile or sanitizerAddPatches.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>module</code> is not a CUDA module or if <code>deviceCallbackName</code> function cannot be located. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2e7500a0e365b2ddf6c273c87a34b22d"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerPatchModule" ref="g2e7500a0e365b2ddf6c273c87a34b22d" args="(CUmodule module)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerPatchModule           </td>
          <td>(</td>
          <td class="paramtype">CUmodule&nbsp;</td>
          <td class="paramname"> <em>module</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Perform the instrumentation of a CUDA module based on previous calls to sanitizerPatchInstructions. This function also specifies the device memory buffer to be passed in as userdata to all callback functions. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: an API user must serialize access to sanitizerAddPatchesFromFile, sanitizerAddPatches, sanitizerPatchInstructions, and sanitizerPatchModule. For example if sanitizerPatchModule(mod, *) and sanitizerPatchInstruction(*, mod, *) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>module</em>&nbsp;</td><td>CUDA module to instrument</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>module</code> is not a CUDA module </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g55712541951d15f51e8c294382f91be0"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerSetCallbackData" ref="g55712541951d15f51e8c294382f91be0" args="(CUfunction kernel, const void *userdata)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerSetCallbackData           </td>
          <td>(</td>
          <td class="paramtype">CUfunction&nbsp;</td>
          <td class="paramname"> <em>kernel</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *&nbsp;</td>
          <td class="paramname"> <em>userdata</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Mark all subsequent launches of <code>kernel</code> to use <code>userdata</code> pointer as the device memory buffer to pass in to callback functions.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>kernel</em>&nbsp;</td><td>CUDA function to link to user data. Callbacks in subsequent launches on this kernel will use <code>userdata</code> as callback data. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>userdata</em>&nbsp;</td><td>Device memory buffer. This data will be passed to callback functions via the <code>userdata</code> parameter.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="ge0ea84fc8d9b9a2a514826be53ef2b14"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerSetLaunchCallbackData" ref="ge0ea84fc8d9b9a2a514826be53ef2b14" args="(Sanitizer_LaunchHandle launch, CUfunction kernel, Sanitizer_StreamHandle stream, const void *userdata)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerSetLaunchCallbackData           </td>
          <td>(</td>
          <td class="paramtype">Sanitizer_LaunchHandle&nbsp;</td>
          <td class="paramname"> <em>launch</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUfunction&nbsp;</td>
          <td class="paramname"> <em>kernel</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Sanitizer_StreamHandle&nbsp;</td>
          <td class="paramname"> <em>stream</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *&nbsp;</td>
          <td class="paramname"> <em>userdata</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Mark <code>launch</code> to use <code>userdata</code> pointer as the device memory buffer to pass in to callback functions. This function is only available if the driver version is 455 or newer.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>launch</em>&nbsp;</td><td>Kernel launch to link to user data. Callbacks in this kernel launch will use <code>userdata</code> as callback data. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>kernel</em>&nbsp;</td><td>CUDA function associated with the kernel launch. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>stream</em>&nbsp;</td><td>CUDA stream associated with the stream launch. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>userdata</em>&nbsp;</td><td>Device memory buffer. This data will be passed to callback functions via the <code>userdata</code> parameter.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g5ac4eb8e7b484ac3e70c459413791b22"></a><!-- doxytag: member="sanitizer_patching.h::sanitizerUnpatchModule" ref="g5ac4eb8e7b484ac3e70c459413791b22" args="(CUmodule module)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerUnpatchModule           </td>
          <td>(</td>
          <td class="paramtype">CUmodule&nbsp;</td>
          <td class="paramname"> <em>module</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Remove any instrumentation of a CUDA module performed by previous calls to sanitizerPatchModule. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: an API user must serialize access to sanitizerPatchModule and sanitizerUnpatchModule on the same module. For example, if sanitizerPatchModule(mod) and sanitizerUnpatchModule(mod) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>module</em>&nbsp;</td><td>CUDA module on which to remove instrumentation</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Tue Jan 24 19:43:08 2023 for SanitizerApi by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
