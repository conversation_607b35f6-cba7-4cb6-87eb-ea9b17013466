<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_vars_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li class="current"><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_v">- v -</a></h3><ul>
<li>value
: <a class="el" href="structCUpti__ActivityMemset.html#711c6ac2fcbad69775408ba63689167d">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#7f11ea562c586fcba8801b509464800a">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#b6d130ed6f73f8f98f24a95a8318a4a3">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityMetricInstance.html#2f3d856f404fbd4f2e3723085a79f1e6">CUpti_ActivityMetricInstance</a>
, <a class="el" href="structCUpti__ActivityDeviceAttribute.html#c30c0591d8d5e598b33ff9dbc1d1a6be">CUpti_ActivityDeviceAttribute</a>
, <a class="el" href="structCUpti__ActivityEvent.html#14be28c7b0e804dcec2e7dd046c93afe">CUpti_ActivityEvent</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#fe0e3db9b555e7bd7a9654085644aa24">CUpti_ActivityUnifiedMemoryCounter</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#3755fae68b31aa2e358b37521d68c05b">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#340a9bbde1474c8a10e5d2632f582f3d">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#cbaea24e7e5cf2af5706aab2a5265c45">CUpti_ActivityInstantaneousMetricInstance</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#f137ea2441d6b8328c35dd99dcdba9a5">CUpti_ActivityInstantaneousMetric</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEventInstance.html#4d094f16317537cf78a2f97dd116ae33">CUpti_ActivityInstantaneousEventInstance</a>
, <a class="el" href="structCUpti__ActivityEventInstance.html#88504dfd8aac4b5cea4db3104a4d4fd2">CUpti_ActivityEventInstance</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#34cbc3149c59cb71fd9d6687efc87e34">CUpti_ActivityInstantaneousEvent</a>
, <a class="el" href="structCUpti__ActivityMetric.html#ce0c5a7b3c98b7db711ec44ef00c0aea">CUpti_ActivityMetric</a>
<li>vectorLength
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#5819c194e93027f30528cd665d7eccc0">CUpti_ActivityOpenAccLaunch</a>
<li>vendorId
: <a class="el" href="structCUpti__ActivityPcie.html#4218974c7a3b2c3e57ba7305d2b73e25">CUpti_ActivityPcie</a>
<li>version
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#7fe4cf59bba0a55c1247bb4f3d2cd653">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenMp.html#97f91d0a3b0a3b9ffc07aa3ae4d52bac">CUpti_ActivityOpenMp</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#d43b316186c9c0eb67277d23410cdb25">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structHeader.html#f09d6b9f74ced441fa260348129483bf">Header</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#34dac6acd09554be6a38a4ba23e64e14">CUpti_ActivityOpenAccOther</a>
<li>vGpu
: <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#40cd0bfb37cf7d9d00dba741dec4e58a">CUpti_Profiler_DeviceSupported_Params</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
