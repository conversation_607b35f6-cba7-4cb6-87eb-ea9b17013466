{"build": "pyhd3eb1b0_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["boltons >=23.0.0", "conda >=23.7.3", "libmambapy >=2", "python >=3.9"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-libmamba-solver-25.1.1-pyhd3eb1b0_0", "files": ["Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/INSTALLER", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/METADATA", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/RECORD", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/REQUESTED", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/WHEEL", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/direct_url.json", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/entry_points.txt", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/licenses/AUTHORS.md", "Lib/site-packages/conda_libmamba_solver-25.1.1.dist-info/licenses/LICENSE", "Lib/site-packages/conda_libmamba_solver/__init__.py", "Lib/site-packages/conda_libmamba_solver/conda_build_exceptions.py", "Lib/site-packages/conda_libmamba_solver/exceptions.py", "Lib/site-packages/conda_libmamba_solver/index.py", "Lib/site-packages/conda_libmamba_solver/mamba_utils.py", "Lib/site-packages/conda_libmamba_solver/plugin.py", "Lib/site-packages/conda_libmamba_solver/repoquery.py", "Lib/site-packages/conda_libmamba_solver/solver.py", "Lib/site-packages/conda_libmamba_solver/state.py", "Lib/site-packages/conda_libmamba_solver/utils.py", "Lib/site-packages/conda_libmamba_solver/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/conda_build_exceptions.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/index.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/mamba_utils.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/plugin.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/repoquery.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/solver.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/state.cpython-310.pyc", "Lib/site-packages/conda_libmamba_solver/__pycache__/utils.cpython-310.pyc"], "fn": "conda-libmamba-solver-25.1.1-pyhd3eb1b0_0.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-libmamba-solver-25.1.1-pyhd3eb1b0_0", "type": 1}, "md5": "6a98b39c0a2191317e22344a10357f2f", "name": "conda-libmamba-solver", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-libmamba-solver-25.1.1-pyhd3eb1b0_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/conda_build_exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/index.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/mamba_utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/plugin.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/repoquery.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/solver.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/state.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_libmamba_solver/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "defaults/noarch::conda-libmamba-solver==25.1.1=pyhd3eb1b0_0[md5=6a98b39c0a2191317e22344a10357f2f]", "sha256": "657668d9c0965dfa89bba368965dbeea8714d4e6194d54695ac9eaf20d9c4f4d", "size": 40255, "subdir": "noarch", "timestamp": 1737733720000, "url": "https://repo.anaconda.com/pkgs/main/noarch/conda-libmamba-solver-25.1.1-pyhd3eb1b0_0.conda", "version": "25.1.1"}