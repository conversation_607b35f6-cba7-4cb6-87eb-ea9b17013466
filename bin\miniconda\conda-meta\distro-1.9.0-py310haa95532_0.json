{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\distro-1.9.0-py310haa95532_0", "files": ["Lib/site-packages/distro-1.9.0.dist-info/INSTALLER", "Lib/site-packages/distro-1.9.0.dist-info/LICENSE", "Lib/site-packages/distro-1.9.0.dist-info/METADATA", "Lib/site-packages/distro-1.9.0.dist-info/RECORD", "Lib/site-packages/distro-1.9.0.dist-info/REQUESTED", "Lib/site-packages/distro-1.9.0.dist-info/WHEEL", "Lib/site-packages/distro-1.9.0.dist-info/direct_url.json", "Lib/site-packages/distro-1.9.0.dist-info/entry_points.txt", "Lib/site-packages/distro-1.9.0.dist-info/top_level.txt", "Lib/site-packages/distro/__init__.py", "Lib/site-packages/distro/__main__.py", "Lib/site-packages/distro/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/distro/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/distro/__pycache__/distro.cpython-310.pyc", "Lib/site-packages/distro/distro.py", "Lib/site-packages/distro/py.typed", "Scripts/distro-script.py", "Scripts/distro.exe"], "fn": "distro-1.9.0-py310haa95532_0.conda", "license": "Apache-2.0", "license_family": "Apache", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\distro-1.9.0-py310haa95532_0", "type": 1}, "md5": "d746776cda8ef4fd083bf331ab2f6798", "name": "distro", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\distro-1.9.0-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::distro==1.9.0=py310haa95532_0[md5=d746776cda8ef4fd083bf331ab2f6798]", "sha256": "ae9a93eb3b73369a491414c532019758df48df90295dc117bff7bed16fadcc71", "size": 57875, "subdir": "win-64", "timestamp": 1714488675000, "url": "https://repo.anaconda.com/pkgs/main/win-64/distro-1.9.0-py310haa95532_0.conda", "version": "1.9.0"}