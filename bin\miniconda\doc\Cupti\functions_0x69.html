<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li class="current"><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_i">- i -</a></h3><ul>
<li>id
: <a class="el" href="structCUpti__ActivityEvent.html#c376b723ee248963978967f9c6c42846">CUpti_ActivityEvent</a>
, <a class="el" href="structCUpti__ActivityEventInstance.html#e035c71ef2afa835aa2e1d9d958a7690">CUpti_ActivityEventInstance</a>
, <a class="el" href="structCUpti__ActivityMetricInstance.html#538e163a4d716dc8756bc481fb4e7952">CUpti_ActivityMetricInstance</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#c204a14b572254d87d5af3331e9c1ea6">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#21f481535903a23ad916969a10639384">CUpti_ActivityInstantaneousEvent</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEventInstance.html#28d3fcf274dd43911aa2ebaa0e71d87f">CUpti_ActivityInstantaneousEventInstance</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#be72f2460387e4d90ce4e2290521aec8">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#5b5400eac530fb39a3c629f115353c9a">CUpti_ActivityInstantaneousMetric</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#b0a626e732a31d5c8d71ff10c81beb1f">CUpti_ActivityInstantaneousMetricInstance</a>
, <a class="el" href="structCUpti__ActivitySourceLocator.html#ba02454027aaa1654ac7274c9176a71b">CUpti_ActivitySourceLocator</a>
, <a class="el" href="structCUpti__ActivityMarker.html#1e1dc438685a30b3adc372cbc331b09e">CUpti_ActivityMarker</a>
, <a class="el" href="structCUpti__ActivityMarker2.html#6762b4a5be5ecd3a8644763ee9821ca0">CUpti_ActivityMarker2</a>
, <a class="el" href="structCUpti__ActivityMetric.html#69d2ff7352f7ecdf7abcab6a78716a94">CUpti_ActivityMetric</a>
, <a class="el" href="structCUpti__ActivityDevice.html#3da00a5266b174dd6a7b39b5037cca09">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityMarkerData.html#cd561f9d1a789d35259f05ff45c959cc">CUpti_ActivityMarkerData</a>
, <a class="el" href="structCUpti__ActivityFunction.html#0289e0d21e2294f7fe52a67c6ec4c8a4">CUpti_ActivityFunction</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#2d0aadd01aa7773f1748979e4257d088">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityModule.html#5b7d253f82f5027e051a82a61228118e">CUpti_ActivityModule</a>
, <a class="el" href="structCUpti__ActivityPcie.html#06d80135bf829251ed12dba9b042ce76">CUpti_ActivityPcie</a>
<li>idDev0
: <a class="el" href="structCUpti__ActivityNvLink.html#2da9e328573fde3d4f0940f92ff64f9f">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#687eabf907f0b40217df4a73749479ee">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#4160274648b0899f2d5c5544e15c9c53">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#42f4991c32defb8dd85d73efc0069661">CUpti_ActivityNvLink4</a>
<li>idDev1
: <a class="el" href="structCUpti__ActivityNvLink.html#25ba8cb612e81c0dfa22d230f2cd496e">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#2118b1a8aefb0008db90781c1a0ff555">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#56298bdd4303f6cf3819d394fe6437c9">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#6f2cfb5493e4b010c72f41aa79c91552">CUpti_ActivityNvLink4</a>
<li>implicit
: <a class="el" href="structCUpti__ActivityOpenAcc.html#056897be910aa4b7f4399fcedf5f7848">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#b80223d24069659ec57dc537db2608b2">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#2cd615f39bac31a2f6bcc2680343ea4f">CUpti_ActivityOpenAccOther</a>
<li>index
: <a class="el" href="structCUpti__ActivityNvLink2.html#76e29d77543928f742e16b2eaf82fb34">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#bbc974610fa9c3668f0211bda30e660b">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#e06472f7c641d835ea89317bce872362">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityNvLink.html#6c2499dad4531ca2b0daaa914b509afa">CUpti_ActivityNvLink</a>
<li>instance
: <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#c11bcf54d6e90fdede46f920aef2b8b8">CUpti_ActivityInstantaneousMetricInstance</a>
, <a class="el" href="structCUpti__ActivityMetricInstance.html#31e2e41336e9d39fafd6fe5d07ce25a1">CUpti_ActivityMetricInstance</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEventInstance.html#094bbefc70735e9f0e604d0b0ddbe585">CUpti_ActivityInstantaneousEventInstance</a>
, <a class="el" href="structCUpti__ActivityEventInstance.html#4098c97f40c902abdbf4ea48f1a1696f">CUpti_ActivityEventInstance</a>
<li>invalidData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#9f72022e5494979f35c59a9d16633392">CUpti_PCSamplingConfigurationInfo</a>
<li>isAsync
: <a class="el" href="structCUpti__ActivityMemory2.html#982110a1d4a8b07e73b98ee8c4128f27">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#64f98eab9137762aed6fa4f946fc693c">CUpti_ActivityMemory3</a>
<li>isCudaVisible
: <a class="el" href="structCUpti__ActivityDevice3.html#6f1f34728af000ea36a1e12724bde3e6">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#17cee191aea6852f11b10a6516bd5a80">CUpti_ActivityDevice4</a>
<li>isMigEnabled
: <a class="el" href="structCUpti__ActivityDevice4.html#4771935964fade3b950c2e21519c9dd6">CUpti_ActivityDevice4</a>
<li>isSharedMemoryCarveoutRequested
: <a class="el" href="structCUpti__ActivityKernel6.html#9553e33a377998a9809007a8ff3ed5e5">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#fd77444d00d44ff97b47470d0879a2ee">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#e370d8359558263835b1677db32687b7">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#cdfc6be84faf239bb9a9d2d2985342da">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#70e44b06c5c6d5e03e43587bf6bced1e">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#4658d1a5cc6be67d9657c22fa58f5531">CUpti_ActivityKernel7</a>
<li>isSupported
: <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#f5f609c65bfa8abdc0d0dbb3a3061ac4">CUpti_Profiler_DeviceSupported_Params</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
