//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#if defined(__CUDA_ARCH__) && __CUDA_ARCH__ < 700
#  error "CUDA synchronization primitives are only supported for sm_70 and up."
#endif

#ifndef _CUDA_LATCH
#define _CUDA_LATCH

#include "atomic"

#include "detail/__config"

#include "detail/__pragma_push"

#include "detail/libcxx/include/latch"

_LIBCUDACXX_BEGIN_NAMESPACE_CUDA

template<thread_scope _Sco>
class latch : public std::__latch_base<_Sco> {
public:
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR
    latch(std::ptrdiff_t __count)
        : std::__latch_base<_Sco>(__count) {
    }
};

_LIBCUDACXX_END_NAMESPACE_CUDA

#include "detail/__pragma_pop"

#endif //_CUDA_LATCH
