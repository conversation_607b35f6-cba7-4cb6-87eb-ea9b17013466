<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemset3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemset3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemset3" -->The activity record for memset. (deprecated in CUDA 11.6).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#aa2da86b7c5b3ecf1f23479b9c3fcdcd">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#751135ae9813d7418610450db86416f2">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#d5db4db588c270483d226067b265f66f">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#0789d97f7211599055138242a6f922cb">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#8ee2f0f0b4421a7162f2060045f5d65e">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#9972f3b0dcf3e7c45e288dcc378bb938">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#647d702168b42fc8d9eb2b183baedc25">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#54a124bca3541e42da99ea37fdfaf1ce">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#9f63d291fbde25ff7460cc1b7848f2ec">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#2bc9a3dc38c0d58c8abcdd285078bcb9">memoryKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#994eb21ab711531996007cda8a928276">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#c8c74336fabc54972fa21a7526d783dd">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#ff1a6519fe35c1c2a8a8a7201b18b1c7">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#85d5dda38e346c14ae0e4f3fd9eacde9">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html#340a9bbde1474c8a10e5d2632f582f3d">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory set operation (CUPTI_ACTIVITY_KIND_MEMSET). <hr><h2>Field Documentation</h2>
<a class="anchor" name="aa2da86b7c5b3ecf1f23479b9c3fcdcd"></a><!-- doxytag: member="CUpti_ActivityMemset3::bytes" ref="aa2da86b7c5b3ecf1f23479b9c3fcdcd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset3.html#aa2da86b7c5b3ecf1f23479b9c3fcdcd">CUpti_ActivityMemset3::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes being set by the memory set. 
</div>
</div><p>
<a class="anchor" name="751135ae9813d7418610450db86416f2"></a><!-- doxytag: member="CUpti_ActivityMemset3::contextId" ref="751135ae9813d7418610450db86416f2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset3.html#751135ae9813d7418610450db86416f2">CUpti_ActivityMemset3::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="d5db4db588c270483d226067b265f66f"></a><!-- doxytag: member="CUpti_ActivityMemset3::correlationId" ref="d5db4db588c270483d226067b265f66f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset3.html#d5db4db588c270483d226067b265f66f">CUpti_ActivityMemset3::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory set. Each memory set is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory set. 
</div>
</div><p>
<a class="anchor" name="0789d97f7211599055138242a6f922cb"></a><!-- doxytag: member="CUpti_ActivityMemset3::deviceId" ref="0789d97f7211599055138242a6f922cb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset3.html#0789d97f7211599055138242a6f922cb">CUpti_ActivityMemset3::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="8ee2f0f0b4421a7162f2060045f5d65e"></a><!-- doxytag: member="CUpti_ActivityMemset3::end" ref="8ee2f0f0b4421a7162f2060045f5d65e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset3.html#8ee2f0f0b4421a7162f2060045f5d65e">CUpti_ActivityMemset3::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="9972f3b0dcf3e7c45e288dcc378bb938"></a><!-- doxytag: member="CUpti_ActivityMemset3::flags" ref="9972f3b0dcf3e7c45e288dcc378bb938" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset3.html#9972f3b0dcf3e7c45e288dcc378bb938">CUpti_ActivityMemset3::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memset. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="647d702168b42fc8d9eb2b183baedc25"></a><!-- doxytag: member="CUpti_ActivityMemset3::graphId" ref="647d702168b42fc8d9eb2b183baedc25" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset3.html#647d702168b42fc8d9eb2b183baedc25">CUpti_ActivityMemset3::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that executed this memset through graph launch. This field will be 0 if the memset is not executed through graph launch. 
</div>
</div><p>
<a class="anchor" name="54a124bca3541e42da99ea37fdfaf1ce"></a><!-- doxytag: member="CUpti_ActivityMemset3::graphNodeId" ref="54a124bca3541e42da99ea37fdfaf1ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset3.html#54a124bca3541e42da99ea37fdfaf1ce">CUpti_ActivityMemset3::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed this memset through graph launch. This field will be 0 if the memset is not executed through graph launch. 
</div>
</div><p>
<a class="anchor" name="9f63d291fbde25ff7460cc1b7848f2ec"></a><!-- doxytag: member="CUpti_ActivityMemset3::kind" ref="9f63d291fbde25ff7460cc1b7848f2ec" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemset3.html#9f63d291fbde25ff7460cc1b7848f2ec">CUpti_ActivityMemset3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMSET. 
</div>
</div><p>
<a class="anchor" name="2bc9a3dc38c0d58c8abcdd285078bcb9"></a><!-- doxytag: member="CUpti_ActivityMemset3::memoryKind" ref="2bc9a3dc38c0d58c8abcdd285078bcb9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset3.html#2bc9a3dc38c0d58c8abcdd285078bcb9">CUpti_ActivityMemset3::memoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory kind of the memory set <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="994eb21ab711531996007cda8a928276"></a><!-- doxytag: member="CUpti_ActivityMemset3::padding" ref="994eb21ab711531996007cda8a928276" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset3.html#994eb21ab711531996007cda8a928276">CUpti_ActivityMemset3::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="c8c74336fabc54972fa21a7526d783dd"></a><!-- doxytag: member="CUpti_ActivityMemset3::reserved0" ref="c8c74336fabc54972fa21a7526d783dd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemset3.html#c8c74336fabc54972fa21a7526d783dd">CUpti_ActivityMemset3::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="ff1a6519fe35c1c2a8a8a7201b18b1c7"></a><!-- doxytag: member="CUpti_ActivityMemset3::start" ref="ff1a6519fe35c1c2a8a8a7201b18b1c7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset3.html#ff1a6519fe35c1c2a8a8a7201b18b1c7">CUpti_ActivityMemset3::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="85d5dda38e346c14ae0e4f3fd9eacde9"></a><!-- doxytag: member="CUpti_ActivityMemset3::streamId" ref="85d5dda38e346c14ae0e4f3fd9eacde9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset3.html#85d5dda38e346c14ae0e4f3fd9eacde9">CUpti_ActivityMemset3::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="340a9bbde1474c8a10e5d2632f582f3d"></a><!-- doxytag: member="CUpti_ActivityMemset3::value" ref="340a9bbde1474c8a10e5d2632f582f3d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset3.html#340a9bbde1474c8a10e5d2632f582f3d">CUpti_ActivityMemset3::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The value being assigned to memory by the memory set. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
