<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingGetStallReasonsParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingGetStallReasonsParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingGetStallReasonsParams" -->Params for cuptiPCSamplingGetStallReasons.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#009ec28dba027a8d0a541d049603d7bd">ctx</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#cc76037484523a10b10a75b634926eaa">numStallReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#fcd7425b7be01cd5c140a1aac6f07eeb">pPriv</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#32a88cb0bfb4f5580f60769b3d25eb05">size</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#c568ee9da54a690f70fda5e5fdf88bd1">stallReasonIndex</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">char **&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#eaced8e7311449f2ce4831bc44efa3a9">stallReasons</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="009ec28dba027a8d0a541d049603d7bd"></a><!-- doxytag: member="CUpti_PCSamplingGetStallReasonsParams::ctx" ref="009ec28dba027a8d0a541d049603d7bd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#009ec28dba027a8d0a541d049603d7bd">CUpti_PCSamplingGetStallReasonsParams::ctx</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] CUcontext 
</div>
</div><p>
<a class="anchor" name="cc76037484523a10b10a75b634926eaa"></a><!-- doxytag: member="CUpti_PCSamplingGetStallReasonsParams::numStallReasons" ref="cc76037484523a10b10a75b634926eaa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#cc76037484523a10b10a75b634926eaa">CUpti_PCSamplingGetStallReasonsParams::numStallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Number of stall reasons 
</div>
</div><p>
<a class="anchor" name="fcd7425b7be01cd5c140a1aac6f07eeb"></a><!-- doxytag: member="CUpti_PCSamplingGetStallReasonsParams::pPriv" ref="fcd7425b7be01cd5c140a1aac6f07eeb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#fcd7425b7be01cd5c140a1aac6f07eeb">CUpti_PCSamplingGetStallReasonsParams::pPriv</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Assign to NULL 
</div>
</div><p>
<a class="anchor" name="32a88cb0bfb4f5580f60769b3d25eb05"></a><!-- doxytag: member="CUpti_PCSamplingGetStallReasonsParams::size" ref="32a88cb0bfb4f5580f60769b3d25eb05" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#32a88cb0bfb4f5580f60769b3d25eb05">CUpti_PCSamplingGetStallReasonsParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure i.e. CUpti_PCSamplingGetStallReasonsParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
<a class="anchor" name="c568ee9da54a690f70fda5e5fdf88bd1"></a><!-- doxytag: member="CUpti_PCSamplingGetStallReasonsParams::stallReasonIndex" ref="c568ee9da54a690f70fda5e5fdf88bd1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t* <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#c568ee9da54a690f70fda5e5fdf88bd1">CUpti_PCSamplingGetStallReasonsParams::stallReasonIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Stall reason index 
</div>
</div><p>
<a class="anchor" name="eaced8e7311449f2ce4831bc44efa3a9"></a><!-- doxytag: member="CUpti_PCSamplingGetStallReasonsParams::stallReasons" ref="eaced8e7311449f2ce4831bc44efa3a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char** <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#eaced8e7311449f2ce4831bc44efa3a9">CUpti_PCSamplingGetStallReasonsParams::stallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Stall reasons name 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
