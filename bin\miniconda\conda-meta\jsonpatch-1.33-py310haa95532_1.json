{"arch": "x86_64", "build": "py310haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["jsonpointer >=1.9", "python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\jsonpatch-1.33-py310haa95532_1", "files": ["Lib/site-packages/__pycache__/jsonpatch.cpython-310.pyc", "Lib/site-packages/jsonpatch-1.33.dist-info/AUTHORS", "Lib/site-packages/jsonpatch-1.33.dist-info/INSTALLER", "Lib/site-packages/jsonpatch-1.33.dist-info/LICENSE", "Lib/site-packages/jsonpatch-1.33.dist-info/METADATA", "Lib/site-packages/jsonpatch-1.33.dist-info/RECORD", "Lib/site-packages/jsonpatch-1.33.dist-info/REQUESTED", "Lib/site-packages/jsonpatch-1.33.dist-info/WHEEL", "Lib/site-packages/jsonpatch-1.33.dist-info/direct_url.json", "Lib/site-packages/jsonpatch-1.33.dist-info/top_level.txt", "Lib/site-packages/jsonpatch.py", "Scripts/jsondiff-script.py", "Scripts/jsondiff.exe", "Scripts/jsonpatch-script.py", "Scripts/jsonpatch.exe"], "fn": "jsonpatch-1.33-py310haa95532_1.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\jsonpatch-1.33-py310haa95532_1", "type": 1}, "md5": "d52e484cc83e5236e8ad1b5b847fd917", "name": "jsonpatch", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\jsonpatch-1.33-py310haa95532_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::jsonpatch==1.33=py310haa95532_1[md5=d52e484cc83e5236e8ad1b5b847fd917]", "sha256": "cd431f10edf7ce08dd08f7ce712f96106550adbf8d2938c7f235c84ece9fd50a", "size": 58242, "subdir": "win-64", "timestamp": 1714484145000, "url": "https://repo.anaconda.com/pkgs/main/win-64/jsonpatch-1.33-py310haa95532_1.conda", "version": "1.33"}