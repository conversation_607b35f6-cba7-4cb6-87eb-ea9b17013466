<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li class="current"><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_vars_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_b">- b -</a></h3><ul>
<li>bandwidth
: <a class="el" href="structCUpti__ActivityNvLink.html#76f5ab2981dde781aee62ab3eceaa95e">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#abe1675b4fafa363a914e81f93ed3521">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#7337ec9bd0628f8feeb14da5419c6fdb">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#a9e7e9d3ccd9b241fb56c5cbb9fabd99">CUpti_ActivityNvLink3</a>
<li>bDumpCounterDataInFile
: <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#3ed440e4991c509e871bd6c38c11a69b">CUpti_Profiler_BeginSession_Params</a>
<li>blockX
: <a class="el" href="structCUpti__ActivityKernel3.html#fd078948ec85708d2df4484788ccb023">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityPreemption.html#853f8d094bb5d0e0cec157183cd1da96">CUpti_ActivityPreemption</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#3167070ca5ac3c82dfcd0e37c06da438">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#fb3dba2058832b1b85dd57b46134634c">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#bcb75b544f458747c8c48dc95cc7ffa2">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel.html#cbb9bac29ee4ab936299bf572ca0e866">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#a18b40e9973894fc043da5803a21d19b">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#13ebab2313546ef43865378625fdf80c">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#bb2b68cdbb218f18820abf88b638e5a7">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#8324794b80bc7af2357b11bf98482563">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#1934e1ac072aed0007ee2406b65f6fe3">CUpti_ActivityCdpKernel</a>
<li>blockY
: <a class="el" href="structCUpti__ActivityKernel.html#6af1a44732c5f34db22a6d83703a2e21">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#824ff4aeeefbf9b746d8f39d22a96cd1">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#ff2a8c3f66d0294cd1021dffdda67828">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#4af602a0d1aad7d12840775b7c8c4650">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#1fcffd0addc89247e05b5fff9f6e2acd">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#c520a9a82295ffb2e0bc14069609681f">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#7f49212be7ec36092fac6652f7aec432">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#fa9c09c410ce8481ab69b722bb7e44e3">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#e63b0aa5f53b77a726b2255ee04cdc38">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#5ccf936b516a402ec93bcf8fb9e6501d">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityPreemption.html#076fdb658f4c3d2cc76abc397120d2e5">CUpti_ActivityPreemption</a>
<li>blockZ
: <a class="el" href="structCUpti__ActivityKernel5.html#9396c9fd7f4e636805daf9d5f2835127">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#775076cf28b6b200d2bd90d1a0e4933d">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#d604a04491cfe5771fbc3978337a22d3">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#70d8420f11e25e92c19a0fabcd8de717">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#b787ec99aae1db911446cc214f1a80dd">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#fbb9f5ef358bc22707af93382d4917b2">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#ae481fa77e0680b7545cd8662587f641">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityPreemption.html#71bbbf76cdc8bb6363fcc2af1cfab561">CUpti_ActivityPreemption</a>
, <a class="el" href="structCUpti__ActivityKernel.html#bcd200d948ddea33f583829aaf2e83d6">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#dfe0deee290c171ccd6e84bd6192d5ee">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#b3a065f7153877130a7d5456e38532e8">CUpti_ActivityKernel3</a>
<li>bridgeId
: <a class="el" href="structCUpti__ActivityPcie.html#3d4685de775cde6674c8ad076347de54">CUpti_ActivityPcie</a>
<li>bufferByteSize
: <a class="el" href="structBufferInfo.html#b92d2f17fbe280b2d52c1b86994a96ac">BufferInfo</a>
<li>bufferInfoData
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#a96e3e7eaeced59974f9cf61fa1e8c16">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a>
<li>bufferType
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#c699349113597fc553aef28c79984368">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#ea6f9e4693e3a4e1a3e655c89107b49f">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a>
<li>bytes
: <a class="el" href="structCUpti__ActivityMemset3.html#aa2da86b7c5b3ecf1f23479b9c3fcdcd">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#f4085ad746614e100684b3df2075482d">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#8e42bb722578733594dfb27ff00456f1">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemory.html#6e134910fdbf47aeeb8f3caa3b13801c">CUpti_ActivityMemory</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#5eb93979694d690dca7529eb58eea735">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#97b161ae27a7b00f5c776d9fc53ba5a2">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#3351348614cb5183a5a225eb01c9bd5f">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#f19fb3c26cfbafe25cf79c1738b4edbe">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#3a6b0871c5161cb8e4cd8e836e2588f3">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#6050f9550f0d7db52036cffe81745f47">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#c84308d7764183c8ed7a73206b50c553">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#f763f61805a53d1ea90b3102a79724e7">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#b548aedee9ae31910b21e61d88e3505a">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#36d54a7ee2da83a790d3a4e5ce84af4c">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemset.html#c064a23adf1ed1f33846c206320808fd">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#01567f6643ea926d59531fcd23452796">CUpti_ActivityMemcpy5</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
