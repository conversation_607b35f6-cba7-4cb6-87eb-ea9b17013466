<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityPCSamplingConfig Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityPCSamplingConfig Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityPCSamplingConfig" -->PC sampling configuration structure.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#ga51d59f0407cce71516a53875bf825fe">CUpti_ActivityPCSamplingPeriod</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSamplingConfig.html#2fc850d7bb051b65831fc0c2dafdc74a">samplingPeriod</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSamplingConfig.html#79a30d2b8a0a784cc404e79bab973cbd">samplingPeriod2</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSamplingConfig.html#be5b7680a63371a3de4a0eaec47cd5e8">size</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure defines the pc sampling configuration.<p>
See function <a class="el" href="group__CUPTI__ACTIVITY__API.html#g115305aadc838df99d88283fc64c4317">cuptiActivityConfigurePCSampling</a> <hr><h2>Field Documentation</h2>
<a class="anchor" name="2fc850d7bb051b65831fc0c2dafdc74a"></a><!-- doxytag: member="CUpti_ActivityPCSamplingConfig::samplingPeriod" ref="2fc850d7bb051b65831fc0c2dafdc74a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#ga51d59f0407cce71516a53875bf825fe">CUpti_ActivityPCSamplingPeriod</a> <a class="el" href="structCUpti__ActivityPCSamplingConfig.html#2fc850d7bb051b65831fc0c2dafdc74a">CUpti_ActivityPCSamplingConfig::samplingPeriod</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
There are 5 level provided for sampling period. The level internally maps to a period in terms of cycles. Same level can map to different number of cycles on different gpus. No of cycles will be chosen to minimize information loss. The period chosen will be given by samplingPeriodInCycles in <a class="el" href="structCUpti__ActivityPCSamplingRecordInfo.html">CUpti_ActivityPCSamplingRecordInfo</a> for each kernel instance. 
</div>
</div><p>
<a class="anchor" name="79a30d2b8a0a784cc404e79bab973cbd"></a><!-- doxytag: member="CUpti_ActivityPCSamplingConfig::samplingPeriod2" ref="79a30d2b8a0a784cc404e79bab973cbd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSamplingConfig.html#79a30d2b8a0a784cc404e79bab973cbd">CUpti_ActivityPCSamplingConfig::samplingPeriod2</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This will override the period set by samplingPeriod. Value 0 in samplingPeriod2 will be considered as samplingPeriod2 should not be used and samplingPeriod should be used. Valid values for samplingPeriod2 are between 5 to 31 both inclusive. This will set the sampling period to (2^samplingPeriod2) cycles. 
</div>
</div><p>
<a class="anchor" name="be5b7680a63371a3de4a0eaec47cd5e8"></a><!-- doxytag: member="CUpti_ActivityPCSamplingConfig::size" ref="be5b7680a63371a3de4a0eaec47cd5e8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSamplingConfig.html#be5b7680a63371a3de4a0eaec47cd5e8">CUpti_ActivityPCSamplingConfig::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of configuration structure. CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
