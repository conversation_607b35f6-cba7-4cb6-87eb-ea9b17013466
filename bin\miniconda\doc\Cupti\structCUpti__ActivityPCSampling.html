<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityPCSampling Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityPCSampling Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityPCSampling" -->The activity record for PC sampling. (deprecated in CUDA 8.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#3115fbabfebcd652c6de89e137b08acf">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#c2c84ca7f56ad78f3641942a1991ef5c">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#6a9defe410cea946cccbfdb74b481e76">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#861b572ea85c6cec1c1d50b4a8deb922">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#637d4c5c9579566fecb98dcf8b2d3b91">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#8d9e18a7983a7208c5f6d86f71559269">samples</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#8f5fc5f957e5ae84ea82789b23e1dce0">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html#5ff5ad24b1af1dbe867809c0bd861dd8">stallReason</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records information obtained by sampling PC (CUPTI_ACTIVITY_KIND_PC_SAMPLING). PC sampling activities are now reported using the <a class="el" href="structCUpti__ActivityPCSampling2.html" title="The activity record for PC sampling. (deprecated in CUDA 9.0).">CUpti_ActivityPCSampling2</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="3115fbabfebcd652c6de89e137b08acf"></a><!-- doxytag: member="CUpti_ActivityPCSampling::correlationId" ref="3115fbabfebcd652c6de89e137b08acf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling.html#3115fbabfebcd652c6de89e137b08acf">CUpti_ActivityPCSampling::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="c2c84ca7f56ad78f3641942a1991ef5c"></a><!-- doxytag: member="CUpti_ActivityPCSampling::flags" ref="c2c84ca7f56ad78f3641942a1991ef5c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityPCSampling.html#c2c84ca7f56ad78f3641942a1991ef5c">CUpti_ActivityPCSampling::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this instruction. 
</div>
</div><p>
<a class="anchor" name="6a9defe410cea946cccbfdb74b481e76"></a><!-- doxytag: member="CUpti_ActivityPCSampling::functionId" ref="6a9defe410cea946cccbfdb74b481e76" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling.html#6a9defe410cea946cccbfdb74b481e76">CUpti_ActivityPCSampling::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="861b572ea85c6cec1c1d50b4a8deb922"></a><!-- doxytag: member="CUpti_ActivityPCSampling::kind" ref="861b572ea85c6cec1c1d50b4a8deb922" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityPCSampling.html#861b572ea85c6cec1c1d50b4a8deb922">CUpti_ActivityPCSampling::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_PC_SAMPLING. 
</div>
</div><p>
<a class="anchor" name="637d4c5c9579566fecb98dcf8b2d3b91"></a><!-- doxytag: member="CUpti_ActivityPCSampling::pcOffset" ref="637d4c5c9579566fecb98dcf8b2d3b91" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling.html#637d4c5c9579566fecb98dcf8b2d3b91">CUpti_ActivityPCSampling::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the instruction. 
</div>
</div><p>
<a class="anchor" name="8d9e18a7983a7208c5f6d86f71559269"></a><!-- doxytag: member="CUpti_ActivityPCSampling::samples" ref="8d9e18a7983a7208c5f6d86f71559269" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling.html#8d9e18a7983a7208c5f6d86f71559269">CUpti_ActivityPCSampling::samples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of times the PC was sampled with the stallReason in the record. The same PC can be sampled with different stall reasons. 
</div>
</div><p>
<a class="anchor" name="8f5fc5f957e5ae84ea82789b23e1dce0"></a><!-- doxytag: member="CUpti_ActivityPCSampling::sourceLocatorId" ref="8f5fc5f957e5ae84ea82789b23e1dce0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling.html#8f5fc5f957e5ae84ea82789b23e1dce0">CUpti_ActivityPCSampling::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="5ff5ad24b1af1dbe867809c0bd861dd8"></a><!-- doxytag: member="CUpti_ActivityPCSampling::stallReason" ref="5ff5ad24b1af1dbe867809c0bd861dd8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a> <a class="el" href="structCUpti__ActivityPCSampling.html#5ff5ad24b1af1dbe867809c0bd861dd8">CUpti_ActivityPCSampling::stallReason</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Current stall reason. Includes one of the reasons from <a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
