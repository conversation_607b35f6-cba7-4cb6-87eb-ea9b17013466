<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: BufferInfo Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>BufferInfo Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html">CUPTI PC Sampling Utility API</a>]</small>
</h1><!-- doxytag: class="BufferInfo" --><a class="el" href="structBufferInfo.html" title="BufferInfo will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBuffe...">BufferInfo</a> will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBufferInFile() API.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structBufferInfo.html#b92d2f17fbe280b2d52c1b86994a96ac">bufferByteSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structBufferInfo.html#88792b744655eb9d28401ccc5e0e4edd">numSelectedStallReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structBufferInfo.html#f7cae81afad438c1f2d4921da89432e3">numStallReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structBufferInfo.html#085c8c6c7565b438f2a5831915b4b07e">recordCount</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="b92d2f17fbe280b2d52c1b86994a96ac"></a><!-- doxytag: member="BufferInfo::bufferByteSize" ref="b92d2f17fbe280b2d52c1b86994a96ac" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structBufferInfo.html#b92d2f17fbe280b2d52c1b86994a96ac">BufferInfo::bufferByteSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Buffer size in Bytes. 
</div>
</div><p>
<a class="anchor" name="88792b744655eb9d28401ccc5e0e4edd"></a><!-- doxytag: member="BufferInfo::numSelectedStallReasons" ref="88792b744655eb9d28401ccc5e0e4edd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structBufferInfo.html#88792b744655eb9d28401ccc5e0e4edd">BufferInfo::numSelectedStallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Total number of stall reasons in single record. 
</div>
</div><p>
<a class="anchor" name="f7cae81afad438c1f2d4921da89432e3"></a><!-- doxytag: member="BufferInfo::numStallReasons" ref="f7cae81afad438c1f2d4921da89432e3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structBufferInfo.html#f7cae81afad438c1f2d4921da89432e3">BufferInfo::numStallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Count of all stall reasons supported on the GPU 
</div>
</div><p>
<a class="anchor" name="085c8c6c7565b438f2a5831915b4b07e"></a><!-- doxytag: member="BufferInfo::recordCount" ref="085c8c6c7565b438f2a5831915b4b07e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structBufferInfo.html#085c8c6c7565b438f2a5831915b4b07e">BufferInfo::recordCount</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Total number of PC records. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
