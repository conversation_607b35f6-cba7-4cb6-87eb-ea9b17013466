{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcusparse-12.0.2.55-0", "features": "", "files": ["lib/x64/cusparse.lib"], "fn": "libcusparse-12.0.2.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcusparse-12.0.2.55-0", "type": 1}, "md5": "2881f716a1e146dc751971549df99864", "name": "libcusparse", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcusparse-12.0.2.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/cusparse.lib", "path_type": "hardlink", "sha256": "087ef9dc43da17dc59c481162a8cedce6706aa9bc0e13c41827a8812b546d228", "sha256_in_prefix": "087ef9dc43da17dc59c481162a8cedce6706aa9bc0e13c41827a8812b546d228", "size_in_bytes": 106368}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 12659, "subdir": "win-64", "timestamp": 1674625286000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libcusparse-12.0.2.55-0.tar.bz2", "version": "12.0.2.55"}