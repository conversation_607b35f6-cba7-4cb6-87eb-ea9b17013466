<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpyPtoP4 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpyPtoP4 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpyPtoP4" -->The activity record for peer-to-peer memory copies.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#f763f61805a53d1ea90b3102a79724e7">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#109fb2f6d8aa27363c3ab130f8e76c91">channelID</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUpti_ChannelType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#f18141681e85457682ff9eaa615ec6ad">channelType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#08eff845e98619368e438c50fa5b4916">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#e2b7b2410638d1c9e2cc9662da6efb76">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#695effe20df007c58ec7af02677ae810">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#4b51371a19abebf3fa45e74899688bae">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#1945745163da396ce4d8718d539a1a33">dstContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#0370ee206a5d8b7dbc656e0558428f8b">dstDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#89f24945819802924f2358d92fe19a48">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#b7ef10394792c28177351069bae13e0d">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#576bee56bea2b04feb7a40e55b334465">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#168ceb082af4e7c95f5b690fa1b65e83">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#3c5593f9893a5599433b59c828ef1468">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#434ca90d787dc2d1826903355c28de59">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#7be4f235246eebb9adb5ba59d40fda00">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#74a7e9eb58c5d138387b0e2480ae4ede">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#59a28c17da1c9bd6dbd6dd28bad907b7">srcContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#448e89607344c732f82127db366551ca">srcDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#05ebfd8cbc450e7bccb1d209dbbbcf53">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#4e3482983ecec51ba22c3e538df5afc0">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#a1565e451475facc31b5bf1f7709f9bd">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a peer-to-peer memory copy (CUPTI_ACTIVITY_KIND_MEMCPY2). <hr><h2>Field Documentation</h2>
<a class="anchor" name="f763f61805a53d1ea90b3102a79724e7"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::bytes" ref="f763f61805a53d1ea90b3102a79724e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#f763f61805a53d1ea90b3102a79724e7">CUpti_ActivityMemcpyPtoP4::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="109fb2f6d8aa27363c3ab130f8e76c91"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::channelID" ref="109fb2f6d8aa27363c3ab130f8e76c91" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#109fb2f6d8aa27363c3ab130f8e76c91">CUpti_ActivityMemcpyPtoP4::channelID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the HW channel on which the memory copy is occuring. 
</div>
</div><p>
<a class="anchor" name="f18141681e85457682ff9eaa615ec6ad"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::channelType" ref="f18141681e85457682ff9eaa615ec6ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUpti_ChannelType <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#f18141681e85457682ff9eaa615ec6ad">CUpti_ActivityMemcpyPtoP4::channelType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the channel 
</div>
</div><p>
<a class="anchor" name="08eff845e98619368e438c50fa5b4916"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::contextId" ref="08eff845e98619368e438c50fa5b4916" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#08eff845e98619368e438c50fa5b4916">CUpti_ActivityMemcpyPtoP4::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="e2b7b2410638d1c9e2cc9662da6efb76"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::copyKind" ref="e2b7b2410638d1c9e2cc9662da6efb76" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#e2b7b2410638d1c9e2cc9662da6efb76">CUpti_ActivityMemcpyPtoP4::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="695effe20df007c58ec7af02677ae810"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::correlationId" ref="695effe20df007c58ec7af02677ae810" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#695effe20df007c58ec7af02677ae810">CUpti_ActivityMemcpyPtoP4::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="4b51371a19abebf3fa45e74899688bae"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::deviceId" ref="4b51371a19abebf3fa45e74899688bae" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#4b51371a19abebf3fa45e74899688bae">CUpti_ActivityMemcpyPtoP4::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="1945745163da396ce4d8718d539a1a33"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::dstContextId" ref="1945745163da396ce4d8718d539a1a33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#1945745163da396ce4d8718d539a1a33">CUpti_ActivityMemcpyPtoP4::dstContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied to. 
</div>
</div><p>
<a class="anchor" name="0370ee206a5d8b7dbc656e0558428f8b"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::dstDeviceId" ref="0370ee206a5d8b7dbc656e0558428f8b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#0370ee206a5d8b7dbc656e0558428f8b">CUpti_ActivityMemcpyPtoP4::dstDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied to. 
</div>
</div><p>
<a class="anchor" name="89f24945819802924f2358d92fe19a48"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::dstKind" ref="89f24945819802924f2358d92fe19a48" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#89f24945819802924f2358d92fe19a48">CUpti_ActivityMemcpyPtoP4::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="b7ef10394792c28177351069bae13e0d"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::end" ref="b7ef10394792c28177351069bae13e0d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#b7ef10394792c28177351069bae13e0d">CUpti_ActivityMemcpyPtoP4::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="576bee56bea2b04feb7a40e55b334465"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::flags" ref="576bee56bea2b04feb7a40e55b334465" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#576bee56bea2b04feb7a40e55b334465">CUpti_ActivityMemcpyPtoP4::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="168ceb082af4e7c95f5b690fa1b65e83"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::graphId" ref="168ceb082af4e7c95f5b690fa1b65e83" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#168ceb082af4e7c95f5b690fa1b65e83">CUpti_ActivityMemcpyPtoP4::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that executed this memcpy through graph launch. This field will be 0 if the memcpy is not done through graph launch. 
</div>
</div><p>
<a class="anchor" name="3c5593f9893a5599433b59c828ef1468"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::graphNodeId" ref="3c5593f9893a5599433b59c828ef1468" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#3c5593f9893a5599433b59c828ef1468">CUpti_ActivityMemcpyPtoP4::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed the memcpy through graph launch. This field will be 0 if memcpy is not done using graph launch. 
</div>
</div><p>
<a class="anchor" name="434ca90d787dc2d1826903355c28de59"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::kind" ref="434ca90d787dc2d1826903355c28de59" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#434ca90d787dc2d1826903355c28de59">CUpti_ActivityMemcpyPtoP4::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY2. 
</div>
</div><p>
<a class="anchor" name="7be4f235246eebb9adb5ba59d40fda00"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::pad" ref="7be4f235246eebb9adb5ba59d40fda00" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#7be4f235246eebb9adb5ba59d40fda00">CUpti_ActivityMemcpyPtoP4::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="74a7e9eb58c5d138387b0e2480ae4ede"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::reserved0" ref="74a7e9eb58c5d138387b0e2480ae4ede" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#74a7e9eb58c5d138387b0e2480ae4ede">CUpti_ActivityMemcpyPtoP4::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="59a28c17da1c9bd6dbd6dd28bad907b7"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::srcContextId" ref="59a28c17da1c9bd6dbd6dd28bad907b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#59a28c17da1c9bd6dbd6dd28bad907b7">CUpti_ActivityMemcpyPtoP4::srcContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied from. 
</div>
</div><p>
<a class="anchor" name="448e89607344c732f82127db366551ca"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::srcDeviceId" ref="448e89607344c732f82127db366551ca" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#448e89607344c732f82127db366551ca">CUpti_ActivityMemcpyPtoP4::srcDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied from. 
</div>
</div><p>
<a class="anchor" name="05ebfd8cbc450e7bccb1d209dbbbcf53"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::srcKind" ref="05ebfd8cbc450e7bccb1d209dbbbcf53" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#05ebfd8cbc450e7bccb1d209dbbbcf53">CUpti_ActivityMemcpyPtoP4::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="4e3482983ecec51ba22c3e538df5afc0"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::start" ref="4e3482983ecec51ba22c3e538df5afc0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#4e3482983ecec51ba22c3e538df5afc0">CUpti_ActivityMemcpyPtoP4::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="a1565e451475facc31b5bf1f7709f9bd"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP4::streamId" ref="a1565e451475facc31b5bf1f7709f9bd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#a1565e451475facc31b5bf1f7709f9bd">CUpti_ActivityMemcpyPtoP4::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
