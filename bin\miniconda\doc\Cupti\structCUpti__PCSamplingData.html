<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingData" -->Collected PC Sampling data.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#4d2032f7507ca7729183a8972ae6e74e">collectNumPcs</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#aa0dd4e9f1715f0413b454cc70134a89">droppedSamples</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#4b17b5a13075396f3c091abd84b680ab">hardwareBufferFull</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#af69505071b7815e9d9a1e9f75bcbd44">nonUsrKernelsTotalSamples</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structCUpti__PCSamplingPCData.html">CUpti_PCSamplingPCData</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#82d7bab4f2b8654b76c0be941e2c0dcf">pPcData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">collectNumPcs  <a href="#82d7bab4f2b8654b76c0be941e2c0dcf"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#f8e5928e814b1697b3236f62c66591f0">rangeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#bb938b9cff32663ea47e138ad76105f7">remainingNumPcs</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#7843c641dd14fb54b8910f1f8290ec26">size</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#3e8220b654f65b35d3a44c1c6191fa5c">totalNumPcs</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingData.html#37f913259cadfb0f61c2e37204328a16">totalSamples</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="4d2032f7507ca7729183a8972ae6e74e"></a><!-- doxytag: member="CUpti_PCSamplingData::collectNumPcs" ref="4d2032f7507ca7729183a8972ae6e74e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingData.html#4d2032f7507ca7729183a8972ae6e74e">CUpti_PCSamplingData::collectNumPcs</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Number of PCs to be collected 
</div>
</div><p>
<a class="anchor" name="aa0dd4e9f1715f0413b454cc70134a89"></a><!-- doxytag: member="CUpti_PCSamplingData::droppedSamples" ref="aa0dd4e9f1715f0413b454cc70134a89" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__PCSamplingData.html#aa0dd4e9f1715f0413b454cc70134a89">CUpti_PCSamplingData::droppedSamples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Number of samples that were dropped by hardware due to backpressure/overflow. 
</div>
</div><p>
<a class="anchor" name="4b17b5a13075396f3c091abd84b680ab"></a><!-- doxytag: member="CUpti_PCSamplingData::hardwareBufferFull" ref="4b17b5a13075396f3c091abd84b680ab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__PCSamplingData.html#4b17b5a13075396f3c091abd84b680ab">CUpti_PCSamplingData::hardwareBufferFull</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Status of the hardware buffer. CUPTI returns the error code CUPTI_ERROR_OUT_OF_MEMORY when hardware buffer is full. When hardware buffer is full, user will get pc data as 0. To mitigate this issue, one or more of the below options can be tried: 1. Increase the hardware buffer size using the attribute CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_HARDWARE_BUFFER_SIZE 2. Decrease the thread sleep span using the attribute CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_WORKER_THREAD_PERIODIC_SLEEP_SPAN 3. Decrease the sampling frequency using the attribute CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_PERIOD 
</div>
</div><p>
<a class="anchor" name="af69505071b7815e9d9a1e9f75bcbd44"></a><!-- doxytag: member="CUpti_PCSamplingData::nonUsrKernelsTotalSamples" ref="af69505071b7815e9d9a1e9f75bcbd44" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__PCSamplingData.html#af69505071b7815e9d9a1e9f75bcbd44">CUpti_PCSamplingData::nonUsrKernelsTotalSamples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Number of samples collected across all non user kernels PCs. It includes samples for non-user kernels. It includes counts for all non selected stall reasons as well. CUPTI does not provide PC records for non-user kernels. 
</div>
</div><p>
<a class="anchor" name="82d7bab4f2b8654b76c0be941e2c0dcf"></a><!-- doxytag: member="CUpti_PCSamplingData::pPcData" ref="82d7bab4f2b8654b76c0be941e2c0dcf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structCUpti__PCSamplingPCData.html">CUpti_PCSamplingPCData</a>* <a class="el" href="structCUpti__PCSamplingData.html#82d7bab4f2b8654b76c0be941e2c0dcf">CUpti_PCSamplingData::pPcData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Profiled PC data This data struct should have enough memory to collect number of PCs mentioned in 
</div>
</div><p>
<a class="anchor" name="f8e5928e814b1697b3236f62c66591f0"></a><!-- doxytag: member="CUpti_PCSamplingData::rangeId" ref="f8e5928e814b1697b3236f62c66591f0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__PCSamplingData.html#f8e5928e814b1697b3236f62c66591f0">CUpti_PCSamplingData::rangeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Unique identifier for each range. Data collected across multiple ranges in multiple buffers can be identified using range id. 
</div>
</div><p>
<a class="anchor" name="bb938b9cff32663ea47e138ad76105f7"></a><!-- doxytag: member="CUpti_PCSamplingData::remainingNumPcs" ref="bb938b9cff32663ea47e138ad76105f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingData.html#bb938b9cff32663ea47e138ad76105f7">CUpti_PCSamplingData::remainingNumPcs</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Number of PCs available for collection 
</div>
</div><p>
<a class="anchor" name="7843c641dd14fb54b8910f1f8290ec26"></a><!-- doxytag: member="CUpti_PCSamplingData::size" ref="7843c641dd14fb54b8910f1f8290ec26" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingData.html#7843c641dd14fb54b8910f1f8290ec26">CUpti_PCSamplingData::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure. CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
<a class="anchor" name="3e8220b654f65b35d3a44c1c6191fa5c"></a><!-- doxytag: member="CUpti_PCSamplingData::totalNumPcs" ref="3e8220b654f65b35d3a44c1c6191fa5c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingData.html#3e8220b654f65b35d3a44c1c6191fa5c">CUpti_PCSamplingData::totalNumPcs</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Number of PCs collected 
</div>
</div><p>
<a class="anchor" name="37f913259cadfb0f61c2e37204328a16"></a><!-- doxytag: member="CUpti_PCSamplingData::totalSamples" ref="37f913259cadfb0f61c2e37204328a16" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__PCSamplingData.html#37f913259cadfb0f61c2e37204328a16">CUpti_PCSamplingData::totalSamples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Number of samples collected across all PCs. It includes samples for user modules, samples for non-user kernels and dropped samples. It includes counts for all non selected stall reasons. CUPTI does not provide PC records for non-user kernels. CUPTI does not provide PC records for instructions for which all selected stall reason metrics counts are zero. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
