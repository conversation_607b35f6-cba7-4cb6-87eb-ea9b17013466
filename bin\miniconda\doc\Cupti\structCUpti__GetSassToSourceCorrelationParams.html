<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_GetSassToSourceCorrelationParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_GetSassToSourceCorrelationParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_GetSassToSourceCorrelationParams" -->Params for cuptiGetSassToSourceCorrelation.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#20ae4d251f00a9e518b3c43078a1de75">cubin</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#61f5e6b65d3cf01a24ec0fc6b6e66ef9">cubinSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#f13995c334f6fb4e3d62bdf8a3ba574c">dirName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#5fd74201d9184aa6d7e482f22ba6cd33">fileName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#d381fee6d12b4b5ea4003be6b0acff52">functionName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#b5f50cab1eb124a26a5b7a5907429906">lineNumber</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#9713b8a495f78284612c1f3403a8bd39">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#2820390c5cb5cbf9b303d32b5c2518ee">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="20ae4d251f00a9e518b3c43078a1de75"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::cubin" ref="20ae4d251f00a9e518b3c43078a1de75" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#20ae4d251f00a9e518b3c43078a1de75">CUpti_GetSassToSourceCorrelationParams::cubin</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Pointer to cubin binary where function belongs. 
</div>
</div><p>
<a class="anchor" name="61f5e6b65d3cf01a24ec0fc6b6e66ef9"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::cubinSize" ref="61f5e6b65d3cf01a24ec0fc6b6e66ef9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#61f5e6b65d3cf01a24ec0fc6b6e66ef9">CUpti_GetSassToSourceCorrelationParams::cubinSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of cubin binary. 
</div>
</div><p>
<a class="anchor" name="f13995c334f6fb4e3d62bdf8a3ba574c"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::dirName" ref="f13995c334f6fb4e3d62bdf8a3ba574c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#f13995c334f6fb4e3d62bdf8a3ba574c">CUpti_GetSassToSourceCorrelationParams::dirName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Path for the directory of source file. 
</div>
</div><p>
<a class="anchor" name="5fd74201d9184aa6d7e482f22ba6cd33"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::fileName" ref="5fd74201d9184aa6d7e482f22ba6cd33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#5fd74201d9184aa6d7e482f22ba6cd33">CUpti_GetSassToSourceCorrelationParams::fileName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Path for the source file. 
</div>
</div><p>
<a class="anchor" name="d381fee6d12b4b5ea4003be6b0acff52"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::functionName" ref="d381fee6d12b4b5ea4003be6b0acff52" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#d381fee6d12b4b5ea4003be6b0acff52">CUpti_GetSassToSourceCorrelationParams::functionName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Function name to which PC belongs. 
</div>
</div><p>
<a class="anchor" name="b5f50cab1eb124a26a5b7a5907429906"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::lineNumber" ref="b5f50cab1eb124a26a5b7a5907429906" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#b5f50cab1eb124a26a5b7a5907429906">CUpti_GetSassToSourceCorrelationParams::lineNumber</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Line number in the source code. 
</div>
</div><p>
<a class="anchor" name="9713b8a495f78284612c1f3403a8bd39"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::pcOffset" ref="9713b8a495f78284612c1f3403a8bd39" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#9713b8a495f78284612c1f3403a8bd39">CUpti_GetSassToSourceCorrelationParams::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] PC offset 
</div>
</div><p>
<a class="anchor" name="2820390c5cb5cbf9b303d32b5c2518ee"></a><!-- doxytag: member="CUpti_GetSassToSourceCorrelationParams::size" ref="2820390c5cb5cbf9b303d32b5c2518ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#2820390c5cb5cbf9b303d32b5c2518ee">CUpti_GetSassToSourceCorrelationParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure i.e. CUpti_GetSassToSourceCorrelationParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
