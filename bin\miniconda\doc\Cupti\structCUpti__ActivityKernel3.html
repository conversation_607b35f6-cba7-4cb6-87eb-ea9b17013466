<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel3" -->The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#fd078948ec85708d2df4484788ccb023">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#ff2a8c3f66d0294cd1021dffdda67828">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#b3a065f7153877130a7d5456e38532e8">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#71db41749d522c1c3784ec96767301e0">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#5aeab8003fdd6d65f06114ea56a619e1">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#80eb7835cb991157cd4561714e375b36">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#c3a71d3168f42a1ce7dcbe69fe183889">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#fb14d70e5108cf375607f8735b6685fa">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#6bb4fa8c3f0ac666213316e5dbe94f97">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#62e1442eb00c5fd71c891aa2937a4902">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#e7f3d203696eb3f5fccbf6764a4adaaa">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#f2bf5e6ebbc191c5ec670504cb740b46">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#7becd35f6966f32aa6aa2adcc5cc3cdf">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#99126399573deb8c0915891c77111334">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#7eb69eef831122583850168410ef3c0d">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#aa207e6fbd4bb22a1d6d476d19b3e67b">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#74d99310843f770590957310c1119031">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#9e94fd60a71ec506777ff5dc1efe1112">partitionedGlobalCacheExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#c486131d9d49586aa959013eae2901f9">partitionedGlobalCacheRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#d3f8a99006bdc0efd21ace4e0c8514e7">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#f72d27d8b8b706c3af8e7fd161cb1b88">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#f32f4efe66730c22eadc4f55b8691e79">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#1bd2d2d4fb7069a04164f0151a758475">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#98eb66964d6014b1e22de1abc8ad8038">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#561127cefbf69d446a9b958f099b5edf">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#3bdeac2ad95ff4bc1173730c401a10e7">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html#a1989ef0d58190bd5f34562bf46b00a9">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL). Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="fd078948ec85708d2df4484788ccb023"></a><!-- doxytag: member="CUpti_ActivityKernel3::blockX" ref="fd078948ec85708d2df4484788ccb023" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#fd078948ec85708d2df4484788ccb023">CUpti_ActivityKernel3::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="ff2a8c3f66d0294cd1021dffdda67828"></a><!-- doxytag: member="CUpti_ActivityKernel3::blockY" ref="ff2a8c3f66d0294cd1021dffdda67828" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#ff2a8c3f66d0294cd1021dffdda67828">CUpti_ActivityKernel3::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="b3a065f7153877130a7d5456e38532e8"></a><!-- doxytag: member="CUpti_ActivityKernel3::blockZ" ref="b3a065f7153877130a7d5456e38532e8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#b3a065f7153877130a7d5456e38532e8">CUpti_ActivityKernel3::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="71db41749d522c1c3784ec96767301e0"></a><!-- doxytag: member="CUpti_ActivityKernel3::completed" ref="71db41749d522c1c3784ec96767301e0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel3.html#71db41749d522c1c3784ec96767301e0">CUpti_ActivityKernel3::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="5aeab8003fdd6d65f06114ea56a619e1"></a><!-- doxytag: member="CUpti_ActivityKernel3::contextId" ref="5aeab8003fdd6d65f06114ea56a619e1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel3.html#5aeab8003fdd6d65f06114ea56a619e1">CUpti_ActivityKernel3::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="80eb7835cb991157cd4561714e375b36"></a><!-- doxytag: member="CUpti_ActivityKernel3::correlationId" ref="80eb7835cb991157cd4561714e375b36" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel3.html#80eb7835cb991157cd4561714e375b36">CUpti_ActivityKernel3::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="c3a71d3168f42a1ce7dcbe69fe183889"></a><!-- doxytag: member="CUpti_ActivityKernel3::deviceId" ref="c3a71d3168f42a1ce7dcbe69fe183889" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel3.html#c3a71d3168f42a1ce7dcbe69fe183889">CUpti_ActivityKernel3::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="fb14d70e5108cf375607f8735b6685fa"></a><!-- doxytag: member="CUpti_ActivityKernel3::dynamicSharedMemory" ref="fb14d70e5108cf375607f8735b6685fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#fb14d70e5108cf375607f8735b6685fa">CUpti_ActivityKernel3::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="6bb4fa8c3f0ac666213316e5dbe94f97"></a><!-- doxytag: member="CUpti_ActivityKernel3::end" ref="6bb4fa8c3f0ac666213316e5dbe94f97" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel3.html#6bb4fa8c3f0ac666213316e5dbe94f97">CUpti_ActivityKernel3::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="3bdeac2ad95ff4bc1173730c401a10e7"></a><!-- doxytag: member="CUpti_ActivityKernel3::executed" ref="3bdeac2ad95ff4bc1173730c401a10e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel3.html#3bdeac2ad95ff4bc1173730c401a10e7">CUpti_ActivityKernel3::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="62e1442eb00c5fd71c891aa2937a4902"></a><!-- doxytag: member="CUpti_ActivityKernel3::gridId" ref="62e1442eb00c5fd71c891aa2937a4902" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel3.html#62e1442eb00c5fd71c891aa2937a4902">CUpti_ActivityKernel3::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="e7f3d203696eb3f5fccbf6764a4adaaa"></a><!-- doxytag: member="CUpti_ActivityKernel3::gridX" ref="e7f3d203696eb3f5fccbf6764a4adaaa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#e7f3d203696eb3f5fccbf6764a4adaaa">CUpti_ActivityKernel3::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="f2bf5e6ebbc191c5ec670504cb740b46"></a><!-- doxytag: member="CUpti_ActivityKernel3::gridY" ref="f2bf5e6ebbc191c5ec670504cb740b46" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#f2bf5e6ebbc191c5ec670504cb740b46">CUpti_ActivityKernel3::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="7becd35f6966f32aa6aa2adcc5cc3cdf"></a><!-- doxytag: member="CUpti_ActivityKernel3::gridZ" ref="7becd35f6966f32aa6aa2adcc5cc3cdf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#7becd35f6966f32aa6aa2adcc5cc3cdf">CUpti_ActivityKernel3::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="99126399573deb8c0915891c77111334"></a><!-- doxytag: member="CUpti_ActivityKernel3::kind" ref="99126399573deb8c0915891c77111334" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel3.html#99126399573deb8c0915891c77111334">CUpti_ActivityKernel3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="7eb69eef831122583850168410ef3c0d"></a><!-- doxytag: member="CUpti_ActivityKernel3::localMemoryPerThread" ref="7eb69eef831122583850168410ef3c0d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel3.html#7eb69eef831122583850168410ef3c0d">CUpti_ActivityKernel3::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="aa207e6fbd4bb22a1d6d476d19b3e67b"></a><!-- doxytag: member="CUpti_ActivityKernel3::localMemoryTotal" ref="aa207e6fbd4bb22a1d6d476d19b3e67b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel3.html#aa207e6fbd4bb22a1d6d476d19b3e67b">CUpti_ActivityKernel3::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="74d99310843f770590957310c1119031"></a><!-- doxytag: member="CUpti_ActivityKernel3::name" ref="74d99310843f770590957310c1119031" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel3.html#74d99310843f770590957310c1119031">CUpti_ActivityKernel3::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="9e94fd60a71ec506777ff5dc1efe1112"></a><!-- doxytag: member="CUpti_ActivityKernel3::partitionedGlobalCacheExecuted" ref="9e94fd60a71ec506777ff5dc1efe1112" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel3.html#9e94fd60a71ec506777ff5dc1efe1112">CUpti_ActivityKernel3::partitionedGlobalCacheExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching executed for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. Partitioned global caching can be automatically disabled if the occupancy requirement of the launch cannot support caching. 
</div>
</div><p>
<a class="anchor" name="c486131d9d49586aa959013eae2901f9"></a><!-- doxytag: member="CUpti_ActivityKernel3::partitionedGlobalCacheRequested" ref="c486131d9d49586aa959013eae2901f9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel3.html#c486131d9d49586aa959013eae2901f9">CUpti_ActivityKernel3::partitionedGlobalCacheRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching requested for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. 
</div>
</div><p>
<a class="anchor" name="d3f8a99006bdc0efd21ace4e0c8514e7"></a><!-- doxytag: member="CUpti_ActivityKernel3::registersPerThread" ref="d3f8a99006bdc0efd21ace4e0c8514e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel3.html#d3f8a99006bdc0efd21ace4e0c8514e7">CUpti_ActivityKernel3::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="a1989ef0d58190bd5f34562bf46b00a9"></a><!-- doxytag: member="CUpti_ActivityKernel3::requested" ref="a1989ef0d58190bd5f34562bf46b00a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel3.html#a1989ef0d58190bd5f34562bf46b00a9">CUpti_ActivityKernel3::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="f72d27d8b8b706c3af8e7fd161cb1b88"></a><!-- doxytag: member="CUpti_ActivityKernel3::reserved0" ref="f72d27d8b8b706c3af8e7fd161cb1b88" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel3.html#f72d27d8b8b706c3af8e7fd161cb1b88">CUpti_ActivityKernel3::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="f32f4efe66730c22eadc4f55b8691e79"></a><!-- doxytag: member="CUpti_ActivityKernel3::sharedMemoryConfig" ref="f32f4efe66730c22eadc4f55b8691e79" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel3.html#f32f4efe66730c22eadc4f55b8691e79">CUpti_ActivityKernel3::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="1bd2d2d4fb7069a04164f0151a758475"></a><!-- doxytag: member="CUpti_ActivityKernel3::start" ref="1bd2d2d4fb7069a04164f0151a758475" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel3.html#1bd2d2d4fb7069a04164f0151a758475">CUpti_ActivityKernel3::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="98eb66964d6014b1e22de1abc8ad8038"></a><!-- doxytag: member="CUpti_ActivityKernel3::staticSharedMemory" ref="98eb66964d6014b1e22de1abc8ad8038" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel3.html#98eb66964d6014b1e22de1abc8ad8038">CUpti_ActivityKernel3::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="561127cefbf69d446a9b958f099b5edf"></a><!-- doxytag: member="CUpti_ActivityKernel3::streamId" ref="561127cefbf69d446a9b958f099b5edf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel3.html#561127cefbf69d446a9b958f099b5edf">CUpti_ActivityKernel3::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
