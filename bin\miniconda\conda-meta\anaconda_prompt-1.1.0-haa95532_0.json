{"arch": "x86_64", "build": "haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": ["anaconda-navigator >=1.9.8", "menuinst >=2.1.1"], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda_prompt-1.1.0-haa95532_0", "files": ["Menu/anaconda_prompt.ico", "Menu/anaconda_prompt_menu.json", "Scripts/.anaconda_prompt-post-link.bat", "Menu/anaconda_prompt_menu.json"], "fn": "anaconda_prompt-1.1.0-haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON> AND CC-BY-NC-ND-4.0", "license_family": "Other", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda_prompt-1.1.0-haa95532_0", "type": 1}, "md5": "f12980dc18dd04d5d50492c7c0f1d1a2", "name": "anaconda_prompt", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda_prompt-1.1.0-haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::anaconda_prompt==1.1.0=haa95532_0[md5=f12980dc18dd04d5d50492c7c0f1d1a2]", "sha256": "62290286ee23d7889a1ef4f973d77449c3e9c1a55d6b7919288595f93dee7b50", "size": 539393, "subdir": "win-64", "timestamp": 1727197160000, "url": "https://repo.anaconda.com/pkgs/main/win-64/anaconda_prompt-1.1.0-haa95532_0.conda", "version": "1.1.0"}