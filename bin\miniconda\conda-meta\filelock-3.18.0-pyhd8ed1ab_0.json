{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\filelock-3.18.0-pyhd8ed1ab_0", "features": "", "files": ["Lib/site-packages/filelock-3.18.0.dist-info/INSTALLER", "Lib/site-packages/filelock-3.18.0.dist-info/METADATA", "Lib/site-packages/filelock-3.18.0.dist-info/RECORD", "Lib/site-packages/filelock-3.18.0.dist-info/REQUESTED", "Lib/site-packages/filelock-3.18.0.dist-info/WHEEL", "Lib/site-packages/filelock-3.18.0.dist-info/direct_url.json", "Lib/site-packages/filelock-3.18.0.dist-info/licenses/LICENSE", "Lib/site-packages/filelock/__init__.py", "Lib/site-packages/filelock/_api.py", "Lib/site-packages/filelock/_error.py", "Lib/site-packages/filelock/_soft.py", "Lib/site-packages/filelock/_unix.py", "Lib/site-packages/filelock/_util.py", "Lib/site-packages/filelock/_windows.py", "Lib/site-packages/filelock/asyncio.py", "Lib/site-packages/filelock/py.typed", "Lib/site-packages/filelock/version.py", "Lib/site-packages/filelock/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_api.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_error.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_soft.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_unix.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_util.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_windows.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/asyncio.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/version.cpython-310.pyc"], "fn": "filelock-3.18.0-pyhd8ed1ab_0.conda", "license": "Unlicense", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\filelock-3.18.0-pyhd8ed1ab_0", "type": 1}, "md5": "4547b39256e296bb758166893e909a7c", "name": "filelock", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\filelock-3.18.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/filelock-3.18.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/filelock-3.18.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6ccceb64c205cad21b820fd668ba261fbf62ffb284c7c6a15f42090716f1d7f2", "sha256_in_prefix": "6ccceb64c205cad21b820fd668ba261fbf62ffb284c7c6a15f42090716f1d7f2", "size_in_bytes": 2897}, {"_path": "site-packages/filelock-3.18.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "223cadd3922d7912cf0183686d3c4a7f84a42ef40c1139f6e0f978907717c9db", "sha256_in_prefix": "223cadd3922d7912cf0183686d3c4a7f84a42ef40c1139f6e0f978907717c9db", "size_in_bytes": 1765}, {"_path": "site-packages/filelock-3.18.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/filelock-3.18.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/filelock-3.18.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "fc3ea3c58a9b2e235acbf444eded9785a51b359e4db0d9a2b6ec644e168a13d5", "sha256_in_prefix": "fc3ea3c58a9b2e235acbf444eded9785a51b359e4db0d9a2b6ec644e168a13d5", "size_in_bytes": 104}, {"_path": "site-packages/filelock-3.18.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "88d9b4eb60579c191ec391ca04c16130572d7eedc4a86daa58bf28c6e14c9bcd", "sha256_in_prefix": "88d9b4eb60579c191ec391ca04c16130572d7eedc4a86daa58bf28c6e14c9bcd", "size_in_bytes": 1210}, {"_path": "site-packages/filelock/__init__.py", "path_type": "hardlink", "sha256": "fedffe380197a3fab23daf65350d589f355812f496dc8d289cfab3a689ac5558", "sha256_in_prefix": "fedffe380197a3fab23daf65350d589f355812f496dc8d289cfab3a689ac5558", "size_in_bytes": 1769}, {"_path": "site-packages/filelock/_api.py", "path_type": "hardlink", "sha256": "d9a01305e277fa3b4c8f93929bb104e77f6235a4c1b1fd77297b5c04ca22f283", "sha256_in_prefix": "d9a01305e277fa3b4c8f93929bb104e77f6235a4c1b1fd77297b5c04ca22f283", "size_in_bytes": 14545}, {"_path": "site-packages/filelock/_error.py", "path_type": "hardlink", "sha256": "fb98cc7234eeeb4600bc03b551ba830f5188123564c2bf31085c0306d31e6038", "sha256_in_prefix": "fb98cc7234eeeb4600bc03b551ba830f5188123564c2bf31085c0306d31e6038", "size_in_bytes": 787}, {"_path": "site-packages/filelock/_soft.py", "path_type": "hardlink", "sha256": "85aaad73f4c1fca25b62fd9af22b8401c94ab8ce1f306d6f4dca76f2c2bdd7d7", "sha256_in_prefix": "85aaad73f4c1fca25b62fd9af22b8401c94ab8ce1f306d6f4dca76f2c2bdd7d7", "size_in_bytes": 1711}, {"_path": "site-packages/filelock/_unix.py", "path_type": "hardlink", "sha256": "7863ace200e067ee5f1a7254cfe3a424379990033382f61c0fc8550fa5c7edee", "sha256_in_prefix": "7863ace200e067ee5f1a7254cfe3a424379990033382f61c0fc8550fa5c7edee", "size_in_bytes": 2351}, {"_path": "site-packages/filelock/_util.py", "path_type": "hardlink", "sha256": "4070683452187db013861a2d1f743c13669c15cf38c291b8f7e4febaed35ed91", "sha256_in_prefix": "4070683452187db013861a2d1f743c13669c15cf38c291b8f7e4febaed35ed91", "size_in_bytes": 1715}, {"_path": "site-packages/filelock/_windows.py", "path_type": "hardlink", "sha256": "f24e1720197fcd955f182da0cf4904afc0d906fa4d6bcc1d53da9e33562b05bf", "sha256_in_prefix": "f24e1720197fcd955f182da0cf4904afc0d906fa4d6bcc1d53da9e33562b05bf", "size_in_bytes": 2179}, {"_path": "site-packages/filelock/asyncio.py", "path_type": "hardlink", "sha256": "1197495646cc9d932e430cee3cde48bd70f4520cedfffbceb6b30fe3eb2255e5", "sha256_in_prefix": "1197495646cc9d932e430cee3cde48bd70f4520cedfffbceb6b30fe3eb2255e5", "size_in_bytes": 12451}, {"_path": "site-packages/filelock/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/filelock/version.py", "path_type": "hardlink", "sha256": "0fd800885f4f187e1d4058db7ba55c5e153c93208ba54efe73bfef7d93fef877", "sha256_in_prefix": "0fd800885f4f187e1d4058db7ba55c5e153c93208ba54efe73bfef7d93fef877", "size_in_bytes": 513}, {"_path": "Lib/site-packages/filelock/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_error.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_soft.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_unix.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/asyncio.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "de7b6d4c4f865609ae88db6fa03c8b7544c2452a1aa5451eb7700aad16824570", "size": 17887, "subdir": "noarch", "timestamp": 1741969612000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda", "version": "3.18.0"}