{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cudart-12.1.55-0", "features": "", "files": ["lib/x64/cuda.lib", "lib/x64/cudadevrt.lib", "lib/x64/cudart.lib", "lib/x64/cudart_static.lib"], "fn": "cuda-cudart-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cudart-12.1.55-0", "type": 1}, "md5": "073f0ef01b8d53001e4238597fe659ee", "name": "cuda-cudart", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cudart-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/cuda.lib", "path_type": "hardlink", "sha256": "babb7c9fb60dc859656ed9cd111e9daf96cd26e6efca6a8434e7b66b3510c9cc", "sha256_in_prefix": "babb7c9fb60dc859656ed9cd111e9daf96cd26e6efca6a8434e7b66b3510c9cc", "size_in_bytes": 145306}, {"_path": "lib/x64/cudadevrt.lib", "path_type": "hardlink", "sha256": "2ad5c8b87f797785b6707c2d80e3c13c502581b964cfa045d09f59c3876690f1", "sha256_in_prefix": "2ad5c8b87f797785b6707c2d80e3c13c502581b964cfa045d09f59c3876690f1", "size_in_bytes": 1606278}, {"_path": "lib/x64/cudart.lib", "path_type": "hardlink", "sha256": "2824a062b05d17e9b3863ec7e2ade18435aee8c0f26d0cb9710c02136295f418", "sha256_in_prefix": "2824a062b05d17e9b3863ec7e2ade18435aee8c0f26d0cb9710c02136295f418", "size_in_bytes": 106556}, {"_path": "lib/x64/cudart_static.lib", "path_type": "hardlink", "sha256": "e0fb55c6f092bc91dc61486ea8aea827cd4529598fe0acaed057362e88ee9a2f", "sha256_in_prefix": "e0fb55c6f092bc91dc61486ea8aea827cd4529598fe0acaed057362e88ee9a2f", "size_in_bytes": 2603618}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 987652, "subdir": "win-64", "timestamp": 1674623865000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-cudart-12.1.55-0.tar.bz2", "version": "12.1.55"}