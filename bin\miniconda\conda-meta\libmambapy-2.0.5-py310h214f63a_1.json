{"arch": "x86_64", "build": "py310h214f63a_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["fmt >=9.1.0,<10.0a0", "libmamba 2.0.5 hcd6fe79_1", "pybind11-abi 5", "python >=3.10,<3.11.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libmambapy-2.0.5-py310h214f63a_1", "files": ["Lib/site-packages/libmambapy-2.0.5.dist-info/INSTALLER", "Lib/site-packages/libmambapy-2.0.5.dist-info/LICENSE", "Lib/site-packages/libmambapy-2.0.5.dist-info/METADATA", "Lib/site-packages/libmambapy-2.0.5.dist-info/RECORD", "Lib/site-packages/libmambapy-2.0.5.dist-info/REQUESTED", "Lib/site-packages/libmambapy-2.0.5.dist-info/WHEEL", "Lib/site-packages/libmambapy-2.0.5.dist-info/direct_url.json", "Lib/site-packages/libmambapy-2.0.5.dist-info/top_level.txt", "Lib/site-packages/libmambapy/__init__.py", "Lib/site-packages/libmambapy/__init__.pyi", "Lib/site-packages/libmambapy/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/libmambapy/__pycache__/specs.cpython-310.pyc", "Lib/site-packages/libmambapy/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/libmambapy/__pycache__/version.cpython-310.pyc", "Lib/site-packages/libmambapy/bindings.cp310-win_amd64.pyd", "Lib/site-packages/libmambapy/py.typed", "Lib/site-packages/libmambapy/solver/__init__.py", "Lib/site-packages/libmambapy/solver/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/libmambapy/solver/__pycache__/libsolv.cpython-310.pyc", "Lib/site-packages/libmambapy/solver/libsolv.py", "Lib/site-packages/libmambapy/specs.py", "Lib/site-packages/libmambapy/utils.py", "Lib/site-packages/libmambapy/version.py"], "fn": "libmambapy-2.0.5-py310h214f63a_1.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libmambapy-2.0.5-py310h214f63a_1", "type": 1}, "md5": "d9efb242110a16dc0b860e4cf9756507", "name": "lib<PERSON><PERSON><PERSON>", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libmambapy-2.0.5-py310h214f63a_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::libmambapy==2.0.5=py310h214f63a_1[md5=d9efb242110a16dc0b860e4cf9756507]", "sha256": "5281ab04ee5fada6056d3a0e7a549d3104d91757c94bbc25893d33c1bd5da739", "size": 470188, "subdir": "win-64", "timestamp": 1734470703000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libmambapy-2.0.5-py310h214f63a_1.conda", "version": "2.0.5"}