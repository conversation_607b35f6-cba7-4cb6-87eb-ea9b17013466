<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li class="current"><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_e">- e -</a></h3><ul>
<li>eccEnabled
: <a class="el" href="structCUpti__ActivityDevice2.html#dfb91054e5f21eecfade7c8a31a8531c">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#74e01758edc213936a49b950a7def423">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#c35f658d021675ac8166ce7890d4b7ef">CUpti_ActivityDevice4</a>
<li>enable
: <a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#e7b6312b500a6b58a01c9d6ef85eacfc">CUpti_ActivityUnifiedMemoryCounterConfig</a>
<li>enabled
: <a class="el" href="structCUpti__ActivityAutoBoostState.html#03e0666defb373bba5f438637dd85c71">CUpti_ActivityAutoBoostState</a>
<li>enableStartStopControlData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#878da908c058ab9b44f43d0d360985b8">CUpti_PCSamplingConfigurationInfo</a>
<li>end
: <a class="el" href="structCUpti__ActivityMemcpy3.html#5bd27ea4d0bb88b5007d7d3c80269968">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#cf3e11b13fe2b518aae0b13fc401db74">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#b2911952d99de824524eca83a7f467dc">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivitySynchronization.html#c4a1752012e7275052cddb462afc2e3a">CUpti_ActivitySynchronization</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#8ee2f0f0b4421a7162f2060045f5d65e">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#3fe92274275627fb28986e5ffc046d57">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#a1c632d5e5a81b1cbd1ebd5a24cb2156">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#9e7622e9401b477f8c0159b9134d0a53">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#7fb783c0f89817f2532bcf2ebcdfef22">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#17ff240af01360230ce7ac60a5edec17">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#e6fce4b07317091d45c4f88b781827d6">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityMemory.html#c65778c1dbe448c2e20d88bb8c25aedd">CUpti_ActivityMemory</a>
, <a class="el" href="structCUpti__ActivityOpenMp.html#9516c0145f454bd6852bc6ed13c4fd77">CUpti_ActivityOpenMp</a>
, <a class="el" href="structCUpti__ActivityJit.html#f950b7889a01a46b90cf3fd92e8035f6">CUpti_ActivityJit</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#ef8025a3827b17b90a4cc99bb11e56ff">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityKernel.html#bf78c6db9c33dfd79974957d089290de">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityGraphTrace.html#969aec6a37b49295f16a8ffc46f00ef5">CUpti_ActivityGraphTrace</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#5919087d294027fdb577f0a49126a8d4">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#07f09f693bb1e992290561f97719600b">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#6bb4fa8c3f0ac666213316e5dbe94f97">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#105333252eb769a65bf72999bcdf1adf">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#96aff067a38adcd032bb1ac4b14a1805">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#8b31b2a5d3549e8fdcd7fe307bc1d9bd">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#4f5c7fc3ffb9c39e579017e9ff2801c5">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#5f358be82cf08920253169515a3e78bd">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#37b75fa3484590e7dee6638cef67574e">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#c0f55394ac1ca5ff5f14ab0e14616914">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#bb7798000bd79dab352b22ef586fbb9b">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#b7ef10394792c28177351069bae13e0d">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#2bc2f653e4279eeb4e1542841848636c">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#35c486131233face3ef7a73906136c4e">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityMemset.html#3f2cbd0bcdc8f359644d6dc48d48e107">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityAPI.html#bd3d87d60799bd4f2c9b4c87fea94065">CUpti_ActivityAPI</a>
, <a class="el" href="structCUpti__ActivityOverhead.html#220db95d05426890eea2c9d54dcddcf5">CUpti_ActivityOverhead</a>
<li>endLineNo
: <a class="el" href="structCUpti__ActivityOpenAcc.html#509bf31ec7eb759fa779b447e1ed3f75">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#6b91c979cf60028b61486a1e81ca83e4">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#99cd7548f7706d58caf4835693ef14a6">CUpti_ActivityOpenAccOther</a>
<li>environmentKind
: <a class="el" href="structCUpti__ActivityEnvironment.html#24a3b403bb0fc5bab993ae9189d9feee">CUpti_ActivityEnvironment</a>
<li>eventGroups
: <a class="el" href="structCUpti__EventGroupSet.html#f60597b272e9f6dc4df31900244fe1ea">CUpti_EventGroupSet</a>
<li>eventId
: <a class="el" href="structCUpti__ActivityCudaEvent.html#8b9e6f02472f8aa68d68fcef5dfc1640">CUpti_ActivityCudaEvent</a>
<li>eventKind
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#3122d644c244764c169d0558b034af55">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#5f7b0689f352034596684eaf0d64f504">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#6df560544faee7bb2e7814bd385da964">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#a65025a8e30dd956d9c194153500dae5">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenMp.html#4387c46cde06fbb6142b52a2940da8f2">CUpti_ActivityOpenMp</a>
<li>executed
: <a class="el" href="structCUpti__ActivityKernel6.html#38754cadc9f22e58b3611be3e90362b7">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#1714f6cfcaa3402a285707591252aeef">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivityBranch.html#ee5c8a83a5913a4205adecb02758433f">CUpti_ActivityBranch</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#3bdeac2ad95ff4bc1173730c401a10e7">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#25cbbe93c18dce9315afa389602bf12d">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess.html#3e39938b2ade61bddf5f7091762acec2">CUpti_ActivityGlobalAccess</a>
, <a class="el" href="structCUpti__ActivityBranch2.html#44f12bb52eb2c19ae96a276d2a1af8a1">CUpti_ActivityBranch2</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#a71e5396c4336e63ef2f6a135383fd2b">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#96a37b99d338e3db9d8e0fdf43ad4f93">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#dd927e9f135243f3033bb65cb36723bb">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#c99848bbc5ac89558f18ffdc00bf06a3">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#b025e85744e58f9c44e0ea1f651a4456">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#91844f8055d4b82dac07ddf422cda673">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#bbfbbb7db5a351a67f81befc71a4efe4">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#a4e77c8d77fd7865212b38c06efe2295">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#11e235e20debbe9f55ee838c5b551769">CUpti_ActivityGlobalAccess3</a>
<li>externalId
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#ff35f0dd72a9d6231f10d31085e6cd21">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityExternalCorrelation.html#17e5969f66b45bd95b1189544c2c854b">CUpti_ActivityExternalCorrelation</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#38d62d6c2b3634c119a1451638edd2ce">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#e4bddc0b6ccca9b232fcbc5bed99906a">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#e42271bc670e3c511ee286b4f574c863">CUpti_ActivityOpenAcc</a>
<li>externalKind
: <a class="el" href="structCUpti__ActivityExternalCorrelation.html#a997f0ac4ebbab28543a94d1460be154">CUpti_ActivityExternalCorrelation</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
