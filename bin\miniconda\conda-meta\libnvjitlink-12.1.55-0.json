{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjitlink-12.1.55-0", "features": "", "files": ["lib/x64/nvJitLink.lib", "lib/x64/nvJitLink_static.lib"], "fn": "libnvjitlink-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjitlink-12.1.55-0", "type": 1}, "md5": "c81cd07a6fd35e72568a1c3db46fc59f", "name": "libnvjitlink", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjitlink-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/nvJitLink.lib", "path_type": "hardlink", "sha256": "69349204a2dfaf76bc53d5fa460982125985ea0525ed4237c0e9d14b70bca656", "sha256_in_prefix": "69349204a2dfaf76bc53d5fa460982125985ea0525ed4237c0e9d14b70bca656", "size_in_bytes": 8708}, {"_path": "lib/x64/nvJitLink_static.lib", "path_type": "hardlink", "sha256": "4aac53d75bb3502e6a8a61ad8bde6ac059df56ccc00b6fd1e87b126377297489", "sha256_in_prefix": "4aac53d75bb3502e6a8a61ad8bde6ac059df56ccc00b6fd1e87b126377297489", "size_in_bytes": 309313044}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 70605328, "subdir": "win-64", "timestamp": 1674622585000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libnvjitlink-12.1.55-0.tar.bz2", "version": "12.1.55"}