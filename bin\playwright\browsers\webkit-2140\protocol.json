[{"domain": "Animation", "description": "Domain for tracking/modifying Web Animations, as well as CSS (declarative) animations and transitions.", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page"], "types": [{"id": "AnimationId", "type": "string", "description": "Unique Web Animation identifier."}, {"id": "AnimationState", "type": "string", "enum": ["ready", "delayed", "active", "canceled", "done"]}, {"id": "PlaybackDirection", "type": "string", "enum": ["normal", "reverse", "alternate", "alternate-reverse"]}, {"id": "FillMode", "type": "string", "enum": ["none", "forwards", "backwards", "both", "auto"]}, {"id": "Animation", "type": "object", "properties": [{"name": "animationId", "$ref": "AnimationId"}, {"name": "name", "type": "string", "optional": true, "description": "Equal to `Animation.prototype.get id`."}, {"name": "cssAnimationName", "type": "string", "optional": true, "description": "Equal to the corresponding `animation-name` CSS property. Should not be provided if `transitionProperty` is also provided."}, {"name": "cssTransitionProperty", "type": "string", "optional": true, "description": "Equal to the corresponding `transition-property` CSS property. Should not be provided if `animationName` is also provided."}, {"name": "effect", "$ref": "Effect", "optional": true}, {"name": "stackTrace", "$ref": "Console.StackTrace", "optional": true, "description": "Backtrace that was captured when this `WebAnimation` was created."}]}, {"id": "Effect", "type": "object", "properties": [{"name": "startDelay", "type": "number", "optional": true}, {"name": "endDelay", "type": "number", "optional": true}, {"name": "iterationCount", "type": "number", "optional": true, "description": "Number of iterations in the animation. <code>Infinity</code> is represented as <code>-1</code>."}, {"name": "iterationStart", "type": "number", "optional": true, "description": "Index of which iteration to start at."}, {"name": "iterationDuration", "type": "number", "optional": true, "description": "Total time of each iteration, measured in milliseconds."}, {"name": "timingFunction", "type": "string", "optional": true, "description": "CSS timing function of the overall animation."}, {"name": "playbackDirection", "$ref": "PlaybackDirection", "optional": true}, {"name": "fillMode", "$ref": "FillMode", "optional": true}, {"name": "keyframes", "type": "array", "items": {"$ref": "Keyframe"}, "optional": true}]}, {"id": "Keyframe", "type": "object", "properties": [{"name": "offset", "type": "number", "description": "Decimal percentage [0,1] representing where this keyframe is in the entire duration of the animation."}, {"name": "easing", "type": "string", "optional": true, "description": "CSS timing function for how the `style` is applied."}, {"name": "style", "type": "string", "optional": true, "description": "CSS style declaration of the CSS properties that will be animated."}]}, {"id": "TrackingUpdate", "type": "object", "properties": [{"name": "trackingAnimationId", "$ref": "AnimationId"}, {"name": "animationState", "$ref": "AnimationState"}, {"name": "nodeId", "$ref": "DOM.NodeId", "optional": true}, {"name": "animationName", "type": "string", "optional": true, "description": "Equal to the corresponding `animation-name` CSS property. Should not be provided if `transitionProperty` is also provided."}, {"name": "transitionProperty", "type": "string", "optional": true, "description": "Equal to the corresponding `transition-property` CSS property. Should not be provided if `animationName` is also provided."}]}], "commands": [{"name": "enable", "description": "Enables Canvas domain events."}, {"name": "disable", "description": "Disables Canvas domain events."}, {"name": "requestEffectTarget", "description": "Gets the `DOM.NodeId` for the target of the effect of the animation with the given `AnimationId`.", "parameters": [{"name": "animationId", "$ref": "AnimationId"}], "returns": [{"name": "effect<PERSON>arget", "$ref": "DOM.Styleable"}]}, {"name": "resolveAnimation", "description": "Resolves JavaScript `WebAnimation` object for given `AnimationId`.", "parameters": [{"name": "animationId", "$ref": "AnimationId"}, {"name": "objectGroup", "type": "string", "optional": true, "description": "Symbolic group name that can be used to release multiple objects."}], "returns": [{"name": "object", "$ref": "Runtime.RemoteObject"}]}, {"name": "startTracking", "description": "Start tracking animations. This will produce a `trackingStart` event."}, {"name": "stopTracking", "description": "Stop tracking animations. This will produce a `trackingComplete` event."}], "events": [{"name": "animationCreated", "description": "Dispatched whenever a `WebAnimation` is created.", "parameters": [{"name": "animation", "$ref": "Animation"}]}, {"name": "nameChanged", "description": "Dispatched whenever `Animation.prototype.set id` is called.", "parameters": [{"name": "animationId", "$ref": "AnimationId"}, {"name": "name", "type": "string", "optional": true, "description": "Equal to `Animation.prototype.get id`."}]}, {"name": "effectChanged", "description": "Dispatched whenever the effect of any animation is changed in any way.", "parameters": [{"name": "animationId", "$ref": "AnimationId"}, {"name": "effect", "$ref": "Effect", "optional": true, "description": "This is omitted when the effect is removed without a replacement."}]}, {"name": "targetChanged", "description": "Dispatched whenever the target of any effect of any animation is changed in any way.", "parameters": [{"name": "animationId", "$ref": "AnimationId"}]}, {"name": "animationDestroyed", "description": "Dispatched whenever a `WebAnimation` is destroyed.", "parameters": [{"name": "animationId", "$ref": "AnimationId"}]}, {"name": "trackingStart", "description": "Dispatched after `startTracking` command.", "parameters": [{"name": "timestamp", "type": "number"}]}, {"name": "trackingUpdate", "description": "Fired for each phase of Web Animation.", "parameters": [{"name": "timestamp", "type": "number"}, {"name": "event", "$ref": "TrackingUpdate"}]}, {"name": "trackingComplete", "description": "Dispatched after `stopTracking` command.", "parameters": [{"name": "timestamp", "type": "number"}]}]}, {"domain": "Audit", "description": "", "version": 4, "debuggableTypes": ["itml", "javascript", "page", "service-worker", "web-page"], "targetTypes": ["itml", "javascript", "page", "service-worker", "worker"], "commands": [{"name": "setup", "description": "Creates the `WebInspectorAudit` object that is passed to run. Must call teardown before calling setup more than once.", "parameters": [{"name": "contextId", "$ref": "Runtime.ExecutionContextId", "optional": true, "description": "Specifies in which isolated context to run the test. Each content script lives in an isolated context and this parameter may be used to specify one of those contexts. If the parameter is omitted or 0 the evaluation will be performed in the context of the inspected page."}]}, {"name": "run", "description": "Parses and evaluates the given test string and sends back the result. Returned values are saved to the \"audit\" object group. Call setup before and teardown after if the `WebInspectorAudit` object should be passed into the test.", "parameters": [{"name": "test", "type": "string", "description": "Test string to parse and evaluate."}, {"name": "contextId", "$ref": "Runtime.ExecutionContextId", "optional": true, "description": "Specifies in which isolated context to run the test. Each content script lives in an isolated context and this parameter may be used to specify one of those contexts. If the parameter is omitted or 0 the evaluation will be performed in the context of the inspected page."}], "returns": [{"name": "result", "$ref": "Runtime.RemoteObject", "description": "Evaluation result."}, {"name": "wasThrown", "type": "boolean", "optional": true, "description": "True if the result was thrown during the evaluation."}]}, {"name": "teardown", "description": "Destroys the `WebInspectorAudit` object that is passed to run. Must call setup before calling teardown."}]}, {"domain": "Browser", "description": "The Browser domain contains commands and events related to getting information about the browser ", "debuggableTypes": ["web-page"], "targetTypes": ["web-page"], "types": [{"id": "ExtensionId", "type": "string", "description": "Unique extension identifier."}, {"id": "Extension", "type": "object", "description": "Information about an extension.", "properties": [{"name": "extensionId", "$ref": "ExtensionId", "description": "Extension identifier."}, {"name": "name", "type": "string", "description": "The display name for the extension."}]}], "commands": [{"name": "enable", "description": "Enables Browser domain events."}, {"name": "disable", "description": "Disables Browser domain events."}], "events": [{"name": "extensionsEnabled", "parameters": [{"name": "extensions", "type": "array", "items": {"$ref": "Extension"}, "description": "Information about the enabled extensions."}]}, {"name": "extensionsDisabled", "parameters": [{"name": "extensionIds", "type": "array", "items": {"$ref": "ExtensionId"}, "description": "Disabled extension identifiers."}]}]}, {"domain": "<PERSON><PERSON>", "description": "Canvas domain allows tracking of canvases that have an associated graphics context. Tracks canvases in the DOM and CSS canvases created with -webkit-canvas.", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page", "worker"], "types": [{"id": "CanvasId", "type": "string", "description": "Unique canvas identifier."}, {"id": "ProgramId", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "type": "string", "description": "Unique shader program identifier."}, {"id": "ColorSpace", "type": "string", "enum": ["srgb", "display-p3"]}, {"id": "ContextType", "type": "string", "enum": ["canvas-2d", "offscreen-canvas-2d", "bitmaprenderer", "offscreen-bitmaprenderer", "webgl", "offscreen-webgl", "webgl2", "offscreen-webgl2"], "description": "The type of rendering context backing the canvas element."}, {"id": "ProgramType", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "type": "string", "enum": ["compute", "render"]}, {"id": "ShaderType", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "type": "string", "enum": ["compute", "fragment", "vertex"]}, {"id": "ContextAttributes", "type": "object", "description": "Drawing surface attributes.", "properties": [{"name": "alpha", "type": "boolean", "optional": true, "description": "WebGL, WebGL2, ImageBitmapRenderingContext"}, {"name": "colorSpace", "$ref": "ColorSpace", "optional": true, "description": "2D"}, {"name": "desynchronized", "type": "boolean", "optional": true, "description": "2D"}, {"name": "willReadFrequently", "type": "boolean", "optional": true, "description": "2D"}, {"name": "depth", "type": "boolean", "optional": true, "description": "WebGL, WebGL2"}, {"name": "stencil", "type": "boolean", "optional": true, "description": "WebGL, WebGL2"}, {"name": "antialias", "type": "boolean", "optional": true, "description": "WebGL, WebGL2"}, {"name": "premultipliedAlpha", "type": "boolean", "optional": true, "description": "WebGL, WebGL2"}, {"name": "preserveDrawingBuffer", "type": "boolean", "optional": true, "description": "WebGL, WebGL2"}, {"name": "failIfMajorPerformanceCaveat", "type": "boolean", "optional": true, "description": "WebGL, WebGL2"}, {"name": "powerPreference", "type": "string", "optional": true, "description": "WebGL, WebGL2"}]}, {"id": "<PERSON><PERSON>", "type": "object", "description": "Information about a canvas for which a rendering context has been created.", "properties": [{"name": "canvasId", "$ref": "CanvasId", "description": "Canvas identifier."}, {"name": "contextType", "$ref": "ContextType", "description": "The type of rendering context backing the canvas."}, {"name": "width", "type": "number", "description": "Width of the canvas in pixels."}, {"name": "height", "type": "number", "description": "Height of the canvas in pixels."}, {"name": "nodeId", "$ref": "DOM.NodeId", "optional": true, "description": "The corresponding DOM node id."}, {"name": "cssCanvasName", "type": "string", "optional": true, "description": "The CSS canvas identifier, for canvases created with <code>document.getCSSCanvasContext</code>."}, {"name": "contextAttributes", "$ref": "ContextAttributes", "optional": true, "description": "Context attributes for rendering contexts."}, {"name": "memoryCost", "type": "number", "optional": true, "description": "Memory usage of the canvas in bytes."}, {"name": "stackTrace", "$ref": "Console.StackTrace", "optional": true, "description": "Backtrace that was captured when this canvas context was created."}]}, {"id": "ShaderProgram", "description": "Information about a WebGL/WebGL2 shader program.", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "type": "object", "properties": [{"name": "programId", "$ref": "ProgramId"}, {"name": "programType", "$ref": "ProgramType"}, {"name": "canvasId", "$ref": "CanvasId"}]}], "commands": [{"name": "enable", "description": "Enables Canvas domain events."}, {"name": "disable", "description": "Disables Canvas domain events."}, {"name": "requestNode", "description": "Gets the NodeId for the canvas node with the given CanvasId.", "targetTypes": ["page"], "parameters": [{"name": "canvasId", "$ref": "CanvasId", "description": "Canvas identifier."}], "returns": [{"name": "nodeId", "$ref": "DOM.NodeId", "description": "Node identifier for given canvas."}]}, {"name": "requestContent", "description": "Gets the data for the canvas node with the given CanvasId.", "parameters": [{"name": "canvasId", "$ref": "CanvasId", "description": "Canvas identifier."}], "returns": [{"name": "content", "type": "string", "description": "Base64-encoded data of the canvas' contents."}]}, {"name": "requestClientNodes", "description": "Gets all <code>-webkit-canvas</code> nodes or active <code>HTMLCanvasElement</code> for a <code>WebGPUDevice</code>.", "targetTypes": ["page"], "parameters": [{"name": "canvasId", "$ref": "CanvasId"}], "returns": [{"name": "clientNodeIds", "type": "array", "items": {"$ref": "DOM.NodeId"}}]}, {"name": "resolveContext", "description": "Resolves JavaScript canvas/device context object for given canvasId.", "parameters": [{"name": "canvasId", "$ref": "CanvasId", "description": "Canvas identifier."}, {"name": "objectGroup", "type": "string", "optional": true, "description": "Symbolic group name that can be used to release multiple objects."}], "returns": [{"name": "object", "$ref": "Runtime.RemoteObject", "description": "JavaScript object wrapper for given canvas context."}]}, {"name": "setRecordingAutoCaptureFrameCount", "description": "Tells the backend to record `count` frames whenever a new context is created.", "parameters": [{"name": "count", "type": "integer", "description": "Number of frames to record (0 means don't record anything)."}]}, {"name": "startRecording", "description": "Record the next frame, or up to the given number of bytes of data, for the given canvas.", "parameters": [{"name": "canvasId", "$ref": "CanvasId"}, {"name": "frameCount", "type": "integer", "optional": true, "description": "Number of frames to record (unlimited when not specified)."}, {"name": "memoryLimit", "type": "integer", "optional": true, "description": "Memory limit of recorded data (100MB when not specified)."}]}, {"name": "stopRecording", "description": "Stop recording the given canvas.", "parameters": [{"name": "canvasId", "$ref": "CanvasId"}]}, {"name": "requestShaderSource", "description": "Requests the source of the shader of the given type from the program with the given id.", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "parameters": [{"name": "programId", "$ref": "ProgramId"}, {"name": "shaderType", "$ref": "ShaderType"}], "returns": [{"name": "source", "type": "string"}]}, {"name": "updateShader", "description": "Compiles and links the shader with identifier and type with the given source code.", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "parameters": [{"name": "programId", "$ref": "ProgramId"}, {"name": "shaderType", "$ref": "ShaderType"}, {"name": "source", "type": "string"}]}, {"name": "setShaderProgramDisabled", "description": "Enable/disable the visibility of the given shader program.", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "parameters": [{"name": "programId", "$ref": "ProgramId"}, {"name": "disabled", "type": "boolean"}]}, {"name": "setShaderProgramHighlighted", "description": "Enable/disable highlighting of the given shader program.", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "parameters": [{"name": "programId", "$ref": "ProgramId"}, {"name": "highlighted", "type": "boolean"}]}], "events": [{"name": "canvasAdded", "parameters": [{"name": "canvas", "$ref": "<PERSON><PERSON>", "description": "Canvas object."}]}, {"name": "canvasRemoved", "parameters": [{"name": "canvasId", "$ref": "CanvasId", "description": "Removed canvas identifier."}]}, {"name": "canvasSizeChanged", "parameters": [{"name": "canvasId", "$ref": "CanvasId", "description": "Identifier of canvas that changed."}, {"name": "width", "type": "number", "description": "Width of the canvas in pixels."}, {"name": "height", "type": "number", "description": "Height of the canvas in pixels."}]}, {"name": "canvasMemoryChanged", "parameters": [{"name": "canvasId", "$ref": "CanvasId", "description": "Identifier of canvas that changed."}, {"name": "memoryCost", "type": "number", "description": "New memory cost value for the canvas in bytes."}]}, {"name": "extensionEnabled", "parameters": [{"name": "canvasId", "$ref": "CanvasId"}, {"name": "extension", "type": "string", "description": "Name of the extension that was enabled."}]}, {"name": "clientNodesChanged", "targetTypes": ["page"], "parameters": [{"name": "canvasId", "$ref": "CanvasId", "description": "Identifier of canvas that changed."}]}, {"name": "recordingStarted", "parameters": [{"name": "canvasId", "$ref": "CanvasId"}, {"name": "initiator", "$ref": "Recording.Initiator"}]}, {"name": "recordingProgress", "parameters": [{"name": "canvasId", "$ref": "CanvasId"}, {"name": "frames", "type": "array", "items": {"$ref": "<PERSON><PERSON>"}}, {"name": "bufferUsed", "type": "integer", "description": "Total memory size in bytes of all data recorded since the recording began."}]}, {"name": "recordingFinished", "parameters": [{"name": "canvasId", "$ref": "CanvasId"}, {"name": "recording", "$ref": "Recording.Recording", "optional": true}]}, {"name": "programCreated", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "parameters": [{"name": "shaderProgram", "$ref": "ShaderProgram"}]}, {"name": "programDeleted", "condition": "defined(ENABLE_WEBGL) && ENABLE_WEBGL", "parameters": [{"name": "programId", "$ref": "ProgramId"}]}]}, {"domain": "<PERSON><PERSON><PERSON>", "description": "Console domain defines methods and events for interaction with the JavaScript console. Console collects messages created by means of the <a href='http://getfirebug.com/wiki/index.php/Console_API'>JavaScript Console API</a>. One needs to enable this domain using <code>enable</code> command in order to start receiving the console messages. <PERSON><PERSON><PERSON> collects messages issued while console domain is not enabled as well and reports them using <code>messageAdded</code> notification upon enabling.", "debuggableTypes": ["itml", "javascript", "page", "service-worker", "web-page"], "targetTypes": ["itml", "javascript", "page", "service-worker", "worker"], "types": [{"id": "ChannelSource", "type": "string", "enum": ["xml", "javascript", "network", "console-api", "storage", "appcache", "rendering", "css", "security", "content-blocker", "media", "mediasource", "webrtc", "itp-debug", "private-click-measurement", "payment-request", "other"], "description": "Channels for different types of log messages."}, {"id": "ChannelLevel", "type": "string", "enum": ["off", "basic", "verbose"], "description": "Level of logging."}, {"id": "ClearReason", "type": "string", "enum": ["console-api", "frontend", "main-frame-navigation"], "description": "The reason the console is being cleared."}, {"id": "Channel", "description": "Logging channel.", "type": "object", "properties": [{"name": "source", "$ref": "ChannelSource"}, {"name": "level", "$ref": "ChannelLevel"}]}, {"id": "ConsoleMessage", "type": "object", "description": "Console message.", "properties": [{"name": "source", "$ref": "ChannelSource"}, {"name": "level", "type": "string", "enum": ["log", "info", "warning", "error", "debug"], "description": "Message severity."}, {"name": "text", "type": "string", "description": "Message text."}, {"name": "type", "type": "string", "optional": true, "enum": ["log", "dir", "dirxml", "table", "trace", "clear", "startGroup", "startGroupCollapsed", "endGroup", "assert", "timing", "profile", "profileEnd", "image"], "description": "Console message type."}, {"name": "url", "type": "string", "optional": true, "description": "URL of the message origin."}, {"name": "line", "type": "integer", "optional": true, "description": "Line number in the resource that generated this message."}, {"name": "column", "type": "integer", "optional": true, "description": "Column number on the line in the resource that generated this message."}, {"name": "repeatCount", "type": "integer", "optional": true, "description": "Repeat count for repeated messages."}, {"name": "parameters", "type": "array", "items": {"$ref": "Runtime.RemoteObject"}, "optional": true, "description": "Message parameters in case of the formatted message."}, {"name": "stackTrace", "$ref": "StackTrace", "optional": true, "description": "JavaScript stack trace for assertions and error messages."}, {"name": "networkRequestId", "$ref": "Network.RequestId", "optional": true, "description": "Identifier of the network request associated with this message."}, {"name": "timestamp", "type": "number", "optional": true, "description": "Time when this message was added. Currently only used when an expensive operation happens to make sure that the frontend can account for it."}]}, {"id": "CallFrame", "type": "object", "description": "Stack entry for console errors and assertions.", "properties": [{"name": "functionName", "type": "string", "description": "JavaScript function name."}, {"name": "url", "type": "string", "description": "JavaScript script name or url."}, {"name": "scriptId", "$ref": "Debugger.ScriptId", "description": "Script identifier."}, {"name": "lineNumber", "type": "integer", "description": "JavaScript script line number."}, {"name": "columnNumber", "type": "integer", "description": "JavaScript script column number."}]}, {"id": "StackTrace", "description": "Call frames for async function calls, console assertions, and error messages.", "type": "object", "properties": [{"name": "callFrames", "type": "array", "items": {"$ref": "CallFrame"}}, {"name": "topCallFrameIsBoundary", "type": "boolean", "optional": true, "description": "Whether the first item in <code>callFrames</code> is the native function that scheduled the asynchronous operation (e.g. setTimeout)."}, {"name": "truncated", "type": "boolean", "optional": true, "description": "Whether one or more frames have been truncated from the bottom of the stack."}, {"name": "parentStackTrace", "$ref": "StackTrace", "optional": true, "description": "<PERSON><PERSON>."}]}], "commands": [{"name": "enable", "description": "Enables console domain, sends the messages collected so far to the client by means of the <code>messageAdded</code> notification."}, {"name": "disable", "description": "Disables console domain, prevents further console messages from being reported to the client."}, {"name": "clearMessages", "description": "Clears console messages collected in the browser."}, {"name": "setConsoleClearAPIEnabled", "description": "Control whether calling <code>console.clear()</code> has an effect in Web Inspector. Defaults to true.", "parameters": [{"name": "enable", "type": "boolean"}]}, {"name": "getLoggingChannels", "description": "List of the different message sources that are non-default logging channels.", "returns": [{"name": "channels", "type": "array", "items": {"$ref": "Channel"}, "description": "Logging channels."}]}, {"name": "setLoggingChannelLevel", "description": "Modify the level of a channel.", "parameters": [{"name": "source", "$ref": "ChannelSource", "description": "Logging channel to modify."}, {"name": "level", "$ref": "ChannelLevel", "description": "New level."}]}], "events": [{"name": "messageAdded", "description": "Issued when new console message is added.", "parameters": [{"name": "message", "$ref": "ConsoleMessage", "description": "Console message that has been added."}]}, {"name": "messageRepeatCountUpdated", "description": "Issued when subsequent message(s) are equal to the previous one(s).", "parameters": [{"name": "count", "type": "integer", "description": "New repeat count value."}, {"name": "timestamp", "type": "number", "optional": true, "description": "Timestamp of the latest message."}]}, {"name": "messagesCleared", "description": "Issued when console is cleared. This happens either upon <code>clearMessages</code> command or after page navigation.", "parameters": [{"name": "reason", "$ref": "ClearReason", "description": "The reason the console is being cleared."}]}, {"name": "heapSnapshot", "description": "Issued from console.takeHeapSnapshot.", "parameters": [{"name": "timestamp", "type": "number"}, {"name": "snapshotData", "$ref": "Heap.<PERSON>napshotD<PERSON>", "description": "Snapshot at the end of tracking."}, {"name": "title", "type": "string", "optional": true, "description": "Optional title provided to console.takeHeapSnapshot."}]}]}, {"domain": "CPUProfiler", "description": "CPUProfiler domain exposes cpu usage tracking.", "condition": "defined(ENABLE_RESOURCE_USAGE) && ENABLE_RESOURCE_USAGE", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page"], "types": [{"id": "ThreadInfo", "description": "CPU usage for an individual thread.", "type": "object", "properties": [{"name": "name", "type": "string", "description": "Some thread identification information."}, {"name": "usage", "type": "number", "description": "CPU usage for this thread. This should not exceed 100% for an individual thread."}, {"name": "type", "type": "string", "enum": ["main", "webkit"], "optional": true, "description": "Type of thread. There should be a single main thread."}, {"name": "targetId", "type": "string", "optional": true, "description": "A thread may be associated with a target, such as a Worker, in the process."}]}, {"id": "Event", "type": "object", "properties": [{"name": "timestamp", "type": "number"}, {"name": "usage", "type": "number", "description": "Percent of total cpu usage. If there are multiple cores the usage may be greater than 100%."}, {"name": "threads", "type": "array", "items": {"$ref": "ThreadInfo"}, "optional": true, "description": "Per-thread CPU usage information. Does not include the main thread."}]}], "commands": [{"name": "startTracking", "description": "Start tracking cpu usage."}, {"name": "stopTracking", "description": "Stop tracking cpu usage. This will produce a `trackingComplete` event."}], "events": [{"name": "trackingStart", "description": "Tracking started.", "parameters": [{"name": "timestamp", "type": "number"}]}, {"name": "trackingUpdate", "description": "Periodic tracking updates with event data.", "parameters": [{"name": "event", "$ref": "Event"}]}, {"name": "trackingComplete", "description": "Tracking stopped.", "parameters": [{"name": "timestamp", "type": "number"}]}]}, {"domain": "CSS", "description": "This domain exposes CSS read/write operations. All CSS objects, like stylesheets, rules, and styles, have an associated <code>id</code> used in subsequent operations on the related object. Each object type has a specific <code>id</code> structure, and those are not interchangeable between objects of different kinds. CSS objects can be loaded using the <code>get*ForNode()</code> calls (which accept a DOM node id). Alternatively, a client can discover all the existing stylesheets with the <code>getAllStyleSheets()</code> method and subsequently load the required stylesheet contents using the <code>getStyleSheet[Text]()</code> methods.", "debuggableTypes": ["itml", "page", "web-page"], "targetTypes": ["itml", "page"], "types": [{"id": "StyleSheetId", "type": "string"}, {"id": "CSSStyleId", "type": "object", "description": "This object identifies a CSS style in a unique way.", "properties": [{"name": "styleSheetId", "$ref": "StyleSheetId", "description": "Enclosing stylesheet identifier."}, {"name": "ordinal", "type": "integer", "description": "The style ordinal within the stylesheet."}]}, {"id": "StyleSheetOrigin", "type": "string", "enum": ["user", "user-agent", "author", "inspector"], "description": "Stylesheet type: \"user\" for user stylesheets, \"user-agent\" for user-agent stylesheets, \"inspector\" for stylesheets created by the inspector (i.e. those holding the \"via inspector\" rules), \"regular\" for regular stylesheets."}, {"id": "CSSRuleId", "type": "object", "description": "This object identifies a CSS rule in a unique way.", "properties": [{"name": "styleSheetId", "$ref": "StyleSheetId", "description": "Enclosing stylesheet identifier."}, {"name": "ordinal", "type": "integer", "description": "The rule ordinal within the stylesheet."}]}, {"id": "PseudoId", "type": "string", "enum": ["first-line", "first-letter", "grammar-error", "highlight", "marker", "before", "after", "selection", "backdrop", "spelling-error", "target-text", "view-transition", "view-transition-group", "view-transition-image-pair", "view-transition-old", "view-transition-new", "-webkit-scrollbar", "-webkit-resizer", "-webkit-scrollbar-thumb", "-webkit-scrollbar-button", "-webkit-scrollbar-track", "-webkit-scrollbar-track-piece", "-webkit-scrollbar-corner"], "description": "Pseudo-style identifier (see <code>enum PseudoId</code> in <code>RenderStyleConstants.h</code>)."}, {"id": "ForceablePseudoClass", "type": "string", "enum": ["active", "focus", "focus-visible", "focus-within", "hover", "target", "visited"], "description": "Pseudo-style identifier (see <code>enum PseudoId</code> in <code>RenderStyleConstants.h</code>)."}, {"id": "PseudoIdMatches", "type": "object", "description": "CSS rule collection for a single pseudo style.", "properties": [{"name": "pseudoId", "$ref": "PseudoId"}, {"name": "matches", "type": "array", "items": {"$ref": "RuleMatch"}, "description": "Matches of CSS rules applicable to the pseudo style."}]}, {"id": "InheritedStyleEntry", "type": "object", "description": "CSS rule collection for a single pseudo style.", "properties": [{"name": "inlineStyle", "$ref": "CSSStyle", "optional": true, "description": "The ancestor node's inline style, if any, in the style inheritance chain."}, {"name": "matchedCSSRules", "type": "array", "items": {"$ref": "RuleMatch"}, "description": "Matches of CSS rules matching the ancestor node in the style inheritance chain."}]}, {"id": "RuleMatch", "type": "object", "description": "Match data for a CSS rule.", "properties": [{"name": "rule", "$ref": "CSSRule", "description": "CSS rule in the match."}, {"name": "matchingSelectors", "type": "array", "items": {"type": "integer"}, "description": "Matching selector indices in the rule's selectorList selectors (0-based)."}]}, {"id": "CSSSelector", "type": "object", "description": "CSS selector.", "properties": [{"name": "text", "type": "string", "description": "Canonicalized selector text."}, {"name": "specificity", "optional": true, "type": "array", "items": {"type": "integer"}, "description": "Specificity (a, b, c) tuple. Included if the selector is sent in response to CSS.getMatchedStylesForNode which provides a context element."}, {"name": "dynamic", "optional": true, "type": "boolean", "description": "Whether or not the specificity can be dynamic. Included if the selector is sent in response to CSS.getMatchedStylesForNode which provides a context element."}]}, {"id": "SelectorList", "type": "object", "description": "Selector list data.", "properties": [{"name": "selectors", "type": "array", "items": {"$ref": "CSSSelector"}, "description": "Selectors in the list."}, {"name": "text", "type": "string", "description": "Rule selector text."}, {"name": "range", "$ref": "SourceRange", "optional": true, "description": "Rule selector range in the underlying resource (if available)."}]}, {"id": "CSSStyleAttribute", "type": "object", "description": "CSS style information for a DOM style attribute.", "properties": [{"name": "name", "type": "string", "description": "DOM attribute name (e.g. \"width\")."}, {"name": "style", "$ref": "CSSStyle", "description": "CSS style generated by the respective DOM attribute."}]}, {"id": "CSSStyleSheetHeader", "type": "object", "description": "CSS stylesheet meta-information.", "properties": [{"name": "styleSheetId", "$ref": "StyleSheetId", "description": "The stylesheet identifier."}, {"name": "frameId", "$ref": "Network.FrameId", "description": "Owner frame identifier."}, {"name": "sourceURL", "type": "string", "description": "Stylesheet resource URL."}, {"name": "origin", "$ref": "StyleSheetOrigin", "description": "Stylesheet origin."}, {"name": "title", "type": "string", "description": "Stylesheet title."}, {"name": "disabled", "type": "boolean", "description": "Denotes whether the stylesheet is disabled."}, {"name": "isInline", "type": "boolean", "description": "Whether this stylesheet is a <style> tag created by the parser. This is not set for document.written <style> tags."}, {"name": "startLine", "type": "number", "description": "Line offset of the stylesheet within the resource (zero based)."}, {"name": "startColumn", "type": "number", "description": "Column offset of the stylesheet within the resource (zero based)."}]}, {"id": "CSSStyleSheetBody", "type": "object", "description": "CSS stylesheet contents.", "properties": [{"name": "styleSheetId", "$ref": "StyleSheetId", "description": "The stylesheet identifier."}, {"name": "rules", "type": "array", "items": {"$ref": "CSSRule"}, "description": "Stylesheet resource URL."}, {"name": "text", "type": "string", "optional": true, "description": "Stylesheet resource contents (if available)."}]}, {"id": "CSSRule", "type": "object", "description": "CSS rule representation.", "properties": [{"name": "ruleId", "$ref": "CSSRuleId", "optional": true, "description": "The CSS rule identifier (absent for user agent stylesheet and user-specified stylesheet rules)."}, {"name": "selectorList", "$ref": "SelectorList", "description": "Rule selector data."}, {"name": "sourceURL", "type": "string", "optional": true, "description": "Parent stylesheet resource URL (for regular rules)."}, {"name": "sourceLine", "type": "integer", "description": "Line ordinal of the rule selector start character in the resource."}, {"name": "origin", "$ref": "StyleSheetOrigin", "description": "Parent stylesheet's origin."}, {"name": "style", "$ref": "CSSStyle", "description": "Associated style declaration."}, {"name": "groupings", "type": "array", "items": {"$ref": "Grouping"}, "optional": true, "description": "Grouping list array (for rules involving @media/@supports). The array enumerates CSS groupings starting with the innermost one, going outwards."}, {"name": "isImplicitlyNested", "type": "boolean", "optional": true, "description": "<code>true</code> if this style is for a rule implicitly wrapping properties declared inside of CSSGrouping."}]}, {"id": "SourceRange", "type": "object", "description": "Text range within a resource.", "properties": [{"name": "startLine", "type": "integer", "description": "Start line of range."}, {"name": "startColumn", "type": "integer", "description": "Start column of range (inclusive)."}, {"name": "endLine", "type": "integer", "description": "End line of range"}, {"name": "endColumn", "type": "integer", "description": "End column of range (exclusive)."}]}, {"id": "ShorthandEntry", "type": "object", "properties": [{"name": "name", "type": "string", "description": "Shorthand name."}, {"name": "value", "type": "string", "description": "Shorthand value."}]}, {"id": "CSSPropertyInfo", "type": "object", "properties": [{"name": "name", "type": "string", "description": "Property name."}, {"name": "aliases", "type": "array", "optional": true, "items": {"type": "string"}, "description": "Other names for this property."}, {"name": "longhands", "type": "array", "optional": true, "items": {"type": "string"}, "description": "Longhand property names."}, {"name": "values", "type": "array", "optional": true, "items": {"type": "string"}, "description": "Supported values for this property."}, {"name": "inherited", "type": "boolean", "optional": true, "description": "Whether the property is able to be inherited."}]}, {"id": "CSSComputedStyleProperty", "type": "object", "properties": [{"name": "name", "type": "string", "description": "Computed style property name."}, {"name": "value", "type": "string", "description": "Computed style property value."}]}, {"id": "CSSStyle", "type": "object", "description": "CSS style representation.", "properties": [{"name": "styleId", "$ref": "CSSStyleId", "optional": true, "description": "The CSS style identifier (absent for attribute styles)."}, {"name": "cssProperties", "type": "array", "items": {"$ref": "CSSProperty"}, "description": "CSS properties in the style."}, {"name": "shorthandEntries", "type": "array", "items": {"$ref": "ShorthandEntry"}, "description": "Computed values for all shorthands found in the style."}, {"name": "cssText", "type": "string", "optional": true, "description": "Style declaration text (if available)."}, {"name": "range", "$ref": "SourceRange", "optional": true, "description": "Style declaration range in the enclosing stylesheet (if available)."}, {"name": "width", "type": "string", "optional": true, "description": "The effective \"width\" property value from this style."}, {"name": "height", "type": "string", "optional": true, "description": "The effective \"height\" property value from this style."}]}, {"id": "CSSPropertyStatus", "type": "string", "enum": ["active", "inactive", "disabled", "style"], "description": "The property status: \"active\" if the property is effective in the style, \"inactive\" if the property is overridden by a same-named property in this style later on, \"disabled\" if the property is disabled by the user, \"style\" (implied if absent) if the property is reported by the browser rather than by the CSS source parser."}, {"id": "CSSProperty", "type": "object", "description": "CSS style effective visual dimensions and source offsets.", "properties": [{"name": "name", "type": "string", "description": "The property name."}, {"name": "value", "type": "string", "description": "The property value."}, {"name": "priority", "type": "string", "optional": true, "description": "The property priority (implies \"\" if absent)."}, {"name": "implicit", "type": "boolean", "optional": true, "description": "Whether the property is implicit (implies <code>false</code> if absent)."}, {"name": "text", "type": "string", "optional": true, "description": "The full property text as specified in the style."}, {"name": "parsedOk", "type": "boolean", "optional": true, "description": "Whether the property is understood by the browser (implies <code>true</code> if absent)."}, {"name": "status", "$ref": "CSSPropertyStatus", "optional": true, "description": "Whether the property is active or disabled."}, {"name": "range", "$ref": "SourceRange", "optional": true, "description": "The entire property range in the enclosing style declaration (if available)."}]}, {"id": "Grouping", "type": "object", "description": "CSS @media (as well as other users of media queries, like @import, <style>, <link>, etc.), @supports, and @layer descriptor.", "properties": [{"name": "type", "type": "string", "enum": ["media-rule", "media-import-rule", "media-link-node", "media-style-node", "supports-rule", "layer-rule", "layer-import-rule", "container-rule", "style-rule"], "description": "Source of the media query: \"media-rule\" if specified by a @media rule, \"media-import-rule\" if specified by an @import rule, \"media-link-node\" if specified by a \"media\" attribute in a linked style sheet's LINK tag, \"media-style-node\" if specified by a \"media\" attribute in an inline style sheet's STYLE tag, \"supports-rule\" if specified by an @supports rule, \"layer-rule\" if specified by an @layer rule, \"container-rule\" if specified by an @container rule, \"style-rule\" if specified by a CSSStyleRule containing the rule inside this grouping."}, {"name": "ruleId", "$ref": "CSSRuleId", "optional": true, "description": "The CSS rule identifier for the `@rule` (absent for non-editable grouping rules) or the nesting parent style rule's selector. In CSSOM terms, this is the parent rule of either the previous Grouping for a CSSRule, or of a CSSRule itself."}, {"name": "text", "type": "string", "optional": true, "description": "Query text if specified by a @media, @supports, or @container rule. Layer name (or not present for anonymous layers) for @layer rules."}, {"name": "sourceURL", "type": "string", "optional": true, "description": "URL of the document containing the CSS grouping."}, {"name": "range", "$ref": "SourceRange", "optional": true, "description": "@-rule's header text range in the enclosing stylesheet (if available). This is from the first non-whitespace character after the @ declarartion to the last non-whitespace character before an opening curly bracket or semicolon."}]}, {"id": "Font", "type": "object", "description": "A representation of WebCore::Font. Conceptually this is backed by either a font file on disk or from the network.", "properties": [{"name": "displayName", "type": "string", "description": "The display name defined by the font."}, {"name": "variationAxes", "type": "array", "items": {"$ref": "FontVariationAxis"}, "description": "The variation axes defined by the font."}, {"name": "synthesizedBold", "type": "boolean", "optional": true, "description": "Whether the font has synthesized its boldness or not."}, {"name": "synthesizedOblique", "type": "boolean", "optional": true, "description": "Whether the font has synthesized its obliqueness or not"}]}, {"id": "FontVariationAxis", "type": "object", "description": "A single variation axis associated with a Font.", "properties": [{"name": "name", "type": "string", "optional": true, "description": "The name, generally human-readable, of the variation axis. Some axes may not provide a human-readable name distiguishable from the tag. This field is ommited when there is no name, or the name matches the tag exactly."}, {"name": "tag", "type": "string", "description": "The four character tag for the variation axis."}, {"name": "minimumValue", "type": "number", "description": "The minimum value that will affect the axis."}, {"name": "maximumValue", "type": "number", "description": "The maximum value that will affect the axis."}, {"name": "defaultValue", "type": "number", "description": "The value that is used for the axis when it is not otherwise controlled."}]}, {"id": "LayoutFlag", "type": "string", "enum": ["rendered", "scrollable", "flex", "grid", "event"], "description": "Relevant layout information about the node. Things not in this list are not important to Web Inspector."}, {"id": "LayoutContextTypeChangedMode", "type": "string", "enum": ["observed", "all"], "description": "The mode for how layout context type changes are handled (default: <code>Observed</code>). <code>Observed</code> limits handling to those nodes already known to the frontend by other means (generally, this means the node is a visible item in the Elements tab). <code>All</code> informs the frontend of all layout context type changes and all nodes with a known layout context are sent to the frontend."}], "commands": [{"name": "enable", "description": "Enables the CSS agent for the given page. Clients should not assume that the CSS agent has been enabled until the result of this command is received."}, {"name": "disable", "description": "Disables the CSS agent for the given page."}, {"name": "getMatchedStylesForNode", "description": "Returns requested styles for a DOM node identified by <code>nodeId</code>.", "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId"}, {"name": "includePseudo", "type": "boolean", "optional": true, "description": "Whether to include pseudo styles (default: true)."}, {"name": "includeInherited", "type": "boolean", "optional": true, "description": "Whether to include inherited styles (default: true)."}], "returns": [{"name": "matchedCSSRules", "type": "array", "items": {"$ref": "RuleMatch"}, "optional": true, "description": "CSS rules matching this node, from all applicable stylesheets."}, {"name": "pseudoElements", "type": "array", "items": {"$ref": "PseudoIdMatches"}, "optional": true, "description": "Pseudo style matches for this node."}, {"name": "inherited", "type": "array", "items": {"$ref": "InheritedStyleEntry"}, "optional": true, "description": "A chain of inherited styles (from the immediate node parent up to the DOM tree root)."}]}, {"name": "getInlineStylesForNode", "description": "Returns the styles defined inline (explicitly in the \"style\" attribute and implicitly, using DOM attributes) for a DOM node identified by <code>nodeId</code>.", "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId"}], "returns": [{"name": "inlineStyle", "$ref": "CSSStyle", "optional": true, "description": "Inline style for the specified DOM node."}, {"name": "attributesStyle", "$ref": "CSSStyle", "optional": true, "description": "Attribute-defined element style (e.g. resulting from \"width=20 height=100%\")."}]}, {"name": "getComputedStyleForNode", "description": "Returns the computed style for a DOM node identified by <code>nodeId</code>.", "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId"}], "returns": [{"name": "computedStyle", "type": "array", "items": {"$ref": "CSSComputedStyleProperty"}, "description": "Computed style for the specified DOM node."}]}, {"name": "getFontDataForNode", "description": "Returns the primary font of the computed font cascade for a DOM node identified by <code>nodeId</code>.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId"}], "returns": [{"name": "primaryFont", "$ref": "Font", "description": "Computed primary font for the specified DOM node."}]}, {"name": "getAllStyleSheets", "description": "Returns metainfo entries for all known stylesheets.", "returns": [{"name": "headers", "type": "array", "items": {"$ref": "CSSStyleSheetHeader"}, "description": "Descriptor entries for all available stylesheets."}]}, {"name": "getStyleSheet", "description": "Returns stylesheet data for the specified <code>styleSheetId</code>.", "parameters": [{"name": "styleSheetId", "$ref": "StyleSheetId"}], "returns": [{"name": "styleSheet", "$ref": "CSSStyleSheetBody", "description": "Stylesheet contents for the specified <code>styleSheetId</code>."}]}, {"name": "getStyleSheetText", "description": "Returns the current textual content and the URL for a stylesheet.", "parameters": [{"name": "styleSheetId", "$ref": "StyleSheetId"}], "returns": [{"name": "text", "type": "string", "description": "The stylesheet text."}]}, {"name": "setStyleSheetText", "description": "Sets the new stylesheet text, thereby invalidating all existing <code>CSSStyleId</code>'s and <code>CSSRuleId</code>'s contained by this stylesheet.", "parameters": [{"name": "styleSheetId", "$ref": "StyleSheetId"}, {"name": "text", "type": "string"}]}, {"name": "setStyleText", "description": "Sets the new <code>text</code> for the respective style.", "parameters": [{"name": "styleId", "$ref": "CSSStyleId"}, {"name": "text", "type": "string"}], "returns": [{"name": "style", "$ref": "CSSStyle", "description": "The resulting style after the text modification."}]}, {"name": "setRuleSelector", "description": "Modifies the rule selector.", "targetTypes": ["page"], "parameters": [{"name": "ruleId", "$ref": "CSSRuleId"}, {"name": "selector", "type": "string"}], "returns": [{"name": "rule", "$ref": "CSSRule", "description": "The resulting rule after the selector modification."}]}, {"name": "setGroupingHeaderText", "description": "Modifies an @rule grouping's header text.", "targetTypes": ["page"], "parameters": [{"name": "ruleId", "$ref": "CSSRuleId"}, {"name": "headerText", "type": "string"}], "returns": [{"name": "grouping", "$ref": "Grouping", "description": "The resulting grouping after the header text modification."}]}, {"name": "createStyleSheet", "description": "Creates a new special \"inspector\" stylesheet in the frame with given <code>frameId</code>.", "targetTypes": ["page"], "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Identifier of the frame where the new \"inspector\" stylesheet should be created."}], "returns": [{"name": "styleSheetId", "$ref": "StyleSheetId", "description": "Identifier of the created \"inspector\" stylesheet."}]}, {"name": "addRule", "description": "Creates a new empty rule with the given <code>selector</code> in a stylesheet with given <code>styleSheetId</code>.", "targetTypes": ["page"], "parameters": [{"name": "styleSheetId", "$ref": "StyleSheetId"}, {"name": "selector", "type": "string"}], "returns": [{"name": "rule", "$ref": "CSSRule", "description": "The newly created rule."}]}, {"name": "getSupportedCSSProperties", "description": "Returns all supported CSS property names.", "returns": [{"name": "cssProperties", "type": "array", "items": {"$ref": "CSSPropertyInfo"}, "description": "Supported property metainfo."}]}, {"name": "getSupportedSystemFontFamilyNames", "description": "Returns all supported system font family names.", "targetTypes": ["page"], "returns": [{"name": "fontFamilyNames", "type": "array", "items": {"type": "string"}, "description": "Supported system font families."}]}, {"name": "forcePseudoState", "description": "Ensures that the given node will have specified pseudo-classes whenever its style is computed by the browser.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId", "description": "The element id for which to force the pseudo state."}, {"name": "forcedPseudoClasses", "type": "array", "items": {"$ref": "ForceablePseudoClass"}, "description": "Element pseudo classes to force when computing the element's style."}]}, {"name": "setLayoutContextTypeChangedMode", "description": "Change how layout context type changes are handled for nodes. When the new mode would observe nodes the frontend has not yet recieved, those nodes will be sent to the frontend immediately.", "targetTypes": ["page"], "parameters": [{"name": "mode", "$ref": "LayoutContextTypeChangedMode", "description": "The mode for how layout context type changes are handled."}]}], "events": [{"name": "mediaQueryResultChanged", "description": "Fires whenever a MediaQuery result changes (for example, after a browser window has been resized.) The current implementation considers only viewport-dependent media features."}, {"name": "styleSheetChanged", "description": "Fired whenever a stylesheet is changed as a result of the client operation.", "parameters": [{"name": "styleSheetId", "$ref": "StyleSheetId"}]}, {"name": "styleSheetAdded", "description": "Fired whenever an active document stylesheet is added.", "targetTypes": ["page"], "parameters": [{"name": "header", "$ref": "CSSStyleSheetHeader", "description": "Added stylesheet metainfo."}]}, {"name": "styleSheetRemoved", "description": "Fired whenever an active document stylesheet is removed.", "targetTypes": ["page"], "parameters": [{"name": "styleSheetId", "$ref": "StyleSheetId", "description": "Identifier of the removed stylesheet."}]}, {"name": "nodeLayoutFlagsChanged", "description": "Called when the layout of a node changes in a way that is important to Web Inspector.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId", "description": "Identifier of the node whose layout changed."}, {"name": "layoutFlags", "type": "array", "items": {"type": "string", "description": "CSS.LayoutFlag"}, "optional": true, "description": "Relevant information about the layout of the node. When not provided, the layout of the node is not important to Web Inspector."}]}]}, {"domain": "Debugger", "description": "Debugger domain exposes JavaScript debugging capabilities. It allows setting and removing breakpoints, stepping through execution, exploring stack traces, etc.", "debuggableTypes": ["itml", "javascript", "page", "service-worker", "web-page"], "targetTypes": ["itml", "javascript", "page", "service-worker", "worker"], "types": [{"id": "BreakpointId", "type": "string", "description": "Breakpoint identifier."}, {"id": "BreakpointActionIdentifier", "type": "integer", "description": "Breakpoint action identifier."}, {"id": "ScriptId", "type": "string", "description": "Unique script identifier."}, {"id": "CallFrameId", "type": "string", "description": "Call frame identifier."}, {"id": "Location", "type": "object", "description": "Location in the source code.", "properties": [{"name": "scriptId", "$ref": "ScriptId", "description": "Script identifier as reported in the <code>Debugger.scriptParsed</code>."}, {"name": "lineNumber", "type": "integer", "description": "Line number in the script (0-based)."}, {"name": "columnNumber", "type": "integer", "optional": true, "description": "Column number in the script (0-based)."}]}, {"id": "BreakpointAction", "type": "object", "description": "Action to perform when a breakpoint is triggered.", "properties": [{"name": "type", "type": "string", "enum": ["log", "evaluate", "sound", "probe"], "description": "Different kinds of breakpoint actions."}, {"name": "data", "type": "string", "optional": true, "description": "Data associated with this breakpoint type (e.g. for type \"eval\" this is the JavaScript string to evaluate)."}, {"name": "id", "$ref": "BreakpointActionIdentifier", "optional": true, "description": "A frontend-assigned identifier for this breakpoint action."}, {"name": "emulateUserGesture", "type": "boolean", "optional": true, "description": "Indicates whether this action should be executed with a user gesture or not. Defaults to <code>false<code>."}]}, {"id": "BreakpointOptions", "type": "object", "description": "Extra options that modify breakpoint behavior.", "properties": [{"name": "condition", "type": "string", "optional": true, "description": "Expression to use as a breakpoint condition. When specified, debugger will only stop on the breakpoint if this expression evaluates to true."}, {"name": "actions", "type": "array", "optional": true, "items": {"$ref": "BreakpointAction"}, "description": "Actions to perform automatically when the breakpoint is triggered."}, {"name": "autoContinue", "type": "boolean", "optional": true, "description": "Automatically continue after hitting this breakpoint and running actions."}, {"name": "ignoreCount", "type": "integer", "optional": true, "description": "Number of times to ignore this breakpoint, before stopping on the breakpoint and running actions."}]}, {"id": "FunctionDetails", "type": "object", "description": "Information about the function.", "properties": [{"name": "location", "$ref": "Location", "description": "Location of the function."}, {"name": "name", "type": "string", "optional": true, "description": "Name of the function. Not present for anonymous functions."}, {"name": "displayName", "type": "string", "optional": true, "description": "Display name of the function(specified in 'displayName' property on the function object)."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "array", "optional": true, "items": {"$ref": "<PERSON><PERSON>"}, "description": "Scope chain for this closure."}]}, {"id": "CallFrame", "type": "object", "description": "JavaScript call frame. Array of call frames form the call stack.", "properties": [{"name": "callFrameId", "$ref": "CallFrameId", "description": "Call frame identifier. This identifier is only valid while the virtual machine is paused."}, {"name": "functionName", "type": "string", "description": "Name of the JavaScript function called on this call frame."}, {"name": "location", "$ref": "Location", "description": "Location in the source code."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "array", "items": {"$ref": "<PERSON><PERSON>"}, "description": "Scope chain for this call frame."}, {"name": "this", "$ref": "Runtime.RemoteObject", "description": "<code>this</code> object for this call frame."}, {"name": "isTailDeleted", "type": "boolean", "description": "Is the current frame tail deleted from a tail call."}]}, {"id": "<PERSON><PERSON>", "type": "object", "description": "Scope description.", "properties": [{"name": "object", "$ref": "Runtime.RemoteObject", "description": "Object representing the scope. For <code>global</code> and <code>with</code> scopes it represents the actual object; for the rest of the scopes, it is artificial transient object enumerating scope variables as its properties."}, {"name": "type", "type": "string", "enum": ["global", "with", "closure", "catch", "functionName", "globalLexicalEnvironment", "nestedLexical"], "description": "Scope type."}, {"name": "name", "type": "string", "optional": true, "description": "Name associated with the scope."}, {"name": "location", "$ref": "Location", "optional": true, "description": "Location if available of the scope definition."}, {"name": "empty", "type": "boolean", "optional": true, "description": "Whether the scope has any variables."}]}, {"id": "ProbeSample", "description": "A sample collected by evaluating a probe breakpoint action.", "type": "object", "properties": [{"name": "probeId", "$ref": "BreakpointActionIdentifier", "description": "Identifier of the probe breakpoint action that created the sample."}, {"name": "sampleId", "type": "integer", "description": "Unique identifier for this sample."}, {"name": "batchId", "type": "integer", "description": "A batch identifier which is the same for all samples taken at the same breakpoint hit."}, {"name": "timestamp", "type": "number", "description": "Timestamp of when the sample was taken."}, {"name": "payload", "$ref": "Runtime.RemoteObject", "description": "Contents of the sample."}]}, {"id": "AssertPauseReason", "type": "object", "description": "The pause reason auxiliary data when paused because of an assertion.", "properties": [{"name": "message", "type": "string", "optional": true, "description": "The console.assert message string if provided."}]}, {"id": "BreakpointPauseReason", "type": "object", "description": "The pause reason auxiliary data when paused because of hitting a breakpoint.", "properties": [{"name": "breakpointId", "type": "string", "description": "The identifier of the breakpoint causing the pause."}]}, {"id": "CSPViolationPauseReason", "type": "object", "description": "The pause reason auxiliary data when paused because of a Content Security Policy directive.", "properties": [{"name": "directive", "type": "string", "description": "The CSP directive that blocked script execution."}]}], "commands": [{"name": "enable", "description": "Enables debugger for the given page. Clients should not assume that the debugging has been enabled until the result for this command is received."}, {"name": "disable", "description": "Disables debugger for given page."}, {"name": "setAsyncStackTraceDepth", "description": "Set the async stack trace depth for the page. A value of zero disables recording of async stack traces.", "parameters": [{"name": "depth", "type": "integer", "description": "Async stack trace depth."}]}, {"name": "setBreakpointsActive", "description": "Activates / deactivates all breakpoints on the page.", "parameters": [{"name": "active", "type": "boolean", "description": "New value for breakpoints active state."}]}, {"name": "setBreakpointByUrl", "description": "Sets JavaScript breakpoint at given location specified either by URL or URL regex. Once this command is issued, all existing parsed scripts will have breakpoints resolved and returned in <code>locations</code> property. Further matching script parsing will result in subsequent <code>breakpointResolved</code> events issued. This logical breakpoint will survive page reloads.", "parameters": [{"name": "lineNumber", "type": "integer", "description": "Line number to set breakpoint at."}, {"name": "url", "type": "string", "optional": true, "description": "URL of the resources to set breakpoint on."}, {"name": "urlRegex", "type": "string", "optional": true, "description": "Regex pattern for the URLs of the resources to set breakpoints on. Either <code>url</code> or <code>urlRegex</code> must be specified."}, {"name": "columnNumber", "type": "integer", "optional": true, "description": "Offset in the line to set breakpoint at."}, {"name": "options", "$ref": "BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}], "returns": [{"name": "breakpointId", "$ref": "BreakpointId", "description": "Id of the created breakpoint for further reference."}, {"name": "locations", "type": "array", "items": {"$ref": "Location"}, "description": "List of the locations this breakpoint resolved into upon addition."}]}, {"name": "setBreakpoint", "description": "Sets JavaScript breakpoint at a given location.", "parameters": [{"name": "location", "$ref": "Location", "description": "Location to set breakpoint in."}, {"name": "options", "$ref": "BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}], "returns": [{"name": "breakpointId", "$ref": "BreakpointId", "description": "Id of the created breakpoint for further reference."}, {"name": "actualLocation", "$ref": "Location", "description": "Location this breakpoint resolved into."}]}, {"name": "removeBreakpoint", "description": "Removes JavaScript breakpoint.", "parameters": [{"name": "breakpointId", "$ref": "BreakpointId"}]}, {"name": "addSymbolicBreakpoint", "description": "Adds a JavaScript breakpoint that pauses execution whenever a function with the given name is about to be called.", "parameters": [{"name": "symbol", "type": "string", "description": "The name of the function to pause in when called."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, symbol is case sensitive. Defaults to true."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats symbol as a regex. Defaults to false."}, {"name": "options", "$ref": "BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "removeSymbolicBreakpoint", "description": "Removes a previously added symbolic breakpoint.", "parameters": [{"name": "symbol", "type": "string", "description": "The name of the function to pause in when called."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, symbol is case sensitive. Defaults to true."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats symbol as a regex. Defaults to false."}]}, {"name": "continueUntilNextRunLoop", "description": "Continues execution until the current evaluation completes. This will trigger either a Debugger.paused or Debugger.resumed event."}, {"name": "continueToLocation", "description": "Continues execution until specific location is reached. This will trigger either a Debugger.paused or Debugger.resumed event.", "parameters": [{"name": "location", "$ref": "Location", "description": "Location to continue to."}]}, {"name": "stepNext", "description": "Steps over the expression. This will trigger either a Debugger.paused or Debugger.resumed event."}, {"name": "stepOver", "description": "Steps over the statement. This will trigger either a Debugger.paused or Debugger.resumed event."}, {"name": "stepI<PERSON>", "description": "Steps into the function call. This will trigger either a Debugger.paused or Debugger.resumed event."}, {"name": "stepOut", "description": "Steps out of the function call. This will trigger either a Debugger.paused or Debugger.resumed event."}, {"name": "pause", "description": "Stops on the next JavaScript statement."}, {"name": "resume", "description": "Resumes JavaScript execution. This will trigger a Debugger.resumed event."}, {"name": "searchInContent", "description": "Searches for given string in script content.", "parameters": [{"name": "scriptId", "$ref": "ScriptId", "description": "Id of the script to search in."}, {"name": "query", "type": "string", "description": "String to search for."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, search is case sensitive."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats string parameter as regex."}], "returns": [{"name": "result", "type": "array", "items": {"$ref": "GenericTypes.SearchMatch"}, "description": "List of search matches."}]}, {"name": "getScriptSource", "description": "Returns source for the script with given id.", "parameters": [{"name": "scriptId", "$ref": "ScriptId", "description": "Id of the script to get source for."}], "returns": [{"name": "scriptSource", "type": "string", "description": "Script source."}]}, {"name": "getFunctionDetails", "description": "Returns detailed information on given function.", "parameters": [{"name": "functionId", "$ref": "Runtime.RemoteObjectId", "description": "Id of the function to get location for."}], "returns": [{"name": "details", "$ref": "FunctionDetails", "description": "Information about the function."}]}, {"name": "getBreakpointLocations", "description": "Returns a list of valid breakpoint locations within the given location range.", "parameters": [{"name": "start", "$ref": "Location", "description": "Starting location to look for breakpoint locations after (inclusive). Must have same scriptId as end."}, {"name": "end", "$ref": "Location", "description": "Ending location to look for breakpoint locations before (exclusive). Must have same scriptId as start."}], "returns": [{"name": "locations", "type": "array", "items": {"$ref": "Location"}, "description": "List of resolved breakpoint locations."}]}, {"name": "setPauseOnDebuggerStatements", "description": "Control whether the debugger pauses execution before `debugger` statements.", "parameters": [{"name": "enabled", "type": "boolean"}, {"name": "options", "$ref": "BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "setPauseOnExceptions", "description": "Defines pause on exceptions state. Can be set to stop on all exceptions, uncaught exceptions or no exceptions. Initial pause on exceptions state is <code>none</code>.", "parameters": [{"name": "state", "type": "string", "enum": ["none", "uncaught", "all"], "description": "Pause on exceptions mode."}, {"name": "options", "$ref": "BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "setPauseOnAssertions", "description": "Set pause on assertions state. Assertions are console.assert assertions.", "parameters": [{"name": "enabled", "type": "boolean"}, {"name": "options", "$ref": "BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "setPauseOnMicrotasks", "description": "Pause when running the next JavaScript microtask.", "parameters": [{"name": "enabled", "type": "boolean"}, {"name": "options", "$ref": "BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "setPauseForInternalScripts", "description": "Change whether to pause in the debugger for internal scripts. The default value is false.", "parameters": [{"name": "shouldP<PERSON>e", "type": "boolean"}]}, {"name": "evaluateOnCallFrame", "description": "Evaluates expression on a given call frame.", "parameters": [{"name": "callFrameId", "$ref": "CallFrameId", "description": "Call frame identifier to evaluate on."}, {"name": "expression", "type": "string", "description": "Expression to evaluate."}, {"name": "objectGroup", "type": "string", "optional": true, "description": "String object group name to put result into (allows rapid releasing resulting object handles using <code>releaseObjectGroup</code>)."}, {"name": "includeCommandLineAPI", "type": "boolean", "optional": true, "description": "Specifies whether command line API should be available to the evaluated expression, defaults to false."}, {"name": "doNotPauseOnExceptionsAndMuteConsole", "type": "boolean", "optional": true, "description": "Specifies whether evaluation should stop on exceptions and mute console. Overrides setPauseOnException state."}, {"name": "returnByValue", "type": "boolean", "optional": true, "description": "Whether the result is expected to be a JSON object that should be sent by value."}, {"name": "generatePreview", "type": "boolean", "optional": true, "description": "Whether preview should be generated for the result."}, {"name": "saveResult", "type": "boolean", "optional": true, "description": "Whether the resulting value should be considered for saving in the $n history."}, {"name": "emulateUserGesture", "type": "boolean", "optional": true, "description": "Whether the expression should be considered to be in a user gesture or not."}], "returns": [{"name": "result", "$ref": "Runtime.RemoteObject", "description": "Object wrapper for the evaluation result."}, {"name": "wasThrown", "type": "boolean", "optional": true, "description": "True if the result was thrown during the evaluation."}, {"name": "savedResultIndex", "type": "integer", "optional": true, "description": "If the result was saved, this is the $n index that can be used to access the value."}]}, {"name": "setShouldBlackboxURL", "description": "Sets whether the given URL should be in the list of blackboxed scripts, which are ignored when pausing.", "parameters": [{"name": "url", "type": "string"}, {"name": "shouldBlackbox", "type": "boolean"}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If <code>true</code>, <code>url</code> is case sensitive."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If <code>true</code>, treat <code>url</code> as regular expression."}, {"name": "sourceRanges", "type": "array", "items": {"type": "integer"}, "optional": true, "description": "If provided, limits where in the script the debugger will skip pauses. Expected structure is a repeated <code>[startLine, startColumn, endLine, endColumn]</code>. Ignored if <code>shouldBlackbox</code> is <code>false</code>."}]}, {"name": "setBlackboxBreakpointEvaluations", "description": "Sets whether evaluation of breakpoint conditions, ignore counts, and actions happen at the location of the breakpoint or are deferred due to blackboxing.", "parameters": [{"name": "blackboxBreakpointEvaluations", "type": "boolean"}]}], "events": [{"name": "globalObjectCleared", "description": "Called when global has been cleared and debugger client should reset its state. Happens upon navigation or reload."}, {"name": "scriptParsed", "description": "Fired when virtual machine parses script. This event is also fired for all known and uncollected scripts upon enabling debugger.", "parameters": [{"name": "scriptId", "$ref": "ScriptId", "description": "Identifier of the script parsed."}, {"name": "url", "type": "string", "description": "URL of the script parsed (if any)."}, {"name": "startLine", "type": "integer", "description": "Line offset of the script within the resource with given URL (for script tags)."}, {"name": "startColumn", "type": "integer", "description": "Column offset of the script within the resource with given URL."}, {"name": "endLine", "type": "integer", "description": "Last line of the script."}, {"name": "endColumn", "type": "integer", "description": "Length of the last line of the script."}, {"name": "isContentScript", "type": "boolean", "optional": true, "description": "Determines whether this script is a user extension script."}, {"name": "sourceURL", "type": "string", "optional": true, "description": "sourceURL name of the script (if any)."}, {"name": "sourceMapURL", "type": "string", "optional": true, "description": "URL of source map associated with script (if any)."}, {"name": "module", "type": "boolean", "optional": true, "description": "True if this script was parsed as a module."}]}, {"name": "scriptFailedToParse", "description": "Fired when virtual machine fails to parse the script.", "parameters": [{"name": "url", "type": "string", "description": "URL of the script that failed to parse."}, {"name": "scriptSource", "type": "string", "description": "Source text of the script that failed to parse."}, {"name": "startLine", "type": "integer", "description": "Line offset of the script within the resource."}, {"name": "errorLine", "type": "integer", "description": "Line with error."}, {"name": "errorMessage", "type": "string", "description": "Parse error message."}]}, {"name": "breakpointResolved", "description": "Fired when breakpoint is resolved to an actual script and location.", "parameters": [{"name": "breakpointId", "$ref": "BreakpointId", "description": "Breakpoint unique identifier."}, {"name": "location", "$ref": "Location", "description": "Actual breakpoint location."}]}, {"name": "paused", "description": "Fired when the virtual machine stopped on breakpoint or exception or any other stop criteria.", "parameters": [{"name": "callFrames", "type": "array", "items": {"$ref": "CallFrame"}, "description": "Call stack the virtual machine stopped on."}, {"name": "reason", "type": "string", "enum": ["URL", "DOM", "AnimationFrame", "Interval", "Listener", "Timeout", "exception", "assert", "CSPViolation", "DebuggerStatement", "Breakpoint", "PauseOnNextStatement", "Microtask", "FunctionCall", "BlackboxedScript", "other"], "description": "Pause reason."}, {"name": "data", "type": "object", "optional": true, "description": "Object containing break-specific auxiliary properties."}, {"name": "asyncStackTrace", "$ref": "Console.StackTrace", "optional": true, "description": "Linked list of asynchronous StackTraces."}]}, {"name": "resumed", "description": "Fired when the virtual machine resumed execution."}, {"name": "didSampleProbe", "description": "Fires when a new probe sample is collected.", "parameters": [{"name": "sample", "$ref": "ProbeSample", "description": "A collected probe sample."}]}, {"name": "playBreakpointActionSound", "description": "Fired when a \"sound\" breakpoint action is triggered on a breakpoint.", "parameters": [{"name": "breakpointActionId", "$ref": "BreakpointActionIdentifier", "description": "Breakpoint action identifier."}]}]}, {"domain": "Dialog", "description": "Actions and events related to alert boxes.", "availability": ["web"], "types": [], "commands": [{"name": "enable", "description": "Enables dialog domain notifications."}, {"name": "disable", "description": "Disables dialog domain notifications."}, {"name": "handleJavaScriptDialog", "description": "Accepts or dismisses a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload).", "parameters": [{"name": "accept", "type": "boolean", "description": "Whether to accept or dismiss the dialog."}, {"name": "promptText", "optional": true, "type": "string", "description": "The text to enter into the dialog prompt before accepting. Used only if this is a prompt dialog."}]}], "events": [{"name": "javascriptDialogOpening", "description": "Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) is about to open.", "parameters": [{"name": "type", "type": "string", "description": "Dialog type."}, {"name": "message", "type": "string", "description": "Message that will be displayed by the dialog."}, {"name": "defaultPrompt", "optional": true, "type": "string", "description": "Default dialog prompt."}]}]}, {"domain": "DOM", "description": "This domain exposes DOM read/write operations. Each DOM Node is represented with its mirror object that has an <code>id</code>. This <code>id</code> can be used to get additional information on the Node, resolve it into the JavaScript object wrapper, etc. It is important that client receives DOM events only for the nodes that are known to the client. Backend keeps track of the nodes that were sent to the client and never sends the same node twice. It is client's responsibility to collect information about the nodes that were sent to the client.<p>Note that <code>iframe</code> owner elements will return corresponding document elements as their child nodes.</p>", "debuggableTypes": ["itml", "page", "web-page"], "targetTypes": ["itml", "page"], "types": [{"id": "NodeId", "type": "integer", "description": "Unique DOM node identifier."}, {"id": "EventListenerId", "type": "integer", "description": "Unique event listener identifier."}, {"id": "PseudoType", "type": "string", "enum": ["before", "after"], "description": "Pseudo element type."}, {"id": "ShadowRootType", "type": "string", "enum": ["user-agent", "open", "closed"], "description": "Shadow root type."}, {"id": "CustomElementState", "type": "string", "enum": ["builtin", "custom", "waiting", "failed"], "description": "Custom element state."}, {"id": "LiveRegionRelevant", "type": "string", "enum": ["additions", "removals", "text"], "description": "Token values of @aria-relevant attribute."}, {"id": "Node", "type": "object", "description": "DOM interaction is implemented in terms of mirror objects that represent the actual DOM nodes. DOMNode is a base node mirror type.", "properties": [{"name": "nodeId", "$ref": "NodeId", "description": "Node identifier that is passed into the rest of the DOM messages as the <code>nodeId</code>. Backend will only push node with given <code>id</code> once. It is aware of all requested nodes and will only fire DOM events for nodes known to the client."}, {"name": "nodeType", "type": "integer", "description": "<code>Node</code>'s nodeType."}, {"name": "nodeName", "type": "string", "description": "<code>Node</code>'s nodeName."}, {"name": "localName", "type": "string", "description": "<code>Node</code>'s localName."}, {"name": "nodeValue", "type": "string", "description": "<code>Node</code>'s nodeValue."}, {"name": "frameId", "$ref": "Network.FrameId", "optional": true, "description": "Identifier of the containing frame."}, {"name": "childNodeCount", "type": "integer", "optional": true, "description": "Child count for <code>Container</code> nodes."}, {"name": "children", "type": "array", "optional": true, "items": {"$ref": "Node"}, "description": "Child nodes of this node when requested with children."}, {"name": "attributes", "type": "array", "optional": true, "items": {"type": "string"}, "description": "Attributes of the <code>Element</code> node in the form of flat array <code>[name1, value1, name2, value2]</code>."}, {"name": "documentURL", "type": "string", "optional": true, "description": "Document URL that <code>Document</code> or <code>FrameOwner</code> node points to."}, {"name": "baseURL", "type": "string", "optional": true, "description": "Base URL that <code>Document</code> or <code>FrameOwner</code> node uses for URL completion."}, {"name": "publicId", "type": "string", "optional": true, "description": "<code>DocumentType</code>'s publicId."}, {"name": "systemId", "type": "string", "optional": true, "description": "<code>DocumentType</code>'s systemId."}, {"name": "xmlVersion", "type": "string", "optional": true, "description": "<code>Document</code>'s XML version in case of XML documents."}, {"name": "name", "type": "string", "optional": true, "description": "<code>Attr</code>'s name."}, {"name": "value", "type": "string", "optional": true, "description": "<code>Attr</code>'s value."}, {"name": "pseudoType", "$ref": "PseudoType", "optional": true, "description": "Pseudo element type for this node."}, {"name": "shadowRootType", "$ref": "ShadowRootType", "optional": true, "description": "Shadow root type."}, {"name": "customElementState", "$ref": "CustomElementState", "optional": true, "description": "Custom element state."}, {"name": "contentDocument", "$ref": "Node", "optional": true, "description": "Content document for frame owner elements."}, {"name": "shadowRoots", "type": "array", "optional": true, "items": {"$ref": "Node"}, "description": "Shadow root list for given element host."}, {"name": "templateContent", "$ref": "Node", "optional": true, "description": "Content document fragment for template elements"}, {"name": "pseudoElements", "type": "array", "items": {"$ref": "Node"}, "optional": true, "description": "Pseudo elements associated with this node."}, {"name": "contentSecurityPolicyHash", "type": "string", "optional": true, "description": "Computed SHA-256 Content Security Policy hash source for given element."}, {"name": "layoutFlags", "type": "array", "items": {"type": "string", "description": "CSS.LayoutFlag"}, "optional": true, "description": "Relevant information about the layout of the node. When not provided, the layout of the node is not important to Web Inspector."}]}, {"id": "DataBinding", "type": "object", "description": "Relationship between data that is associated with a node and the node itself.", "properties": [{"name": "binding", "type": "string", "description": "The binding key that is specified."}, {"name": "type", "optional": true, "type": "string", "description": "A more descriptive name for the type of binding that represents this paritcular data relationship"}, {"name": "value", "type": "string", "description": "The value that is resolved to with this data binding relationship."}]}, {"id": "Rect", "type": "object", "properties": [{"name": "x", "type": "integer", "description": "X coordinate"}, {"name": "y", "type": "integer", "description": "Y coordinate"}, {"name": "width", "type": "integer", "description": "Rectangle width"}, {"name": "height", "type": "integer", "description": "Rectangle height"}]}, {"id": "EventListener", "type": "object", "description": "A structure holding event listener properties.", "properties": [{"name": "eventListenerId", "$ref": "EventListenerId"}, {"name": "type", "type": "string", "description": "<code>EventListener</code>'s type."}, {"name": "useCapture", "type": "boolean", "description": "<code>EventListener</code>'s useCapture."}, {"name": "isAttribute", "type": "boolean", "description": "<code>EventListener</code>'s isAttribute."}, {"name": "nodeId", "$ref": "NodeId", "optional": true, "description": "The target <code>DOMNode</code> id if the event listener is for a node."}, {"name": "onWindow", "type": "boolean", "optional": true, "description": "True if the event listener was added to the window."}, {"name": "location", "$ref": "Debugger.Location", "optional": true, "description": "Handler code location."}, {"name": "handler<PERSON>ame", "type": "string", "optional": true, "description": "Event handler function name."}, {"name": "passive", "type": "boolean", "optional": true, "description": "<code>EventListener</code>'s passive."}, {"name": "once", "type": "boolean", "optional": true, "description": "<code>EventListener</code>'s once."}, {"name": "disabled", "type": "boolean", "optional": true}, {"name": "hasBreakpoint", "type": "boolean", "optional": true}]}, {"id": "AccessibilityProperties", "description": "A structure holding accessibility properties.", "type": "object", "properties": [{"name": "activeDescendantNodeId", "$ref": "NodeId", "optional": true, "description": "<code>DOMNode</code> id of the accessibility object referenced by aria-activedescendant."}, {"name": "busy", "type": "boolean", "optional": true, "description": "Value of @aria-busy on current or ancestor node."}, {"name": "checked", "type": "string", "optional": true, "enum": ["true", "false", "mixed"], "description": "Checked state of certain form controls."}, {"name": "childNodeIds", "type": "array", "items": {"$ref": "NodeId"}, "optional": true, "description": "Array of <code>DOMNode</code> ids of the accessibility tree children if available."}, {"name": "controlledNodeIds", "type": "array", "items": {"$ref": "NodeId"}, "optional": true, "description": "Array of <code>DOMNode</code> ids of any nodes referenced via @aria-controls."}, {"name": "current", "type": "string", "optional": true, "enum": ["true", "false", "page", "step", "location", "date", "time"], "description": "Current item within a container or set of related elements."}, {"name": "disabled", "type": "boolean", "optional": true, "description": "Disabled state of form controls."}, {"name": "headingLevel", "type": "number", "optional": true, "description": "Heading level of a heading element."}, {"name": "hierarchyLevel", "type": "number", "optional": true, "description": "The hierarchical level of an element."}, {"name": "isPopUpButton", "type": "boolean", "optional": true, "description": "Whether an element is a popup button."}, {"name": "exists", "type": "boolean", "description": "Indicates whether there is an existing AX object for the DOM node. If this is false, all the other properties will be default values."}, {"name": "expanded", "type": "boolean", "optional": true, "description": "Expanded state."}, {"name": "flowedNodeIds", "type": "array", "items": {"$ref": "NodeId"}, "optional": true, "description": "Array of <code>DOMNode</code> ids of any nodes referenced via @aria-flowto."}, {"name": "focused", "type": "boolean", "optional": true, "description": "Focused state. Only defined on focusable elements."}, {"name": "ignored", "type": "boolean", "optional": true, "description": "Indicates whether the accessibility of the associated AX object node is ignored, whether heuristically or explicitly."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "boolean", "optional": true, "description": "State indicating whether the accessibility of the associated AX object node is ignored by default for node type."}, {"name": "invalid", "type": "string", "optional": true, "enum": ["true", "false", "grammar", "spelling"], "description": "Invalid status of form controls."}, {"name": "hidden", "type": "boolean", "optional": true, "description": "Hidden state. True if node or an ancestor is hidden via CSS or explicit @aria-hidden, to clarify why the element is ignored."}, {"name": "label", "type": "string", "description": "Computed label value for the node, sometimes calculated by referencing other nodes."}, {"name": "liveRegionAtomic", "type": "boolean", "optional": true, "description": "Value of @aria-atomic."}, {"name": "liveRegionRelevant", "type": "array", "items": {"type": "string"}, "optional": true, "description": "Token value(s) of element's @aria-relevant attribute. Array of string values matching $ref LiveRegionRelevant. FIXME: Enum values blocked by http://webkit.org/b/133711"}, {"name": "liveRegionStatus", "type": "string", "optional": true, "enum": ["assertive", "polite", "off"], "description": "Value of element's @aria-live attribute."}, {"name": "mouseEventNodeId", "$ref": "NodeId", "optional": true, "description": "<code>DOMNode</code> id of node or closest ancestor node that has a mousedown, mouseup, or click event handler."}, {"name": "nodeId", "$ref": "NodeId", "description": "Target <code>DOMNode</code> id."}, {"name": "ownedNodeIds", "type": "array", "items": {"$ref": "NodeId"}, "optional": true, "description": "Array of <code>DOMNode</code> ids of any nodes referenced via @aria-owns."}, {"name": "parentNodeId", "$ref": "NodeId", "optional": true, "description": "<code>DOMNode</code> id of the accessibility tree parent object if available."}, {"name": "pressed", "type": "boolean", "optional": true, "description": "Pressed state for toggle buttons."}, {"name": "readonly", "type": "boolean", "optional": true, "description": "Readonly state of text controls."}, {"name": "required", "type": "boolean", "optional": true, "description": "Required state of form controls."}, {"name": "role", "type": "string", "description": "Computed value for first recognized role token, default role per element, or overridden role."}, {"name": "selected", "type": "boolean", "optional": true, "description": "Selected state of certain form controls."}, {"name": "selectedChildNodeIds", "type": "array", "items": {"$ref": "NodeId"}, "optional": true, "description": "Array of <code>DOMNode</code> ids of any children marked as selected."}, {"name": "switchState", "type": "string", "optional": true, "enum": ["off", "on"], "description": "On / off state of switch form controls."}]}, {"id": "RGBAColor", "type": "object", "description": "A structure holding an RGBA color.", "properties": [{"name": "r", "type": "integer", "description": "The red component, in the [0-255] range."}, {"name": "g", "type": "integer", "description": "The green component, in the [0-255] range."}, {"name": "b", "type": "integer", "description": "The blue component, in the [0-255] range."}, {"name": "a", "type": "number", "optional": true, "description": "The alpha component, in the [0-1] range (default: 1)."}]}, {"id": "Quad", "type": "array", "items": {"type": "number"}, "minItems": 8, "maxItems": 8, "description": "An array of quad vertices, x immediately followed by y for each point, points clock-wise."}, {"id": "HighlightConfig", "type": "object", "description": "Configuration data for the highlighting of page elements.", "properties": [{"name": "showInfo", "type": "boolean", "optional": true, "description": "Whether the node info tooltip should be shown (default: false)."}, {"name": "contentColor", "$ref": "RGBAColor", "optional": true, "description": "The content box highlight fill color (default: transparent)."}, {"name": "paddingColor", "$ref": "RGBAColor", "optional": true, "description": "The padding highlight fill color (default: transparent)."}, {"name": "borderColor", "$ref": "RGBAColor", "optional": true, "description": "The border highlight fill color (default: transparent)."}, {"name": "marginColor", "$ref": "RGBAColor", "optional": true, "description": "The margin highlight fill color (default: transparent)."}]}, {"id": "GridOverlayConfig", "type": "object", "description": "Configuration data for grid overlays.", "properties": [{"name": "gridColor", "$ref": "RGBAColor", "description": "The primary color to use for the grid overlay."}, {"name": "showLineNames", "type": "boolean", "optional": true, "description": "Show labels for grid line names. If not specified, the default value is false."}, {"name": "showLineNumbers", "type": "boolean", "optional": true, "description": "Show labels for grid line numbers. If not specified, the default value is false."}, {"name": "showExtendedGridLines", "type": "boolean", "optional": true, "description": "Show grid lines that extend beyond the bounds of the grid. If not specified, the default value is false."}, {"name": "showTrackSizes", "type": "boolean", "optional": true, "description": "Show grid track size information. If not specified, the default value is false."}, {"name": "showAreaNames", "type": "boolean", "optional": true, "description": "Show labels for grid area names. If not specified, the default value is false."}]}, {"id": "FlexOverlayConfig", "type": "object", "description": "Configuration data for flex overlays.", "properties": [{"name": "flexColor", "$ref": "RGBAColor", "description": "The primary color to use for the flex overlay."}, {"name": "showOrderNumbers", "type": "boolean", "optional": true, "description": "Show labels for flex order. If not specified, the default value is false."}]}, {"id": "Styleable", "type": "object", "properties": [{"name": "nodeId", "$ref": "NodeId"}, {"name": "pseudoId", "$ref": "CSS.PseudoId", "optional": true}], "description": "An object referencing a node and a pseudo-element, primarily used to identify an animation effect target."}, {"id": "MediaStats", "type": "object", "description": "A structure holding media element statistics and configurations.", "properties": [{"name": "audio", "$ref": "AudioMediaStats", "optional": true}, {"name": "video", "$ref": "VideoMediaStats", "optional": true}, {"name": "devicePixelRatio", "type": "number", "optional": true, "description": "The ratio between physical screen pixels and CSS pixels."}, {"name": "viewport", "$ref": "ViewportSize", "optional": true, "description": "The viewport size occupied by the media element."}, {"name": "quality", "$ref": "VideoPlaybackQuality", "optional": true}, {"name": "source", "type": "string", "optional": true, "description": "The source type of the media element."}]}, {"id": "AudioMediaStats", "type": "object", "description": "A structure holding media element's audio-specific statistics and configurations.", "properties": [{"name": "bitrate", "type": "integer", "description": "The data rate of the primary audio track in bits/s."}, {"name": "codec", "type": "string", "description": "The codec string of the primary audio track. (E.g., \"hvc1.1.6.L123.B0\")"}, {"name": "humanReadableCodecString", "type": "string", "description": "A human readable version of the `codec` parameter."}, {"name": "numberOfChannels", "type": "integer", "description": "The number of audio channels in the primary audio track."}, {"name": "sampleRate", "type": "number", "description": "The sample rate of the primary audio track in hertz."}]}, {"id": "VideoMediaStats", "type": "object", "description": "A structure holding media element's audio-specific statistics and configurations.", "properties": [{"name": "bitrate", "type": "integer", "description": "The data rate of the video track in bits/s."}, {"name": "codec", "type": "string", "description": "The codec string of the video track. (E.g., \"hvc1.1.6.L123.B0\")"}, {"name": "humanReadableCodecString", "type": "string", "description": "A human readable version of the `codec` parameter."}, {"name": "colorSpace", "$ref": "VideoColorSpace"}, {"name": "framerate", "type": "number", "description": "The nominal frame rate of video track in frames per second."}, {"name": "height", "type": "integer", "description": "The native height of the video track in CSS pixels"}, {"name": "width", "type": "integer", "description": "The native width of the video track in CSS pixels"}]}, {"id": "VideoColorSpace", "type": "object", "description": "WebCodecs VideoColorSpace", "properties": [{"name": "fullRange", "type": "boolean", "optional": true, "description": "A flag indicating whether the colorspace is Full range (true) or Video range (false)"}, {"name": "matrix", "type": "string", "optional": true, "description": "The matrix specification of the colorspace"}, {"name": "primaries", "type": "string", "optional": true, "description": "The color primaries specification of the colorspace"}, {"name": "transfer", "type": "string", "optional": true, "description": "The transfer function specification of the colorspace"}]}, {"id": "VideoPlaybackQuality", "type": "object", "description": "A count of frames enqueued for display by the media element, and a subset count of dropped and display composited frames.", "properties": [{"name": "displayCompositedVideoFrames", "type": "integer", "description": "The number of frames of the total which were composited by the display."}, {"name": "droppedVideoFrames", "type": "integer", "description": "The number of frames of the total which were dropped without being displayed."}, {"name": "totalVideoFrames", "type": "integer", "description": "The total number of frames enqueued for display by the media element."}]}, {"id": "ViewportSize", "type": "object", "properties": [{"name": "width", "type": "number"}, {"name": "height", "type": "number"}]}, {"id": "FilePayload", "type": "object", "description": "Data to construct File object.", "properties": [{"name": "name", "type": "string", "description": "File name."}, {"name": "type", "type": "string", "description": "File type."}, {"name": "data", "type": "string", "description": "Base64-encoded file data."}]}], "commands": [{"name": "getDocument", "description": "Returns the root DOM node to the caller.", "returns": [{"name": "root", "$ref": "Node", "description": "Resulting node."}]}, {"name": "requestChildNodes", "description": "Requests that children of the node with given id are returned to the caller in form of <code>setChildNodes</code> events where not only immediate children are retrieved, but all children down to the specified depth.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to get children for."}, {"name": "depth", "type": "integer", "optional": true, "description": "The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0."}]}, {"name": "querySelector", "description": "Executes <code>querySelector</code> on a given node.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to query upon."}, {"name": "selector", "type": "string", "description": "Selector string."}], "returns": [{"name": "nodeId", "$ref": "NodeId", "optional": true, "description": "Query selector result."}]}, {"name": "querySelectorAll", "description": "Executes <code>querySelectorAll</code> on a given node.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to query upon."}, {"name": "selector", "type": "string", "description": "Selector string."}], "returns": [{"name": "nodeIds", "type": "array", "items": {"$ref": "NodeId"}, "description": "Query selector result."}]}, {"name": "setNodeName", "description": "Sets node name for a node with given id.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to set name for."}, {"name": "name", "type": "string", "description": "New node's name."}], "returns": [{"name": "nodeId", "$ref": "NodeId", "description": "New node's id."}]}, {"name": "setNodeValue", "description": "Sets node value for a node with given id.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to set value for."}, {"name": "value", "type": "string", "description": "New node's value."}]}, {"name": "removeNode", "description": "Removes node with given id.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to remove."}]}, {"name": "setAttributeValue", "description": "Sets attribute for an element with given id.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the element to set attribute for."}, {"name": "name", "type": "string", "description": "Attribute name."}, {"name": "value", "type": "string", "description": "Attribute value."}]}, {"name": "setAttributesAsText", "description": "Sets attributes on element with given id. This method is useful when user edits some existing attribute value and types in several attribute name/value pairs.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the element to set attributes for."}, {"name": "text", "type": "string", "description": "Text with a number of attributes. Will parse this text using HTML parser."}, {"name": "name", "type": "string", "optional": true, "description": "Attribute name to replace with new attributes derived from text in case text parsed successfully."}]}, {"name": "removeAttribute", "description": "Removes attribute with given name from an element with given id.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the element to remove attribute from."}, {"name": "name", "type": "string", "description": "Name of the attribute to remove."}]}, {"name": "getSupportedEventNames", "description": "Gets the list of builtin DOM event names.", "returns": [{"name": "eventNames", "type": "array", "items": {"type": "string"}}]}, {"name": "getDataBindingsForNode", "description": "Returns all data binding relationships between data that is associated with the node and the node itself.", "condition": "defined(ENABLE_INSPECTOR_ALTERNATE_DISPATCHERS) && ENABLE_INSPECTOR_ALTERNATE_DISPATCHERS", "targetTypes": ["itml"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to get data bindings for."}], "returns": [{"name": "dataBindings", "type": "array", "items": {"$ref": "DataBinding"}, "description": "Array of binding relationships between data and node"}]}, {"name": "getAssociatedDataForNode", "description": "Returns all data that has been associated with the node and is available for data binding.", "condition": "defined(ENABLE_INSPECTOR_ALTERNATE_DISPATCHERS) && ENABLE_INSPECTOR_ALTERNATE_DISPATCHERS", "targetTypes": ["itml"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to get associated data for."}], "returns": [{"name": "associatedData", "type": "string", "optional": true, "description": "Associated data bound to this node. Sent as a JSON string."}]}, {"name": "getEventListenersForNode", "description": "Returns event listeners relevant to the node.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to get listeners for."}, {"name": "includeAncestors", "type": "boolean", "optional": true, "description": "Controls whether ancestor event listeners are included. Defaults to true."}], "returns": [{"name": "listeners", "type": "array", "items": {"$ref": "EventListener"}, "description": "Array of relevant listeners."}]}, {"name": "setEventListenerDisabled", "description": "Enable/disable the given event listener. A disabled event listener will not fire.", "parameters": [{"name": "eventListenerId", "$ref": "EventListenerId"}, {"name": "disabled", "type": "boolean"}]}, {"name": "setBreakpointForEventListener", "description": "Set a breakpoint on the given event listener.", "targetTypes": ["page"], "parameters": [{"name": "eventListenerId", "$ref": "EventListenerId"}, {"name": "options", "$ref": "Debugger.BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "removeBreakpointForEventListener", "description": "Remove any breakpoints on the given event listener.", "targetTypes": ["page"], "parameters": [{"name": "eventListenerId", "$ref": "EventListenerId"}]}, {"name": "getAccessibilityPropertiesForNode", "description": "Returns a dictionary of accessibility properties for the node.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node for which to get accessibility properties."}], "returns": [{"name": "properties", "$ref": "AccessibilityProperties", "description": "Dictionary of relevant accessibility properties."}]}, {"name": "getOuterHTML", "description": "Returns node's HTML markup.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to get markup for."}], "returns": [{"name": "outerHTML", "type": "string", "description": "Outer HTML markup."}]}, {"name": "setOuterHTML", "description": "Sets node HTML markup, returns new node id.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to set markup for."}, {"name": "outerHTML", "type": "string", "description": "Outer HTML markup to set."}]}, {"name": "insertAdjacentHTML", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId"}, {"name": "position", "type": "string"}, {"name": "html", "type": "string"}]}, {"name": "performSearch", "description": "Searches for a given string in the DOM tree. Use <code>getSearchResults</code> to access search results or <code>cancelSearch</code> to end this search session.", "parameters": [{"name": "query", "type": "string", "description": "Plain text or query selector or XPath search query."}, {"name": "nodeIds", "type": "array", "items": {"$ref": "NodeId"}, "optional": true, "description": "Ids of nodes to use as starting points for the search."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, search is case sensitive."}], "returns": [{"name": "searchId", "type": "string", "description": "Unique search session identifier."}, {"name": "resultCount", "type": "integer", "description": "Number of search results."}]}, {"name": "getSearchResults", "description": "Returns search results from given <code>fromIndex</code> to given <code>toIndex</code> from the sarch with the given identifier.", "parameters": [{"name": "searchId", "type": "string", "description": "Unique search session identifier."}, {"name": "fromIndex", "type": "integer", "description": "Start index of the search result to be returned."}, {"name": "toIndex", "type": "integer", "description": "End index of the search result to be returned."}], "returns": [{"name": "nodeIds", "type": "array", "items": {"$ref": "NodeId"}, "description": "Ids of the search result nodes."}]}, {"name": "discardSearchResults", "description": "Discards search results from the session with the given id. <code>getSearchResults</code> should no longer be called for that search.", "parameters": [{"name": "searchId", "type": "string", "description": "Unique search session identifier."}]}, {"name": "requestNode", "description": "Requests that the node is sent to the caller given the JavaScript node object reference. All nodes that form the path from the node to the root are also sent to the client as a series of <code>setChildNodes</code> notifications.", "targetTypes": ["page"], "parameters": [{"name": "objectId", "$ref": "Runtime.RemoteObjectId", "description": "JavaScript object id to convert into node."}], "returns": [{"name": "nodeId", "$ref": "NodeId", "description": "Node id for given object."}]}, {"name": "setInspectModeEnabled", "description": "Enters the 'inspect' mode. In this mode, elements that user is hovering over are highlighted. <PERSON>end then generates 'inspect' command upon element selection.", "condition": "defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY", "parameters": [{"name": "enabled", "type": "boolean", "description": "True to enable inspection mode, false to disable it."}, {"name": "highlightConfig", "$ref": "HighlightConfig", "optional": true, "description": "A descriptor for the highlight appearance of hovered-over nodes. May be omitted if <code>enabled == false</code>."}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}]}, {"name": "setInspectModeEnabled", "description": "Enters the 'inspect' mode. In this mode, elements that user is hovering over are highlighted. <PERSON>end then generates 'inspect' command upon element selection.", "condition": "!(defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY)", "parameters": [{"name": "enabled", "type": "boolean", "description": "True to enable inspection mode, false to disable it."}, {"name": "highlightConfig", "$ref": "HighlightConfig", "optional": true, "description": "A descriptor for the highlight appearance of hovered-over nodes. May be omitted if <code>enabled == false</code>."}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}, {"name": "showRulers", "type": "boolean", "optional": true, "description": "Whether the rulers should be shown during element selection. This overrides Page.setShowRulers."}]}, {"name": "highlightRect", "description": "Highlights given rectangle. Coordinates are absolute with respect to the main frame viewport.", "targetTypes": ["page"], "parameters": [{"name": "x", "type": "integer", "description": "X coordinate"}, {"name": "y", "type": "integer", "description": "Y coordinate"}, {"name": "width", "type": "integer", "description": "Rectangle width"}, {"name": "height", "type": "integer", "description": "Rectangle height"}, {"name": "color", "$ref": "RGBAColor", "optional": true, "description": "The highlight fill color (default: transparent)."}, {"name": "outlineColor", "$ref": "RGBAColor", "optional": true, "description": "The highlight outline color (default: transparent)."}, {"name": "usePageCoordinates", "type": "boolean", "optional": true, "description": "Indicates whether the provided parameters are in page coordinates or in viewport coordinates (the default)."}]}, {"name": "highlightQuad", "description": "Highlights given quad. Coordinates are absolute with respect to the main frame viewport.", "targetTypes": ["page"], "parameters": [{"name": "quad", "$ref": "Quad", "description": "Quad to highlight"}, {"name": "color", "$ref": "RGBAColor", "optional": true, "description": "The highlight fill color (default: transparent)."}, {"name": "outlineColor", "$ref": "RGBAColor", "optional": true, "description": "The highlight outline color (default: transparent)."}, {"name": "usePageCoordinates", "type": "boolean", "optional": true, "description": "Indicates whether the provided parameters are in page coordinates or in viewport coordinates (the default)."}]}, {"name": "highlightSelector", "description": "Highlights all DOM nodes that match a given selector. A string containing a CSS selector must be specified.", "targetTypes": ["page"], "condition": "defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY", "parameters": [{"name": "selectorString", "type": "string", "description": "A CSS selector for finding matching nodes to highlight."}, {"name": "frameId", "type": "string", "optional": true, "description": "Identifier of the frame which will be searched using the selector.  If not provided, the main frame will be used."}, {"name": "highlightConfig", "$ref": "HighlightConfig", "description": "A descriptor for the highlight appearance."}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}]}, {"name": "highlightSelector", "description": "Highlights all DOM nodes that match a given selector. A string containing a CSS selector must be specified.", "targetTypes": ["page"], "condition": "!(defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY)", "parameters": [{"name": "selectorString", "type": "string", "description": "A CSS selector for finding matching nodes to highlight."}, {"name": "frameId", "type": "string", "optional": true, "description": "Identifier of the frame which will be searched using the selector.  If not provided, the main frame will be used."}, {"name": "highlightConfig", "$ref": "HighlightConfig", "description": "A descriptor for the highlight appearance."}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}, {"name": "showRulers", "type": "boolean", "optional": true, "description": "Whether the rulers should be shown during element selection. This overrides Page.setShowRulers."}]}, {"name": "highlightNode", "description": "Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or objectId must be specified.", "condition": "defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY", "parameters": [{"name": "nodeId", "$ref": "NodeId", "optional": true, "description": "Identifier of the node to highlight."}, {"name": "objectId", "$ref": "Runtime.RemoteObjectId", "optional": true, "description": "JavaScript object id of the node to be highlighted."}, {"name": "highlightConfig", "$ref": "HighlightConfig", "description": "A descriptor for the highlight appearance."}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}]}, {"name": "highlightNode", "description": "Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or objectId must be specified.", "condition": "!(defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY)", "parameters": [{"name": "nodeId", "$ref": "NodeId", "optional": true, "description": "Identifier of the node to highlight."}, {"name": "objectId", "$ref": "Runtime.RemoteObjectId", "optional": true, "description": "JavaScript object id of the node to be highlighted."}, {"name": "highlightConfig", "$ref": "HighlightConfig", "description": "A descriptor for the highlight appearance."}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}, {"name": "showRulers", "type": "boolean", "optional": true, "description": "Whether the rulers should be shown during element selection. This overrides Page.setShowRulers."}]}, {"name": "highlightNodeList", "description": "Highlights each DOM node in the given list.", "condition": "defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY", "parameters": [{"name": "nodeIds", "type": "array", "items": {"$ref": "NodeId"}}, {"name": "highlightConfig", "$ref": "HighlightConfig"}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}]}, {"name": "highlightNodeList", "description": "Highlights each DOM node in the given list.", "condition": "!(defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY)", "parameters": [{"name": "nodeIds", "type": "array", "items": {"$ref": "NodeId"}}, {"name": "highlightConfig", "$ref": "HighlightConfig"}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "optional": true, "description": "If provided, used to configure a grid overlay shown during element selection. This overrides DOM.showGridOverlay."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "optional": true, "description": "If provided, used to configure a flex overlay shown during element selection. This overrides DOM.showFlexOverlay."}, {"name": "showRulers", "type": "boolean", "optional": true, "description": "Whether the rulers should be shown during element selection. This overrides Page.setShowRulers."}]}, {"name": "hide<PERSON><PERSON><PERSON>", "description": "Hides DOM node highlight."}, {"name": "highlightFrame", "targetTypes": ["page"], "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Identifier of the frame to highlight."}, {"name": "contentColor", "$ref": "RGBAColor", "optional": true, "description": "The content box highlight fill color (default: transparent)."}, {"name": "contentOutlineColor", "$ref": "RGBAColor", "optional": true, "description": "The content box highlight outline color (default: transparent)."}], "description": "Highlights owner element of the frame with given id."}, {"name": "showGridOverlay", "description": "Shows a grid overlay for a node that begins a 'grid' layout context. The command has no effect if <code>nodeId</code> is invalid or the associated node does not begin a 'grid' layout context. A node can only have one grid overlay at a time; subsequent calls with the same <code>nodeId</code> will override earlier calls.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "The node for which a grid overlay should be shown."}, {"name": "gridOverlayConfig", "$ref": "GridOverlayConfig", "description": "Configuration options for the grid overlay."}]}, {"name": "hideGridOverlay", "description": "Hides a grid overlay for a node that begins a 'grid' layout context. The command has no effect if <code>nodeId</code> is specified and invalid, or if there is not currently an overlay set for the <code>nodeId</code>.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "optional": true, "description": "The node for which a grid overlay should be hidden. If a <code>nodeId</code> is not specified, all grid overlays will be hidden."}]}, {"name": "showFlexOverlay", "description": "Shows a flex overlay for a node that begins a 'flex' layout context. The command has no effect if <code>nodeId</code> is invalid or the associated node does not begin a 'flex' layout context. A node can only have one flex overlay at a time; subsequent calls with the same <code>nodeId</code> will override earlier calls.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "The node for which a flex overlay should be shown."}, {"name": "flexOverlayConfig", "$ref": "FlexOverlayConfig", "description": "Configuration options for the flex overlay."}]}, {"name": "hideFlexOverlay", "description": "Hides a flex overlay for a node that begins a 'flex' layout context. The command has no effect if <code>nodeId</code> is specified and invalid, or if there is not currently an overlay set for the <code>nodeId</code>.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "optional": true, "description": "The node for which a flex overlay should be hidden. If a <code>nodeId</code> is not specified, all flex overlays will be hidden."}]}, {"name": "pushNodeByPathToFrontend", "description": "Requests that the node is sent to the caller given its path.", "targetTypes": ["page"], "parameters": [{"name": "path", "type": "string", "description": "Path to node in the proprietary format."}], "returns": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node for given path."}]}, {"name": "resolveNode", "description": "Resolves JavaScript node object for given node id.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "optional": true, "description": "Id of the node to resolve."}, {"name": "objectId", "$ref": "Runtime.RemoteObjectId", "optional": true, "description": "Source element handle."}, {"name": "frameId", "$ref": "Network.FrameId", "optional": true, "description": "Id of the frame to resolve the owner element."}, {"name": "executionContextId", "$ref": "Runtime.ExecutionContextId", "optional": true, "description": "Specifies in which execution context to adopt to."}, {"name": "objectGroup", "type": "string", "optional": true, "description": "Symbolic group name that can be used to release multiple objects."}], "returns": [{"name": "object", "$ref": "Runtime.RemoteObject", "description": "JavaScript object wrapper for given node."}]}, {"name": "getAttributes", "description": "Returns attributes for the specified node.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to retrieve attributes for."}], "returns": [{"name": "attributes", "type": "array", "items": {"type": "string"}, "description": "An interleaved array of node attribute names and values."}]}, {"name": "moveTo", "description": "Moves node into the new container, places it before the given anchor.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to drop."}, {"name": "targetNodeId", "$ref": "NodeId", "description": "Id of the element to drop into."}, {"name": "insertBeforeNodeId", "$ref": "NodeId", "optional": true, "description": "Drop node before given one."}], "returns": [{"name": "nodeId", "$ref": "NodeId", "description": "New id of the moved node."}]}, {"name": "undo", "description": "Undoes the last performed action.", "targetTypes": ["page"]}, {"name": "redo", "description": "Re-does the last undone action.", "targetTypes": ["page"]}, {"name": "markUndoableState", "description": "Marks last undoable state.", "targetTypes": ["page"]}, {"name": "focus", "description": "Focuses the given element.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to focus."}]}, {"name": "setInspectedNode", "description": "Enables console to refer to the node with given id via $0 (see Command Line API for more details).", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "DOM node id to be accessible by means of $0 command line API."}]}, {"name": "setAllowEditingUserAgentShadowTrees", "description": "Controls whether any DOM commands work for nodes inside a UserAgent shadow tree.", "targetTypes": ["page"], "parameters": [{"name": "allow", "type": "boolean"}]}, {"name": "getMediaStats", "description": "Returns media stats for the selected node.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node to retrieve mediastats for."}], "returns": [{"name": "mediaStats", "$ref": "MediaStats", "description": "An interleaved array of node attribute names and values."}]}, {"name": "describeNode", "description": "Returns node description.", "parameters": [{"name": "objectId", "$ref": "Runtime.RemoteObjectId", "description": "JavaScript object id of the node wrapper."}], "returns": [{"name": "contentFrameId", "$ref": "Network.FrameId", "optional": true, "description": "Frame ID for frame owner elements."}, {"name": "ownerFrameId", "$ref": "Network.FrameId", "optional": true, "description": "ID of the owning frame element."}]}, {"name": "scrollIntoViewIfNeeded", "description": "Scrolls the given rect into view if not already in the viewport.", "parameters": [{"name": "objectId", "$ref": "Runtime.RemoteObjectId", "description": "JavaScript object id of the node wrapper."}, {"name": "rect", "$ref": "Rect", "optional": true, "description": "Rect relative to the node's border box, in CSS pixels."}]}, {"name": "getContentQuads", "description": "Returns quads that describe node position on the page. This method\nmight return multiple quads for inline nodes.", "parameters": [{"name": "objectId", "$ref": "Runtime.RemoteObjectId", "description": "JavaScript object id of the node wrapper."}], "returns": [{"name": "quads", "type": "array", "items": {"$ref": "Quad"}, "description": "Quads that describe node layout relative to viewport."}]}, {"name": "setInputFiles", "description": "Sets input files for given <input type=file>", "parameters": [{"name": "objectId", "$ref": "Runtime.RemoteObjectId", "description": "Input element handle."}, {"name": "files", "type": "array", "items": {"$ref": "FilePayload"}, "optional": true, "description": "Files to set"}, {"name": "paths", "type": "array", "items": {"type": "string"}, "optional": true, "description": "File paths to set"}], "async": true}], "events": [{"name": "documentUpdated", "description": "Fired when <code>Document</code> has been totally updated. Node ids are no longer valid."}, {"name": "inspect", "description": "Inspect a particular node.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Equivalent of Inspector.inspect but with a nodeId instead of a RemoteObject. Useful for augmented contexts."}]}, {"name": "setChildNodes", "description": "Fired when backend wants to provide client with the missing DOM structure. This happens upon most of the calls requesting node ids.", "parameters": [{"name": "parentId", "$ref": "NodeId", "description": "Parent node id to populate with children."}, {"name": "nodes", "type": "array", "items": {"$ref": "Node"}, "description": "Child nodes array."}]}, {"name": "attributeModified", "description": "Fired when <code>Element</code>'s attribute is modified.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node that has changed."}, {"name": "name", "type": "string", "description": "Attribute name."}, {"name": "value", "type": "string", "description": "Attribute value."}]}, {"name": "attributeRemoved", "description": "Fired when <code>Element</code>'s attribute is removed.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node that has changed."}, {"name": "name", "type": "string", "description": "Attribute name."}]}, {"name": "inlineStyleInvalidated", "description": "Fired when <code>Element</code>'s inline style is modified via a CSS property modification.", "parameters": [{"name": "nodeIds", "type": "array", "items": {"$ref": "NodeId"}, "description": "Ids of the nodes for which the inline styles have been invalidated."}]}, {"name": "characterDataModified", "description": "Mirrors <code>DOMCharacterDataModified</code> event.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node that has changed."}, {"name": "characterData", "type": "string", "description": "New text value."}]}, {"name": "childNodeCountUpdated", "description": "Fired when <code>Container</code>'s child node count has changed.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node that has changed."}, {"name": "childNodeCount", "type": "integer", "description": "New node count."}]}, {"name": "childNodeInserted", "description": "Mirrors <code>DOMNodeInserted</code> event.", "parameters": [{"name": "parentNodeId", "$ref": "NodeId", "description": "Id of the node that has changed."}, {"name": "previousNodeId", "$ref": "NodeId", "description": "Id of the previous sibling."}, {"name": "node", "$ref": "Node", "description": "Inserted node data."}]}, {"name": "childNodeRemoved", "description": "Mirrors <code>DOMNodeRemoved</code> event.", "parameters": [{"name": "parentNodeId", "$ref": "NodeId", "description": "Parent id."}, {"name": "nodeId", "$ref": "NodeId", "description": "Id of the node that has been removed."}]}, {"name": "willDestroyDOMNode", "description": "Fired when a detached DOM node is about to be destroyed. Currently, this event will only be fired when a DOM node that is detached is about to be destructed.", "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Id of the node that will be destroyed."}]}, {"name": "shadowR<PERSON>Pushed", "description": "Called when shadow root is pushed into the element.", "targetTypes": ["page"], "parameters": [{"name": "hostId", "$ref": "NodeId", "description": "Host element id."}, {"name": "root", "$ref": "Node", "description": "Shadow root."}]}, {"name": "shadowRootPopped", "description": "Called when shadow root is popped from the element.", "targetTypes": ["page"], "parameters": [{"name": "hostId", "$ref": "NodeId", "description": "Host element id."}, {"name": "rootId", "$ref": "NodeId", "description": "Shadow root id."}]}, {"name": "customElementStateChanged", "description": "Called when the custom element state is changed.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId", "description": "Element id."}, {"name": "customElementState", "$ref": "CustomElementState", "description": "Custom element state."}]}, {"name": "pseudoElementAdded", "description": "Called when a pseudo element is added to an element.", "targetTypes": ["page"], "parameters": [{"name": "parentId", "$ref": "NodeId", "description": "Pseudo element's parent element id."}, {"name": "pseudoElement", "$ref": "Node", "description": "The added pseudo element."}]}, {"name": "pseudoElementRemoved", "description": "Called when a pseudo element is removed from an element.", "targetTypes": ["page"], "parameters": [{"name": "parentId", "$ref": "NodeId", "description": "Pseudo element's parent element id."}, {"name": "pseudoElementId", "$ref": "NodeId", "description": "The removed pseudo element id."}]}, {"name": "didAddEventListener", "description": "Called when an event listener is added to a node.", "parameters": [{"name": "nodeId", "$ref": "NodeId"}]}, {"name": "willRemoveEventListener", "description": "Called after a request has been made to remove an event listener from a node.", "parameters": [{"name": "nodeId", "$ref": "NodeId"}]}, {"name": "didFireEvent", "description": "Called when an event is fired on a node.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId"}, {"name": "eventName", "type": "string"}, {"name": "timestamp", "$ref": "Network.Timestamp", "description": "Time when the event was fired"}, {"name": "data", "type": "object", "optional": true, "description": "Holds ancillary information about the event or its target."}]}, {"name": "powerEfficientPlaybackStateChanged", "description": "Called when an element enters/exits a power efficient playback state.", "condition": "defined(ENABLE_VIDEO) && ENABLE_VIDEO", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "NodeId"}, {"name": "timestamp", "$ref": "Network.Timestamp"}, {"name": "isPowerEfficient", "type": "boolean"}]}]}, {"domain": "DOMDebugger", "description": "DOM debugging allows setting breakpoints on particular DOM operations and events. JavaScript execution will stop on these operations as if there was a regular breakpoint set.", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page", "worker"], "types": [{"id": "DOMBreakpointType", "type": "string", "enum": ["subtree-modified", "attribute-modified", "node-removed"], "description": "DOM breakpoint type."}, {"id": "EventBreakpointType", "type": "string", "enum": ["animation-frame", "interval", "listener", "timeout"], "description": "Event breakpoint type."}], "commands": [{"name": "setDOMBreakpoint", "description": "Sets breakpoint on particular operation with DOM.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId", "description": "Identifier of the node to set breakpoint on."}, {"name": "type", "$ref": "DOMBreakpointType", "description": "Type of the operation to stop upon."}, {"name": "options", "$ref": "Debugger.BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "removeDOMBreakpoint", "description": "Removes DOM breakpoint that was set using <code>setDOMBreakpoint</code>.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId", "description": "Identifier of the node to remove breakpoint from."}, {"name": "type", "$ref": "DOMBreakpointType", "description": "Type of the breakpoint to remove."}]}, {"name": "setEventBreakpoint", "description": "Sets breakpoint on particular event of given type.", "parameters": [{"name": "breakpointType", "$ref": "EventBreakpointType"}, {"name": "eventName", "type": "string", "optional": true, "description": "The name of the specific event to stop on."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, event<PERSON><PERSON> is case sensitive. Defaults to true."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats <PERSON><PERSON><PERSON> as a regex. Defaults to false."}, {"name": "options", "$ref": "Debugger.BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "removeEventBreakpoint", "description": "Removes breakpoint on particular event of given type.", "parameters": [{"name": "breakpointType", "$ref": "EventBreakpointType"}, {"name": "eventName", "type": "string", "optional": true, "description": "The name of the specific event to stop on."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, event<PERSON><PERSON> is case sensitive. Defaults to true."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats <PERSON><PERSON><PERSON> as a regex. Defaults to false."}]}, {"name": "setURLBreakpoint", "description": "Sets breakpoint on network activity for the given URL.", "parameters": [{"name": "url", "type": "string", "description": "Resource URL substring or regular expression. All requests having this substring in the URL will get stopped upon. An empty string will pause on all requests."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "Whether the URL string is a regular expression."}, {"name": "options", "$ref": "Debugger.BreakpointOptions", "optional": true, "description": "Options to apply to this breakpoint to modify its behavior."}]}, {"name": "removeURLBreakpoint", "description": "Removes breakpoint from network activity for the given URL.", "parameters": [{"name": "url", "type": "string", "description": "Resource URL substring. An empty string will stop pausing on all requests."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "Whether the URL string is a regular expression."}]}]}, {"domain": "DOMStorage", "description": "Query and modify DOM storage.", "debuggableTypes": ["itml", "page", "web-page"], "targetTypes": ["itml", "page"], "types": [{"id": "StorageId", "type": "object", "description": "DOM Storage identifier.", "properties": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "Security origin for the storage."}, {"name": "isLocalStorage", "type": "boolean", "description": "Whether the storage is local storage (not session storage)."}]}, {"id": "<PERSON><PERSON>", "type": "array", "description": "DOM Storage item.", "items": {"type": "string"}}], "commands": [{"name": "enable", "description": "Enables storage tracking, storage events will now be delivered to the client."}, {"name": "disable", "description": "Disables storage tracking, prevents storage events from being sent to the client."}, {"name": "getDOMStorageItems", "parameters": [{"name": "storageId", "$ref": "StorageId"}], "returns": [{"name": "entries", "type": "array", "items": {"$ref": "<PERSON><PERSON>"}}]}, {"name": "setDOMStorageItem", "parameters": [{"name": "storageId", "$ref": "StorageId"}, {"name": "key", "type": "string"}, {"name": "value", "type": "string"}]}, {"name": "removeDOMStorageItem", "parameters": [{"name": "storageId", "$ref": "StorageId"}, {"name": "key", "type": "string"}]}, {"name": "clearDOMStorageItems", "targetTypes": ["page"], "parameters": [{"name": "storageId", "$ref": "StorageId"}]}], "events": [{"name": "domStorageItemsCleared", "parameters": [{"name": "storageId", "$ref": "StorageId"}]}, {"name": "domStorageItemRemoved", "parameters": [{"name": "storageId", "$ref": "StorageId"}, {"name": "key", "type": "string"}]}, {"name": "domStorageItemAdded", "parameters": [{"name": "storageId", "$ref": "StorageId"}, {"name": "key", "type": "string"}, {"name": "newValue", "type": "string"}]}, {"name": "domStorageItemUpdated", "parameters": [{"name": "storageId", "$ref": "StorageId"}, {"name": "key", "type": "string"}, {"name": "oldValue", "type": "string"}, {"name": "newValue", "type": "string"}]}]}, {"domain": "Emulation", "availability": ["web"], "commands": [{"name": "setDeviceMetricsOverride", "description": "Overrides device metrics with provided values.", "async": true, "parameters": [{"name": "width", "type": "integer"}, {"name": "height", "type": "integer"}, {"name": "fixedLayout", "type": "boolean"}, {"name": "deviceScaleFactor", "type": "number", "optional": true}]}, {"name": "setJavaScriptEnabled", "description": "Allows to disable script execution for the page.", "parameters": [{"name": "enabled", "type": "boolean"}]}, {"name": "setAuthCredentials", "description": "Credentials to use during HTTP authentication.", "parameters": [{"name": "username", "type": "string", "optional": true}, {"name": "password", "type": "string", "optional": true}, {"name": "origin", "type": "string", "optional": true}]}, {"name": "setActiveAndFocused", "description": "Makes page focused for test.", "parameters": [{"name": "active", "type": "boolean", "optional": true}]}, {"name": "grantPermissions", "parameters": [{"name": "origin", "type": "string"}, {"name": "permissions", "type": "array", "items": {"type": "string"}}], "description": "Overrides the permissions."}, {"name": "resetPermissions", "description": "Clears permission overrides."}, {"name": "setOrientationOverride", "description": "Overrides window.orientation with provided value.", "parameters": [{"name": "angle", "type": "integer", "optional": true}]}]}, {"domain": "GenericTypes", "description": "Exposes generic types to be used by any domain.", "types": [{"id": "SearchMatch", "type": "object", "description": "Search match in a resource.", "properties": [{"name": "lineNumber", "type": "number", "description": "Line number in resource content."}, {"name": "lineContent", "type": "string", "description": "Line with match content."}]}]}, {"domain": "<PERSON><PERSON>", "description": "Heap domain exposes JavaScript heap attributes and capabilities.", "debuggableTypes": ["itml", "javascript", "page", "service-worker", "web-page"], "targetTypes": ["itml", "javascript", "page", "service-worker", "worker"], "types": [{"id": "GarbageCollection", "description": "Information about a garbage collection.", "type": "object", "properties": [{"name": "type", "type": "string", "enum": ["full", "partial"], "description": "The type of garbage collection."}, {"name": "startTime", "type": "number"}, {"name": "endTime", "type": "number"}]}, {"id": "HeapSnapshotData", "description": "JavaScriptCore HeapSnapshot JSON data.", "type": "string"}], "commands": [{"name": "enable", "description": "Enables Heap domain events."}, {"name": "disable", "description": "Disables Heap domain events."}, {"name": "gc", "description": "Trigger a full garbage collection."}, {"name": "snapshot", "description": "Take a heap snapshot.", "returns": [{"name": "timestamp", "type": "number"}, {"name": "snapshotData", "$ref": "HeapSnapshotData"}]}, {"name": "startTracking", "description": "Start tracking heap changes. This will produce a `trackingStart` event."}, {"name": "stopTracking", "description": "Stop tracking heap changes. This will produce a `trackingComplete` event."}, {"name": "getPreview", "description": "Returns a preview (string, Debugger.FunctionDetails, or Runtime.ObjectPreview) for a Heap.HeapObjectId.", "parameters": [{"name": "heapObjectId", "type": "integer", "description": "Identifier of the heap object within the snapshot."}], "returns": [{"name": "string", "type": "string", "optional": true, "description": "String value."}, {"name": "functionDetails", "$ref": "Debugger.FunctionDetails", "optional": true, "description": "Function details."}, {"name": "preview", "$ref": "Runtime.ObjectPreview", "optional": true, "description": "Object preview."}]}, {"name": "getRemoteObject", "description": "Returns the strongly referenced Runtime.RemoteObject for a Heap.HeapObjectId.", "parameters": [{"name": "heapObjectId", "type": "integer", "description": "Identifier of the heap object within the snapshot."}, {"name": "objectGroup", "type": "string", "optional": true, "description": "Symbolic group name that can be used to release multiple objects."}], "returns": [{"name": "result", "$ref": "Runtime.RemoteObject", "description": "Resulting object."}]}], "events": [{"name": "garbageCollected", "description": "Information about the garbage collection.", "parameters": [{"name": "collection", "$ref": "GarbageCollection"}]}, {"name": "trackingStart", "description": "Tracking started.", "parameters": [{"name": "timestamp", "type": "number"}, {"name": "snapshotData", "$ref": "HeapSnapshotData", "description": "Snapshot at the start of tracking."}]}, {"name": "trackingComplete", "description": "Tracking stopped.", "parameters": [{"name": "timestamp", "type": "number"}, {"name": "snapshotData", "$ref": "HeapSnapshotData", "description": "Snapshot at the end of tracking."}]}]}, {"domain": "IndexedDB", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page"], "types": [{"id": "DatabaseWithObjectStores", "type": "object", "description": "Database with an array of object stores.", "properties": [{"name": "name", "type": "string", "description": "Database name."}, {"name": "version", "type": "number", "description": "Database version."}, {"name": "objectStores", "type": "array", "items": {"$ref": "ObjectStore"}, "description": "Object stores in this database."}]}, {"id": "ObjectStore", "type": "object", "description": "Object store.", "properties": [{"name": "name", "type": "string", "description": "Object store name."}, {"name": "keyP<PERSON>", "$ref": "Key<PERSON><PERSON>", "description": "Object store key path."}, {"name": "autoIncrement", "type": "boolean", "description": "If true, object store has auto increment flag set."}, {"name": "indexes", "type": "array", "items": {"$ref": "ObjectStoreIndex"}, "description": "Indexes in this object store."}]}, {"id": "ObjectStoreIndex", "type": "object", "description": "Object store index.", "properties": [{"name": "name", "type": "string", "description": "Index name."}, {"name": "keyP<PERSON>", "$ref": "Key<PERSON><PERSON>", "description": "Index key path."}, {"name": "unique", "type": "boolean", "description": "If true, index is unique."}, {"name": "multiEntry", "type": "boolean", "description": "If true, index allows multiple entries for a key."}]}, {"id": "Key", "type": "object", "description": "Key.", "properties": [{"name": "type", "type": "string", "enum": ["number", "string", "date", "array"], "description": "Key type."}, {"name": "number", "type": "number", "optional": true, "description": "Number value."}, {"name": "string", "type": "string", "optional": true, "description": "String value."}, {"name": "date", "type": "number", "optional": true, "description": "Date value."}, {"name": "array", "type": "array", "optional": true, "items": {"$ref": "Key"}, "description": "Array value."}]}, {"id": "KeyRange", "type": "object", "description": "Key range.", "properties": [{"name": "lower", "$ref": "Key", "optional": true, "description": "Lower bound."}, {"name": "upper", "$ref": "Key", "optional": true, "description": "Upper bound."}, {"name": "lowerOpen", "type": "boolean", "description": "If true lower bound is open."}, {"name": "upperOpen", "type": "boolean", "description": "If true upper bound is open."}]}, {"id": "DataEntry", "type": "object", "description": "Data entry.", "properties": [{"name": "key", "$ref": "Runtime.RemoteObject", "description": "Key."}, {"name": "<PERSON><PERSON><PERSON>", "$ref": "Runtime.RemoteObject", "description": "Primary key."}, {"name": "value", "$ref": "Runtime.RemoteObject", "description": "Value."}]}, {"id": "Key<PERSON><PERSON>", "type": "object", "description": "Key path.", "properties": [{"name": "type", "type": "string", "enum": ["null", "string", "array"], "description": "Key path type."}, {"name": "string", "type": "string", "optional": true, "description": "String value."}, {"name": "array", "type": "array", "optional": true, "items": {"type": "string"}, "description": "Array value."}]}], "commands": [{"name": "enable", "description": "Enables events from backend."}, {"name": "disable", "description": "Disables events from backend."}, {"name": "requestDatabaseNames", "description": "Requests database names for given security origin.", "async": true, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "Security origin."}], "returns": [{"name": "databaseNames", "type": "array", "items": {"type": "string"}, "description": "Database names for origin."}]}, {"name": "requestDatabase", "description": "Requests database with given name in given frame.", "async": true, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "Security origin."}, {"name": "databaseName", "type": "string", "description": "Database name."}], "returns": [{"name": "databaseWithObjectStores", "$ref": "DatabaseWithObjectStores", "description": "Database with an array of object stores."}]}, {"name": "requestData", "description": "Requests data from object store or index.", "async": true, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "Security origin."}, {"name": "databaseName", "type": "string", "description": "Database name."}, {"name": "objectStoreName", "type": "string", "description": "Object store name."}, {"name": "indexName", "type": "string", "description": "Index name, empty string for object store data requests."}, {"name": "skip<PERSON><PERSON>nt", "type": "integer", "description": "Number of records to skip."}, {"name": "pageSize", "type": "integer", "description": "Number of records to fetch."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "$ref": "KeyRange", "optional": true, "description": "Key range."}], "returns": [{"name": "objectStoreDataEntries", "type": "array", "items": {"$ref": "DataEntry"}, "description": "Array of object store data entries."}, {"name": "hasMore", "type": "boolean", "description": "If true, there are more entries to fetch in the given range."}]}, {"name": "clearObjectStore", "description": "Clears all entries from an object store.", "async": true, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "Security origin."}, {"name": "databaseName", "type": "string", "description": "Database name."}, {"name": "objectStoreName", "type": "string", "description": "Object store name."}]}]}, {"domain": "Input", "availability": ["web"], "types": [{"id": "TimeSinceEpoch", "description": "UTC time in seconds, counted from January 1, 1970.", "type": "number"}, {"id": "TouchPoint", "type": "object", "description": "Touch point.", "properties": [{"name": "x", "type": "integer", "description": "X coordinate of the event relative to the main frame's viewport in CSS pixels."}, {"name": "y", "type": "integer", "description": "Y coordinate of the event relative to the main frame's viewport in CSS pixels."}, {"name": "id", "type": "integer", "description": "Identifier used to track touch sources between events, must be unique within an event."}]}], "commands": [{"name": "dispatchKeyEvent", "description": "Dispatches a key event to the page.", "async": true, "parameters": [{"name": "type", "description": "Type of the key event.", "type": "string", "enum": ["keyDown", "keyUp"]}, {"name": "modifiers", "description": "Bit field representing pressed modifier keys. (default: 0).", "optional": true, "type": "integer"}, {"name": "text", "description": "Text as generated by processing a virtual key code with a keyboard layout. Not needed for\nfor `keyUp` and `rawKeyDown` events (default: \"\")", "optional": true, "type": "string"}, {"name": "unmodifiedText", "description": "Text that would have been generated by the keyboard if no modifiers were pressed (except for\nshift). Useful for shortcut (accelerator) key handling (default: \"\").", "optional": true, "type": "string"}, {"name": "code", "description": "Unique DOM defined string value for each physical key (e.g., 'KeyA') (default: \"\").", "optional": true, "type": "string"}, {"name": "key", "description": "Unique DOM defined string value describing the meaning of the key in the context of active\nmodifiers, keyboard layout, etc (e.g., 'AltGr') (default: \"\").", "optional": true, "type": "string"}, {"name": "windowsVirtualKeyCode", "description": "Windows virtual key code (default: 0).", "optional": true, "type": "integer"}, {"name": "nativeVirtualKeyCode", "description": "Native virtual key code (default: 0).", "optional": true, "type": "integer"}, {"name": "autoRepeat", "description": "Whether the event was generated from auto repeat (default: false).", "optional": true, "type": "boolean"}, {"name": "isKeypad", "description": "Whether the event was generated from the keypad (default: false).", "optional": true, "type": "boolean"}, {"name": "isSystemKey", "description": "Whether the event was a system key event (default: false).", "optional": true, "type": "boolean"}, {"name": "macCommands", "description": "Mac editing commands associated with this key", "type": "array", "optional": true, "items": {"type": "string"}}]}, {"name": "dispatchMouseEvent", "description": "Dispatches a mouse event to the page.", "async": true, "parameters": [{"name": "type", "description": "Type of the mouse event.", "type": "string", "enum": ["move", "down", "up", "wheel"]}, {"name": "x", "description": "X coordinate of the event relative to the main frame's viewport in CSS pixels.", "type": "integer"}, {"name": "y", "description": "Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to\nthe top of the viewport and Y increases as it proceeds towards the bottom of the viewport.", "type": "integer"}, {"name": "modifiers", "description": "Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).", "optional": true, "type": "integer"}, {"name": "button", "description": "Mouse button (default: \"none\").", "optional": true, "type": "string", "enum": ["none", "left", "middle", "right", "back", "forward"]}, {"name": "buttons", "description": "A number indicating which buttons are pressed on the mouse when a mouse event is triggered.\nLeft=1, Right=2, Middle=4, Back=8, Forward=16, None=0.", "optional": true, "type": "integer"}, {"name": "clickCount", "description": "Number of times the mouse button was clicked (default: 0).", "optional": true, "type": "integer"}, {"name": "deltaX", "description": "X delta in CSS pixels for mouse wheel event (default: 0).", "optional": true, "type": "integer"}, {"name": "deltaY", "description": "Y delta in CSS pixels for mouse wheel event (default: 0).", "optional": true, "type": "integer"}]}, {"name": "dispatchWheelEvent", "description": "Dispatches a wheel event to the page.", "async": true, "parameters": [{"name": "x", "description": "X coordinate of the event relative to the main frame's viewport in CSS pixels.", "type": "integer"}, {"name": "y", "description": "Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to\nthe top of the viewport and Y increases as it proceeds towards the bottom of the viewport.", "type": "integer"}, {"name": "modifiers", "description": "Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).", "optional": true, "type": "integer"}, {"name": "deltaX", "description": "X delta in CSS pixels for mouse wheel event (default: 0).", "optional": true, "type": "integer"}, {"name": "deltaY", "description": "Y delta in CSS pixels for mouse wheel event (default: 0).", "optional": true, "type": "integer"}]}, {"name": "dispatchTapEvent", "description": "Dispatches a tap event to the page.", "async": true, "parameters": [{"name": "x", "description": "X coordinate of the event relative to the main frame's viewport in CSS pixels.", "type": "integer"}, {"name": "y", "description": "Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to\nthe top of the viewport and Y increases as it proceeds towards the bottom of the viewport.", "type": "integer"}, {"name": "modifiers", "description": "Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).", "optional": true, "type": "integer"}]}, {"name": "dispatchTouchEvent", "description": "Dispatches a touch event to the page.", "async": true, "parameters": [{"name": "type", "description": "Type of the touch event.", "type": "string", "enum": ["touchStart", "touchMove", "touchEnd", "touchCancel"]}, {"name": "modifiers", "description": "Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).", "optional": true, "type": "integer"}, {"name": "touchPoints", "description": "List of touch points", "type": "array", "optional": true, "items": {"$ref": "TouchPoint"}}]}]}, {"domain": "Inspector", "debuggableTypes": ["itml", "javascript", "page", "web-page"], "targetTypes": ["itml", "javascript", "page"], "commands": [{"name": "enable", "description": "Enables inspector domain notifications."}, {"name": "disable", "description": "Disables inspector domain notifications."}, {"name": "initialized", "description": "Sent by the frontend after all initialization messages have been sent."}], "events": [{"name": "evaluateForTestInFrontend", "parameters": [{"name": "script", "type": "string"}]}, {"name": "inspect", "parameters": [{"name": "object", "$ref": "Runtime.RemoteObject"}, {"name": "hints", "type": "object"}]}]}, {"domain": "LayerTree", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page"], "types": [{"id": "LayerId", "type": "string", "description": "Unique RenderLayer identifier."}, {"id": "PseudoElementId", "type": "string", "description": "Unique PseudoElement identifier."}, {"id": "IntRect", "type": "object", "description": "A rectangle.", "properties": [{"name": "x", "type": "integer", "description": "The x position."}, {"name": "y", "type": "integer", "description": "The y position."}, {"name": "width", "type": "integer", "description": "The width metric."}, {"name": "height", "type": "integer", "description": "The height metric."}]}, {"id": "Layer", "type": "object", "description": "Information about a compositing layer.", "properties": [{"name": "layerId", "$ref": "LayerId", "description": "The unique id for this layer."}, {"name": "nodeId", "$ref": "DOM.NodeId", "description": "The id for the node associated with this layer."}, {"name": "bounds", "$ref": "IntRect", "description": "Bounds of the layer in absolute page coordinates."}, {"name": "paintCount", "type": "integer", "description": "Indicates how many time this layer has painted."}, {"name": "memory", "type": "integer", "description": "Estimated memory used by this layer."}, {"name": "compositedBounds", "$ref": "IntRect", "description": "The bounds of the composited layer."}, {"name": "isInShadowTree", "type": "boolean", "optional": true, "description": "Indicates whether this layer is associated with an element hosted in a shadow tree."}, {"name": "isReflection", "type": "boolean", "optional": true, "description": "Indicates whether this layer was used to provide a reflection for the element."}, {"name": "isGeneratedContent", "type": "boolean", "optional": true, "description": "Indicates whether the layer is attached to a pseudo element that is CSS generated content."}, {"name": "isAnonymous", "type": "boolean", "optional": true, "description": "Indicates whether the layer was created for a CSS anonymous block or box."}, {"name": "pseudoElementId", "$ref": "PseudoElementId", "optional": true, "description": "The id for the pseudo element associated with this layer."}, {"name": "pseudoElement", "type": "string", "optional": true, "description": "The name of the CSS pseudo-element that prompted the layer to be generated."}]}, {"id": "CompositingReasons", "type": "object", "description": "An object containing the reasons why the layer was composited as properties.", "properties": [{"name": "transform3D", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a CSS 3D transform."}, {"name": "video", "type": "boolean", "optional": true, "description": "Composition due to association with a <video> element."}, {"name": "canvas", "type": "boolean", "optional": true, "description": "Composition due to the element being a <canvas> element."}, {"name": "plugin", "type": "boolean", "optional": true, "description": "Composition due to association with a plugin."}, {"name": "iFrame", "type": "boolean", "optional": true, "description": "Composition due to association with an <iframe> element."}, {"name": "model", "type": "boolean", "optional": true, "description": "Composition due to association with a <model> element."}, {"name": "backfaceVisibilityHidden", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a \"backface-visibility: hidden\" style."}, {"name": "clipsCompositingDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with an element clipping compositing descendants."}, {"name": "animation", "type": "boolean", "optional": true, "description": "Composition due to association with an animated element."}, {"name": "filters", "type": "boolean", "optional": true, "description": "Composition due to association with an element with CSS filters applied."}, {"name": "positionFixed", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a \"position: fixed\" style."}, {"name": "positionSticky", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a \"position: sticky\" style."}, {"name": "overflowScrollingTouch", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a \"overflow-scrolling: touch\" style."}, {"name": "stacking", "type": "boolean", "optional": true, "description": "Composition due to association with an element establishing a stacking context."}, {"name": "overlap", "type": "boolean", "optional": true, "description": "Composition due to association with an element overlapping other composited elements."}, {"name": "negativeZIndexChildren", "type": "boolean", "optional": true, "description": "Composition due to association with an element with descendants that have a negative z-index."}, {"name": "transformWithCompositedDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with an element with composited descendants."}, {"name": "opacityWithCompositedDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with an element with opacity applied and composited descendants."}, {"name": "maskWithCompositedDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with a masked element and composited descendants."}, {"name": "reflectionWithCompositedDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a reflection and composited descendants."}, {"name": "filterWithCompositedDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with an element with CSS filters applied and composited descendants."}, {"name": "blendingWithCompositedDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with an element with CSS blending applied and composited descendants."}, {"name": "isolatesCompositedBlendingDescendants", "type": "boolean", "optional": true, "description": "Composition due to association with an element isolating compositing descendants having CSS blending applied."}, {"name": "perspective", "type": "boolean", "optional": true, "description": "Composition due to association with an element with perspective applied."}, {"name": "preserve3D", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a \"transform-style: preserve-3d\" style."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a \"will-change\" style."}, {"name": "root", "type": "boolean", "optional": true, "description": "Composition due to association with the root element."}, {"name": "blending", "type": "boolean", "optional": true, "description": "Composition due to association with an element with a \"blend-mode\" style."}, {"name": "backdropRoot", "type": "boolean", "optional": true, "description": "Composition due to association with an element that is a backdrop root"}]}], "commands": [{"name": "enable", "description": "Enables compositing tree inspection."}, {"name": "disable", "description": "Disables compositing tree inspection."}, {"name": "layersForNode", "description": "Returns the layer tree structure of the current page.", "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId", "description": "Root of the subtree for which we want to gather layers."}], "returns": [{"name": "layers", "type": "array", "items": {"$ref": "Layer"}, "description": "Child layers."}]}, {"name": "reasonsForCompositingLayer", "description": "Provides the reasons why the given layer was composited.", "parameters": [{"name": "layerId", "$ref": "LayerId", "description": "The id of the layer for which we want to get the reasons it was composited."}], "returns": [{"name": "compositingReasons", "$ref": "CompositingReasons", "description": "An object containing the reasons why the layer was composited as properties."}]}], "events": [{"name": "layerTreeDidChange"}]}, {"domain": "Memory", "description": "Memory domain exposes page memory tracking.", "condition": "defined(ENABLE_RESOURCE_USAGE) && ENABLE_RESOURCE_USAGE", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page"], "types": [{"id": "Event", "type": "object", "properties": [{"name": "timestamp", "type": "number"}, {"name": "categories", "type": "array", "items": {"$ref": "CategoryData"}, "description": "Breakdown of memory in categories."}]}, {"id": "CategoryData", "type": "object", "properties": [{"name": "type", "type": "string", "enum": ["javascript", "jit", "images", "layers", "page", "other"], "description": "Category type."}, {"name": "size", "type": "number", "description": "Category size in bytes."}]}], "commands": [{"name": "enable", "description": "Enables Memory domain events."}, {"name": "disable", "description": "Disables Memory domain events."}, {"name": "startTracking", "description": "Start tracking memory. This will produce a `trackingStart` event."}, {"name": "stopTracking", "description": "Stop tracking memory. This will produce a `trackingComplete` event."}], "events": [{"name": "memoryPressure", "description": "Memory pressure was encountered.", "parameters": [{"name": "timestamp", "type": "number"}, {"name": "severity", "type": "string", "enum": ["critical", "non-critical"], "description": "The severity of the memory pressure event."}]}, {"name": "trackingStart", "description": "Tracking started.", "parameters": [{"name": "timestamp", "type": "number"}]}, {"name": "trackingUpdate", "description": "Periodic tracking updates with event data.", "parameters": [{"name": "event", "$ref": "Event"}]}, {"name": "trackingComplete", "description": "Tracking stopped.", "parameters": [{"name": "timestamp", "type": "number"}]}]}, {"domain": "Network", "description": "Network domain allows tracking network activities of the page. It exposes information about http, file, data and other requests and responses, their headers, bodies, timing, etc.", "debuggableTypes": ["itml", "page", "service-worker", "web-page"], "targetTypes": ["itml", "page", "service-worker"], "types": [{"id": "LoaderId", "type": "string", "description": "Unique loader identifier."}, {"id": "FrameId", "type": "string", "description": "Unique frame identifier."}, {"id": "RequestId", "type": "string", "description": "Unique request identifier."}, {"id": "Timestamp", "type": "number", "description": "Elapsed seconds since frontend connected."}, {"id": "Walltime", "type": "number", "description": "Number of seconds since epoch."}, {"id": "ReferrerPolicy", "type": "string", "description": "Controls how much referrer information is sent with the request", "enum": ["empty-string", "no-referrer", "no-referrer-when-downgrade", "same-origin", "origin", "strict-origin", "origin-when-cross-origin", "strict-origin-when-cross-origin", "unsafe-url"]}, {"id": "Headers", "type": "object", "description": "Request / response headers as keys / values of JSON object."}, {"id": "ResourceTiming", "type": "object", "description": "Timing information for the request.", "properties": [{"name": "startTime", "$ref": "Timestamp", "description": "Request is initiated"}, {"name": "redirectStart", "$ref": "Timestamp", "description": "Started redirect resolution."}, {"name": "redirectEnd", "$ref": "Timestamp", "description": "Finished redirect resolution."}, {"name": "fetchStart", "$ref": "Timestamp", "description": "Resource fetching started."}, {"name": "domainLookupStart", "type": "number", "description": "Started DNS address resolve in milliseconds relative to fetchStart."}, {"name": "domainLookupEnd", "type": "number", "description": "Finished DNS address resolve in milliseconds relative to fetchStart."}, {"name": "connectStart", "type": "number", "description": "Started connecting to the remote host in milliseconds relative to fetchStart."}, {"name": "connectEnd", "type": "number", "description": "Connected to the remote host in milliseconds relative to fetchStart."}, {"name": "secureConnectionStart", "type": "number", "description": "Started SSL handshake in milliseconds relative to fetchStart."}, {"name": "requestStart", "type": "number", "description": "Started sending request in milliseconds relative to fetchStart."}, {"name": "responseStart", "type": "number", "description": "Started receiving response headers in milliseconds relative to fetchStart."}, {"name": "responseEnd", "type": "number", "description": "Finished receiving response headers in milliseconds relative to fetchStart."}]}, {"id": "Request", "type": "object", "description": "HTTP request data.", "properties": [{"name": "url", "type": "string", "description": "Request URL."}, {"name": "method", "type": "string", "description": "HTTP request method."}, {"name": "headers", "$ref": "Headers", "description": "HTTP request headers."}, {"name": "postData", "type": "string", "optional": true, "description": "HTTP POST request data."}, {"name": "referrerPolicy", "$ref": "ReferrerPolicy", "optional": true, "description": "The level of included referrer information."}, {"name": "integrity", "type": "string", "optional": true, "description": "The base64 cryptographic hash of the resource."}]}, {"id": "Response", "type": "object", "description": "HTTP response data.", "properties": [{"name": "url", "type": "string", "description": "Response URL. This URL can be different from CachedResource.url in case of redirect."}, {"name": "status", "type": "integer", "description": "HTTP response status code."}, {"name": "statusText", "type": "string", "description": "HTTP response status text."}, {"name": "headers", "$ref": "Headers", "description": "HTTP response headers."}, {"name": "mimeType", "type": "string", "description": "Resource mimeType as determined by the browser."}, {"name": "source", "type": "string", "enum": ["unknown", "network", "memory-cache", "disk-cache", "service-worker", "inspector-override"], "description": "Specifies where the response came from."}, {"name": "requestHeaders", "$ref": "Headers", "optional": true, "description": "Refined HTTP request headers that were actually transmitted over the network."}, {"name": "timing", "$ref": "ResourceTiming", "optional": true, "description": "Timing information for the given request."}, {"name": "security", "$ref": "Security.Security", "optional": true, "description": "The security information for the given request."}]}, {"id": "Metrics", "type": "object", "description": "Network load metrics.", "properties": [{"name": "protocol", "type": "string", "optional": true, "description": "Network protocol. ALPN Protocol ID Identification Sequence, as per RFC 7301 (for example, http/2, http/1.1, spdy/3.1)"}, {"name": "priority", "type": "string", "enum": ["low", "medium", "high"], "optional": true, "description": "Network priority."}, {"name": "connectionIdentifier", "type": "string", "optional": true, "description": "Connection identifier."}, {"name": "remoteAddress", "type": "string", "optional": true, "description": "Remote IP address."}, {"name": "requestHeaders", "$ref": "Headers", "optional": true, "description": "Refined HTTP request headers that were actually transmitted over the network."}, {"name": "requestHeaderBytesSent", "type": "number", "optional": true, "description": "Total HTTP request header bytes sent over the network."}, {"name": "requestBodyBytesSent", "type": "number", "optional": true, "description": "Total HTTP request body bytes sent over the network."}, {"name": "responseHeaderBytesReceived", "type": "number", "optional": true, "description": "Total HTTP response header bytes received over the network."}, {"name": "responseBodyBytesReceived", "type": "number", "optional": true, "description": "Total HTTP response body bytes received over the network."}, {"name": "responseBodyDecodedSize", "type": "number", "optional": true, "description": "Total decoded response body size in bytes."}, {"name": "securityConnection", "$ref": "Security.Connection", "optional": true, "description": "Connection information for the completed request."}, {"name": "isProxyConnection", "type": "boolean", "optional": true, "description": "Whether or not the connection was proxied through a server. If <code>true</code>, the <code>remoteAddress</code> will be for the proxy server, not the server that provided the resource to the proxy server."}]}, {"id": "WebSocketRequest", "type": "object", "description": "WebSocket request data.", "properties": [{"name": "headers", "$ref": "Headers", "description": "HTTP response headers."}]}, {"id": "WebSocketResponse", "type": "object", "description": "WebSocket response data.", "properties": [{"name": "status", "type": "integer", "description": "HTTP response status code."}, {"name": "statusText", "type": "string", "description": "HTTP response status text."}, {"name": "headers", "$ref": "Headers", "description": "HTTP response headers."}]}, {"id": "WebSocketFrame", "type": "object", "description": "WebSocket frame data.", "properties": [{"name": "opcode", "type": "number", "description": "WebSocket frame opcode."}, {"name": "mask", "type": "boolean", "description": "WebSocket frame mask."}, {"name": "payloadData", "type": "string", "description": "WebSocket frame payload data, binary frames (opcode = 2) are base64-encoded."}, {"name": "payloadLength", "type": "number", "description": "WebSocket frame payload length in bytes."}]}, {"id": "CachedResource", "type": "object", "description": "Information about the cached resource.", "properties": [{"name": "url", "type": "string", "description": "Resource URL. This is the url of the original network request."}, {"name": "type", "$ref": "Page.ResourceType", "description": "Type of this resource."}, {"name": "response", "$ref": "Response", "optional": true, "description": "Cached response data."}, {"name": "bodySize", "type": "number", "description": "Cached response body size."}, {"name": "sourceMapURL", "type": "string", "optional": true, "description": "URL of source map associated with this resource (if any)."}]}, {"id": "Initiator", "type": "object", "description": "Information about the request initiator.", "properties": [{"name": "type", "type": "string", "enum": ["parser", "script", "other"], "description": "Type of this initiator."}, {"name": "stackTrace", "$ref": "Console.StackTrace", "optional": true, "description": "Initiator JavaScript stack trace, set for Script only."}, {"name": "url", "type": "string", "optional": true, "description": "Initiator URL, set for Parser type only."}, {"name": "lineNumber", "type": "number", "optional": true, "description": "Initiator line number, set for Parser type only."}, {"name": "nodeId", "$ref": "DOM.NodeId", "optional": true, "description": "Set if the load was triggered by a DOM node, in addition to the other initiator information."}]}, {"id": "NetworkStage", "type": "string", "description": "Different stages of a network request.", "enum": ["request", "response"]}, {"id": "ResourceErrorType", "type": "string", "description": "Different stages of a network request.", "enum": ["General", "AccessControl", "Cancellation", "Timeout"]}], "commands": [{"name": "enable", "description": "Enables network tracking, network events will now be delivered to the client."}, {"name": "disable", "description": "Disables network tracking, prevents network events from being sent to the client."}, {"name": "setExtraHTTPHeaders", "description": "Specifies whether to always send extra HTTP headers with the requests from this page.", "targetTypes": ["page"], "parameters": [{"name": "headers", "$ref": "Headers", "description": "Map with extra HTTP headers."}]}, {"name": "getResponseBody", "description": "Returns content served for the given request.", "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier of the network request to get content for."}], "returns": [{"name": "body", "type": "string", "description": "Response body."}, {"name": "base64Encoded", "type": "boolean", "description": "True, if content was sent as base64."}]}, {"name": "setResourceCachingDisabled", "description": "Toggles whether the resource cache may be used when loading resources in the inspected page. If <code>true</code>, the resource cache will not be used when loading resources.", "parameters": [{"name": "disabled", "type": "boolean", "description": "Whether to prevent usage of the resource cache."}]}, {"name": "loadResource", "description": "Loads a resource in the context of a frame on the inspected page without cross origin checks.", "targetTypes": ["page"], "async": true, "parameters": [{"name": "frameId", "$ref": "FrameId", "description": "Frame to load the resource from."}, {"name": "url", "type": "string", "description": "URL of the resource to load."}], "returns": [{"name": "content", "type": "string", "description": "Resource content."}, {"name": "mimeType", "type": "string", "description": "Resource mimeType."}, {"name": "status", "type": "integer", "description": "HTTP response status code."}]}, {"name": "getSerializedCertificate", "description": "Fetches a serialized secure certificate for the given requestId to be displayed via InspectorFrontendHost.showCertificate.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId"}], "returns": [{"name": "serializedCertificate", "type": "string", "description": "Represents a base64 encoded WebCore::CertificateInfo object."}]}, {"name": "resolveWebSocket", "description": "Resolves JavaScript WebSocket object for given request id.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier of the WebSocket resource to resolve."}, {"name": "objectGroup", "type": "string", "optional": true, "description": "Symbolic group name that can be used to release multiple objects."}], "returns": [{"name": "object", "$ref": "Runtime.RemoteObject", "description": "JavaScript object wrapper for given node."}]}, {"name": "setInterceptionEnabled", "description": "Enable interception of network requests.", "targetTypes": ["page"], "parameters": [{"name": "enabled", "type": "boolean"}]}, {"name": "addInterception", "description": "Add an interception.", "targetTypes": ["page"], "parameters": [{"name": "url", "type": "string", "description": "URL pattern to intercept, intercept everything if not specified or empty"}, {"name": "stage", "$ref": "NetworkStage", "description": "Stage to intercept."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If false, ignores letter casing of `url` parameter."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats `url` parameter as a regular expression."}]}, {"name": "removeInterception", "description": "Remove an interception.", "targetTypes": ["page"], "parameters": [{"name": "url", "type": "string"}, {"name": "stage", "$ref": "NetworkStage", "description": "Stage to intercept."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If false, ignores letter casing of `url` parameter."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats `url` parameter as a regular expression."}]}, {"name": "interceptContinue", "description": "Continue request or response without modifications.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier for the intercepted Network request or response to continue."}, {"name": "stage", "$ref": "NetworkStage", "description": "Stage to continue."}]}, {"name": "interceptWithRequest", "description": "<PERSON><PERSON> intercepted request with the provided one.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier for the intercepted Network request or response to continue."}, {"name": "url", "type": "string", "optional": true, "description": "HTTP request url."}, {"name": "method", "type": "string", "optional": true, "description": "HTTP request method."}, {"name": "headers", "$ref": "Headers", "optional": true, "description": "HTTP response headers. Pass through original values if unmodified."}, {"name": "postData", "type": "string", "optional": true, "description": "HTTP POST request data, base64-encoded."}]}, {"name": "interceptWithResponse", "description": "Provide response content for an intercepted response.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier for the intercepted Network response to modify."}, {"name": "content", "type": "string"}, {"name": "base64Encoded", "type": "boolean", "description": "True, if content was sent as base64."}, {"name": "mimeType", "type": "string", "optional": true, "description": "MIME Type for the data."}, {"name": "status", "type": "integer", "optional": true, "description": "HTTP response status code. Pass through original values if unmodified."}, {"name": "statusText", "type": "string", "optional": true, "description": "HTTP response status text. Pass through original values if unmodified."}, {"name": "headers", "$ref": "Headers", "optional": true, "description": "HTTP response headers. Pass through original values if unmodified."}]}, {"name": "interceptRequestWithResponse", "description": "Provide response for an intercepted request. Request completely bypasses the network in this case and is immediately fulfilled with the provided data.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier for the intercepted Network response to modify."}, {"name": "content", "type": "string"}, {"name": "base64Encoded", "type": "boolean", "description": "True, if content was sent as base64."}, {"name": "mimeType", "type": "string", "description": "MIME Type for the data."}, {"name": "status", "type": "integer", "description": "HTTP response status code."}, {"name": "statusText", "type": "string", "description": "HTTP response status text."}, {"name": "headers", "$ref": "Headers", "description": "HTTP response headers."}]}, {"name": "interceptRequestWithError", "description": "Fail request with given error type.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier for the intercepted Network request to fail."}, {"name": "errorType", "$ref": "ResourceErrorType", "description": "Deliver error reason for the request failure."}]}, {"name": "setEmulatedConditions", "description": "Emulate various network conditions (e.g. bytes per second, latency, etc.).", "targetTypes": ["page"], "condition": "defined(ENABLE_INSPECTOR_NETWORK_THROTTLING) && ENABLE_INSPECTOR_NETWORK_THROTTLING", "parameters": [{"name": "bytesPerSecondLimit", "type": "integer", "optional": true, "description": "Limits the bytes per second of requests if positive. Removes any limits if zero or not provided."}]}, {"name": "setEmulateOfflineState", "description": "Emulate offline state overriding the actual state.", "parameters": [{"name": "offline", "type": "boolean", "description": "True to emulate offline."}]}], "events": [{"name": "requestWillBeSent", "description": "Fired when page is about to send HTTP request.", "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "frameId", "$ref": "FrameId", "description": "Frame identifier."}, {"name": "loaderId", "$ref": "LoaderId", "description": "Loader identifier."}, {"name": "documentURL", "type": "string", "description": "URL of the document this request is loaded for."}, {"name": "request", "$ref": "Request", "description": "Request data."}, {"name": "timestamp", "$ref": "Timestamp"}, {"name": "walltime", "$ref": "Walltime"}, {"name": "initiator", "$ref": "Initiator", "description": "Request initiator."}, {"name": "redirectResponse", "optional": true, "$ref": "Response", "description": "Redirect response data."}, {"name": "type", "$ref": "Page.ResourceType", "optional": true, "description": "Resource type."}, {"name": "targetId", "type": "string", "optional": true, "description": "Identifier for the context of where the load originated. In general this is the target identifier. For Workers this will be the workerId."}]}, {"name": "responseReceived", "description": "Fired when HTTP response is available.", "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "frameId", "$ref": "FrameId", "description": "Frame identifier."}, {"name": "loaderId", "$ref": "LoaderId", "description": "Loader identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "type", "$ref": "Page.ResourceType", "description": "Resource type."}, {"name": "response", "$ref": "Response", "description": "Response data."}]}, {"name": "dataReceived", "description": "Fired when data chunk was received over the network.", "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "dataLength", "type": "integer", "description": "Data chunk length."}, {"name": "encodedDataLength", "type": "integer", "description": "Actual bytes received (might be less than dataLength for compressed encodings)."}]}, {"name": "loadingFinished", "description": "Fired when HTTP request has finished loading.", "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "sourceMapURL", "type": "string", "optional": true, "description": "URL of source map associated with this resource (if any)."}, {"name": "metrics", "$ref": "Metrics", "optional": true, "description": "Network metrics."}]}, {"name": "loadingFailed", "description": "Fired when HTTP request has failed to load.", "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "errorText", "type": "string", "description": "User friendly error message."}, {"name": "canceled", "type": "boolean", "optional": true, "description": "True if loading was canceled."}]}, {"name": "requestServedFromMemoryCache", "description": "Fired when HTTP request has been served from memory cache.", "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "frameId", "$ref": "FrameId", "description": "Frame identifier."}, {"name": "loaderId", "$ref": "LoaderId", "description": "Loader identifier."}, {"name": "documentURL", "type": "string", "description": "URL of the document this request is loaded for."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "initiator", "$ref": "Initiator", "description": "Request initiator."}, {"name": "resource", "$ref": "CachedResource", "description": "Cached resource data."}]}, {"name": "requestIntercepted", "description": "Fired when HTTP request has been intercepted. The frontend must respond with <code>Network.interceptContinue</code>, <code>Network.interceptWithRequest</code>` or <code>Network.interceptWithResponse</code>` to resolve this request.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier for this intercepted network. Corresponds with an earlier <code>Network.requestWillBeSent</code>."}, {"name": "request", "$ref": "Request", "description": "Original request content that would proceed if this is continued."}]}, {"name": "responseIntercepted", "description": "Fired when HTTP response has been intercepted. The frontend must response with <code>Network.interceptContinue</code> or <code>Network.interceptWithResponse</code>` to continue this response.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Identifier for this intercepted network. Corresponds with an earlier <code>Network.requestWillBeSent</code>."}, {"name": "response", "$ref": "Response", "description": "Original response content that would proceed if this is continued."}]}, {"name": "webSocketWillSendHandshakeRequest", "description": "Fired when WebSocket is about to initiate handshake.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp"}, {"name": "walltime", "$ref": "Walltime"}, {"name": "request", "$ref": "WebSocketRequest", "description": "WebSocket request data."}]}, {"name": "webSocketHandshakeResponseReceived", "description": "Fired when WebSocket handshake response becomes available.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp"}, {"name": "response", "$ref": "WebSocketResponse", "description": "WebSocket response data."}]}, {"name": "webSocketCreated", "description": "Fired upon WebSocket creation.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "url", "type": "string", "description": "WebSocket request URL."}]}, {"name": "webSocketClosed", "description": "Fired when WebSocket is closed.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}]}, {"name": "webSocketFrameReceived", "description": "Fired when WebSocket frame is received.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "response", "$ref": "WebSocketFrame", "description": "WebSocket response data."}]}, {"name": "webSocketFrameError", "description": "Fired when WebSocket frame error occurs.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "errorMessage", "type": "string", "description": "WebSocket frame error message."}]}, {"name": "webSocketFrameSent", "description": "Fired when WebSocket frame is sent.", "targetTypes": ["page"], "parameters": [{"name": "requestId", "$ref": "RequestId", "description": "Request identifier."}, {"name": "timestamp", "$ref": "Timestamp", "description": "Timestamp."}, {"name": "response", "$ref": "WebSocketFrame", "description": "WebSocket response data."}]}]}, {"domain": "Page", "description": "Actions and events related to the inspected page belong to the page domain.", "debuggableTypes": ["itml", "page", "web-page"], "targetTypes": ["itml", "page"], "types": [{"id": "Setting", "type": "string", "description": "List of settings able to be overridden by WebInspector.", "enum": ["PrivateClickMeasurementDebugModeEnabled", "AuthorAndUserStylesEnabled", "ICECandidateFilteringEnabled", "ITPDebugModeEnabled", "ImagesEnabled", "MediaCaptureRequiresSecureConnection", "MockCaptureDevicesEnabled", "NeedsSiteSpecificQuirks", "<PERSON><PERSON>tEnabled", "ShowDebugBorders", "ShowRepaintCounter", "WebSecurityEnabled", "DeviceOrientationEventEnabled", "SpeechRecognitionEnabled", "Pointer<PERSON><PERSON><PERSON>nabled", "NotificationsEnabled", "FullScreenEnabled", "InputTypeMonthEnabled", "InputTypeWeekEnabled", "FixedBackgroundsPaintRelativeToDocument"]}, {"id": "UserPreference", "type": "object", "description": "A user preference that can be overriden by Web Inspector, like an accessibility preference.", "properties": [{"name": "name", "$ref": "UserPreferenceName", "description": "Preference name."}, {"name": "value", "$ref": "UserPreferenceValue", "description": "Preference value."}]}, {"id": "UserPreferenceName", "type": "string", "enum": ["PrefersReducedMotion", "PrefersContrast", "PrefersColorScheme"], "description": "User preference name."}, {"id": "UserPreferenceValue", "type": "string", "enum": ["NoPreference", "Reduce", "More", "Light", "Dark"], "description": "User preference value."}, {"id": "ResourceType", "type": "string", "enum": ["Document", "StyleSheet", "Image", "Font", "<PERSON><PERSON><PERSON>", "XHR", "<PERSON>tch", "<PERSON>", "Beacon", "WebSocket", "EventSource", "Other"], "description": "Resource type as it was perceived by the rendering engine."}, {"id": "CoordinateSystem", "type": "string", "enum": ["Viewport", "Page"], "description": "Coordinate system used by supplied coordinates."}, {"id": "CookieSameSitePolicy", "type": "string", "enum": ["None", "Lax", "Strict"], "description": "Same-Site policy of a cookie."}, {"id": "ForcedColors", "type": "string", "enum": ["Active", "None"], "description": "Page forced-colors media query override."}, {"id": "<PERSON>ame", "type": "object", "description": "Information about the Frame on the page.", "properties": [{"name": "id", "type": "string", "description": "Frame unique identifier."}, {"name": "parentId", "type": "string", "optional": true, "description": "Parent frame identifier."}, {"name": "loaderId", "$ref": "Network.LoaderId", "description": "Identifier of the loader associated with this frame."}, {"name": "name", "type": "string", "optional": true, "description": "<PERSON><PERSON>'s name as specified in the tag."}, {"name": "url", "type": "string", "description": "Frame document's URL."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "Frame document's security origin."}, {"name": "mimeType", "type": "string", "description": "Frame document's mimeType as determined by the browser."}]}, {"id": "FrameResource", "type": "object", "properties": [{"name": "url", "type": "string", "description": "Resource URL."}, {"name": "type", "$ref": "ResourceType", "description": "Type of this resource."}, {"name": "mimeType", "type": "string", "description": "Resource mimeType as determined by the browser."}, {"name": "failed", "type": "boolean", "optional": true, "description": "True if the resource failed to load."}, {"name": "canceled", "type": "boolean", "optional": true, "description": "True if the resource was canceled during loading."}, {"name": "sourceMapURL", "type": "string", "optional": true, "description": "URL of source map associated with this resource (if any)."}, {"name": "targetId", "type": "string", "optional": true, "description": "Identifier for the context of where the load originated. In general this is the target identifier. For Workers this will be the workerId."}]}, {"id": "FrameResourceTree", "type": "object", "description": "Information about the Frame hierarchy along with their cached resources.", "properties": [{"name": "frame", "$ref": "<PERSON>ame", "description": "Frame information for this tree item."}, {"name": "childFrames", "type": "array", "optional": true, "items": {"$ref": "FrameResourceTree"}, "description": "Child frames."}, {"name": "resources", "type": "array", "items": {"$ref": "FrameResource"}, "description": "Information about frame resources."}]}, {"id": "SearchResult", "type": "object", "description": "Search result for resource.", "properties": [{"name": "url", "type": "string", "description": "Resource URL."}, {"name": "frameId", "$ref": "Network.FrameId", "description": "Resource frame id."}, {"name": "matchesCount", "type": "number", "description": "Number of matches in the resource content."}, {"name": "requestId", "$ref": "Network.RequestId", "optional": true, "description": "Network request id."}]}, {"id": "<PERSON><PERSON>", "type": "object", "description": "<PERSON><PERSON> object", "properties": [{"name": "name", "type": "string", "description": "Cookie name."}, {"name": "value", "type": "string", "description": "Cookie value."}, {"name": "domain", "type": "string", "description": "Cookie domain."}, {"name": "path", "type": "string", "description": "<PERSON><PERSON> path."}, {"name": "expires", "type": "number", "description": "<PERSON><PERSON> expires."}, {"name": "session", "type": "boolean", "description": "True in case of session cookie."}, {"name": "httpOnly", "type": "boolean", "description": "True if cookie is http-only."}, {"name": "secure", "type": "boolean", "description": "True if cookie is secure."}, {"name": "sameSite", "$ref": "CookieSameSitePolicy", "description": "<PERSON><PERSON> Same-Site policy."}, {"name": "partition<PERSON>ey", "type": "string", "optional": true, "description": "<PERSON>ie partition key. If null and partitioned property is true, then key must be computed."}]}, {"id": "AXNode", "type": "object", "description": "Accessibility Node", "properties": [{"name": "role", "type": "string", "description": "The role."}, {"name": "name", "type": "string", "optional": true, "description": "A human readable name for the node."}, {"name": "value", "type": "any", "optional": true, "description": "The current value of the node."}, {"name": "description", "type": "string", "optional": true, "description": "An additional human readable description of the node."}, {"name": "keyshortcuts", "type": "string", "optional": true, "description": "Keyboard shortcuts associated with this node."}, {"name": "roledescription", "type": "string", "optional": true, "description": "A human readable alternative to the role."}, {"name": "valuetext", "type": "string", "optional": true, "description": "A description of the current value."}, {"name": "disabled", "type": "boolean", "optional": true, "description": "Whether the node is disabled."}, {"name": "expanded", "type": "boolean", "optional": true, "description": "Whether the node is expanded or collapsed."}, {"name": "focused", "type": "boolean", "optional": true, "description": "Whether the node is focused."}, {"name": "modal", "type": "boolean", "optional": true, "description": "Whether the node is modal."}, {"name": "multiselectable", "type": "boolean", "optional": true, "description": "Whether more than one child can be selected."}, {"name": "readonly", "type": "boolean", "optional": true, "description": "Whether the node is read only."}, {"name": "required", "type": "boolean", "optional": true, "description": "Whether the node is required."}, {"name": "selected", "type": "boolean", "optional": true, "description": "Whether the node is selected in its parent node."}, {"name": "checked", "type": "string", "optional": true, "enum": ["true", "false", "mixed"], "description": "Whether the checkbox is checked, or \"mixed\"."}, {"name": "pressed", "type": "string", "optional": true, "enum": ["true", "false", "mixed"], "description": "Whether the toggle button is checked, or \"mixed\"."}, {"name": "level", "type": "integer", "optional": true, "description": "The level of a heading."}, {"name": "valuemin", "type": "number", "optional": true, "description": "The minimum value in a node."}, {"name": "valuemax", "type": "number", "optional": true, "description": "The maximum value in a node."}, {"name": "autocomplete", "type": "string", "optional": true, "description": "What kind of autocomplete is supported by a control."}, {"name": "haspopup", "type": "string", "optional": true, "description": "What kind of popup is currently being shown for a node."}, {"name": "invalid", "type": "string", "optional": true, "enum": ["true", "false", "grammar", "spelling"], "description": "Whether and in what way this node's value is invalid."}, {"name": "orientation", "type": "string", "optional": true, "description": "Whether the node is oriented horizontally or vertically."}, {"name": "focusable", "type": "boolean", "optional": true, "description": "Whether the node is focusable."}, {"name": "children", "type": "array", "optional": true, "items": {"$ref": "AXNode"}, "description": "Child AXNodes of this node, if any."}, {"name": "found", "type": "boolean", "optional": true, "description": "True if this AXNode corresponds with the ObjectId passed into acessibilitySnapshot."}]}, {"id": "Insets", "type": "object", "properties": [{"name": "top", "type": "number"}, {"name": "right", "type": "number"}, {"name": "bottom", "type": "number"}, {"name": "left", "type": "number"}]}], "commands": [{"name": "enable", "description": "Enables page domain notifications."}, {"name": "disable", "description": "Disables page domain notifications."}, {"name": "reload", "description": "Reloads the main frame of the inspected page.", "parameters": [{"name": "ignoreCache", "type": "boolean", "optional": true, "description": "If true, the page is reloaded from its origin without using cached resources."}, {"name": "revalidateAllResources", "type": "boolean", "optional": true, "description": "If true, all cached subresources will be revalidated when the main resource loads. Otherwise, only expired cached subresources will be revalidated (the default behavior for most WebKit clients)."}]}, {"name": "goBack", "description": "Goes back in the history."}, {"name": "goForward", "description": "Goes forward in the history."}, {"name": "navigate", "description": "Navigates current page to the given URL.", "targetTypes": ["page"], "parameters": [{"name": "url", "type": "string", "description": "URL to navigate the page to."}]}, {"name": "overrideUserAgent", "description": "Override's the user agent of the inspected page", "targetTypes": ["page"], "parameters": [{"name": "value", "type": "string", "optional": true, "description": "Value to override the user agent with. If this value is not provided, the override is removed. Overrides are removed when Web Inspector closes/disconnects."}]}, {"name": "overridePlatform", "description": "Override's the navigator.platform of the inspected page", "targetTypes": ["page"], "parameters": [{"name": "value", "type": "string", "optional": true, "description": "Value to override the platform with. If this value is not provided, the override is removed. Overrides are removed when Web Inspector closes/disconnects."}]}, {"name": "overrideSetting", "description": "Allows the frontend to override the inspected page's settings.", "targetTypes": ["page"], "parameters": [{"name": "setting", "$ref": "Setting"}, {"name": "value", "type": "boolean", "optional": true, "description": "Value to override the setting with. If this value is not provided, the override is removed. Overrides are removed when Web Inspector closes/disconnects."}]}, {"name": "overrideUserPreference", "description": "Allows the frontend to override the user's preferences on the inspected page.", "targetTypes": ["page"], "parameters": [{"name": "name", "$ref": "UserPreferenceName"}, {"name": "value", "$ref": "UserPreferenceValue", "optional": true, "description": "Value to override the user preference with. If this value is not provided, the override is removed. Overrides are removed when Web Inspector closes/disconnects."}]}, {"name": "getCookies", "description": "Returns all browser cookies. Depending on the backend support, will return detailed cookie information in the <code>cookies</code> field.", "targetTypes": ["page"], "returns": [{"name": "cookies", "type": "array", "items": {"$ref": "<PERSON><PERSON>"}, "description": "Array of cookie objects."}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sets a new browser cookie with the given name, domain, and path.", "targetTypes": ["page"], "parameters": [{"name": "cookie", "$ref": "<PERSON><PERSON>"}, {"name": "shouldPartition", "type": "boolean", "optional": true, "description": "If true, then cookie's partition key should be set."}]}, {"name": "deleteC<PERSON>ie", "description": "Deletes browser cookie with given name, domain, and path.", "targetTypes": ["page"], "parameters": [{"name": "cookieName", "type": "string", "description": "Name of the cookie to remove."}, {"name": "url", "type": "string", "description": "URL to match cookie domain and path."}]}, {"name": "getResourceTree", "description": "Returns present frame / resource tree structure.", "returns": [{"name": "frameTree", "$ref": "FrameResourceTree", "description": "Present frame / resource tree structure."}]}, {"name": "getResourceContent", "description": "Returns content of the given resource.", "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Frame id to get resource for."}, {"name": "url", "type": "string", "description": "URL of the resource to get content for."}], "returns": [{"name": "content", "type": "string", "description": "Resource content."}, {"name": "base64Encoded", "type": "boolean", "description": "True, if content was served as base64."}]}, {"name": "setBootstrapScript", "targetTypes": ["page"], "parameters": [{"name": "source", "type": "string", "optional": true, "description": "If `source` is provided (and not empty), it will be injected into all future global objects as soon as they're created. Omitting `source` will stop this from happening."}]}, {"name": "searchInResource", "description": "Searches for given string in resource content.", "targetTypes": ["page"], "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Frame id for resource to search in."}, {"name": "url", "type": "string", "description": "URL of the resource to search in."}, {"name": "query", "type": "string", "description": "String to search for."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, search is case sensitive."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats string parameter as regex."}, {"name": "requestId", "$ref": "Network.RequestId", "optional": true, "description": "Request id for resource to search in."}], "returns": [{"name": "result", "type": "array", "items": {"$ref": "GenericTypes.SearchMatch"}, "description": "List of search matches."}]}, {"name": "searchInResources", "description": "Searches for given string in frame / resource tree structure.", "targetTypes": ["page"], "parameters": [{"name": "text", "type": "string", "description": "String to search for."}, {"name": "caseSensitive", "type": "boolean", "optional": true, "description": "If true, search is case sensitive."}, {"name": "isRegex", "type": "boolean", "optional": true, "description": "If true, treats string parameter as regex."}], "returns": [{"name": "result", "type": "array", "items": {"$ref": "SearchResult"}, "description": "List of search results."}]}, {"name": "setShowRulers", "description": "Requests that backend draw rulers in the inspector overlay", "condition": "!(defined(WTF_PLATFORM_IOS_FAMILY) && WTF_PLATFORM_IOS_FAMILY)", "targetTypes": ["page"], "parameters": [{"name": "result", "type": "boolean", "description": "True for showing rulers"}]}, {"name": "setShowPaintRects", "description": "Requests that backend shows paint rectangles", "targetTypes": ["page"], "parameters": [{"name": "result", "type": "boolean", "description": "True for showing paint rectangles"}]}, {"name": "setEmulatedMedia", "description": "Emulates the given media for CSS media queries.", "targetTypes": ["page"], "parameters": [{"name": "media", "type": "string", "description": "Media type to emulate. Empty string disables the override."}]}, {"name": "setForcedColors", "description": "Forces the forced-colors media query for the page.", "targetTypes": ["page"], "parameters": [{"name": "forcedColors", "$ref": "ForcedColors", "optional": true}]}, {"name": "setTimeZone", "description": "Enables time zone emulation.", "parameters": [{"name": "timeZone", "type": "string", "optional": true}]}, {"name": "setTouchEmulationEnabled", "description": "Enables touch events on platforms that lack them.", "parameters": [{"name": "enabled", "type": "boolean", "description": "Whether touch should be enabled."}]}, {"name": "snapshotNode", "description": "Capture a snapshot of the specified node that does not include unrelated layers.", "targetTypes": ["page"], "parameters": [{"name": "nodeId", "$ref": "DOM.NodeId", "description": "Id of the node to snapshot."}], "returns": [{"name": "dataURL", "type": "string", "description": "Base64-encoded image data (PNG)."}]}, {"name": "snapshotRect", "description": "Capture a snapshot of the page within the specified rectangle and coordinate system.", "targetTypes": ["page"], "parameters": [{"name": "x", "type": "integer", "description": "X coordinate"}, {"name": "y", "type": "integer", "description": "Y coordinate"}, {"name": "width", "type": "integer", "description": "Rectangle width"}, {"name": "height", "type": "integer", "description": "Rectangle height"}, {"name": "coordinateSystem", "$ref": "CoordinateSystem", "description": "Indicates the coordinate system of the supplied rectangle."}, {"name": "omitDeviceScaleFactor", "type": "boolean", "optional": true, "description": "By default, screenshot is inflated by device scale factor to avoid blurry image. This flag disables it."}], "returns": [{"name": "dataURL", "type": "string", "description": "Base64-encoded image data (PNG)."}]}, {"name": "archive", "description": "Grab an archive of the page.", "condition": "(defined(ENABLE_WEB_ARCHIVE) && ENABLE_WEB_ARCHIVE) && (defined(USE_CF) && USE_CF)", "targetTypes": ["page"], "returns": [{"name": "data", "type": "string", "description": "Base64-encoded web archive."}]}, {"name": "setScreenSizeOverride", "description": "Overrides screen size exposed to DOM and used in media queries for testing with provided values.", "targetTypes": ["page"], "parameters": [{"name": "width", "type": "integer", "description": "Screen width", "optional": true}, {"name": "height", "type": "integer", "description": "Screen height", "optional": true}]}, {"name": "insertText", "description": "Insert text into the current selection of the page.", "parameters": [{"name": "text", "type": "string", "description": "Text to insert."}]}, {"name": "accessibilitySnapshot", "description": "Serializes and returns all of the accessibility nodes of the page.", "parameters": [{"name": "objectId", "type": "string", "optional": true, "description": "Object Id of a node to find in the accessibility tree."}], "returns": [{"name": "axNode", "$ref": "AXNode", "description": "The root AXNode."}]}, {"name": "setInterceptFileChooserDialog", "description": "Intercepts file chooser dialog", "parameters": [{"name": "enabled", "type": "boolean", "description": "True to enable."}]}, {"name": "setDefaultBackgroundColorOverride", "description": "Sets or clears an override of the default background color of the frame. This override is used if the content does not specify one.", "parameters": [{"name": "color", "$ref": "DOM.RGBAColor", "optional": true, "description": "RGBA of the default background color. If not specified, any existing override will be cleared."}]}, {"name": "createUserWorld", "description": "Creates an user world for every loaded frame.", "parameters": [{"name": "name", "type": "string", "description": "Isolated world name, will be used as an execution context name."}]}, {"name": "setBypassCSP", "description": "Enable page Content Security Policy by-passing.", "parameters": [{"name": "enabled", "type": "boolean", "description": "Whether to bypass page CSP."}]}, {"name": "crash", "description": "Crashes the page process"}, {"name": "updateScrollingState", "description": "Ensures that the scroll regions are up to date."}], "events": [{"name": "domContentEventFired", "targetTypes": ["page"], "parameters": [{"name": "timestamp", "type": "number"}, {"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has fired DOMContentLoaded event."}]}, {"name": "loadEventFired", "targetTypes": ["page"], "parameters": [{"name": "timestamp", "type": "number"}, {"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has fired load event."}]}, {"name": "frameNavigated", "description": "Fired once navigation of the frame has completed. Frame is now associated with the new loader.", "parameters": [{"name": "frame", "$ref": "<PERSON>ame", "description": "Frame object."}]}, {"name": "frameAttached", "description": "Fired when frame has been attached to its parent.", "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has been detached."}, {"name": "parentFrameId", "$ref": "Network.FrameId", "optional": true, "description": "Parent frame id if non-root."}]}, {"name": "frameDetached", "description": "Fired when frame has been detached from its parent.", "targetTypes": ["page"], "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has been detached."}]}, {"name": "frameStartedLoading", "description": "Fired when frame has started loading.", "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has started loading."}]}, {"name": "frameStoppedLoading", "description": "Fired when frame has stopped loading.", "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has stopped loading."}]}, {"name": "frameScheduledNavigation", "description": "Fired when frame schedules a potential navigation.", "targetTypes": ["page"], "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has scheduled a navigation."}, {"name": "delay", "type": "number", "description": "Delay (in seconds) until the navigation is scheduled to begin. The navigation is not guaranteed to start."}, {"name": "targetIsCurrentFrame", "type": "boolean", "description": "Whether the naviation will happen in the same frame."}]}, {"name": "frameClearedScheduledNavigation", "description": "Fired when frame no longer has a scheduled navigation.", "targetTypes": ["page"], "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the frame that has cleared its scheduled navigation."}]}, {"name": "navigatedWithinDocument", "description": "Fired when same-document navigation happens, e.g. due to history API usage or anchor navigation.", "parameters": [{"name": "frameId", "description": "Id of the frame.", "$ref": "Network.FrameId"}, {"name": "url", "description": "<PERSON><PERSON>'s new url.", "type": "string"}]}, {"name": "defaultUserPreferencesDidChange", "description": "Fired when the default value of a user preference changes at the system level.", "targetTypes": ["page"], "parameters": [{"name": "preferences", "type": "array", "items": {"$ref": "UserPreference"}, "description": "List of user preferences that can be overriden and their new system (default) values."}]}, {"name": "willCheckNavigationPolicy", "description": "Fired when page is about to check policy for newly triggered navigation.", "parameters": [{"name": "frameId", "description": "Id of the frame.", "$ref": "Network.FrameId"}]}, {"name": "didCheckNavigationPolicy", "description": "Fired when page has received navigation policy decision.", "parameters": [{"name": "frameId", "description": "Id of the frame.", "$ref": "Network.FrameId"}, {"name": "cancel", "description": "True if the navigation will not continue in this frame.", "type": "boolean", "optional": true}]}, {"name": "fileChooserOpened", "description": "Fired when the page shows file chooser for it's <input type=file>.", "parameters": [{"name": "frameId", "$ref": "Network.FrameId", "description": "Frame where file chooser is opened."}, {"name": "element", "$ref": "Runtime.RemoteObject", "description": "Input element."}]}]}, {"domain": "Playwright", "availability": ["web"], "types": [{"id": "ContextID", "type": "string", "description": "Id of Browser context."}, {"id": "PageProxyID", "type": "string", "description": "Id of WebPageProxy."}, {"id": "CookieSameSitePolicy", "type": "string", "enum": ["None", "Lax", "Strict"], "description": "Same-Site policy of a cookie."}, {"id": "<PERSON><PERSON>", "type": "object", "description": "<PERSON><PERSON> object", "properties": [{"name": "name", "type": "string", "description": "Cookie name."}, {"name": "value", "type": "string", "description": "Cookie value."}, {"name": "domain", "type": "string", "description": "Cookie domain."}, {"name": "path", "type": "string", "description": "<PERSON><PERSON> path."}, {"name": "expires", "type": "number", "description": "<PERSON><PERSON> expires."}, {"name": "httpOnly", "type": "boolean", "description": "True if cookie is http-only."}, {"name": "secure", "type": "boolean", "description": "True if cookie is secure."}, {"name": "session", "type": "boolean", "description": "True if cookie is session cookie."}, {"name": "sameSite", "$ref": "CookieSameSitePolicy", "description": "<PERSON><PERSON> Same-Site policy."}]}, {"id": "SetCookieParam", "type": "object", "description": "<PERSON><PERSON> object", "properties": [{"name": "name", "type": "string", "description": "Cookie name."}, {"name": "value", "type": "string", "description": "Cookie value."}, {"name": "domain", "type": "string", "description": "Cookie domain."}, {"name": "path", "type": "string", "description": "<PERSON><PERSON> path."}, {"name": "expires", "type": "number", "optional": true, "description": "<PERSON><PERSON> expires."}, {"name": "httpOnly", "type": "boolean", "optional": true, "description": "True if cookie is http-only."}, {"name": "secure", "type": "boolean", "optional": true, "description": "True if cookie is secure."}, {"name": "session", "type": "boolean", "optional": true, "description": "True if cookie is session cookie."}, {"name": "sameSite", "$ref": "CookieSameSitePolicy", "optional": true, "description": "<PERSON><PERSON> Same-Site policy."}]}, {"id": "NameValue", "type": "object", "description": "Name-value pair", "properties": [{"name": "name", "type": "string"}, {"name": "value", "type": "string"}]}, {"id": "OriginStorage", "type": "object", "description": "Origin object", "properties": [{"name": "origin", "type": "string", "description": "Origin."}, {"name": "items", "type": "array", "items": {"$ref": "NameValue"}, "description": "Storage entries."}]}, {"id": "Geolocation", "type": "object", "description": "Geolocation", "properties": [{"name": "timestamp", "type": "number", "description": "Mock latitude"}, {"name": "latitude", "type": "number", "description": "Mock latitude"}, {"name": "longitude", "type": "number", "description": "Mock longitude"}, {"name": "accuracy", "type": "number", "description": "Mock accuracy"}]}], "commands": [{"name": "enable"}, {"name": "disable"}, {"name": "getInfo", "returns": [{"name": "os", "type": "string", "description": "Name of the operating system where the browser is running (macOS, Linux or Windows)."}]}, {"name": "close", "async": true, "description": "Close browser."}, {"name": "createContext", "description": "Creates new ephemeral browser context.", "parameters": [{"name": "proxyServer", "type": "string", "optional": true, "description": "Proxy server, similar to the one passed to --proxy-server"}, {"name": "proxyBypassList", "type": "string", "optional": true, "description": "Proxy bypass list, similar to the one passed to --proxy-bypass-list"}], "returns": [{"name": "browserContextId", "$ref": "ContextID", "description": "Unique identifier of the context."}]}, {"name": "deleteContext", "async": true, "description": "Deletes browser context previously created with createContect. The command will automatically close all pages that use the context.", "parameters": [{"name": "browserContextId", "$ref": "ContextID", "description": "Identifier of the context to delete."}]}, {"name": "createPage", "parameters": [{"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "JSON Inspector Protocol message (command) to be dispatched on the backend."}], "returns": [{"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}]}, {"name": "navigate", "async": true, "description": "Navigates current page to the given URL.", "parameters": [{"name": "url", "type": "string", "description": "URL to navigate the page to."}, {"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}, {"name": "frameId", "$ref": "Network.FrameId", "optional": true, "description": "Id of the frame to navigate."}, {"name": "referrer", "type": "string", "optional": true, "description": "Referrer URL."}], "returns": [{"name": "loaderId", "$ref": "Network.LoaderId", "optional": true, "description": "Identifier of the loader associated with the navigation."}]}, {"name": "grantFileReadAccess", "description": "Grants read access for the specified files to the web process of the page.", "parameters": [{"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}, {"name": "paths", "type": "array", "items": {"type": "string"}, "description": "Id of the frame to navigate."}]}, {"name": "takePageScreenshot", "description": "Capture a snapshot of the page.", "async": true, "parameters": [{"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}, {"name": "x", "type": "integer", "description": "X coordinate"}, {"name": "y", "type": "integer", "description": "Y coordinate"}, {"name": "width", "type": "integer", "description": "Rectangle width"}, {"name": "height", "type": "integer", "description": "Rectangle height"}, {"name": "omitDeviceScaleFactor", "type": "boolean", "optional": true, "description": "By default, screenshot is inflated by device scale factor to avoid blurry image. This flag disables it."}], "returns": [{"name": "dataURL", "type": "string", "description": "Base64-encoded image data (PNG)."}]}, {"name": "setIgnoreCertificateErrors", "description": "Change whether all certificate errors should be ignored.", "parameters": [{"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "Browser context id."}, {"name": "ignore", "type": "boolean"}]}, {"name": "setPageZoomFactor", "description": "Changes page zoom factor.", "parameters": [{"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}, {"name": "zoomFactor", "type": "number"}]}, {"name": "getAllCookies", "description": "Returns all cookies in the given browser context.", "async": true, "parameters": [{"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "Browser context id."}], "returns": [{"name": "cookies", "type": "array", "items": {"$ref": "<PERSON><PERSON>"}, "description": "Cookies."}]}, {"name": "setCookies", "description": "Sets cookies in the given browser context.", "async": true, "parameters": [{"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "Browser context id."}, {"name": "cookies", "type": "array", "items": {"$ref": "SetCookieParam"}, "description": "Cookies."}]}, {"name": "deleteAllCookies", "description": "Deletes cookies in the given browser context.", "async": true, "parameters": [{"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "Browser context id."}]}, {"name": "setGeolocationOverride", "parameters": [{"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "Browser context id."}, {"name": "geolocation", "$ref": "Geolocation", "optional": true, "description": "Geolocation to set, if missing emulates position unavailable."}], "description": "Overrides the geolocation position or error."}, {"name": "setLanguages", "description": "Allows to set locale language for context.", "parameters": [{"name": "languages", "type": "array", "items": {"type": "string"}}, {"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "Browser context id."}]}, {"name": "setDownloadBehavior", "description": "Allows to override download behavior.", "parameters": [{"name": "behavior", "optional": true, "type": "string", "enum": ["allow", "deny"]}, {"name": "downloadPath", "optional": true, "type": "string"}, {"name": "browserContextId", "$ref": "ContextID", "optional": true, "description": "Browser context id."}]}, {"name": "cancelDownload", "parameters": [{"name": "uuid", "type": "string"}], "description": "Cancels a current running download."}, {"name": "clearMemoryCache", "description": "Clears browser memory cache.", "async": true, "parameters": [{"name": "browserContextId", "$ref": "ContextID", "optional": false, "description": "Browser context id."}]}], "events": [{"name": "pageProxyCreated", "parameters": [{"name": "browserContextId", "$ref": "ContextID", "description": "Unique identifier of the context."}, {"name": "pageProxyId", "$ref": "PageProxyID"}, {"name": "openerId", "$ref": "PageProxyID", "optional": true, "description": "Unique identifier of the opening page. Only set for pages created by window.open()."}]}, {"name": "pageProxyDestroyed", "parameters": [{"name": "pageProxyId", "$ref": "PageProxyID"}]}, {"name": "provisionalLoadFailed", "description": "Fired when provisional load fails.", "parameters": [{"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}, {"name": "loaderId", "$ref": "Network.LoaderId", "description": "Identifier of the loader associated with the navigation."}, {"name": "error", "type": "string", "description": "Localized error string."}]}, {"name": "windowOpen", "description": "Fired when page opens a new window.", "parameters": [{"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}, {"name": "url", "type": "string"}, {"name": "windowFeatures", "type": "array", "items": {"type": "string"}}]}, {"name": "downloadCreated", "parameters": [{"name": "pageProxyId", "$ref": "PageProxyID", "description": "Unique identifier of the page proxy."}, {"name": "frameId", "$ref": "Network.FrameId", "description": "Unique identifier of the originating frame."}, {"name": "uuid", "type": "string"}, {"name": "url", "type": "string"}]}, {"name": "downloadFilenameSuggested", "parameters": [{"name": "uuid", "type": "string"}, {"name": "suggestedFilename", "type": "string"}]}, {"name": "downloadFinished", "parameters": [{"name": "uuid", "type": "string"}, {"name": "error", "type": "string"}]}, {"name": "screencastFinished", "parameters": [{"name": "screencastId", "$ref": "Screencast.ScreencastId", "description": "Unique identifier of the screencast."}]}]}, {"domain": "Recording", "description": "General types used for recordings of actions performed in the inspected page.", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page", "worker"], "version": 2, "types": [{"id": "Type", "type": "string", "enum": ["canvas-2d", "offscreen-canvas-2d", "canvas-bitmaprenderer", "offscreen-canvas-bitmaprenderer", "canvas-webgl", "offscreen-canvas-webgl", "canvas-webgl2", "offscreen-canvas-webgl2"], "description": "The type of the recording."}, {"id": "Initiator", "type": "string", "enum": ["frontend", "console", "auto-capture"]}, {"id": "InitialState", "type": "object", "description": "Information about the initial state of the recorded object.", "properties": [{"name": "attributes", "type": "object", "optional": true, "description": "Key-value map for each attribute of the state."}, {"name": "states", "type": "array", "items": {"type": "object"}, "optional": true, "description": "Array of saved states of the context."}, {"name": "parameters", "type": "array", "items": {"type": "any"}, "optional": true, "description": "Array of values that were used to construct the recorded object."}, {"name": "content", "type": "string", "optional": true, "description": "Current content at the start of the recording."}]}, {"id": "<PERSON>ame", "type": "object", "description": "Container object for a single frame of the recording.", "properties": [{"name": "actions", "type": "array", "items": {"type": "any"}, "description": "Information about an action made to the recorded object. Follows the structure [name, parameters, swizzleTypes, stackTrace, snapshot], where name is a string, parameters is an array, swizzleTypes is an array, stackTrace is a Console.StackTrace, and snapshot is a data URL image of the current contents after this action."}, {"name": "duration", "type": "number", "optional": true, "description": "Total execution time of all actions recorded in this frame in milliseconds. "}, {"name": "incomplete", "type": "boolean", "optional": true, "description": "Flag indicating if the recording was stopped before this frame ended."}]}, {"id": "Recording", "type": "object", "properties": [{"name": "version", "type": "integer", "description": "Used for future/backwards compatibility."}, {"name": "type", "$ref": "Type"}, {"name": "initialState", "$ref": "InitialState", "description": "JSON data of inital state of object before recording."}, {"name": "data", "type": "array", "items": {"type": "any"}, "description": "Array of objects that can be referenced by index. Used to avoid duplicating objects."}, {"name": "name", "type": "string", "optional": true}]}]}, {"domain": "Runtime", "description": "Runtime domain exposes JavaScript runtime by means of remote evaluation and mirror objects. Evaluation results are returned as mirror object that expose object type, string representation and unique identifier that can be used for further object reference. Original objects are maintained in memory unless they are either explicitly released or are released along with the other objects in their object group.", "debuggableTypes": ["itml", "javascript", "page", "service-worker", "web-page"], "targetTypes": ["itml", "javascript", "page", "service-worker", "worker"], "types": [{"id": "RemoteObjectId", "type": "string", "description": "Unique object identifier."}, {"id": "RemoteObject", "type": "object", "description": "Mirror object referencing original JavaScript object.", "properties": [{"name": "type", "type": "string", "enum": ["object", "function", "undefined", "string", "number", "boolean", "symbol", "bigint"], "description": "Object type."}, {"name": "subtype", "type": "string", "optional": true, "enum": ["array", "null", "node", "regexp", "date", "error", "map", "set", "weakmap", "weakset", "iterator", "class", "proxy", "weakref"], "description": "Object subtype hint. Specified for <code>object</code> <code>function</code> (for class) type values only."}, {"name": "className", "type": "string", "optional": true, "description": "Object class (constructor) name. Specified for <code>object</code> type values only."}, {"name": "value", "type": "any", "optional": true, "description": "Remote object value (in case of primitive values or JSON values if it was requested)."}, {"name": "description", "type": "string", "optional": true, "description": "String representation of the object."}, {"name": "objectId", "$ref": "RemoteObjectId", "optional": true, "description": "Unique object identifier (for non-primitive values)."}, {"name": "size", "type": "integer", "optional": true, "description": "Size of the array/collection. Specified for array/map/set/weakmap/weakset object type values only."}, {"name": "classPrototype", "$ref": "RemoteObject", "optional": true, "description": "Remote object for the class prototype. Specified for class object type values only."}, {"name": "preview", "$ref": "ObjectPreview", "optional": true, "description": "Preview containing abbreviated property values. Specified for <code>object</code> type values only."}]}, {"id": "ObjectPreview", "type": "object", "description": "Object containing abbreviated remote object value.", "properties": [{"name": "type", "type": "string", "enum": ["object", "function", "undefined", "string", "number", "boolean", "symbol", "bigint"], "description": "Object type."}, {"name": "subtype", "type": "string", "optional": true, "enum": ["array", "null", "node", "regexp", "date", "error", "map", "set", "weakmap", "weakset", "iterator", "class", "proxy", "weakref"], "description": "Object subtype hint. Specified for <code>object</code> type values only."}, {"name": "description", "type": "string", "optional": true, "description": "String representation of the object."}, {"name": "lossless", "type": "boolean", "description": "Determines whether preview is lossless (contains all information of the original object)."}, {"name": "overflow", "type": "boolean", "optional": true, "description": "True iff some of the properties of the original did not fit."}, {"name": "properties", "type": "array", "items": {"$ref": "PropertyPreview"}, "optional": true, "description": "List of the properties."}, {"name": "entries", "type": "array", "items": {"$ref": "EntryPreview"}, "optional": true, "description": "List of the entries. Specified for <code>map</code> and <code>set</code> subtype values only."}, {"name": "size", "type": "integer", "optional": true, "description": "Size of the array/collection. Specified for array/map/set/weakmap/weakset object type values only."}]}, {"id": "PropertyPreview", "type": "object", "properties": [{"name": "name", "type": "string", "description": "Property name."}, {"name": "type", "type": "string", "enum": ["object", "function", "undefined", "string", "number", "boolean", "symbol", "bigint", "accessor"], "description": "Object type."}, {"name": "subtype", "type": "string", "optional": true, "enum": ["array", "null", "node", "regexp", "date", "error", "map", "set", "weakmap", "weakset", "iterator", "class", "proxy", "weakref"], "description": "Object subtype hint. Specified for <code>object</code> type values only."}, {"name": "value", "type": "string", "optional": true, "description": "User-friendly property value string."}, {"name": "valuePreview", "$ref": "ObjectPreview", "optional": true, "description": "Nested value preview."}, {"name": "isPrivate", "type": "boolean", "optional": true, "description": "True if this is a private field."}, {"name": "internal", "type": "boolean", "optional": true, "description": "True if this is an internal property."}]}, {"id": "EntryPreview", "type": "object", "properties": [{"name": "key", "$ref": "ObjectPreview", "optional": true, "description": "Entry key. Specified for map-like collection entries."}, {"name": "value", "$ref": "ObjectPreview", "description": "Entry value."}]}, {"id": "CollectionEntry", "type": "object", "properties": [{"name": "key", "$ref": "Runtime.RemoteObject", "optional": true, "description": "Entry key of a map-like collection, otherwise not provided."}, {"name": "value", "$ref": "Runtime.RemoteObject", "description": "Entry value."}]}, {"id": "PropertyDescriptor", "type": "object", "description": "Object property descriptor.", "properties": [{"name": "name", "type": "string", "description": "Property name or symbol description."}, {"name": "value", "$ref": "RemoteObject", "optional": true, "description": "The value associated with the property."}, {"name": "writable", "type": "boolean", "optional": true, "description": "True if the value associated with the property may be changed (data descriptors only)."}, {"name": "get", "$ref": "RemoteObject", "optional": true, "description": "A function which serves as a getter for the property, or <code>undefined</code> if there is no getter (accessor descriptors only)."}, {"name": "set", "$ref": "RemoteObject", "optional": true, "description": "A function which serves as a setter for the property, or <code>undefined</code> if there is no setter (accessor descriptors only)."}, {"name": "wasThrown", "type": "boolean", "optional": true, "description": "True if the result was thrown during the evaluation."}, {"name": "configurable", "type": "boolean", "optional": true, "description": "True if the type of this property descriptor may be changed and if the property may be deleted from the corresponding object."}, {"name": "enumerable", "type": "boolean", "optional": true, "description": "True if this property shows up during enumeration of the properties on the corresponding object."}, {"name": "isOwn", "optional": true, "type": "boolean", "description": "True if the property is owned for the object."}, {"name": "symbol", "optional": true, "$ref": "Runtime.RemoteObject", "description": "Property symbol object, if the property is a symbol."}, {"name": "isPrivate", "optional": true, "$ref": "boolean", "description": "True if the property is a private field."}, {"name": "nativeGetter", "optional": true, "type": "boolean", "description": "True if the property value came from a native getter."}]}, {"id": "InternalPropertyDescriptor", "type": "object", "description": "Object internal property descriptor. This property isn't normally visible in JavaScript code.", "properties": [{"name": "name", "type": "string", "description": "Conventional property name."}, {"name": "value", "$ref": "RemoteObject", "optional": true, "description": "The value associated with the property."}]}, {"id": "CallArgument", "type": "object", "description": "Represents function call argument. Either remote object id <code>objectId</code> or primitive <code>value</code> or neither of (for undefined) them should be specified.", "properties": [{"name": "value", "type": "any", "optional": true, "description": "Primitive value."}, {"name": "objectId", "$ref": "RemoteObjectId", "optional": true, "description": "Remote object handle."}]}, {"id": "ExecutionContextId", "type": "integer", "description": "Id of an execution context."}, {"id": "ExecutionContextType", "type": "string", "enum": ["normal", "user", "internal"], "description": "Type of the execution context."}, {"id": "ExecutionContextDescription", "type": "object", "description": "Description of an isolated world.", "properties": [{"name": "id", "$ref": "ExecutionContextId", "description": "Unique id of the execution context. It can be used to specify in which execution context script evaluation should be performed."}, {"name": "type", "$ref": "ExecutionContextType"}, {"name": "name", "type": "string", "description": "Human readable name describing given context."}, {"name": "frameId", "$ref": "Network.FrameId", "description": "Id of the owning frame."}]}, {"id": "SyntaxErrorType", "type": "string", "enum": ["none", "irrecoverable", "unterminated-literal", "recoverable"], "description": "Syntax error type: \"none\" for no error, \"irrecoverable\" for unrecoverable errors, \"unterminated-literal\" for when there is an unterminated literal, \"recoverable\" for when the expression is unfinished but valid so far."}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "description": "Range of an error in source code.", "properties": [{"name": "startOffset", "type": "integer", "description": "Start offset of range (inclusive)."}, {"name": "endOffset", "type": "integer", "description": "End offset of range (exclusive)."}]}, {"id": "StructureDescription", "type": "object", "properties": [{"name": "fields", "type": "array", "items": {"type": "string"}, "optional": true, "description": "Array of strings, where the strings represent object properties."}, {"name": "optionalFields", "type": "array", "items": {"type": "string"}, "optional": true, "description": "Array of strings, where the strings represent optional object properties."}, {"name": "constructorName", "type": "string", "optional": true, "description": "Name of the constructor."}, {"name": "prototypeStructure", "$ref": "StructureDescription", "optional": true, "description": "Pointer to the StructureRepresentation of the protoype if one exists."}, {"name": "isImprecise", "type": "boolean", "optional": true, "description": "If true, it indicates that the fields in this StructureDescription may be inaccurate. I.e, there might have been fields that have been deleted before it was profiled or it has fields we haven't profiled."}]}, {"id": "TypeSet", "type": "object", "properties": [{"name": "isFunction", "type": "boolean", "description": "Indicates if this type description has been type Function."}, {"name": "isUndefined", "type": "boolean", "description": "Indicates if this type description has been type Undefined."}, {"name": "isNull", "type": "boolean", "description": "Indicates if this type description has been type Null."}, {"name": "isBoolean", "type": "boolean", "description": "Indicates if this type description has been type Boolean."}, {"name": "isInteger", "type": "boolean", "description": "Indicates if this type description has been type Integer."}, {"name": "isNumber", "type": "boolean", "description": "Indicates if this type description has been type Number."}, {"name": "isString", "type": "boolean", "description": "Indicates if this type description has been type String."}, {"name": "isObject", "type": "boolean", "description": "Indicates if this type description has been type Object."}, {"name": "isSymbol", "type": "boolean", "description": "Indicates if this type description has been type Symbol."}, {"name": "isBigInt", "type": "boolean", "description": "Indicates if this type description has been type BigInt."}]}, {"id": "TypeDescription", "type": "object", "description": "Container for type information that has been gathered.", "properties": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "boolean", "description": "If true, we were able to correlate the offset successfuly with a program location. If false, the offset may be bogus or the offset may be from a CodeBlock that hasn't executed."}, {"name": "leastCommonAncestor", "type": "string", "optional": true, "description": "Least common ancestor of all Constructors if the TypeDescription has seen any structures. This string is the display name of the shared constructor function."}, {"name": "typeSet", "$ref": "TypeSet", "optional": true, "description": "Set of booleans for determining the aggregate type of this type description."}, {"name": "structures", "type": "array", "items": {"$ref": "StructureDescription"}, "optional": true, "description": "Array of descriptions for all structures seen for this variable."}, {"name": "isTruncated", "type": "boolean", "optional": true, "description": "If true, this indicates that no more structures are being profiled because some maximum threshold has been reached and profiling has stopped because of memory pressure."}]}, {"id": "TypeLocation", "type": "object", "description": "Describes the location of an expression we want type information for.", "properties": [{"name": "typeInformationDescriptor", "type": "integer", "description": "What kind of type information do we want (normal, function return values, 'this' statement)."}, {"name": "sourceID", "type": "string", "description": "sourceID uniquely identifying a script"}, {"name": "divot", "type": "integer", "description": "character offset for assignment range"}]}, {"id": "BasicBlock", "type": "object", "description": "From Wikipedia: a basic block is a portion of the code within a program with only one entry point and only one exit point. This type gives the location of a basic block and if that basic block has executed.", "properties": [{"name": "startOffset", "type": "integer", "description": "Start offset of the basic block."}, {"name": "endOffset", "type": "integer", "description": "End offset of the basic block."}, {"name": "hasExecuted", "type": "boolean", "description": "Indicates if the basic block has executed before."}, {"name": "executionCount", "type": "integer", "description": "Indicates how many times the basic block has executed."}]}], "commands": [{"name": "parse", "description": "Parses JavaScript source code for errors.", "parameters": [{"name": "source", "type": "string", "description": "Source code to parse."}], "returns": [{"name": "result", "$ref": "SyntaxErrorType", "description": "Parse result."}, {"name": "message", "type": "string", "optional": true, "description": "Parse error message."}, {"name": "range", "$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "description": "Range in the source where the error occurred."}]}, {"name": "evaluate", "description": "Evaluates expression on global object.", "parameters": [{"name": "expression", "type": "string", "description": "Expression to evaluate."}, {"name": "objectGroup", "type": "string", "optional": true, "description": "Symbolic group name that can be used to release multiple objects."}, {"name": "includeCommandLineAPI", "type": "boolean", "optional": true, "description": "Determines whether Command Line API should be available during the evaluation."}, {"name": "doNotPauseOnExceptionsAndMuteConsole", "type": "boolean", "optional": true, "description": "Specifies whether evaluation should stop on exceptions and mute console. Overrides setPauseOnException state."}, {"name": "contextId", "$ref": "Runtime.ExecutionContextId", "optional": true, "description": "Specifies in which isolated context to perform evaluation. Each content script lives in an isolated context and this parameter may be used to specify one of those contexts. If the parameter is omitted or 0 the evaluation will be performed in the context of the inspected page."}, {"name": "returnByValue", "type": "boolean", "optional": true, "description": "Whether the result is expected to be a JSON object that should be sent by value."}, {"name": "generatePreview", "type": "boolean", "optional": true, "description": "Whether preview should be generated for the result."}, {"name": "saveResult", "type": "boolean", "optional": true, "description": "Whether the resulting value should be considered for saving in the $n history."}, {"name": "emulateUserGesture", "type": "boolean", "optional": true, "description": "Whether the expression should be considered to be in a user gesture or not."}], "returns": [{"name": "result", "$ref": "RemoteObject", "description": "Evaluation result."}, {"name": "wasThrown", "type": "boolean", "optional": true, "description": "True if the result was thrown during the evaluation."}, {"name": "savedResultIndex", "type": "integer", "optional": true, "description": "If the result was saved, this is the $n index that can be used to access the value."}]}, {"name": "await<PERSON><PERSON><PERSON>", "description": "Calls the async callback when the promise with the given ID gets settled.", "parameters": [{"name": "promiseObjectId", "$ref": "RemoteObjectId", "description": "Identifier of the promise."}, {"name": "returnByValue", "type": "boolean", "optional": true, "description": "Whether the result is expected to be a JSON object that should be sent by value."}, {"name": "generatePreview", "type": "boolean", "optional": true, "description": "Whether preview should be generated for the result."}, {"name": "saveResult", "type": "boolean", "optional": true, "description": "Whether the resulting value should be considered for saving in the $n history."}], "returns": [{"name": "result", "$ref": "RemoteObject", "description": "Evaluation result."}, {"name": "wasThrown", "type": "boolean", "optional": true, "description": "True if the result was thrown during the evaluation."}, {"name": "savedResultIndex", "type": "integer", "optional": true, "description": "If the result was saved, this is the $n index that can be used to access the value."}], "async": true}, {"name": "callFunctionOn", "description": "Calls function with given declaration on the given object. Object group of the result is inherited from the target object.", "parameters": [{"name": "objectId", "$ref": "RemoteObjectId", "description": "Identifier of the object to call function on."}, {"name": "functionDeclaration", "type": "string", "description": "Declaration of the function to call."}, {"name": "arguments", "type": "array", "items": {"$ref": "CallArgument", "description": "Call argument."}, "optional": true, "description": "Call arguments. All call arguments must belong to the same JavaScript world as the target object."}, {"name": "doNotPauseOnExceptionsAndMuteConsole", "type": "boolean", "optional": true, "description": "Specifies whether function call should stop on exceptions and mute console. Overrides setPauseOnException state."}, {"name": "returnByValue", "type": "boolean", "optional": true, "description": "Whether the result is expected to be a JSON object which should be sent by value."}, {"name": "generatePreview", "type": "boolean", "optional": true, "description": "Whether preview should be generated for the result."}, {"name": "emulateUserGesture", "type": "boolean", "optional": true, "description": "Whether the expression should be considered to be in a user gesture or not."}, {"name": "await<PERSON><PERSON><PERSON>", "type": "boolean", "optional": true, "description": "Whether to automatically await returned promise."}], "returns": [{"name": "result", "$ref": "RemoteObject", "description": "Call result."}, {"name": "wasThrown", "type": "boolean", "optional": true, "description": "True if the result was thrown during the evaluation."}], "async": true}, {"name": "addBinding", "description": "Adds binding with the given name on the global objects of all inspected contexts. Each binding function call produces Runtime.bindingCalled event.", "parameters": [{"name": "name", "type": "string", "description": "Name of the bound function."}]}, {"name": "getPreview", "description": "Returns a preview for the given object.", "parameters": [{"name": "objectId", "$ref": "RemoteObjectId", "description": "Identifier of the object to return a preview for."}], "returns": [{"name": "preview", "$ref": "ObjectPreview"}]}, {"name": "getProperties", "description": "Returns properties of a given object. Object group of the result is inherited from the target object.", "parameters": [{"name": "objectId", "$ref": "RemoteObjectId", "description": "Identifier of the object to return properties for."}, {"name": "ownProperties", "optional": true, "type": "boolean", "description": "If true, returns properties belonging only to the object itself, not to its prototype chain."}, {"name": "fetchStart", "optional": true, "type": "integer", "description": "If provided skip to this value before collecting values. Otherwise, start at the beginning. Has no effect when the `objectId` is for a `iterator`/`WeakMap`/`WeakSet` object."}, {"name": "fetchCount", "optional": true, "type": "integer", "description": "If provided only return `fetchCount` values. Otherwise, return values all the way to the end."}, {"name": "generatePreview", "type": "boolean", "optional": true, "description": "Whether preview should be generated for property values."}], "returns": [{"name": "properties", "type": "array", "items": {"$ref": "PropertyDescriptor"}, "description": "Object properties."}, {"name": "internalProperties", "optional": true, "type": "array", "items": {"$ref": "InternalPropertyDescriptor"}, "description": "Internal object properties. Only included if `fetchStart` is 0."}]}, {"name": "getDisplayableProperties", "description": "Returns displayable properties of a given object. Object group of the result is inherited from the target object. Displayable properties are own properties, internal properties, and native getters in the prototype chain (assumed to be bindings and treated like own properties for the frontend).", "parameters": [{"name": "objectId", "$ref": "RemoteObjectId", "description": "Identifier of the object to return properties for."}, {"name": "fetchStart", "optional": true, "type": "integer", "description": "If provided skip to this value before collecting values. Otherwise, start at the beginning. Has no effect when the `objectId` is for a `iterator`/`WeakMap`/`WeakSet` object."}, {"name": "fetchCount", "optional": true, "type": "integer", "description": "If provided only return `fetchCount` values. Otherwise, return values all the way to the end."}, {"name": "generatePreview", "type": "boolean", "optional": true, "description": "Whether preview should be generated for property values."}], "returns": [{"name": "properties", "type": "array", "items": {"$ref": "PropertyDescriptor"}, "description": "Object properties."}, {"name": "internalProperties", "optional": true, "type": "array", "items": {"$ref": "InternalPropertyDescriptor"}, "description": "Internal object properties. Only included if `fetchStart` is 0."}]}, {"name": "getCollectionEntries", "description": "Returns entries of given Map / Set collection.", "parameters": [{"name": "objectId", "$ref": "Runtime.RemoteObjectId", "description": "Id of the collection to get entries for."}, {"name": "objectGroup", "optional": true, "type": "string", "description": "Symbolic group name that can be used to release multiple. If not provided, it will be the same objectGroup as the RemoteObject determined from <code>objectId</code>. This is useful for WeakMap to release the collection entries."}, {"name": "fetchStart", "optional": true, "type": "integer", "description": "If provided skip to this value before collecting values. Otherwise, start at the beginning. Has no effect when the `objectId<` is for a `iterator<`/`WeakMap<`/`WeakSet<` object."}, {"name": "fetchCount", "optional": true, "type": "integer", "description": "If provided only return `fetchCount` values. Otherwise, return values all the way to the end."}], "returns": [{"name": "entries", "type": "array", "items": {"$ref": "CollectionEntry"}, "description": "Array of collection entries."}]}, {"name": "saveResult", "description": "Assign a saved result index to this value.", "parameters": [{"name": "value", "$ref": "CallArgument", "description": "Id or value of the object to save."}, {"name": "contextId", "optional": true, "$ref": "ExecutionContextId", "description": "Unique id of the execution context. To specify in which execution context script evaluation should be performed. If not provided, determine from the CallArgument's objectId."}], "returns": [{"name": "savedResultIndex", "type": "integer", "optional": true, "description": "If the value was saved, this is the $n index that can be used to access the value."}]}, {"name": "setSavedResultAlias", "description": "Creates an additional reference to all saved values in the Console using the the given string as a prefix instead of $.", "parameters": [{"name": "alias", "type": "string", "optional": true, "description": "Passing an empty/null string will clear the alias."}]}, {"name": "releaseObject", "description": "Releases remote object with given id.", "parameters": [{"name": "objectId", "$ref": "RemoteObjectId", "description": "Identifier of the object to release."}]}, {"name": "releaseObjectGroup", "description": "Releases all remote objects that belong to a given group.", "parameters": [{"name": "objectGroup", "type": "string", "description": "Symbolic object group name."}]}, {"name": "enable", "description": "Enables reporting of execution contexts creation by means of <code>executionContextCreated</code> event. When the reporting gets enabled the event will be sent immediately for each existing execution context."}, {"name": "disable", "description": "Disables reporting of execution contexts creation."}, {"name": "getRuntimeTypesForVariablesAtOffsets", "description": "Returns detailed information on the given function.", "parameters": [{"name": "locations", "type": "array", "items": {"$ref": "TypeLocation"}, "description": "An array of type locations we're requesting information for. Results are expected in the same order they're sent in."}], "returns": [{"name": "types", "type": "array", "items": {"$ref": "TypeDescription", "description": "Types for requested variable."}}]}, {"name": "enableTypeProfiler", "description": "Enables type profiling on the VM."}, {"name": "disableTypeProfiler", "description": "Disables type profiling on the VM."}, {"name": "enableControlFlowProfiler", "description": "Enables control flow profiling on the VM."}, {"name": "disableControlFlowProfiler", "description": "Disables control flow profiling on the VM."}, {"name": "getBasicBlocks", "description": "Returns a list of basic blocks for the given sourceID with information about their text ranges and whether or not they have executed.", "parameters": [{"name": "sourceID", "type": "string", "description": "Indicates which sourceID information is requested for."}], "returns": [{"name": "basicBlocks", "type": "array", "items": {"$ref": "BasicBlock", "description": "Array of basic blocks."}}]}], "events": [{"name": "executionContextCreated", "description": "Issued when new execution context is created.", "parameters": [{"name": "context", "$ref": "ExecutionContextDescription", "description": "A newly created execution context."}]}, {"name": "bindingCalled", "description": "Issued when new execution context is created.", "parameters": [{"name": "contextId", "$ref": "ExecutionContextId", "description": "Id of the execution context where the binding was called."}, {"name": "name", "type": "string", "description": "Name of the bound function."}, {"name": "argument", "type": "string", "description": "String argument passed to the function."}]}]}, {"domain": "Screencast", "availability": ["web"], "types": [{"id": "ScreencastId", "type": "string", "description": "Unique identifier of the screencast."}], "commands": [{"name": "startVideo", "description": "Starts recoring video to speified file.", "parameters": [{"name": "file", "type": "string", "description": "Output file location."}, {"name": "width", "type": "integer"}, {"name": "height", "type": "integer"}, {"name": "toolbarHeight", "type": "integer"}], "returns": [{"name": "screencastId", "$ref": "ScreencastId", "description": "Unique identifier of the screencast."}]}, {"name": "stopVideo", "async": true, "description": "Stops recoding video. Returns after the file has been closed."}, {"name": "startScreencast", "description": "Starts screencast.", "parameters": [{"name": "width", "type": "integer"}, {"name": "height", "type": "integer"}, {"name": "toolbarHeight", "type": "integer"}, {"name": "quality", "type": "integer"}], "returns": [{"name": "generation", "type": "integer", "description": "Screencast session generation."}]}, {"name": "stopScreencast", "description": "Stops screencast."}, {"name": "screencastFrameAck", "parameters": [{"name": "generation", "type": "integer", "description": "Screencast session generation"}]}], "events": [{"name": "screencastFrame", "parameters": [{"name": "data", "type": "string", "description": "Base64 data"}, {"name": "deviceWidth", "type": "integer"}, {"name": "deviceHeight", "type": "integer"}]}]}, {"domain": "ScriptProfiler", "description": "Profiler domain exposes JavaScript evaluation timing and profiling.", "debuggableTypes": ["itml", "javascript", "page", "web-page"], "targetTypes": ["itml", "javascript", "page", "worker"], "types": [{"id": "EventType", "type": "string", "enum": ["API", "Microtask", "Other"]}, {"id": "Event", "type": "object", "properties": [{"name": "startTime", "type": "number"}, {"name": "endTime", "type": "number"}, {"name": "type", "$ref": "EventType"}]}, {"id": "ExpressionLocation", "type": "object", "properties": [{"name": "line", "type": "integer", "description": "1-based."}, {"name": "column", "type": "integer", "description": "1-based."}]}, {"id": "StackFrame", "type": "object", "properties": [{"name": "sourceID", "$ref": "Debugger.ScriptId", "description": "Unique script identifier."}, {"name": "name", "type": "string", "description": "A displayable name for the stack frame. i.e function name, (program), etc."}, {"name": "line", "type": "integer", "description": "-1 if unavailable. 1-based if available."}, {"name": "column", "type": "integer", "description": "-1 if unavailable. 1-based if available."}, {"name": "url", "type": "string"}, {"name": "expressionLocation", "$ref": "ExpressionLocation", "optional": true}]}, {"id": "StackTrace", "type": "object", "properties": [{"name": "timestamp", "type": "number"}, {"name": "stackFrames", "type": "array", "items": {"$ref": "StackFrame"}, "description": "First array item is the bottom of the call stack and last array item is the top of the call stack."}]}, {"id": "<PERSON><PERSON>", "type": "object", "properties": [{"name": "stackTraces", "type": "array", "items": {"$ref": "StackTrace"}}]}], "commands": [{"name": "startTracking", "description": "Start tracking script evaluations.", "parameters": [{"name": "includeSamples", "type": "boolean", "optional": true, "description": "Start the sampling profiler, defaults to false."}]}, {"name": "stopTracking", "description": "Stop tracking script evaluations. This will produce a `trackingComplete` event."}], "events": [{"name": "trackingStart", "description": "Tracking started.", "parameters": [{"name": "timestamp", "type": "number"}]}, {"name": "trackingUpdate", "description": "Periodic tracking updates with event data.", "parameters": [{"name": "event", "$ref": "Event"}]}, {"name": "trackingComplete", "description": "Tracking stopped. Includes any buffered data during tracking, such as profiling information.", "parameters": [{"name": "timestamp", "type": "number"}, {"name": "samples", "$ref": "<PERSON><PERSON>", "optional": true, "description": "Stack traces."}]}]}, {"domain": "Security", "description": "Security domain allows the frontend to query for information relating to the security of the page (e.g. HTTPS info, TLS info, user activity, etc.).", "debuggableTypes": ["itml", "page", "service-worker", "web-page"], "targetTypes": ["itml", "page", "service-worker"], "types": [{"id": "Connection", "type": "object", "description": "Information about a SSL connection to display in the frontend.", "properties": [{"name": "protocol", "type": "string", "optional": true}, {"name": "cipher", "type": "string", "optional": true}]}, {"id": "Certificate", "type": "object", "description": "Information about a SSL certificate to display in the frontend.", "properties": [{"name": "subject", "type": "string", "optional": true}, {"name": "validFrom", "$ref": "Network.Walltime", "optional": true}, {"name": "validUntil", "$ref": "Network.Walltime", "optional": true}, {"name": "dnsNames", "type": "array", "items": {"type": "string"}, "optional": true, "description": "DNS names listed on the certificate."}, {"name": "ipAddresses", "type": "array", "items": {"type": "string"}, "optional": true, "description": "IP addresses listed on the certificate."}]}, {"id": "Security", "type": "object", "description": "Security information for a given Network.Response.", "properties": [{"name": "connection", "$ref": "Connection", "optional": true}, {"name": "certificate", "$ref": "Certificate", "optional": true}]}]}, {"domain": "ServiceWorker", "description": "Actions and events related to the inspected service worker.", "debuggableTypes": ["service-worker"], "targetTypes": ["service-worker"], "types": [{"id": "Configuration", "type": "object", "description": "ServiceWorker metadata and initial state.", "properties": [{"name": "targetId", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"name": "url", "type": "string", "description": "ServiceWorker main script URL."}, {"name": "content", "type": "string", "description": "ServiceWorker main script content."}]}], "commands": [{"name": "getInitializationInfo", "description": "Returns the initialization information for this target.", "returns": [{"name": "info", "$ref": "Configuration"}]}]}, {"domain": "Target", "debuggableTypes": ["web-page"], "targetTypes": ["web-page"], "types": [{"id": "TargetInfo", "type": "object", "description": "Description of a target.", "properties": [{"name": "targetId", "type": "string", "description": "Unique identifier for the target."}, {"name": "type", "type": "string", "enum": ["page", "service-worker", "worker"]}, {"name": "isProvisional", "type": "boolean", "optional": true, "description": "True value indicates that this is a provisional page target i.e. Such target may be created when current page starts cross-origin navigation. Eventually each provisional target is either committed and swaps with the current target or gets destroyed, e.g. in case of load request failure."}, {"name": "isPaused", "type": "boolean", "optional": true, "description": "Whether the target is paused on start and has to be explicitely resumed by inspector."}]}], "commands": [{"name": "setPauseOnStart", "description": "If set to true, new targets will be paused on start waiting for resume command. Other commands can be dispatched on the target before it is resumed.", "parameters": [{"name": "pauseOnStart", "type": "boolean", "description": "If set to true, new targets will be paused on start waiting for resume command."}]}, {"name": "resume", "description": "Will resume target if it was paused on start.", "parameters": [{"name": "targetId", "type": "string"}]}, {"name": "sendMessageToTarget", "description": "Send an Inspector Protocol message to be dispatched to a Target's agents.", "parameters": [{"name": "targetId", "type": "string"}, {"name": "message", "type": "string", "description": "JSON Inspector Protocol message (command) to be dispatched on the backend."}]}, {"name": "activate", "description": "Reveals the target on screen.", "parameters": [{"name": "targetId", "type": "string"}]}, {"name": "close", "description": "Closes the target.", "parameters": [{"name": "targetId", "type": "string"}, {"name": "runBeforeUnload", "type": "boolean", "optional": true}]}], "events": [{"name": "targetCreated", "parameters": [{"name": "targetInfo", "$ref": "TargetInfo"}]}, {"name": "targetDestroyed", "parameters": [{"name": "targetId", "type": "string"}, {"name": "crashed", "type": "boolean"}]}, {"name": "didCommitProvisionalTarget", "description": "This event is fired when provisional load is committed. Provisional target swaps with the current target.", "parameters": [{"name": "oldTargetId", "type": "string", "description": "ID of the old target that is swapped with the committed one."}, {"name": "newTargetId", "type": "string", "description": "ID of the committed target."}]}, {"name": "dispatchMessageFromTarget", "parameters": [{"name": "targetId", "type": "string"}, {"name": "message", "type": "string", "description": "JSON Inspector Protocol message (response or event) to be dispatched on the frontend."}]}]}, {"domain": "Timeline", "description": "Timeline provides its clients with instrumentation records that are generated during the page runtime. Timeline instrumentation can be started and stopped using corresponding commands. While timeline is started, it is generating timeline event records.", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page", "worker"], "types": [{"id": "EventType", "type": "string", "description": "Timeline record type.", "enum": ["EventDispatch", "ScheduleStyleRecalculation", "RecalculateStyles", "InvalidateLayout", "Layout", "Paint", "Composite", "RenderingFrame", "TimerInstall", "Timer<PERSON><PERSON><PERSON>", "Timer<PERSON>ire", "EvaluateScript", "TimeStamp", "Time", "TimeEnd", "FunctionCall", "ProbeSample", "ConsoleProfile", "RequestAnimationFrame", "CancelAnimationFrame", "FireAnimationFrame", "ObserverCallback", "Screenshot"]}, {"id": "Instrument", "type": "string", "description": "Instrument types.", "enum": ["ScriptProfiler", "Timeline", "CPU", "Memory", "<PERSON><PERSON>", "Animation", "Screenshot"]}, {"id": "TimelineEvent", "type": "object", "description": "Timeline record contains information about the recorded activity.", "properties": [{"name": "type", "$ref": "EventType", "description": "Event type."}, {"name": "data", "type": "object", "description": "Event data."}, {"name": "children", "type": "array", "optional": true, "items": {"$ref": "TimelineEvent"}, "description": "Nested records."}]}], "commands": [{"name": "enable", "description": "Enables Timeline domain events."}, {"name": "disable", "description": "Disables Timeline domain events."}, {"name": "start", "description": "Starts capturing instrumentation events.", "parameters": [{"name": "maxCallStackDepth", "optional": true, "type": "integer", "description": "Samples JavaScript stack traces up to <code>maxCallStackDepth</code>, defaults to 5."}]}, {"name": "stop", "description": "Stops capturing instrumentation events."}, {"name": "setAutoCaptureEnabled", "description": "Toggle auto capture state. If <code>true</code> the backend will disable breakpoints and start capturing on navigation. The backend will fire the <code>autoCaptureStarted</code> event when an auto capture starts. The frontend should stop the auto capture when appropriate and re-enable breakpoints.", "targetTypes": ["page"], "parameters": [{"name": "enabled", "type": "boolean", "description": "New auto capture state."}]}, {"name": "setInstruments", "description": "Instruments to enable when capture starts on the backend (e.g. auto capture or programmatic capture).", "parameters": [{"name": "instruments", "type": "array", "items": {"$ref": "Instrument"}, "description": "Instruments to enable."}]}], "events": [{"name": "eventRecorded", "description": "Fired for every instrumentation event while timeline is started.", "parameters": [{"name": "record", "$ref": "TimelineEvent", "description": "Timeline event record data."}]}, {"name": "recordingStarted", "description": "Fired when recording has started.", "parameters": [{"name": "startTime", "type": "number", "description": "Start time of this new recording."}]}, {"name": "recordingStopped", "description": "Fired when recording has stopped.", "parameters": [{"name": "endTime", "type": "number", "description": "End time of this recording."}]}, {"name": "autoCaptureStarted", "description": "Fired when auto capture started.", "targetTypes": ["page"]}]}, {"domain": "Worker", "debuggableTypes": ["page", "web-page"], "targetTypes": ["page", "worker"], "commands": [{"name": "enable", "description": "Enable Worker domain events."}, {"name": "disable", "description": "Disable Worker domain events."}, {"name": "initialized", "description": "Sent after the frontend has sent all initialization messages and can resume this worker. This command is required to allow execution in the worker.", "parameters": [{"name": "workerId", "type": "string"}]}, {"name": "sendMessageToWorker", "description": "Send an Inspector Protocol message to be dispatched to a Worker's agents.", "parameters": [{"name": "workerId", "type": "string"}, {"name": "message", "type": "string", "description": "JSON Inspector Protocol message (command) to be dispatched on the backend."}]}], "events": [{"name": "workerCreated", "parameters": [{"name": "workerId", "type": "string"}, {"name": "url", "type": "string"}, {"name": "name", "type": "string"}]}, {"name": "workerTerminated", "parameters": [{"name": "workerId", "type": "string"}]}, {"name": "dispatchMessageFromWorker", "parameters": [{"name": "workerId", "type": "string"}, {"name": "message", "type": "string", "description": "JSON Inspector Protocol message (response or event) to be dispatched on the frontend."}]}]}]