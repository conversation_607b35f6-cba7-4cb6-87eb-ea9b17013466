{"build": "h86e1c39_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\git-lfs-3.7.0-h86e1c39_0", "features": "", "files": ["Library/bin/git-lfs.exe"], "fn": "git-lfs-3.7.0-h86e1c39_0.conda", "license": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\git-lfs-3.7.0-h86e1c39_0", "type": 1}, "md5": "ebcf0143d55e24802ee6d4e5d0b7ec8e", "name": "git-lfs", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\git-lfs-3.7.0-h86e1c39_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/git-lfs.exe", "path_type": "hardlink", "sha256": "ab075ea0428b2c217023203daf0a5f2660f4287da9b546f831a725570b7ed12b", "sha256_in_prefix": "ab075ea0428b2c217023203daf0a5f2660f4287da9b546f831a725570b7ed12b", "size_in_bytes": 12604416}], "paths_version": 1}, "requested_spec": "git-lfs", "sha256": "a14acabcdfa29f5faa34b32aac9b3096c181c6fec01ef8d186e9c392a743df07", "size": 4095810, "subdir": "win-64", "timestamp": 1750975920000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/git-lfs-3.7.0-h86e1c39_0.conda", "version": "3.7.0"}