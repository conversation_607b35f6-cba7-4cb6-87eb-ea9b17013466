{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvvm-samples-12.1.55-0", "features": "", "files": ["nvvm/LICENSE", "nvvm/nvvm/libnvvm-samples/CMakeLists.txt", "nvvm/nvvm/libnvvm-samples/README.txt", "nvvm/nvvm/libnvvm-samples/build.bat", "nvvm/nvvm/libnvvm-samples/build.sh", "nvvm/nvvm/libnvvm-samples/common/include/DDSWriter.h", "nvvm/nvvm/libnvvm-samples/common/include/drvapi_error_string.h", "nvvm/nvvm/libnvvm-samples/cuda-c-linking/CMakeLists.txt", "nvvm/nvvm/libnvvm-samples/cuda-c-linking/README.txt", "nvvm/nvvm/libnvvm-samples/cuda-c-linking/cuda-c-linking.cpp", "nvvm/nvvm/libnvvm-samples/cuda-c-linking/math-funcs.cu", "nvvm/nvvm/libnvvm-samples/ptxgen/CMakeLists.txt", "nvvm/nvvm/libnvvm-samples/ptxgen/README.txt", "nvvm/nvvm/libnvvm-samples/ptxgen/ptxgen.c", "nvvm/nvvm/libnvvm-samples/simple/CMakeLists.txt", "nvvm/nvvm/libnvvm-samples/simple/README.txt", "nvvm/nvvm/libnvvm-samples/simple/simple-gpu64.ll", "nvvm/nvvm/libnvvm-samples/simple/simple.c"], "fn": "libnvvm-samples-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvvm-samples-12.1.55-0", "type": 1}, "md5": "56850c5fb6859cf012cc4496add355f4", "name": "libnvvm-samples", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvvm-samples-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "nvvm/LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "nvvm/nvvm/libnvvm-samples/CMakeLists.txt", "path_type": "hardlink", "sha256": "a93c3bbfa8137b271e7798b8c76cd4890dbeb182491af2a6adfe412228aac699", "sha256_in_prefix": "a93c3bbfa8137b271e7798b8c76cd4890dbeb182491af2a6adfe412228aac699", "size_in_bytes": 4861}, {"_path": "nvvm/nvvm/libnvvm-samples/README.txt", "path_type": "hardlink", "sha256": "6e9d9e14b0f52c598ad18c71accc3455a26fcc671b7e2295a8fa8ecd0a9e7862", "sha256_in_prefix": "6e9d9e14b0f52c598ad18c71accc3455a26fcc671b7e2295a8fa8ecd0a9e7862", "size_in_bytes": 3051}, {"_path": "nvvm/nvvm/libnvvm-samples/build.bat", "path_type": "hardlink", "sha256": "41789129f7f8097a252aa580f8c4cab4dd970650661d3be3ba6e8ed41ad2e48f", "sha256_in_prefix": "41789129f7f8097a252aa580f8c4cab4dd970650661d3be3ba6e8ed41ad2e48f", "size_in_bytes": 108}, {"_path": "nvvm/nvvm/libnvvm-samples/build.sh", "path_type": "hardlink", "sha256": "690b5f88592f7ae203112bfd8aa2caac1ded40748dcf717f591d02c1c7a1e8a0", "sha256_in_prefix": "690b5f88592f7ae203112bfd8aa2caac1ded40748dcf717f591d02c1c7a1e8a0", "size_in_bytes": 94}, {"_path": "nvvm/nvvm/libnvvm-samples/common/include/DDSWriter.h", "path_type": "hardlink", "sha256": "dc32cd6abfedd811a5d6e368317ef5e37bdca1fd6fa153c6e6b07fd2ec81b709", "sha256_in_prefix": "dc32cd6abfedd811a5d6e368317ef5e37bdca1fd6fa153c6e6b07fd2ec81b709", "size_in_bytes": 3036}, {"_path": "nvvm/nvvm/libnvvm-samples/common/include/drvapi_error_string.h", "path_type": "hardlink", "sha256": "7a3a5b8595d83be3de1df0a57fc83c5ae3e9a3acaea429f3e362b5c52e72e850", "sha256_in_prefix": "7a3a5b8595d83be3de1df0a57fc83c5ae3e9a3acaea429f3e362b5c52e72e850", "size_in_bytes": 12054}, {"_path": "nvvm/nvvm/libnvvm-samples/cuda-c-linking/CMakeLists.txt", "path_type": "hardlink", "sha256": "78e4a6cd6c5ba2dcc4d4a5d1e0d499f4af37bc6dc959e4432f0531ab7c25b336", "sha256_in_prefix": "78e4a6cd6c5ba2dcc4d4a5d1e0d499f4af37bc6dc959e4432f0531ab7c25b336", "size_in_bytes": 3206}, {"_path": "nvvm/nvvm/libnvvm-samples/cuda-c-linking/README.txt", "path_type": "hardlink", "sha256": "7657ac1eed61033e468aaed4f093f7ccdec3e9cd7306b9e466363527d8d1c591", "sha256_in_prefix": "7657ac1eed61033e468aaed4f093f7ccdec3e9cd7306b9e466363527d8d1c591", "size_in_bytes": 1660}, {"_path": "nvvm/nvvm/libnvvm-samples/cuda-c-linking/cuda-c-linking.cpp", "path_type": "hardlink", "sha256": "0d245c977ea46fe25c0c22f3fdb077ad45d2990d13be5c8e0dc576b630ac1f31", "sha256_in_prefix": "0d245c977ea46fe25c0c22f3fdb077ad45d2990d13be5c8e0dc576b630ac1f31", "size_in_bytes": 11021}, {"_path": "nvvm/nvvm/libnvvm-samples/cuda-c-linking/math-funcs.cu", "path_type": "hardlink", "sha256": "d4ce5e9488331b67e859306a94184f693965f901bf5875b1475de75d78d1e60a", "sha256_in_prefix": "d4ce5e9488331b67e859306a94184f693965f901bf5875b1475de75d78d1e60a", "size_in_bytes": 1722}, {"_path": "nvvm/nvvm/libnvvm-samples/ptxgen/CMakeLists.txt", "path_type": "hardlink", "sha256": "f7efdd5c63c7cdd4b4c8cfb58ae562752002ede6476c1a4c69fae1225a9d8c7e", "sha256_in_prefix": "f7efdd5c63c7cdd4b4c8cfb58ae562752002ede6476c1a4c69fae1225a9d8c7e", "size_in_bytes": 2019}, {"_path": "nvvm/nvvm/libnvvm-samples/ptxgen/README.txt", "path_type": "hardlink", "sha256": "78ce58bb6b1a7ff8a5d7adce21ba855f15fda668864eae48b3410b9475eb50e1", "sha256_in_prefix": "78ce58bb6b1a7ff8a5d7adce21ba855f15fda668864eae48b3410b9475eb50e1", "size_in_bytes": 1172}, {"_path": "nvvm/nvvm/libnvvm-samples/ptxgen/ptxgen.c", "path_type": "hardlink", "sha256": "0271ef17001af7f0670d03425eeab0670fe4885ee0212c6b36fe13f5b74c902b", "sha256_in_prefix": "0271ef17001af7f0670d03425eeab0670fe4885ee0212c6b36fe13f5b74c902b", "size_in_bytes": 8277}, {"_path": "nvvm/nvvm/libnvvm-samples/simple/CMakeLists.txt", "path_type": "hardlink", "sha256": "a1574548e40ac6e4eece2ccbcd12810d557631bf26c88ad35cfcb1c4fee595e6", "sha256_in_prefix": "a1574548e40ac6e4eece2ccbcd12810d557631bf26c88ad35cfcb1c4fee595e6", "size_in_bytes": 2065}, {"_path": "nvvm/nvvm/libnvvm-samples/simple/README.txt", "path_type": "hardlink", "sha256": "fe2ff2cbb1d16979579e199a0e21d9de1a02832ed46d53a10318e5021bfbb716", "sha256_in_prefix": "fe2ff2cbb1d16979579e199a0e21d9de1a02832ed46d53a10318e5021bfbb716", "size_in_bytes": 270}, {"_path": "nvvm/nvvm/libnvvm-samples/simple/simple-gpu64.ll", "path_type": "hardlink", "sha256": "3aa920c95ddc270ca7864f77c5e3e05cce307fc088cd2c5d620e7f8e3fe6df7e", "sha256_in_prefix": "3aa920c95ddc270ca7864f77c5e3e05cce307fc088cd2c5d620e7f8e3fe6df7e", "size_in_bytes": 1744}, {"_path": "nvvm/nvvm/libnvvm-samples/simple/simple.c", "path_type": "hardlink", "sha256": "90d764fafc4fa135328cac8a19b7f0f89d47dd4e14bae842a9ac4088fc505fd2", "sha256_in_prefix": "90d764fafc4fa135328cac8a19b7f0f89d47dd4e14bae842a9ac4088fc505fd2", "size_in_bytes": 6074}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 33215, "subdir": "win-64", "timestamp": 1674618927000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libnvvm-samples-12.1.55-0.tar.bz2", "version": "12.1.55"}