<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: PcSamplingStallReasons Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>PcSamplingStallReasons Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html">CUPTI PC Sampling Utility API</a>]</small>
</h1><!-- doxytag: class="PcSamplingStallReasons" -->All available stall reasons name and respective indexes will be stored in it.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPcSamplingStallReasons.html#bef119b31f4c7d9f3fc14c7988703326">numStallReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPcSamplingStallReasons.html#adbd7f82361702725bf54225fcdf9605">stallReasonIndex</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">char **&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPcSamplingStallReasons.html#0db3ddf0898e98c97c0691f3e47cff34">stallReasons</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="bef119b31f4c7d9f3fc14c7988703326"></a><!-- doxytag: member="PcSamplingStallReasons::numStallReasons" ref="bef119b31f4c7d9f3fc14c7988703326" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structPcSamplingStallReasons.html#bef119b31f4c7d9f3fc14c7988703326">PcSamplingStallReasons::numStallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of all available stall reasons 
</div>
</div><p>
<a class="anchor" name="adbd7f82361702725bf54225fcdf9605"></a><!-- doxytag: member="PcSamplingStallReasons::stallReasonIndex" ref="adbd7f82361702725bf54225fcdf9605" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t* <a class="el" href="structPcSamplingStallReasons.html#adbd7f82361702725bf54225fcdf9605">PcSamplingStallReasons::stallReasonIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Stall reason index of all available stall reasons 
</div>
</div><p>
<a class="anchor" name="0db3ddf0898e98c97c0691f3e47cff34"></a><!-- doxytag: member="PcSamplingStallReasons::stallReasons" ref="0db3ddf0898e98c97c0691f3e47cff34" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char** <a class="el" href="structPcSamplingStallReasons.html#0db3ddf0898e98c97c0691f3e47cff34">PcSamplingStallReasons::stallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Stall reasons names of all available stall reasons 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:52 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
