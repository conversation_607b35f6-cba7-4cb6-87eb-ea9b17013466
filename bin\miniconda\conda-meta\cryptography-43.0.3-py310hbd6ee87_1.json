{"arch": "x86_64", "build": "py310hbd6ee87_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": ["pyopenssl >=23.0.0"], "depends": ["cffi >=1.12", "openssl >=3.0.15,<4.0a0", "python >=3.10,<3.11.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cryptography-43.0.3-py310hbd6ee87_1", "files": ["Lib/site-packages/cryptography-43.0.3.dist-info/INSTALLER", "Lib/site-packages/cryptography-43.0.3.dist-info/METADATA", "Lib/site-packages/cryptography-43.0.3.dist-info/RECORD", "Lib/site-packages/cryptography-43.0.3.dist-info/REQUESTED", "Lib/site-packages/cryptography-43.0.3.dist-info/WHEEL", "Lib/site-packages/cryptography-43.0.3.dist-info/direct_url.json", "Lib/site-packages/cryptography-43.0.3.dist-info/license_files/LICENSE", "Lib/site-packages/cryptography-43.0.3.dist-info/license_files/LICENSE.APACHE", "Lib/site-packages/cryptography-43.0.3.dist-info/license_files/LICENSE.BSD", "Lib/site-packages/cryptography/__about__.py", "Lib/site-packages/cryptography/__init__.py", "Lib/site-packages/cryptography/__pycache__/__about__.cpython-310.pyc", "Lib/site-packages/cryptography/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/cryptography/__pycache__/fernet.cpython-310.pyc", "Lib/site-packages/cryptography/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/cryptography/exceptions.py", "Lib/site-packages/cryptography/fernet.py", "Lib/site-packages/cryptography/hazmat/__init__.py", "Lib/site-packages/cryptography/hazmat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/__pycache__/_oid.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/_oid.py", "Lib/site-packages/cryptography/hazmat/backends/__init__.py", "Lib/site-packages/cryptography/hazmat/backends/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/backends/openssl/__init__.py", "Lib/site-packages/cryptography/hazmat/backends/openssl/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/backends/openssl/__pycache__/backend.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/backends/openssl/backend.py", "Lib/site-packages/cryptography/hazmat/bindings/__init__.py", "Lib/site-packages/cryptography/hazmat/bindings/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/bindings/_rust.pyd", "Lib/site-packages/cryptography/hazmat/bindings/_rust/__init__.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/_openssl.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/asn1.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/exceptions.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/ocsp.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/__init__.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/aead.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ciphers.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/cmac.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/dh.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/dsa.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ec.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ed25519.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ed448.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/hashes.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/hmac.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/kdf.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/keys.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/poly1305.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/rsa.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/x25519.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/x448.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/pkcs12.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/pkcs7.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/test_support.pyi", "Lib/site-packages/cryptography/hazmat/bindings/_rust/x509.pyi", "Lib/site-packages/cryptography/hazmat/bindings/openssl/__init__.py", "Lib/site-packages/cryptography/hazmat/bindings/openssl/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/bindings/openssl/__pycache__/_conditional.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/bindings/openssl/__pycache__/binding.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/bindings/openssl/_conditional.py", "Lib/site-packages/cryptography/hazmat/bindings/openssl/binding.py", "Lib/site-packages/cryptography/hazmat/decrepit/__init__.py", "Lib/site-packages/cryptography/hazmat/decrepit/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/decrepit/ciphers/__init__.py", "Lib/site-packages/cryptography/hazmat/decrepit/ciphers/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/decrepit/ciphers/__pycache__/algorithms.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/decrepit/ciphers/algorithms.py", "Lib/site-packages/cryptography/hazmat/primitives/__init__.py", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/_asymmetric.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/_cipheralgorithm.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/_serialization.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/cmac.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/constant_time.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/hashes.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/hmac.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/keywrap.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/padding.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/__pycache__/poly1305.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/_asymmetric.py", "Lib/site-packages/cryptography/hazmat/primitives/_cipheralgorithm.py", "Lib/site-packages/cryptography/hazmat/primitives/_serialization.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__init__.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/dh.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/dsa.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/ec.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/ed25519.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/ed448.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/padding.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/rsa.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/types.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/x25519.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__pycache__/x448.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/dh.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/dsa.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/ec.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/ed25519.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/ed448.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/padding.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/rsa.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/types.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/utils.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/x25519.py", "Lib/site-packages/cryptography/hazmat/primitives/asymmetric/x448.py", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/__init__.py", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/__pycache__/aead.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/__pycache__/algorithms.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/__pycache__/base.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/__pycache__/modes.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/aead.py", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/algorithms.py", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/base.py", "Lib/site-packages/cryptography/hazmat/primitives/ciphers/modes.py", "Lib/site-packages/cryptography/hazmat/primitives/cmac.py", "Lib/site-packages/cryptography/hazmat/primitives/constant_time.py", "Lib/site-packages/cryptography/hazmat/primitives/hashes.py", "Lib/site-packages/cryptography/hazmat/primitives/hmac.py", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__init__.py", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__pycache__/concatkdf.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__pycache__/hkdf.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__pycache__/kbkdf.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__pycache__/pbkdf2.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__pycache__/scrypt.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/kdf/__pycache__/x963kdf.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/kdf/concatkdf.py", "Lib/site-packages/cryptography/hazmat/primitives/kdf/hkdf.py", "Lib/site-packages/cryptography/hazmat/primitives/kdf/kbkdf.py", "Lib/site-packages/cryptography/hazmat/primitives/kdf/pbkdf2.py", "Lib/site-packages/cryptography/hazmat/primitives/kdf/scrypt.py", "Lib/site-packages/cryptography/hazmat/primitives/kdf/x963kdf.py", "Lib/site-packages/cryptography/hazmat/primitives/keywrap.py", "Lib/site-packages/cryptography/hazmat/primitives/padding.py", "Lib/site-packages/cryptography/hazmat/primitives/poly1305.py", "Lib/site-packages/cryptography/hazmat/primitives/serialization/__init__.py", "Lib/site-packages/cryptography/hazmat/primitives/serialization/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/serialization/__pycache__/base.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/serialization/__pycache__/pkcs12.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/serialization/__pycache__/pkcs7.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/serialization/__pycache__/ssh.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/serialization/base.py", "Lib/site-packages/cryptography/hazmat/primitives/serialization/pkcs12.py", "Lib/site-packages/cryptography/hazmat/primitives/serialization/pkcs7.py", "Lib/site-packages/cryptography/hazmat/primitives/serialization/ssh.py", "Lib/site-packages/cryptography/hazmat/primitives/twofactor/__init__.py", "Lib/site-packages/cryptography/hazmat/primitives/twofactor/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/twofactor/__pycache__/hotp.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/twofactor/__pycache__/totp.cpython-310.pyc", "Lib/site-packages/cryptography/hazmat/primitives/twofactor/hotp.py", "Lib/site-packages/cryptography/hazmat/primitives/twofactor/totp.py", "Lib/site-packages/cryptography/py.typed", "Lib/site-packages/cryptography/utils.py", "Lib/site-packages/cryptography/x509/__init__.py", "Lib/site-packages/cryptography/x509/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/base.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/certificate_transparency.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/extensions.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/general_name.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/name.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/ocsp.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/oid.cpython-310.pyc", "Lib/site-packages/cryptography/x509/__pycache__/verification.cpython-310.pyc", "Lib/site-packages/cryptography/x509/base.py", "Lib/site-packages/cryptography/x509/certificate_transparency.py", "Lib/site-packages/cryptography/x509/extensions.py", "Lib/site-packages/cryptography/x509/general_name.py", "Lib/site-packages/cryptography/x509/name.py", "Lib/site-packages/cryptography/x509/ocsp.py", "Lib/site-packages/cryptography/x509/oid.py", "Lib/site-packages/cryptography/x509/verification.py"], "fn": "cryptography-43.0.3-py310hbd6ee87_1.conda", "license": "Apache-2.0 OR BSD-3-<PERSON><PERSON>", "license_family": "OTHER", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cryptography-43.0.3-py310hbd6ee87_1", "type": 1}, "md5": "586afc811408f0063f627e4d0dcc24aa", "name": "cryptography", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cryptography-43.0.3-py310hbd6ee87_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::cryptography==43.0.3=py310hbd6ee87_1[md5=586afc811408f0063f627e4d0dcc24aa]", "sha256": "c672bbef9b69d19e419f4dab7e678b110f00b550e56ae66fc7e80213d5c4d9c0", "size": 1362226, "subdir": "win-64", "timestamp": 1732131081000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cryptography-43.0.3-py310hbd6ee87_1.conda", "version": "43.0.3"}