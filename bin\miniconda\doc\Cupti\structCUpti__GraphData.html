<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_GraphData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_GraphData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__CALLBACK__API.html">CUPTI Callback API</a>]</small>
</h1><!-- doxytag: class="CUpti_GraphData" -->CUDA graphs data passed into a resource callback function.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphNode&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html#ea417dbcc763388c4f45f8a10dfe552f">dependency</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraph&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html#db7f4693eac9e5166ce301fe2814fbd0">graph</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphExec&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html#18c68bc1e2bfcda94ecd55dcc2ad520e">graphExec</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphNode&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html#432140988d6d3883d41a438a3b5906fa">node</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphNodeType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html#8d6e9993380ae829ad380d1549c85889">nodeType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraph&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html#184a1fb7a48f5722a6700489278292ee">originalGraph</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphNode&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html#5dda3dc77f844c88c31cd14cc3241b87">originalNode</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
CUDA graphs data passed into a resource callback function as the <code>cbdata</code> argument to <a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a>. The <code>cbdata</code> will be this type for <code>domain</code> equal to CUPTI_CB_DOMAIN_RESOURCE. The graph data is valid only within the invocation of the callback function that is passed the data. If you need to retain some data for use outside of the callback, you must make a copy of that data. <hr><h2>Field Documentation</h2>
<a class="anchor" name="ea417dbcc763388c4f45f8a10dfe552f"></a><!-- doxytag: member="CUpti_GraphData::dependency" ref="ea417dbcc763388c4f45f8a10dfe552f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphNode <a class="el" href="structCUpti__GraphData.html#ea417dbcc763388c4f45f8a10dfe552f">CUpti_GraphData::dependency</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dependent graph node The size of the array is <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>numDependencies.</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="db7f4693eac9e5166ce301fe2814fbd0"></a><!-- doxytag: member="CUpti_GraphData::graph" ref="db7f4693eac9e5166ce301fe2814fbd0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraph <a class="el" href="structCUpti__GraphData.html#db7f4693eac9e5166ce301fe2814fbd0">CUpti_GraphData::graph</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA graph 
</div>
</div><p>
<a class="anchor" name="18c68bc1e2bfcda94ecd55dcc2ad520e"></a><!-- doxytag: member="CUpti_GraphData::graphExec" ref="18c68bc1e2bfcda94ecd55dcc2ad520e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphExec <a class="el" href="structCUpti__GraphData.html#18c68bc1e2bfcda94ecd55dcc2ad520e">CUpti_GraphData::graphExec</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA executable graph 
</div>
</div><p>
<a class="anchor" name="432140988d6d3883d41a438a3b5906fa"></a><!-- doxytag: member="CUpti_GraphData::node" ref="432140988d6d3883d41a438a3b5906fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphNode <a class="el" href="structCUpti__GraphData.html#432140988d6d3883d41a438a3b5906fa">CUpti_GraphData::node</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA graph node 
</div>
</div><p>
<a class="anchor" name="8d6e9993380ae829ad380d1549c85889"></a><!-- doxytag: member="CUpti_GraphData::nodeType" ref="8d6e9993380ae829ad380d1549c85889" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphNodeType <a class="el" href="structCUpti__GraphData.html#8d6e9993380ae829ad380d1549c85889">CUpti_GraphData::nodeType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of the <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>node</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="184a1fb7a48f5722a6700489278292ee"></a><!-- doxytag: member="CUpti_GraphData::originalGraph" ref="184a1fb7a48f5722a6700489278292ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraph <a class="el" href="structCUpti__GraphData.html#184a1fb7a48f5722a6700489278292ee">CUpti_GraphData::originalGraph</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The original CUDA graph from which <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>graph</em>&nbsp;</td><td>is cloned </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="5dda3dc77f844c88c31cd14cc3241b87"></a><!-- doxytag: member="CUpti_GraphData::originalNode" ref="5dda3dc77f844c88c31cd14cc3241b87" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphNode <a class="el" href="structCUpti__GraphData.html#5dda3dc77f844c88c31cd14cc3241b87">CUpti_GraphData::originalNode</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The original CUDA graph node from which <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>node</em>&nbsp;</td><td>is cloned </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
