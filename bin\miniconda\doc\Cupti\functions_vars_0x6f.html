<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_vars_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li class="current"><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_o">- o -</a></h3><ul>
<li>objectId
: <a class="el" href="structCUpti__ActivityName.html#3bea1dcb4a8685e478e07670c3115cd7">CUpti_ActivityName</a>
, <a class="el" href="structCUpti__ActivityMarker.html#e81855f68e5c9f80bacd0ec66b0b3613">CUpti_ActivityMarker</a>
, <a class="el" href="structCUpti__ActivityOverhead.html#9295888f85da6d51ab2f0b8e0a0a23bc">CUpti_ActivityOverhead</a>
, <a class="el" href="structCUpti__ActivityMarker2.html#97f04715d8e2704cc3ce1541ea63967b">CUpti_ActivityMarker2</a>
<li>objectKind
: <a class="el" href="structCUpti__ActivityOverhead.html#412ff0b2e814b0b86c9b0a43c9239380">CUpti_ActivityOverhead</a>
, <a class="el" href="structCUpti__ActivityName.html#973d783185d57b673dbcc3d1cabaaab8">CUpti_ActivityName</a>
, <a class="el" href="structCUpti__ActivityMarker.html#c1b912d09e94461e8dc8b06aa1e8a0ff">CUpti_ActivityMarker</a>
, <a class="el" href="structCUpti__ActivityMarker2.html#8020c1ecff9e4c54d4123e1a5fdd3b12">CUpti_ActivityMarker2</a>
<li>onePassCollected
: <a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#e38c86e191d4c3ba0036e56ec0e8ca6f">CUpti_Profiler_IsPassCollected_Params</a>
<li>optimizations
: <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#72214c99d5ecc2b7bf1614405c4759b6">NV::Cupti::Checkpoint::CUpti_Checkpoint</a>
<li>originalGraph
: <a class="el" href="structCUpti__GraphData.html#184a1fb7a48f5722a6700489278292ee">CUpti_GraphData</a>
<li>originalNode
: <a class="el" href="structCUpti__GraphData.html#5dda3dc77f844c88c31cd14cc3241b87">CUpti_GraphData</a>
<li>outputDataFormatData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#b083c6e1deab377f674a5aa9a34d1d6e">CUpti_PCSamplingConfigurationInfo</a>
<li>overheadKind
: <a class="el" href="structCUpti__ActivityOverhead.html#4c7e3fda3ba2e374f3b62f1ab15b272f">CUpti_ActivityOverhead</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
