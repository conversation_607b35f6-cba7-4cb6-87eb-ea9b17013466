{"arch": "x86_64", "build": "h9243413_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["bzip2 >=1.0.8,<2.0a0", "libiconv >=1.16,<2.0a0", "libxml2 >=2.13.5,<2.14.0a0", "lz4-c >=1.9.4,<1.10.0a0", "openssl >=3.0.15,<4.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "xz >=5.4.6,<6.0a0", "zlib >=1.2.13,<1.3.0a0", "zstd >=1.5.2,<1.6.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libarchive-3.7.7-h9243413_0", "files": ["Library/bin/archive.dll", "Library/bin/bsdcat.exe", "Library/bin/bsdcpio.exe", "Library/bin/bsdtar.exe", "Library/include/archive.h", "Library/include/archive_entry.h", "Library/lib/archive.lib", "Library/lib/archive_static.lib", "Library/lib/pkgconfig/libarchive.pc", "Library/share/man/man1/bsdcat.1", "Library/share/man/man1/bsdcpio.1", "Library/share/man/man1/bsdtar.1", "Library/share/man/man3/archive_entry.3", "Library/share/man/man3/archive_entry_acl.3", "Library/share/man/man3/archive_entry_linkify.3", "Library/share/man/man3/archive_entry_misc.3", "Library/share/man/man3/archive_entry_paths.3", "Library/share/man/man3/archive_entry_perms.3", "Library/share/man/man3/archive_entry_stat.3", "Library/share/man/man3/archive_entry_time.3", "Library/share/man/man3/archive_read.3", "Library/share/man/man3/archive_read_add_passphrase.3", "Library/share/man/man3/archive_read_data.3", "Library/share/man/man3/archive_read_disk.3", "Library/share/man/man3/archive_read_extract.3", "Library/share/man/man3/archive_read_filter.3", "Library/share/man/man3/archive_read_format.3", "Library/share/man/man3/archive_read_free.3", "Library/share/man/man3/archive_read_header.3", "Library/share/man/man3/archive_read_new.3", "Library/share/man/man3/archive_read_open.3", "Library/share/man/man3/archive_read_set_options.3", "Library/share/man/man3/archive_util.3", "Library/share/man/man3/archive_write.3", "Library/share/man/man3/archive_write_blocksize.3", "Library/share/man/man3/archive_write_data.3", "Library/share/man/man3/archive_write_disk.3", "Library/share/man/man3/archive_write_filter.3", "Library/share/man/man3/archive_write_finish_entry.3", "Library/share/man/man3/archive_write_format.3", "Library/share/man/man3/archive_write_free.3", "Library/share/man/man3/archive_write_header.3", "Library/share/man/man3/archive_write_new.3", "Library/share/man/man3/archive_write_open.3", "Library/share/man/man3/archive_write_set_options.3", "Library/share/man/man3/archive_write_set_passphrase.3", "Library/share/man/man3/libarchive.3", "Library/share/man/man3/libarchive_changes.3", "Library/share/man/man3/libarchive_internals.3", "Library/share/man/man5/cpio.5", "Library/share/man/man5/libarchive-formats.5", "Library/share/man/man5/mtree.5", "Library/share/man/man5/tar.5", "Library/share/man/pdf/archive_entry.3.pdf", "Library/share/man/pdf/archive_entry_acl.3.pdf", "Library/share/man/pdf/archive_entry_linkify.3.pdf", "Library/share/man/pdf/archive_entry_misc.3.pdf", "Library/share/man/pdf/archive_entry_paths.3.pdf", "Library/share/man/pdf/archive_entry_perms.3.pdf", "Library/share/man/pdf/archive_entry_stat.3.pdf", "Library/share/man/pdf/archive_entry_time.3.pdf", "Library/share/man/pdf/archive_read.3.pdf", "Library/share/man/pdf/archive_read_add_passphrase.3.pdf", "Library/share/man/pdf/archive_read_data.3.pdf", "Library/share/man/pdf/archive_read_disk.3.pdf", "Library/share/man/pdf/archive_read_extract.3.pdf", "Library/share/man/pdf/archive_read_filter.3.pdf", "Library/share/man/pdf/archive_read_format.3.pdf", "Library/share/man/pdf/archive_read_free.3.pdf", "Library/share/man/pdf/archive_read_header.3.pdf", "Library/share/man/pdf/archive_read_new.3.pdf", "Library/share/man/pdf/archive_read_open.3.pdf", "Library/share/man/pdf/archive_read_set_options.3.pdf", "Library/share/man/pdf/archive_util.3.pdf", "Library/share/man/pdf/archive_write.3.pdf", "Library/share/man/pdf/archive_write_blocksize.3.pdf", "Library/share/man/pdf/archive_write_data.3.pdf", "Library/share/man/pdf/archive_write_disk.3.pdf", "Library/share/man/pdf/archive_write_filter.3.pdf", "Library/share/man/pdf/archive_write_finish_entry.3.pdf", "Library/share/man/pdf/archive_write_format.3.pdf", "Library/share/man/pdf/archive_write_free.3.pdf", "Library/share/man/pdf/archive_write_header.3.pdf", "Library/share/man/pdf/archive_write_new.3.pdf", "Library/share/man/pdf/archive_write_open.3.pdf", "Library/share/man/pdf/archive_write_set_options.3.pdf", "Library/share/man/pdf/archive_write_set_passphrase.3.pdf", "Library/share/man/pdf/bsdcpio.1.pdf", "Library/share/man/pdf/bsdtar.1.pdf", "Library/share/man/pdf/cpio.5.pdf", "Library/share/man/pdf/libarchive-formats.5.pdf", "Library/share/man/pdf/libarchive.3.pdf", "Library/share/man/pdf/libarchive_changes.3.pdf", "Library/share/man/pdf/libarchive_internals.3.pdf", "Library/share/man/pdf/mtree.5.pdf", "Library/share/man/pdf/tar.5.pdf"], "fn": "libarchive-3.7.7-h9243413_0.conda", "license": "BSD-2-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libarchive-3.7.7-h9243413_0", "type": 1}, "md5": "68fdc3f6bdfea294d35b154ff3810daa", "name": "libarchive", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libarchive-3.7.7-h9243413_0.conda", "paths_data": {"paths": [{"_path": "Library/lib/pkgconfig/libarchive.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_55ez3326wm/croot/libarchive_1733951429175/_h_env", "sha256": "2ff068f4651806062508b577cbe25d23322da0d50c6c6c4673dcbdd497de0d2b", "sha256_in_prefix": "79f6d66c12c4170e4f9f62bda61528834edb3de9457005c636fcf0478a27e54a", "size_in_bytes": 470}], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::libarchive==3.7.7=h9243413_0[md5=68fdc3f6bdfea294d35b154ff3810daa]", "sha256": "2c0d296017dbd0f02ad9ecb6cf5949a296d1e6feaa4a0b9fa031c5e51f480fcd", "size": 1935569, "subdir": "win-64", "timestamp": 1733951814000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libarchive-3.7.7-h9243413_0.conda", "version": "3.7.7"}