<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params" -->Params for cuptiProfilerCounterDataImageCalculateScratchBufferSize.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="f6d86033296f95d41dba6bd8713993b4"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params::counterDataImageSize" ref="f6d86033296f95d41dba6bd8713993b4" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#f6d86033296f95d41dba6bd8713993b4">counterDataImageSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] size calculated from cuptiProfilerCounterDataImageCalculateSize <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="88eef26c1d443fa13803c775d84078e3"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params::counterDataScratchBufferSize" ref="88eef26c1d443fa13803c775d84078e3" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#88eef26c1d443fa13803c775d84078e3">counterDataScratchBufferSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="d0d7ab35a470595e78208e014c9ebcf9"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params::pCounterDataImage" ref="d0d7ab35a470595e78208e014c9ebcf9" args="" -->
uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#d0d7ab35a470595e78208e014c9ebcf9">pCounterDataImage</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="4485c5c0d54a45cbc1f1b4756e55eadf"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params::pPriv" ref="4485c5c0d54a45cbc1f1b4756e55eadf" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#4485c5c0d54a45cbc1f1b4756e55eadf">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="e102811eea536a9ba9cf4944e071afb0"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params::structSize" ref="e102811eea536a9ba9cf4944e071afb0" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#e102811eea536a9ba9cf4944e071afb0">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params_STRUCT_SIZE <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
