<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>SanitizerApi: Sanitizer Callback API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Sanitizer Callback API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__BatchMemopData.html">Sanitizer_BatchMemopData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a batch memop callback function.  <a href="structSanitizer__BatchMemopData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__CallbackData.html">Sanitizer_CallbackData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a runtime or driver API callback function.  <a href="structSanitizer__CallbackData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__EventData.html">Sanitizer_EventData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into an event callback function.  <a href="structSanitizer__EventData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__GraphExecData.html">Sanitizer_GraphExecData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a graphexec creation callback function.  <a href="structSanitizer__GraphExecData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__GraphLaunchData.html">Sanitizer_GraphLaunchData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a graph launch callback function.  <a href="structSanitizer__GraphLaunchData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__GraphNodeLaunchData.html">Sanitizer_GraphNodeLaunchData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a graph node launch callback function.  <a href="structSanitizer__GraphNodeLaunchData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__LaunchData.html">Sanitizer_LaunchData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a launch callback function.  <a href="structSanitizer__LaunchData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__MemcpyData.html">Sanitizer_MemcpyData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a memcpy callback function.  <a href="structSanitizer__MemcpyData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__MemsetData.html">Sanitizer_MemsetData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a memset callback function.  <a href="structSanitizer__MemsetData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__ResourceArrayData.html">Sanitizer_ResourceArrayData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a CUDA array callback function.  <a href="structSanitizer__ResourceArrayData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__ResourceContextData.html">Sanitizer_ResourceContextData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a context resource callback function.  <a href="structSanitizer__ResourceContextData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__ResourceFunctionsLazyLoadedData.html">Sanitizer_ResourceFunctionsLazyLoadedData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a CUDA function callback function.  <a href="structSanitizer__ResourceFunctionsLazyLoadedData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__ResourceMemoryData.html">Sanitizer_ResourceMemoryData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a memory resource callback function.  <a href="structSanitizer__ResourceMemoryData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__ResourceMempoolData.html">Sanitizer_ResourceMempoolData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a mempool resource callback function.  <a href="structSanitizer__ResourceMempoolData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__ResourceModuleData.html">Sanitizer_ResourceModuleData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a module resource callback function.  <a href="structSanitizer__ResourceModuleData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__ResourceStreamData.html">Sanitizer_ResourceStreamData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a stream resource callback function.  <a href="structSanitizer__ResourceStreamData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__SynchronizeData.html">Sanitizer_SynchronizeData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a synchronization callback function.  <a href="structSanitizer__SynchronizeData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structSanitizer__UvmData.html">Sanitizer_UvmData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a managed memory callback function.  <a href="structSanitizer__UvmData.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef void(SANITIZERAPI *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gf031ccd4dcc013ab017d9ee1032858c4">Sanitizer_CallbackFunc</a> )(void *userdata, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a> domain, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6e4655fe27c8ee6f85661350bfbc7bd7">Sanitizer_CallbackId</a> cbid, const void *cbdata)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a callback.  <a href="#gf031ccd4dcc013ab017d9ee1032858c4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g6e4655fe27c8ee6f85661350bfbc7bd7"></a><!-- doxytag: member="SANITIZER_CALLBACK_API::Sanitizer_CallbackId" ref="g6e4655fe27c8ee6f85661350bfbc7bd7" args="" -->
typedef uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6e4655fe27c8ee6f85661350bfbc7bd7">Sanitizer_CallbackId</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback ID. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g11604a410fe99da425a0bd40b17c7464"></a><!-- doxytag: member="SANITIZER_CALLBACK_API::Sanitizer_SubscriberHandle" ref="g11604a410fe99da425a0bd40b17c7464" args="" -->
typedef struct <br class="typebreak">
Sanitizer_Subscriber_st *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A callback subscriber. <br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g0bc24cc0af4c2b8f9c50355c1c1d36ae">Sanitizer_ApiCallbackSite</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg0bc24cc0af4c2b8f9c50355c1c1d36ae2c586ea71b178fefe7bba33fbf934412">SANITIZER_API_ENTER</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg0bc24cc0af4c2b8f9c50355c1c1d36aeb0d68d7e16d4d3a67df49b7e0dd7e446">SANITIZER_API_EXIT</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Specifies the point in an API call that a callback is issued.  <a href="group__SANITIZER__CALLBACK__API.html#g0bc24cc0af4c2b8f9c50355c1c1d36ae">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gab53af117ae98aa6acf0a6b4dfb71cfa">Sanitizer_BatchMemopType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggab53af117ae98aa6acf0a6b4dfb71cfaee1dd1870400d2b5d4abc9bc1b274995">SANITIZER_BATCH_MEMOP_TYPE_32B</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggab53af117ae98aa6acf0a6b4dfb71cfa429cd1d863fefe747795c064679a6b7d">SANITIZER_BATCH_MEMOP_TYPE_64B</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Specifies the type of batch memory operation.  <a href="group__SANITIZER__CALLBACK__API.html#gab53af117ae98aa6acf0a6b4dfb71cfa">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gdf767501dec0e4e2deb55b0c76d35f5f">Sanitizer_CallackIdSync</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggdf767501dec0e4e2deb55b0c76d35f5fcf44b97bbe7f31befa64b938a3631fd1">SANITIZER_CBID_SYNCHRONIZE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggdf767501dec0e4e2deb55b0c76d35f5f2a581721ae7bfc5da910a4b21c38a323">SANITIZER_CBID_SYNCHRONIZE_STREAM_SYNCHRONIZED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggdf767501dec0e4e2deb55b0c76d35f5fb5a0541ea36095bad50c9a77cb7f2b37">SANITIZER_CBID_SYNCHRONIZE_CONTEXT_SYNCHRONIZED</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for synchronization domain.  <a href="group__SANITIZER__CALLBACK__API.html#gdf767501dec0e4e2deb55b0c76d35f5f">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46bd636f502a9364e0031d5fc0ea47d94e2">SANITIZER_CB_DOMAIN_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b584e0d10a04d1ce4d91b604ba17af174">SANITIZER_CB_DOMAIN_DRIVER_API</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b7aa34f41d42ddf2360cc8d54d8b0f521">SANITIZER_CB_DOMAIN_RUNTIME_API</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b5e762a37fc6de9651fd86615f2fa537b">SANITIZER_CB_DOMAIN_RESOURCE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46bb9bb9a6373d15cbff742fc1656bb0416">SANITIZER_CB_DOMAIN_SYNCHRONIZE</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b76da136b51d4868c98cb706c02bd3a60">SANITIZER_CB_DOMAIN_LAUNCH</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b95fbde80182449300ec201f264e7dfb3">SANITIZER_CB_DOMAIN_MEMCPY</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b8fd6b1f2d4230f466089bd823e8f7f6a">SANITIZER_CB_DOMAIN_MEMSET</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46bc413ad3c7766b3d5baa4857b9281328f">SANITIZER_CB_DOMAIN_BATCH_MEMOP</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b3fb8aa18ec9e7c7cab4b07ab6a7bcd08">SANITIZER_CB_DOMAIN_UVM</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46bbca42bd5841566bfa1c3fb4cf3cc24ba">SANITIZER_CB_DOMAIN_GRAPHS</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg2184025e5309c5989412b9f64beac46b55aaba2060ad6b533536107498647aa0">SANITIZER_CB_DOMAIN_EVENTS</a> =  11
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback domains.  <a href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#ge089f8eea1aee2408f559e304d9d0ffa">Sanitizer_CallbackIdBatchMemop</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gge089f8eea1aee2408f559e304d9d0ffac36e6935acb088cb8297fe031f0820bb">SANITIZER_CBID_BATCH_MEMOP_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gge089f8eea1aee2408f559e304d9d0ffa3d805bd2669a0619aa0e6cc639b716ca">SANITIZER_CBID_BATCH_MEMOP_WRITE</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for batch memop domain.  <a href="group__SANITIZER__CALLBACK__API.html#ge089f8eea1aee2408f559e304d9d0ffa">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gc64a40e719db73937178eba5ffea9b67">Sanitizer_CallbackIdEvents</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc64a40e719db73937178eba5ffea9b67bb3d0267b41ff1732614bc19f341e8e1">SANITIZER_CBID_EVENTS_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc64a40e719db73937178eba5ffea9b677ffbc0eff672e59e69664946cdf18b80">SANITIZER_CBID_EVENTS_CREATED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc64a40e719db73937178eba5ffea9b67b2e80fab3842b23f3441d720987eb10e">SANITIZER_CBID_EVENTS_DESTROYED</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc64a40e719db73937178eba5ffea9b674377192e40db2eccc3814762b34565f0">SANITIZER_CBID_EVENTS_RECORD</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc64a40e719db73937178eba5ffea9b674ddf6e8055238263b9f01565b20fe7a2">SANITIZER_CBID_EVENTS_STREAM_WAIT</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc64a40e719db73937178eba5ffea9b679ec800d3b0f497588ee7e6fd3f482ad7">SANITIZER_CBID_EVENTS_SYNCHRONIZE</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for events domain.  <a href="group__SANITIZER__CALLBACK__API.html#gc64a40e719db73937178eba5ffea9b67">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g65ad906ecc3006f2b49f63d8b09f38fd">Sanitizer_CallbackIdGraphs</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fdf18bdb5d2ccad6aa429f5c6165ee918b">SANITIZER_CBID_GRAPHS_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fd9fe0de660c091ef11ef7eacbd271935a">SANITIZER_CBID_GRAPHS_GRAPHEXEC_CREATING</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fd6c1bfed487cc476bba510aaab710085f">SANITIZER_CBID_GRAPHS_GRAPHEXEC_CREATED</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fd74ef5c3024f3805feb6477e80a13058d">SANITIZER_CBID_GRAPHS_GRAPHEXEC_DESTROYING</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fd9aa8253c5fe915071883bfbf16045a68">SANITIZER_CBID_GRAPHS_NODE_LAUNCH_BEGIN</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fd1a2e9175953b497467eb788f362fd38a">SANITIZER_CBID_GRAPHS_NODE_LAUNCH_END</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fd7c2be05510e7bc0f8d88746a21b259ec">SANITIZER_CBID_GRAPHS_LAUNCH_BEGIN</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg65ad906ecc3006f2b49f63d8b09f38fdeb48e77124225829861586f0daaa0bfb">SANITIZER_CBID_GRAPHS_LAUNCH_END</a> =  7
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for graphs domain.  <a href="group__SANITIZER__CALLBACK__API.html#g65ad906ecc3006f2b49f63d8b09f38fd">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g023cd56b97f1231cb0c4d11b4f8f95d4">Sanitizer_CallbackIdLaunch</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg023cd56b97f1231cb0c4d11b4f8f95d4a4c3af939550c790d74ff11a1dfa27cb">SANITIZER_CBID_LAUNCH_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg023cd56b97f1231cb0c4d11b4f8f95d4ed7d9f21349e1e6a0042178c717d9b8b">SANITIZER_CBID_LAUNCH_BEGIN</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg023cd56b97f1231cb0c4d11b4f8f95d420b20cd0488dc397426be7b416b97310">SANITIZER_CBID_LAUNCH_AFTER_SYSCALL_SETUP</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg023cd56b97f1231cb0c4d11b4f8f95d4e99da23dfb7ec9f9ae126088f0bf33dd">SANITIZER_CBID_LAUNCH_END</a> =  3
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for launch domain.  <a href="group__SANITIZER__CALLBACK__API.html#g023cd56b97f1231cb0c4d11b4f8f95d4">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9047997c07890c2fd3136ca731228bd5">Sanitizer_CallbackIdMemcpy</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg9047997c07890c2fd3136ca731228bd53ead2079459d28219a7a6f3aaa122c2b">SANITIZER_CBID_MEMCPY_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg9047997c07890c2fd3136ca731228bd589c7fd9883238502bb28e26e76dda383">SANITIZER_CBID_MEMCPY_STARTING</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for memcpy domain.  <a href="group__SANITIZER__CALLBACK__API.html#g9047997c07890c2fd3136ca731228bd5">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g70a5aabab8a7528c5213ee5f535b466c">Sanitizer_CallbackIdMemset</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg70a5aabab8a7528c5213ee5f535b466c2ed084a804472ab66f0d898049b601fb">SANITIZER_CBID_MEMSET_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg70a5aabab8a7528c5213ee5f535b466c627ffcfed33317f86d4adc88d0c8eb5a">SANITIZER_CBID_MEMSET_STARTING</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for memset domain.  <a href="group__SANITIZER__CALLBACK__API.html#g70a5aabab8a7528c5213ee5f535b466c">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gd447a2768c522f1d2b2a086a47705a49">Sanitizer_CallbackIdResource</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49d43439a668fcd368dd306a6fed0bf729">SANITIZER_CBID_RESOURCE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a491ee5702cda826f9ee18e8992a955d57c">SANITIZER_CBID_RESOURCE_INIT_FINISHED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49eedce847aa53b7df5cbad11f822db088">SANITIZER_CBID_RESOURCE_CONTEXT_CREATION_STARTING</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49e757f7d5ecc22ae45bc8990b02dca0f9">SANITIZER_CBID_RESOURCE_CONTEXT_CREATION_FINISHED</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a4913c6e827f9f82f075d46a7215b87aba4">SANITIZER_CBID_RESOURCE_CONTEXT_DESTROY_STARTING</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49df1aecd4fe1514ce55ce7888cddae732">SANITIZER_CBID_RESOURCE_CONTEXT_DESTROY_FINISHED</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a4936eece499c3921982d4c005c99eb4994">SANITIZER_CBID_RESOURCE_STREAM_CREATED</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a4968c8eb02e7f7b4823233eb77e7ce7494">SANITIZER_CBID_RESOURCE_STREAM_DESTROY_STARTING</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49c8951f93a907cc1e70f52d3fd6aba083">SANITIZER_CBID_RESOURCE_STREAM_DESTROY_FINISHED</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49cbed32968d4482003381ca8c50abe464">SANITIZER_CBID_RESOURCE_MODULE_LOADED</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49d2e3ceaa1adb9c6099c1d07a096eaf8d">SANITIZER_CBID_RESOURCE_MODULE_UNLOAD_STARTING</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a4962f2c8af52f8285f51684fa6ce776162">SANITIZER_CBID_RESOURCE_DEVICE_MEMORY_ALLOC</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a495638b6ba05cc418a127a123a743d9fda">SANITIZER_CBID_RESOURCE_DEVICE_MEMORY_FREE</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49ec3615d6adaf20d8b89637fd64bc0974">SANITIZER_CBID_RESOURCE_HOST_MEMORY_ALLOC</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49aabeddb71b2fa843418a90b8760ea9d7">SANITIZER_CBID_RESOURCE_HOST_MEMORY_FREE</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49fb5edae93d426e280a547e18490863ab">SANITIZER_CBID_RESOURCE_MEMORY_ALLOC_ASYNC</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49dc8da45895d34fc420bc5604e80edc49">SANITIZER_CBID_RESOURCE_MEMORY_FREE_ASYNC</a> =  16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49ad42f1afe7973734d5941952c40c4304">SANITIZER_CBID_RESOURCE_MEMORY_FREE_ASYNC_DONE</a> =  17, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a4940745c67c29fd59a8842a9a7bb6a807b">SANITIZER_CBID_RESOURCE_MEMPOOL_CREATED</a> =  18, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49b42ebe6b4e398725675c96d9aff15725">SANITIZER_CBID_RESOURCE_MEMPOOL_DESTROYING</a> =  19, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a497f8c0e11f215840906b2652e64e4d502">SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_ENABLED</a> =  20, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49f39a1fd0fde3d834f3af301ee9c0d1fd">SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_DISABLING</a> =  21, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a493240b79721f1a81d5de1b2c95ba6f98e">SANITIZER_CBID_RESOURCE_ARRAY_CREATED</a> =  22, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49c7167a9a77562b81eeb5ca89e69c0a48">SANITIZER_CBID_RESOURCE_ARRAY_DESTROYED</a> =  23, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd447a2768c522f1d2b2a086a47705a49697a6e4db682a1f34097ae42e461d21c">SANITIZER_CBID_RESOURCE_FUNCTIONS_LAZY_LOADED</a> =  24
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for resource domain.  <a href="group__SANITIZER__CALLBACK__API.html#gd447a2768c522f1d2b2a086a47705a49">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gd0dca53274794d63531e7993fb6248a0">Sanitizer_CallbackIdUvm</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd0dca53274794d63531e7993fb6248a0707ca0649121416d591274ad94cf38ae">SANITIZER_CBID_UVM_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd0dca53274794d63531e7993fb6248a092eb9620669167609f2623faedc47c2b">SANITIZER_CBID_UVM_ATTACH_MEM</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for managed memory domain.  <a href="group__SANITIZER__CALLBACK__API.html#gd0dca53274794d63531e7993fb6248a0">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6dd430318fea9a813020c974d07da85e">Sanitizer_MemcpyDirection</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg6dd430318fea9a813020c974d07da85e60dffdfafa7bcdb76a8adae0f7e4fb65">SANITIZER_MEMCPY_DIRECTION_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg6dd430318fea9a813020c974d07da85e5be6c4f49002493f69fba85794c571de">SANITIZER_MEMCPY_DIRECTION_HOST_TO_HOST</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg6dd430318fea9a813020c974d07da85e4bb24eea51783a23a040b5d840921724">SANITIZER_MEMCPY_DIRECTION_HOST_TO_DEVICE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg6dd430318fea9a813020c974d07da85ed745d5602c5688537b69de243c957330">SANITIZER_MEMCPY_DIRECTION_DEVICE_TO_HOST</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg6dd430318fea9a813020c974d07da85eba06c89f6f834bfc27291fe66a52bdb1">SANITIZER_MEMCPY_DIRECTION_DEVICE_TO_DEVICE</a> =  4
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Memcpy direction.  <a href="group__SANITIZER__CALLBACK__API.html#g6dd430318fea9a813020c974d07da85e">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g62d02f306fdb955ab1f5add810c45564">Sanitizer_MemoryVisibility</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg62d02f306fdb955ab1f5add810c45564ec98e94be6b92519a97382661efc7ec3">SANITIZER_MEMORY_VISIBILITY_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg62d02f306fdb955ab1f5add810c455647054a49c377ec2be82a2a0649e470cd7">SANITIZER_MEMORY_VISIBILITY_GLOBAL</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg62d02f306fdb955ab1f5add810c455640ce73516f2ab9ecebda2870c42403c87">SANITIZER_MEMORY_VISIBILITY_HOST</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gg62d02f306fdb955ab1f5add810c455643c1f84ccf3f2c63b65630004aafbaee9">SANITIZER_MEMORY_VISIBILITY_STREAM</a> =  3
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Specifies the visibility of an allocation.  <a href="group__SANITIZER__CALLBACK__API.html#g62d02f306fdb955ab1f5add810c45564">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gd29155bb34524587a5a42d41be1d3fb2">Sanitizer_ResourceMemoryFlags</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb2fe628d1619f6084b2ff9d546d62754d5">SANITIZER_MEMORY_FLAG_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb2de496bf8bb3e2f56a4e5b4f13ccf1958">SANITIZER_MEMORY_FLAG_MODULE</a> =  0x1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb2b4f87d77b74a0fcdb803e884896f4e1d">SANITIZER_MEMORY_FLAG_MANAGED</a> =  0x2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb28e50f29060b27b767df0a344dd68f5d0">SANITIZER_MEMORY_FLAG_HOST_MAPPED</a> =  0x4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb2edd0c300182b331d0efa950226e6eb2d">SANITIZER_MEMORY_FLAG_HOST_PINNED</a> =  0x8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb2e48682eff0dbf2410d29e85dcba2363c">SANITIZER_MEMORY_FLAG_PEER</a> =  0x10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb25f5c733652a1f6523aba62c52ec17cf8">SANITIZER_MEMORY_FLAG_PEER_ATOMIC</a> =  0x20, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb2d658f4fb8f76628770b7600cb445a42b">SANITIZER_MEMORY_FLAG_CG_RUNTIME</a> =  0x40, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggd29155bb34524587a5a42d41be1d3fb2fb39111f58e43b9e00035279a321396f">SANITIZER_MEMORY_FLAG_CNP</a> =  0x80
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags describing a memory allocation.  <a href="group__SANITIZER__CALLBACK__API.html#gd29155bb34524587a5a42d41be1d3fb2">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gc172db1fa20fecca6ac23c2954ae15a1">Sanitizer_ResourceMemoryPermissions</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc172db1fa20fecca6ac23c2954ae15a1d007a64340a1fa1c858e1735e2b732e9">SANITIZER_MEMORY_PERMISSION_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc172db1fa20fecca6ac23c2954ae15a1e75db3c063e08e0e62554d4ea3f6f555">SANITIZER_MEMORY_PERMISSION_READ</a> =  0x1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc172db1fa20fecca6ac23c2954ae15a15d9b919025a4c997d342a44e6545e985">SANITIZER_MEMORY_PERMISSION_WRITE</a> =  0x2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc172db1fa20fecca6ac23c2954ae15a17104209c55e959075959198586bf8ec7">SANITIZER_MEMORY_PERMISSION_ATOMIC</a> =  0x4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ggc172db1fa20fecca6ac23c2954ae15a109f85c91d0f602a335c9f8511bbe1069">SANITIZER_MEMORY_PERMISSION_ALL</a> =  0x7
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Permissions for a memory allocation.  <a href="group__SANITIZER__CALLBACK__API.html#gc172db1fa20fecca6ac23c2954ae15a1">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g333a1254396a7a8ba5f8921d1f65642a">sanitizerEnableAllDomains</a> (uint32_t enable, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a> subscriber)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable or disable all callbacks in all domains.  <a href="#g333a1254396a7a8ba5f8921d1f65642a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g5463cc746520167ff93e22636549b66b">sanitizerEnableCallback</a> (uint32_t enable, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a> subscriber, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a> domain, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6e4655fe27c8ee6f85661350bfbc7bd7">Sanitizer_CallbackId</a> cbid)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable or disable callbacks for a specific domain and callback ID.  <a href="#g5463cc746520167ff93e22636549b66b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gffda58e1b795d575a0439557ff9f6fa5">sanitizerEnableDomain</a> (uint32_t enable, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a> subscriber, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a> domain)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable or disable all callbacks for a specific domain.  <a href="#gffda58e1b795d575a0439557ff9f6fa5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g631b78eb2d8bef7ac892461cda0eff67">sanitizerGetCallbackState</a> (uint32_t *enable, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a> subscriber, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a> domain, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6e4655fe27c8ee6f85661350bfbc7bd7">Sanitizer_CallbackId</a> cbid)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the current enabled/disabled state of a callback for a specific domain and function ID.  <a href="#g631b78eb2d8bef7ac892461cda0eff67"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g2740c794198ce1cae40f02d5bad3ab8b">sanitizerSubscribe</a> (<a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a> *subscriber, <a class="el" href="group__SANITIZER__CALLBACK__API.html#gf031ccd4dcc013ab017d9ee1032858c4">Sanitizer_CallbackFunc</a> callback, void *userdata)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize a callback subscriber with a callback function and user data.  <a href="#g2740c794198ce1cae40f02d5bad3ab8b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g143e5fe94ccaab6559c7af04df01a0e8">sanitizerUnsubscribe</a> (<a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a> subscriber)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Unregister a callback subscriber.  <a href="#g143e5fe94ccaab6559c7af04df01a0e8"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Variables</h2></td></tr>
<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;<a class="el" href="structSanitizer__LaunchData.html">Sanitizer_LaunchData</a>&nbsp;&nbsp;&nbsp;<a class="el" href="structSanitizer__GraphNodeLaunchData.html#b72a6cc5de9dbd55624130fb65b66f7b">launchData</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;<a class="el" href="structSanitizer__ResourceMemoryData.html">Sanitizer_ResourceMemoryData</a>&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ge334046a272b7c07489c0d80fab9f8b6">Sanitizer_GraphNodeLaunchData::memAllocData</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;<a class="el" href="structSanitizer__MemcpyData.html">Sanitizer_MemcpyData</a>&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gf2b961a7eae0b73c12f7ec099d4726f6">Sanitizer_GraphNodeLaunchData::memcpyData</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint64_t&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gda8d9c0a133719825143b35a3e544eea">Sanitizer_GraphNodeLaunchData::memFreeAddress</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;<a class="el" href="structSanitizer__MemsetData.html">Sanitizer_MemsetData</a>&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ga931adc0a0129a4490a2c6bb786b8d0f">Sanitizer_GraphNodeLaunchData::memsetData</a></td></tr>

<tr><td class="memItemLeft" nowrap valign="top">};&nbsp;</td><td class="memItemRight" valign="bottom"></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g1e8e80e5a6533ffea2ca5a435e0827fe">Sanitizer_UvmData::address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gb535f9480452944a891c80ba054268ba">Sanitizer_BatchMemopData::address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g86864b17ab8ef4bcbdf9cf27ea86b6a9">Sanitizer_MemsetData::address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g0f74ceb1522b0d27bb1d38ce41f83d73">Sanitizer_LaunchData::apiContext</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g73236f03601aa99a589d4c46a4a8c19e">Sanitizer_LaunchData::apiStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6da08037d5ae573a39e4f54183a6db35">Sanitizer_EventData::context</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g306fed61e5fc1aefe907f67b6aeb3c7b">Sanitizer_ResourceMemoryData::context</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6952454cc861e5a97c40d9e459a90351">Sanitizer_CallbackData::context</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g4d94a109dba9f60ed5bae597ea93b2b7">Sanitizer_ResourceModuleData::cubinSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g54ac0dec13a70e0cb9d1dc3849c40063">Sanitizer_LaunchData::device</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9d01c7ed7b6d8b3ea539b530393edb43">Sanitizer_ResourceMempoolData::device</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gc23e893ab78877ab395678c4dc1bddfa">Sanitizer_ResourceMemoryData::device</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gf1b86ab2888f0132953f56bfb63bfb38">Sanitizer_ResourceContextData::device</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6dd430318fea9a813020c974d07da85e">Sanitizer_MemcpyDirection</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g01d588f3dbd03429170cf7a9c64ecbca">Sanitizer_MemcpyData::direction</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g176afbebea17970c434b9d5a7c1e2eb9">Sanitizer_MemcpyData::dstAddress</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#ga536604f8271c4d7d3c77ced6bb87899">Sanitizer_MemcpyData::dstContext</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9a4d69875b446d382590721f3e352ab1">Sanitizer_MemcpyData::dstPitch</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gf11d697d3d994de8b25059b1992b3135">Sanitizer_MemcpyData::dstStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g7bee77cc4075471b0397cf557c843c4a">Sanitizer_ResourceMemoryData::flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUfunction&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6ae473df62bfb4cac682ebedaf8627fd">Sanitizer_LaunchData::function</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gf217ff198ada2e206aa01e3e2f4aaef8">Sanitizer_LaunchData::functionName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gcffbfadfe4c7af894d8e39407595efd2">Sanitizer_CallbackData::functionName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g3866442ee35daa08d5bbd2eeb366f91f">Sanitizer_CallbackData::functionParams</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gb523a3af8e202cbbdd706277014041fa">Sanitizer_CallbackData::functionReturnValue</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const CUfunction *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g54c88e675e099bc4f01351f871107aab">Sanitizer_ResourceFunctionsLazyLoadedData::functions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphExec&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6c69e3440ef748c6c5338505f6bb521e">Sanitizer_GraphLaunchData::graphExec</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphExec&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gb1a817799b020868a692b5b2d82be2a9">Sanitizer_GraphExecData::graphExec</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gb9302cae916c7eaab0c6de268b246e59">Sanitizer_LaunchData::gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gb4c74f8d8eaa10a76a9eb055e02bbfdb">Sanitizer_LaunchData::hApiStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUarray&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6dbe2da80b68f48859499c24d325b9f0">Sanitizer_ResourceArrayData::hArray</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g3e5d47e9ca81bbf4192dc9cd4958ca9d">Sanitizer_MemcpyData::hDstStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_LaunchHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g57fec3035b443ac0cc7e35908ca53117">Sanitizer_LaunchData::hLaunch</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gbe70ebd1173d6137ca064e66b4b99965">Sanitizer_MemcpyData::hSrcStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g2f6747e6a89033d27f91d7b72e5599be">Sanitizer_EventData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g27bdfc7058401c7a88e7bf538752c5ab">Sanitizer_GraphLaunchData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g0e5aefe3e911df95c5a4b6f0e227467e">Sanitizer_UvmData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g3ef9cb88408697511f56effdfeed5da8">Sanitizer_BatchMemopData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g0246494f29ba1644b2a3abb5ac2d6cee">Sanitizer_MemsetData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g07a4addf3473a03f38060b769517bd9e">Sanitizer_LaunchData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g1ef3eb36bdce259175dab94c3cee852d">Sanitizer_SynchronizeData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9cd401e3148076d3b5cabcdbf54f3f33">Sanitizer_ResourceMemoryData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">Sanitizer_StreamHandle&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g17058d030f8c196b4a250148c3e2c662">Sanitizer_ResourceStreamData::hStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g25e97b753473f3e78a76cbe75d66758c">Sanitizer_MemsetData::isAsync</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gd42ab4aa1a7f1ffbfe4e3787d45141f6">Sanitizer_MemcpyData::isAsync</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gc77f7a54e4953682dcb29ff7bf794914">Sanitizer_GraphExecData::isDeviceLaunch</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g43ba40476321bc1b026eab8e3ef364d1">Sanitizer_GraphLaunchData::isGraphUpload</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g72f04317e3ea0b0ba1e62d54c06606ad">Sanitizer_GraphNodeLaunchData::isGraphUpload</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gdf81e4d9627fd5f003f01075878e2a89">Sanitizer_GraphNodeLaunchData::launchId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUlibrary&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g0f7484826c1b8bb5f2eca75040fd44cc">Sanitizer_ResourceModuleData::library</a></td></tr>

<tr><td class="memItemLeft" nowrap><a class="el" href="structSanitizer__ResourceMemoryData.html">Sanitizer_ResourceMemoryData</a>&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ge334046a272b7c07489c0d80fab9f8b6">Sanitizer_GraphNodeLaunchData::memAllocData</a></td></tr>

<tr><td class="memItemLeft" nowrap><a class="el" href="structSanitizer__MemcpyData.html">Sanitizer_MemcpyData</a>&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gf2b961a7eae0b73c12f7ec099d4726f6">Sanitizer_GraphNodeLaunchData::memcpyData</a></td></tr>

<tr><td class="memItemLeft" nowrap>uint64_t&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#gda8d9c0a133719825143b35a3e544eea">Sanitizer_GraphNodeLaunchData::memFreeAddress</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUmemoryPool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gbe9335a4874c70f9e8fdc287b8a14a38">Sanitizer_ResourceMemoryData::memoryPool</a></td></tr>

<tr><td class="memItemLeft" nowrap><a class="el" href="structSanitizer__MemsetData.html">Sanitizer_MemsetData</a>&nbsp;&nbsp;&nbsp;<a class="el" href="group__SANITIZER__CALLBACK__API.html#ga931adc0a0129a4490a2c6bb786b8d0f">Sanitizer_GraphNodeLaunchData::memsetData</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUmodule&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g149c3ada82b4937473864e2b27bc5825">Sanitizer_LaunchData::module</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUmodule&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g549f330264682c91331b433868c166fc">Sanitizer_ResourceFunctionsLazyLoadedData::module</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUmodule&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g786e3c1a3f4f6d4611f7fa0b4093dca7">Sanitizer_ResourceModuleData::module</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphNode&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g86c2a7422b28ed2929f072e3d546b9c3">Sanitizer_GraphNodeLaunchData::node</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUgraphNodeType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#ge6c93bc5c7a5dc8a5d6ec72a42f87526">Sanitizer_GraphNodeLaunchData::nodeType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g2fc6eb609d8345b59f58b5469e73e121">Sanitizer_ResourceFunctionsLazyLoadedData::numFunctions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g39bce574d2d1e1ef944c1090ce01b09a">Sanitizer_ResourceModuleData::pCubin</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g60c827072502fc8e4aa10c8e8abd8e1b">Sanitizer_ResourceMempoolData::peerDevice</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gfa68ea5df21d945ae35162a7573c4d61">Sanitizer_ResourceMemoryData::permissions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g297d5464c98df6630501e117c1262b48">Sanitizer_MemcpyData::size</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gf33b6cf45e80c06fc9130c719d6e13c7">Sanitizer_ResourceMemoryData::size</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g34ae0e07bbf2f9ae371d132c59138ce0">Sanitizer_ResourceMemoryData::sourceDevice</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gb300021e73a68db7b679bebfc81425e6">Sanitizer_MemcpyData::srcAddress</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gc851b50eb12aa0e38d1bf5e349b5a66b">Sanitizer_MemcpyData::srcPitch</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g5dbf45c2f3f09b40d901628ab557ef48">Sanitizer_MemcpyData::srcStream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g96c543b1fdcbd06824271501fc8d1b89">Sanitizer_EventData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g4e19a42decf1a32b5582e4ba19346209">Sanitizer_GraphLaunchData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#ga2452503ea44bf5b345f230da98b9402">Sanitizer_UvmData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gd0b50ad0f897b982a76d9542253fec93">Sanitizer_BatchMemopData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g568656a07f80c2747dfd6eaf3ac6fa95">Sanitizer_MemsetData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g670de4d81de9ea2c2a2f875281f3d726">Sanitizer_LaunchData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g88f86980a41884a31acac2b04f1d42f9">Sanitizer_SynchronizeData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gbb146be5d2857f1294a1af6edf28f6f5">Sanitizer_ResourceMemoryData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9ac109c0cc16da3636538dfcebaeac33">Sanitizer_ResourceStreamData::stream</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g1ee030118e093f440f953d896ee80be6">Sanitizer_CallbackData::symbolName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gab53af117ae98aa6acf0a6b4dfb71cfa">Sanitizer_BatchMemopType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g874b875cc64512766ed6d8f43fea3afd">Sanitizer_BatchMemopData::type</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g61c24785086fa41e344d54faa076b6d0">Sanitizer_BatchMemopData::value</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g5fc409e9d272bef19b26b754103152f2">Sanitizer_MemsetData::value</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g62d02f306fdb955ab1f5add810c45564">Sanitizer_MemoryVisibility</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9c9d131f19f523c3c107ea5ea55f6676">Sanitizer_UvmData::visibility</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g62d02f306fdb955ab1f5add810c45564">Sanitizer_MemoryVisibility</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g5838f90e131f6b6761c39986b9b6efd8">Sanitizer_ResourceMemoryData::visibility</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gdf48f929e8ccaf649688cdfac51b63a2">Sanitizer_MemsetData::width</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9bb9e8fa40bb441864e2620ebb2eb430">Sanitizer_MemcpyData::width</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gda45296bad0f62f6df2ca07a7b545794">Sanitizer_ResourceArrayData::width</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gd71d9596f42c5280117c58aec28f7677">Sanitizer_LaunchData::blockDim_x</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#geaccddc162d325b688fb59813e854df6">Sanitizer_LaunchData::blockDim_y</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gdea40e9c9cab2e4b98b0e0abaf04ed7a">Sanitizer_LaunchData::blockDim_z</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gc83c9a72971e8b4fcbae6e5299a0bb52">Sanitizer_LaunchData::clusterDim_x</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gb69bc0f86a204aef81bbb27ab3599121">Sanitizer_LaunchData::clusterDim_y</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g3d0219ba9537466c00a8d8d6f7fe5232">Sanitizer_LaunchData::clusterDim_z</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g934581cb477c3d030f30e2b2ee7944d4">Sanitizer_LaunchData::gridDim_x</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g66f032ce9b5eaac75a5993167cf282c8">Sanitizer_LaunchData::gridDim_y</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g9a0d553822926781996a01e8255ac049">Sanitizer_LaunchData::gridDim_z</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the Sanitizer Callback API. <hr><h2>Typedef Documentation</h2>
<a class="anchor" name="gf031ccd4dcc013ab017d9ee1032858c4"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackFunc" ref="gf031ccd4dcc013ab017d9ee1032858c4" args=")(void *userdata, Sanitizer_CallbackDomain domain, Sanitizer_CallbackId cbid, const void *cbdata)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(SANITIZERAPI * <a class="el" href="group__SANITIZER__CALLBACK__API.html#gf031ccd4dcc013ab017d9ee1032858c4">Sanitizer_CallbackFunc</a>)(void *userdata, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a> domain, <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6e4655fe27c8ee6f85661350bfbc7bd7">Sanitizer_CallbackId</a> cbid, const void *cbdata)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Function type for a callback. The type of the data passed to the callback in <code>cbdata</code> depends on the domain. If <code>domain</code> is SANITIZER_CB_DOMAIN_DRIVER_API or SANITIZER_CB_DOMAIN_RUNTIME_API the type of <code>cbdata</code> will be <a class="el" href="structSanitizer__CallbackData.html" title="Data passed into a runtime or driver API callback function.">Sanitizer_CallbackData</a>. If <code>domain</code> is SANITIZER_CB_DOMAIN_RESOURCE the type of <code>cbdata</code> will be dependent on cbid. Refer to <a class="el" href="structSanitizer__ResourceContextData.html">Sanitizer_ResourceContextData</a>, <a class="el" href="structSanitizer__ResourceStreamData.html">Sanitizer_ResourceStreamData</a>, <a class="el" href="structSanitizer__ResourceModuleData.html">Sanitizer_ResourceModuleData</a> and <a class="el" href="group__SANITIZER__CALLBACK__API.html#gd29155bb34524587a5a42d41be1d3fb2">Sanitizer_ResourceMemoryFlags</a> documentations. If <code>domain</code> is SANITIZER_CB_DOMAIN_SYNCHRONIZE the type of <code>cbdata</code> will be <a class="el" href="structSanitizer__SynchronizeData.html" title="Data passed into a synchronization callback function.">Sanitizer_SynchronizeData</a>. If <code>domain</code> is SANITIZER_CB_DOMAIN_LAUNCH the type of <code>cbdata</code> will be <a class="el" href="structSanitizer__LaunchData.html" title="Data passed into a launch callback function.">Sanitizer_LaunchData</a>. If <code>domain</code> is SANITIZER_CB_DOMAIN_MEMCPY the type of <code>cbdata</code> will be <a class="el" href="structSanitizer__MemcpyData.html" title="Data passed into a memcpy callback function.">Sanitizer_MemcpyData</a>. If <code>domain</code> is SANITIZER_CB_DOMAIN_MEMSET the type of <code>cbdata</code> will be <a class="el" href="structSanitizer__MemsetData.html" title="Data passed into a memset callback function.">Sanitizer_MemsetData</a>. If <code>domain</code> is SANITIZER_CB_DOMAIN_BATCH_MEMOP the type of <code>cbdata</code> will be <a class="el" href="structSanitizer__BatchMemopData.html" title="Data passed into a batch memop callback function.">Sanitizer_BatchMemopData</a>. 
</div>
</div><p>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g0bc24cc0af4c2b8f9c50355c1c1d36ae"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_ApiCallbackSite" ref="g0bc24cc0af4c2b8f9c50355c1c1d36ae" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g0bc24cc0af4c2b8f9c50355c1c1d36ae">Sanitizer_ApiCallbackSite</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Specifies the point in an API that a callback is issued. This value is communicated to the callback function via Sanitizer_CallbackData::CallbackSize. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg0bc24cc0af4c2b8f9c50355c1c1d36ae2c586ea71b178fefe7bba33fbf934412"></a><!-- doxytag: member="SANITIZER_API_ENTER" ref="gg0bc24cc0af4c2b8f9c50355c1c1d36ae2c586ea71b178fefe7bba33fbf934412" args="" -->SANITIZER_API_ENTER</em>&nbsp;</td><td>
This callback is at API entry. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg0bc24cc0af4c2b8f9c50355c1c1d36aeb0d68d7e16d4d3a67df49b7e0dd7e446"></a><!-- doxytag: member="SANITIZER_API_EXIT" ref="gg0bc24cc0af4c2b8f9c50355c1c1d36aeb0d68d7e16d4d3a67df49b7e0dd7e446" args="" -->SANITIZER_API_EXIT</em>&nbsp;</td><td>
This callback is at API exit. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gab53af117ae98aa6acf0a6b4dfb71cfa"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_BatchMemopType" ref="gab53af117ae98aa6acf0a6b4dfb71cfa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#gab53af117ae98aa6acf0a6b4dfb71cfa">Sanitizer_BatchMemopType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Specifies the type of batch memory operation reported by a callback in domain SANITIZER_CB_DOMAIN_BATCH_MEMOP. This value is communicated to the callback function via <a class="el" href="group__SANITIZER__CALLBACK__API.html#g874b875cc64512766ed6d8f43fea3afd">Sanitizer_BatchMemopData::type</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggab53af117ae98aa6acf0a6b4dfb71cfaee1dd1870400d2b5d4abc9bc1b274995"></a><!-- doxytag: member="SANITIZER_BATCH_MEMOP_TYPE_32B" ref="ggab53af117ae98aa6acf0a6b4dfb71cfaee1dd1870400d2b5d4abc9bc1b274995" args="" -->SANITIZER_BATCH_MEMOP_TYPE_32B</em>&nbsp;</td><td>
Batch memory operation size is 32 bits. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggab53af117ae98aa6acf0a6b4dfb71cfa429cd1d863fefe747795c064679a6b7d"></a><!-- doxytag: member="SANITIZER_BATCH_MEMOP_TYPE_64B" ref="ggab53af117ae98aa6acf0a6b4dfb71cfa429cd1d863fefe747795c064679a6b7d" args="" -->SANITIZER_BATCH_MEMOP_TYPE_64B</em>&nbsp;</td><td>
Batch memory operation size is 64 bits. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gdf767501dec0e4e2deb55b0c76d35f5f"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallackIdSync" ref="gdf767501dec0e4e2deb55b0c76d35f5f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#gdf767501dec0e4e2deb55b0c76d35f5f">Sanitizer_CallackIdSync</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_SYNCHRONIZE. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggdf767501dec0e4e2deb55b0c76d35f5fcf44b97bbe7f31befa64b938a3631fd1"></a><!-- doxytag: member="SANITIZER_CBID_SYNCHRONIZE_INVALID" ref="ggdf767501dec0e4e2deb55b0c76d35f5fcf44b97bbe7f31befa64b938a3631fd1" args="" -->SANITIZER_CBID_SYNCHRONIZE_INVALID</em>&nbsp;</td><td>
Invalid synchronize callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf767501dec0e4e2deb55b0c76d35f5f2a581721ae7bfc5da910a4b21c38a323"></a><!-- doxytag: member="SANITIZER_CBID_SYNCHRONIZE_STREAM_SYNCHRONIZED" ref="ggdf767501dec0e4e2deb55b0c76d35f5f2a581721ae7bfc5da910a4b21c38a323" args="" -->SANITIZER_CBID_SYNCHRONIZE_STREAM_SYNCHRONIZED</em>&nbsp;</td><td>
Stream synchronization has completed for a given stream. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf767501dec0e4e2deb55b0c76d35f5fb5a0541ea36095bad50c9a77cb7f2b37"></a><!-- doxytag: member="SANITIZER_CBID_SYNCHRONIZE_CONTEXT_SYNCHRONIZED" ref="ggdf767501dec0e4e2deb55b0c76d35f5fb5a0541ea36095bad50c9a77cb7f2b37" args="" -->SANITIZER_CBID_SYNCHRONIZE_CONTEXT_SYNCHRONIZED</em>&nbsp;</td><td>
Context synchronization has completed for a given context. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2184025e5309c5989412b9f64beac46b"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackDomain" ref="g2184025e5309c5989412b9f64beac46b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback domain. Each domain represents callback points for a group of related API functions or CUDA driver activity. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46bd636f502a9364e0031d5fc0ea47d94e2"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_INVALID" ref="gg2184025e5309c5989412b9f64beac46bd636f502a9364e0031d5fc0ea47d94e2" args="" -->SANITIZER_CB_DOMAIN_INVALID</em>&nbsp;</td><td>
Invalid domain. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b584e0d10a04d1ce4d91b604ba17af174"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_DRIVER_API" ref="gg2184025e5309c5989412b9f64beac46b584e0d10a04d1ce4d91b604ba17af174" args="" -->SANITIZER_CB_DOMAIN_DRIVER_API</em>&nbsp;</td><td>
Domain containing callback points for all driver API functions. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b7aa34f41d42ddf2360cc8d54d8b0f521"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_RUNTIME_API" ref="gg2184025e5309c5989412b9f64beac46b7aa34f41d42ddf2360cc8d54d8b0f521" args="" -->SANITIZER_CB_DOMAIN_RUNTIME_API</em>&nbsp;</td><td>
Domain containing callback points for all runtime API functions. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b5e762a37fc6de9651fd86615f2fa537b"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_RESOURCE" ref="gg2184025e5309c5989412b9f64beac46b5e762a37fc6de9651fd86615f2fa537b" args="" -->SANITIZER_CB_DOMAIN_RESOURCE</em>&nbsp;</td><td>
Domain containing callback points for CUDA resource tracking. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46bb9bb9a6373d15cbff742fc1656bb0416"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_SYNCHRONIZE" ref="gg2184025e5309c5989412b9f64beac46bb9bb9a6373d15cbff742fc1656bb0416" args="" -->SANITIZER_CB_DOMAIN_SYNCHRONIZE</em>&nbsp;</td><td>
Domain containing callback points for CUDA synchronization. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b76da136b51d4868c98cb706c02bd3a60"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_LAUNCH" ref="gg2184025e5309c5989412b9f64beac46b76da136b51d4868c98cb706c02bd3a60" args="" -->SANITIZER_CB_DOMAIN_LAUNCH</em>&nbsp;</td><td>
Domain containing callback points for CUDA grid launches. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b95fbde80182449300ec201f264e7dfb3"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_MEMCPY" ref="gg2184025e5309c5989412b9f64beac46b95fbde80182449300ec201f264e7dfb3" args="" -->SANITIZER_CB_DOMAIN_MEMCPY</em>&nbsp;</td><td>
Domain containing callback points for CUDA memcpy operations. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b8fd6b1f2d4230f466089bd823e8f7f6a"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_MEMSET" ref="gg2184025e5309c5989412b9f64beac46b8fd6b1f2d4230f466089bd823e8f7f6a" args="" -->SANITIZER_CB_DOMAIN_MEMSET</em>&nbsp;</td><td>
Domain containing callback points for CUDA memset operations. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46bc413ad3c7766b3d5baa4857b9281328f"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_BATCH_MEMOP" ref="gg2184025e5309c5989412b9f64beac46bc413ad3c7766b3d5baa4857b9281328f" args="" -->SANITIZER_CB_DOMAIN_BATCH_MEMOP</em>&nbsp;</td><td>
Domain containing callback points for CUDA batch memop operations. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b3fb8aa18ec9e7c7cab4b07ab6a7bcd08"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_UVM" ref="gg2184025e5309c5989412b9f64beac46b3fb8aa18ec9e7c7cab4b07ab6a7bcd08" args="" -->SANITIZER_CB_DOMAIN_UVM</em>&nbsp;</td><td>
Domain containing callback points for CUDA managed memory operations. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46bbca42bd5841566bfa1c3fb4cf3cc24ba"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_GRAPHS" ref="gg2184025e5309c5989412b9f64beac46bbca42bd5841566bfa1c3fb4cf3cc24ba" args="" -->SANITIZER_CB_DOMAIN_GRAPHS</em>&nbsp;</td><td>
Domain containing callback points for CUDA graphs operations. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2184025e5309c5989412b9f64beac46b55aaba2060ad6b533536107498647aa0"></a><!-- doxytag: member="SANITIZER_CB_DOMAIN_EVENTS" ref="gg2184025e5309c5989412b9f64beac46b55aaba2060ad6b533536107498647aa0" args="" -->SANITIZER_CB_DOMAIN_EVENTS</em>&nbsp;</td><td>
Domain containing callback points for CUDA events. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="ge089f8eea1aee2408f559e304d9d0ffa"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdBatchMemop" ref="ge089f8eea1aee2408f559e304d9d0ffa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#ge089f8eea1aee2408f559e304d9d0ffa">Sanitizer_CallbackIdBatchMemop</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_BATCH_MEMOP. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gge089f8eea1aee2408f559e304d9d0ffac36e6935acb088cb8297fe031f0820bb"></a><!-- doxytag: member="SANITIZER_CBID_BATCH_MEMOP_INVALID" ref="gge089f8eea1aee2408f559e304d9d0ffac36e6935acb088cb8297fe031f0820bb" args="" -->SANITIZER_CBID_BATCH_MEMOP_INVALID</em>&nbsp;</td><td>
Invalid batch memop callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge089f8eea1aee2408f559e304d9d0ffa3d805bd2669a0619aa0e6cc639b716ca"></a><!-- doxytag: member="SANITIZER_CBID_BATCH_MEMOP_WRITE" ref="gge089f8eea1aee2408f559e304d9d0ffa3d805bd2669a0619aa0e6cc639b716ca" args="" -->SANITIZER_CBID_BATCH_MEMOP_WRITE</em>&nbsp;</td><td>
A batch memory operation was initiated. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc64a40e719db73937178eba5ffea9b67"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdEvents" ref="gc64a40e719db73937178eba5ffea9b67" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#gc64a40e719db73937178eba5ffea9b67">Sanitizer_CallbackIdEvents</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_EVENTS. This value is communicated to the callback function via the <code>cbid</code> parameter. Available with a driver version of 515 or newer. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggc64a40e719db73937178eba5ffea9b67bb3d0267b41ff1732614bc19f341e8e1"></a><!-- doxytag: member="SANITIZER_CBID_EVENTS_INVALID" ref="ggc64a40e719db73937178eba5ffea9b67bb3d0267b41ff1732614bc19f341e8e1" args="" -->SANITIZER_CBID_EVENTS_INVALID</em>&nbsp;</td><td>
Invalid event callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc64a40e719db73937178eba5ffea9b677ffbc0eff672e59e69664946cdf18b80"></a><!-- doxytag: member="SANITIZER_CBID_EVENTS_CREATED" ref="ggc64a40e719db73937178eba5ffea9b677ffbc0eff672e59e69664946cdf18b80" args="" -->SANITIZER_CBID_EVENTS_CREATED</em>&nbsp;</td><td>
An event was created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc64a40e719db73937178eba5ffea9b67b2e80fab3842b23f3441d720987eb10e"></a><!-- doxytag: member="SANITIZER_CBID_EVENTS_DESTROYED" ref="ggc64a40e719db73937178eba5ffea9b67b2e80fab3842b23f3441d720987eb10e" args="" -->SANITIZER_CBID_EVENTS_DESTROYED</em>&nbsp;</td><td>
An event was destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc64a40e719db73937178eba5ffea9b674377192e40db2eccc3814762b34565f0"></a><!-- doxytag: member="SANITIZER_CBID_EVENTS_RECORD" ref="ggc64a40e719db73937178eba5ffea9b674377192e40db2eccc3814762b34565f0" args="" -->SANITIZER_CBID_EVENTS_RECORD</em>&nbsp;</td><td>
An event was recorded. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc64a40e719db73937178eba5ffea9b674ddf6e8055238263b9f01565b20fe7a2"></a><!-- doxytag: member="SANITIZER_CBID_EVENTS_STREAM_WAIT" ref="ggc64a40e719db73937178eba5ffea9b674ddf6e8055238263b9f01565b20fe7a2" args="" -->SANITIZER_CBID_EVENTS_STREAM_WAIT</em>&nbsp;</td><td>
A stream was synchronized to an event. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc64a40e719db73937178eba5ffea9b679ec800d3b0f497588ee7e6fd3f482ad7"></a><!-- doxytag: member="SANITIZER_CBID_EVENTS_SYNCHRONIZE" ref="ggc64a40e719db73937178eba5ffea9b679ec800d3b0f497588ee7e6fd3f482ad7" args="" -->SANITIZER_CBID_EVENTS_SYNCHRONIZE</em>&nbsp;</td><td>
An event was synchronized. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g65ad906ecc3006f2b49f63d8b09f38fd"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdGraphs" ref="g65ad906ecc3006f2b49f63d8b09f38fd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g65ad906ecc3006f2b49f63d8b09f38fd">Sanitizer_CallbackIdGraphs</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_GRAPHS. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fdf18bdb5d2ccad6aa429f5c6165ee918b"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_INVALID" ref="gg65ad906ecc3006f2b49f63d8b09f38fdf18bdb5d2ccad6aa429f5c6165ee918b" args="" -->SANITIZER_CBID_GRAPHS_INVALID</em>&nbsp;</td><td>
Invalid graphs callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fd9fe0de660c091ef11ef7eacbd271935a"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_GRAPHEXEC_CREATING" ref="gg65ad906ecc3006f2b49f63d8b09f38fd9fe0de660c091ef11ef7eacbd271935a" args="" -->SANITIZER_CBID_GRAPHS_GRAPHEXEC_CREATING</em>&nbsp;</td><td>
A new graphexec is being created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fd6c1bfed487cc476bba510aaab710085f"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_GRAPHEXEC_CREATED" ref="gg65ad906ecc3006f2b49f63d8b09f38fd6c1bfed487cc476bba510aaab710085f" args="" -->SANITIZER_CBID_GRAPHS_GRAPHEXEC_CREATED</em>&nbsp;</td><td>
A new graphexec is created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fd74ef5c3024f3805feb6477e80a13058d"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_GRAPHEXEC_DESTROYING" ref="gg65ad906ecc3006f2b49f63d8b09f38fd74ef5c3024f3805feb6477e80a13058d" args="" -->SANITIZER_CBID_GRAPHS_GRAPHEXEC_DESTROYING</em>&nbsp;</td><td>
A graphexec is being destroyed </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fd9aa8253c5fe915071883bfbf16045a68"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_NODE_LAUNCH_BEGIN" ref="gg65ad906ecc3006f2b49f63d8b09f38fd9aa8253c5fe915071883bfbf16045a68" args="" -->SANITIZER_CBID_GRAPHS_NODE_LAUNCH_BEGIN</em>&nbsp;</td><td>
A node launch was initiated. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fd1a2e9175953b497467eb788f362fd38a"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_NODE_LAUNCH_END" ref="gg65ad906ecc3006f2b49f63d8b09f38fd1a2e9175953b497467eb788f362fd38a" args="" -->SANITIZER_CBID_GRAPHS_NODE_LAUNCH_END</em>&nbsp;</td><td>
A node launch is complete. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fd7c2be05510e7bc0f8d88746a21b259ec"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_LAUNCH_BEGIN" ref="gg65ad906ecc3006f2b49f63d8b09f38fd7c2be05510e7bc0f8d88746a21b259ec" args="" -->SANITIZER_CBID_GRAPHS_LAUNCH_BEGIN</em>&nbsp;</td><td>
A graph launch was initiated. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg65ad906ecc3006f2b49f63d8b09f38fdeb48e77124225829861586f0daaa0bfb"></a><!-- doxytag: member="SANITIZER_CBID_GRAPHS_LAUNCH_END" ref="gg65ad906ecc3006f2b49f63d8b09f38fdeb48e77124225829861586f0daaa0bfb" args="" -->SANITIZER_CBID_GRAPHS_LAUNCH_END</em>&nbsp;</td><td>
A graph launch is complete. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g023cd56b97f1231cb0c4d11b4f8f95d4"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdLaunch" ref="g023cd56b97f1231cb0c4d11b4f8f95d4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g023cd56b97f1231cb0c4d11b4f8f95d4">Sanitizer_CallbackIdLaunch</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_LAUNCH. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg023cd56b97f1231cb0c4d11b4f8f95d4a4c3af939550c790d74ff11a1dfa27cb"></a><!-- doxytag: member="SANITIZER_CBID_LAUNCH_INVALID" ref="gg023cd56b97f1231cb0c4d11b4f8f95d4a4c3af939550c790d74ff11a1dfa27cb" args="" -->SANITIZER_CBID_LAUNCH_INVALID</em>&nbsp;</td><td>
Invalid launch callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg023cd56b97f1231cb0c4d11b4f8f95d4ed7d9f21349e1e6a0042178c717d9b8b"></a><!-- doxytag: member="SANITIZER_CBID_LAUNCH_BEGIN" ref="gg023cd56b97f1231cb0c4d11b4f8f95d4ed7d9f21349e1e6a0042178c717d9b8b" args="" -->SANITIZER_CBID_LAUNCH_BEGIN</em>&nbsp;</td><td>
A grid launch was initiated. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg023cd56b97f1231cb0c4d11b4f8f95d420b20cd0488dc397426be7b416b97310"></a><!-- doxytag: member="SANITIZER_CBID_LAUNCH_AFTER_SYSCALL_SETUP" ref="gg023cd56b97f1231cb0c4d11b4f8f95d420b20cd0488dc397426be7b416b97310" args="" -->SANITIZER_CBID_LAUNCH_AFTER_SYSCALL_SETUP</em>&nbsp;</td><td>
A grid launch has completed syscalls setup. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg023cd56b97f1231cb0c4d11b4f8f95d4e99da23dfb7ec9f9ae126088f0bf33dd"></a><!-- doxytag: member="SANITIZER_CBID_LAUNCH_END" ref="gg023cd56b97f1231cb0c4d11b4f8f95d4e99da23dfb7ec9f9ae126088f0bf33dd" args="" -->SANITIZER_CBID_LAUNCH_END</em>&nbsp;</td><td>
The grid launch is complete. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g9047997c07890c2fd3136ca731228bd5"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdMemcpy" ref="g9047997c07890c2fd3136ca731228bd5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9047997c07890c2fd3136ca731228bd5">Sanitizer_CallbackIdMemcpy</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_MEMCPY. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg9047997c07890c2fd3136ca731228bd53ead2079459d28219a7a6f3aaa122c2b"></a><!-- doxytag: member="SANITIZER_CBID_MEMCPY_INVALID" ref="gg9047997c07890c2fd3136ca731228bd53ead2079459d28219a7a6f3aaa122c2b" args="" -->SANITIZER_CBID_MEMCPY_INVALID</em>&nbsp;</td><td>
Invalid memcpy callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9047997c07890c2fd3136ca731228bd589c7fd9883238502bb28e26e76dda383"></a><!-- doxytag: member="SANITIZER_CBID_MEMCPY_STARTING" ref="gg9047997c07890c2fd3136ca731228bd589c7fd9883238502bb28e26e76dda383" args="" -->SANITIZER_CBID_MEMCPY_STARTING</em>&nbsp;</td><td>
A memcpy operation was initiated. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g70a5aabab8a7528c5213ee5f535b466c"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdMemset" ref="g70a5aabab8a7528c5213ee5f535b466c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g70a5aabab8a7528c5213ee5f535b466c">Sanitizer_CallbackIdMemset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_MEMSET. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg70a5aabab8a7528c5213ee5f535b466c2ed084a804472ab66f0d898049b601fb"></a><!-- doxytag: member="SANITIZER_CBID_MEMSET_INVALID" ref="gg70a5aabab8a7528c5213ee5f535b466c2ed084a804472ab66f0d898049b601fb" args="" -->SANITIZER_CBID_MEMSET_INVALID</em>&nbsp;</td><td>
Invalid memset callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg70a5aabab8a7528c5213ee5f535b466c627ffcfed33317f86d4adc88d0c8eb5a"></a><!-- doxytag: member="SANITIZER_CBID_MEMSET_STARTING" ref="gg70a5aabab8a7528c5213ee5f535b466c627ffcfed33317f86d4adc88d0c8eb5a" args="" -->SANITIZER_CBID_MEMSET_STARTING</em>&nbsp;</td><td>
A memset operation was initiated. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd447a2768c522f1d2b2a086a47705a49"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdResource" ref="gd447a2768c522f1d2b2a086a47705a49" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#gd447a2768c522f1d2b2a086a47705a49">Sanitizer_CallbackIdResource</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_RESOURCE. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49d43439a668fcd368dd306a6fed0bf729"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_INVALID" ref="ggd447a2768c522f1d2b2a086a47705a49d43439a668fcd368dd306a6fed0bf729" args="" -->SANITIZER_CBID_RESOURCE_INVALID</em>&nbsp;</td><td>
Invalid resource callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a491ee5702cda826f9ee18e8992a955d57c"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_INIT_FINISHED" ref="ggd447a2768c522f1d2b2a086a47705a491ee5702cda826f9ee18e8992a955d57c" args="" -->SANITIZER_CBID_RESOURCE_INIT_FINISHED</em>&nbsp;</td><td>
Driver initialization is finished. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49eedce847aa53b7df5cbad11f822db088"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_CONTEXT_CREATION_STARTING" ref="ggd447a2768c522f1d2b2a086a47705a49eedce847aa53b7df5cbad11f822db088" args="" -->SANITIZER_CBID_RESOURCE_CONTEXT_CREATION_STARTING</em>&nbsp;</td><td>
A new context is about to be created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49e757f7d5ecc22ae45bc8990b02dca0f9"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_CONTEXT_CREATION_FINISHED" ref="ggd447a2768c522f1d2b2a086a47705a49e757f7d5ecc22ae45bc8990b02dca0f9" args="" -->SANITIZER_CBID_RESOURCE_CONTEXT_CREATION_FINISHED</em>&nbsp;</td><td>
A new context was created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a4913c6e827f9f82f075d46a7215b87aba4"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_CONTEXT_DESTROY_STARTING" ref="ggd447a2768c522f1d2b2a086a47705a4913c6e827f9f82f075d46a7215b87aba4" args="" -->SANITIZER_CBID_RESOURCE_CONTEXT_DESTROY_STARTING</em>&nbsp;</td><td>
A context is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49df1aecd4fe1514ce55ce7888cddae732"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_CONTEXT_DESTROY_FINISHED" ref="ggd447a2768c522f1d2b2a086a47705a49df1aecd4fe1514ce55ce7888cddae732" args="" -->SANITIZER_CBID_RESOURCE_CONTEXT_DESTROY_FINISHED</em>&nbsp;</td><td>
A context was destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a4936eece499c3921982d4c005c99eb4994"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_STREAM_CREATED" ref="ggd447a2768c522f1d2b2a086a47705a4936eece499c3921982d4c005c99eb4994" args="" -->SANITIZER_CBID_RESOURCE_STREAM_CREATED</em>&nbsp;</td><td>
A new stream was created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a4968c8eb02e7f7b4823233eb77e7ce7494"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_STREAM_DESTROY_STARTING" ref="ggd447a2768c522f1d2b2a086a47705a4968c8eb02e7f7b4823233eb77e7ce7494" args="" -->SANITIZER_CBID_RESOURCE_STREAM_DESTROY_STARTING</em>&nbsp;</td><td>
A stream is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49c8951f93a907cc1e70f52d3fd6aba083"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_STREAM_DESTROY_FINISHED" ref="ggd447a2768c522f1d2b2a086a47705a49c8951f93a907cc1e70f52d3fd6aba083" args="" -->SANITIZER_CBID_RESOURCE_STREAM_DESTROY_FINISHED</em>&nbsp;</td><td>
A stream was destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49cbed32968d4482003381ca8c50abe464"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MODULE_LOADED" ref="ggd447a2768c522f1d2b2a086a47705a49cbed32968d4482003381ca8c50abe464" args="" -->SANITIZER_CBID_RESOURCE_MODULE_LOADED</em>&nbsp;</td><td>
A module was loaded. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49d2e3ceaa1adb9c6099c1d07a096eaf8d"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MODULE_UNLOAD_STARTING" ref="ggd447a2768c522f1d2b2a086a47705a49d2e3ceaa1adb9c6099c1d07a096eaf8d" args="" -->SANITIZER_CBID_RESOURCE_MODULE_UNLOAD_STARTING</em>&nbsp;</td><td>
A module is about to be unloaded. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a4962f2c8af52f8285f51684fa6ce776162"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_DEVICE_MEMORY_ALLOC" ref="ggd447a2768c522f1d2b2a086a47705a4962f2c8af52f8285f51684fa6ce776162" args="" -->SANITIZER_CBID_RESOURCE_DEVICE_MEMORY_ALLOC</em>&nbsp;</td><td>
Device memory was allocated. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a495638b6ba05cc418a127a123a743d9fda"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_DEVICE_MEMORY_FREE" ref="ggd447a2768c522f1d2b2a086a47705a495638b6ba05cc418a127a123a743d9fda" args="" -->SANITIZER_CBID_RESOURCE_DEVICE_MEMORY_FREE</em>&nbsp;</td><td>
Device memory was freed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49ec3615d6adaf20d8b89637fd64bc0974"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_HOST_MEMORY_ALLOC" ref="ggd447a2768c522f1d2b2a086a47705a49ec3615d6adaf20d8b89637fd64bc0974" args="" -->SANITIZER_CBID_RESOURCE_HOST_MEMORY_ALLOC</em>&nbsp;</td><td>
Pinned host memory was allocated. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49aabeddb71b2fa843418a90b8760ea9d7"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_HOST_MEMORY_FREE" ref="ggd447a2768c522f1d2b2a086a47705a49aabeddb71b2fa843418a90b8760ea9d7" args="" -->SANITIZER_CBID_RESOURCE_HOST_MEMORY_FREE</em>&nbsp;</td><td>
Pinned host memory was freed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49fb5edae93d426e280a547e18490863ab"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MEMORY_ALLOC_ASYNC" ref="ggd447a2768c522f1d2b2a086a47705a49fb5edae93d426e280a547e18490863ab" args="" -->SANITIZER_CBID_RESOURCE_MEMORY_ALLOC_ASYNC</em>&nbsp;</td><td>
Memory was allocated asynchronously. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49dc8da45895d34fc420bc5604e80edc49"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MEMORY_FREE_ASYNC" ref="ggd447a2768c522f1d2b2a086a47705a49dc8da45895d34fc420bc5604e80edc49" args="" -->SANITIZER_CBID_RESOURCE_MEMORY_FREE_ASYNC</em>&nbsp;</td><td>
Memory was freed asynchronously. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49ad42f1afe7973734d5941952c40c4304"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MEMORY_FREE_ASYNC_DONE" ref="ggd447a2768c522f1d2b2a086a47705a49ad42f1afe7973734d5941952c40c4304" args="" -->SANITIZER_CBID_RESOURCE_MEMORY_FREE_ASYNC_DONE</em>&nbsp;</td><td>
Memory freed asynchronously was released, only happens if a regular allocation (cudaMalloc) is free'd asynchronously (cudaFreeAsync).<p>
See CUDA runtime documentation for cudaFreeAsync </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a4940745c67c29fd59a8842a9a7bb6a807b"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MEMPOOL_CREATED" ref="ggd447a2768c522f1d2b2a086a47705a4940745c67c29fd59a8842a9a7bb6a807b" args="" -->SANITIZER_CBID_RESOURCE_MEMPOOL_CREATED</em>&nbsp;</td><td>
A new mempool was created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49b42ebe6b4e398725675c96d9aff15725"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MEMPOOL_DESTROYING" ref="ggd447a2768c522f1d2b2a086a47705a49b42ebe6b4e398725675c96d9aff15725" args="" -->SANITIZER_CBID_RESOURCE_MEMPOOL_DESTROYING</em>&nbsp;</td><td>
A mempool is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a497f8c0e11f215840906b2652e64e4d502"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_ENABLED" ref="ggd447a2768c522f1d2b2a086a47705a497f8c0e11f215840906b2652e64e4d502" args="" -->SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_ENABLED</em>&nbsp;</td><td>
A mempool is now accessible from a peer device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49f39a1fd0fde3d834f3af301ee9c0d1fd"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_DISABLING" ref="ggd447a2768c522f1d2b2a086a47705a49f39a1fd0fde3d834f3af301ee9c0d1fd" args="" -->SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_DISABLING</em>&nbsp;</td><td>
A mempool is no longer accessible from a peer device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a493240b79721f1a81d5de1b2c95ba6f98e"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_ARRAY_CREATED" ref="ggd447a2768c522f1d2b2a086a47705a493240b79721f1a81d5de1b2c95ba6f98e" args="" -->SANITIZER_CBID_RESOURCE_ARRAY_CREATED</em>&nbsp;</td><td>
A CUDA array was created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49c7167a9a77562b81eeb5ca89e69c0a48"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_ARRAY_DESTROYED" ref="ggd447a2768c522f1d2b2a086a47705a49c7167a9a77562b81eeb5ca89e69c0a48" args="" -->SANITIZER_CBID_RESOURCE_ARRAY_DESTROYED</em>&nbsp;</td><td>
A CUDA array was destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd447a2768c522f1d2b2a086a47705a49697a6e4db682a1f34097ae42e461d21c"></a><!-- doxytag: member="SANITIZER_CBID_RESOURCE_FUNCTIONS_LAZY_LOADED" ref="ggd447a2768c522f1d2b2a086a47705a49697a6e4db682a1f34097ae42e461d21c" args="" -->SANITIZER_CBID_RESOURCE_FUNCTIONS_LAZY_LOADED</em>&nbsp;</td><td>
CUDA functions were loaded lazily and are fully loaded. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd0dca53274794d63531e7993fb6248a0"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_CallbackIdUvm" ref="gd0dca53274794d63531e7993fb6248a0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#gd0dca53274794d63531e7993fb6248a0">Sanitizer_CallbackIdUvm</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain SANITIZER_CB_DOMAIN_UVM. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd0dca53274794d63531e7993fb6248a0707ca0649121416d591274ad94cf38ae"></a><!-- doxytag: member="SANITIZER_CBID_UVM_INVALID" ref="ggd0dca53274794d63531e7993fb6248a0707ca0649121416d591274ad94cf38ae" args="" -->SANITIZER_CBID_UVM_INVALID</em>&nbsp;</td><td>
Invalid managed memory callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd0dca53274794d63531e7993fb6248a092eb9620669167609f2623faedc47c2b"></a><!-- doxytag: member="SANITIZER_CBID_UVM_ATTACH_MEM" ref="ggd0dca53274794d63531e7993fb6248a092eb9620669167609f2623faedc47c2b" args="" -->SANITIZER_CBID_UVM_ATTACH_MEM</em>&nbsp;</td><td>
Modify the stream association of an allocation (see cudaStreamAttachMemAsync) </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6dd430318fea9a813020c974d07da85e"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_MemcpyDirection" ref="g6dd430318fea9a813020c974d07da85e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6dd430318fea9a813020c974d07da85e">Sanitizer_MemcpyDirection</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Indicates the direction of a memcpy, passed inside <code>Sanitizer_Memcpydata</code>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg6dd430318fea9a813020c974d07da85e60dffdfafa7bcdb76a8adae0f7e4fb65"></a><!-- doxytag: member="SANITIZER_MEMCPY_DIRECTION_UNKNOWN" ref="gg6dd430318fea9a813020c974d07da85e60dffdfafa7bcdb76a8adae0f7e4fb65" args="" -->SANITIZER_MEMCPY_DIRECTION_UNKNOWN</em>&nbsp;</td><td>
Unknown memcpy direction </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6dd430318fea9a813020c974d07da85e5be6c4f49002493f69fba85794c571de"></a><!-- doxytag: member="SANITIZER_MEMCPY_DIRECTION_HOST_TO_HOST" ref="gg6dd430318fea9a813020c974d07da85e5be6c4f49002493f69fba85794c571de" args="" -->SANITIZER_MEMCPY_DIRECTION_HOST_TO_HOST</em>&nbsp;</td><td>
Memcpy from host to host. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6dd430318fea9a813020c974d07da85e4bb24eea51783a23a040b5d840921724"></a><!-- doxytag: member="SANITIZER_MEMCPY_DIRECTION_HOST_TO_DEVICE" ref="gg6dd430318fea9a813020c974d07da85e4bb24eea51783a23a040b5d840921724" args="" -->SANITIZER_MEMCPY_DIRECTION_HOST_TO_DEVICE</em>&nbsp;</td><td>
Memcpy from host to device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6dd430318fea9a813020c974d07da85ed745d5602c5688537b69de243c957330"></a><!-- doxytag: member="SANITIZER_MEMCPY_DIRECTION_DEVICE_TO_HOST" ref="gg6dd430318fea9a813020c974d07da85ed745d5602c5688537b69de243c957330" args="" -->SANITIZER_MEMCPY_DIRECTION_DEVICE_TO_HOST</em>&nbsp;</td><td>
Memcpy from device to host. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6dd430318fea9a813020c974d07da85eba06c89f6f834bfc27291fe66a52bdb1"></a><!-- doxytag: member="SANITIZER_MEMCPY_DIRECTION_DEVICE_TO_DEVICE" ref="gg6dd430318fea9a813020c974d07da85eba06c89f6f834bfc27291fe66a52bdb1" args="" -->SANITIZER_MEMCPY_DIRECTION_DEVICE_TO_DEVICE</em>&nbsp;</td><td>
Memcpy from device to device. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g62d02f306fdb955ab1f5add810c45564"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_MemoryVisibility" ref="g62d02f306fdb955ab1f5add810c45564" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#g62d02f306fdb955ab1f5add810c45564">Sanitizer_MemoryVisibility</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Specifies the visibility of an allocation. This is typically GLOBAL on allocations made via cudaMalloc, cudaHostAlloc and similar APIs. This can be GLOBAL or HOST for cudaMallocManaged allocations depending on the flags parameter. This can be changed after allocation time using cudaMemAttachSingle API (see SANITIZER_CBID_UVM_ATTACH_MEM for the corresponding callback). <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg62d02f306fdb955ab1f5add810c45564ec98e94be6b92519a97382661efc7ec3"></a><!-- doxytag: member="SANITIZER_MEMORY_VISIBILITY_INVALID" ref="gg62d02f306fdb955ab1f5add810c45564ec98e94be6b92519a97382661efc7ec3" args="" -->SANITIZER_MEMORY_VISIBILITY_INVALID</em>&nbsp;</td><td>
Invalid memory visibility </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg62d02f306fdb955ab1f5add810c455647054a49c377ec2be82a2a0649e470cd7"></a><!-- doxytag: member="SANITIZER_MEMORY_VISIBILITY_GLOBAL" ref="gg62d02f306fdb955ab1f5add810c455647054a49c377ec2be82a2a0649e470cd7" args="" -->SANITIZER_MEMORY_VISIBILITY_GLOBAL</em>&nbsp;</td><td>
Memory can be accessed by any stream on any device (see cudaMemAttachGlobal) </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg62d02f306fdb955ab1f5add810c455640ce73516f2ab9ecebda2870c42403c87"></a><!-- doxytag: member="SANITIZER_MEMORY_VISIBILITY_HOST" ref="gg62d02f306fdb955ab1f5add810c455640ce73516f2ab9ecebda2870c42403c87" args="" -->SANITIZER_MEMORY_VISIBILITY_HOST</em>&nbsp;</td><td>
Memory cannot be accessed by any stream on any device (see cudaMemAttachHost) </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg62d02f306fdb955ab1f5add810c455643c1f84ccf3f2c63b65630004aafbaee9"></a><!-- doxytag: member="SANITIZER_MEMORY_VISIBILITY_STREAM" ref="gg62d02f306fdb955ab1f5add810c455643c1f84ccf3f2c63b65630004aafbaee9" args="" -->SANITIZER_MEMORY_VISIBILITY_STREAM</em>&nbsp;</td><td>
Memory can only be accessed by a single stream on the associated device (see cudaMemAttachSingle) </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd29155bb34524587a5a42d41be1d3fb2"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_ResourceMemoryFlags" ref="gd29155bb34524587a5a42d41be1d3fb2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#gd29155bb34524587a5a42d41be1d3fb2">Sanitizer_ResourceMemoryFlags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flags describing a memory allocation. These values are to be used in order to interpret the value of <a class="el" href="group__SANITIZER__CALLBACK__API.html#g7bee77cc4075471b0397cf557c843c4a">Sanitizer_ResourceMemoryData::flags</a> <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb2fe628d1619f6084b2ff9d546d62754d5"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_NONE" ref="ggd29155bb34524587a5a42d41be1d3fb2fe628d1619f6084b2ff9d546d62754d5" args="" -->SANITIZER_MEMORY_FLAG_NONE</em>&nbsp;</td><td>
Empty flag. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb2de496bf8bb3e2f56a4e5b4f13ccf1958"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_MODULE" ref="ggd29155bb34524587a5a42d41be1d3fb2de496bf8bb3e2f56a4e5b4f13ccf1958" args="" -->SANITIZER_MEMORY_FLAG_MODULE</em>&nbsp;</td><td>
Specifies that the allocation is static scoped to a module. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb2b4f87d77b74a0fcdb803e884896f4e1d"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_MANAGED" ref="ggd29155bb34524587a5a42d41be1d3fb2b4f87d77b74a0fcdb803e884896f4e1d" args="" -->SANITIZER_MEMORY_FLAG_MANAGED</em>&nbsp;</td><td>
Specifies that the allocation is managed memory. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb28e50f29060b27b767df0a344dd68f5d0"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_HOST_MAPPED" ref="ggd29155bb34524587a5a42d41be1d3fb28e50f29060b27b767df0a344dd68f5d0" args="" -->SANITIZER_MEMORY_FLAG_HOST_MAPPED</em>&nbsp;</td><td>
Species that the allocation accessible from the host. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb2edd0c300182b331d0efa950226e6eb2d"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_HOST_PINNED" ref="ggd29155bb34524587a5a42d41be1d3fb2edd0c300182b331d0efa950226e6eb2d" args="" -->SANITIZER_MEMORY_FLAG_HOST_PINNED</em>&nbsp;</td><td>
Specifies that the allocation is pinned on the host. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb2e48682eff0dbf2410d29e85dcba2363c"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_PEER" ref="ggd29155bb34524587a5a42d41be1d3fb2e48682eff0dbf2410d29e85dcba2363c" args="" -->SANITIZER_MEMORY_FLAG_PEER</em>&nbsp;</td><td>
Specifies that the allocation is located on a peer GPU. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb25f5c733652a1f6523aba62c52ec17cf8"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_PEER_ATOMIC" ref="ggd29155bb34524587a5a42d41be1d3fb25f5c733652a1f6523aba62c52ec17cf8" args="" -->SANITIZER_MEMORY_FLAG_PEER_ATOMIC</em>&nbsp;</td><td>
Specifies that the allocation is located on a peer GPU supporting native atomics. This implies that SANITIZER_MEMORY_FLAG_PEER is set as well. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb2d658f4fb8f76628770b7600cb445a42b"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_CG_RUNTIME" ref="ggd29155bb34524587a5a42d41be1d3fb2d658f4fb8f76628770b7600cb445a42b" args="" -->SANITIZER_MEMORY_FLAG_CG_RUNTIME</em>&nbsp;</td><td>
Specifies that the allocation is used by the Cooperative Groups runtime functions. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd29155bb34524587a5a42d41be1d3fb2fb39111f58e43b9e00035279a321396f"></a><!-- doxytag: member="SANITIZER_MEMORY_FLAG_CNP" ref="ggd29155bb34524587a5a42d41be1d3fb2fb39111f58e43b9e00035279a321396f" args="" -->SANITIZER_MEMORY_FLAG_CNP</em>&nbsp;</td><td>
Specifies that this is an allocation used for CUDA Dynamic Parallelism purposes. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc172db1fa20fecca6ac23c2954ae15a1"></a><!-- doxytag: member="sanitizer_callbacks.h::Sanitizer_ResourceMemoryPermissions" ref="gc172db1fa20fecca6ac23c2954ae15a1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__CALLBACK__API.html#gc172db1fa20fecca6ac23c2954ae15a1">Sanitizer_ResourceMemoryPermissions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Permissions for a memory allocation. These values are to be used in order to interpret the value of <a class="el" href="group__SANITIZER__CALLBACK__API.html#gfa68ea5df21d945ae35162a7573c4d61">Sanitizer_ResourceMemoryData::permissions</a> <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggc172db1fa20fecca6ac23c2954ae15a1d007a64340a1fa1c858e1735e2b732e9"></a><!-- doxytag: member="SANITIZER_MEMORY_PERMISSION_NONE" ref="ggc172db1fa20fecca6ac23c2954ae15a1d007a64340a1fa1c858e1735e2b732e9" args="" -->SANITIZER_MEMORY_PERMISSION_NONE</em>&nbsp;</td><td>
No permissions. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc172db1fa20fecca6ac23c2954ae15a1e75db3c063e08e0e62554d4ea3f6f555"></a><!-- doxytag: member="SANITIZER_MEMORY_PERMISSION_READ" ref="ggc172db1fa20fecca6ac23c2954ae15a1e75db3c063e08e0e62554d4ea3f6f555" args="" -->SANITIZER_MEMORY_PERMISSION_READ</em>&nbsp;</td><td>
Specifies that the allocation is readable. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc172db1fa20fecca6ac23c2954ae15a15d9b919025a4c997d342a44e6545e985"></a><!-- doxytag: member="SANITIZER_MEMORY_PERMISSION_WRITE" ref="ggc172db1fa20fecca6ac23c2954ae15a15d9b919025a4c997d342a44e6545e985" args="" -->SANITIZER_MEMORY_PERMISSION_WRITE</em>&nbsp;</td><td>
Specifies that the allocation is writable. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc172db1fa20fecca6ac23c2954ae15a17104209c55e959075959198586bf8ec7"></a><!-- doxytag: member="SANITIZER_MEMORY_PERMISSION_ATOMIC" ref="ggc172db1fa20fecca6ac23c2954ae15a17104209c55e959075959198586bf8ec7" args="" -->SANITIZER_MEMORY_PERMISSION_ATOMIC</em>&nbsp;</td><td>
Specifies that the allocation is readable/writable with atomic operations. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc172db1fa20fecca6ac23c2954ae15a109f85c91d0f602a335c9f8511bbe1069"></a><!-- doxytag: member="SANITIZER_MEMORY_PERMISSION_ALL" ref="ggc172db1fa20fecca6ac23c2954ae15a109f85c91d0f602a335c9f8511bbe1069" args="" -->SANITIZER_MEMORY_PERMISSION_ALL</em>&nbsp;</td><td>
Specifies that the allocation has all permissions. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g333a1254396a7a8ba5f8921d1f65642a"></a><!-- doxytag: member="sanitizer_callbacks.h::sanitizerEnableAllDomains" ref="g333a1254396a7a8ba5f8921d1f65642a" args="(uint32_t enable, Sanitizer_SubscriberHandle subscriber)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerEnableAllDomains           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable or disable all callbacks in all domains.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to sanitizerGetCallbackState, sanitizerEnableCallback, sanitizerEnableDomain, and sanitizerEnableAllDomains. For example, if sanitizerGetCallbackState(sub, d, *) and sanitizerEnableAllDomains(sub) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>New enable state for all callbacks in all domains. Zero disables all callbacks, non-zero enables all callbacks. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>- Handle of the initialized subscriber</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> is invalid </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g5463cc746520167ff93e22636549b66b"></a><!-- doxytag: member="sanitizer_callbacks.h::sanitizerEnableCallback" ref="g5463cc746520167ff93e22636549b66b" args="(uint32_t enable, Sanitizer_SubscriberHandle subscriber, Sanitizer_CallbackDomain domain, Sanitizer_CallbackId cbid)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerEnableCallback           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a>&nbsp;</td>
          <td class="paramname"> <em>domain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6e4655fe27c8ee6f85661350bfbc7bd7">Sanitizer_CallbackId</a>&nbsp;</td>
          <td class="paramname"> <em>cbid</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable or disable callbacks for a subscriber for a specific domain and callback ID.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to sanitizerGetCallbackState, sanitizerEnableCallback, sanitizerEnableDomain, and sanitizerEnableAllDomains. For example, if sanitizerGetCallbackState(sub, d, c) and sanitizerEnableCallback(sub, d, c) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>New enable state for the callback. Zero disables the callback, non-zero enables the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>- Handle of the initialized subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cbid</em>&nbsp;</td><td>The ID of the callback</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code>, <code>domain</code> or <code>cbid</code> is invalid </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gffda58e1b795d575a0439557ff9f6fa5"></a><!-- doxytag: member="sanitizer_callbacks.h::sanitizerEnableDomain" ref="gffda58e1b795d575a0439557ff9f6fa5" args="(uint32_t enable, Sanitizer_SubscriberHandle subscriber, Sanitizer_CallbackDomain domain)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerEnableDomain           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a>&nbsp;</td>
          <td class="paramname"> <em>domain</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable or disable all callbacks for a specific domain.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to sanitizerGetCallbackState, sanitizerEnableCallback, sanitizerEnableDomain, and sanitizerEnableAllDomains. For example, if sanitizerGetCallbackEnabled(sub, d, *) and sanitizerEnableDomain(sub, d) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>New enable state for all callbacks in the domain. Zero disables all callbacks, non-zero enables all callbacks </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>- Handle of the initialized subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> or <code>domain</code> is invalid </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g631b78eb2d8bef7ac892461cda0eff67"></a><!-- doxytag: member="sanitizer_callbacks.h::sanitizerGetCallbackState" ref="g631b78eb2d8bef7ac892461cda0eff67" args="(uint32_t *enable, Sanitizer_SubscriberHandle subscriber, Sanitizer_CallbackDomain domain, Sanitizer_CallbackId cbid)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetCallbackState           </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g2184025e5309c5989412b9f64beac46b">Sanitizer_CallbackDomain</a>&nbsp;</td>
          <td class="paramname"> <em>domain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6e4655fe27c8ee6f85661350bfbc7bd7">Sanitizer_CallbackId</a>&nbsp;</td>
          <td class="paramname"> <em>cbid</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns non-zero in <code>*enable</code> if the callback for a domain and callback ID is enabled, and zero if not enabled.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to sanitizerGetCallbackState, sanitizerEnableCallback, sanitizerEnableDomain, and sanitizerEnableAllDomains. For example, if sanitizerGetCallbackState(sub, d, c) and sanitizerEnableCallback(sub, d, c) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>Returns non-zero if callback enabled, zero if not enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>Handle to the initialized subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cbid</em>&nbsp;</td><td>The ID of the callback</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>enabled</code> is NULL, or if <code>subscriber</code>, <code>domain</code> or <code>cbid</code> is invalid. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2740c794198ce1cae40f02d5bad3ab8b"></a><!-- doxytag: member="sanitizer_callbacks.h::sanitizerSubscribe" ref="g2740c794198ce1cae40f02d5bad3ab8b" args="(Sanitizer_SubscriberHandle *subscriber, Sanitizer_CallbackFunc callback, void *userdata)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerSubscribe           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a> *&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gf031ccd4dcc013ab017d9ee1032858c4">Sanitizer_CallbackFunc</a>&nbsp;</td>
          <td class="paramname"> <em>callback</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>userdata</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Initialize a callback subscriber with a callback function and (optionally) a pointer to user data. The returned subscriber handle can be used to enable and disable the callback for specific domains and callback IDs. <dl class="note" compact><dt><b>Note:</b></dt><dd>Only one subscriber can be registered at a time. <p>
This function does not enable any callbacks. <p>
<b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>Returns handle to initialize subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>callback</em>&nbsp;</td><td>The callback function </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>userdata</em>&nbsp;</td><td>A pointer to user data. This data will be passed to the callback function via the <code>userdata</code> parameter</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_MAX_LIMIT_RACHED</em>&nbsp;</td><td>if there is already a sanitizer subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g143e5fe94ccaab6559c7af04df01a0e8"></a><!-- doxytag: member="sanitizer_callbacks.h::sanitizerUnsubscribe" ref="g143e5fe94ccaab6559c7af04df01a0e8" args="(Sanitizer_SubscriberHandle subscriber)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerUnsubscribe           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g11604a410fe99da425a0bd40b17c7464">Sanitizer_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Removes a callback subscriber so that no future callback will be issued to that subscriber. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>Handle to the initialized subscriber</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize the sanitizer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> is NULL or not initialized </td></tr>
  </table>
</dl>

</div>
</div><p>
<hr><h2>Variable Documentation</h2>
<a class="anchor" name="g09a372a0998195ee1c8c53c9d2d85458"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::@1" ref="g09a372a0998195ee1c8c53c9d2d85458" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... } <code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Data for this node launch. 
</div>
</div><p>
<a class="anchor" name="g1e8e80e5a6533ffea2ca5a435e0827fe"></a><!-- doxytag: member="Sanitizer_UvmData::address" ref="g1e8e80e5a6533ffea2ca5a435e0827fe" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g1e8e80e5a6533ffea2ca5a435e0827fe">Sanitizer_UvmData::address</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The address of the allocation. 
</div>
</div><p>
<a class="anchor" name="gb535f9480452944a891c80ba054268ba"></a><!-- doxytag: member="Sanitizer_BatchMemopData::address" ref="gb535f9480452944a891c80ba054268ba" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gb535f9480452944a891c80ba054268ba">Sanitizer_BatchMemopData::address</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The address to be written. 
</div>
</div><p>
<a class="anchor" name="g86864b17ab8ef4bcbdf9cf27ea86b6a9"></a><!-- doxytag: member="Sanitizer_MemsetData::address" ref="g86864b17ab8ef4bcbdf9cf27ea86b6a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g86864b17ab8ef4bcbdf9cf27ea86b6a9">Sanitizer_MemsetData::address</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The address of the memset start. 
</div>
</div><p>
<a class="anchor" name="g0f74ceb1522b0d27bb1d38ce41f83d73"></a><!-- doxytag: member="Sanitizer_LaunchData::apiContext" ref="g0f74ceb1522b0d27bb1d38ce41f83d73" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="group__SANITIZER__CALLBACK__API.html#g0f74ceb1522b0d27bb1d38ce41f83d73">Sanitizer_LaunchData::apiContext</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Only valid for graph node launches. This is the context of the stream used in the graph launch API call. 
</div>
</div><p>
<a class="anchor" name="g73236f03601aa99a589d4c46a4a8c19e"></a><!-- doxytag: member="Sanitizer_LaunchData::apiStream" ref="g73236f03601aa99a589d4c46a4a8c19e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g73236f03601aa99a589d4c46a4a8c19e">Sanitizer_LaunchData::apiStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Only valid for graph node launches. This is the stream used in the graph launch API call. 
</div>
</div><p>
<a class="anchor" name="gd71d9596f42c5280117c58aec28f7677"></a><!-- doxytag: member="Sanitizer_LaunchData::blockDim_x" ref="gd71d9596f42c5280117c58aec28f7677" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gd71d9596f42c5280117c58aec28f7677">Sanitizer_LaunchData::blockDim_x</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="geaccddc162d325b688fb59813e854df6"></a><!-- doxytag: member="Sanitizer_LaunchData::blockDim_y" ref="geaccddc162d325b688fb59813e854df6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#geaccddc162d325b688fb59813e854df6">Sanitizer_LaunchData::blockDim_y</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="gdea40e9c9cab2e4b98b0e0abaf04ed7a"></a><!-- doxytag: member="Sanitizer_LaunchData::blockDim_z" ref="gdea40e9c9cab2e4b98b0e0abaf04ed7a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gdea40e9c9cab2e4b98b0e0abaf04ed7a">Sanitizer_LaunchData::blockDim_z</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="gc83c9a72971e8b4fcbae6e5299a0bb52"></a><!-- doxytag: member="Sanitizer_LaunchData::clusterDim_x" ref="gc83c9a72971e8b4fcbae6e5299a0bb52" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gc83c9a72971e8b4fcbae6e5299a0bb52">Sanitizer_LaunchData::clusterDim_x</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="gb69bc0f86a204aef81bbb27ab3599121"></a><!-- doxytag: member="Sanitizer_LaunchData::clusterDim_y" ref="gb69bc0f86a204aef81bbb27ab3599121" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gb69bc0f86a204aef81bbb27ab3599121">Sanitizer_LaunchData::clusterDim_y</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="g3d0219ba9537466c00a8d8d6f7fe5232"></a><!-- doxytag: member="Sanitizer_LaunchData::clusterDim_z" ref="g3d0219ba9537466c00a8d8d6f7fe5232" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g3d0219ba9537466c00a8d8d6f7fe5232">Sanitizer_LaunchData::clusterDim_z</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="g6da08037d5ae573a39e4f54183a6db35"></a><!-- doxytag: member="Sanitizer_EventData::context" ref="g6da08037d5ae573a39e4f54183a6db35" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6da08037d5ae573a39e4f54183a6db35">Sanitizer_EventData::context</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For SANITIZER_CBID_EVENTS_CREATED, SANITIZER_CBID_EVENTS_DESTROYED, and SANITIZER_CBID_EVENTS_SYNCHNONIZED, this is the context containing the event. For SANITIZER_CBID_EVENTS_RECORD and SANITIZER_CBID_EVENTS_STREAM_WAIT, this is the context containing the stream being recorded or waiting. 
</div>
</div><p>
<a class="anchor" name="g306fed61e5fc1aefe907f67b6aeb3c7b"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::context" ref="g306fed61e5fc1aefe907f67b6aeb3c7b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="group__SANITIZER__CALLBACK__API.html#g306fed61e5fc1aefe907f67b6aeb3c7b">Sanitizer_ResourceMemoryData::context</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Context containing the allocation being created or destroyed. Can be NULL if the allocation is not attached to a context. 
</div>
</div><p>
<a class="anchor" name="g6952454cc861e5a97c40d9e459a90351"></a><!-- doxytag: member="Sanitizer_CallbackData::context" ref="g6952454cc861e5a97c40d9e459a90351" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6952454cc861e5a97c40d9e459a90351">Sanitizer_CallbackData::context</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Driver context current to the thread, or null if no context is current. This value can change from the entry to exit callback of a runtime API function if the runtime initialized a context. 
</div>
</div><p>
<a class="anchor" name="g4d94a109dba9f60ed5bae597ea93b2b7"></a><!-- doxytag: member="Sanitizer_ResourceModuleData::cubinSize" ref="g4d94a109dba9f60ed5bae597ea93b2b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g4d94a109dba9f60ed5bae597ea93b2b7">Sanitizer_ResourceModuleData::cubinSize</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the cubin. 
</div>
</div><p>
<a class="anchor" name="g54ac0dec13a70e0cb9d1dc3849c40063"></a><!-- doxytag: member="Sanitizer_LaunchData::device" ref="g54ac0dec13a70e0cb9d1dc3849c40063" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="group__SANITIZER__CALLBACK__API.html#g54ac0dec13a70e0cb9d1dc3849c40063">Sanitizer_LaunchData::device</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device where the grid is launched 
</div>
</div><p>
<a class="anchor" name="g9d01c7ed7b6d8b3ea539b530393edb43"></a><!-- doxytag: member="Sanitizer_ResourceMempoolData::device" ref="g9d01c7ed7b6d8b3ea539b530393edb43" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9d01c7ed7b6d8b3ea539b530393edb43">Sanitizer_ResourceMempoolData::device</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device that owns the memory pool. 
</div>
</div><p>
<a class="anchor" name="gc23e893ab78877ab395678c4dc1bddfa"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::device" ref="gc23e893ab78877ab395678c4dc1bddfa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="group__SANITIZER__CALLBACK__API.html#gc23e893ab78877ab395678c4dc1bddfa">Sanitizer_ResourceMemoryData::device</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device where the allocation is being created. Available for all cbid with a driver version of 455 or newer. 
</div>
</div><p>
<a class="anchor" name="gf1b86ab2888f0132953f56bfb63bfb38"></a><!-- doxytag: member="Sanitizer_ResourceContextData::device" ref="gf1b86ab2888f0132953f56bfb63bfb38" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="group__SANITIZER__CALLBACK__API.html#gf1b86ab2888f0132953f56bfb63bfb38">Sanitizer_ResourceContextData::device</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device on which the context is being created or destroyed. This field is only valid for SANITIZER_CBID_RESOURCE_CONTEXT_CREATION_* callbacks 
</div>
</div><p>
<a class="anchor" name="g01d588f3dbd03429170cf7a9c64ecbca"></a><!-- doxytag: member="Sanitizer_MemcpyData::direction" ref="g01d588f3dbd03429170cf7a9c64ecbca" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g6dd430318fea9a813020c974d07da85e">Sanitizer_MemcpyDirection</a> <a class="el" href="group__SANITIZER__CALLBACK__API.html#g01d588f3dbd03429170cf7a9c64ecbca">Sanitizer_MemcpyData::direction</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The direction of the transfer 
</div>
</div><p>
<a class="anchor" name="g176afbebea17970c434b9d5a7c1e2eb9"></a><!-- doxytag: member="Sanitizer_MemcpyData::dstAddress" ref="g176afbebea17970c434b9d5a7c1e2eb9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g176afbebea17970c434b9d5a7c1e2eb9">Sanitizer_MemcpyData::dstAddress</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination allocation address. 
</div>
</div><p>
<a class="anchor" name="ga536604f8271c4d7d3c77ced6bb87899"></a><!-- doxytag: member="Sanitizer_MemcpyData::dstContext" ref="ga536604f8271c4d7d3c77ced6bb87899" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="group__SANITIZER__CALLBACK__API.html#ga536604f8271c4d7d3c77ced6bb87899">Sanitizer_MemcpyData::dstContext</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The context where the destination allocation is located 
</div>
</div><p>
<a class="anchor" name="g9a4d69875b446d382590721f3e352ab1"></a><!-- doxytag: member="Sanitizer_MemcpyData::dstPitch" ref="g9a4d69875b446d382590721f3e352ab1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9a4d69875b446d382590721f3e352ab1">Sanitizer_MemcpyData::dstPitch</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination allocation pitch. 
</div>
</div><p>
<a class="anchor" name="gf11d697d3d994de8b25059b1992b3135"></a><!-- doxytag: member="Sanitizer_MemcpyData::dstStream" ref="gf11d697d3d994de8b25059b1992b3135" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#gf11d697d3d994de8b25059b1992b3135">Sanitizer_MemcpyData::dstStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream where the memcpy is executed on the destination context 
</div>
</div><p>
<a class="anchor" name="g7bee77cc4075471b0397cf557c843c4a"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::flags" ref="g7bee77cc4075471b0397cf557c843c4a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g7bee77cc4075471b0397cf557c843c4a">Sanitizer_ResourceMemoryData::flags</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Allocation details: use Sanitizer_ResourceMemoryFlags to interpret this field. 
</div>
</div><p>
<a class="anchor" name="g6ae473df62bfb4cac682ebedaf8627fd"></a><!-- doxytag: member="Sanitizer_LaunchData::function" ref="g6ae473df62bfb4cac682ebedaf8627fd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUfunction <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6ae473df62bfb4cac682ebedaf8627fd">Sanitizer_LaunchData::function</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The function of the grid launch. 
</div>
</div><p>
<a class="anchor" name="gf217ff198ada2e206aa01e3e2f4aaef8"></a><!-- doxytag: member="Sanitizer_LaunchData::functionName" ref="gf217ff198ada2e206aa01e3e2f4aaef8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="group__SANITIZER__CALLBACK__API.html#gf217ff198ada2e206aa01e3e2f4aaef8">Sanitizer_LaunchData::functionName</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the launched function. 
</div>
</div><p>
<a class="anchor" name="gcffbfadfe4c7af894d8e39407595efd2"></a><!-- doxytag: member="Sanitizer_CallbackData::functionName" ref="gcffbfadfe4c7af894d8e39407595efd2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="group__SANITIZER__CALLBACK__API.html#gcffbfadfe4c7af894d8e39407595efd2">Sanitizer_CallbackData::functionName</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Name of the runtime or driver API function which issued the callback. This string is a global constant and so may be accessed outside of the callback. 
</div>
</div><p>
<a class="anchor" name="g3866442ee35daa08d5bbd2eeb366f91f"></a><!-- doxytag: member="Sanitizer_CallbackData::functionParams" ref="g3866442ee35daa08d5bbd2eeb366f91f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="group__SANITIZER__CALLBACK__API.html#g3866442ee35daa08d5bbd2eeb366f91f">Sanitizer_CallbackData::functionParams</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the arguments passed to the runtime or driver API call. See generated_cuda_runtime_api_meta.h and generated_cuda_meta.h for structure definitions for the parameters for each runtime and driver API function. 
</div>
</div><p>
<a class="anchor" name="gb523a3af8e202cbbdd706277014041fa"></a><!-- doxytag: member="Sanitizer_CallbackData::functionReturnValue" ref="gb523a3af8e202cbbdd706277014041fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="group__SANITIZER__CALLBACK__API.html#gb523a3af8e202cbbdd706277014041fa">Sanitizer_CallbackData::functionReturnValue</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the return value of the runtime or driver API call. This field is only valid within the SANITIZER_API_EXIT callback. For a runtime API <code>functionReturnValue</code> points to a <code>cudaError_t</code>. For a driver API <code>functionReturnValue</code> points to a <code>CUresult</code>. 
</div>
</div><p>
<a class="anchor" name="g54c88e675e099bc4f01351f871107aab"></a><!-- doxytag: member="Sanitizer_ResourceFunctionsLazyLoadedData::functions" ref="g54c88e675e099bc4f01351f871107aab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const CUfunction* <a class="el" href="group__SANITIZER__CALLBACK__API.html#g54c88e675e099bc4f01351f871107aab">Sanitizer_ResourceFunctionsLazyLoadedData::functions</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An array containing the functions. 
</div>
</div><p>
<a class="anchor" name="g6c69e3440ef748c6c5338505f6bb521e"></a><!-- doxytag: member="Sanitizer_GraphLaunchData::graphExec" ref="g6c69e3440ef748c6c5338505f6bb521e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphExec <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6c69e3440ef748c6c5338505f6bb521e">Sanitizer_GraphLaunchData::graphExec</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Instance of the CUDA graph being launched. 
</div>
</div><p>
<a class="anchor" name="gb1a817799b020868a692b5b2d82be2a9"></a><!-- doxytag: member="Sanitizer_GraphExecData::graphExec" ref="gb1a817799b020868a692b5b2d82be2a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphExec <a class="el" href="group__SANITIZER__CALLBACK__API.html#gb1a817799b020868a692b5b2d82be2a9">Sanitizer_GraphExecData::graphExec</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Instance of the CUDA graph. Can be NULL for device graph launches in the SANITIZER_CBID_GRAPHS_GRAPHEXEC_CREATING callback. 
</div>
</div><p>
<a class="anchor" name="g934581cb477c3d030f30e2b2ee7944d4"></a><!-- doxytag: member="Sanitizer_LaunchData::gridDim_x" ref="g934581cb477c3d030f30e2b2ee7944d4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g934581cb477c3d030f30e2b2ee7944d4">Sanitizer_LaunchData::gridDim_x</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="g66f032ce9b5eaac75a5993167cf282c8"></a><!-- doxytag: member="Sanitizer_LaunchData::gridDim_y" ref="g66f032ce9b5eaac75a5993167cf282c8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g66f032ce9b5eaac75a5993167cf282c8">Sanitizer_LaunchData::gridDim_y</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="g9a0d553822926781996a01e8255ac049"></a><!-- doxytag: member="Sanitizer_LaunchData::gridDim_z" ref="g9a0d553822926781996a01e8255ac049" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9a0d553822926781996a01e8255ac049">Sanitizer_LaunchData::gridDim_z</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch properties of the grid. These values are only valid for SANITIZER_CBID_LAUNCH_BEGIN and graph node launch callbacks 
</div>
</div><p>
<a class="anchor" name="gb9302cae916c7eaab0c6de268b246e59"></a><!-- doxytag: member="Sanitizer_LaunchData::gridId" ref="gb9302cae916c7eaab0c6de268b246e59" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gb9302cae916c7eaab0c6de268b246e59">Sanitizer_LaunchData::gridId</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique identifier of the grid launch. For graph node launches, this is only unique within the graphexec launch. 
</div>
</div><p>
<a class="anchor" name="gb4c74f8d8eaa10a76a9eb055e02bbfdb"></a><!-- doxytag: member="Sanitizer_LaunchData::hApiStream" ref="gb4c74f8d8eaa10a76a9eb055e02bbfdb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#gb4c74f8d8eaa10a76a9eb055e02bbfdb">Sanitizer_LaunchData::hApiStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the API stream. 
</div>
</div><p>
<a class="anchor" name="g6dbe2da80b68f48859499c24d325b9f0"></a><!-- doxytag: member="Sanitizer_ResourceArrayData::hArray" ref="g6dbe2da80b68f48859499c24d325b9f0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUarray <a class="el" href="group__SANITIZER__CALLBACK__API.html#g6dbe2da80b68f48859499c24d325b9f0">Sanitizer_ResourceArrayData::hArray</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The CUDA array being created or destroyed. 
</div>
</div><p>
<a class="anchor" name="g3e5d47e9ca81bbf4192dc9cd4958ca9d"></a><!-- doxytag: member="Sanitizer_MemcpyData::hDstStream" ref="g3e5d47e9ca81bbf4192dc9cd4958ca9d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g3e5d47e9ca81bbf4192dc9cd4958ca9d">Sanitizer_MemcpyData::hDstStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the destination context stream. 
</div>
</div><p>
<a class="anchor" name="g57fec3035b443ac0cc7e35908ca53117"></a><!-- doxytag: member="Sanitizer_LaunchData::hLaunch" ref="g57fec3035b443ac0cc7e35908ca53117" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_LaunchHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g57fec3035b443ac0cc7e35908ca53117">Sanitizer_LaunchData::hLaunch</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Handle of the grid launch. This is only valid between the launch begin and end callbacks. 
</div>
</div><p>
<a class="anchor" name="gbe70ebd1173d6137ca064e66b4b99965"></a><!-- doxytag: member="Sanitizer_MemcpyData::hSrcStream" ref="gbe70ebd1173d6137ca064e66b4b99965" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#gbe70ebd1173d6137ca064e66b4b99965">Sanitizer_MemcpyData::hSrcStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the source context stream. 
</div>
</div><p>
<a class="anchor" name="g2f6747e6a89033d27f91d7b72e5599be"></a><!-- doxytag: member="Sanitizer_EventData::hStream" ref="g2f6747e6a89033d27f91d7b72e5599be" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2f6747e6a89033d27f91d7b72e5599be">Sanitizer_EventData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g27bdfc7058401c7a88e7bf538752c5ab"></a><!-- doxytag: member="Sanitizer_GraphLaunchData::hStream" ref="g27bdfc7058401c7a88e7bf538752c5ab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g27bdfc7058401c7a88e7bf538752c5ab">Sanitizer_GraphLaunchData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g0e5aefe3e911df95c5a4b6f0e227467e"></a><!-- doxytag: member="Sanitizer_UvmData::hStream" ref="g0e5aefe3e911df95c5a4b6f0e227467e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g0e5aefe3e911df95c5a4b6f0e227467e">Sanitizer_UvmData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g3ef9cb88408697511f56effdfeed5da8"></a><!-- doxytag: member="Sanitizer_BatchMemopData::hStream" ref="g3ef9cb88408697511f56effdfeed5da8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g3ef9cb88408697511f56effdfeed5da8">Sanitizer_BatchMemopData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g0246494f29ba1644b2a3abb5ac2d6cee"></a><!-- doxytag: member="Sanitizer_MemsetData::hStream" ref="g0246494f29ba1644b2a3abb5ac2d6cee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g0246494f29ba1644b2a3abb5ac2d6cee">Sanitizer_MemsetData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g07a4addf3473a03f38060b769517bd9e"></a><!-- doxytag: member="Sanitizer_LaunchData::hStream" ref="g07a4addf3473a03f38060b769517bd9e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g07a4addf3473a03f38060b769517bd9e">Sanitizer_LaunchData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g1ef3eb36bdce259175dab94c3cee852d"></a><!-- doxytag: member="Sanitizer_SynchronizeData::hStream" ref="g1ef3eb36bdce259175dab94c3cee852d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g1ef3eb36bdce259175dab94c3cee852d">Sanitizer_SynchronizeData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g9cd401e3148076d3b5cabcdbf54f3f33"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::hStream" ref="g9cd401e3148076d3b5cabcdbf54f3f33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9cd401e3148076d3b5cabcdbf54f3f33">Sanitizer_ResourceMemoryData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Stream containing the allocation being created or destroyed. Can be NULL if the allocation is not attached to a stream. 
</div>
</div><p>
<a class="anchor" name="g17058d030f8c196b4a250148c3e2c662"></a><!-- doxytag: member="Sanitizer_ResourceStreamData::hStream" ref="g17058d030f8c196b4a250148c3e2c662" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_StreamHandle <a class="el" href="group__SANITIZER__CALLBACK__API.html#g17058d030f8c196b4a250148c3e2c662">Sanitizer_ResourceStreamData::hStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g25e97b753473f3e78a76cbe75d66758c"></a><!-- doxytag: member="Sanitizer_MemsetData::isAsync" ref="g25e97b753473f3e78a76cbe75d66758c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g25e97b753473f3e78a76cbe75d66758c">Sanitizer_MemsetData::isAsync</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Boolean value indicating if the transfer is asynchronous. 
</div>
</div><p>
<a class="anchor" name="gd42ab4aa1a7f1ffbfe4e3787d45141f6"></a><!-- doxytag: member="Sanitizer_MemcpyData::isAsync" ref="gd42ab4aa1a7f1ffbfe4e3787d45141f6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gd42ab4aa1a7f1ffbfe4e3787d45141f6">Sanitizer_MemcpyData::isAsync</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Boolean value indicating if the transfer is asynchronous. 
</div>
</div><p>
<a class="anchor" name="gc77f7a54e4953682dcb29ff7bf794914"></a><!-- doxytag: member="Sanitizer_GraphExecData::isDeviceLaunch" ref="gc77f7a54e4953682dcb29ff7bf794914" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gc77f7a54e4953682dcb29ff7bf794914">Sanitizer_GraphExecData::isDeviceLaunch</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Boolean value indicating if the graphexec is for a device graph launch 
</div>
</div><p>
<a class="anchor" name="g43ba40476321bc1b026eab8e3ef364d1"></a><!-- doxytag: member="Sanitizer_GraphLaunchData::isGraphUpload" ref="g43ba40476321bc1b026eab8e3ef364d1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g43ba40476321bc1b026eab8e3ef364d1">Sanitizer_GraphLaunchData::isGraphUpload</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Boolean value indicating if the launch callback is part of a graph upload. This field is only valid if the driver version is 510 or newer. 
</div>
</div><p>
<a class="anchor" name="g72f04317e3ea0b0ba1e62d54c06606ad"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::isGraphUpload" ref="g72f04317e3ea0b0ba1e62d54c06606ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g72f04317e3ea0b0ba1e62d54c06606ad">Sanitizer_GraphNodeLaunchData::isGraphUpload</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Boolean value indicating if the node launch callback is part of a graph upload. 
</div>
</div><p>
<a class="anchor" name="ga44c74c876929dd6c55ee9bb0ea1f833"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::@0::launchData" ref="ga44c74c876929dd6c55ee9bb0ea1f833" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_LaunchData { ... } ::launchData<code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is only valid if nodeType is CU_GRAPH_NODE_TYPE_KERNEL. 
</div>
</div><p>
<a class="anchor" name="gdf81e4d9627fd5f003f01075878e2a89"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::launchId" ref="gdf81e4d9627fd5f003f01075878e2a89" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gdf81e4d9627fd5f003f01075878e2a89">Sanitizer_GraphNodeLaunchData::launchId</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Launch ID for this CUDA graph instance 
</div>
</div><p>
<a class="anchor" name="g0f7484826c1b8bb5f2eca75040fd44cc"></a><!-- doxytag: member="Sanitizer_ResourceModuleData::library" ref="g0f7484826c1b8bb5f2eca75040fd44cc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUlibrary <a class="el" href="group__SANITIZER__CALLBACK__API.html#g0f7484826c1b8bb5f2eca75040fd44cc">Sanitizer_ResourceModuleData::library</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Library associated with the module. 
</div>
</div><p>
<a class="anchor" name="gb77c324ee0c00b852188a048e8402292"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::@0::memAllocData" ref="gb77c324ee0c00b852188a048e8402292" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_ResourceMemoryData { ... } ::memAllocData<code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEM_ALLOC. 
</div>
</div><p>
<a class="anchor" name="ge334046a272b7c07489c0d80fab9f8b6"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::memAllocData" ref="ge334046a272b7c07489c0d80fab9f8b6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structSanitizer__ResourceMemoryData.html">Sanitizer_ResourceMemoryData</a> <a class="el" href="group__SANITIZER__CALLBACK__API.html#ge334046a272b7c07489c0d80fab9f8b6">Sanitizer_GraphNodeLaunchData::memAllocData</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEM_ALLOC. 
</div>
</div><p>
<a class="anchor" name="g1da9f79bf2f871d07b357fc7e3da0c95"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::@0::memcpyData" ref="g1da9f79bf2f871d07b357fc7e3da0c95" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_MemcpyData { ... } ::memcpyData<code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEMCPY. 
</div>
</div><p>
<a class="anchor" name="gf2b961a7eae0b73c12f7ec099d4726f6"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::memcpyData" ref="gf2b961a7eae0b73c12f7ec099d4726f6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structSanitizer__MemcpyData.html">Sanitizer_MemcpyData</a> <a class="el" href="group__SANITIZER__CALLBACK__API.html#gf2b961a7eae0b73c12f7ec099d4726f6">Sanitizer_GraphNodeLaunchData::memcpyData</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEMCPY. 
</div>
</div><p>
<a class="anchor" name="gb14e45e0d7b3921b6e155308a54d7945"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::@0::memFreeAddress" ref="gb14e45e0d7b3921b6e155308a54d7945" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t { ... } ::memFreeAddress<code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The freed device pointer This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEM_FREE. 
</div>
</div><p>
<a class="anchor" name="gda8d9c0a133719825143b35a3e544eea"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::memFreeAddress" ref="gda8d9c0a133719825143b35a3e544eea" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gda8d9c0a133719825143b35a3e544eea">Sanitizer_GraphNodeLaunchData::memFreeAddress</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The freed device pointer This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEM_FREE. 
</div>
</div><p>
<a class="anchor" name="gbe9335a4874c70f9e8fdc287b8a14a38"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::memoryPool" ref="gbe9335a4874c70f9e8fdc287b8a14a38" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUmemoryPool <a class="el" href="group__SANITIZER__CALLBACK__API.html#gbe9335a4874c70f9e8fdc287b8a14a38">Sanitizer_ResourceMemoryData::memoryPool</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Memory pool containing the allocation being created or destroyed. Can be NULL if the allocation is not attached to a memory pool. 
</div>
</div><p>
<a class="anchor" name="g47ec542f59cd0f3e8937121c006eefa2"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::@0::memsetData" ref="g47ec542f59cd0f3e8937121c006eefa2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Sanitizer_MemsetData { ... } ::memsetData<code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEMSET. 
</div>
</div><p>
<a class="anchor" name="ga931adc0a0129a4490a2c6bb786b8d0f"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::memsetData" ref="ga931adc0a0129a4490a2c6bb786b8d0f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structSanitizer__MemsetData.html">Sanitizer_MemsetData</a> <a class="el" href="group__SANITIZER__CALLBACK__API.html#ga931adc0a0129a4490a2c6bb786b8d0f">Sanitizer_GraphNodeLaunchData::memsetData</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is only valid if nodeType is CU_GRAPH_NODE_TYPE_MEMSET. 
</div>
</div><p>
<a class="anchor" name="g149c3ada82b4937473864e2b27bc5825"></a><!-- doxytag: member="Sanitizer_LaunchData::module" ref="g149c3ada82b4937473864e2b27bc5825" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUmodule <a class="el" href="group__SANITIZER__CALLBACK__API.html#g149c3ada82b4937473864e2b27bc5825">Sanitizer_LaunchData::module</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The module containing the grid code. 
</div>
</div><p>
<a class="anchor" name="g549f330264682c91331b433868c166fc"></a><!-- doxytag: member="Sanitizer_ResourceFunctionsLazyLoadedData::module" ref="g549f330264682c91331b433868c166fc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUmodule <a class="el" href="group__SANITIZER__CALLBACK__API.html#g549f330264682c91331b433868c166fc">Sanitizer_ResourceFunctionsLazyLoadedData::module</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The module containing the functions. 
</div>
</div><p>
<a class="anchor" name="g786e3c1a3f4f6d4611f7fa0b4093dca7"></a><!-- doxytag: member="Sanitizer_ResourceModuleData::module" ref="g786e3c1a3f4f6d4611f7fa0b4093dca7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUmodule <a class="el" href="group__SANITIZER__CALLBACK__API.html#g786e3c1a3f4f6d4611f7fa0b4093dca7">Sanitizer_ResourceModuleData::module</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The module being loaded or unloaded. 
</div>
</div><p>
<a class="anchor" name="g86c2a7422b28ed2929f072e3d546b9c3"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::node" ref="g86c2a7422b28ed2929f072e3d546b9c3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphNode <a class="el" href="group__SANITIZER__CALLBACK__API.html#g86c2a7422b28ed2929f072e3d546b9c3">Sanitizer_GraphNodeLaunchData::node</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA graphs node being launched. 
</div>
</div><p>
<a class="anchor" name="ge6c93bc5c7a5dc8a5d6ec72a42f87526"></a><!-- doxytag: member="Sanitizer_GraphNodeLaunchData::nodeType" ref="ge6c93bc5c7a5dc8a5d6ec72a42f87526" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUgraphNodeType <a class="el" href="group__SANITIZER__CALLBACK__API.html#ge6c93bc5c7a5dc8a5d6ec72a42f87526">Sanitizer_GraphNodeLaunchData::nodeType</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA graphs node type. 
</div>
</div><p>
<a class="anchor" name="g2fc6eb609d8345b59f58b5469e73e121"></a><!-- doxytag: member="Sanitizer_ResourceFunctionsLazyLoadedData::numFunctions" ref="g2fc6eb609d8345b59f58b5469e73e121" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g2fc6eb609d8345b59f58b5469e73e121">Sanitizer_ResourceFunctionsLazyLoadedData::numFunctions</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the function array. 
</div>
</div><p>
<a class="anchor" name="g39bce574d2d1e1ef944c1090ce01b09a"></a><!-- doxytag: member="Sanitizer_ResourceModuleData::pCubin" ref="g39bce574d2d1e1ef944c1090ce01b09a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="group__SANITIZER__CALLBACK__API.html#g39bce574d2d1e1ef944c1090ce01b09a">Sanitizer_ResourceModuleData::pCubin</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the associated cubin. 
</div>
</div><p>
<a class="anchor" name="g60c827072502fc8e4aa10c8e8abd8e1b"></a><!-- doxytag: member="Sanitizer_ResourceMempoolData::peerDevice" ref="g60c827072502fc8e4aa10c8e8abd8e1b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="group__SANITIZER__CALLBACK__API.html#g60c827072502fc8e4aa10c8e8abd8e1b">Sanitizer_ResourceMempoolData::peerDevice</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device that access type changed. Available if cbid is SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_ENABLED or SANITIZER_CBID_RESOURCE_MEMPOOL_PEER_ACCESS_DISABLING. 
</div>
</div><p>
<a class="anchor" name="gfa68ea5df21d945ae35162a7573c4d61"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::permissions" ref="gfa68ea5df21d945ae35162a7573c4d61" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gfa68ea5df21d945ae35162a7573c4d61">Sanitizer_ResourceMemoryData::permissions</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Allocation permissions: use Sanitizer_ResourceMemoryPermissions to interpret this field. 
</div>
</div><p>
<a class="anchor" name="g297d5464c98df6630501e117c1262b48"></a><!-- doxytag: member="Sanitizer_MemcpyData::size" ref="g297d5464c98df6630501e117c1262b48" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g297d5464c98df6630501e117c1262b48">Sanitizer_MemcpyData::size</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of the transfer in bytes. 
</div>
</div><p>
<a class="anchor" name="gf33b6cf45e80c06fc9130c719d6e13c7"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::size" ref="gf33b6cf45e80c06fc9130c719d6e13c7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gf33b6cf45e80c06fc9130c719d6e13c7">Sanitizer_ResourceMemoryData::size</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of the allocation being created or destroyed. 
</div>
</div><p>
<a class="anchor" name="g34ae0e07bbf2f9ae371d132c59138ce0"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::sourceDevice" ref="g34ae0e07bbf2f9ae371d132c59138ce0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="group__SANITIZER__CALLBACK__API.html#g34ae0e07bbf2f9ae371d132c59138ce0">Sanitizer_ResourceMemoryData::sourceDevice</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Source device of this allocation (different from device if SANITIZER_MEMORY_FLAG_PEER is set). 
</div>
</div><p>
<a class="anchor" name="gb300021e73a68db7b679bebfc81425e6"></a><!-- doxytag: member="Sanitizer_MemcpyData::srcAddress" ref="gb300021e73a68db7b679bebfc81425e6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gb300021e73a68db7b679bebfc81425e6">Sanitizer_MemcpyData::srcAddress</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source allocation address. 
</div>
</div><p>
<a class="anchor" name="gc851b50eb12aa0e38d1bf5e349b5a66b"></a><!-- doxytag: member="Sanitizer_MemcpyData::srcPitch" ref="gc851b50eb12aa0e38d1bf5e349b5a66b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gc851b50eb12aa0e38d1bf5e349b5a66b">Sanitizer_MemcpyData::srcPitch</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source allocation pitch. 
</div>
</div><p>
<a class="anchor" name="g5dbf45c2f3f09b40d901628ab557ef48"></a><!-- doxytag: member="Sanitizer_MemcpyData::srcStream" ref="g5dbf45c2f3f09b40d901628ab557ef48" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g5dbf45c2f3f09b40d901628ab557ef48">Sanitizer_MemcpyData::srcStream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream where the memcpy is executed on the source context 
</div>
</div><p>
<a class="anchor" name="g96c543b1fdcbd06824271501fc8d1b89"></a><!-- doxytag: member="Sanitizer_EventData::stream" ref="g96c543b1fdcbd06824271501fc8d1b89" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g96c543b1fdcbd06824271501fc8d1b89">Sanitizer_EventData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream being recorded or waiting. Available if cbid is SANITIZER_CBID_EVENTS_RECORD or SANITIZER_CBID_EVENTS_STREAM_WAIT. 
</div>
</div><p>
<a class="anchor" name="g4e19a42decf1a32b5582e4ba19346209"></a><!-- doxytag: member="Sanitizer_GraphLaunchData::stream" ref="g4e19a42decf1a32b5582e4ba19346209" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g4e19a42decf1a32b5582e4ba19346209">Sanitizer_GraphLaunchData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream where the graph is launched. 
</div>
</div><p>
<a class="anchor" name="ga2452503ea44bf5b345f230da98b9402"></a><!-- doxytag: member="Sanitizer_UvmData::stream" ref="ga2452503ea44bf5b345f230da98b9402" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#ga2452503ea44bf5b345f230da98b9402">Sanitizer_UvmData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream on which the memory is attached. This is only valid if visibility is SANITIZER_MEMORY_VISIBILITY_STREAM 
</div>
</div><p>
<a class="anchor" name="gd0b50ad0f897b982a76d9542253fec93"></a><!-- doxytag: member="Sanitizer_BatchMemopData::stream" ref="gd0b50ad0f897b982a76d9542253fec93" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#gd0b50ad0f897b982a76d9542253fec93">Sanitizer_BatchMemopData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream where the batch memop is executed. 
</div>
</div><p>
<a class="anchor" name="g568656a07f80c2747dfd6eaf3ac6fa95"></a><!-- doxytag: member="Sanitizer_MemsetData::stream" ref="g568656a07f80c2747dfd6eaf3ac6fa95" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g568656a07f80c2747dfd6eaf3ac6fa95">Sanitizer_MemsetData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream where the memset is executed. 
</div>
</div><p>
<a class="anchor" name="g670de4d81de9ea2c2a2f875281f3d726"></a><!-- doxytag: member="Sanitizer_LaunchData::stream" ref="g670de4d81de9ea2c2a2f875281f3d726" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g670de4d81de9ea2c2a2f875281f3d726">Sanitizer_LaunchData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream where the grid is launched. 
</div>
</div><p>
<a class="anchor" name="g88f86980a41884a31acac2b04f1d42f9"></a><!-- doxytag: member="Sanitizer_SynchronizeData::stream" ref="g88f86980a41884a31acac2b04f1d42f9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g88f86980a41884a31acac2b04f1d42f9">Sanitizer_SynchronizeData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This field is only valid for SANITIZER_CBID_SYNCHRONIZE_STREAM_SYNCHRONIZED. This is the stream being synchronized. 
</div>
</div><p>
<a class="anchor" name="gbb146be5d2857f1294a1af6edf28f6f5"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::stream" ref="gbb146be5d2857f1294a1af6edf28f6f5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#gbb146be5d2857f1294a1af6edf28f6f5">Sanitizer_ResourceMemoryData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Public handle for the stream. 
</div>
</div><p>
<a class="anchor" name="g9ac109c0cc16da3636538dfcebaeac33"></a><!-- doxytag: member="Sanitizer_ResourceStreamData::stream" ref="g9ac109c0cc16da3636538dfcebaeac33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9ac109c0cc16da3636538dfcebaeac33">Sanitizer_ResourceStreamData::stream</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The stream being created or destroyed. This handle will be NULL for the STREAM_DESTROY_FINISHED cbid. 
</div>
</div><p>
<a class="anchor" name="g1ee030118e093f440f953d896ee80be6"></a><!-- doxytag: member="Sanitizer_CallbackData::symbolName" ref="g1ee030118e093f440f953d896ee80be6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="group__SANITIZER__CALLBACK__API.html#g1ee030118e093f440f953d896ee80be6">Sanitizer_CallbackData::symbolName</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Name of the symbol operated on by the runtime or driver API function which issued the callback. This entry is valid only for driver and runtime launch callbacks, where it returns the name of the kernel. 
</div>
</div><p>
<a class="anchor" name="g874b875cc64512766ed6d8f43fea3afd"></a><!-- doxytag: member="Sanitizer_BatchMemopData::type" ref="g874b875cc64512766ed6d8f43fea3afd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__CALLBACK__API.html#gab53af117ae98aa6acf0a6b4dfb71cfa">Sanitizer_BatchMemopType</a> <a class="el" href="group__SANITIZER__CALLBACK__API.html#g874b875cc64512766ed6d8f43fea3afd">Sanitizer_BatchMemopData::type</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of batch memory operation. 
</div>
</div><p>
<a class="anchor" name="g61c24785086fa41e344d54faa076b6d0"></a><!-- doxytag: member="Sanitizer_BatchMemopData::value" ref="g61c24785086fa41e344d54faa076b6d0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g61c24785086fa41e344d54faa076b6d0">Sanitizer_BatchMemopData::value</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The value to be written. 
</div>
</div><p>
<a class="anchor" name="g5fc409e9d272bef19b26b754103152f2"></a><!-- doxytag: member="Sanitizer_MemsetData::value" ref="g5fc409e9d272bef19b26b754103152f2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g5fc409e9d272bef19b26b754103152f2">Sanitizer_MemsetData::value</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Value to be written. 
</div>
</div><p>
<a class="anchor" name="g9c9d131f19f523c3c107ea5ea55f6676"></a><!-- doxytag: member="Sanitizer_UvmData::visibility" ref="g9c9d131f19f523c3c107ea5ea55f6676" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g62d02f306fdb955ab1f5add810c45564">Sanitizer_MemoryVisibility</a> <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9c9d131f19f523c3c107ea5ea55f6676">Sanitizer_UvmData::visibility</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
New visibility for the allocation. 
</div>
</div><p>
<a class="anchor" name="g5838f90e131f6b6761c39986b9b6efd8"></a><!-- doxytag: member="Sanitizer_ResourceMemoryData::visibility" ref="g5838f90e131f6b6761c39986b9b6efd8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__CALLBACK__API.html#g62d02f306fdb955ab1f5add810c45564">Sanitizer_MemoryVisibility</a> <a class="el" href="group__SANITIZER__CALLBACK__API.html#g5838f90e131f6b6761c39986b9b6efd8">Sanitizer_ResourceMemoryData::visibility</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Visibility of the allocation. 
</div>
</div><p>
<a class="anchor" name="gdf48f929e8ccaf649688cdfac51b63a2"></a><!-- doxytag: member="Sanitizer_MemsetData::width" ref="gdf48f929e8ccaf649688cdfac51b63a2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gdf48f929e8ccaf649688cdfac51b63a2">Sanitizer_MemsetData::width</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Memset size configuration. 
</div>
</div><p>
<a class="anchor" name="g9bb9e8fa40bb441864e2620ebb2eb430"></a><!-- doxytag: member="Sanitizer_MemcpyData::width" ref="g9bb9e8fa40bb441864e2620ebb2eb430" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#g9bb9e8fa40bb441864e2620ebb2eb430">Sanitizer_MemcpyData::width</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Memcpy size configuration. 
</div>
</div><p>
<a class="anchor" name="gda45296bad0f62f6df2ca07a7b545794"></a><!-- doxytag: member="Sanitizer_ResourceArrayData::width" ref="gda45296bad0f62f6df2ca07a7b545794" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="group__SANITIZER__CALLBACK__API.html#gda45296bad0f62f6df2ca07a7b545794">Sanitizer_ResourceArrayData::width</a><code> [inherited]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The CUDA array size. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Tue Jan 24 19:43:08 2023 for SanitizerApi by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
