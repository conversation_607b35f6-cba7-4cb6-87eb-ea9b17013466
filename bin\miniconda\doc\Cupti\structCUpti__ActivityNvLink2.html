<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityNvLink2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityNvLink2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityNvLink2" -->NVLink information. (deprecated in CUDA 10.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#abe1675b4fafa363a914e81f93ed3521">bandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#f217b34a45bda6725821b44bf5c2c035">flag</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#687eabf907f0b40217df4a73749479ee">idDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#2118b1a8aefb0008db90781c1a0ff555">idDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#844e5321697af1287f88ed321f6c870b">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#6a1b2ad0944e964b9aa86ac339407ed5">nvlinkVersion</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#7e41e33987558b0c22d6d02d30bc5e6e">physicalNvLinkCount</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#cebbaa71fc8a2210aeb76b31f00b9bb5">portDev0</a> [CUPTI_MAX_NVLINK_PORTS]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#4a69b1b6b568c83d4f41b765c3d5dd72">portDev1</a> [CUPTI_MAX_NVLINK_PORTS]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#eb937266117b2e71c7438fc63f9a6cbb">typeDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#505ebfd0a0324e4bd11bf90d4773826d">typeDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#0861caf72817ac60c3a3bc880f114e36">domainId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html#76e29d77543928f742e16b2eaf82fb34">index</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure gives capabilities of each logical NVLink connection between two devices, gpu&lt;-&gt;gpu or gpu&lt;-&gt;CPU which can be used to understand the topology. NvLink information are now reported using the <a class="el" href="structCUpti__ActivityNvLink4.html" title="NVLink information.">CUpti_ActivityNvLink4</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="abe1675b4fafa363a914e81f93ed3521"></a><!-- doxytag: member="CUpti_ActivityNvLink2::bandwidth" ref="abe1675b4fafa363a914e81f93ed3521" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityNvLink2.html#abe1675b4fafa363a914e81f93ed3521">CUpti_ActivityNvLink2::bandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Banwidth of NVLink in kbytes/sec 
</div>
</div><p>
<a class="anchor" name="0861caf72817ac60c3a3bc880f114e36"></a><!-- doxytag: member="CUpti_ActivityNvLink2::domainId" ref="0861caf72817ac60c3a3bc880f114e36" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink2.html#0861caf72817ac60c3a3bc880f114e36">CUpti_ActivityNvLink2::domainId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Domain ID of NPU. On Linux, this can be queried using lspci. 
</div>
</div><p>
<a class="anchor" name="f217b34a45bda6725821b44bf5c2c035"></a><!-- doxytag: member="CUpti_ActivityNvLink2::flag" ref="f217b34a45bda6725821b44bf5c2c035" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink2.html#f217b34a45bda6725821b44bf5c2c035">CUpti_ActivityNvLink2::flag</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flag gives capabilities of the link <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g819d686d23d2a3dfed9cc1d3c4ecb162" title="Link flags.">CUpti_LinkFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="687eabf907f0b40217df4a73749479ee"></a><!-- doxytag: member="CUpti_ActivityNvLink2::idDev0" ref="687eabf907f0b40217df4a73749479ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink2.html#687eabf907f0b40217df4a73749479ee">CUpti_ActivityNvLink2::idDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev0 is CUPTI_DEV_TYPE_GPU, UUID for device 0. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev0 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="2118b1a8aefb0008db90781c1a0ff555"></a><!-- doxytag: member="CUpti_ActivityNvLink2::idDev1" ref="2118b1a8aefb0008db90781c1a0ff555" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink2.html#2118b1a8aefb0008db90781c1a0ff555">CUpti_ActivityNvLink2::idDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev1 is CUPTI_DEV_TYPE_GPU, UUID for device 1. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev1 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="76e29d77543928f742e16b2eaf82fb34"></a><!-- doxytag: member="CUpti_ActivityNvLink2::index" ref="76e29d77543928f742e16b2eaf82fb34" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink2.html#76e29d77543928f742e16b2eaf82fb34">CUpti_ActivityNvLink2::index</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Index of the NPU. First index will always be zero. 
</div>
</div><p>
<a class="anchor" name="844e5321697af1287f88ed321f6c870b"></a><!-- doxytag: member="CUpti_ActivityNvLink2::kind" ref="844e5321697af1287f88ed321f6c870b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityNvLink2.html#844e5321697af1287f88ed321f6c870b">CUpti_ActivityNvLink2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_NVLINK. 
</div>
</div><p>
<a class="anchor" name="6a1b2ad0944e964b9aa86ac339407ed5"></a><!-- doxytag: member="CUpti_ActivityNvLink2::nvlinkVersion" ref="6a1b2ad0944e964b9aa86ac339407ed5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink2.html#6a1b2ad0944e964b9aa86ac339407ed5">CUpti_ActivityNvLink2::nvlinkVersion</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
NvLink version. 
</div>
</div><p>
<a class="anchor" name="7e41e33987558b0c22d6d02d30bc5e6e"></a><!-- doxytag: member="CUpti_ActivityNvLink2::physicalNvLinkCount" ref="7e41e33987558b0c22d6d02d30bc5e6e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink2.html#7e41e33987558b0c22d6d02d30bc5e6e">CUpti_ActivityNvLink2::physicalNvLinkCount</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of physical NVLinks present between two devices. 
</div>
</div><p>
<a class="anchor" name="cebbaa71fc8a2210aeb76b31f00b9bb5"></a><!-- doxytag: member="CUpti_ActivityNvLink2::portDev0" ref="cebbaa71fc8a2210aeb76b31f00b9bb5" args="[CUPTI_MAX_NVLINK_PORTS]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink2.html#cebbaa71fc8a2210aeb76b31f00b9bb5">CUpti_ActivityNvLink2::portDev0</a>[CUPTI_MAX_NVLINK_PORTS]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 16 NVLinks connected to device 0. If typeDev0 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="4a69b1b6b568c83d4f41b765c3d5dd72"></a><!-- doxytag: member="CUpti_ActivityNvLink2::portDev1" ref="4a69b1b6b568c83d4f41b765c3d5dd72" args="[CUPTI_MAX_NVLINK_PORTS]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink2.html#4a69b1b6b568c83d4f41b765c3d5dd72">CUpti_ActivityNvLink2::portDev1</a>[CUPTI_MAX_NVLINK_PORTS]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 16 NVLinks connected to device 1. If typeDev1 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="eb937266117b2e71c7438fc63f9a6cbb"></a><!-- doxytag: member="CUpti_ActivityNvLink2::typeDev0" ref="eb937266117b2e71c7438fc63f9a6cbb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink2.html#eb937266117b2e71c7438fc63f9a6cbb">CUpti_ActivityNvLink2::typeDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 0 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
<a class="anchor" name="505ebfd0a0324e4bd11bf90d4773826d"></a><!-- doxytag: member="CUpti_ActivityNvLink2::typeDev1" ref="505ebfd0a0324e4bd11bf90d4773826d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink2.html#505ebfd0a0324e4bd11bf90d4773826d">CUpti_ActivityNvLink2::typeDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 1 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
