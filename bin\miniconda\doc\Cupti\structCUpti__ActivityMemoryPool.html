<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemoryPool Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemoryPool Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemoryPool" -->The activity record for memory pool.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#3afd484b38b277c0c316bdc63908d234">address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#aa3950c8b0948cd7695d056d6085ca23">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#149052bae96013047636a686f3431363">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#c064cd3f0b2082c9f6b5c0f6dedeb27a">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#c528fd8e8bbaee5a059f2bd37294be5b">memoryPoolOperationType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#ef44346ffbd9c5d4453622d092ef09bb">memoryPoolType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#2edb7239b29bfa31423278dabcab29fb">minBytesToKeep</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#866a54d44c9c5fb25df9da5e51052843">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#7c1777244ba2c452f778dfcbc44d7e95">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#7e2918760a23740da9eef884d33f1ad9">releaseThreshold</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#8eebc490f35e113385549c55e4900fcf">size</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html#8dd5075b882ee9f4fa3adb0c2d71151c">timestamp</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory pool creation, destruction and trimming (CUPTI_ACTIVITY_KIND_MEMORY_POOL). This activity record provides separate records for memory pool creation, destruction and triming operations. This allows to correlate the corresponding driver and runtime API activity record with the memory pool operation. <hr><h2>Field Documentation</h2>
<a class="anchor" name="3afd484b38b277c0c316bdc63908d234"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::address" ref="3afd484b38b277c0c316bdc63908d234" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool.html#3afd484b38b277c0c316bdc63908d234">CUpti_ActivityMemoryPool::address</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The virtual address of the allocation. 
</div>
</div><p>
<a class="anchor" name="aa3950c8b0948cd7695d056d6085ca23"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::correlationId" ref="aa3950c8b0948cd7695d056d6085ca23" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool.html#aa3950c8b0948cd7695d056d6085ca23">CUpti_ActivityMemoryPool::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory pool operation. Each memory pool operation is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory operation. 
</div>
</div><p>
<a class="anchor" name="149052bae96013047636a686f3431363"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::deviceId" ref="149052bae96013047636a686f3431363" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool.html#149052bae96013047636a686f3431363">CUpti_ActivityMemoryPool::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory pool is created. 
</div>
</div><p>
<a class="anchor" name="c064cd3f0b2082c9f6b5c0f6dedeb27a"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::kind" ref="c064cd3f0b2082c9f6b5c0f6dedeb27a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemoryPool.html#c064cd3f0b2082c9f6b5c0f6dedeb27a">CUpti_ActivityMemoryPool::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMORY_POOL 
</div>
</div><p>
<a class="anchor" name="c528fd8e8bbaee5a059f2bd37294be5b"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::memoryPoolOperationType" ref="c528fd8e8bbaee5a059f2bd37294be5b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a> <a class="el" href="structCUpti__ActivityMemoryPool.html#c528fd8e8bbaee5a059f2bd37294be5b">CUpti_ActivityMemoryPool::memoryPoolOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory operation requested by the user, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a>. 
</div>
</div><p>
<a class="anchor" name="ef44346ffbd9c5d4453622d092ef09bb"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::memoryPoolType" ref="ef44346ffbd9c5d4453622d092ef09bb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> <a class="el" href="structCUpti__ActivityMemoryPool.html#ef44346ffbd9c5d4453622d092ef09bb">CUpti_ActivityMemoryPool::memoryPoolType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the memory pool, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> 
</div>
</div><p>
<a class="anchor" name="2edb7239b29bfa31423278dabcab29fb"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::minBytesToKeep" ref="2edb7239b29bfa31423278dabcab29fb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__ActivityMemoryPool.html#2edb7239b29bfa31423278dabcab29fb">CUpti_ActivityMemoryPool::minBytesToKeep</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The minimum bytes to keep of the memory pool. <code>minBytesToKeep</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_TRIMMED, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a> 
</div>
</div><p>
<a class="anchor" name="866a54d44c9c5fb25df9da5e51052843"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::pad" ref="866a54d44c9c5fb25df9da5e51052843" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool.html#866a54d44c9c5fb25df9da5e51052843">CUpti_ActivityMemoryPool::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="7c1777244ba2c452f778dfcbc44d7e95"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::processId" ref="7c1777244ba2c452f778dfcbc44d7e95" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool.html#7c1777244ba2c452f778dfcbc44d7e95">CUpti_ActivityMemoryPool::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process to which this record belongs to. 
</div>
</div><p>
<a class="anchor" name="7e2918760a23740da9eef884d33f1ad9"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::releaseThreshold" ref="7e2918760a23740da9eef884d33f1ad9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool.html#7e2918760a23740da9eef884d33f1ad9">CUpti_ActivityMemoryPool::releaseThreshold</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The release threshold of the memory pool. <code>releaseThreshold</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="8eebc490f35e113385549c55e4900fcf"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::size" ref="8eebc490f35e113385549c55e4900fcf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool.html#8eebc490f35e113385549c55e4900fcf">CUpti_ActivityMemoryPool::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the memory pool operation in bytes. <code>size</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="8dd5075b882ee9f4fa3adb0c2d71151c"></a><!-- doxytag: member="CUpti_ActivityMemoryPool::timestamp" ref="8dd5075b882ee9f4fa3adb0c2d71151c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool.html#8dd5075b882ee9f4fa3adb0c2d71151c">CUpti_ActivityMemoryPool::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory operation, in ns. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
