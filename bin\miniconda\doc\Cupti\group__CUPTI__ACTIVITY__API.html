<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Activity API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Activity API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Activity.html">CUpti_Activity</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The base activity record.  <a href="structCUpti__Activity.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAPI.html">CUpti_ActivityAPI</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a driver or runtime API invocation.  <a href="structCUpti__ActivityAPI.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAutoBoostState.html">CUpti_ActivityAutoBoostState</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Device auto boost state structure.  <a href="structCUpti__ActivityAutoBoostState.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch.html">CUpti_ActivityBranch</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source level result branch. (deprecated).  <a href="structCUpti__ActivityBranch.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html">CUpti_ActivityBranch2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source level result branch.  <a href="structCUpti__ActivityBranch2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html">CUpti_ActivityCdpKernel</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for CDP (CUDA Dynamic Parallelism) kernel.  <a href="structCUpti__ActivityCdpKernel.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityContext.html">CUpti_ActivityContext</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a context.  <a href="structCUpti__ActivityContext.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCudaEvent.html">CUpti_ActivityCudaEvent</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for CUDA event.  <a href="structCUpti__ActivityCudaEvent.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html">CUpti_ActivityDevice</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a device. (deprecated).  <a href="structCUpti__ActivityDevice.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html">CUpti_ActivityDevice2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a device. (deprecated).  <a href="structCUpti__ActivityDevice2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html">CUpti_ActivityDevice3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a device. (CUDA 7.0 onwards).  <a href="structCUpti__ActivityDevice3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a device. (CUDA 11.6 onwards).  <a href="structCUpti__ActivityDevice4.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDeviceAttribute.html">CUpti_ActivityDeviceAttribute</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a device attribute.  <a href="structCUpti__ActivityDeviceAttribute.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html">CUpti_ActivityEnvironment</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for CUPTI environmental data.  <a href="structCUpti__ActivityEnvironment.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEvent.html">CUpti_ActivityEvent</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a CUPTI event.  <a href="structCUpti__ActivityEvent.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEventInstance.html">CUpti_ActivityEventInstance</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a CUPTI event with instance information.  <a href="structCUpti__ActivityEventInstance.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityExternalCorrelation.html">CUpti_ActivityExternalCorrelation</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for correlation with external records.  <a href="structCUpti__ActivityExternalCorrelation.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityFunction.html">CUpti_ActivityFunction</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for global/device functions.  <a href="structCUpti__ActivityFunction.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess.html">CUpti_ActivityGlobalAccess</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source-level global access. (deprecated).  <a href="structCUpti__ActivityGlobalAccess.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html">CUpti_ActivityGlobalAccess2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source-level global access. (deprecated in CUDA 9.0).  <a href="structCUpti__ActivityGlobalAccess2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess3.html">CUpti_ActivityGlobalAccess3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source-level global access.  <a href="structCUpti__ActivityGlobalAccess3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGraphTrace.html">CUpti_ActivityGraphTrace</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for trace of graph execution.  <a href="structCUpti__ActivityGraphTrace.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEvent.html">CUpti_ActivityInstantaneousEvent</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for an instantaneous CUPTI event.  <a href="structCUpti__ActivityInstantaneousEvent.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEventInstance.html">CUpti_ActivityInstantaneousEventInstance</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for an instantaneous CUPTI event with event domain instance information.  <a href="structCUpti__ActivityInstantaneousEventInstance.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html">CUpti_ActivityInstantaneousMetric</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for an instantaneous CUPTI metric.  <a href="structCUpti__ActivityInstantaneousMetric.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html">CUpti_ActivityInstantaneousMetricInstance</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The instantaneous activity record for a CUPTI metric with instance information.  <a href="structCUpti__ActivityInstantaneousMetricInstance.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionCorrelation.html">CUpti_ActivityInstructionCorrelation</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source-level sass/source line-by-line correlation.  <a href="structCUpti__ActivityInstructionCorrelation.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstructionExecution.html">CUpti_ActivityInstructionExecution</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source-level instruction execution.  <a href="structCUpti__ActivityInstructionExecution.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html">CUpti_ActivityJit</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation.  <a href="structCUpti__ActivityJit.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html">CUpti_ActivityKernel</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for kernel. (deprecated).  <a href="structCUpti__ActivityKernel.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html">CUpti_ActivityKernel2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for kernel. (deprecated).  <a href="structCUpti__ActivityKernel2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel3.html">CUpti_ActivityKernel3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0).  <a href="structCUpti__ActivityKernel3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html">CUpti_ActivityKernel4</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0).  <a href="structCUpti__ActivityKernel4.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html">CUpti_ActivityKernel5</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record.  <a href="structCUpti__ActivityKernel5.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html">CUpti_ActivityKernel6</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for kernel. (deprecated in CUDA 11.6).  <a href="structCUpti__ActivityKernel6.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html">CUpti_ActivityKernel7</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for kernel. (deprecated in CUDA 11.8).  <a href="structCUpti__ActivityKernel7.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html">CUpti_ActivityKernel8</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for kernel.  <a href="structCUpti__ActivityKernel8.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html">CUpti_ActivityKernel9</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for kernel.  <a href="structCUpti__ActivityKernel9.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html">CUpti_ActivityMarker</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0).  <a href="structCUpti__ActivityMarker.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html">CUpti_ActivityMarker2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record providing a marker which is an instantaneous point in time.  <a href="structCUpti__ActivityMarker2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarkerData.html">CUpti_ActivityMarkerData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record providing detailed information for a marker.  <a href="structCUpti__ActivityMarkerData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html">CUpti_ActivityMemcpy</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory copies. (deprecated).  <a href="structCUpti__ActivityMemcpy.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html">CUpti_ActivityMemcpy3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory copies. (deprecated in CUDA 11.1).  <a href="structCUpti__ActivityMemcpy3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html">CUpti_ActivityMemcpy4</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory copies. (deprecated in CUDA 11.6).  <a href="structCUpti__ActivityMemcpy4.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html">CUpti_ActivityMemcpy5</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory copies.  <a href="structCUpti__ActivityMemcpy5.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html">CUpti_ActivityMemcpyPtoP</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for peer-to-peer memory copies.  <a href="structCUpti__ActivityMemcpyPtoP.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html">CUpti_ActivityMemcpyPtoP2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1).  <a href="structCUpti__ActivityMemcpyPtoP2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html">CUpti_ActivityMemcpyPtoP3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6).  <a href="structCUpti__ActivityMemcpyPtoP3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP4.html">CUpti_ActivityMemcpyPtoP4</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for peer-to-peer memory copies.  <a href="structCUpti__ActivityMemcpyPtoP4.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html">CUpti_ActivityMemory</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory.  <a href="structCUpti__ActivityMemory.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html">CUpti_ActivityMemory2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory.  <a href="structCUpti__ActivityMemory2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html">CUpti_ActivityMemory3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory.  <a href="structCUpti__ActivityMemory3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool.html">CUpti_ActivityMemoryPool</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory pool.  <a href="structCUpti__ActivityMemoryPool.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html">CUpti_ActivityMemoryPool2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memory pool.  <a href="structCUpti__ActivityMemoryPool2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html">CUpti_ActivityMemset</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memset. (deprecated).  <a href="structCUpti__ActivityMemset.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html">CUpti_ActivityMemset2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memset. (deprecated in CUDA 11.1).  <a href="structCUpti__ActivityMemset2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset3.html">CUpti_ActivityMemset3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memset. (deprecated in CUDA 11.6).  <a href="structCUpti__ActivityMemset3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html">CUpti_ActivityMemset4</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for memset.  <a href="structCUpti__ActivityMemset4.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMetric.html">CUpti_ActivityMetric</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a CUPTI metric.  <a href="structCUpti__ActivityMetric.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMetricInstance.html">CUpti_ActivityMetricInstance</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a CUPTI metric with instance information.  <a href="structCUpti__ActivityMetricInstance.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityModule.html">CUpti_ActivityModule</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a CUDA module.  <a href="structCUpti__ActivityModule.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityName.html">CUpti_ActivityName</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record providing a name.  <a href="structCUpti__ActivityName.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html">CUpti_ActivityNvLink</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">NVLink information. (deprecated in CUDA 9.0).  <a href="structCUpti__ActivityNvLink.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink2.html">CUpti_ActivityNvLink2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">NVLink information. (deprecated in CUDA 10.0).  <a href="structCUpti__ActivityNvLink2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html">CUpti_ActivityNvLink3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">NVLink information.  <a href="structCUpti__ActivityNvLink3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html">CUpti_ActivityNvLink4</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">NVLink information.  <a href="structCUpti__ActivityNvLink4.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">union &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionCUpti__ActivityObjectKindId.html">CUpti_ActivityObjectKindId</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Identifiers for object kinds as specified by CUpti_ActivityObjectKind.  <a href="unionCUpti__ActivityObjectKindId.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html">CUpti_ActivityOpenAcc</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The base activity record for OpenAcc records.  <a href="structCUpti__ActivityOpenAcc.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html">CUpti_ActivityOpenAccData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for OpenACC data.  <a href="structCUpti__ActivityOpenAccData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html">CUpti_ActivityOpenAccLaunch</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for OpenACC launch.  <a href="structCUpti__ActivityOpenAccLaunch.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html">CUpti_ActivityOpenAccOther</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for OpenACC other.  <a href="structCUpti__ActivityOpenAccOther.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenMp.html">CUpti_ActivityOpenMp</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The base activity record for OpenMp records.  <a href="structCUpti__ActivityOpenMp.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOverhead.html">CUpti_ActivityOverhead</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for CUPTI and driver overheads.  <a href="structCUpti__ActivityOverhead.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html">CUpti_ActivityPcie</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PCI devices information required to construct topology.  <a href="structCUpti__ActivityPcie.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling.html">CUpti_ActivityPCSampling</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for PC sampling. (deprecated in CUDA 8.0).  <a href="structCUpti__ActivityPCSampling.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html">CUpti_ActivityPCSampling2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for PC sampling. (deprecated in CUDA 9.0).  <a href="structCUpti__ActivityPCSampling2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html">CUpti_ActivityPCSampling3</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for PC sampling.  <a href="structCUpti__ActivityPCSampling3.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSamplingConfig.html">CUpti_ActivityPCSamplingConfig</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PC sampling configuration structure.  <a href="structCUpti__ActivityPCSamplingConfig.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSamplingRecordInfo.html">CUpti_ActivityPCSamplingRecordInfo</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for record status for PC sampling.  <a href="structCUpti__ActivityPCSamplingRecordInfo.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html">CUpti_ActivityPreemption</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for a preemption of a CDP kernel.  <a href="structCUpti__ActivityPreemption.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html">CUpti_ActivitySharedAccess</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source-level shared access.  <a href="structCUpti__ActivitySharedAccess.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySourceLocator.html">CUpti_ActivitySourceLocator</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for source locator.  <a href="structCUpti__ActivitySourceLocator.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityStream.html">CUpti_ActivityStream</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for CUDA stream.  <a href="structCUpti__ActivityStream.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html">CUpti_ActivitySynchronization</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for synchronization management.  <a href="structCUpti__ActivitySynchronization.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html">CUpti_ActivityUnifiedMemoryCounter</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for Unified Memory counters (deprecated in CUDA 7.0).  <a href="structCUpti__ActivityUnifiedMemoryCounter.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html">CUpti_ActivityUnifiedMemoryCounter2</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The activity record for Unified Memory counters (CUDA 7.0 and beyond).  <a href="structCUpti__ActivityUnifiedMemoryCounter2.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html">CUpti_ActivityUnifiedMemoryCounterConfig</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Unified Memory counters configuration structure.  <a href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g43ce5c40a7db28eba98f969fd830dc76">CUPTI_AUTO_BOOST_INVALID_CLIENT_PID</a>&nbsp;&nbsp;&nbsp;0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g97e26d46f328081037f013942dbb82c5">CUPTI_CORRELATION_ID_UNKNOWN</a>&nbsp;&nbsp;&nbsp;0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g3877af5383ad7f9b89aa38d1ae137b0c">CUPTI_FUNCTION_INDEX_ID_INVALID</a>&nbsp;&nbsp;&nbsp;0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g55b5747de740c3daaa3aa63650eb654e">CUPTI_GRID_ID_UNKNOWN</a>&nbsp;&nbsp;&nbsp;0LL</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b7eced29b14203cc46f425adc1471a6">CUPTI_MAX_NVLINK_PORTS</a>&nbsp;&nbsp;&nbsp;32</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gec9d5c6508090f41530b7a2e0e68b3b4">CUPTI_NVLINK_INVALID_PORT</a>&nbsp;&nbsp;&nbsp;-1</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g79142ac09b1bf1b37cf6db06be375533">CUPTI_SOURCE_LOCATOR_ID_UNKNOWN</a>&nbsp;&nbsp;&nbsp;0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd7733ba3c3dc5ac4330a882bbb6f6985">CUPTI_SYNCHRONIZATION_INVALID_VALUE</a>&nbsp;&nbsp;&nbsp;-1</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g03ed33cb1e0d4bf43c446ee3375d0c18">CUPTI_TIMESTAMP_UNKNOWN</a>&nbsp;&nbsp;&nbsp;0LL</td></tr>

<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef void(*&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g081fec67efc2c2504362e8e34fce7e6e">CUpti_BuffersCallbackCompleteFunc</a> )(CUcontext context, uint32_t streamId, uint8_t *buffer, size_t size, size_t validSize)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for callback used by CUPTI to return a buffer of activity records.  <a href="#g081fec67efc2c2504362e8e34fce7e6e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef void(*&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g3337d3cbfe624b05d4af435fd17e5b28">CUpti_BuffersCallbackRequestFunc</a> )(uint8_t **buffer, size_t *size, size_t *maxNumRecords)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for callback used by CUPTI to request an empty buffer for storing activity records.  <a href="#g3337d3cbfe624b05d4af435fd17e5b28"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef uint64_t(*&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gdd850477a3ae876889eb8dae31c8c664">CUpti_TimestampCallbackFunc</a> )(void)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for callback used by CUPTI to request a timestamp to be used in activity records.  <a href="#gdd850477a3ae876889eb8dae31c8c664"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g1c31fe3f8ea0e46c6c20dd454a6caab6">CUpti_ActivityAttribute</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6d4ba37cb123069fa778513f87cbb4b37">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6384faa1e65511b8169aafc2a008479dd">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE_CDP</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab63e84be3bdc3dc8a3477a56a1247287cc">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_POOL_LIMIT</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab68ee6c22d0d55bb65700974bf9b1d75c2">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_SIZE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6d58f5aa6e9afbbcd2f7f7d6bd0896b8b">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_LIMIT</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab69857b104198e9388e15c20ec7650a517">CUPTI_ACTIVITY_ATTR_ZEROED_OUT_ACTIVITY_BUFFER</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab621f4deb586a4d759218e1d7f9530395f">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_PRE_ALLOCATE_VALUE</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab652ecb99e83c0fa9da4bea4250089f3dd">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_PRE_ALLOCATE_VALUE</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab60baaaf2f89ee9a711b635ea3bc4b2e1e">CUPTI_ACTIVITY_ATTR_MEM_ALLOCATION_TYPE_HOST_PINNED</a> =  8
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Activity attributes.  <a href="group__CUPTI__ACTIVITY__API.html#g1c31fe3f8ea0e46c6c20dd454a6caab6">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gf46dcec421da358ab4e9edad76774179">CUpti_ActivityComputeApiKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggf46dcec421da358ab4e9edad76774179f25237989ad34cb0fcc92a91cba86f50">CUPTI_ACTIVITY_COMPUTE_API_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggf46dcec421da358ab4e9edad76774179b1183e2266d08045ce30f7b3f3c6013b">CUPTI_ACTIVITY_COMPUTE_API_CUDA</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggf46dcec421da358ab4e9edad7677417981c34b48f58ef9c0f2eaa50cd2dee0a1">CUPTI_ACTIVITY_COMPUTE_API_CUDA_MPS</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kind of a compute API.  <a href="group__CUPTI__ACTIVITY__API.html#gf46dcec421da358ab4e9edad76774179">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gea1ccba1baca62ba5d6919e5685bfedf">CUpti_ActivityEnvironmentKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggea1ccba1baca62ba5d6919e5685bfedf7b63d7da15a8132974cc5db815eb0eb6">CUPTI_ACTIVITY_ENVIRONMENT_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggea1ccba1baca62ba5d6919e5685bfedf7be05ebb56bd4220518e306040a6178f">CUPTI_ACTIVITY_ENVIRONMENT_SPEED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggea1ccba1baca62ba5d6919e5685bfedf309cd1405723afb008c0c3c84d0db2f2">CUPTI_ACTIVITY_ENVIRONMENT_TEMPERATURE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggea1ccba1baca62ba5d6919e5685bfedf1134e640ea5b503bfa2a30b5fc9cb95d">CUPTI_ACTIVITY_ENVIRONMENT_POWER</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggea1ccba1baca62ba5d6919e5685bfedf223a90fa39a8242f6d91117f7d77a52d">CUPTI_ACTIVITY_ENVIRONMENT_COOLING</a> =  4
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kind of environment data. Used to indicate what type of data is being reported by an environment activity record.  <a href="group__CUPTI__ACTIVITY__API.html#gea1ccba1baca62ba5d6919e5685bfedf">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639377b3dbcdc6b709c20be055d6d81a377">CUPTI_ACTIVITY_FLAG_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639cc20353f87f213030f6fd596c0f18fe0">CUPTI_ACTIVITY_FLAG_DEVICE_CONCURRENT_KERNELS</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639404ddcb5f521402a5a0ff254bd412d14">CUPTI_ACTIVITY_FLAG_DEVICE_ATTRIBUTE_CUDEVICE</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763946792830925ef2f287c5887733a336b7">CUPTI_ACTIVITY_FLAG_MEMCPY_ASYNC</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763983dc601ec20a11fb12f73c91f45d9b59">CUPTI_ACTIVITY_FLAG_MARKER_INSTANTANEOUS</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c376396bad08ee6097ebe3ec034d2af71e69de">CUPTI_ACTIVITY_FLAG_MARKER_START</a> =  1 &lt;&lt; 1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763916e4a905136ad86a17c825d0451761f7">CUPTI_ACTIVITY_FLAG_MARKER_END</a> =  1 &lt;&lt; 2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763913bfb2db6c64e2bc6c5fdd492011919e">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE</a> =  1 &lt;&lt; 3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639d07f530982f52f40bd979dab8096041a">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_SUCCESS</a> =  1 &lt;&lt; 4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763904cc96d6594ce8d30ea8b70c6740fbab">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_FAILED</a> =  1 &lt;&lt; 5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639e2e6eea731826fb203122ca8ada03e22">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_RELEASE</a> =  1 &lt;&lt; 6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c376390b697cbddeb26df86f0737b674ebfa8b">CUPTI_ACTIVITY_FLAG_MARKER_COLOR_NONE</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639b882d6a86cd706c086ef12557c4df0b0">CUPTI_ACTIVITY_FLAG_MARKER_COLOR_ARGB</a> =  1 &lt;&lt; 1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639cc8c74dee3d0b56abf76e3fc7d6e3b43">CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_SIZE_MASK</a> =  0xFF &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c376392014dc1d117edc79dc65e6523f978e45">CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_LOAD</a> =  1 &lt;&lt; 8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639df3379e3b8db5a1b26f9614414594d6f">CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_CACHED</a> =  1 &lt;&lt; 9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c376397590de3d3ae597de222be9a69aa6d9ab">CUPTI_ACTIVITY_FLAG_METRIC_OVERFLOWED</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763976e4f4d5b3b1364bcf8270489ad01b72">CUPTI_ACTIVITY_FLAG_METRIC_VALUE_INVALID</a> =  1 &lt;&lt; 1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639498ee644f34b932babdb8afd382f1031">CUPTI_ACTIVITY_FLAG_INSTRUCTION_VALUE_INVALID</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763981786d0fa620fed299cc99199ad55da9">CUPTI_ACTIVITY_FLAG_INSTRUCTION_CLASS_MASK</a> =  0xFF &lt;&lt; 1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c376398e3cd42d64eeaa1e7247994a4333c4f4">CUPTI_ACTIVITY_FLAG_FLUSH_FORCED</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c3763954fc49d6f1eff82ee2822264bb64bb47">CUPTI_ACTIVITY_FLAG_SHARED_ACCESS_KIND_SIZE_MASK</a> =  0xFF &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639de2d361da014b75fe61816173f161b29">CUPTI_ACTIVITY_FLAG_SHARED_ACCESS_KIND_LOAD</a> =  1 &lt;&lt; 8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639fa320fd74dd5f35a1eb6cffbb96d5f08">CUPTI_ACTIVITY_FLAG_MEMSET_ASYNC</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c37639fb23b459627bb82c9062d27bc86121e0">CUPTI_ACTIVITY_FLAG_THRASHING_IN_CPU</a> =  1 &lt;&lt; 0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggaef8ced52897c4377b0aee6196c376394f3db5910c065411a270f9832d70cb00">CUPTI_ACTIVITY_FLAG_THROTTLING_IN_CPU</a> =  1 &lt;&lt; 0
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags associated with activity records.  <a href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gbb6c00413aa3491f7da9e9cbbecf4133">CUpti_ActivityInstructionClass</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf413369c66577ac6c61ae443998cca46ae6e9">CUPTI_ACTIVITY_INSTRUCTION_CLASS_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf4133d7954a60ae04be8b6e583d345fef83ad">CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_32</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41331d29bad48ff705e330ae564a5af14102">CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_64</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf4133023b6bea22054c06c7371eb1998e8c87">CUPTI_ACTIVITY_INSTRUCTION_CLASS_INTEGER</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41334f9f138df4253fbec73106bb6660c75e">CUPTI_ACTIVITY_INSTRUCTION_CLASS_BIT_CONVERSION</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf4133ac12734ffd94d867cae4c94fba08704a">CUPTI_ACTIVITY_INSTRUCTION_CLASS_CONTROL_FLOW</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41333ac77d9f6306be54d6228f7edc4a6208">CUPTI_ACTIVITY_INSTRUCTION_CLASS_GLOBAL</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf4133a9f84506d4ce98f4c26db01e1dcde05f">CUPTI_ACTIVITY_INSTRUCTION_CLASS_SHARED</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf413364ece2ba214f7dfa3d19e0c8390f153e">CUPTI_ACTIVITY_INSTRUCTION_CLASS_LOCAL</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41335d1ab6cb1a3185e4a4bdf3899eeaafa7">CUPTI_ACTIVITY_INSTRUCTION_CLASS_GENERIC</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf4133bde49bea9638e85637ea8a2d43f1975f">CUPTI_ACTIVITY_INSTRUCTION_CLASS_SURFACE</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41336276d7cc0403fb0d797d7f94a9b2f5c2">CUPTI_ACTIVITY_INSTRUCTION_CLASS_CONSTANT</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf413335ee4b225023125eab1f10d7545a5a5a">CUPTI_ACTIVITY_INSTRUCTION_CLASS_TEXTURE</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41330b79b6df028b26c6c25f92c000895e92">CUPTI_ACTIVITY_INSTRUCTION_CLASS_GLOBAL_ATOMIC</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41333dd52615d531b6b2fc7798facc45902a">CUPTI_ACTIVITY_INSTRUCTION_CLASS_SHARED_ATOMIC</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf413379b7a5ee372195ae9e45c8686f0e394d">CUPTI_ACTIVITY_INSTRUCTION_CLASS_SURFACE_ATOMIC</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41330a8e9652bb787af6887b360d241baf56">CUPTI_ACTIVITY_INSTRUCTION_CLASS_INTER_THREAD_COMMUNICATION</a> =  16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf41335a0c07a63d9ca24a9f23425f6c63acda">CUPTI_ACTIVITY_INSTRUCTION_CLASS_BARRIER</a> =  17, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf413301eaff2bc4644d994215b2e1124552c4">CUPTI_ACTIVITY_INSTRUCTION_CLASS_MISCELLANEOUS</a> =  18, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf413338e269c97a9b7bd7264e7a3746de91cb">CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_16</a> =  19, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggbb6c00413aa3491f7da9e9cbbecf413303a0c6504cb5f5cf3fb358dd45b01fb4">CUPTI_ACTIVITY_INSTRUCTION_CLASS_UNIFORM</a> =  20
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">SASS instruction classification.  <a href="group__CUPTI__ACTIVITY__API.html#gbb6c00413aa3491f7da9e9cbbecf4133">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0367503f3fc0af7b3c5051a9408efa80">CUpti_ActivityJitEntryType</a> { , <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg0367503f3fc0af7b3c5051a9408efa804eb8b2afc6f06f12dc9c9d4f3e846f38">CUPTI_ACTIVITY_JIT_ENTRY_PTX_TO_CUBIN</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg0367503f3fc0af7b3c5051a9408efa808823fecaafeb850ceaba438876005952">CUPTI_ACTIVITY_JIT_ENTRY_NVVM_IR_TO_PTX</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The types of JIT entry.  <a href="group__CUPTI__ACTIVITY__API.html#g0367503f3fc0af7b3c5051a9408efa80">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g2dc4ccb146a37875b3d8564b8100e59a">CUpti_ActivityJitOperationType</a> { , <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg2dc4ccb146a37875b3d8564b8100e59a328630e05a69f381cb3c3a2d1480995f">CUPTI_ACTIVITY_JIT_OPERATION_CACHE_LOAD</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg2dc4ccb146a37875b3d8564b8100e59a2e69624b157ef18daf6670b8531cc13c">CUPTI_ACTIVITY_JIT_OPERATION_CACHE_STORE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg2dc4ccb146a37875b3d8564b8100e59a9917d36d8c47835ca564e4dd79c9e89b">CUPTI_ACTIVITY_JIT_OPERATION_COMPILE</a> =  3
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The types of JIT compilation operations.  <a href="group__CUPTI__ACTIVITY__API.html#g2dc4ccb146a37875b3d8564b8100e59a">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e38cdb3d455f5d74c761d065729278e564">CUPTI_ACTIVITY_KIND_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e39b9fdab31e2ddab02aa765836be54c89">CUPTI_ACTIVITY_KIND_MEMCPY</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3d1e7f3094032b82536f82af699a29c70">CUPTI_ACTIVITY_KIND_MEMSET</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e308040ca72a37cd63bf7e590717ce31c0">CUPTI_ACTIVITY_KIND_KERNEL</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e32adab6350d6daadf8b1e9227dddc176d">CUPTI_ACTIVITY_KIND_DRIVER</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3fca9d0fbaff7ffe0b90f08339d366549">CUPTI_ACTIVITY_KIND_RUNTIME</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3bb664d1fb2726042eec36481fcbdb8f2">CUPTI_ACTIVITY_KIND_EVENT</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3428ef6e0f8b2b41c8b5d6b59b241a38c">CUPTI_ACTIVITY_KIND_METRIC</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3cced468a28d3632fc7bde703a93e1d88">CUPTI_ACTIVITY_KIND_DEVICE</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e390dff26f684f68817b4d99b4b9288dda">CUPTI_ACTIVITY_KIND_CONTEXT</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e355d11bb9d376e95d141ddd13468f0b7a">CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e377da5359610e2caf8a0832959e2d3bf7">CUPTI_ACTIVITY_KIND_NAME</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3a6651558af56e8c679911494734d9d7f">CUPTI_ACTIVITY_KIND_MARKER</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3288c468ae1e204ac839f406dbbaa507c">CUPTI_ACTIVITY_KIND_MARKER_DATA</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3eab8764eb0d81db50890228847ff1128">CUPTI_ACTIVITY_KIND_SOURCE_LOCATOR</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e36c5e42f5070918d147d9b8f4c15302c0">CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e334e3196f630c78f15870381577735069">CUPTI_ACTIVITY_KIND_BRANCH</a> =  16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3139bef6f29a8a4e79d541dd71670413a">CUPTI_ACTIVITY_KIND_OVERHEAD</a> =  17, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e33ca38c360ea18f473013ff1dca2cf354">CUPTI_ACTIVITY_KIND_CDP_KERNEL</a> =  18, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3c7ba07cf57d3a2eacee4223d4dd4c3ab">CUPTI_ACTIVITY_KIND_PREEMPTION</a> =  19, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3f2951c08c789ccd61966404c688905c2">CUPTI_ACTIVITY_KIND_ENVIRONMENT</a> =  20, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3541a8007acdad9959cf4e23d43173a1c">CUPTI_ACTIVITY_KIND_EVENT_INSTANCE</a> =  21, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e37579ea13179193e084f2d1e25cbe839f">CUPTI_ACTIVITY_KIND_MEMCPY2</a> =  22, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e34bc352176dc5687b7165e04ff2c8d4e8">CUPTI_ACTIVITY_KIND_METRIC_INSTANCE</a> =  23, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e337fd58215e526c80cc88e80d64aa3333">CUPTI_ACTIVITY_KIND_INSTRUCTION_EXECUTION</a> =  24, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e318847504bf44386482cb871f49292621">CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER</a> =  25, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e34cfcb61638437470debc278416806bcd">CUPTI_ACTIVITY_KIND_FUNCTION</a> =  26, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e393ff4cf2c1b1c7d0c7273ee27cafcaa8">CUPTI_ACTIVITY_KIND_MODULE</a> =  27, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e34ca74aca624c83d99dc8ce98f6e91f30">CUPTI_ACTIVITY_KIND_DEVICE_ATTRIBUTE</a> =  28, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3726760f4e8b2dbaa2e408a5f36913b12">CUPTI_ACTIVITY_KIND_SHARED_ACCESS</a> =  29, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3a5724fb8f45bfe030a3553dd314d7363">CUPTI_ACTIVITY_KIND_PC_SAMPLING</a> =  30, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3496ce7a481bd3e0cce62325a77cb52d1">CUPTI_ACTIVITY_KIND_PC_SAMPLING_RECORD_INFO</a> =  31, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3554db0163e7cbe4695b71e1da4084560">CUPTI_ACTIVITY_KIND_INSTRUCTION_CORRELATION</a> =  32, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3da3d084df073956a01904c3206663c1c">CUPTI_ACTIVITY_KIND_OPENACC_DATA</a> =  33, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3f2eb19f7ad6e2f213433c01141310f0f">CUPTI_ACTIVITY_KIND_OPENACC_LAUNCH</a> =  34, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3e0f80000acb3ac91d39d17f4d886d833">CUPTI_ACTIVITY_KIND_OPENACC_OTHER</a> =  35, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e337ae649424643f577b4606aa25f551c5">CUPTI_ACTIVITY_KIND_CUDA_EVENT</a> =  36, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e349715578146bfad02f666d5dd34875be">CUPTI_ACTIVITY_KIND_STREAM</a> =  37, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e326584cd85d1c069703320337b400decc">CUPTI_ACTIVITY_KIND_SYNCHRONIZATION</a> =  38, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3b1e47c623392bf8bac53812156659738">CUPTI_ACTIVITY_KIND_EXTERNAL_CORRELATION</a> =  39, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3de82e94ed2b1c83bb9030c247fb36807">CUPTI_ACTIVITY_KIND_NVLINK</a> =  40, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e38a93f9498a95c6c51d0ebde5cc308e3b">CUPTI_ACTIVITY_KIND_INSTANTANEOUS_EVENT</a> =  41, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3740e64c9d40ed8170749616c9325bfc9">CUPTI_ACTIVITY_KIND_INSTANTANEOUS_EVENT_INSTANCE</a> =  42, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3c44636f1c1d0d33b5bdfba2d11bc1694">CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC</a> =  43, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e352790c9f804f2406625e7d3dc57a650e">CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC_INSTANCE</a> =  44, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3fee93ef030088d7a992a1abd6d6e53b0">CUPTI_ACTIVITY_KIND_MEMORY</a> =  45, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3b8459839e64ca4e93e33bafe32448a7c">CUPTI_ACTIVITY_KIND_PCIE</a> =  46, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e30bd244aacfdf212dd6a441a61c2ecc2f">CUPTI_ACTIVITY_KIND_OPENMP</a> =  47, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3b38c32aeca137a0777959055fd22e911">CUPTI_ACTIVITY_KIND_INTERNAL_LAUNCH_API</a> =  48, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3fc702c2e896e657930de3685d4ea3233">CUPTI_ACTIVITY_KIND_MEMORY2</a> =  49, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3083e033a6dbfd50789ab610345c08cf6">CUPTI_ACTIVITY_KIND_MEMORY_POOL</a> =  50, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3547a9c6a0ab9917e2681187d74cc6b14">CUPTI_ACTIVITY_KIND_GRAPH_TRACE</a> =  51, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3aa65dae0c007f2d80813e606bfa8ce69">CUPTI_ACTIVITY_KIND_JIT</a> =  52
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kinds of activity records.  <a href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c">CUpti_ActivityLaunchType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggd054e12847a4a5a0ece53d2cb6dc6d8c21c9328557bdc3c946d11b1a7675a71d">CUPTI_ACTIVITY_LAUNCH_TYPE_REGULAR</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggd054e12847a4a5a0ece53d2cb6dc6d8c5d1f5068218a430756a163dbcc1bb3e0">CUPTI_ACTIVITY_LAUNCH_TYPE_COOPERATIVE_SINGLE_DEVICE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggd054e12847a4a5a0ece53d2cb6dc6d8cea6403588743260fafe7f3cfe573819a">CUPTI_ACTIVITY_LAUNCH_TYPE_COOPERATIVE_MULTI_DEVICE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggd054e12847a4a5a0ece53d2cb6dc6d8c531975c9016f9dfa21c657d8e44742d6">CUPTI_ACTIVITY_LAUNCH_TYPE_CBL_COMMANDLIST</a> =  3
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The type of the CUDA kernel launch.  <a href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661">CUpti_ActivityMemcpyKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661a1ab03ae9b14e757fe458db8f72864d4">CUPTI_ACTIVITY_MEMCPY_KIND_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661a6da0b06091d2d92c6eadf0492bfe9ab">CUPTI_ACTIVITY_MEMCPY_KIND_HTOD</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661ef545ba0bb77911dc948c0387bcbd707">CUPTI_ACTIVITY_MEMCPY_KIND_DTOH</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661b2599683471ac60b7650ffc008c08998">CUPTI_ACTIVITY_MEMCPY_KIND_HTOA</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a366142afec25f21cba3421f9b9fff1b63180">CUPTI_ACTIVITY_MEMCPY_KIND_ATOH</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661d25c1f160462df8798bc80b4bc149b10">CUPTI_ACTIVITY_MEMCPY_KIND_ATOA</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661f00443a5ed3e12a3f51bc8168c2e5b72">CUPTI_ACTIVITY_MEMCPY_KIND_ATOD</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661c3483eaa97fff7a411988346327a69e9">CUPTI_ACTIVITY_MEMCPY_KIND_DTOA</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a36617964cc727c860bbfe24527e7407cf0ad">CUPTI_ACTIVITY_MEMCPY_KIND_DTOD</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661c0d8fd604c3fe9b21474c3e4d184fb31">CUPTI_ACTIVITY_MEMCPY_KIND_HTOH</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10056d66c2ee966fc5cde439eb0a3661dd1465d676763b5eed70b40ed78f9d11">CUPTI_ACTIVITY_MEMCPY_KIND_PTOP</a> =  10
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kind of a memory copy, indicating the source and destination targets of the copy.  <a href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bc936e97ceb6f0e6675192a6b3ae0a2806">CUPTI_ACTIVITY_MEMORY_KIND_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bcd15f71ae8aabd1e0bd240ec2684ca6a8">CUPTI_ACTIVITY_MEMORY_KIND_PAGEABLE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bc9fc7a14791c26dc66146df9dd769b81d">CUPTI_ACTIVITY_MEMORY_KIND_PINNED</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bc8303e9fe524dfdba14dabf9d097c3a66">CUPTI_ACTIVITY_MEMORY_KIND_DEVICE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bca24f0ba883d51eb4b35a12b70f014347">CUPTI_ACTIVITY_MEMORY_KIND_ARRAY</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bc21ab1d11996502d5cf0abb895d621c82">CUPTI_ACTIVITY_MEMORY_KIND_MANAGED</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bc1f1782ef45cf76205ded5818c621f1c2">CUPTI_ACTIVITY_MEMORY_KIND_DEVICE_STATIC</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9969b86f0e54989b27080dc6083263bcc3954098a2d6fb407fbd4f3267213b8c">CUPTI_ACTIVITY_MEMORY_KIND_MANAGED_STATIC</a> =  7
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kinds of memory accessed by a memory operation/copy.  <a href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg6404b89cfbbd60e04204244d230b15c451740ba2573be3e95f801d5a74852872">CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg6404b89cfbbd60e04204244d230b15c40693280cc92c31000e479c10c36f046c">CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_ALLOCATION</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg6404b89cfbbd60e04204244d230b15c433546fcb0556ba361b7d8a3a1b8f8664">CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_RELEASE</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Memory operation types.  <a href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg878fb2c94e0051169fdf0ca7982612a267ac93f4d2f869d52c287374f0f91b54">CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg878fb2c94e0051169fdf0ca7982612a2a1c55e97b82416e1c3eb43a583e94118">CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_CREATED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg878fb2c94e0051169fdf0ca7982612a2c52f1aa08afe1875891682a148bdfe27">CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_DESTROYED</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg878fb2c94e0051169fdf0ca7982612a2bf9f79a9d787c6be5965366672d6ff12">CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_TRIMMED</a> =  3
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Memory pool operation types.  <a href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg8c40b23a5fe82862b18d60c5c42399a88ff8e248e56bc838617586e91d01cd51">CUPTI_ACTIVITY_MEMORY_POOL_TYPE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg8c40b23a5fe82862b18d60c5c42399a842fb40627946b24dad67c11a1ac7de2a">CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg8c40b23a5fe82862b18d60c5c42399a8aa132b412c76db4ab0a174db4108d13e">CUPTI_ACTIVITY_MEMORY_POOL_TYPE_IMPORTED</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Memory pool types.  <a href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9b6136c1123883722ded735eb52cf2701c4785a6ac2acff605a9567a03df2f04">CUPTI_ACTIVITY_OBJECT_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9b6136c1123883722ded735eb52cf270c3895b1c8a5d791685cc4874ecd9d4fe">CUPTI_ACTIVITY_OBJECT_PROCESS</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9b6136c1123883722ded735eb52cf270defb468f187cc200521fb30fcf57c7ac">CUPTI_ACTIVITY_OBJECT_THREAD</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9b6136c1123883722ded735eb52cf270af5b89eca120e06d8fa4db1848bc4ad1">CUPTI_ACTIVITY_OBJECT_DEVICE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9b6136c1123883722ded735eb52cf2707e7a1eb1d58672eb3d67d0838cf6dc5c">CUPTI_ACTIVITY_OBJECT_CONTEXT</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9b6136c1123883722ded735eb52cf270f7a19f33ba61f622efc8721ea7628590">CUPTI_ACTIVITY_OBJECT_STREAM</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kinds of activity objects.  <a href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcc8863d79c939f6bb57324db4503b265">CUpti_ActivityOverheadKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcc8863d79c939f6bb57324db4503b265debf137c41c5cd4f78d625e9a96143d0">CUPTI_ACTIVITY_OVERHEAD_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcc8863d79c939f6bb57324db4503b2650f8237ff52b16444e23b0f4fbe321c9c">CUPTI_ACTIVITY_OVERHEAD_DRIVER_COMPILER</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcc8863d79c939f6bb57324db4503b265e1d3c3fc20e50bc7f309c4c5a3bf63f5">CUPTI_ACTIVITY_OVERHEAD_CUPTI_BUFFER_FLUSH</a> =  1&lt;&lt;16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcc8863d79c939f6bb57324db4503b26520afe08544af18668c8433fa61781083">CUPTI_ACTIVITY_OVERHEAD_CUPTI_INSTRUMENTATION</a> =  2&lt;&lt;16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcc8863d79c939f6bb57324db4503b2659eb4b20e1813e34e711a3506956c0638">CUPTI_ACTIVITY_OVERHEAD_CUPTI_RESOURCE</a> =  3&lt;&lt;16
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kinds of activity overhead.  <a href="group__CUPTI__ACTIVITY__API.html#gcc8863d79c939f6bb57324db4503b265">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9d3467131ce6c87aa85f3a5a43f834842d7bdc24c11fd2183cd7a72d6723e202">CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9d3467131ce6c87aa85f3a5a43f83484464d34d766c2215a7793fb5a80d5f07c">CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_NOT_SUPPORTED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9d3467131ce6c87aa85f3a5a43f83484d28746ef8a7578e4ff185dfbaf6fe65f">CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_OFF</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9d3467131ce6c87aa85f3a5a43f8348430014ec479ce99cb07a212b02eba6ce8">CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_ON</a> =  3
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Partitioned global caching option.  <a href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#ga51d59f0407cce71516a53875bf825fe">CUpti_ActivityPCSamplingPeriod</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gga51d59f0407cce71516a53875bf825fee6fac4282894adedef77eae540af2cc7">CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gga51d59f0407cce71516a53875bf825fe8c3d1b4457ddf0c9d071f7d2060fcf8c">CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MIN</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gga51d59f0407cce71516a53875bf825fec1fccce5da5597c9eb416f67ac180cf1">CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_LOW</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gga51d59f0407cce71516a53875bf825fe3b0a60268a0c62ccbf9a7a290809b0c3">CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MID</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gga51d59f0407cce71516a53875bf825fe56be90f1308ca0ca961f8e5a60804779">CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_HIGH</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gga51d59f0407cce71516a53875bf825fe993553d38db8c1fb797807645efbef40">CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MAX</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Sampling period for PC sampling method.  <a href="group__CUPTI__ACTIVITY__API.html#ga51d59f0407cce71516a53875bf825fe">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d6ac7664868ca4a2bbaa461e17f848828">CUPTI_ACTIVITY_PC_SAMPLING_STALL_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d383ee917a199aa0f4519406a2b102bf8">CUPTI_ACTIVITY_PC_SAMPLING_STALL_NONE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d2ffdb23cfecebb1e1d24b5e0420c705f">CUPTI_ACTIVITY_PC_SAMPLING_STALL_INST_FETCH</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12dd753605c5a160e267bc0937d4d4f1044">CUPTI_ACTIVITY_PC_SAMPLING_STALL_EXEC_DEPENDENCY</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d2f084ea3a334985840c088f889e634d2">CUPTI_ACTIVITY_PC_SAMPLING_STALL_MEMORY_DEPENDENCY</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d3e22171773f0a899eb8a42b7c5af7700">CUPTI_ACTIVITY_PC_SAMPLING_STALL_TEXTURE</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d65934d7473dbad97a5cac997ce2fad7b">CUPTI_ACTIVITY_PC_SAMPLING_STALL_SYNC</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d92e0a26e16f508e35e1af4965e87fa9e">CUPTI_ACTIVITY_PC_SAMPLING_STALL_CONSTANT_MEMORY_DEPENDENCY</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d783f30f4ea4230ab9ca4202771315e80">CUPTI_ACTIVITY_PC_SAMPLING_STALL_PIPE_BUSY</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d6f56d940fefbd4bdbf348982a7293ff8">CUPTI_ACTIVITY_PC_SAMPLING_STALL_MEMORY_THROTTLE</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12d0f753c861de0d226e9392c5eb16605de">CUPTI_ACTIVITY_PC_SAMPLING_STALL_NOT_SELECTED</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12dfb5b3327f4380c5e4231dea600b6eb45">CUPTI_ACTIVITY_PC_SAMPLING_STALL_OTHER</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg57bafb9baafeae0880dae6eaa1a8e12df15199d80a762688794ecdcd6c41ca0d">CUPTI_ACTIVITY_PC_SAMPLING_STALL_SLEEPING</a> =  12
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The stall reason for PC sampling activity.  <a href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g7609527c3c6c122a8bb5b1e5d1ae1c4a">CUpti_ActivityPreemptionKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg7609527c3c6c122a8bb5b1e5d1ae1c4afff0a18953bcb6f6133ff94f85af73e2">CUPTI_ACTIVITY_PREEMPTION_KIND_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg7609527c3c6c122a8bb5b1e5d1ae1c4acea6a07f2e98653a0c581c8e6b8f12ba">CUPTI_ACTIVITY_PREEMPTION_KIND_SAVE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg7609527c3c6c122a8bb5b1e5d1ae1c4a6f0ac3503f46be050347060d14a154d2">CUPTI_ACTIVITY_PREEMPTION_KIND_RESTORE</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kind of a preemption activity.  <a href="group__CUPTI__ACTIVITY__API.html#g7609527c3c6c122a8bb5b1e5d1ae1c4a">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10b985831ad4db3bd401953e999c877a">CUpti_ActivityStreamFlag</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10b985831ad4db3bd401953e999c877a941ba0264440bca0b7e4414410e0cc07">CUPTI_ACTIVITY_STREAM_CREATE_FLAG_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10b985831ad4db3bd401953e999c877a209125987238fb9340a8ade23f912d43">CUPTI_ACTIVITY_STREAM_CREATE_FLAG_DEFAULT</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10b985831ad4db3bd401953e999c877aa1dd7a9e2e04e1ee7987a4ff6eb48cff">CUPTI_ACTIVITY_STREAM_CREATE_FLAG_NON_BLOCKING</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10b985831ad4db3bd401953e999c877a6a2dc494836887e9b09193f95d1d2a58">CUPTI_ACTIVITY_STREAM_CREATE_FLAG_NULL</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg10b985831ad4db3bd401953e999c877a7442c91de69f89372575011031bbd322">CUPTI_ACTIVITY_STREAM_CREATE_MASK</a> =  0xFFFF
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">stream type.  <a href="group__CUPTI__ACTIVITY__API.html#g10b985831ad4db3bd401953e999c877a">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g80e1eb47615e31021f574df8ebbe5d9a">CUpti_ActivitySynchronizationType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg80e1eb47615e31021f574df8ebbe5d9a0498f57ab29d3b29a99147e25bee1f85">CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg80e1eb47615e31021f574df8ebbe5d9adc8d6d127acb840d44e8184078e26dbd">CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_EVENT_SYNCHRONIZE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg80e1eb47615e31021f574df8ebbe5d9a0ff3988e29580f6fc4e463a9da776588">CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_STREAM_WAIT_EVENT</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg80e1eb47615e31021f574df8ebbe5d9add24d6847b9a948156064328912cc109">CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_STREAM_SYNCHRONIZE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg80e1eb47615e31021f574df8ebbe5d9a26b796c4c7eabdab1e0ad8896b34dd68">CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_CONTEXT_SYNCHRONIZE</a> =  4
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Synchronization type.  <a href="group__CUPTI__ACTIVITY__API.html#g80e1eb47615e31021f574df8ebbe5d9a">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">CUpti_ActivityThreadIdType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc6fcebeb84a89d8f1862d31338efd4c55f5057868d612674b75b4071567ed4d7">CUPTI_ACTIVITY_THREAD_ID_TYPE_DEFAULT</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc6fcebeb84a89d8f1862d31338efd4c5d78df9968546a822f0178712c86b36f9">CUPTI_ACTIVITY_THREAD_ID_TYPE_SYSTEM</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Thread-Id types.  <a href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g58bc092ea88426687492fdea7b1c0ff3">CUpti_ActivityUnifiedMemoryAccessType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg58bc092ea88426687492fdea7b1c0ff3bd4a26c06d8ceef0e5d2f8d3a0827fbe">CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg58bc092ea88426687492fdea7b1c0ff3999bc60af1f9f7fe5a1d6299e20c8181">CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_READ</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg58bc092ea88426687492fdea7b1c0ff3d81e561fa074e610ddbaa5567ee4162d">CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_WRITE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg58bc092ea88426687492fdea7b1c0ff3569d2c161c404576bda5f42aa7f52b24">CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_ATOMIC</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg58bc092ea88426687492fdea7b1c0ff3624b140a60e22a6b0f172aad9a77ee90">CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_PREFETCH</a> =  4
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Memory access type for unified memory page faults.  <a href="group__CUPTI__ACTIVITY__API.html#g58bc092ea88426687492fdea7b1c0ff3">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa7820bf7f03dd426a7768ae72a6f911992a7">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa7827c4755228387e83351ae02b5ea5157f1">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa782ef4bdc9da8f158bf337bdf2aedd8d780">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOH</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa78294faffc246c392748ab7f0de9a858b22">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa78249ecdbc6d0c9b52829c8bc276932b7bf">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa7827ae56c5c14c999bb8977ebc273161c6a">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa7822275e1bca9aaf222a491c2599cdbca96">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa782d6e6079c49b8024aab5afd22c3ec1706">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa7824fe8185509ddecb727197de672cb6e2b">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOD</a> =  8
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Kind of the Unified Memory counter.  <a href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">CUpti_ActivityUnifiedMemoryCounterScope</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcf829db187f1553461cea1a6c9e6748bed53921f81c92d0688295502be796844">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcf829db187f1553461cea1a6c9e6748becdb22d1f83773f3a24801f679359cb3">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_PROCESS_SINGLE_DEVICE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggcf829db187f1553461cea1a6c9e6748b3ba7e53206f5c3cdc85e1c8049df555c">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_PROCESS_ALL_DEVICES</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Scope of the unified memory counter (deprecated in CUDA 7.0).  <a href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gc5e53db4204f170a6c31e520e080c1fc">CUpti_ActivityUnifiedMemoryMigrationCause</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc5e53db4204f170a6c31e520e080c1fceaf33c52957361f6988fcba202469e9c">CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc5e53db4204f170a6c31e520e080c1fc72842be49a8798e8f954ca353545b794">CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_USER</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc5e53db4204f170a6c31e520e080c1fcd6ef5866188f26c40cee666b7c5faf7b">CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_COHERENCE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc5e53db4204f170a6c31e520e080c1fca8ea9335e1e2f15d31ba4cf0f5a82722">CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_PREFETCH</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc5e53db4204f170a6c31e520e080c1fcf912bc57c1a6ac6ab5c60e3b7b237abf">CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_EVICTION</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc5e53db4204f170a6c31e520e080c1fcaccf7ec09f1ac3e95108521f590f9c60">CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_ACCESS_COUNTERS</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Migration cause of the Unified Memory counter.  <a href="group__CUPTI__ACTIVITY__API.html#gc5e53db4204f170a6c31e520e080c1fc">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gc38ad61408d643a789562314cd18d6b7">CUpti_ActivityUnifiedMemoryRemoteMapCause</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc38ad61408d643a789562314cd18d6b728dc6efe39dac1d16b12b28a74c5b1e1">CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc38ad61408d643a789562314cd18d6b7a78d260cad8ee26c26d32685c418fc17">CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_COHERENCE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc38ad61408d643a789562314cd18d6b7c25a1ba662111af949f5f0df78f09f1e">CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_THRASHING</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc38ad61408d643a789562314cd18d6b79b2a1361c4b217135b45d1bae02f9b92">CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_POLICY</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc38ad61408d643a789562314cd18d6b7dab6db9f2025900838524e69d5a563a1">CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_OUT_OF_MEMORY</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggc38ad61408d643a789562314cd18d6b71aa58222d3350f3d50d58f8f6ad18308">CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_EVICTION</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Remote memory map cause of the Unified Memory counter.  <a href="group__CUPTI__ACTIVITY__API.html#gc38ad61408d643a789562314cd18d6b7">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g5ec1eab2a306637e8d49bcf8a678cc40">CUpti_DeviceVirtualizationMode</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg5ec1eab2a306637e8d49bcf8a678cc40fe6083edd2d97ae3328fcf166317993e">CUPTI_DEVICE_VIRTUALIZATION_MODE_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg5ec1eab2a306637e8d49bcf8a678cc404649872b3b366bc04543d836725b46ca">CUPTI_DEVICE_VIRTUALIZATION_MODE_PASS_THROUGH</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg5ec1eab2a306637e8d49bcf8a678cc403d669bb81c0c454eb9cd347a725f6bf8">CUPTI_DEVICE_VIRTUALIZATION_MODE_VIRTUAL_GPU</a> =  2
<br>
 }</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> { , <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggd67907716d28ba51253f08d3e0bd0dda97d7ef27e4e54f6a72f558e34caff65d">CUPTI_DEV_TYPE_GPU</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#ggd67907716d28ba51253f08d3e0bd0ddac666a1bd36bb23d1dace0a85af640554">CUPTI_DEV_TYPE_NPU</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The device type for device connected to NVLink.  <a href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g3dcc6c3e9409a1183932febfdde58173">CUpti_EnvironmentClocksThrottleReason</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3dcc6c3e9409a1183932febfdde58173e63f422f0ff685c257189084a31fdb40">CUPTI_CLOCKS_THROTTLE_REASON_GPU_IDLE</a> =  0x00000001, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3dcc6c3e9409a1183932febfdde58173413948508232d2a798f77e01a065b6c1">CUPTI_CLOCKS_THROTTLE_REASON_USER_DEFINED_CLOCKS</a> =  0x00000002, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3dcc6c3e9409a1183932febfdde58173b102e3934593d0af7653bf24b9407b1c">CUPTI_CLOCKS_THROTTLE_REASON_SW_POWER_CAP</a> =  0x00000004, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3dcc6c3e9409a1183932febfdde5817349ee33b3ea489df857e1ff14973d3319">CUPTI_CLOCKS_THROTTLE_REASON_HW_SLOWDOWN</a> =  0x00000008, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3dcc6c3e9409a1183932febfdde58173234a50aea43864aac11006ad86c4a22b">CUPTI_CLOCKS_THROTTLE_REASON_UNKNOWN</a> =  0x80000000, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3dcc6c3e9409a1183932febfdde5817344d0f920914e72d842ed2d2d88ab5492">CUPTI_CLOCKS_THROTTLE_REASON_UNSUPPORTED</a> =  0x40000000, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3dcc6c3e9409a1183932febfdde58173b2eeca89cc2fe97895bae2047adc17f5">CUPTI_CLOCKS_THROTTLE_REASON_NONE</a> =  0x00000000
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Reasons for clock throttling.  <a href="group__CUPTI__ACTIVITY__API.html#g3dcc6c3e9409a1183932febfdde58173">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">CUpti_ExternalCorrelationKind</a> { , <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9ac4ae6e6237e99db3f8b4c66df2f9aaef14617c7678231aa5b32e85d23acb8a">CUPTI_EXTERNAL_CORRELATION_KIND_UNKNOWN</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9ac4ae6e6237e99db3f8b4c66df2f9aa532f4536a1934e19424c7b9401290326">CUPTI_EXTERNAL_CORRELATION_KIND_OPENACC</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9ac4ae6e6237e99db3f8b4c66df2f9aabcdb78368dd97101f04b43f6f8c21109">CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM0</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9ac4ae6e6237e99db3f8b4c66df2f9aa518b903d84e83f8d7229494931b3cc1a">CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM1</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9ac4ae6e6237e99db3f8b4c66df2f9aa8f3d574b27a8636def43becc25ce2a0d">CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM2</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg9ac4ae6e6237e99db3f8b4c66df2f9aac068de68a0f566e64a4954db5afe4b85">CUPTI_EXTERNAL_CORRELATION_KIND_SIZE</a>
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The kind of external APIs supported for correlation.  <a href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg6662b8786c121eed35068b4c1cc931ab225c2c7345a27de6e296fae703992c2f">CUPTI_FUNC_SHMEM_LIMIT_DEFAULT</a> =  0x00, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg6662b8786c121eed35068b4c1cc931ab5602c54f716f2fa86107e91e592329d7">CUPTI_FUNC_SHMEM_LIMIT_OPTIN</a> =  0x01
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The shared memory limit per block config for a kernel This should be used to set 'cudaOccFuncShmemConfig' field in occupancy calculator API.  <a href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g819d686d23d2a3dfed9cc1d3c4ecb162">CUpti_LinkFlag</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg819d686d23d2a3dfed9cc1d3c4ecb1621c1b0bc894a29b98d0700b5eb0bc6cfc">CUPTI_LINK_FLAG_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg819d686d23d2a3dfed9cc1d3c4ecb16250a2b8823e88498e98c9f8ee7f4563a8">CUPTI_LINK_FLAG_PEER_ACCESS</a> =  (1 &lt;&lt; 1), 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg819d686d23d2a3dfed9cc1d3c4ecb1622be6a457d2e2bca9839f4e15b8d93651">CUPTI_LINK_FLAG_SYSMEM_ACCESS</a> =  (1 &lt;&lt; 2), 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg819d686d23d2a3dfed9cc1d3c4ecb1627c94f9434f034cb76be1cbf078d20340">CUPTI_LINK_FLAG_PEER_ATOMICS</a> =  (1 &lt;&lt; 3), 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg819d686d23d2a3dfed9cc1d3c4ecb1624760f46d7c14118420c5a608532553db">CUPTI_LINK_FLAG_SYSMEM_ATOMICS</a> =  (1 &lt;&lt; 4)
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Link flags.  <a href="group__CUPTI__ACTIVITY__API.html#g819d686d23d2a3dfed9cc1d3c4ecb162">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e">CUpti_OpenAccConstructKind</a> </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The OpenAcc parent construct kind for OpenAcc activity records. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a> </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The OpenAcc event kind for OpenAcc activity records.  <a href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g21881d93eb4622a660f0ebd8e2fd2bab">CUpti_PcieDeviceType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg21881d93eb4622a660f0ebd8e2fd2bab41ddedbc6b2ebbf19911530148f2416d">CUPTI_PCIE_DEVICE_TYPE_GPU</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg21881d93eb4622a660f0ebd8e2fd2bab95dbeeedd98f35e0987137bdabfad0cd">CUPTI_PCIE_DEVICE_TYPE_BRIDGE</a> =  1
<br>
 }</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g3cc86677bf301c75dc24ed2adef85145">CUpti_PcieGen</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3cc86677bf301c75dc24ed2adef851453f2e4f0d700c47cc44959cb6b05ae3c1">CUPTI_PCIE_GEN_GEN1</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3cc86677bf301c75dc24ed2adef851450c3d2f67c924c6067ace618782d59998">CUPTI_PCIE_GEN_GEN2</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3cc86677bf301c75dc24ed2adef85145004e58ac7e3b682963a133293850cce2">CUPTI_PCIE_GEN_GEN3</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3cc86677bf301c75dc24ed2adef85145d7bd860d830ab2dc50a1b31453f8184c">CUPTI_PCIE_GEN_GEN4</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#gg3cc86677bf301c75dc24ed2adef85145d91af313c241350864584e7a9289c471">CUPTI_PCIE_GEN_GEN5</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">PCIE Generation.  <a href="group__CUPTI__ACTIVITY__API.html#g3cc86677bf301c75dc24ed2adef85145">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g115305aadc838df99d88283fc64c4317">cuptiActivityConfigurePCSampling</a> (CUcontext ctx, <a class="el" href="structCUpti__ActivityPCSamplingConfig.html">CUpti_ActivityPCSamplingConfig</a> *config)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set PC sampling configuration.  <a href="#g115305aadc838df99d88283fc64c4317"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8ec9b1229ba07a98aac9db4600d4325c">cuptiActivityConfigureUnifiedMemoryCounter</a> (<a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html">CUpti_ActivityUnifiedMemoryCounterConfig</a> *config, uint32_t count)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set Unified Memory Counter configuration.  <a href="#g8ec9b1229ba07a98aac9db4600d4325c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gbc086f5a89450f4e3b75c6f6832f569c">cuptiActivityDisable</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> kind)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Disable collection of a specific kind of activity record.  <a href="#gbc086f5a89450f4e3b75c6f6832f569c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g90d628b01bcc8044b86060e8a1ff7457">cuptiActivityDisableContext</a> (CUcontext context, <a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> kind)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Disable collection of a specific kind of activity record for a context.  <a href="#g90d628b01bcc8044b86060e8a1ff7457"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g348cf81393b39ab2f89604aaaa8defc2">cuptiActivityEnable</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> kind)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable collection of a specific kind of activity record.  <a href="#g348cf81393b39ab2f89604aaaa8defc2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g12080fe6fdacf80869db472e41b98027">cuptiActivityEnableAndDump</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> kind)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable collection of a specific kind of activity record. For certain activity kinds it dumps existing records.  <a href="#g12080fe6fdacf80869db472e41b98027"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g2e2ee46c089fa139481e434f1b0f4cfc">cuptiActivityEnableContext</a> (CUcontext context, <a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> kind)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable collection of a specific kind of activity record for a context.  <a href="#g2e2ee46c089fa139481e434f1b0f4cfc"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps</a> (uint8_t enable)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Controls the collection of queued and submitted timestamps for kernels.  <a href="#g4b50c0c1634913f8157cfcfe84f19fa9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g5fc8cdb0ce9c2cb08a990d26c700f969">cuptiActivityEnableLaunchAttributes</a> (uint8_t enable)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Controls the collection of launch attributes for kernels.  <a href="#g5fc8cdb0ce9c2cb08a990d26c700f969"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g805065d34981e9284b9943e753b20122">cuptiActivityFlush</a> (CUcontext context, uint32_t streamId, uint32_t flag)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Wait for all activity records to be delivered via the completion callback.  <a href="#g805065d34981e9284b9943e753b20122"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gbae7160b2db7e97247a0d23812e373eb">cuptiActivityFlushAll</a> (uint32_t flag)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Request to deliver activity records via the buffer completion callback.  <a href="#gbae7160b2db7e97247a0d23812e373eb"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gded430c34c6b7dbf935377c65c7d6491">cuptiActivityFlushPeriod</a> (uint32_t time)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Sets the flush period for the worker thread.  <a href="#gded430c34c6b7dbf935377c65c7d6491"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g92859af3b4bc569577d386dea71404b2">cuptiActivityGetAttribute</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#g1c31fe3f8ea0e46c6c20dd454a6caab6">CUpti_ActivityAttribute</a> attr, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read an activity API attribute.  <a href="#g92859af3b4bc569577d386dea71404b2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gb397f490a0df4a1633ea7b6e2420294f">cuptiActivityGetNextRecord</a> (uint8_t *buffer, size_t validBufferSizeBytes, <a class="el" href="structCUpti__Activity.html">CUpti_Activity</a> **record)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Iterate over the activity records in a buffer.  <a href="#gb397f490a0df4a1633ea7b6e2420294f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g496d13eb1f4f4fcce37ce3f3434c5e4a">cuptiActivityGetNumDroppedRecords</a> (CUcontext context, uint32_t streamId, size_t *dropped)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the number of activity records that were dropped of insufficient buffer space.  <a href="#g496d13eb1f4f4fcce37ce3f3434c5e4a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g47395bf12ff55f30822d408b940567e3">cuptiActivityPopExternalCorrelationId</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">CUpti_ExternalCorrelationKind</a> kind, uint64_t *lastId)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Pop an external correlation id for the calling thread.  <a href="#g47395bf12ff55f30822d408b940567e3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g2c373f1be967db0227fa4d42a593d1a0">cuptiActivityPushExternalCorrelationId</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">CUpti_ExternalCorrelationKind</a> kind, uint64_t id)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Push an external correlation id for the calling thread.  <a href="#g2c373f1be967db0227fa4d42a593d1a0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g237e2401b0ce69dcb265b1f9079f0b65">cuptiActivityRegisterCallbacks</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#g3337d3cbfe624b05d4af435fd17e5b28">CUpti_BuffersCallbackRequestFunc</a> funcBufferRequested, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g081fec67efc2c2504362e8e34fce7e6e">CUpti_BuffersCallbackCompleteFunc</a> funcBufferCompleted)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Registers callback functions with CUPTI for activity buffer handling.  <a href="#g237e2401b0ce69dcb265b1f9079f0b65"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gc168c4a9b5f4e9bd7553ca26118a307d">cuptiActivityRegisterTimestampCallback</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#gdd850477a3ae876889eb8dae31c8c664">CUpti_TimestampCallbackFunc</a> funcTimestamp)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Registers callback function with CUPTI for providing timestamp.  <a href="#gc168c4a9b5f4e9bd7553ca26118a307d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#ga4ce1e3f22626c5c2a8b450a996fe580">cuptiActivitySetAttribute</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#g1c31fe3f8ea0e46c6c20dd454a6caab6">CUpti_ActivityAttribute</a> attr, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Write an activity API attribute.  <a href="#ga4ce1e3f22626c5c2a8b450a996fe580"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g22c5ce610ffbf5940b7c05be54fc813d">cuptiComputeCapabilitySupported</a> (int major, int minor, int *support)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Check support for a compute capability.  <a href="#g22c5ce610ffbf5940b7c05be54fc813d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g2493c952b9ceccf953ade5a6816fefdb">cuptiDeviceSupported</a> (CUdevice dev, int *support)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Check support for a compute device.  <a href="#g2493c952b9ceccf953ade5a6816fefdb"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g395c59b62aeac395e38ced9d40677c76">cuptiDeviceVirtualizationMode</a> (CUdevice dev, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g5ec1eab2a306637e8d49bcf8a678cc40">CUpti_DeviceVirtualizationMode</a> *mode)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Query the virtualization mode of the device.  <a href="#g395c59b62aeac395e38ced9d40677c76"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1be905ea718ed54246e52e02667e8f">cuptiFinalize</a> (void)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Detach CUPTI from the running process.  <a href="#gad1be905ea718ed54246e52e02667e8f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g1ac1cce5ce788b9f2c679d13e982384b">cuptiGetAutoBoostState</a> (CUcontext context, <a class="el" href="structCUpti__ActivityAutoBoostState.html">CUpti_ActivityAutoBoostState</a> *state)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get auto boost state.  <a href="#g1ac1cce5ce788b9f2c679d13e982384b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g036dfd802a6c28c7e4239c82ed98df21">cuptiGetContextId</a> (CUcontext context, uint32_t *contextId)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the ID of a context.  <a href="#g036dfd802a6c28c7e4239c82ed98df21"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0cc36b42dbf08fffc772e9c932749c77">cuptiGetDeviceId</a> (CUcontext context, uint32_t *deviceId)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the ID of a device.  <a href="#g0cc36b42dbf08fffc772e9c932749c77"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g4add923efce4731de28c9f0b04e1e3f9">cuptiGetGraphId</a> (CUgraph graph, uint32_t *pId)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the unique ID of graph.  <a href="#g4add923efce4731de28c9f0b04e1e3f9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g22370b53102428305a97cb37fbc14678">cuptiGetGraphNodeId</a> (CUgraphNode node, uint64_t *nodeId)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the unique ID of a graph node.  <a href="#g22370b53102428305a97cb37fbc14678"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0c83719b0248e09ef94390000d3f1035">cuptiGetLastError</a> (void)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the last error from a cupti call or callback.  <a href="#g0c83719b0248e09ef94390000d3f1035"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g04ece23d24e29e8d98daadba09f1839c">cuptiGetStreamId</a> (CUcontext context, CUstream stream, uint32_t *streamId)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the ID of a stream.  <a href="#g04ece23d24e29e8d98daadba09f1839c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g062d04c62fdfeed9adb8157cecbaaa55">cuptiGetStreamIdEx</a> (CUcontext context, CUstream stream, uint8_t perThreadStream, uint32_t *streamId)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the ID of a stream.  <a href="#g062d04c62fdfeed9adb8157cecbaaa55"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gbc957f426b741e46d6e9a99a43a974b5">cuptiGetThreadIdType</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">CUpti_ActivityThreadIdType</a> *type)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the thread-id type.  <a href="#gbc957f426b741e46d6e9a99a43a974b5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g7d8294c686b5293237a6daae8eae3dde">cuptiGetTimestamp</a> (uint64_t *timestamp)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the CUPTI timestamp.  <a href="#g7d8294c686b5293237a6daae8eae3dde"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g1821f090b841d60643ee37d977d9c64a">cuptiSetThreadIdType</a> (<a class="el" href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">CUpti_ActivityThreadIdType</a> type)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set the thread-id type.  <a href="#g1821f090b841d60643ee37d977d9c64a"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI Activity API. <hr><h2>Define Documentation</h2>
<a class="anchor" name="g43ce5c40a7db28eba98f969fd830dc76"></a><!-- doxytag: member="cupti_activity.h::CUPTI_AUTO_BOOST_INVALID_CLIENT_PID" ref="g43ce5c40a7db28eba98f969fd830dc76" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_AUTO_BOOST_INVALID_CLIENT_PID&nbsp;&nbsp;&nbsp;0          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An invalid/unknown process id. 
</div>
</div><p>
<a class="anchor" name="g97e26d46f328081037f013942dbb82c5"></a><!-- doxytag: member="cupti_activity.h::CUPTI_CORRELATION_ID_UNKNOWN" ref="g97e26d46f328081037f013942dbb82c5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_CORRELATION_ID_UNKNOWN&nbsp;&nbsp;&nbsp;0          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An invalid/unknown correlation ID. A correlation ID of this value indicates that there is no correlation for the activity record. 
</div>
</div><p>
<a class="anchor" name="g3877af5383ad7f9b89aa38d1ae137b0c"></a><!-- doxytag: member="cupti_activity.h::CUPTI_FUNCTION_INDEX_ID_INVALID" ref="g3877af5383ad7f9b89aa38d1ae137b0c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_FUNCTION_INDEX_ID_INVALID&nbsp;&nbsp;&nbsp;0          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An invalid function index ID. 
</div>
</div><p>
<a class="anchor" name="g55b5747de740c3daaa3aa63650eb654e"></a><!-- doxytag: member="cupti_activity.h::CUPTI_GRID_ID_UNKNOWN" ref="g55b5747de740c3daaa3aa63650eb654e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_GRID_ID_UNKNOWN&nbsp;&nbsp;&nbsp;0LL          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An invalid/unknown grid ID. 
</div>
</div><p>
<a class="anchor" name="g9b7eced29b14203cc46f425adc1471a6"></a><!-- doxytag: member="cupti_activity.h::CUPTI_MAX_NVLINK_PORTS" ref="g9b7eced29b14203cc46f425adc1471a6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_MAX_NVLINK_PORTS&nbsp;&nbsp;&nbsp;32          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum NVLink port numbers. 
</div>
</div><p>
<a class="anchor" name="gec9d5c6508090f41530b7a2e0e68b3b4"></a><!-- doxytag: member="cupti_activity.h::CUPTI_NVLINK_INVALID_PORT" ref="gec9d5c6508090f41530b7a2e0e68b3b4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_NVLINK_INVALID_PORT&nbsp;&nbsp;&nbsp;-1          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Invalid/unknown NVLink port number. 
</div>
</div><p>
<a class="anchor" name="g79142ac09b1bf1b37cf6db06be375533"></a><!-- doxytag: member="cupti_activity.h::CUPTI_SOURCE_LOCATOR_ID_UNKNOWN" ref="g79142ac09b1bf1b37cf6db06be375533" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_SOURCE_LOCATOR_ID_UNKNOWN&nbsp;&nbsp;&nbsp;0          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source-locator ID that indicates an unknown source location. There is not an actual <a class="el" href="structCUpti__ActivitySourceLocator.html" title="The activity record for source locator.">CUpti_ActivitySourceLocator</a> object corresponding to this value. 
</div>
</div><p>
<a class="anchor" name="gd7733ba3c3dc5ac4330a882bbb6f6985"></a><!-- doxytag: member="cupti_activity.h::CUPTI_SYNCHRONIZATION_INVALID_VALUE" ref="gd7733ba3c3dc5ac4330a882bbb6f6985" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_SYNCHRONIZATION_INVALID_VALUE&nbsp;&nbsp;&nbsp;-1          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An invalid/unknown value. 
</div>
</div><p>
<a class="anchor" name="g03ed33cb1e0d4bf43c446ee3375d0c18"></a><!-- doxytag: member="cupti_activity.h::CUPTI_TIMESTAMP_UNKNOWN" ref="g03ed33cb1e0d4bf43c446ee3375d0c18" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_TIMESTAMP_UNKNOWN&nbsp;&nbsp;&nbsp;0LL          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An invalid/unknown timestamp for a start, end, queued, submitted, or completed time. 
</div>
</div><p>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g081fec67efc2c2504362e8e34fce7e6e"></a><!-- doxytag: member="cupti_activity.h::CUpti_BuffersCallbackCompleteFunc" ref="g081fec67efc2c2504362e8e34fce7e6e" args=")(CUcontext context, uint32_t streamId, uint8_t *buffer, size_t size, size_t validSize)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void( * <a class="el" href="group__CUPTI__ACTIVITY__API.html#g081fec67efc2c2504362e8e34fce7e6e">CUpti_BuffersCallbackCompleteFunc</a>)(CUcontext context, uint32_t streamId, uint8_t *buffer, size_t size, size_t validSize)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This callback function returns to the CUPTI client a buffer containing activity records. The buffer contains <code>validSize</code> bytes of activity records which should be read using cuptiActivityGetNextRecord. The number of dropped records can be read using cuptiActivityGetNumDroppedRecords. After this call CUPTI relinquished ownership of the buffer and will not use it anymore. The client may return the buffer to CUPTI using the CUpti_BuffersCallbackRequestFunc callback. Note: CUDA 6.0 onwards, all buffers returned by this callback are global buffers i.e. there is no context/stream specific buffer. User needs to parse the global buffer to extract the context/stream specific activity records.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context this buffer is associated with. If NULL, the buffer is associated with the global activities. This field is deprecated as of CUDA 6.0 and will always be NULL. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>streamId</em>&nbsp;</td><td>The stream id this buffer is associated with. This field is deprecated as of CUDA 6.0 and will always be NULL. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>buffer</em>&nbsp;</td><td>The activity record buffer. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>size</em>&nbsp;</td><td>The total size of the buffer in bytes as set in CUpti_BuffersCallbackRequestFunc. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>validSize</em>&nbsp;</td><td>The number of valid bytes in the buffer. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g3337d3cbfe624b05d4af435fd17e5b28"></a><!-- doxytag: member="cupti_activity.h::CUpti_BuffersCallbackRequestFunc" ref="g3337d3cbfe624b05d4af435fd17e5b28" args=")(uint8_t **buffer, size_t *size, size_t *maxNumRecords)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void( * <a class="el" href="group__CUPTI__ACTIVITY__API.html#g3337d3cbfe624b05d4af435fd17e5b28">CUpti_BuffersCallbackRequestFunc</a>)(uint8_t **buffer, size_t *size, size_t *maxNumRecords)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This callback function signals the CUPTI client that an activity buffer is needed by CUPTI. The activity buffer is used by CUPTI to store activity records. The callback function can decline the request by setting <code>*buffer</code> to NULL. In this case CUPTI may drop activity records.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>buffer</em>&nbsp;</td><td>Returns the new buffer. If set to NULL then no buffer is returned. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>size</em>&nbsp;</td><td>Returns the size of the returned buffer. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>maxNumRecords</em>&nbsp;</td><td>Returns the maximum number of records that should be placed in the buffer. If 0 then the buffer is filled with as many records as possible. If &gt; 0 the buffer is filled with at most that many records before it is returned. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gdd850477a3ae876889eb8dae31c8c664"></a><!-- doxytag: member="cupti_activity.h::CUpti_TimestampCallbackFunc" ref="gdd850477a3ae876889eb8dae31c8c664" args=")(void)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint64_t( * <a class="el" href="group__CUPTI__ACTIVITY__API.html#gdd850477a3ae876889eb8dae31c8c664">CUpti_TimestampCallbackFunc</a>)(void)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This callback function signals the CUPTI client that a timestamp needs to be returned. This timestamp would be treated as normalized timestamp to be used for various purposes in CUPTI. For example to store start and end timestamps reported in the CUPTI activity records. The returned timestamp must be in nanoseconds.<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gc168c4a9b5f4e9bd7553ca26118a307d" title="Registers callback function with CUPTI for providing timestamp.">cuptiActivityRegisterTimestampCallback</a> </dd></dl>

</div>
</div><p>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g1c31fe3f8ea0e46c6c20dd454a6caab6"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityAttribute" ref="g1c31fe3f8ea0e46c6c20dd454a6caab6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g1c31fe3f8ea0e46c6c20dd454a6caab6">CUpti_ActivityAttribute</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
These attributes are used to control the behavior of the activity API. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab6d4ba37cb123069fa778513f87cbb4b37"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab6d4ba37cb123069fa778513f87cbb4b37" args="" -->CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE</em>&nbsp;</td><td>
The device memory size (in bytes) reserved for storing profiling data for concurrent kernels (activity kind <a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e355d11bb9d376e95d141ddd13468f0b7a">CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL</a>), memcopies and memsets for each buffer on a context. The value is a size_t.<p>
There is a limit on how many device buffers can be allocated per context. User can query and set this limit using the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab63e84be3bdc3dc8a3477a56a1247287cc">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_POOL_LIMIT</a>. CUPTI doesn't pre-allocate all the buffers, it pre-allocates only those many buffers as set by the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab621f4deb586a4d759218e1d7f9530395f">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_PRE_ALLOCATE_VALUE</a>. When all of the data in a buffer is consumed, it is added in the reuse pool, and CUPTI picks a buffer from this pool when a new buffer is needed. Thus memory footprint does not scale with the kernel count. Applications with the high density of kernels, memcopies and memsets might result in having CUPTI to allocate more device buffers. CUPTI allocates another buffer only when it runs out of the buffers in the reuse pool.<p>
Since buffer allocation happens in the main application thread, this might result in stalls in the critical path. CUPTI pre-allocates 3 buffers of the same size to mitigate this issue. User can query and set the pre-allocation limit using the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab621f4deb586a4d759218e1d7f9530395f">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_PRE_ALLOCATE_VALUE</a>.<p>
Having larger buffer size leaves less device memory for the application. Having smaller buffer size increases the risk of dropping timestamps for records if too many kernels or memcopies or memsets are launched at one time.<p>
This value only applies to new buffer allocations. Set this value before initializing CUDA or before creating a context to ensure it is considered for the following allocations.<p>
The default value is 3200000 (~3MB) which can accommodate profiling data up to 100,000 kernels, memcopies and memsets combined.<p>
Note: Starting with the CUDA 12.0 Update 1 release, CUPTI allocates profiling buffer in the device memory by default as this might help in improving the performance of the tracing run. Refer to the description of the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab60baaaf2f89ee9a711b635ea3bc4b2e1e">CUPTI_ACTIVITY_ATTR_MEM_ALLOCATION_TYPE_HOST_PINNED</a> for more details. Size of the memory and maximum number of pools are still controlled by the attributes <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6d4ba37cb123069fa778513f87cbb4b37">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE</a> and <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab63e84be3bdc3dc8a3477a56a1247287cc">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_POOL_LIMIT</a>.<p>
Note: The actual amount of device memory per buffer reserved by CUPTI might be larger. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab6384faa1e65511b8169aafc2a008479dd"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE_CDP" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab6384faa1e65511b8169aafc2a008479dd" args="" -->CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE_CDP</em>&nbsp;</td><td>
The device memory size (in bytes) reserved for storing profiling data for CDP operations for each buffer on a context. The value is a size_t.<p>
Having larger buffer size means less flush operations but consumes more device memory. This value only applies to new allocations.<p>
Set this value before initializing CUDA or before creating a context to ensure it is considered for the following allocations.<p>
The default value is 8388608 (8MB).<p>
Note: The actual amount of device memory per context reserved by CUPTI might be larger. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab63e84be3bdc3dc8a3477a56a1247287cc"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_POOL_LIMIT" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab63e84be3bdc3dc8a3477a56a1247287cc" args="" -->CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_POOL_LIMIT</em>&nbsp;</td><td>
The maximum number of device memory buffers per context. The value is a size_t.<p>
For an application with high rate of kernel launches, memcopies and memsets having a bigger pool limit helps in timestamp collection for all these activties at the expense of a larger memory footprint. Refer to the description of the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6d4ba37cb123069fa778513f87cbb4b37">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE</a> for more details.<p>
Setting this value will not modify the number of memory buffers currently stored.<p>
Set this value before initializing CUDA to ensure the limit is not exceeded.<p>
The default value is 250. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab68ee6c22d0d55bb65700974bf9b1d75c2"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_SIZE" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab68ee6c22d0d55bb65700974bf9b1d75c2" args="" -->CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_SIZE</em>&nbsp;</td><td>
The profiling semaphore pool size reserved for storing profiling data for serialized kernels tracing (activity kind <a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e308040ca72a37cd63bf7e590717ce31c0">CUPTI_ACTIVITY_KIND_KERNEL</a>) for each context. The value is a size_t.<p>
There is a limit on how many semaphore pools can be allocated per context. User can query and set this limit using the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6d58f5aa6e9afbbcd2f7f7d6bd0896b8b">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_LIMIT</a>. CUPTI doesn't pre-allocate all the semaphore pools, it pre-allocates only those many semaphore pools as set by the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab652ecb99e83c0fa9da4bea4250089f3dd">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_PRE_ALLOCATE_VALUE</a>. When all of the data in a semaphore pool is consumed, it is added in the reuse pool, and CUPTI picks a semaphore pool from the reuse pool when a new semaphore pool is needed. Thus memory footprint does not scale with the kernel count. Applications with the high density of kernels might result in having CUPTI to allocate more semaphore pools. CUPTI allocates another semaphore pool only when it runs out of the semaphore pools in the reuse pool.<p>
Since semaphore pool allocation happens in the main application thread, this might result in stalls in the critical path. CUPTI pre-allocates 3 semaphore pools of the same size to mitigate this issue. User can query and set the pre-allocation limit using the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab652ecb99e83c0fa9da4bea4250089f3dd">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_PRE_ALLOCATE_VALUE</a>.<p>
Having larger semaphore pool size leaves less device memory for the application. Having smaller semaphore pool size increases the risk of dropping timestamps for kernel records if too many kernels are issued/launched at one time.<p>
This value only applies to new semaphore pool allocations. Set this value before initializing CUDA or before creating a context to ensure it is considered for the following allocations.<p>
The default value is 25000 which can accommodate profiling data for upto 25,000 kernels. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab6d58f5aa6e9afbbcd2f7f7d6bd0896b8b"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_LIMIT" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab6d58f5aa6e9afbbcd2f7f7d6bd0896b8b" args="" -->CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_LIMIT</em>&nbsp;</td><td>
The maximum number of profiling semaphore pools per context. The value is a size_t.<p>
For an application with high rate of kernel launches, having a bigger pool limit helps in timestamp collection for all the kernels, at the expense of a larger device memory footprint. Refer to the description of the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab68ee6c22d0d55bb65700974bf9b1d75c2">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_SIZE</a> for more details.<p>
Set this value before initializing CUDA to ensure the limit is not exceeded.<p>
The default value is 250. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab69857b104198e9388e15c20ec7650a517"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_ZEROED_OUT_ACTIVITY_BUFFER" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab69857b104198e9388e15c20ec7650a517" args="" -->CUPTI_ACTIVITY_ATTR_ZEROED_OUT_ACTIVITY_BUFFER</em>&nbsp;</td><td>
The flag to indicate whether user should provide activity buffer of zero value. The value is a uint8_t.<p>
If the value of this attribute is non-zero, user should provide a zero value buffer in the <a class="el" href="group__CUPTI__ACTIVITY__API.html#g3337d3cbfe624b05d4af435fd17e5b28">CUpti_BuffersCallbackRequestFunc</a>. If the user does not provide a zero value buffer after setting this to non-zero, the activity buffer may contain some uninitialized values when CUPTI returns it in <a class="el" href="group__CUPTI__ACTIVITY__API.html#g081fec67efc2c2504362e8e34fce7e6e">CUpti_BuffersCallbackCompleteFunc</a><p>
If the value of this attribute is zero, CUPTI will initialize the user buffer received in the <a class="el" href="group__CUPTI__ACTIVITY__API.html#g3337d3cbfe624b05d4af435fd17e5b28">CUpti_BuffersCallbackRequestFunc</a> to zero before filling it. If the user sets this to zero, a few stalls may appear in critical path because CUPTI will zero out the buffer in the main thread. Set this value before returning from <a class="el" href="group__CUPTI__ACTIVITY__API.html#g3337d3cbfe624b05d4af435fd17e5b28">CUpti_BuffersCallbackRequestFunc</a> to ensure it is considered for all the subsequent user buffers.<p>
The default value is 0. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab621f4deb586a4d759218e1d7f9530395f"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_PRE_ALLOCATE_VALUE" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab621f4deb586a4d759218e1d7f9530395f" args="" -->CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_PRE_ALLOCATE_VALUE</em>&nbsp;</td><td>
Number of device buffers to pre-allocate for a context during the initialization phase. The value is a size_t.<p>
Refer to the description of the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6d4ba37cb123069fa778513f87cbb4b37">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE</a> for details.<p>
This value must be less than the maximum number of device buffers set using the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab63e84be3bdc3dc8a3477a56a1247287cc">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_POOL_LIMIT</a><p>
Set this value before initializing CUDA or before creating a context to ensure it is considered by the CUPTI.<p>
The default value is set to 3 to ping pong between these buffers (if possible). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab652ecb99e83c0fa9da4bea4250089f3dd"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_PRE_ALLOCATE_VALUE" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab652ecb99e83c0fa9da4bea4250089f3dd" args="" -->CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_PRE_ALLOCATE_VALUE</em>&nbsp;</td><td>
Number of profiling semaphore pools to pre-allocate for a context during the initialization phase. The value is a size_t.<p>
Refer to the description of the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab68ee6c22d0d55bb65700974bf9b1d75c2">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_SIZE</a> for details.<p>
This value must be less than the maximum number of profiling semaphore pools set using the attribute <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg1c31fe3f8ea0e46c6c20dd454a6caab6d58f5aa6e9afbbcd2f7f7d6bd0896b8b">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_LIMIT</a><p>
Set this value before initializing CUDA or before creating a context to ensure it is considered by the CUPTI.<p>
The default value is set to 3 to ping pong between these pools (if possible). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1c31fe3f8ea0e46c6c20dd454a6caab60baaaf2f89ee9a711b635ea3bc4b2e1e"></a><!-- doxytag: member="CUPTI_ACTIVITY_ATTR_MEM_ALLOCATION_TYPE_HOST_PINNED" ref="gg1c31fe3f8ea0e46c6c20dd454a6caab60baaaf2f89ee9a711b635ea3bc4b2e1e" args="" -->CUPTI_ACTIVITY_ATTR_MEM_ALLOCATION_TYPE_HOST_PINNED</em>&nbsp;</td><td>
Allocate page-locked (pinned) host memory for storing profiling data for concurrent kernels, memcopies and memsets for each buffer on a context. The value is a uint8_t.<p>
Starting with the CUDA 11.2 release, CUPTI allocates profiling buffer in the pinned host memory by default as this might help in improving the performance of the tracing run. Allocating excessive amounts of pinned memory may degrade system performance, since it reduces the amount of memory available to the system for paging. For this reason user might want to change the location from pinned host memory to device memory by setting value of this attribute to 0.<p>
The default value is 1. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gf46dcec421da358ab4e9edad76774179"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityComputeApiKind" ref="gf46dcec421da358ab4e9edad76774179" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gf46dcec421da358ab4e9edad76774179">CUpti_ActivityComputeApiKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggf46dcec421da358ab4e9edad76774179f25237989ad34cb0fcc92a91cba86f50"></a><!-- doxytag: member="CUPTI_ACTIVITY_COMPUTE_API_UNKNOWN" ref="ggf46dcec421da358ab4e9edad76774179f25237989ad34cb0fcc92a91cba86f50" args="" -->CUPTI_ACTIVITY_COMPUTE_API_UNKNOWN</em>&nbsp;</td><td>
The compute API is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggf46dcec421da358ab4e9edad76774179b1183e2266d08045ce30f7b3f3c6013b"></a><!-- doxytag: member="CUPTI_ACTIVITY_COMPUTE_API_CUDA" ref="ggf46dcec421da358ab4e9edad76774179b1183e2266d08045ce30f7b3f3c6013b" args="" -->CUPTI_ACTIVITY_COMPUTE_API_CUDA</em>&nbsp;</td><td>
The compute APIs are for CUDA. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggf46dcec421da358ab4e9edad7677417981c34b48f58ef9c0f2eaa50cd2dee0a1"></a><!-- doxytag: member="CUPTI_ACTIVITY_COMPUTE_API_CUDA_MPS" ref="ggf46dcec421da358ab4e9edad7677417981c34b48f58ef9c0f2eaa50cd2dee0a1" args="" -->CUPTI_ACTIVITY_COMPUTE_API_CUDA_MPS</em>&nbsp;</td><td>
The compute APIs are for CUDA running in MPS (Multi-Process Service) environment. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gea1ccba1baca62ba5d6919e5685bfedf"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityEnvironmentKind" ref="gea1ccba1baca62ba5d6919e5685bfedf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gea1ccba1baca62ba5d6919e5685bfedf">CUpti_ActivityEnvironmentKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggea1ccba1baca62ba5d6919e5685bfedf7b63d7da15a8132974cc5db815eb0eb6"></a><!-- doxytag: member="CUPTI_ACTIVITY_ENVIRONMENT_UNKNOWN" ref="ggea1ccba1baca62ba5d6919e5685bfedf7b63d7da15a8132974cc5db815eb0eb6" args="" -->CUPTI_ACTIVITY_ENVIRONMENT_UNKNOWN</em>&nbsp;</td><td>
Unknown data. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggea1ccba1baca62ba5d6919e5685bfedf7be05ebb56bd4220518e306040a6178f"></a><!-- doxytag: member="CUPTI_ACTIVITY_ENVIRONMENT_SPEED" ref="ggea1ccba1baca62ba5d6919e5685bfedf7be05ebb56bd4220518e306040a6178f" args="" -->CUPTI_ACTIVITY_ENVIRONMENT_SPEED</em>&nbsp;</td><td>
The environment data is related to speed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggea1ccba1baca62ba5d6919e5685bfedf309cd1405723afb008c0c3c84d0db2f2"></a><!-- doxytag: member="CUPTI_ACTIVITY_ENVIRONMENT_TEMPERATURE" ref="ggea1ccba1baca62ba5d6919e5685bfedf309cd1405723afb008c0c3c84d0db2f2" args="" -->CUPTI_ACTIVITY_ENVIRONMENT_TEMPERATURE</em>&nbsp;</td><td>
The environment data is related to temperature. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggea1ccba1baca62ba5d6919e5685bfedf1134e640ea5b503bfa2a30b5fc9cb95d"></a><!-- doxytag: member="CUPTI_ACTIVITY_ENVIRONMENT_POWER" ref="ggea1ccba1baca62ba5d6919e5685bfedf1134e640ea5b503bfa2a30b5fc9cb95d" args="" -->CUPTI_ACTIVITY_ENVIRONMENT_POWER</em>&nbsp;</td><td>
The environment data is related to power. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggea1ccba1baca62ba5d6919e5685bfedf223a90fa39a8242f6d91117f7d77a52d"></a><!-- doxytag: member="CUPTI_ACTIVITY_ENVIRONMENT_COOLING" ref="ggea1ccba1baca62ba5d6919e5685bfedf223a90fa39a8242f6d91117f7d77a52d" args="" -->CUPTI_ACTIVITY_ENVIRONMENT_COOLING</em>&nbsp;</td><td>
The environment data is related to cooling. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gaef8ced52897c4377b0aee6196c37639"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityFlag" ref="gaef8ced52897c4377b0aee6196c37639" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Activity record flags. Flags can be combined by bitwise OR to associated multiple flags with an activity record. Each flag is specific to a certain activity kind, as noted below. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639377b3dbcdc6b709c20be055d6d81a377"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_NONE" ref="ggaef8ced52897c4377b0aee6196c37639377b3dbcdc6b709c20be055d6d81a377" args="" -->CUPTI_ACTIVITY_FLAG_NONE</em>&nbsp;</td><td>
Indicates the activity record has no flags. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639cc20353f87f213030f6fd596c0f18fe0"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_DEVICE_CONCURRENT_KERNELS" ref="ggaef8ced52897c4377b0aee6196c37639cc20353f87f213030f6fd596c0f18fe0" args="" -->CUPTI_ACTIVITY_FLAG_DEVICE_CONCURRENT_KERNELS</em>&nbsp;</td><td>
Indicates the activity represents a device that supports concurrent kernel execution. Valid for CUPTI_ACTIVITY_KIND_DEVICE. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639404ddcb5f521402a5a0ff254bd412d14"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_DEVICE_ATTRIBUTE_CUDEVICE" ref="ggaef8ced52897c4377b0aee6196c37639404ddcb5f521402a5a0ff254bd412d14" args="" -->CUPTI_ACTIVITY_FLAG_DEVICE_ATTRIBUTE_CUDEVICE</em>&nbsp;</td><td>
Indicates if the activity represents a CUdevice_attribute value or a CUpti_DeviceAttribute value. Valid for CUPTI_ACTIVITY_KIND_DEVICE_ATTRIBUTE. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763946792830925ef2f287c5887733a336b7"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MEMCPY_ASYNC" ref="ggaef8ced52897c4377b0aee6196c3763946792830925ef2f287c5887733a336b7" args="" -->CUPTI_ACTIVITY_FLAG_MEMCPY_ASYNC</em>&nbsp;</td><td>
Indicates the activity represents an asynchronous memcpy operation. Valid for CUPTI_ACTIVITY_KIND_MEMCPY. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763983dc601ec20a11fb12f73c91f45d9b59"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_INSTANTANEOUS" ref="ggaef8ced52897c4377b0aee6196c3763983dc601ec20a11fb12f73c91f45d9b59" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_INSTANTANEOUS</em>&nbsp;</td><td>
Indicates the activity represents an instantaneous marker. Valid for CUPTI_ACTIVITY_KIND_MARKER. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c376396bad08ee6097ebe3ec034d2af71e69de"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_START" ref="ggaef8ced52897c4377b0aee6196c376396bad08ee6097ebe3ec034d2af71e69de" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_START</em>&nbsp;</td><td>
Indicates the activity represents a region start marker. Valid for CUPTI_ACTIVITY_KIND_MARKER. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763916e4a905136ad86a17c825d0451761f7"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_END" ref="ggaef8ced52897c4377b0aee6196c3763916e4a905136ad86a17c825d0451761f7" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_END</em>&nbsp;</td><td>
Indicates the activity represents a region end marker. Valid for CUPTI_ACTIVITY_KIND_MARKER. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763913bfb2db6c64e2bc6c5fdd492011919e"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE" ref="ggaef8ced52897c4377b0aee6196c3763913bfb2db6c64e2bc6c5fdd492011919e" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE</em>&nbsp;</td><td>
Indicates the activity represents an attempt to acquire a user defined synchronization object. Valid for CUPTI_ACTIVITY_KIND_MARKER. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639d07f530982f52f40bd979dab8096041a"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_SUCCESS" ref="ggaef8ced52897c4377b0aee6196c37639d07f530982f52f40bd979dab8096041a" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_SUCCESS</em>&nbsp;</td><td>
Indicates the activity represents success in acquiring the user defined synchronization object. Valid for CUPTI_ACTIVITY_KIND_MARKER. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763904cc96d6594ce8d30ea8b70c6740fbab"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_FAILED" ref="ggaef8ced52897c4377b0aee6196c3763904cc96d6594ce8d30ea8b70c6740fbab" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_FAILED</em>&nbsp;</td><td>
Indicates the activity represents failure in acquiring the user defined synchronization object. Valid for CUPTI_ACTIVITY_KIND_MARKER. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639e2e6eea731826fb203122ca8ada03e22"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_SYNC_RELEASE" ref="ggaef8ced52897c4377b0aee6196c37639e2e6eea731826fb203122ca8ada03e22" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_SYNC_RELEASE</em>&nbsp;</td><td>
Indicates the activity represents releasing a reservation on user defined synchronization object. Valid for CUPTI_ACTIVITY_KIND_MARKER. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c376390b697cbddeb26df86f0737b674ebfa8b"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_COLOR_NONE" ref="ggaef8ced52897c4377b0aee6196c376390b697cbddeb26df86f0737b674ebfa8b" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_COLOR_NONE</em>&nbsp;</td><td>
Indicates the activity represents a marker that does not specify a color. Valid for CUPTI_ACTIVITY_KIND_MARKER_DATA. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639b882d6a86cd706c086ef12557c4df0b0"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MARKER_COLOR_ARGB" ref="ggaef8ced52897c4377b0aee6196c37639b882d6a86cd706c086ef12557c4df0b0" args="" -->CUPTI_ACTIVITY_FLAG_MARKER_COLOR_ARGB</em>&nbsp;</td><td>
Indicates the activity represents a marker that specifies a color in alpha-red-green-blue format. Valid for CUPTI_ACTIVITY_KIND_MARKER_DATA. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639cc8c74dee3d0b56abf76e3fc7d6e3b43"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_SIZE_MASK" ref="ggaef8ced52897c4377b0aee6196c37639cc8c74dee3d0b56abf76e3fc7d6e3b43" args="" -->CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_SIZE_MASK</em>&nbsp;</td><td>
The number of bytes requested by each thread Valid for <a class="el" href="structCUpti__ActivityGlobalAccess3.html" title="The activity record for source-level global access.">CUpti_ActivityGlobalAccess3</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c376392014dc1d117edc79dc65e6523f978e45"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_LOAD" ref="ggaef8ced52897c4377b0aee6196c376392014dc1d117edc79dc65e6523f978e45" args="" -->CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_LOAD</em>&nbsp;</td><td>
If bit in this flag is set, the access was load, else it is a store access. Valid for <a class="el" href="structCUpti__ActivityGlobalAccess3.html" title="The activity record for source-level global access.">CUpti_ActivityGlobalAccess3</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639df3379e3b8db5a1b26f9614414594d6f"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_CACHED" ref="ggaef8ced52897c4377b0aee6196c37639df3379e3b8db5a1b26f9614414594d6f" args="" -->CUPTI_ACTIVITY_FLAG_GLOBAL_ACCESS_KIND_CACHED</em>&nbsp;</td><td>
If this bit in flag is set, the load access was cached else it is uncached. Valid for <a class="el" href="structCUpti__ActivityGlobalAccess3.html" title="The activity record for source-level global access.">CUpti_ActivityGlobalAccess3</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c376397590de3d3ae597de222be9a69aa6d9ab"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_METRIC_OVERFLOWED" ref="ggaef8ced52897c4377b0aee6196c376397590de3d3ae597de222be9a69aa6d9ab" args="" -->CUPTI_ACTIVITY_FLAG_METRIC_OVERFLOWED</em>&nbsp;</td><td>
If this bit in flag is set, the metric value overflowed. Valid for <a class="el" href="structCUpti__ActivityMetric.html" title="The activity record for a CUPTI metric.">CUpti_ActivityMetric</a> and <a class="el" href="structCUpti__ActivityMetricInstance.html" title="The activity record for a CUPTI metric with instance information.">CUpti_ActivityMetricInstance</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763976e4f4d5b3b1364bcf8270489ad01b72"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_METRIC_VALUE_INVALID" ref="ggaef8ced52897c4377b0aee6196c3763976e4f4d5b3b1364bcf8270489ad01b72" args="" -->CUPTI_ACTIVITY_FLAG_METRIC_VALUE_INVALID</em>&nbsp;</td><td>
If this bit in flag is set, the metric value couldn't be calculated. This occurs when a value(s) required to calculate the metric is missing. Valid for <a class="el" href="structCUpti__ActivityMetric.html" title="The activity record for a CUPTI metric.">CUpti_ActivityMetric</a> and <a class="el" href="structCUpti__ActivityMetricInstance.html" title="The activity record for a CUPTI metric with instance information.">CUpti_ActivityMetricInstance</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639498ee644f34b932babdb8afd382f1031"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_INSTRUCTION_VALUE_INVALID" ref="ggaef8ced52897c4377b0aee6196c37639498ee644f34b932babdb8afd382f1031" args="" -->CUPTI_ACTIVITY_FLAG_INSTRUCTION_VALUE_INVALID</em>&nbsp;</td><td>
If this bit in flag is set, the source level metric value couldn't be calculated. This occurs when a value(s) required to calculate the source level metric cannot be evaluated. Valid for <a class="el" href="structCUpti__ActivityInstructionExecution.html" title="The activity record for source-level instruction execution.">CUpti_ActivityInstructionExecution</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763981786d0fa620fed299cc99199ad55da9"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_INSTRUCTION_CLASS_MASK" ref="ggaef8ced52897c4377b0aee6196c3763981786d0fa620fed299cc99199ad55da9" args="" -->CUPTI_ACTIVITY_FLAG_INSTRUCTION_CLASS_MASK</em>&nbsp;</td><td>
The mask for the instruction class, <a class="el" href="group__CUPTI__ACTIVITY__API.html#gbb6c00413aa3491f7da9e9cbbecf4133">CUpti_ActivityInstructionClass</a> Valid for <a class="el" href="structCUpti__ActivityInstructionExecution.html" title="The activity record for source-level instruction execution.">CUpti_ActivityInstructionExecution</a> and <a class="el" href="structCUpti__ActivityInstructionCorrelation.html" title="The activity record for source-level sass/source line-by-line correlation.">CUpti_ActivityInstructionCorrelation</a> </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c376398e3cd42d64eeaa1e7247994a4333c4f4"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_FLUSH_FORCED" ref="ggaef8ced52897c4377b0aee6196c376398e3cd42d64eeaa1e7247994a4333c4f4" args="" -->CUPTI_ACTIVITY_FLAG_FLUSH_FORCED</em>&nbsp;</td><td>
When calling cuptiActivityFlushAll, this flag can be set to force CUPTI to flush all records in the buffer, whether finished or not </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c3763954fc49d6f1eff82ee2822264bb64bb47"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_SHARED_ACCESS_KIND_SIZE_MASK" ref="ggaef8ced52897c4377b0aee6196c3763954fc49d6f1eff82ee2822264bb64bb47" args="" -->CUPTI_ACTIVITY_FLAG_SHARED_ACCESS_KIND_SIZE_MASK</em>&nbsp;</td><td>
The number of bytes requested by each thread Valid for <a class="el" href="structCUpti__ActivitySharedAccess.html" title="The activity record for source-level shared access.">CUpti_ActivitySharedAccess</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639de2d361da014b75fe61816173f161b29"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_SHARED_ACCESS_KIND_LOAD" ref="ggaef8ced52897c4377b0aee6196c37639de2d361da014b75fe61816173f161b29" args="" -->CUPTI_ACTIVITY_FLAG_SHARED_ACCESS_KIND_LOAD</em>&nbsp;</td><td>
If bit in this flag is set, the access was load, else it is a store access. Valid for <a class="el" href="structCUpti__ActivitySharedAccess.html" title="The activity record for source-level shared access.">CUpti_ActivitySharedAccess</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639fa320fd74dd5f35a1eb6cffbb96d5f08"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_MEMSET_ASYNC" ref="ggaef8ced52897c4377b0aee6196c37639fa320fd74dd5f35a1eb6cffbb96d5f08" args="" -->CUPTI_ACTIVITY_FLAG_MEMSET_ASYNC</em>&nbsp;</td><td>
Indicates the activity represents an asynchronous memset operation. Valid for CUPTI_ACTIVITY_KIND_MEMSET. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c37639fb23b459627bb82c9062d27bc86121e0"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_THRASHING_IN_CPU" ref="ggaef8ced52897c4377b0aee6196c37639fb23b459627bb82c9062d27bc86121e0" args="" -->CUPTI_ACTIVITY_FLAG_THRASHING_IN_CPU</em>&nbsp;</td><td>
Indicates the activity represents thrashing in CPU. Valid for counter of kind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING in CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggaef8ced52897c4377b0aee6196c376394f3db5910c065411a270f9832d70cb00"></a><!-- doxytag: member="CUPTI_ACTIVITY_FLAG_THROTTLING_IN_CPU" ref="ggaef8ced52897c4377b0aee6196c376394f3db5910c065411a270f9832d70cb00" args="" -->CUPTI_ACTIVITY_FLAG_THROTTLING_IN_CPU</em>&nbsp;</td><td>
Indicates the activity represents page throttling in CPU. Valid for counter of kind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING in CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gbb6c00413aa3491f7da9e9cbbecf4133"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityInstructionClass" ref="gbb6c00413aa3491f7da9e9cbbecf4133" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gbb6c00413aa3491f7da9e9cbbecf4133">CUpti_ActivityInstructionClass</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The sass instruction are broadly divided into different class. Each enum represents a classification. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf413369c66577ac6c61ae443998cca46ae6e9"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_UNKNOWN" ref="ggbb6c00413aa3491f7da9e9cbbecf413369c66577ac6c61ae443998cca46ae6e9" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_UNKNOWN</em>&nbsp;</td><td>
The instruction class is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf4133d7954a60ae04be8b6e583d345fef83ad"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_32" ref="ggbb6c00413aa3491f7da9e9cbbecf4133d7954a60ae04be8b6e583d345fef83ad" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_32</em>&nbsp;</td><td>
Represents a 32 bit floating point operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41331d29bad48ff705e330ae564a5af14102"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_64" ref="ggbb6c00413aa3491f7da9e9cbbecf41331d29bad48ff705e330ae564a5af14102" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_64</em>&nbsp;</td><td>
Represents a 64 bit floating point operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf4133023b6bea22054c06c7371eb1998e8c87"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_INTEGER" ref="ggbb6c00413aa3491f7da9e9cbbecf4133023b6bea22054c06c7371eb1998e8c87" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_INTEGER</em>&nbsp;</td><td>
Represents an integer operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41334f9f138df4253fbec73106bb6660c75e"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_BIT_CONVERSION" ref="ggbb6c00413aa3491f7da9e9cbbecf41334f9f138df4253fbec73106bb6660c75e" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_BIT_CONVERSION</em>&nbsp;</td><td>
Represents a bit conversion operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf4133ac12734ffd94d867cae4c94fba08704a"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_CONTROL_FLOW" ref="ggbb6c00413aa3491f7da9e9cbbecf4133ac12734ffd94d867cae4c94fba08704a" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_CONTROL_FLOW</em>&nbsp;</td><td>
Represents a control flow instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41333ac77d9f6306be54d6228f7edc4a6208"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_GLOBAL" ref="ggbb6c00413aa3491f7da9e9cbbecf41333ac77d9f6306be54d6228f7edc4a6208" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_GLOBAL</em>&nbsp;</td><td>
Represents a global load-store instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf4133a9f84506d4ce98f4c26db01e1dcde05f"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_SHARED" ref="ggbb6c00413aa3491f7da9e9cbbecf4133a9f84506d4ce98f4c26db01e1dcde05f" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_SHARED</em>&nbsp;</td><td>
Represents a shared load-store instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf413364ece2ba214f7dfa3d19e0c8390f153e"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_LOCAL" ref="ggbb6c00413aa3491f7da9e9cbbecf413364ece2ba214f7dfa3d19e0c8390f153e" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_LOCAL</em>&nbsp;</td><td>
Represents a local load-store instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41335d1ab6cb1a3185e4a4bdf3899eeaafa7"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_GENERIC" ref="ggbb6c00413aa3491f7da9e9cbbecf41335d1ab6cb1a3185e4a4bdf3899eeaafa7" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_GENERIC</em>&nbsp;</td><td>
Represents a generic load-store instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf4133bde49bea9638e85637ea8a2d43f1975f"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_SURFACE" ref="ggbb6c00413aa3491f7da9e9cbbecf4133bde49bea9638e85637ea8a2d43f1975f" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_SURFACE</em>&nbsp;</td><td>
Represents a surface load-store instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41336276d7cc0403fb0d797d7f94a9b2f5c2"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_CONSTANT" ref="ggbb6c00413aa3491f7da9e9cbbecf41336276d7cc0403fb0d797d7f94a9b2f5c2" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_CONSTANT</em>&nbsp;</td><td>
Represents a constant load instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf413335ee4b225023125eab1f10d7545a5a5a"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_TEXTURE" ref="ggbb6c00413aa3491f7da9e9cbbecf413335ee4b225023125eab1f10d7545a5a5a" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_TEXTURE</em>&nbsp;</td><td>
Represents a texture load-store instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41330b79b6df028b26c6c25f92c000895e92"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_GLOBAL_ATOMIC" ref="ggbb6c00413aa3491f7da9e9cbbecf41330b79b6df028b26c6c25f92c000895e92" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_GLOBAL_ATOMIC</em>&nbsp;</td><td>
Represents a global atomic instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41333dd52615d531b6b2fc7798facc45902a"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_SHARED_ATOMIC" ref="ggbb6c00413aa3491f7da9e9cbbecf41333dd52615d531b6b2fc7798facc45902a" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_SHARED_ATOMIC</em>&nbsp;</td><td>
Represents a shared atomic instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf413379b7a5ee372195ae9e45c8686f0e394d"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_SURFACE_ATOMIC" ref="ggbb6c00413aa3491f7da9e9cbbecf413379b7a5ee372195ae9e45c8686f0e394d" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_SURFACE_ATOMIC</em>&nbsp;</td><td>
Represents a surface atomic instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41330a8e9652bb787af6887b360d241baf56"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_INTER_THREAD_COMMUNICATION" ref="ggbb6c00413aa3491f7da9e9cbbecf41330a8e9652bb787af6887b360d241baf56" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_INTER_THREAD_COMMUNICATION</em>&nbsp;</td><td>
Represents a inter-thread communication instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf41335a0c07a63d9ca24a9f23425f6c63acda"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_BARRIER" ref="ggbb6c00413aa3491f7da9e9cbbecf41335a0c07a63d9ca24a9f23425f6c63acda" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_BARRIER</em>&nbsp;</td><td>
Represents a barrier instruction. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf413301eaff2bc4644d994215b2e1124552c4"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_MISCELLANEOUS" ref="ggbb6c00413aa3491f7da9e9cbbecf413301eaff2bc4644d994215b2e1124552c4" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_MISCELLANEOUS</em>&nbsp;</td><td>
Represents some miscellaneous instructions which do not fit in the above classification. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf413338e269c97a9b7bd7264e7a3746de91cb"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_16" ref="ggbb6c00413aa3491f7da9e9cbbecf413338e269c97a9b7bd7264e7a3746de91cb" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_FP_16</em>&nbsp;</td><td>
Represents a 16 bit floating point operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggbb6c00413aa3491f7da9e9cbbecf413303a0c6504cb5f5cf3fb358dd45b01fb4"></a><!-- doxytag: member="CUPTI_ACTIVITY_INSTRUCTION_CLASS_UNIFORM" ref="ggbb6c00413aa3491f7da9e9cbbecf413303a0c6504cb5f5cf3fb358dd45b01fb4" args="" -->CUPTI_ACTIVITY_INSTRUCTION_CLASS_UNIFORM</em>&nbsp;</td><td>
Represents uniform instruction. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0367503f3fc0af7b3c5051a9408efa80"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityJitEntryType" ref="g0367503f3fc0af7b3c5051a9408efa80" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g0367503f3fc0af7b3c5051a9408efa80">CUpti_ActivityJitEntryType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
To be used in <a class="el" href="structCUpti__ActivityJit.html" title="The activity record for JIT operations. This activity represents the JIT operations...">CUpti_ActivityJit</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg0367503f3fc0af7b3c5051a9408efa804eb8b2afc6f06f12dc9c9d4f3e846f38"></a><!-- doxytag: member="CUPTI_ACTIVITY_JIT_ENTRY_PTX_TO_CUBIN" ref="gg0367503f3fc0af7b3c5051a9408efa804eb8b2afc6f06f12dc9c9d4f3e846f38" args="" -->CUPTI_ACTIVITY_JIT_ENTRY_PTX_TO_CUBIN</em>&nbsp;</td><td>
PTX to CUBIN. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg0367503f3fc0af7b3c5051a9408efa808823fecaafeb850ceaba438876005952"></a><!-- doxytag: member="CUPTI_ACTIVITY_JIT_ENTRY_NVVM_IR_TO_PTX" ref="gg0367503f3fc0af7b3c5051a9408efa808823fecaafeb850ceaba438876005952" args="" -->CUPTI_ACTIVITY_JIT_ENTRY_NVVM_IR_TO_PTX</em>&nbsp;</td><td>
NVVM-IR to PTX </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2dc4ccb146a37875b3d8564b8100e59a"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityJitOperationType" ref="g2dc4ccb146a37875b3d8564b8100e59a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g2dc4ccb146a37875b3d8564b8100e59a">CUpti_ActivityJitOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
To be used in <a class="el" href="structCUpti__ActivityJit.html" title="The activity record for JIT operations. This activity represents the JIT operations...">CUpti_ActivityJit</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg2dc4ccb146a37875b3d8564b8100e59a328630e05a69f381cb3c3a2d1480995f"></a><!-- doxytag: member="CUPTI_ACTIVITY_JIT_OPERATION_CACHE_LOAD" ref="gg2dc4ccb146a37875b3d8564b8100e59a328630e05a69f381cb3c3a2d1480995f" args="" -->CUPTI_ACTIVITY_JIT_OPERATION_CACHE_LOAD</em>&nbsp;</td><td>
Loaded from the compute cache. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2dc4ccb146a37875b3d8564b8100e59a2e69624b157ef18daf6670b8531cc13c"></a><!-- doxytag: member="CUPTI_ACTIVITY_JIT_OPERATION_CACHE_STORE" ref="gg2dc4ccb146a37875b3d8564b8100e59a2e69624b157ef18daf6670b8531cc13c" args="" -->CUPTI_ACTIVITY_JIT_OPERATION_CACHE_STORE</em>&nbsp;</td><td>
Stored in the compute cache. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2dc4ccb146a37875b3d8564b8100e59a9917d36d8c47835ca564e4dd79c9e89b"></a><!-- doxytag: member="CUPTI_ACTIVITY_JIT_OPERATION_COMPILE" ref="gg2dc4ccb146a37875b3d8564b8100e59a9917d36d8c47835ca564e4dd79c9e89b" args="" -->CUPTI_ACTIVITY_JIT_OPERATION_COMPILE</em>&nbsp;</td><td>
JIT compilation. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gefed720d5a60c3e8b286cd386c4913e3"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityKind" ref="gefed720d5a60c3e8b286cd386c4913e3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Each activity record kind represents information about a GPU or an activity occurring on a CPU or GPU. Each kind is associated with a activity record structure that holds the information associated with the kind. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="structCUpti__Activity.html" title="The base activity record.">CUpti_Activity</a> <p>
<a class="el" href="structCUpti__ActivityAPI.html" title="The activity record for a driver or runtime API invocation.">CUpti_ActivityAPI</a> <p>
<a class="el" href="structCUpti__ActivityContext.html" title="The activity record for a context.">CUpti_ActivityContext</a> <p>
<a class="el" href="structCUpti__ActivityDevice.html" title="The activity record for a device. (deprecated).">CUpti_ActivityDevice</a> <p>
<a class="el" href="structCUpti__ActivityDevice2.html" title="The activity record for a device. (deprecated).">CUpti_ActivityDevice2</a> <p>
<a class="el" href="structCUpti__ActivityDevice3.html" title="The activity record for a device. (CUDA 7.0 onwards).">CUpti_ActivityDevice3</a> <p>
<a class="el" href="structCUpti__ActivityDevice4.html" title="The activity record for a device. (CUDA 11.6 onwards).">CUpti_ActivityDevice4</a> <p>
<a class="el" href="structCUpti__ActivityDeviceAttribute.html" title="The activity record for a device attribute.">CUpti_ActivityDeviceAttribute</a> <p>
<a class="el" href="structCUpti__ActivityEvent.html" title="The activity record for a CUPTI event.">CUpti_ActivityEvent</a> <p>
<a class="el" href="structCUpti__ActivityEventInstance.html" title="The activity record for a CUPTI event with instance information.">CUpti_ActivityEventInstance</a> <p>
<a class="el" href="structCUpti__ActivityKernel.html" title="The activity record for kernel. (deprecated).">CUpti_ActivityKernel</a> <p>
<a class="el" href="structCUpti__ActivityKernel2.html" title="The activity record for kernel. (deprecated).">CUpti_ActivityKernel2</a> <p>
<a class="el" href="structCUpti__ActivityKernel3.html" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated...">CUpti_ActivityKernel3</a> <p>
<a class="el" href="structCUpti__ActivityKernel4.html" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated...">CUpti_ActivityKernel4</a> <p>
<a class="el" href="structCUpti__ActivityKernel5.html" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated...">CUpti_ActivityKernel5</a> <p>
<a class="el" href="structCUpti__ActivityKernel6.html" title="The activity record for kernel. (deprecated in CUDA 11.6).">CUpti_ActivityKernel6</a> <p>
<a class="el" href="structCUpti__ActivityKernel7.html" title="The activity record for kernel. (deprecated in CUDA 11.8).">CUpti_ActivityKernel7</a> <p>
<a class="el" href="structCUpti__ActivityKernel8.html" title="The activity record for kernel.">CUpti_ActivityKernel8</a> <p>
<a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> <p>
<a class="el" href="structCUpti__ActivityCdpKernel.html" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel.">CUpti_ActivityCdpKernel</a> <p>
<a class="el" href="structCUpti__ActivityPreemption.html" title="The activity record for a preemption of a CDP kernel.">CUpti_ActivityPreemption</a> <p>
<a class="el" href="structCUpti__ActivityMemcpy.html" title="The activity record for memory copies. (deprecated).">CUpti_ActivityMemcpy</a> <p>
<a class="el" href="structCUpti__ActivityMemcpy3.html" title="The activity record for memory copies. (deprecated in CUDA 11.1).">CUpti_ActivityMemcpy3</a> <p>
<a class="el" href="structCUpti__ActivityMemcpy4.html" title="The activity record for memory copies. (deprecated in CUDA 11.6).">CUpti_ActivityMemcpy4</a> <p>
<a class="el" href="structCUpti__ActivityMemcpy5.html" title="The activity record for memory copies.">CUpti_ActivityMemcpy5</a> <p>
<a class="el" href="structCUpti__ActivityMemcpyPtoP.html" title="The activity record for peer-to-peer memory copies.">CUpti_ActivityMemcpyPtoP</a> <p>
<a class="el" href="structCUpti__ActivityMemcpyPtoP2.html" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1).">CUpti_ActivityMemcpyPtoP2</a> <p>
<a class="el" href="structCUpti__ActivityMemcpyPtoP3.html" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6).">CUpti_ActivityMemcpyPtoP3</a> <p>
<a class="el" href="structCUpti__ActivityMemcpyPtoP4.html" title="The activity record for peer-to-peer memory copies.">CUpti_ActivityMemcpyPtoP4</a> <p>
<a class="el" href="structCUpti__ActivityMemset.html" title="The activity record for memset. (deprecated).">CUpti_ActivityMemset</a> <p>
<a class="el" href="structCUpti__ActivityMemset2.html" title="The activity record for memset. (deprecated in CUDA 11.1).">CUpti_ActivityMemset2</a> <p>
<a class="el" href="structCUpti__ActivityMemset3.html" title="The activity record for memset. (deprecated in CUDA 11.6).">CUpti_ActivityMemset3</a> <p>
<a class="el" href="structCUpti__ActivityMemset4.html" title="The activity record for memset.">CUpti_ActivityMemset4</a> <p>
<a class="el" href="structCUpti__ActivityMetric.html" title="The activity record for a CUPTI metric.">CUpti_ActivityMetric</a> <p>
<a class="el" href="structCUpti__ActivityMetricInstance.html" title="The activity record for a CUPTI metric with instance information.">CUpti_ActivityMetricInstance</a> <p>
<a class="el" href="structCUpti__ActivityName.html" title="The activity record providing a name.">CUpti_ActivityName</a> <p>
<a class="el" href="structCUpti__ActivityMarker.html" title="The activity record providing a marker which is an instantaneous point in time. (deprecated...">CUpti_ActivityMarker</a> <p>
<a class="el" href="structCUpti__ActivityMarker2.html" title="The activity record providing a marker which is an instantaneous point in time.">CUpti_ActivityMarker2</a> <p>
<a class="el" href="structCUpti__ActivityMarkerData.html" title="The activity record providing detailed information for a marker.">CUpti_ActivityMarkerData</a> <p>
<a class="el" href="structCUpti__ActivitySourceLocator.html" title="The activity record for source locator.">CUpti_ActivitySourceLocator</a> <p>
<a class="el" href="structCUpti__ActivityGlobalAccess.html" title="The activity record for source-level global access. (deprecated).">CUpti_ActivityGlobalAccess</a> <p>
<a class="el" href="structCUpti__ActivityGlobalAccess2.html" title="The activity record for source-level global access. (deprecated in CUDA 9.0).">CUpti_ActivityGlobalAccess2</a> <p>
<a class="el" href="structCUpti__ActivityGlobalAccess3.html" title="The activity record for source-level global access.">CUpti_ActivityGlobalAccess3</a> <p>
<a class="el" href="structCUpti__ActivityBranch.html" title="The activity record for source level result branch. (deprecated).">CUpti_ActivityBranch</a> <p>
<a class="el" href="structCUpti__ActivityBranch2.html" title="The activity record for source level result branch.">CUpti_ActivityBranch2</a> <p>
<a class="el" href="structCUpti__ActivityOverhead.html" title="The activity record for CUPTI and driver overheads.">CUpti_ActivityOverhead</a> <p>
<a class="el" href="structCUpti__ActivityEnvironment.html" title="The activity record for CUPTI environmental data.">CUpti_ActivityEnvironment</a> <p>
<a class="el" href="structCUpti__ActivityInstructionExecution.html" title="The activity record for source-level instruction execution.">CUpti_ActivityInstructionExecution</a> <p>
<a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0).">CUpti_ActivityUnifiedMemoryCounter</a> <p>
<a class="el" href="structCUpti__ActivityFunction.html" title="The activity record for global/device functions.">CUpti_ActivityFunction</a> <p>
<a class="el" href="structCUpti__ActivityModule.html" title="The activity record for a CUDA module.">CUpti_ActivityModule</a> <p>
<a class="el" href="structCUpti__ActivitySharedAccess.html" title="The activity record for source-level shared access.">CUpti_ActivitySharedAccess</a> <p>
<a class="el" href="structCUpti__ActivityPCSampling.html" title="The activity record for PC sampling. (deprecated in CUDA 8.0).">CUpti_ActivityPCSampling</a> <p>
<a class="el" href="structCUpti__ActivityPCSampling2.html" title="The activity record for PC sampling. (deprecated in CUDA 9.0).">CUpti_ActivityPCSampling2</a> <p>
<a class="el" href="structCUpti__ActivityPCSampling3.html" title="The activity record for PC sampling.">CUpti_ActivityPCSampling3</a> <p>
<a class="el" href="structCUpti__ActivityPCSamplingRecordInfo.html" title="The activity record for record status for PC sampling.">CUpti_ActivityPCSamplingRecordInfo</a> <p>
<a class="el" href="structCUpti__ActivityCudaEvent.html" title="The activity record for CUDA event.">CUpti_ActivityCudaEvent</a> <p>
<a class="el" href="structCUpti__ActivityStream.html" title="The activity record for CUDA stream.">CUpti_ActivityStream</a> <p>
<a class="el" href="structCUpti__ActivitySynchronization.html" title="The activity record for synchronization management.">CUpti_ActivitySynchronization</a> <p>
<a class="el" href="structCUpti__ActivityInstructionCorrelation.html" title="The activity record for source-level sass/source line-by-line correlation.">CUpti_ActivityInstructionCorrelation</a> <p>
<a class="el" href="structCUpti__ActivityExternalCorrelation.html" title="The activity record for correlation with external records.">CUpti_ActivityExternalCorrelation</a> <p>
<a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond).">CUpti_ActivityUnifiedMemoryCounter2</a> <p>
<a class="el" href="structCUpti__ActivityOpenAccData.html" title="The activity record for OpenACC data.">CUpti_ActivityOpenAccData</a> <p>
<a class="el" href="structCUpti__ActivityOpenAccLaunch.html" title="The activity record for OpenACC launch.">CUpti_ActivityOpenAccLaunch</a> <p>
<a class="el" href="structCUpti__ActivityOpenAccOther.html" title="The activity record for OpenACC other.">CUpti_ActivityOpenAccOther</a> <p>
<a class="el" href="structCUpti__ActivityOpenMp.html" title="The base activity record for OpenMp records.">CUpti_ActivityOpenMp</a> <p>
<a class="el" href="structCUpti__ActivityNvLink.html" title="NVLink information. (deprecated in CUDA 9.0).">CUpti_ActivityNvLink</a> <p>
<a class="el" href="structCUpti__ActivityNvLink2.html" title="NVLink information. (deprecated in CUDA 10.0).">CUpti_ActivityNvLink2</a> <p>
<a class="el" href="structCUpti__ActivityNvLink3.html" title="NVLink information.">CUpti_ActivityNvLink3</a> <p>
<a class="el" href="structCUpti__ActivityNvLink4.html" title="NVLink information.">CUpti_ActivityNvLink4</a> <p>
<a class="el" href="structCUpti__ActivityMemory.html" title="The activity record for memory.">CUpti_ActivityMemory</a> <p>
<a class="el" href="structCUpti__ActivityPcie.html" title="PCI devices information required to construct topology.">CUpti_ActivityPcie</a> </dd></dl>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e38cdb3d455f5d74c761d065729278e564"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INVALID" ref="ggefed720d5a60c3e8b286cd386c4913e38cdb3d455f5d74c761d065729278e564" args="" -->CUPTI_ACTIVITY_KIND_INVALID</em>&nbsp;</td><td>
The activity record is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e39b9fdab31e2ddab02aa765836be54c89"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MEMCPY" ref="ggefed720d5a60c3e8b286cd386c4913e39b9fdab31e2ddab02aa765836be54c89" args="" -->CUPTI_ACTIVITY_KIND_MEMCPY</em>&nbsp;</td><td>
A host&lt;-&gt;host, host&lt;-&gt;device, or device&lt;-&gt;device memory copy. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMemcpy5.html">CUpti_ActivityMemcpy5</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3d1e7f3094032b82536f82af699a29c70"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MEMSET" ref="ggefed720d5a60c3e8b286cd386c4913e3d1e7f3094032b82536f82af699a29c70" args="" -->CUPTI_ACTIVITY_KIND_MEMSET</em>&nbsp;</td><td>
A memory set executing on the GPU. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMemset4.html">CUpti_ActivityMemset4</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e308040ca72a37cd63bf7e590717ce31c0"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_KERNEL" ref="ggefed720d5a60c3e8b286cd386c4913e308040ca72a37cd63bf7e590717ce31c0" args="" -->CUPTI_ACTIVITY_KIND_KERNEL</em>&nbsp;</td><td>
A kernel executing on the GPU. This activity kind may significantly change the overall performance characteristics of the application because all kernel executions are serialized on the GPU. Other activity kind for kernel CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL doesn't break kernel concurrency. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityKernel9.html">CUpti_ActivityKernel9</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e32adab6350d6daadf8b1e9227dddc176d"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_DRIVER" ref="ggefed720d5a60c3e8b286cd386c4913e32adab6350d6daadf8b1e9227dddc176d" args="" -->CUPTI_ACTIVITY_KIND_DRIVER</em>&nbsp;</td><td>
A CUDA driver API function execution. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityAPI.html">CUpti_ActivityAPI</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3fca9d0fbaff7ffe0b90f08339d366549"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_RUNTIME" ref="ggefed720d5a60c3e8b286cd386c4913e3fca9d0fbaff7ffe0b90f08339d366549" args="" -->CUPTI_ACTIVITY_KIND_RUNTIME</em>&nbsp;</td><td>
A CUDA runtime API function execution. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityAPI.html">CUpti_ActivityAPI</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3bb664d1fb2726042eec36481fcbdb8f2"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_EVENT" ref="ggefed720d5a60c3e8b286cd386c4913e3bb664d1fb2726042eec36481fcbdb8f2" args="" -->CUPTI_ACTIVITY_KIND_EVENT</em>&nbsp;</td><td>
An event value. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityEvent.html">CUpti_ActivityEvent</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3428ef6e0f8b2b41c8b5d6b59b241a38c"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_METRIC" ref="ggefed720d5a60c3e8b286cd386c4913e3428ef6e0f8b2b41c8b5d6b59b241a38c" args="" -->CUPTI_ACTIVITY_KIND_METRIC</em>&nbsp;</td><td>
A metric value. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMetric.html">CUpti_ActivityMetric</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3cced468a28d3632fc7bde703a93e1d88"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_DEVICE" ref="ggefed720d5a60c3e8b286cd386c4913e3cced468a28d3632fc7bde703a93e1d88" args="" -->CUPTI_ACTIVITY_KIND_DEVICE</em>&nbsp;</td><td>
Information about a device. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e390dff26f684f68817b4d99b4b9288dda"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_CONTEXT" ref="ggefed720d5a60c3e8b286cd386c4913e390dff26f684f68817b4d99b4b9288dda" args="" -->CUPTI_ACTIVITY_KIND_CONTEXT</em>&nbsp;</td><td>
Information about a context. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityContext.html">CUpti_ActivityContext</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e355d11bb9d376e95d141ddd13468f0b7a"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL" ref="ggefed720d5a60c3e8b286cd386c4913e355d11bb9d376e95d141ddd13468f0b7a" args="" -->CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL</em>&nbsp;</td><td>
A kernel executing on the GPU. This activity kind doesn't break kernel concurrency. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityKernel9.html">CUpti_ActivityKernel9</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e377da5359610e2caf8a0832959e2d3bf7"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_NAME" ref="ggefed720d5a60c3e8b286cd386c4913e377da5359610e2caf8a0832959e2d3bf7" args="" -->CUPTI_ACTIVITY_KIND_NAME</em>&nbsp;</td><td>
Resource naming done via NVTX APIs for thread, device, context, etc. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityName.html">CUpti_ActivityName</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3a6651558af56e8c679911494734d9d7f"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MARKER" ref="ggefed720d5a60c3e8b286cd386c4913e3a6651558af56e8c679911494734d9d7f" args="" -->CUPTI_ACTIVITY_KIND_MARKER</em>&nbsp;</td><td>
Instantaneous, start, or end NVTX marker. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMarker2.html">CUpti_ActivityMarker2</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3288c468ae1e204ac839f406dbbaa507c"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MARKER_DATA" ref="ggefed720d5a60c3e8b286cd386c4913e3288c468ae1e204ac839f406dbbaa507c" args="" -->CUPTI_ACTIVITY_KIND_MARKER_DATA</em>&nbsp;</td><td>
Extended, optional, data about a marker. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMarkerData.html">CUpti_ActivityMarkerData</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3eab8764eb0d81db50890228847ff1128"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_SOURCE_LOCATOR" ref="ggefed720d5a60c3e8b286cd386c4913e3eab8764eb0d81db50890228847ff1128" args="" -->CUPTI_ACTIVITY_KIND_SOURCE_LOCATOR</em>&nbsp;</td><td>
Source information about source level result. The corresponding activity record structure is <a class="el" href="structCUpti__ActivitySourceLocator.html">CUpti_ActivitySourceLocator</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e36c5e42f5070918d147d9b8f4c15302c0"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS" ref="ggefed720d5a60c3e8b286cd386c4913e36c5e42f5070918d147d9b8f4c15302c0" args="" -->CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS</em>&nbsp;</td><td>
Results for source-level global acccess. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityGlobalAccess3.html">CUpti_ActivityGlobalAccess3</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e334e3196f630c78f15870381577735069"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_BRANCH" ref="ggefed720d5a60c3e8b286cd386c4913e334e3196f630c78f15870381577735069" args="" -->CUPTI_ACTIVITY_KIND_BRANCH</em>&nbsp;</td><td>
Results for source-level branch. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityBranch2.html">CUpti_ActivityBranch2</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3139bef6f29a8a4e79d541dd71670413a"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_OVERHEAD" ref="ggefed720d5a60c3e8b286cd386c4913e3139bef6f29a8a4e79d541dd71670413a" args="" -->CUPTI_ACTIVITY_KIND_OVERHEAD</em>&nbsp;</td><td>
Overhead activity records. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityOverhead.html">CUpti_ActivityOverhead</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e33ca38c360ea18f473013ff1dca2cf354"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_CDP_KERNEL" ref="ggefed720d5a60c3e8b286cd386c4913e33ca38c360ea18f473013ff1dca2cf354" args="" -->CUPTI_ACTIVITY_KIND_CDP_KERNEL</em>&nbsp;</td><td>
A CDP (CUDA Dynamic Parallel) kernel executing on the GPU. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityCdpKernel.html">CUpti_ActivityCdpKernel</a>. This activity can not be directly enabled or disabled. It is enabled and disabled through concurrent kernel activity i.e. _CONCURRENT_KERNEL. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3c7ba07cf57d3a2eacee4223d4dd4c3ab"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_PREEMPTION" ref="ggefed720d5a60c3e8b286cd386c4913e3c7ba07cf57d3a2eacee4223d4dd4c3ab" args="" -->CUPTI_ACTIVITY_KIND_PREEMPTION</em>&nbsp;</td><td>
Preemption activity record indicating a preemption of a CDP (CUDA Dynamic Parallel) kernel executing on the GPU. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityPreemption.html">CUpti_ActivityPreemption</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3f2951c08c789ccd61966404c688905c2"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_ENVIRONMENT" ref="ggefed720d5a60c3e8b286cd386c4913e3f2951c08c789ccd61966404c688905c2" args="" -->CUPTI_ACTIVITY_KIND_ENVIRONMENT</em>&nbsp;</td><td>
Environment activity records indicating power, clock, thermal, etc. levels of the GPU. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityEnvironment.html">CUpti_ActivityEnvironment</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3541a8007acdad9959cf4e23d43173a1c"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_EVENT_INSTANCE" ref="ggefed720d5a60c3e8b286cd386c4913e3541a8007acdad9959cf4e23d43173a1c" args="" -->CUPTI_ACTIVITY_KIND_EVENT_INSTANCE</em>&nbsp;</td><td>
An event value associated with a specific event domain instance. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityEventInstance.html">CUpti_ActivityEventInstance</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e37579ea13179193e084f2d1e25cbe839f"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MEMCPY2" ref="ggefed720d5a60c3e8b286cd386c4913e37579ea13179193e084f2d1e25cbe839f" args="" -->CUPTI_ACTIVITY_KIND_MEMCPY2</em>&nbsp;</td><td>
A peer to peer memory copy. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html">CUpti_ActivityMemcpyPtoP4</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e34bc352176dc5687b7165e04ff2c8d4e8"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_METRIC_INSTANCE" ref="ggefed720d5a60c3e8b286cd386c4913e34bc352176dc5687b7165e04ff2c8d4e8" args="" -->CUPTI_ACTIVITY_KIND_METRIC_INSTANCE</em>&nbsp;</td><td>
A metric value associated with a specific metric domain instance. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMetricInstance.html">CUpti_ActivityMetricInstance</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e337fd58215e526c80cc88e80d64aa3333"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INSTRUCTION_EXECUTION" ref="ggefed720d5a60c3e8b286cd386c4913e337fd58215e526c80cc88e80d64aa3333" args="" -->CUPTI_ACTIVITY_KIND_INSTRUCTION_EXECUTION</em>&nbsp;</td><td>
Results for source-level instruction execution. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityInstructionExecution.html">CUpti_ActivityInstructionExecution</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e318847504bf44386482cb871f49292621"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER" ref="ggefed720d5a60c3e8b286cd386c4913e318847504bf44386482cb871f49292621" args="" -->CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER</em>&nbsp;</td><td>
Unified Memory counter record. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html">CUpti_ActivityUnifiedMemoryCounter2</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e34cfcb61638437470debc278416806bcd"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_FUNCTION" ref="ggefed720d5a60c3e8b286cd386c4913e34cfcb61638437470debc278416806bcd" args="" -->CUPTI_ACTIVITY_KIND_FUNCTION</em>&nbsp;</td><td>
Device global/function record. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityFunction.html">CUpti_ActivityFunction</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e393ff4cf2c1b1c7d0c7273ee27cafcaa8"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MODULE" ref="ggefed720d5a60c3e8b286cd386c4913e393ff4cf2c1b1c7d0c7273ee27cafcaa8" args="" -->CUPTI_ACTIVITY_KIND_MODULE</em>&nbsp;</td><td>
CUDA Module record. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityModule.html">CUpti_ActivityModule</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e34ca74aca624c83d99dc8ce98f6e91f30"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_DEVICE_ATTRIBUTE" ref="ggefed720d5a60c3e8b286cd386c4913e34ca74aca624c83d99dc8ce98f6e91f30" args="" -->CUPTI_ACTIVITY_KIND_DEVICE_ATTRIBUTE</em>&nbsp;</td><td>
A device attribute value. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityDeviceAttribute.html">CUpti_ActivityDeviceAttribute</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3726760f4e8b2dbaa2e408a5f36913b12"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_SHARED_ACCESS" ref="ggefed720d5a60c3e8b286cd386c4913e3726760f4e8b2dbaa2e408a5f36913b12" args="" -->CUPTI_ACTIVITY_KIND_SHARED_ACCESS</em>&nbsp;</td><td>
Results for source-level shared acccess. The corresponding activity record structure is <a class="el" href="structCUpti__ActivitySharedAccess.html">CUpti_ActivitySharedAccess</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3a5724fb8f45bfe030a3553dd314d7363"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_PC_SAMPLING" ref="ggefed720d5a60c3e8b286cd386c4913e3a5724fb8f45bfe030a3553dd314d7363" args="" -->CUPTI_ACTIVITY_KIND_PC_SAMPLING</em>&nbsp;</td><td>
Enable PC sampling for kernels. This will serialize kernels. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityPCSampling3.html">CUpti_ActivityPCSampling3</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3496ce7a481bd3e0cce62325a77cb52d1"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_PC_SAMPLING_RECORD_INFO" ref="ggefed720d5a60c3e8b286cd386c4913e3496ce7a481bd3e0cce62325a77cb52d1" args="" -->CUPTI_ACTIVITY_KIND_PC_SAMPLING_RECORD_INFO</em>&nbsp;</td><td>
Summary information about PC sampling records. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityPCSamplingRecordInfo.html">CUpti_ActivityPCSamplingRecordInfo</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3554db0163e7cbe4695b71e1da4084560"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INSTRUCTION_CORRELATION" ref="ggefed720d5a60c3e8b286cd386c4913e3554db0163e7cbe4695b71e1da4084560" args="" -->CUPTI_ACTIVITY_KIND_INSTRUCTION_CORRELATION</em>&nbsp;</td><td>
SASS/Source line-by-line correlation record. This will generate sass/source correlation for functions that have source level analysis or pc sampling results. The records will be generated only when either of source level analysis or pc sampling activity is enabled. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityInstructionCorrelation.html">CUpti_ActivityInstructionCorrelation</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3da3d084df073956a01904c3206663c1c"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_OPENACC_DATA" ref="ggefed720d5a60c3e8b286cd386c4913e3da3d084df073956a01904c3206663c1c" args="" -->CUPTI_ACTIVITY_KIND_OPENACC_DATA</em>&nbsp;</td><td>
OpenACC data events. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityOpenAccData.html">CUpti_ActivityOpenAccData</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3f2eb19f7ad6e2f213433c01141310f0f"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_OPENACC_LAUNCH" ref="ggefed720d5a60c3e8b286cd386c4913e3f2eb19f7ad6e2f213433c01141310f0f" args="" -->CUPTI_ACTIVITY_KIND_OPENACC_LAUNCH</em>&nbsp;</td><td>
OpenACC launch events. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityOpenAccLaunch.html">CUpti_ActivityOpenAccLaunch</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3e0f80000acb3ac91d39d17f4d886d833"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_OPENACC_OTHER" ref="ggefed720d5a60c3e8b286cd386c4913e3e0f80000acb3ac91d39d17f4d886d833" args="" -->CUPTI_ACTIVITY_KIND_OPENACC_OTHER</em>&nbsp;</td><td>
OpenACC other events. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityOpenAccOther.html">CUpti_ActivityOpenAccOther</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e337ae649424643f577b4606aa25f551c5"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_CUDA_EVENT" ref="ggefed720d5a60c3e8b286cd386c4913e337ae649424643f577b4606aa25f551c5" args="" -->CUPTI_ACTIVITY_KIND_CUDA_EVENT</em>&nbsp;</td><td>
Information about a CUDA event. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityCudaEvent.html">CUpti_ActivityCudaEvent</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e349715578146bfad02f666d5dd34875be"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_STREAM" ref="ggefed720d5a60c3e8b286cd386c4913e349715578146bfad02f666d5dd34875be" args="" -->CUPTI_ACTIVITY_KIND_STREAM</em>&nbsp;</td><td>
Information about a CUDA stream. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityStream.html">CUpti_ActivityStream</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e326584cd85d1c069703320337b400decc"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_SYNCHRONIZATION" ref="ggefed720d5a60c3e8b286cd386c4913e326584cd85d1c069703320337b400decc" args="" -->CUPTI_ACTIVITY_KIND_SYNCHRONIZATION</em>&nbsp;</td><td>
Records for synchronization management. The corresponding activity record structure is <a class="el" href="structCUpti__ActivitySynchronization.html">CUpti_ActivitySynchronization</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3b1e47c623392bf8bac53812156659738"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_EXTERNAL_CORRELATION" ref="ggefed720d5a60c3e8b286cd386c4913e3b1e47c623392bf8bac53812156659738" args="" -->CUPTI_ACTIVITY_KIND_EXTERNAL_CORRELATION</em>&nbsp;</td><td>
Records for correlation of different programming APIs. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityExternalCorrelation.html">CUpti_ActivityExternalCorrelation</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3de82e94ed2b1c83bb9030c247fb36807"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_NVLINK" ref="ggefed720d5a60c3e8b286cd386c4913e3de82e94ed2b1c83bb9030c247fb36807" args="" -->CUPTI_ACTIVITY_KIND_NVLINK</em>&nbsp;</td><td>
NVLink information. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityNvLink4.html">CUpti_ActivityNvLink4</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e38a93f9498a95c6c51d0ebde5cc308e3b"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INSTANTANEOUS_EVENT" ref="ggefed720d5a60c3e8b286cd386c4913e38a93f9498a95c6c51d0ebde5cc308e3b" args="" -->CUPTI_ACTIVITY_KIND_INSTANTANEOUS_EVENT</em>&nbsp;</td><td>
Instantaneous Event information. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityInstantaneousEvent.html">CUpti_ActivityInstantaneousEvent</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3740e64c9d40ed8170749616c9325bfc9"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INSTANTANEOUS_EVENT_INSTANCE" ref="ggefed720d5a60c3e8b286cd386c4913e3740e64c9d40ed8170749616c9325bfc9" args="" -->CUPTI_ACTIVITY_KIND_INSTANTANEOUS_EVENT_INSTANCE</em>&nbsp;</td><td>
Instantaneous Event information for a specific event domain instance. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityInstantaneousEventInstance.html">CUpti_ActivityInstantaneousEventInstance</a> </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3c44636f1c1d0d33b5bdfba2d11bc1694"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC" ref="ggefed720d5a60c3e8b286cd386c4913e3c44636f1c1d0d33b5bdfba2d11bc1694" args="" -->CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC</em>&nbsp;</td><td>
Instantaneous Metric information The corresponding activity record structure is <a class="el" href="structCUpti__ActivityInstantaneousMetric.html">CUpti_ActivityInstantaneousMetric</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e352790c9f804f2406625e7d3dc57a650e"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC_INSTANCE" ref="ggefed720d5a60c3e8b286cd386c4913e352790c9f804f2406625e7d3dc57a650e" args="" -->CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC_INSTANCE</em>&nbsp;</td><td>
Instantaneous Metric information for a specific metric domain instance. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html">CUpti_ActivityInstantaneousMetricInstance</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3fee93ef030088d7a992a1abd6d6e53b0"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MEMORY" ref="ggefed720d5a60c3e8b286cd386c4913e3fee93ef030088d7a992a1abd6d6e53b0" args="" -->CUPTI_ACTIVITY_KIND_MEMORY</em>&nbsp;</td><td>
Memory activity tracking allocation and freeing of the memory The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMemory.html">CUpti_ActivityMemory</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3b8459839e64ca4e93e33bafe32448a7c"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_PCIE" ref="ggefed720d5a60c3e8b286cd386c4913e3b8459839e64ca4e93e33bafe32448a7c" args="" -->CUPTI_ACTIVITY_KIND_PCIE</em>&nbsp;</td><td>
PCI devices information used for PCI topology. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityPcie.html">CUpti_ActivityPcie</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e30bd244aacfdf212dd6a441a61c2ecc2f"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_OPENMP" ref="ggefed720d5a60c3e8b286cd386c4913e30bd244aacfdf212dd6a441a61c2ecc2f" args="" -->CUPTI_ACTIVITY_KIND_OPENMP</em>&nbsp;</td><td>
OpenMP parallel events. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityOpenMp.html">CUpti_ActivityOpenMp</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3b38c32aeca137a0777959055fd22e911"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_INTERNAL_LAUNCH_API" ref="ggefed720d5a60c3e8b286cd386c4913e3b38c32aeca137a0777959055fd22e911" args="" -->CUPTI_ACTIVITY_KIND_INTERNAL_LAUNCH_API</em>&nbsp;</td><td>
A CUDA driver kernel launch occurring outside of any public API function execution. Tools can handle these like records for driver API launch functions, although the cbid field is not used here. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityAPI.html">CUpti_ActivityAPI</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3fc702c2e896e657930de3685d4ea3233"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MEMORY2" ref="ggefed720d5a60c3e8b286cd386c4913e3fc702c2e896e657930de3685d4ea3233" args="" -->CUPTI_ACTIVITY_KIND_MEMORY2</em>&nbsp;</td><td>
Memory activity tracking allocation and freeing of the memory The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMemory3.html">CUpti_ActivityMemory3</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3083e033a6dbfd50789ab610345c08cf6"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_MEMORY_POOL" ref="ggefed720d5a60c3e8b286cd386c4913e3083e033a6dbfd50789ab610345c08cf6" args="" -->CUPTI_ACTIVITY_KIND_MEMORY_POOL</em>&nbsp;</td><td>
Memory pool activity tracking creation, destruction and triming of the memory pool. The corresponding activity record structure is <a class="el" href="structCUpti__ActivityMemoryPool2.html">CUpti_ActivityMemoryPool2</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3547a9c6a0ab9917e2681187d74cc6b14"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_GRAPH_TRACE" ref="ggefed720d5a60c3e8b286cd386c4913e3547a9c6a0ab9917e2681187d74cc6b14" args="" -->CUPTI_ACTIVITY_KIND_GRAPH_TRACE</em>&nbsp;</td><td>
The corresponding activity record structure is <a class="el" href="structCUpti__ActivityGraphTrace.html">CUpti_ActivityGraphTrace</a>. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggefed720d5a60c3e8b286cd386c4913e3aa65dae0c007f2d80813e606bfa8ce69"></a><!-- doxytag: member="CUPTI_ACTIVITY_KIND_JIT" ref="ggefed720d5a60c3e8b286cd386c4913e3aa65dae0c007f2d80813e606bfa8ce69" args="" -->CUPTI_ACTIVITY_KIND_JIT</em>&nbsp;</td><td>
JIT operation tracking The corresponding activity record structure is <a class="el" href="structCUpti__ActivityJit.html">CUpti_ActivityJit</a>. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd054e12847a4a5a0ece53d2cb6dc6d8c"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityLaunchType" ref="gd054e12847a4a5a0ece53d2cb6dc6d8c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c">CUpti_ActivityLaunchType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd054e12847a4a5a0ece53d2cb6dc6d8c21c9328557bdc3c946d11b1a7675a71d"></a><!-- doxytag: member="CUPTI_ACTIVITY_LAUNCH_TYPE_REGULAR" ref="ggd054e12847a4a5a0ece53d2cb6dc6d8c21c9328557bdc3c946d11b1a7675a71d" args="" -->CUPTI_ACTIVITY_LAUNCH_TYPE_REGULAR</em>&nbsp;</td><td>
The kernel was launched via a regular kernel call </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd054e12847a4a5a0ece53d2cb6dc6d8c5d1f5068218a430756a163dbcc1bb3e0"></a><!-- doxytag: member="CUPTI_ACTIVITY_LAUNCH_TYPE_COOPERATIVE_SINGLE_DEVICE" ref="ggd054e12847a4a5a0ece53d2cb6dc6d8c5d1f5068218a430756a163dbcc1bb3e0" args="" -->CUPTI_ACTIVITY_LAUNCH_TYPE_COOPERATIVE_SINGLE_DEVICE</em>&nbsp;</td><td>
The kernel was launched via API cudaLaunchCooperativeKernel() or cuLaunchCooperativeKernel() </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd054e12847a4a5a0ece53d2cb6dc6d8cea6403588743260fafe7f3cfe573819a"></a><!-- doxytag: member="CUPTI_ACTIVITY_LAUNCH_TYPE_COOPERATIVE_MULTI_DEVICE" ref="ggd054e12847a4a5a0ece53d2cb6dc6d8cea6403588743260fafe7f3cfe573819a" args="" -->CUPTI_ACTIVITY_LAUNCH_TYPE_COOPERATIVE_MULTI_DEVICE</em>&nbsp;</td><td>
The kernel was launched via API cudaLaunchCooperativeKernelMultiDevice() or cuLaunchCooperativeKernelMultiDevice() </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd054e12847a4a5a0ece53d2cb6dc6d8c531975c9016f9dfa21c657d8e44742d6"></a><!-- doxytag: member="CUPTI_ACTIVITY_LAUNCH_TYPE_CBL_COMMANDLIST" ref="ggd054e12847a4a5a0ece53d2cb6dc6d8c531975c9016f9dfa21c657d8e44742d6" args="" -->CUPTI_ACTIVITY_LAUNCH_TYPE_CBL_COMMANDLIST</em>&nbsp;</td><td>
The kernel was launched as a CBL commandlist </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g10056d66c2ee966fc5cde439eb0a3661"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityMemcpyKind" ref="g10056d66c2ee966fc5cde439eb0a3661" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661">CUpti_ActivityMemcpyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Each kind represents the source and destination targets of a memory copy. Targets are host, device, and array. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661a1ab03ae9b14e757fe458db8f72864d4"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_UNKNOWN" ref="gg10056d66c2ee966fc5cde439eb0a3661a1ab03ae9b14e757fe458db8f72864d4" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_UNKNOWN</em>&nbsp;</td><td>
The memory copy kind is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661a6da0b06091d2d92c6eadf0492bfe9ab"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_HTOD" ref="gg10056d66c2ee966fc5cde439eb0a3661a6da0b06091d2d92c6eadf0492bfe9ab" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_HTOD</em>&nbsp;</td><td>
A host to device memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661ef545ba0bb77911dc948c0387bcbd707"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_DTOH" ref="gg10056d66c2ee966fc5cde439eb0a3661ef545ba0bb77911dc948c0387bcbd707" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_DTOH</em>&nbsp;</td><td>
A device to host memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661b2599683471ac60b7650ffc008c08998"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_HTOA" ref="gg10056d66c2ee966fc5cde439eb0a3661b2599683471ac60b7650ffc008c08998" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_HTOA</em>&nbsp;</td><td>
A host to device array memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a366142afec25f21cba3421f9b9fff1b63180"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_ATOH" ref="gg10056d66c2ee966fc5cde439eb0a366142afec25f21cba3421f9b9fff1b63180" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_ATOH</em>&nbsp;</td><td>
A device array to host memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661d25c1f160462df8798bc80b4bc149b10"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_ATOA" ref="gg10056d66c2ee966fc5cde439eb0a3661d25c1f160462df8798bc80b4bc149b10" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_ATOA</em>&nbsp;</td><td>
A device array to device array memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661f00443a5ed3e12a3f51bc8168c2e5b72"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_ATOD" ref="gg10056d66c2ee966fc5cde439eb0a3661f00443a5ed3e12a3f51bc8168c2e5b72" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_ATOD</em>&nbsp;</td><td>
A device array to device memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661c3483eaa97fff7a411988346327a69e9"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_DTOA" ref="gg10056d66c2ee966fc5cde439eb0a3661c3483eaa97fff7a411988346327a69e9" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_DTOA</em>&nbsp;</td><td>
A device to device array memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a36617964cc727c860bbfe24527e7407cf0ad"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_DTOD" ref="gg10056d66c2ee966fc5cde439eb0a36617964cc727c860bbfe24527e7407cf0ad" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_DTOD</em>&nbsp;</td><td>
A device to device memory copy on the same device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661c0d8fd604c3fe9b21474c3e4d184fb31"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_HTOH" ref="gg10056d66c2ee966fc5cde439eb0a3661c0d8fd604c3fe9b21474c3e4d184fb31" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_HTOH</em>&nbsp;</td><td>
A host to host memory copy. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10056d66c2ee966fc5cde439eb0a3661dd1465d676763b5eed70b40ed78f9d11"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMCPY_KIND_PTOP" ref="gg10056d66c2ee966fc5cde439eb0a3661dd1465d676763b5eed70b40ed78f9d11" args="" -->CUPTI_ACTIVITY_MEMCPY_KIND_PTOP</em>&nbsp;</td><td>
A peer to peer memory copy across different devices. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g9969b86f0e54989b27080dc6083263bc"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityMemoryKind" ref="g9969b86f0e54989b27080dc6083263bc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Each kind represents the type of the memory accessed by a memory operation/copy. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bc936e97ceb6f0e6675192a6b3ae0a2806"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_UNKNOWN" ref="gg9969b86f0e54989b27080dc6083263bc936e97ceb6f0e6675192a6b3ae0a2806" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_UNKNOWN</em>&nbsp;</td><td>
The memory kind is unknown. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bcd15f71ae8aabd1e0bd240ec2684ca6a8"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_PAGEABLE" ref="gg9969b86f0e54989b27080dc6083263bcd15f71ae8aabd1e0bd240ec2684ca6a8" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_PAGEABLE</em>&nbsp;</td><td>
The memory is pageable. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bc9fc7a14791c26dc66146df9dd769b81d"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_PINNED" ref="gg9969b86f0e54989b27080dc6083263bc9fc7a14791c26dc66146df9dd769b81d" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_PINNED</em>&nbsp;</td><td>
The memory is pinned. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bc8303e9fe524dfdba14dabf9d097c3a66"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_DEVICE" ref="gg9969b86f0e54989b27080dc6083263bc8303e9fe524dfdba14dabf9d097c3a66" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_DEVICE</em>&nbsp;</td><td>
The memory is on the device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bca24f0ba883d51eb4b35a12b70f014347"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_ARRAY" ref="gg9969b86f0e54989b27080dc6083263bca24f0ba883d51eb4b35a12b70f014347" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_ARRAY</em>&nbsp;</td><td>
The memory is an array. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bc21ab1d11996502d5cf0abb895d621c82"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_MANAGED" ref="gg9969b86f0e54989b27080dc6083263bc21ab1d11996502d5cf0abb895d621c82" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_MANAGED</em>&nbsp;</td><td>
The memory is managed </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bc1f1782ef45cf76205ded5818c621f1c2"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_DEVICE_STATIC" ref="gg9969b86f0e54989b27080dc6083263bc1f1782ef45cf76205ded5818c621f1c2" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_DEVICE_STATIC</em>&nbsp;</td><td>
The memory is device static </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9969b86f0e54989b27080dc6083263bcc3954098a2d6fb407fbd4f3267213b8c"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_KIND_MANAGED_STATIC" ref="gg9969b86f0e54989b27080dc6083263bcc3954098a2d6fb407fbd4f3267213b8c" args="" -->CUPTI_ACTIVITY_MEMORY_KIND_MANAGED_STATIC</em>&nbsp;</td><td>
The memory is managed static </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6404b89cfbbd60e04204244d230b15c4"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityMemoryOperationType" ref="g6404b89cfbbd60e04204244d230b15c4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Describes the type of memory operation, to be used with <a class="el" href="structCUpti__ActivityMemory3.html" title="The activity record for memory.">CUpti_ActivityMemory3</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg6404b89cfbbd60e04204244d230b15c451740ba2573be3e95f801d5a74852872"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_INVALID" ref="gg6404b89cfbbd60e04204244d230b15c451740ba2573be3e95f801d5a74852872" args="" -->CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_INVALID</em>&nbsp;</td><td>
The operation is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6404b89cfbbd60e04204244d230b15c40693280cc92c31000e479c10c36f046c"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_ALLOCATION" ref="gg6404b89cfbbd60e04204244d230b15c40693280cc92c31000e479c10c36f046c" args="" -->CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_ALLOCATION</em>&nbsp;</td><td>
Memory is allocated. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6404b89cfbbd60e04204244d230b15c433546fcb0556ba361b7d8a3a1b8f8664"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_RELEASE" ref="gg6404b89cfbbd60e04204244d230b15c433546fcb0556ba361b7d8a3a1b8f8664" args="" -->CUPTI_ACTIVITY_MEMORY_OPERATION_TYPE_RELEASE</em>&nbsp;</td><td>
Memory is released. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g878fb2c94e0051169fdf0ca7982612a2"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityMemoryPoolOperationType" ref="g878fb2c94e0051169fdf0ca7982612a2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Describes the type of memory pool operation, to be used with <a class="el" href="structCUpti__ActivityMemoryPool2.html" title="The activity record for memory pool.">CUpti_ActivityMemoryPool2</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg878fb2c94e0051169fdf0ca7982612a267ac93f4d2f869d52c287374f0f91b54"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_INVALID" ref="gg878fb2c94e0051169fdf0ca7982612a267ac93f4d2f869d52c287374f0f91b54" args="" -->CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_INVALID</em>&nbsp;</td><td>
The operation is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg878fb2c94e0051169fdf0ca7982612a2a1c55e97b82416e1c3eb43a583e94118"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_CREATED" ref="gg878fb2c94e0051169fdf0ca7982612a2a1c55e97b82416e1c3eb43a583e94118" args="" -->CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_CREATED</em>&nbsp;</td><td>
Memory pool is created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg878fb2c94e0051169fdf0ca7982612a2c52f1aa08afe1875891682a148bdfe27"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_DESTROYED" ref="gg878fb2c94e0051169fdf0ca7982612a2c52f1aa08afe1875891682a148bdfe27" args="" -->CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_DESTROYED</em>&nbsp;</td><td>
Memory pool is destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg878fb2c94e0051169fdf0ca7982612a2bf9f79a9d787c6be5965366672d6ff12"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_TRIMMED" ref="gg878fb2c94e0051169fdf0ca7982612a2bf9f79a9d787c6be5965366672d6ff12" args="" -->CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_TRIMMED</em>&nbsp;</td><td>
Memory pool is trimmed. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g8c40b23a5fe82862b18d60c5c42399a8"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityMemoryPoolType" ref="g8c40b23a5fe82862b18d60c5c42399a8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Describes the type of memory pool, to be used with <a class="el" href="structCUpti__ActivityMemory3.html" title="The activity record for memory.">CUpti_ActivityMemory3</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg8c40b23a5fe82862b18d60c5c42399a88ff8e248e56bc838617586e91d01cd51"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_POOL_TYPE_INVALID" ref="gg8c40b23a5fe82862b18d60c5c42399a88ff8e248e56bc838617586e91d01cd51" args="" -->CUPTI_ACTIVITY_MEMORY_POOL_TYPE_INVALID</em>&nbsp;</td><td>
The operation is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c40b23a5fe82862b18d60c5c42399a842fb40627946b24dad67c11a1ac7de2a"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL" ref="gg8c40b23a5fe82862b18d60c5c42399a842fb40627946b24dad67c11a1ac7de2a" args="" -->CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL</em>&nbsp;</td><td>
Memory pool is local to the process. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8c40b23a5fe82862b18d60c5c42399a8aa132b412c76db4ab0a174db4108d13e"></a><!-- doxytag: member="CUPTI_ACTIVITY_MEMORY_POOL_TYPE_IMPORTED" ref="gg8c40b23a5fe82862b18d60c5c42399a8aa132b412c76db4ab0a174db4108d13e" args="" -->CUPTI_ACTIVITY_MEMORY_POOL_TYPE_IMPORTED</em>&nbsp;</td><td>
Memory pool is imported by the process. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g9b6136c1123883722ded735eb52cf270"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityObjectKind" ref="g9b6136c1123883722ded735eb52cf270" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="unionCUpti__ActivityObjectKindId.html" title="Identifiers for object kinds as specified by CUpti_ActivityObjectKind.">CUpti_ActivityObjectKindId</a> </dd></dl>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg9b6136c1123883722ded735eb52cf2701c4785a6ac2acff605a9567a03df2f04"></a><!-- doxytag: member="CUPTI_ACTIVITY_OBJECT_UNKNOWN" ref="gg9b6136c1123883722ded735eb52cf2701c4785a6ac2acff605a9567a03df2f04" args="" -->CUPTI_ACTIVITY_OBJECT_UNKNOWN</em>&nbsp;</td><td>
The object kind is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9b6136c1123883722ded735eb52cf270c3895b1c8a5d791685cc4874ecd9d4fe"></a><!-- doxytag: member="CUPTI_ACTIVITY_OBJECT_PROCESS" ref="gg9b6136c1123883722ded735eb52cf270c3895b1c8a5d791685cc4874ecd9d4fe" args="" -->CUPTI_ACTIVITY_OBJECT_PROCESS</em>&nbsp;</td><td>
A process. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9b6136c1123883722ded735eb52cf270defb468f187cc200521fb30fcf57c7ac"></a><!-- doxytag: member="CUPTI_ACTIVITY_OBJECT_THREAD" ref="gg9b6136c1123883722ded735eb52cf270defb468f187cc200521fb30fcf57c7ac" args="" -->CUPTI_ACTIVITY_OBJECT_THREAD</em>&nbsp;</td><td>
A thread. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9b6136c1123883722ded735eb52cf270af5b89eca120e06d8fa4db1848bc4ad1"></a><!-- doxytag: member="CUPTI_ACTIVITY_OBJECT_DEVICE" ref="gg9b6136c1123883722ded735eb52cf270af5b89eca120e06d8fa4db1848bc4ad1" args="" -->CUPTI_ACTIVITY_OBJECT_DEVICE</em>&nbsp;</td><td>
A device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9b6136c1123883722ded735eb52cf2707e7a1eb1d58672eb3d67d0838cf6dc5c"></a><!-- doxytag: member="CUPTI_ACTIVITY_OBJECT_CONTEXT" ref="gg9b6136c1123883722ded735eb52cf2707e7a1eb1d58672eb3d67d0838cf6dc5c" args="" -->CUPTI_ACTIVITY_OBJECT_CONTEXT</em>&nbsp;</td><td>
A context. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9b6136c1123883722ded735eb52cf270f7a19f33ba61f622efc8721ea7628590"></a><!-- doxytag: member="CUPTI_ACTIVITY_OBJECT_STREAM" ref="gg9b6136c1123883722ded735eb52cf270f7a19f33ba61f622efc8721ea7628590" args="" -->CUPTI_ACTIVITY_OBJECT_STREAM</em>&nbsp;</td><td>
A stream. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gcc8863d79c939f6bb57324db4503b265"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityOverheadKind" ref="gcc8863d79c939f6bb57324db4503b265" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gcc8863d79c939f6bb57324db4503b265">CUpti_ActivityOverheadKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggcc8863d79c939f6bb57324db4503b265debf137c41c5cd4f78d625e9a96143d0"></a><!-- doxytag: member="CUPTI_ACTIVITY_OVERHEAD_UNKNOWN" ref="ggcc8863d79c939f6bb57324db4503b265debf137c41c5cd4f78d625e9a96143d0" args="" -->CUPTI_ACTIVITY_OVERHEAD_UNKNOWN</em>&nbsp;</td><td>
The overhead kind is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcc8863d79c939f6bb57324db4503b2650f8237ff52b16444e23b0f4fbe321c9c"></a><!-- doxytag: member="CUPTI_ACTIVITY_OVERHEAD_DRIVER_COMPILER" ref="ggcc8863d79c939f6bb57324db4503b2650f8237ff52b16444e23b0f4fbe321c9c" args="" -->CUPTI_ACTIVITY_OVERHEAD_DRIVER_COMPILER</em>&nbsp;</td><td>
Compiler overhead. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcc8863d79c939f6bb57324db4503b265e1d3c3fc20e50bc7f309c4c5a3bf63f5"></a><!-- doxytag: member="CUPTI_ACTIVITY_OVERHEAD_CUPTI_BUFFER_FLUSH" ref="ggcc8863d79c939f6bb57324db4503b265e1d3c3fc20e50bc7f309c4c5a3bf63f5" args="" -->CUPTI_ACTIVITY_OVERHEAD_CUPTI_BUFFER_FLUSH</em>&nbsp;</td><td>
Activity buffer flush overhead. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcc8863d79c939f6bb57324db4503b26520afe08544af18668c8433fa61781083"></a><!-- doxytag: member="CUPTI_ACTIVITY_OVERHEAD_CUPTI_INSTRUMENTATION" ref="ggcc8863d79c939f6bb57324db4503b26520afe08544af18668c8433fa61781083" args="" -->CUPTI_ACTIVITY_OVERHEAD_CUPTI_INSTRUMENTATION</em>&nbsp;</td><td>
CUPTI instrumentation overhead. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcc8863d79c939f6bb57324db4503b2659eb4b20e1813e34e711a3506956c0638"></a><!-- doxytag: member="CUPTI_ACTIVITY_OVERHEAD_CUPTI_RESOURCE" ref="ggcc8863d79c939f6bb57324db4503b2659eb4b20e1813e34e711a3506956c0638" args="" -->CUPTI_ACTIVITY_OVERHEAD_CUPTI_RESOURCE</em>&nbsp;</td><td>
CUPTI resource creation and destruction overhead. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g9d3467131ce6c87aa85f3a5a43f83484"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityPartitionedGlobalCacheConfig" ref="g9d3467131ce6c87aa85f3a5a43f83484" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg9d3467131ce6c87aa85f3a5a43f834842d7bdc24c11fd2183cd7a72d6723e202"></a><!-- doxytag: member="CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_UNKNOWN" ref="gg9d3467131ce6c87aa85f3a5a43f834842d7bdc24c11fd2183cd7a72d6723e202" args="" -->CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_UNKNOWN</em>&nbsp;</td><td>
Partitioned global cache config unknown. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9d3467131ce6c87aa85f3a5a43f83484464d34d766c2215a7793fb5a80d5f07c"></a><!-- doxytag: member="CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_NOT_SUPPORTED" ref="gg9d3467131ce6c87aa85f3a5a43f83484464d34d766c2215a7793fb5a80d5f07c" args="" -->CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_NOT_SUPPORTED</em>&nbsp;</td><td>
Partitioned global cache not supported. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9d3467131ce6c87aa85f3a5a43f83484d28746ef8a7578e4ff185dfbaf6fe65f"></a><!-- doxytag: member="CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_OFF" ref="gg9d3467131ce6c87aa85f3a5a43f83484d28746ef8a7578e4ff185dfbaf6fe65f" args="" -->CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_OFF</em>&nbsp;</td><td>
Partitioned global cache config off. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9d3467131ce6c87aa85f3a5a43f8348430014ec479ce99cb07a212b02eba6ce8"></a><!-- doxytag: member="CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_ON" ref="gg9d3467131ce6c87aa85f3a5a43f8348430014ec479ce99cb07a212b02eba6ce8" args="" -->CUPTI_ACTIVITY_PARTITIONED_GLOBAL_CACHE_CONFIG_ON</em>&nbsp;</td><td>
Partitioned global cache config on. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="ga51d59f0407cce71516a53875bf825fe"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityPCSamplingPeriod" ref="ga51d59f0407cce71516a53875bf825fe" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#ga51d59f0407cce71516a53875bf825fe">CUpti_ActivityPCSamplingPeriod</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Sampling period can be set using <a class="el" href="group__CUPTI__ACTIVITY__API.html#g115305aadc838df99d88283fc64c4317">cuptiActivityConfigurePCSampling</a> <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gga51d59f0407cce71516a53875bf825fee6fac4282894adedef77eae540af2cc7"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_INVALID" ref="gga51d59f0407cce71516a53875bf825fee6fac4282894adedef77eae540af2cc7" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_INVALID</em>&nbsp;</td><td>
The PC sampling period is not set. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga51d59f0407cce71516a53875bf825fe8c3d1b4457ddf0c9d071f7d2060fcf8c"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MIN" ref="gga51d59f0407cce71516a53875bf825fe8c3d1b4457ddf0c9d071f7d2060fcf8c" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MIN</em>&nbsp;</td><td>
Minimum sampling period available on the device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga51d59f0407cce71516a53875bf825fec1fccce5da5597c9eb416f67ac180cf1"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_LOW" ref="gga51d59f0407cce71516a53875bf825fec1fccce5da5597c9eb416f67ac180cf1" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_LOW</em>&nbsp;</td><td>
Sampling period in lower range. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga51d59f0407cce71516a53875bf825fe3b0a60268a0c62ccbf9a7a290809b0c3"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MID" ref="gga51d59f0407cce71516a53875bf825fe3b0a60268a0c62ccbf9a7a290809b0c3" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MID</em>&nbsp;</td><td>
Medium sampling period. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga51d59f0407cce71516a53875bf825fe56be90f1308ca0ca961f8e5a60804779"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_HIGH" ref="gga51d59f0407cce71516a53875bf825fe56be90f1308ca0ca961f8e5a60804779" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_HIGH</em>&nbsp;</td><td>
Sampling period in higher range. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga51d59f0407cce71516a53875bf825fe993553d38db8c1fb797807645efbef40"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MAX" ref="gga51d59f0407cce71516a53875bf825fe993553d38db8c1fb797807645efbef40" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MAX</em>&nbsp;</td><td>
Maximum sampling period available on the device. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g57bafb9baafeae0880dae6eaa1a8e12d"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityPCSamplingStallReason" ref="g57bafb9baafeae0880dae6eaa1a8e12d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d6ac7664868ca4a2bbaa461e17f848828"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_INVALID" ref="gg57bafb9baafeae0880dae6eaa1a8e12d6ac7664868ca4a2bbaa461e17f848828" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_INVALID</em>&nbsp;</td><td>
Invalid reason </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d383ee917a199aa0f4519406a2b102bf8"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_NONE" ref="gg57bafb9baafeae0880dae6eaa1a8e12d383ee917a199aa0f4519406a2b102bf8" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_NONE</em>&nbsp;</td><td>
No stall, instruction is selected for issue </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d2ffdb23cfecebb1e1d24b5e0420c705f"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_INST_FETCH" ref="gg57bafb9baafeae0880dae6eaa1a8e12d2ffdb23cfecebb1e1d24b5e0420c705f" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_INST_FETCH</em>&nbsp;</td><td>
Warp is blocked because next instruction is not yet available, because of instruction cache miss, or because of branching effects </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12dd753605c5a160e267bc0937d4d4f1044"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_EXEC_DEPENDENCY" ref="gg57bafb9baafeae0880dae6eaa1a8e12dd753605c5a160e267bc0937d4d4f1044" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_EXEC_DEPENDENCY</em>&nbsp;</td><td>
Instruction is waiting on an arithmatic dependency </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d2f084ea3a334985840c088f889e634d2"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_MEMORY_DEPENDENCY" ref="gg57bafb9baafeae0880dae6eaa1a8e12d2f084ea3a334985840c088f889e634d2" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_MEMORY_DEPENDENCY</em>&nbsp;</td><td>
Warp is blocked because it is waiting for a memory access to complete. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d3e22171773f0a899eb8a42b7c5af7700"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_TEXTURE" ref="gg57bafb9baafeae0880dae6eaa1a8e12d3e22171773f0a899eb8a42b7c5af7700" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_TEXTURE</em>&nbsp;</td><td>
Texture sub-system is fully utilized or has too many outstanding requests. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d65934d7473dbad97a5cac997ce2fad7b"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_SYNC" ref="gg57bafb9baafeae0880dae6eaa1a8e12d65934d7473dbad97a5cac997ce2fad7b" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_SYNC</em>&nbsp;</td><td>
Warp is blocked as it is waiting at __syncthreads() or at memory barrier. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d92e0a26e16f508e35e1af4965e87fa9e"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_CONSTANT_MEMORY_DEPENDENCY" ref="gg57bafb9baafeae0880dae6eaa1a8e12d92e0a26e16f508e35e1af4965e87fa9e" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_CONSTANT_MEMORY_DEPENDENCY</em>&nbsp;</td><td>
Warp is blocked waiting for __constant__ memory and immediate memory access to complete. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d783f30f4ea4230ab9ca4202771315e80"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_PIPE_BUSY" ref="gg57bafb9baafeae0880dae6eaa1a8e12d783f30f4ea4230ab9ca4202771315e80" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_PIPE_BUSY</em>&nbsp;</td><td>
Compute operation cannot be performed due to the required resources not being available. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d6f56d940fefbd4bdbf348982a7293ff8"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_MEMORY_THROTTLE" ref="gg57bafb9baafeae0880dae6eaa1a8e12d6f56d940fefbd4bdbf348982a7293ff8" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_MEMORY_THROTTLE</em>&nbsp;</td><td>
Warp is blocked because there are too many pending memory operations. In Kepler architecture it often indicates high number of memory replays. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12d0f753c861de0d226e9392c5eb16605de"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_NOT_SELECTED" ref="gg57bafb9baafeae0880dae6eaa1a8e12d0f753c861de0d226e9392c5eb16605de" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_NOT_SELECTED</em>&nbsp;</td><td>
Warp was ready to issue, but some other warp issued instead. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12dfb5b3327f4380c5e4231dea600b6eb45"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_OTHER" ref="gg57bafb9baafeae0880dae6eaa1a8e12dfb5b3327f4380c5e4231dea600b6eb45" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_OTHER</em>&nbsp;</td><td>
Miscellaneous reasons </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg57bafb9baafeae0880dae6eaa1a8e12df15199d80a762688794ecdcd6c41ca0d"></a><!-- doxytag: member="CUPTI_ACTIVITY_PC_SAMPLING_STALL_SLEEPING" ref="gg57bafb9baafeae0880dae6eaa1a8e12df15199d80a762688794ecdcd6c41ca0d" args="" -->CUPTI_ACTIVITY_PC_SAMPLING_STALL_SLEEPING</em>&nbsp;</td><td>
Sleeping. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g7609527c3c6c122a8bb5b1e5d1ae1c4a"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityPreemptionKind" ref="g7609527c3c6c122a8bb5b1e5d1ae1c4a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g7609527c3c6c122a8bb5b1e5d1ae1c4a">CUpti_ActivityPreemptionKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg7609527c3c6c122a8bb5b1e5d1ae1c4afff0a18953bcb6f6133ff94f85af73e2"></a><!-- doxytag: member="CUPTI_ACTIVITY_PREEMPTION_KIND_UNKNOWN" ref="gg7609527c3c6c122a8bb5b1e5d1ae1c4afff0a18953bcb6f6133ff94f85af73e2" args="" -->CUPTI_ACTIVITY_PREEMPTION_KIND_UNKNOWN</em>&nbsp;</td><td>
The preemption kind is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7609527c3c6c122a8bb5b1e5d1ae1c4acea6a07f2e98653a0c581c8e6b8f12ba"></a><!-- doxytag: member="CUPTI_ACTIVITY_PREEMPTION_KIND_SAVE" ref="gg7609527c3c6c122a8bb5b1e5d1ae1c4acea6a07f2e98653a0c581c8e6b8f12ba" args="" -->CUPTI_ACTIVITY_PREEMPTION_KIND_SAVE</em>&nbsp;</td><td>
Preemption to save CDP block. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7609527c3c6c122a8bb5b1e5d1ae1c4a6f0ac3503f46be050347060d14a154d2"></a><!-- doxytag: member="CUPTI_ACTIVITY_PREEMPTION_KIND_RESTORE" ref="gg7609527c3c6c122a8bb5b1e5d1ae1c4a6f0ac3503f46be050347060d14a154d2" args="" -->CUPTI_ACTIVITY_PREEMPTION_KIND_RESTORE</em>&nbsp;</td><td>
Preemption to restore CDP block. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g10b985831ad4db3bd401953e999c877a"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityStreamFlag" ref="g10b985831ad4db3bd401953e999c877a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g10b985831ad4db3bd401953e999c877a">CUpti_ActivityStreamFlag</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The types of stream to be used with <a class="el" href="structCUpti__ActivityStream.html" title="The activity record for CUDA stream.">CUpti_ActivityStream</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg10b985831ad4db3bd401953e999c877a941ba0264440bca0b7e4414410e0cc07"></a><!-- doxytag: member="CUPTI_ACTIVITY_STREAM_CREATE_FLAG_UNKNOWN" ref="gg10b985831ad4db3bd401953e999c877a941ba0264440bca0b7e4414410e0cc07" args="" -->CUPTI_ACTIVITY_STREAM_CREATE_FLAG_UNKNOWN</em>&nbsp;</td><td>
Unknown data. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10b985831ad4db3bd401953e999c877a209125987238fb9340a8ade23f912d43"></a><!-- doxytag: member="CUPTI_ACTIVITY_STREAM_CREATE_FLAG_DEFAULT" ref="gg10b985831ad4db3bd401953e999c877a209125987238fb9340a8ade23f912d43" args="" -->CUPTI_ACTIVITY_STREAM_CREATE_FLAG_DEFAULT</em>&nbsp;</td><td>
Default stream. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10b985831ad4db3bd401953e999c877aa1dd7a9e2e04e1ee7987a4ff6eb48cff"></a><!-- doxytag: member="CUPTI_ACTIVITY_STREAM_CREATE_FLAG_NON_BLOCKING" ref="gg10b985831ad4db3bd401953e999c877aa1dd7a9e2e04e1ee7987a4ff6eb48cff" args="" -->CUPTI_ACTIVITY_STREAM_CREATE_FLAG_NON_BLOCKING</em>&nbsp;</td><td>
Non-blocking stream. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10b985831ad4db3bd401953e999c877a6a2dc494836887e9b09193f95d1d2a58"></a><!-- doxytag: member="CUPTI_ACTIVITY_STREAM_CREATE_FLAG_NULL" ref="gg10b985831ad4db3bd401953e999c877a6a2dc494836887e9b09193f95d1d2a58" args="" -->CUPTI_ACTIVITY_STREAM_CREATE_FLAG_NULL</em>&nbsp;</td><td>
Null stream. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg10b985831ad4db3bd401953e999c877a7442c91de69f89372575011031bbd322"></a><!-- doxytag: member="CUPTI_ACTIVITY_STREAM_CREATE_MASK" ref="gg10b985831ad4db3bd401953e999c877a7442c91de69f89372575011031bbd322" args="" -->CUPTI_ACTIVITY_STREAM_CREATE_MASK</em>&nbsp;</td><td>
Stream create Mask </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g80e1eb47615e31021f574df8ebbe5d9a"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivitySynchronizationType" ref="g80e1eb47615e31021f574df8ebbe5d9a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g80e1eb47615e31021f574df8ebbe5d9a">CUpti_ActivitySynchronizationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The types of synchronization to be used with <a class="el" href="structCUpti__ActivitySynchronization.html" title="The activity record for synchronization management.">CUpti_ActivitySynchronization</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg80e1eb47615e31021f574df8ebbe5d9a0498f57ab29d3b29a99147e25bee1f85"></a><!-- doxytag: member="CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_UNKNOWN" ref="gg80e1eb47615e31021f574df8ebbe5d9a0498f57ab29d3b29a99147e25bee1f85" args="" -->CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_UNKNOWN</em>&nbsp;</td><td>
Unknown data. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg80e1eb47615e31021f574df8ebbe5d9adc8d6d127acb840d44e8184078e26dbd"></a><!-- doxytag: member="CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_EVENT_SYNCHRONIZE" ref="gg80e1eb47615e31021f574df8ebbe5d9adc8d6d127acb840d44e8184078e26dbd" args="" -->CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_EVENT_SYNCHRONIZE</em>&nbsp;</td><td>
Event synchronize API. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg80e1eb47615e31021f574df8ebbe5d9a0ff3988e29580f6fc4e463a9da776588"></a><!-- doxytag: member="CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_STREAM_WAIT_EVENT" ref="gg80e1eb47615e31021f574df8ebbe5d9a0ff3988e29580f6fc4e463a9da776588" args="" -->CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_STREAM_WAIT_EVENT</em>&nbsp;</td><td>
Stream wait event API. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg80e1eb47615e31021f574df8ebbe5d9add24d6847b9a948156064328912cc109"></a><!-- doxytag: member="CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_STREAM_SYNCHRONIZE" ref="gg80e1eb47615e31021f574df8ebbe5d9add24d6847b9a948156064328912cc109" args="" -->CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_STREAM_SYNCHRONIZE</em>&nbsp;</td><td>
Stream synchronize API. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg80e1eb47615e31021f574df8ebbe5d9a26b796c4c7eabdab1e0ad8896b34dd68"></a><!-- doxytag: member="CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_CONTEXT_SYNCHRONIZE" ref="gg80e1eb47615e31021f574df8ebbe5d9a26b796c4c7eabdab1e0ad8896b34dd68" args="" -->CUPTI_ACTIVITY_SYNCHRONIZATION_TYPE_CONTEXT_SYNCHRONIZE</em>&nbsp;</td><td>
Context synchronize API. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc6fcebeb84a89d8f1862d31338efd4c5"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityThreadIdType" ref="gc6fcebeb84a89d8f1862d31338efd4c5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">CUpti_ActivityThreadIdType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI uses different methods to obtain the thread-id depending on the support and the underlying platform. This enum documents these methods for each type. APIs <a class="el" href="group__CUPTI__ACTIVITY__API.html#g1821f090b841d60643ee37d977d9c64a">cuptiSetThreadIdType</a> and <a class="el" href="group__CUPTI__ACTIVITY__API.html#gbc957f426b741e46d6e9a99a43a974b5">cuptiGetThreadIdType</a> can be used to set and get the thread-id type. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggc6fcebeb84a89d8f1862d31338efd4c55f5057868d612674b75b4071567ed4d7"></a><!-- doxytag: member="CUPTI_ACTIVITY_THREAD_ID_TYPE_DEFAULT" ref="ggc6fcebeb84a89d8f1862d31338efd4c55f5057868d612674b75b4071567ed4d7" args="" -->CUPTI_ACTIVITY_THREAD_ID_TYPE_DEFAULT</em>&nbsp;</td><td>
Default type Windows uses API GetCurrentThreadId() Linux/Mac/Android/QNX use POSIX pthread API pthread_self() </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc6fcebeb84a89d8f1862d31338efd4c5d78df9968546a822f0178712c86b36f9"></a><!-- doxytag: member="CUPTI_ACTIVITY_THREAD_ID_TYPE_SYSTEM" ref="ggc6fcebeb84a89d8f1862d31338efd4c5d78df9968546a822f0178712c86b36f9" args="" -->CUPTI_ACTIVITY_THREAD_ID_TYPE_SYSTEM</em>&nbsp;</td><td>
This type is based on the system API available on the underlying platform and thread-id obtained is supposed to be unique for the process lifetime. Windows uses API GetCurrentThreadId() Linux uses syscall SYS_gettid Mac uses syscall SYS_thread_selfid Android/QNX use gettid() </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g58bc092ea88426687492fdea7b1c0ff3"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityUnifiedMemoryAccessType" ref="g58bc092ea88426687492fdea7b1c0ff3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g58bc092ea88426687492fdea7b1c0ff3">CUpti_ActivityUnifiedMemoryAccessType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is valid for <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa78249ecdbc6d0c9b52829c8bc276932b7bf">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT</a> and <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa78294faffc246c392748ab7f0de9a858b22">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT</a> <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg58bc092ea88426687492fdea7b1c0ff3bd4a26c06d8ceef0e5d2f8d3a0827fbe"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_UNKNOWN" ref="gg58bc092ea88426687492fdea7b1c0ff3bd4a26c06d8ceef0e5d2f8d3a0827fbe" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_UNKNOWN</em>&nbsp;</td><td>
The unified memory access type is not known </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg58bc092ea88426687492fdea7b1c0ff3999bc60af1f9f7fe5a1d6299e20c8181"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_READ" ref="gg58bc092ea88426687492fdea7b1c0ff3999bc60af1f9f7fe5a1d6299e20c8181" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_READ</em>&nbsp;</td><td>
The page fault was triggered by read memory instruction </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg58bc092ea88426687492fdea7b1c0ff3d81e561fa074e610ddbaa5567ee4162d"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_WRITE" ref="gg58bc092ea88426687492fdea7b1c0ff3d81e561fa074e610ddbaa5567ee4162d" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_WRITE</em>&nbsp;</td><td>
The page fault was triggered by write memory instruction </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg58bc092ea88426687492fdea7b1c0ff3569d2c161c404576bda5f42aa7f52b24"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_ATOMIC" ref="gg58bc092ea88426687492fdea7b1c0ff3569d2c161c404576bda5f42aa7f52b24" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_ATOMIC</em>&nbsp;</td><td>
The page fault was triggered by atomic memory instruction </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg58bc092ea88426687492fdea7b1c0ff3624b140a60e22a6b0f172aad9a77ee90"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_PREFETCH" ref="gg58bc092ea88426687492fdea7b1c0ff3624b140a60e22a6b0f172aad9a77ee90" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_ACCESS_TYPE_PREFETCH</em>&nbsp;</td><td>
The page fault was triggered by memory prefetch operation </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g601877eb6f7d248a5f538fd74b8fa782"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityUnifiedMemoryCounterKind" ref="g601877eb6f7d248a5f538fd74b8fa782" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Many activities are associated with Unified Memory mechanism; among them are tranfer from host to device, device to host, page fault at host side. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa7820bf7f03dd426a7768ae72a6f911992a7"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_UNKNOWN" ref="gg601877eb6f7d248a5f538fd74b8fa7820bf7f03dd426a7768ae72a6f911992a7" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_UNKNOWN</em>&nbsp;</td><td>
The unified memory counter kind is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa7827c4755228387e83351ae02b5ea5157f1"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD" ref="gg601877eb6f7d248a5f538fd74b8fa7827c4755228387e83351ae02b5ea5157f1" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD</em>&nbsp;</td><td>
Number of bytes transfered from host to device </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa782ef4bdc9da8f158bf337bdf2aedd8d780"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOH" ref="gg601877eb6f7d248a5f538fd74b8fa782ef4bdc9da8f158bf337bdf2aedd8d780" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOH</em>&nbsp;</td><td>
Number of bytes transfered from device to host </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa78294faffc246c392748ab7f0de9a858b22"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT" ref="gg601877eb6f7d248a5f538fd74b8fa78294faffc246c392748ab7f0de9a858b22" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT</em>&nbsp;</td><td>
Number of CPU page faults, this is only supported on 64 bit Linux and Mac platforms </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa78249ecdbc6d0c9b52829c8bc276932b7bf"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT" ref="gg601877eb6f7d248a5f538fd74b8fa78249ecdbc6d0c9b52829c8bc276932b7bf" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT</em>&nbsp;</td><td>
Number of GPU page faults, this is only supported on devices with compute capability 6.0 and higher and 64 bit Linux platforms </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa7827ae56c5c14c999bb8977ebc273161c6a"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING" ref="gg601877eb6f7d248a5f538fd74b8fa7827ae56c5c14c999bb8977ebc273161c6a" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING</em>&nbsp;</td><td>
Thrashing occurs when data is frequently accessed by multiple processors and has to be constantly migrated around to achieve data locality. In this case the overhead of migration may exceed the benefits of locality. This is only supported on 64 bit Linux platforms. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa7822275e1bca9aaf222a491c2599cdbca96"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING" ref="gg601877eb6f7d248a5f538fd74b8fa7822275e1bca9aaf222a491c2599cdbca96" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING</em>&nbsp;</td><td>
Throttling is a prevention technique used by the driver to avoid further thrashing. Here, the driver doesn't service the fault for one of the contending processors for a specific period of time, so that the other processor can run at full-speed. This is only supported on 64 bit Linux platforms. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa782d6e6079c49b8024aab5afd22c3ec1706"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP" ref="gg601877eb6f7d248a5f538fd74b8fa782d6e6079c49b8024aab5afd22c3ec1706" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP</em>&nbsp;</td><td>
In case throttling does not help, the driver tries to pin the memory to a processor for a specific period of time. One of the contending processors will have slow access to the memory, while the other will have fast access. This is only supported on 64 bit Linux platforms. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg601877eb6f7d248a5f538fd74b8fa7824fe8185509ddecb727197de672cb6e2b"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOD" ref="gg601877eb6f7d248a5f538fd74b8fa7824fe8185509ddecb727197de672cb6e2b" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOD</em>&nbsp;</td><td>
Number of bytes transferred from one device to another device. This is only supported on 64 bit Linux platforms. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gcf829db187f1553461cea1a6c9e6748b"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityUnifiedMemoryCounterScope" ref="gcf829db187f1553461cea1a6c9e6748b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">CUpti_ActivityUnifiedMemoryCounterScope</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggcf829db187f1553461cea1a6c9e6748bed53921f81c92d0688295502be796844"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_UNKNOWN" ref="ggcf829db187f1553461cea1a6c9e6748bed53921f81c92d0688295502be796844" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_UNKNOWN</em>&nbsp;</td><td>
The unified memory counter scope is not known. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcf829db187f1553461cea1a6c9e6748becdb22d1f83773f3a24801f679359cb3"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_PROCESS_SINGLE_DEVICE" ref="ggcf829db187f1553461cea1a6c9e6748becdb22d1f83773f3a24801f679359cb3" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_PROCESS_SINGLE_DEVICE</em>&nbsp;</td><td>
Collect unified memory counter for single process on one device </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcf829db187f1553461cea1a6c9e6748b3ba7e53206f5c3cdc85e1c8049df555c"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_PROCESS_ALL_DEVICES" ref="ggcf829db187f1553461cea1a6c9e6748b3ba7e53206f5c3cdc85e1c8049df555c" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_SCOPE_PROCESS_ALL_DEVICES</em>&nbsp;</td><td>
Collect unified memory counter for single process across all devices </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc5e53db4204f170a6c31e520e080c1fc"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityUnifiedMemoryMigrationCause" ref="gc5e53db4204f170a6c31e520e080c1fc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gc5e53db4204f170a6c31e520e080c1fc">CUpti_ActivityUnifiedMemoryMigrationCause</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is valid for <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa7827c4755228387e83351ae02b5ea5157f1">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD</a> and <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa782ef4bdc9da8f158bf337bdf2aedd8d780">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOH</a> <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggc5e53db4204f170a6c31e520e080c1fceaf33c52957361f6988fcba202469e9c"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_UNKNOWN" ref="ggc5e53db4204f170a6c31e520e080c1fceaf33c52957361f6988fcba202469e9c" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_UNKNOWN</em>&nbsp;</td><td>
The unified memory migration cause is not known </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc5e53db4204f170a6c31e520e080c1fc72842be49a8798e8f954ca353545b794"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_USER" ref="ggc5e53db4204f170a6c31e520e080c1fc72842be49a8798e8f954ca353545b794" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_USER</em>&nbsp;</td><td>
The unified memory migrated due to an explicit call from the user e.g. cudaMemPrefetchAsync </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc5e53db4204f170a6c31e520e080c1fcd6ef5866188f26c40cee666b7c5faf7b"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_COHERENCE" ref="ggc5e53db4204f170a6c31e520e080c1fcd6ef5866188f26c40cee666b7c5faf7b" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_COHERENCE</em>&nbsp;</td><td>
The unified memory migrated to guarantee data coherence e.g. CPU/GPU faults on Pascal+ and kernel launch on pre-Pascal GPUs </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc5e53db4204f170a6c31e520e080c1fca8ea9335e1e2f15d31ba4cf0f5a82722"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_PREFETCH" ref="ggc5e53db4204f170a6c31e520e080c1fca8ea9335e1e2f15d31ba4cf0f5a82722" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_PREFETCH</em>&nbsp;</td><td>
The unified memory was speculatively migrated by the UVM driver before being accessed by the destination processor to improve performance </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc5e53db4204f170a6c31e520e080c1fcf912bc57c1a6ac6ab5c60e3b7b237abf"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_EVICTION" ref="ggc5e53db4204f170a6c31e520e080c1fcf912bc57c1a6ac6ab5c60e3b7b237abf" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_EVICTION</em>&nbsp;</td><td>
The unified memory migrated to the CPU because it was evicted to make room for another block of memory on the GPU </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc5e53db4204f170a6c31e520e080c1fcaccf7ec09f1ac3e95108521f590f9c60"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_ACCESS_COUNTERS" ref="ggc5e53db4204f170a6c31e520e080c1fcaccf7ec09f1ac3e95108521f590f9c60" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_MIGRATION_CAUSE_ACCESS_COUNTERS</em>&nbsp;</td><td>
The unified memory migrated to another processor because of access counter notifications. Only frequently accessed pages are migrated between CPU and GPU, or between peer GPUs. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc38ad61408d643a789562314cd18d6b7"></a><!-- doxytag: member="cupti_activity.h::CUpti_ActivityUnifiedMemoryRemoteMapCause" ref="gc38ad61408d643a789562314cd18d6b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gc38ad61408d643a789562314cd18d6b7">CUpti_ActivityUnifiedMemoryRemoteMapCause</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is valid for <a class="el" href="group__CUPTI__ACTIVITY__API.html#gg601877eb6f7d248a5f538fd74b8fa782d6e6079c49b8024aab5afd22c3ec1706">CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP</a> <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggc38ad61408d643a789562314cd18d6b728dc6efe39dac1d16b12b28a74c5b1e1"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_UNKNOWN" ref="ggc38ad61408d643a789562314cd18d6b728dc6efe39dac1d16b12b28a74c5b1e1" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_UNKNOWN</em>&nbsp;</td><td>
The cause of mapping to remote memory was unknown </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc38ad61408d643a789562314cd18d6b7a78d260cad8ee26c26d32685c418fc17"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_COHERENCE" ref="ggc38ad61408d643a789562314cd18d6b7a78d260cad8ee26c26d32685c418fc17" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_COHERENCE</em>&nbsp;</td><td>
Mapping to remote memory was added to maintain data coherence. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc38ad61408d643a789562314cd18d6b7c25a1ba662111af949f5f0df78f09f1e"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_THRASHING" ref="ggc38ad61408d643a789562314cd18d6b7c25a1ba662111af949f5f0df78f09f1e" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_THRASHING</em>&nbsp;</td><td>
Mapping to remote memory was added to prevent further thrashing </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc38ad61408d643a789562314cd18d6b79b2a1361c4b217135b45d1bae02f9b92"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_POLICY" ref="ggc38ad61408d643a789562314cd18d6b79b2a1361c4b217135b45d1bae02f9b92" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_POLICY</em>&nbsp;</td><td>
Mapping to remote memory was added to enforce the hints specified by the programmer or by performance heuristics of the UVM driver </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc38ad61408d643a789562314cd18d6b7dab6db9f2025900838524e69d5a563a1"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_OUT_OF_MEMORY" ref="ggc38ad61408d643a789562314cd18d6b7dab6db9f2025900838524e69d5a563a1" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_OUT_OF_MEMORY</em>&nbsp;</td><td>
Mapping to remote memory was added because there is no more memory available on the processor and eviction was not possible </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc38ad61408d643a789562314cd18d6b71aa58222d3350f3d50d58f8f6ad18308"></a><!-- doxytag: member="CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_EVICTION" ref="ggc38ad61408d643a789562314cd18d6b71aa58222d3350f3d50d58f8f6ad18308" args="" -->CUPTI_ACTIVITY_UNIFIED_MEMORY_REMOTE_MAP_CAUSE_EVICTION</em>&nbsp;</td><td>
Mapping to remote memory was added after the memory was evicted to make room for another block of memory on the GPU </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g5ec1eab2a306637e8d49bcf8a678cc40"></a><!-- doxytag: member="cupti_activity.h::CUpti_DeviceVirtualizationMode" ref="g5ec1eab2a306637e8d49bcf8a678cc40" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g5ec1eab2a306637e8d49bcf8a678cc40">CUpti_DeviceVirtualizationMode</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This indicates the virtualization mode in which CUDA device is running <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg5ec1eab2a306637e8d49bcf8a678cc40fe6083edd2d97ae3328fcf166317993e"></a><!-- doxytag: member="CUPTI_DEVICE_VIRTUALIZATION_MODE_NONE" ref="gg5ec1eab2a306637e8d49bcf8a678cc40fe6083edd2d97ae3328fcf166317993e" args="" -->CUPTI_DEVICE_VIRTUALIZATION_MODE_NONE</em>&nbsp;</td><td>
No virtualization mode isassociated with the device i.e. it's a baremetal GPU </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg5ec1eab2a306637e8d49bcf8a678cc404649872b3b366bc04543d836725b46ca"></a><!-- doxytag: member="CUPTI_DEVICE_VIRTUALIZATION_MODE_PASS_THROUGH" ref="gg5ec1eab2a306637e8d49bcf8a678cc404649872b3b366bc04543d836725b46ca" args="" -->CUPTI_DEVICE_VIRTUALIZATION_MODE_PASS_THROUGH</em>&nbsp;</td><td>
The device is associated with the pass-through GPU. In this mode, an entire physical GPU is directly assigned to one virtual machine (VM). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg5ec1eab2a306637e8d49bcf8a678cc403d669bb81c0c454eb9cd347a725f6bf8"></a><!-- doxytag: member="CUPTI_DEVICE_VIRTUALIZATION_MODE_VIRTUAL_GPU" ref="gg5ec1eab2a306637e8d49bcf8a678cc403d669bb81c0c454eb9cd347a725f6bf8" args="" -->CUPTI_DEVICE_VIRTUALIZATION_MODE_VIRTUAL_GPU</em>&nbsp;</td><td>
The device is associated with the virtual GPU (vGPU). In this mode multiple virtual machines (VMs) have simultaneous, direct access to a single physical GPU. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd67907716d28ba51253f08d3e0bd0dda"></a><!-- doxytag: member="cupti_activity.h::CUpti_DevType" ref="gd67907716d28ba51253f08d3e0bd0dda" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd67907716d28ba51253f08d3e0bd0dda97d7ef27e4e54f6a72f558e34caff65d"></a><!-- doxytag: member="CUPTI_DEV_TYPE_GPU" ref="ggd67907716d28ba51253f08d3e0bd0dda97d7ef27e4e54f6a72f558e34caff65d" args="" -->CUPTI_DEV_TYPE_GPU</em>&nbsp;</td><td>
The device type is GPU. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd67907716d28ba51253f08d3e0bd0ddac666a1bd36bb23d1dace0a85af640554"></a><!-- doxytag: member="CUPTI_DEV_TYPE_NPU" ref="ggd67907716d28ba51253f08d3e0bd0ddac666a1bd36bb23d1dace0a85af640554" args="" -->CUPTI_DEV_TYPE_NPU</em>&nbsp;</td><td>
The device type is NVLink processing unit in CPU. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g3dcc6c3e9409a1183932febfdde58173"></a><!-- doxytag: member="cupti_activity.h::CUpti_EnvironmentClocksThrottleReason" ref="g3dcc6c3e9409a1183932febfdde58173" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g3dcc6c3e9409a1183932febfdde58173">CUpti_EnvironmentClocksThrottleReason</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The possible reasons that a clock can be throttled. There can be more than one reason that a clock is being throttled so these types can be combined by bitwise OR. These are used in the clocksThrottleReason field in the Environment Activity Record. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg3dcc6c3e9409a1183932febfdde58173e63f422f0ff685c257189084a31fdb40"></a><!-- doxytag: member="CUPTI_CLOCKS_THROTTLE_REASON_GPU_IDLE" ref="gg3dcc6c3e9409a1183932febfdde58173e63f422f0ff685c257189084a31fdb40" args="" -->CUPTI_CLOCKS_THROTTLE_REASON_GPU_IDLE</em>&nbsp;</td><td>
Nothing is running on the GPU and the clocks are dropping to idle state. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3dcc6c3e9409a1183932febfdde58173413948508232d2a798f77e01a065b6c1"></a><!-- doxytag: member="CUPTI_CLOCKS_THROTTLE_REASON_USER_DEFINED_CLOCKS" ref="gg3dcc6c3e9409a1183932febfdde58173413948508232d2a798f77e01a065b6c1" args="" -->CUPTI_CLOCKS_THROTTLE_REASON_USER_DEFINED_CLOCKS</em>&nbsp;</td><td>
The GPU clocks are limited by a user specified limit. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3dcc6c3e9409a1183932febfdde58173b102e3934593d0af7653bf24b9407b1c"></a><!-- doxytag: member="CUPTI_CLOCKS_THROTTLE_REASON_SW_POWER_CAP" ref="gg3dcc6c3e9409a1183932febfdde58173b102e3934593d0af7653bf24b9407b1c" args="" -->CUPTI_CLOCKS_THROTTLE_REASON_SW_POWER_CAP</em>&nbsp;</td><td>
A software power scaling algorithm is reducing the clocks below requested clocks. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3dcc6c3e9409a1183932febfdde5817349ee33b3ea489df857e1ff14973d3319"></a><!-- doxytag: member="CUPTI_CLOCKS_THROTTLE_REASON_HW_SLOWDOWN" ref="gg3dcc6c3e9409a1183932febfdde5817349ee33b3ea489df857e1ff14973d3319" args="" -->CUPTI_CLOCKS_THROTTLE_REASON_HW_SLOWDOWN</em>&nbsp;</td><td>
Hardware slowdown to reduce the clock by a factor of two or more is engaged. This is an indicator of one of the following: 1) Temperature is too high, 2) External power brake assertion is being triggered (e.g. by the system power supply), 3) Change in power state. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3dcc6c3e9409a1183932febfdde58173234a50aea43864aac11006ad86c4a22b"></a><!-- doxytag: member="CUPTI_CLOCKS_THROTTLE_REASON_UNKNOWN" ref="gg3dcc6c3e9409a1183932febfdde58173234a50aea43864aac11006ad86c4a22b" args="" -->CUPTI_CLOCKS_THROTTLE_REASON_UNKNOWN</em>&nbsp;</td><td>
Some unspecified factor is reducing the clocks. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3dcc6c3e9409a1183932febfdde5817344d0f920914e72d842ed2d2d88ab5492"></a><!-- doxytag: member="CUPTI_CLOCKS_THROTTLE_REASON_UNSUPPORTED" ref="gg3dcc6c3e9409a1183932febfdde5817344d0f920914e72d842ed2d2d88ab5492" args="" -->CUPTI_CLOCKS_THROTTLE_REASON_UNSUPPORTED</em>&nbsp;</td><td>
Throttle reason is not supported for this GPU. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3dcc6c3e9409a1183932febfdde58173b2eeca89cc2fe97895bae2047adc17f5"></a><!-- doxytag: member="CUPTI_CLOCKS_THROTTLE_REASON_NONE" ref="gg3dcc6c3e9409a1183932febfdde58173b2eeca89cc2fe97895bae2047adc17f5" args="" -->CUPTI_CLOCKS_THROTTLE_REASON_NONE</em>&nbsp;</td><td>
No clock throttling. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g9ac4ae6e6237e99db3f8b4c66df2f9aa"></a><!-- doxytag: member="cupti_activity.h::CUpti_ExternalCorrelationKind" ref="g9ac4ae6e6237e99db3f8b4c66df2f9aa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">CUpti_ExternalCorrelationKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Custom correlation kinds are reserved for usage in external tools.<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="structCUpti__ActivityExternalCorrelation.html" title="The activity record for correlation with external records.">CUpti_ActivityExternalCorrelation</a> </dd></dl>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg9ac4ae6e6237e99db3f8b4c66df2f9aaef14617c7678231aa5b32e85d23acb8a"></a><!-- doxytag: member="CUPTI_EXTERNAL_CORRELATION_KIND_UNKNOWN" ref="gg9ac4ae6e6237e99db3f8b4c66df2f9aaef14617c7678231aa5b32e85d23acb8a" args="" -->CUPTI_EXTERNAL_CORRELATION_KIND_UNKNOWN</em>&nbsp;</td><td>
The external API is unknown to CUPTI </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9ac4ae6e6237e99db3f8b4c66df2f9aa532f4536a1934e19424c7b9401290326"></a><!-- doxytag: member="CUPTI_EXTERNAL_CORRELATION_KIND_OPENACC" ref="gg9ac4ae6e6237e99db3f8b4c66df2f9aa532f4536a1934e19424c7b9401290326" args="" -->CUPTI_EXTERNAL_CORRELATION_KIND_OPENACC</em>&nbsp;</td><td>
The external API is OpenACC </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9ac4ae6e6237e99db3f8b4c66df2f9aabcdb78368dd97101f04b43f6f8c21109"></a><!-- doxytag: member="CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM0" ref="gg9ac4ae6e6237e99db3f8b4c66df2f9aabcdb78368dd97101f04b43f6f8c21109" args="" -->CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM0</em>&nbsp;</td><td>
The external API is custom0 </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9ac4ae6e6237e99db3f8b4c66df2f9aa518b903d84e83f8d7229494931b3cc1a"></a><!-- doxytag: member="CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM1" ref="gg9ac4ae6e6237e99db3f8b4c66df2f9aa518b903d84e83f8d7229494931b3cc1a" args="" -->CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM1</em>&nbsp;</td><td>
The external API is custom1 </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9ac4ae6e6237e99db3f8b4c66df2f9aa8f3d574b27a8636def43becc25ce2a0d"></a><!-- doxytag: member="CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM2" ref="gg9ac4ae6e6237e99db3f8b4c66df2f9aa8f3d574b27a8636def43becc25ce2a0d" args="" -->CUPTI_EXTERNAL_CORRELATION_KIND_CUSTOM2</em>&nbsp;</td><td>
The external API is custom2 </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9ac4ae6e6237e99db3f8b4c66df2f9aac068de68a0f566e64a4954db5afe4b85"></a><!-- doxytag: member="CUPTI_EXTERNAL_CORRELATION_KIND_SIZE" ref="gg9ac4ae6e6237e99db3f8b4c66df2f9aac068de68a0f566e64a4954db5afe4b85" args="" -->CUPTI_EXTERNAL_CORRELATION_KIND_SIZE</em>&nbsp;</td><td>
Add new kinds before this line </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6662b8786c121eed35068b4c1cc931ab"></a><!-- doxytag: member="cupti_activity.h::CUpti_FuncShmemLimitConfig" ref="g6662b8786c121eed35068b4c1cc931ab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg6662b8786c121eed35068b4c1cc931ab225c2c7345a27de6e296fae703992c2f"></a><!-- doxytag: member="CUPTI_FUNC_SHMEM_LIMIT_DEFAULT" ref="gg6662b8786c121eed35068b4c1cc931ab225c2c7345a27de6e296fae703992c2f" args="" -->CUPTI_FUNC_SHMEM_LIMIT_DEFAULT</em>&nbsp;</td><td>
The shared memory limit config is default </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6662b8786c121eed35068b4c1cc931ab5602c54f716f2fa86107e91e592329d7"></a><!-- doxytag: member="CUPTI_FUNC_SHMEM_LIMIT_OPTIN" ref="gg6662b8786c121eed35068b4c1cc931ab5602c54f716f2fa86107e91e592329d7" args="" -->CUPTI_FUNC_SHMEM_LIMIT_OPTIN</em>&nbsp;</td><td>
User has opted for a higher dynamic shared memory limit using function attribute 'cudaFuncAttributeMaxDynamicSharedMemorySize' for runtime API or CU_FUNC_ATTRIBUTE_MAX_DYNAMIC_SHARED_SIZE_BYTES for driver API </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g819d686d23d2a3dfed9cc1d3c4ecb162"></a><!-- doxytag: member="cupti_activity.h::CUpti_LinkFlag" ref="g819d686d23d2a3dfed9cc1d3c4ecb162" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g819d686d23d2a3dfed9cc1d3c4ecb162">CUpti_LinkFlag</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Describes link properties, to be used with <a class="el" href="structCUpti__ActivityNvLink.html" title="NVLink information. (deprecated in CUDA 9.0).">CUpti_ActivityNvLink</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg819d686d23d2a3dfed9cc1d3c4ecb1621c1b0bc894a29b98d0700b5eb0bc6cfc"></a><!-- doxytag: member="CUPTI_LINK_FLAG_INVALID" ref="gg819d686d23d2a3dfed9cc1d3c4ecb1621c1b0bc894a29b98d0700b5eb0bc6cfc" args="" -->CUPTI_LINK_FLAG_INVALID</em>&nbsp;</td><td>
The flag is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg819d686d23d2a3dfed9cc1d3c4ecb16250a2b8823e88498e98c9f8ee7f4563a8"></a><!-- doxytag: member="CUPTI_LINK_FLAG_PEER_ACCESS" ref="gg819d686d23d2a3dfed9cc1d3c4ecb16250a2b8823e88498e98c9f8ee7f4563a8" args="" -->CUPTI_LINK_FLAG_PEER_ACCESS</em>&nbsp;</td><td>
Is peer to peer access supported by this link. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg819d686d23d2a3dfed9cc1d3c4ecb1622be6a457d2e2bca9839f4e15b8d93651"></a><!-- doxytag: member="CUPTI_LINK_FLAG_SYSMEM_ACCESS" ref="gg819d686d23d2a3dfed9cc1d3c4ecb1622be6a457d2e2bca9839f4e15b8d93651" args="" -->CUPTI_LINK_FLAG_SYSMEM_ACCESS</em>&nbsp;</td><td>
Is system memory access supported by this link. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg819d686d23d2a3dfed9cc1d3c4ecb1627c94f9434f034cb76be1cbf078d20340"></a><!-- doxytag: member="CUPTI_LINK_FLAG_PEER_ATOMICS" ref="gg819d686d23d2a3dfed9cc1d3c4ecb1627c94f9434f034cb76be1cbf078d20340" args="" -->CUPTI_LINK_FLAG_PEER_ATOMICS</em>&nbsp;</td><td>
Is peer atomic access supported by this link. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg819d686d23d2a3dfed9cc1d3c4ecb1624760f46d7c14118420c5a608532553db"></a><!-- doxytag: member="CUPTI_LINK_FLAG_SYSMEM_ATOMICS" ref="gg819d686d23d2a3dfed9cc1d3c4ecb1624760f46d7c14118420c5a608532553db" args="" -->CUPTI_LINK_FLAG_SYSMEM_ATOMICS</em>&nbsp;</td><td>
Is system memory atomic access supported by this link. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0e638b0b6a210164345ab159bcba6717"></a><!-- doxytag: member="cupti_activity.h::CUpti_OpenAccEventKind" ref="g0e638b0b6a210164345ab159bcba6717" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd>CUpti_ActivityKindOpenAcc </dd></dl>

</div>
</div><p>
<a class="anchor" name="g21881d93eb4622a660f0ebd8e2fd2bab"></a><!-- doxytag: member="cupti_activity.h::CUpti_PcieDeviceType" ref="g21881d93eb4622a660f0ebd8e2fd2bab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g21881d93eb4622a660f0ebd8e2fd2bab">CUpti_PcieDeviceType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Field to differentiate whether PCIE Activity record is of a GPU or a PCI Bridge <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg21881d93eb4622a660f0ebd8e2fd2bab41ddedbc6b2ebbf19911530148f2416d"></a><!-- doxytag: member="CUPTI_PCIE_DEVICE_TYPE_GPU" ref="gg21881d93eb4622a660f0ebd8e2fd2bab41ddedbc6b2ebbf19911530148f2416d" args="" -->CUPTI_PCIE_DEVICE_TYPE_GPU</em>&nbsp;</td><td>
PCIE GPU record </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg21881d93eb4622a660f0ebd8e2fd2bab95dbeeedd98f35e0987137bdabfad0cd"></a><!-- doxytag: member="CUPTI_PCIE_DEVICE_TYPE_BRIDGE" ref="gg21881d93eb4622a660f0ebd8e2fd2bab95dbeeedd98f35e0987137bdabfad0cd" args="" -->CUPTI_PCIE_DEVICE_TYPE_BRIDGE</em>&nbsp;</td><td>
PCIE Bridge record </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g3cc86677bf301c75dc24ed2adef85145"></a><!-- doxytag: member="cupti_activity.h::CUpti_PcieGen" ref="g3cc86677bf301c75dc24ed2adef85145" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#g3cc86677bf301c75dc24ed2adef85145">CUpti_PcieGen</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enumeration of PCIE Generation for pcie activity attribute pcieGeneration <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg3cc86677bf301c75dc24ed2adef851453f2e4f0d700c47cc44959cb6b05ae3c1"></a><!-- doxytag: member="CUPTI_PCIE_GEN_GEN1" ref="gg3cc86677bf301c75dc24ed2adef851453f2e4f0d700c47cc44959cb6b05ae3c1" args="" -->CUPTI_PCIE_GEN_GEN1</em>&nbsp;</td><td>
PCIE Generation 1 </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3cc86677bf301c75dc24ed2adef851450c3d2f67c924c6067ace618782d59998"></a><!-- doxytag: member="CUPTI_PCIE_GEN_GEN2" ref="gg3cc86677bf301c75dc24ed2adef851450c3d2f67c924c6067ace618782d59998" args="" -->CUPTI_PCIE_GEN_GEN2</em>&nbsp;</td><td>
PCIE Generation 2 </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3cc86677bf301c75dc24ed2adef85145004e58ac7e3b682963a133293850cce2"></a><!-- doxytag: member="CUPTI_PCIE_GEN_GEN3" ref="gg3cc86677bf301c75dc24ed2adef85145004e58ac7e3b682963a133293850cce2" args="" -->CUPTI_PCIE_GEN_GEN3</em>&nbsp;</td><td>
PCIE Generation 3 </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3cc86677bf301c75dc24ed2adef85145d7bd860d830ab2dc50a1b31453f8184c"></a><!-- doxytag: member="CUPTI_PCIE_GEN_GEN4" ref="gg3cc86677bf301c75dc24ed2adef85145d7bd860d830ab2dc50a1b31453f8184c" args="" -->CUPTI_PCIE_GEN_GEN4</em>&nbsp;</td><td>
PCIE Generation 4 </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg3cc86677bf301c75dc24ed2adef85145d91af313c241350864584e7a9289c471"></a><!-- doxytag: member="CUPTI_PCIE_GEN_GEN5" ref="gg3cc86677bf301c75dc24ed2adef85145d91af313c241350864584e7a9289c471" args="" -->CUPTI_PCIE_GEN_GEN5</em>&nbsp;</td><td>
PCIE Generation 5 </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g115305aadc838df99d88283fc64c4317"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityConfigurePCSampling" ref="g115305aadc838df99d88283fc64c4317" args="(CUcontext ctx, CUpti_ActivityPCSamplingConfig *config)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityConfigurePCSampling           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structCUpti__ActivityPCSamplingConfig.html">CUpti_ActivityPCSamplingConfig</a> *&nbsp;</td>
          <td class="paramname"> <em>config</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For Pascal and older GPU architectures this API must be called before enabling activity kind CUPTI_ACTIVITY_KIND_PC_SAMPLING. There is no such requirement for Volta and newer GPU architectures.<p>
For Volta and newer GPU architectures if this API is called in the middle of execution, PC sampling configuration will be updated for subsequent kernel launches.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>ctx</em>&nbsp;</td><td>The context </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>config</em>&nbsp;</td><td>A pointer to <a class="el" href="structCUpti__ActivityPCSamplingConfig.html">CUpti_ActivityPCSamplingConfig</a> structure containing PC sampling configuration.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if this api is called while some valid event collection method is set. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>config</code> is NULL or any parameter in the <code>config</code> structures is not a valid value </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>Indicates that the system/device does not support the unified memory counters </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g8ec9b1229ba07a98aac9db4600d4325c"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityConfigureUnifiedMemoryCounter" ref="g8ec9b1229ba07a98aac9db4600d4325c" args="(CUpti_ActivityUnifiedMemoryCounterConfig *config, uint32_t count)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityConfigureUnifiedMemoryCounter           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html">CUpti_ActivityUnifiedMemoryCounterConfig</a> *&nbsp;</td>
          <td class="paramname"> <em>config</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>count</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>config</em>&nbsp;</td><td>A pointer to <a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html">CUpti_ActivityUnifiedMemoryCounterConfig</a> structures containing Unified Memory counter configuration. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>count</em>&nbsp;</td><td>Number of Unified Memory counter configuration structures</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>config</code> is NULL or any parameter in the <code>config</code> structures is not a valid value </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED</em>&nbsp;</td><td>One potential reason is that platform (OS/arch) does not support the unified memory counters </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_DEVICE</em>&nbsp;</td><td>Indicates that the device does not support the unified memory counters </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UM_PROFILING_NOT_SUPPORTED_ON_NON_P2P_DEVICES</em>&nbsp;</td><td>Indicates that multi-GPU configuration without P2P support between any pair of devices does not support the unified memory counters </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gbc086f5a89450f4e3b75c6f6832f569c"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityDisable" ref="gbc086f5a89450f4e3b75c6f6832f569c" args="(CUpti_ActivityKind kind)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityDisable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Disable collection of a specific kind of activity record. Multiple kinds can be disabled by calling this function multiple times. By default all activity kinds are disabled for collection.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>kind</em>&nbsp;</td><td>The kind of activity record to stop collecting</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_KIND</em>&nbsp;</td><td>if the activity kind is not supported </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g90d628b01bcc8044b86060e8a1ff7457"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityDisableContext" ref="g90d628b01bcc8044b86060e8a1ff7457" args="(CUcontext context, CUpti_ActivityKind kind)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityDisableContext           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Disable collection of a specific kind of activity record for a context. This setting done by this API will supersede the global settings for activity records. Multiple kinds can be enabled by calling this function multiple times.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context for which activity is to be disabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>kind</em>&nbsp;</td><td>The kind of activity record to stop collecting</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_KIND</em>&nbsp;</td><td>if the activity kind is not supported </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g348cf81393b39ab2f89604aaaa8defc2"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityEnable" ref="g348cf81393b39ab2f89604aaaa8defc2" args="(CUpti_ActivityKind kind)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityEnable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable collection of a specific kind of activity record. Multiple kinds can be enabled by calling this function multiple times. By default all activity kinds are disabled for collection.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>kind</em>&nbsp;</td><td>The kind of activity record to collect</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>if the activity kind cannot be enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_KIND</em>&nbsp;</td><td>if the activity kind is not supported </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g12080fe6fdacf80869db472e41b98027"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityEnableAndDump" ref="g12080fe6fdacf80869db472e41b98027" args="(CUpti_ActivityKind kind)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityEnableAndDump           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
In general, the behavior of this API is similar to the API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g348cf81393b39ab2f89604aaaa8defc2">cuptiActivityEnable</a> i.e. it enables the collection of a specific kind of activity record. Additionally, this API can help in dumping the records for activities which happened in the past before enabling the corresponding activity kind. The API allows to get records for the current resource allocations done in CUDA For CUPTI_ACTIVITY_KIND_DEVICE, existing device records are dumped For CUPTI_ACTIVITY_KIND_CONTEXT, existing context records are dumped For CUPTI_ACTIVITY_KIND_STREAM, existing stream records are dumped For CUPTI_ACTIVITY_KIND_ NVLINK, existing NVLINK records are dumped For CUPTI_ACTIVITY_KIND_PCIE, existing PCIE records are dumped For other activities, the behavior is similar to the API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g348cf81393b39ab2f89604aaaa8defc2">cuptiActivityEnable</a><p>
Device records are emitted in CUPTI on CUDA driver initialization. Those records can only be retrieved by the user if CUPTI is attached before CUDA initialization. Context and stream records are emitted on context and stream creation. The use case of the API is to provide the records for CUDA resources (contexs/streams/devices) that are currently active if user late attaches CUPTI.<p>
Before calling this function, the user must register buffer callbacks to get the activity records by calling <a class="el" href="group__CUPTI__ACTIVITY__API.html#g237e2401b0ce69dcb265b1f9079f0b65">cuptiActivityRegisterCallbacks</a>. If the user does not register the buffers and calls API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g12080fe6fdacf80869db472e41b98027">cuptiActivityEnableAndDump</a>, then CUPTI will enable the activity kind but not provide any records for that activity kind.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>kind</em>&nbsp;</td><td>The kind of activity record to collect</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UNKNOWN</em>&nbsp;</td><td>if buffer is not initialized. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>if the activity kind cannot be enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_KIND</em>&nbsp;</td><td>if the activity kind is not supported </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2e2ee46c089fa139481e434f1b0f4cfc"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityEnableContext" ref="g2e2ee46c089fa139481e434f1b0f4cfc" args="(CUcontext context, CUpti_ActivityKind kind)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityEnableContext           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable collection of a specific kind of activity record for a context. This setting done by this API will supersede the global settings for activity records enabled by <a class="el" href="group__CUPTI__ACTIVITY__API.html#g348cf81393b39ab2f89604aaaa8defc2">cuptiActivityEnable</a>. Multiple kinds can be enabled by calling this function multiple times.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context for which activity is to be enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>kind</em>&nbsp;</td><td>The kind of activity record to collect</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>if the activity kind cannot be enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_KIND</em>&nbsp;</td><td>if the activity kind is not supported </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g4b50c0c1634913f8157cfcfe84f19fa9"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityEnableLatencyTimestamps" ref="g4b50c0c1634913f8157cfcfe84f19fa9" args="(uint8_t enable)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityEnableLatencyTimestamps           </td>
          <td>(</td>
          <td class="paramtype">uint8_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API is used to control the collection of queued and submitted timestamps for kernels whose records are provided through the struct <a class="el" href="structCUpti__ActivityKernel9.html">CUpti_ActivityKernel9</a>. Default value is 0, i.e. these timestamps are not collected. This API needs to be called before initialization of CUDA and this setting should not be changed during the profiling session.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>is a boolean, denoting whether these timestamps should be collected</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g5fc8cdb0ce9c2cb08a990d26c700f969"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityEnableLaunchAttributes" ref="g5fc8cdb0ce9c2cb08a990d26c700f969" args="(uint8_t enable)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityEnableLaunchAttributes           </td>
          <td>(</td>
          <td class="paramtype">uint8_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API is used to control the collection of launch attributes for kernels whose records are provided through the struct <a class="el" href="structCUpti__ActivityKernel9.html">CUpti_ActivityKernel9</a>. Default value is 0, i.e. these attributes are not collected.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>is a boolean denoting whether these launch attributes should be collected </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g805065d34981e9284b9943e753b20122"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityFlush" ref="g805065d34981e9284b9943e753b20122" args="(CUcontext context, uint32_t streamId, uint32_t flag)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityFlush           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>streamId</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>flag</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function does not return until all activity records associated with the specified context/stream are returned to the CUPTI client using the callback registered in cuptiActivityRegisterCallbacks. To ensure that all activity records are complete, the requested stream(s), if any, are synchronized.<p>
If <code>context</code> is NULL, the global activity records (i.e. those not associated with a particular stream) are flushed (in this case no streams are synchonized). If <code>context</code> is a valid CUcontext and <code>streamId</code> is 0, the buffers of all streams of this context are flushed. Otherwise, the buffers of the specified stream in this context is flushed.<p>
Before calling this function, the buffer handling callback api must be activated by calling cuptiActivityRegisterCallbacks.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>A valid CUcontext or NULL. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>streamId</em>&nbsp;</td><td>The stream ID. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>flag</em>&nbsp;</td><td>The flag can be set to indicate a forced flush. See CUpti_ActivityFlag</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if not preceeded by a successful call to cuptiActivityRegisterCallbacks </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UNKNOWN</em>&nbsp;</td><td>an internal error occurred</td></tr>
  </table>
</dl>
**DEPRECATED** This method is deprecated CONTEXT and STREAMID will be ignored. Use cuptiActivityFlushAll to flush all data. 
</div>
</div><p>
<a class="anchor" name="gbae7160b2db7e97247a0d23812e373eb"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityFlushAll" ref="gbae7160b2db7e97247a0d23812e373eb" args="(uint32_t flag)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityFlushAll           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>flag</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function returns the activity records associated with all contexts/streams (and the global buffers not associated with any stream) to the CUPTI client using the callback registered in cuptiActivityRegisterCallbacks.<p>
This is a blocking call but it doesn't issue any CUDA synchronization calls implicitly thus it's not guaranteed that all activities are completed on the underlying devices. Activity record is considered as completed if it has all the information filled up including the timestamps if any. It is the client's responsibility to issue necessary CUDA synchronization calls before calling this function if all activity records with complete information are expected to be delivered.<p>
Behavior of the function based on the input flag:<ul>
<li>For default flush i.e. when flag is set as 0, it returns all the activity buffers which have all the activity records completed, buffers need not to be full though. It doesn't return buffers which have one or more incomplete records. Default flush can be done at a regular interval in a separate thread.</li><li>For forced flush i.e. when flag CUPTI_ACTIVITY_FLAG_FLUSH_FORCED is passed to the function, it returns all the activity buffers including the ones which have one or more incomplete activity records. It's suggested for clients to do the force flush before the termination of the profiling session to allow remaining buffers to be delivered. In general, it can be done in the at-exit handler.</li></ul>
<p>
Before calling this function, the buffer handling callback api must be activated by calling cuptiActivityRegisterCallbacks.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>flag</em>&nbsp;</td><td>The flag can be set to indicate a forced flush. See CUpti_ActivityFlag</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if not preceeded by a successful call to cuptiActivityRegisterCallbacks </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UNKNOWN</em>&nbsp;</td><td>an internal error occurred</td></tr>
  </table>
</dl>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gded430c34c6b7dbf935377c65c7d6491" title="Sets the flush period for the worker thread.">cuptiActivityFlushPeriod</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="gded430c34c6b7dbf935377c65c7d6491"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityFlushPeriod" ref="gded430c34c6b7dbf935377c65c7d6491" args="(uint32_t time)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityFlushPeriod           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>time</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI creates a worker thread to minimize the perturbance for the application created threads. CUPTI offloads certain operations from the application threads to the worker thread, this includes synchronization of profiling resources between host and device, delivery of the activity buffers to the client using the callback registered in cuptiActivityRegisterCallbacks. For performance reasons, CUPTI wakes up the worker thread based on certain heuristics.<p>
This API is used to control the flush period of the worker thread. This setting will override the CUPTI heurtistics. Setting time to zero disables the periodic flush and restores the default behavior.<p>
Periodic flush can return only those activity buffers which are full and have all the activity records completed.<p>
It's allowed to use the API <a class="el" href="group__CUPTI__ACTIVITY__API.html#gbae7160b2db7e97247a0d23812e373eb">cuptiActivityFlushAll</a> to flush the data on-demand, even when client sets the periodic flush.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>time</em>&nbsp;</td><td>flush period in msec</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
  </table>
</dl>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gbae7160b2db7e97247a0d23812e373eb" title="Request to deliver activity records via the buffer completion callback.">cuptiActivityFlushAll</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="g92859af3b4bc569577d386dea71404b2"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityGetAttribute" ref="g92859af3b4bc569577d386dea71404b2" args="(CUpti_ActivityAttribute attr, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityGetAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g1c31fe3f8ea0e46c6c20dd454a6caab6">CUpti_ActivityAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Read an activity API attribute and return it in <code>*value</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>attr</em>&nbsp;</td><td>The attribute to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>Size of buffer pointed by the value, and returns the number of bytes written to <code>value</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>Returns the value of the attribute</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attr</code> is not an activity attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>Indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gb397f490a0df4a1633ea7b6e2420294f"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityGetNextRecord" ref="gb397f490a0df4a1633ea7b6e2420294f" args="(uint8_t *buffer, size_t validBufferSizeBytes, CUpti_Activity **record)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityGetNextRecord           </td>
          <td>(</td>
          <td class="paramtype">uint8_t *&nbsp;</td>
          <td class="paramname"> <em>buffer</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>validBufferSizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structCUpti__Activity.html">CUpti_Activity</a> **&nbsp;</td>
          <td class="paramname"> <em>record</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is a helper function to iterate over the activity records in a buffer. A buffer of activity records is typically obtained by receiving a CUpti_BuffersCallbackCompleteFunc callback.<p>
An example of typical usage: <div class="fragment"><pre class="fragment"> <a class="code" href="structCUpti__Activity.html" title="The base activity record.">CUpti_Activity</a> *record = NULL;
 <a class="code" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714" title="CUPTI result codes.">CUptiResult</a> status = <a class="code" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871460bd0257372573920d9bb2c802ce3b71">CUPTI_SUCCESS</a>;
   <span class="keywordflow">do</span> {
      status = <a class="code" href="group__CUPTI__ACTIVITY__API.html#gb397f490a0df4a1633ea7b6e2420294f" title="Iterate over the activity records in a buffer.">cuptiActivityGetNextRecord</a>(buffer, validSize, &amp;record);
      <span class="keywordflow">if</span>(status == <a class="code" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c8871460bd0257372573920d9bb2c802ce3b71">CUPTI_SUCCESS</a>) {
           <span class="comment">// Use record here...</span>
      }
      <span class="keywordflow">else</span> <span class="keywordflow">if</span> (status == <a class="code" href="group__CUPTI__RESULT__API.html#gg8c54bf95108e67d858f37fcf76c88714a6d97464748158d2c5e45eeac821d1da">CUPTI_ERROR_MAX_LIMIT_REACHED</a>)
          <span class="keywordflow">break</span>;
      <span class="keywordflow">else</span> {
          <span class="keywordflow">goto</span> Error;
      }
    } <span class="keywordflow">while</span> (1);
</pre></div><p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>buffer</em>&nbsp;</td><td>The buffer containing activity records </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>record</em>&nbsp;</td><td>Inputs the previous record returned by cuptiActivityGetNextRecord and returns the next activity record from the buffer. If input value is NULL, returns the first activity record in the buffer. Records of kind CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL may contain invalid (0) timestamps, indicating that no timing information could be collected for lack of device memory. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>validBufferSizeBytes</em>&nbsp;</td><td>The number of valid bytes in the buffer.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_MAX_LIMIT_REACHED</em>&nbsp;</td><td>if no more records in the buffer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>buffer</code> is NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g496d13eb1f4f4fcce37ce3f3434c5e4a"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityGetNumDroppedRecords" ref="g496d13eb1f4f4fcce37ce3f3434c5e4a" args="(CUcontext context, uint32_t streamId, size_t *dropped)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityGetNumDroppedRecords           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>streamId</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>dropped</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Get the number of records that were dropped because of insufficient buffer space. The dropped count includes records that could not be recorded because CUPTI did not have activity buffer space available for the record (because the CUpti_BuffersCallbackRequestFunc callback did not return an empty buffer of sufficient size) and also CDP records that could not be record because the device-size buffer was full (size is controlled by the CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE_CDP attribute). The dropped count maintained for the queue is reset to zero when this function is called.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context, or NULL to get dropped count from global queue </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>streamId</em>&nbsp;</td><td>The stream ID </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>dropped</em>&nbsp;</td><td>The number of records that were dropped since the last call to this function.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>dropped</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g47395bf12ff55f30822d408b940567e3"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityPopExternalCorrelationId" ref="g47395bf12ff55f30822d408b940567e3" args="(CUpti_ExternalCorrelationKind kind, uint64_t *lastId)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityPopExternalCorrelationId           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">CUpti_ExternalCorrelationKind</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>lastId</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function notifies CUPTI that the calling thread is leaving an external API region.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>kind</em>&nbsp;</td><td>The kind of external API activities should be correlated with. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>lastId</em>&nbsp;</td><td>If the function returns successful, contains the last external correlation id for this <code>kind</code>, can be NULL.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>The external API kind is invalid. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_QUEUE_EMPTY</em>&nbsp;</td><td>No external id is currently associated with <code>kind</code>. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2c373f1be967db0227fa4d42a593d1a0"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityPushExternalCorrelationId" ref="g2c373f1be967db0227fa4d42a593d1a0" args="(CUpti_ExternalCorrelationKind kind, uint64_t id)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityPushExternalCorrelationId           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">CUpti_ExternalCorrelationKind</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&nbsp;</td>
          <td class="paramname"> <em>id</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function notifies CUPTI that the calling thread is entering an external API region. When a CUPTI activity API record is created while within an external API region and CUPTI_ACTIVITY_KIND_EXTERNAL_CORRELATION is enabled, the activity API record will be preceeded by a <a class="el" href="structCUpti__ActivityExternalCorrelation.html" title="The activity record for correlation with external records.">CUpti_ActivityExternalCorrelation</a> record for each <a class="el" href="group__CUPTI__ACTIVITY__API.html#g9ac4ae6e6237e99db3f8b4c66df2f9aa">CUpti_ExternalCorrelationKind</a>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>kind</em>&nbsp;</td><td>The kind of external API activities should be correlated with. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>id</em>&nbsp;</td><td>External correlation id.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>The external API kind is invalid </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g237e2401b0ce69dcb265b1f9079f0b65"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityRegisterCallbacks" ref="g237e2401b0ce69dcb265b1f9079f0b65" args="(CUpti_BuffersCallbackRequestFunc funcBufferRequested, CUpti_BuffersCallbackCompleteFunc funcBufferCompleted)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityRegisterCallbacks           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g3337d3cbfe624b05d4af435fd17e5b28">CUpti_BuffersCallbackRequestFunc</a>&nbsp;</td>
          <td class="paramname"> <em>funcBufferRequested</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g081fec67efc2c2504362e8e34fce7e6e">CUpti_BuffersCallbackCompleteFunc</a>&nbsp;</td>
          <td class="paramname"> <em>funcBufferCompleted</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function registers two callback functions to be used in asynchronous buffer handling. If registered, activity record buffers are handled using asynchronous requested/completed callbacks from CUPTI.<p>
Registering these callbacks prevents the client from using CUPTI's blocking enqueue/dequeue functions.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>funcBufferRequested</em>&nbsp;</td><td>callback which is invoked when an empty buffer is requested by CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>funcBufferCompleted</em>&nbsp;</td><td>callback which is invoked when a buffer containing activity records is available from CUPTI</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if either <code>funcBufferRequested</code> or <code>funcBufferCompleted</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc168c4a9b5f4e9bd7553ca26118a307d"></a><!-- doxytag: member="cupti_activity.h::cuptiActivityRegisterTimestampCallback" ref="gc168c4a9b5f4e9bd7553ca26118a307d" args="(CUpti_TimestampCallbackFunc funcTimestamp)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivityRegisterTimestampCallback           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gdd850477a3ae876889eb8dae31c8c664">CUpti_TimestampCallbackFunc</a>&nbsp;</td>
          <td class="paramname"> <em>funcTimestamp</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function registers a callback function to obtain timestamp of user's choice instead of using CUPTI provided timestamp. By default CUPTI uses different methods, based on the underlying platform, to retrieve the timestamp Linux and Android use clock_gettime(CLOCK_REALTIME, ..) Windows uses QueryPerformanceCounter() Mac uses mach_absolute_time() QNX uses ClockCycles() Timestamps retrieved using these methods are converted to nanosecond if needed before usage.<p>
The registration of timestamp callback should be done before any of the CUPTI activity kinds are enabled to make sure that all the records report the timestamp using the callback function registered through cuptiActivityRegisterTimestampCallback API.<p>
Changing the timestamp callback function in CUPTI through cuptiActivityRegisterTimestampCallback API in the middle of the profiling session can cause records generated prior to the change to report timestamps through previous timestamp method.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>funcTimestamp</em>&nbsp;</td><td>callback which is invoked when a timestamp is needed by CUPTI</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>funcTimestamp</code> is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="ga4ce1e3f22626c5c2a8b450a996fe580"></a><!-- doxytag: member="cupti_activity.h::cuptiActivitySetAttribute" ref="ga4ce1e3f22626c5c2a8b450a996fe580" args="(CUpti_ActivityAttribute attr, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiActivitySetAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g1c31fe3f8ea0e46c6c20dd454a6caab6">CUpti_ActivityAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Write an activity API attribute.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>attr</em>&nbsp;</td><td>The attribute to write </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>The size, in bytes, of the value </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>The attribute value to write</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attr</code> is not an activity attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>Indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g22c5ce610ffbf5940b7c05be54fc813d"></a><!-- doxytag: member="cupti_activity.h::cuptiComputeCapabilitySupported" ref="g22c5ce610ffbf5940b7c05be54fc813d" args="(int major, int minor, int *support)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiComputeCapabilitySupported           </td>
          <td>(</td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>major</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>minor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&nbsp;</td>
          <td class="paramname"> <em>support</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function is used to check the support for a device based on it's compute capability. It sets the <code>support</code> when the compute capability is supported by the current version of CUPTI, and clears it otherwise. This version of CUPTI might not support all GPUs sharing the same compute capability. It is suggested to use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g2493c952b9ceccf953ade5a6816fefdb">cuptiDeviceSupported</a> which provides correct information.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>major</em>&nbsp;</td><td>The major revision number of the compute capability </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>minor</em>&nbsp;</td><td>The minor revision number of the compute capability </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>support</em>&nbsp;</td><td>Pointer to an integer to return the support status</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>support</code> is NULL</td></tr>
  </table>
</dl>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g2493c952b9ceccf953ade5a6816fefdb" title="Check support for a compute device.">cuptiDeviceSupported</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="g2493c952b9ceccf953ade5a6816fefdb"></a><!-- doxytag: member="cupti_activity.h::cuptiDeviceSupported" ref="g2493c952b9ceccf953ade5a6816fefdb" args="(CUdevice dev, int *support)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceSupported           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>dev</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&nbsp;</td>
          <td class="paramname"> <em>support</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function is used to check the support for a compute device. It sets the <code>support</code> when the device is supported by the current version of CUPTI, and clears it otherwise.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>dev</em>&nbsp;</td><td>The device handle returned by CUDA Driver API cuDeviceGet </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>support</em>&nbsp;</td><td>Pointer to an integer to return the support status</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>support</code> is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td>if <code>dev</code> is not a valid device</td></tr>
  </table>
</dl>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g22c5ce610ffbf5940b7c05be54fc813d" title="Check support for a compute capability.">cuptiComputeCapabilitySupported</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="g395c59b62aeac395e38ced9d40677c76"></a><!-- doxytag: member="cupti_activity.h::cuptiDeviceVirtualizationMode" ref="g395c59b62aeac395e38ced9d40677c76" args="(CUdevice dev, CUpti_DeviceVirtualizationMode *mode)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceVirtualizationMode           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>dev</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g5ec1eab2a306637e8d49bcf8a678cc40">CUpti_DeviceVirtualizationMode</a> *&nbsp;</td>
          <td class="paramname"> <em>mode</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This function is used to query the virtualization mode of the CUDA device.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>dev</em>&nbsp;</td><td>The device handle returned by CUDA Driver API cuDeviceGet </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>mode</em>&nbsp;</td><td>Pointer to an CUpti_DeviceVirtualizationMode to return the virtualization mode</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td>if <code>dev</code> is not a valid device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>mode</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gad1be905ea718ed54246e52e02667e8f"></a><!-- doxytag: member="cupti_activity.h::cuptiFinalize" ref="gad1be905ea718ed54246e52e02667e8f" args="(void)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiFinalize           </td>
          <td>(</td>
          <td class="paramtype">void&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API detaches the CUPTI from the running process. It destroys and cleans up all the resources associated with CUPTI in the current process. After CUPTI detaches from the process, the process will keep on running with no CUPTI attached to it. For safe operation of the API, it is recommended this API is invoked from the exit callsite of any of the CUDA Driver or Runtime API. Otherwise CUPTI client needs to make sure that required CUDA synchronization and CUPTI activity buffer flush is done before calling the API. Sample code showing the usage of the API in the cupti callback handler code: <div class="fragment"><pre class="fragment">  <span class="keywordtype">void</span> CUPTIAPI
  cuptiCallbackHandler(<span class="keywordtype">void</span> *userdata, <a class="code" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e" title="Callback domains.">CUpti_CallbackDomain</a> domain,
      <a class="code" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8" title="An ID for a driver API, runtime API, resource or synchronization callback.">CUpti_CallbackId</a> cbid, <span class="keywordtype">void</span> *cbdata)
  {
    <span class="keyword">const</span> <a class="code" href="structCUpti__CallbackData.html" title="Data passed into a runtime or driver API callback function.">CUpti_CallbackData</a> *cbInfo = (<a class="code" href="structCUpti__CallbackData.html" title="Data passed into a runtime or driver API callback function.">CUpti_CallbackData</a> *)cbdata;

    <span class="comment">// Take this code path when CUPTI detach is requested</span>
    <span class="keywordflow">if</span> (detachCupti) {
      <span class="keywordflow">switch</span>(domain)
      {
        <span class="keywordflow">case</span> <a class="code" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9e06a7d035b9d878cc4b43c7dc93b3c632">CUPTI_CB_DOMAIN_RUNTIME_API</a>:
        <span class="keywordflow">case</span> <a class="code" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9e77c508fe29e5781fff2900676cd63213">CUPTI_CB_DOMAIN_DRIVER_API</a>:
          <span class="keywordflow">if</span> (cbInfo-&gt;<a class="code" href="structCUpti__CallbackData.html#01337ce329bea0e08d803cb99c1f1f01">callbackSite</a> == <a class="code" href="group__CUPTI__CALLBACK__API.html#gg7bd557c9b3084014c680b9925842be241df739da617612b774bb0a8895c15fab">CUPTI_API_EXIT</a>) {
              <span class="comment">// call the CUPTI detach API</span>
              <a class="code" href="group__CUPTI__ACTIVITY__API.html#gad1be905ea718ed54246e52e02667e8f" title="Detach CUPTI from the running process.">cuptiFinalize</a>();
          }
          <span class="keywordflow">break</span>;
        <span class="keywordflow">default</span>:
          <span class="keywordflow">break</span>;
      }
    }
  }
</pre></div> 
</div>
</div><p>
<a class="anchor" name="g1ac1cce5ce788b9f2c679d13e982384b"></a><!-- doxytag: member="cupti_activity.h::cuptiGetAutoBoostState" ref="g1ac1cce5ce788b9f2c679d13e982384b" args="(CUcontext context, CUpti_ActivityAutoBoostState *state)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetAutoBoostState           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structCUpti__ActivityAutoBoostState.html">CUpti_ActivityAutoBoostState</a> *&nbsp;</td>
          <td class="paramname"> <em>state</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The profiling results can be inconsistent in case auto boost is enabled. CUPTI tries to disable auto boost while profiling. It can fail to disable in cases where user does not have the permissions or CUDA_AUTO_BOOST env variable is set. The function can be used to query whether auto boost is enabled.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>A valid CUcontext. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>state</em>&nbsp;</td><td>A pointer to <a class="el" href="structCUpti__ActivityAutoBoostState.html">CUpti_ActivityAutoBoostState</a> structure which contains the current state and the id of the process that has requested the current state</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>CUcontext</code> or <code>state</code> is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>Indicates that the device does not support auto boost </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UNKNOWN</em>&nbsp;</td><td>an internal error occurred </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g036dfd802a6c28c7e4239c82ed98df21"></a><!-- doxytag: member="cupti_activity.h::cuptiGetContextId" ref="g036dfd802a6c28c7e4239c82ed98df21" args="(CUcontext context, uint32_t *contextId)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetContextId           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>contextId</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Get the ID of a context.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>contextId</em>&nbsp;</td><td>Returns a process-unique ID for the context</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td>The context is NULL or not valid. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>contextId</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0cc36b42dbf08fffc772e9c932749c77"></a><!-- doxytag: member="cupti_activity.h::cuptiGetDeviceId" ref="g0cc36b42dbf08fffc772e9c932749c77" args="(CUcontext context, uint32_t *deviceId)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetDeviceId           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>deviceId</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If <code>context</code> is NULL, returns the ID of the device that contains the currently active context. If <code>context</code> is non-NULL, returns the ID of the device which contains that context. Operates in a similar manner to cudaGetDevice() or cuCtxGetDevice() but may be called from within callback functions.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context, or NULL to indicate the current context. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>deviceId</em>&nbsp;</td><td>Returns the ID of the device that is current for the calling thread.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td>if unable to get device ID </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>deviceId</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g4add923efce4731de28c9f0b04e1e3f9"></a><!-- doxytag: member="cupti_activity.h::cuptiGetGraphId" ref="g4add923efce4731de28c9f0b04e1e3f9" args="(CUgraph graph, uint32_t *pId)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetGraphId           </td>
          <td>(</td>
          <td class="paramtype">CUgraph&nbsp;</td>
          <td class="paramname"> <em>graph</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>pId</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the unique ID of CUDA graph.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>graph</em>&nbsp;</td><td>The graph. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>pId</em>&nbsp;</td><td>Returns the unique ID of the graph</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>graph</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g22370b53102428305a97cb37fbc14678"></a><!-- doxytag: member="cupti_activity.h::cuptiGetGraphNodeId" ref="g22370b53102428305a97cb37fbc14678" args="(CUgraphNode node, uint64_t *nodeId)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetGraphNodeId           </td>
          <td>(</td>
          <td class="paramtype">CUgraphNode&nbsp;</td>
          <td class="paramname"> <em>node</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>nodeId</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the unique ID of the CUDA graph node.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>node</em>&nbsp;</td><td>The graph node. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>nodeId</em>&nbsp;</td><td>Returns the unique ID of the node</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>node</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0c83719b0248e09ef94390000d3f1035"></a><!-- doxytag: member="cupti_activity.h::cuptiGetLastError" ref="g0c83719b0248e09ef94390000d3f1035" args="(void)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetLastError           </td>
          <td>(</td>
          <td class="paramtype">void&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the last error that has been produced by any of the cupti api calls or the callback in the same host thread and resets it to CUPTI_SUCCESS. 
</div>
</div><p>
<a class="anchor" name="g04ece23d24e29e8d98daadba09f1839c"></a><!-- doxytag: member="cupti_activity.h::cuptiGetStreamId" ref="g04ece23d24e29e8d98daadba09f1839c" args="(CUcontext context, CUstream stream, uint32_t *streamId)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetStreamId           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUstream&nbsp;</td>
          <td class="paramname"> <em>stream</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>streamId</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Get the ID of a stream. The stream ID is unique within a context (i.e. all streams within a context will have unique stream IDs).<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>If non-NULL then the stream is checked to ensure that it belongs to this context. Typically this parameter should be null. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>stream</em>&nbsp;</td><td>The stream </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>streamId</em>&nbsp;</td><td>Returns a context-unique ID for the stream</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_STREAM</em>&nbsp;</td><td>if unable to get stream ID, or if <code>context</code> is non-NULL and <code>stream</code> does not belong to the context </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>streamId</code> is NULL</td></tr>
  </table>
</dl>
**DEPRECATED** This method is deprecated as of CUDA 8.0. Use method cuptiGetStreamIdEx instead. 
</div>
</div><p>
<a class="anchor" name="g062d04c62fdfeed9adb8157cecbaaa55"></a><!-- doxytag: member="cupti_activity.h::cuptiGetStreamIdEx" ref="g062d04c62fdfeed9adb8157cecbaaa55" args="(CUcontext context, CUstream stream, uint8_t perThreadStream, uint32_t *streamId)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetStreamIdEx           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">CUstream&nbsp;</td>
          <td class="paramname"> <em>stream</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&nbsp;</td>
          <td class="paramname"> <em>perThreadStream</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>streamId</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Get the ID of a stream. The stream ID is unique within a context (i.e. all streams within a context will have unique stream IDs).<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>If non-NULL then the stream is checked to ensure that it belongs to this context. Typically this parameter should be null. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>stream</em>&nbsp;</td><td>The stream </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>perThreadStream</em>&nbsp;</td><td>Flag to indicate if program is compiled for per-thread streams </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>streamId</em>&nbsp;</td><td>Returns a context-unique ID for the stream</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_STREAM</em>&nbsp;</td><td>if unable to get stream ID, or if <code>context</code> is non-NULL and <code>stream</code> does not belong to the context </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>streamId</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gbc957f426b741e46d6e9a99a43a974b5"></a><!-- doxytag: member="cupti_activity.h::cuptiGetThreadIdType" ref="gbc957f426b741e46d6e9a99a43a974b5" args="(CUpti_ActivityThreadIdType *type)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetThreadIdType           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">CUpti_ActivityThreadIdType</a> *&nbsp;</td>
          <td class="paramname"> <em>type</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the thread-id type used in CUPTI<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>type</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g7d8294c686b5293237a6daae8eae3dde"></a><!-- doxytag: member="cupti_activity.h::cuptiGetTimestamp" ref="g7d8294c686b5293237a6daae8eae3dde" args="(uint64_t *timestamp)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetTimestamp           </td>
          <td>(</td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>timestamp</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns a timestamp normalized to correspond with the start and end timestamps reported in the CUPTI activity records. The timestamp is reported in nanoseconds.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>timestamp</em>&nbsp;</td><td>Returns the CUPTI timestamp</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>timestamp</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g1821f090b841d60643ee37d977d9c64a"></a><!-- doxytag: member="cupti_activity.h::cuptiSetThreadIdType" ref="g1821f090b841d60643ee37d977d9c64a" args="(CUpti_ActivityThreadIdType type)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiSetThreadIdType           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">CUpti_ActivityThreadIdType</a>&nbsp;</td>
          <td class="paramname"> <em>type</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI uses the method corresponding to set type to generate the thread-id. See enum <a class="el" href="group__CUPTI__ACTIVITY__API.html#gc6fcebeb84a89d8f1862d31338efd4c5">CUpti_ActivityThreadIdType</a> for the list of methods. Activity records having thread-id field contain the same value. Thread id type must not be changed during the profiling session to avoid thread-id value mismatch across activity records.<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>if <code>type</code> is not supported on the platform </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
