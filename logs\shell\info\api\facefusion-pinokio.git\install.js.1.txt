######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: a9c67c6d-79ee-41cf-ae3c-6d06be90ed2b
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate base && git clone https://github.com/facefusion/facefusion --branch 3.3.2 --single-branch
# timestamp: 7/23/2025, 2:17:45 PM (1753305465835)

path: C:\pinokio\api\facefusion-pinokio.git
cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate &&
  conda activate base && git clone https://github.com/facefusion/facefusion
  --branch 3.3.2 --single-branch
index: 1
group: C:\pinokio\api\facefusion-pinokio.git\install.js
env:
  ALLUSERSPROFILE: C:\ProgramData
  APPDATA: C:\Users\<USER>\AppData\Roaming
  ChocolateyInstall: C:\ProgramData\chocolatey
  ChocolateyLastPathUpdate: "133579645654484976"
  CommonProgramFiles: C:\Program Files\Common Files
  CommonProgramW6432: C:\Program Files\Common Files
  COMPUTERNAME: CONQUEST
  ComSpec: C:\Windows\system32\cmd.exe
  DriverData: C:\Windows\System32\Drivers\DriverData
  EFC_10960: "1"
  FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
  FPS_BROWSER_USER_PROFILE_STRING: Default
  GOOGLE_APPLICATION_CREDENTIALS: C:\Users\<USER>\OneDrive\Documents\Local
    Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API
    Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
  GROQ_API_KEY: ********************************************************
  HOMEDRIVE: "C:"
  HOMEPATH: \Users\ullin
  LOCALAPPDATA: C:\Users\<USER>\AppData\Local
  LOGONSERVER: \\CONQUEST
  MSMPI_BENCHMARKS: C:\Program Files\Microsoft MPI\Benchmarks\
  MSMPI_BIN: C:\Program Files\Microsoft MPI\Bin\
  NUMBER_OF_PROCESSORS: "24"
  OculusBase: C:\Program Files\Oculus\
  OneDrive: C:\Users\<USER>\OneDrive
  OneDriveConsumer: C:\Users\<USER>\OneDrive
  ORIGINAL_XDG_CURRENT_DESKTOP: undefined
  OS: Windows_NT
  Path: C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program
    Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA
    GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft
    MPI\Bin\;C:\Program
    Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program
    Files\dotnet\;C:\Program Files (x86)\NVIDIA
    Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program
    Files\NVIDIA Corporation\Nsight Compute
    2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual
    Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program
    Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program
    Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program
    Files\Docker\Docker\resources\bin;C:\Program
    Files\nodejs\;C:\Users\<USER>\AppData\Local\Muse
    Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft
    VS
    Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
  PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
  PLAYWRIGHT_BROWSERS_PATH: C:\pinokio\bin\playwright\browsers
  PROCESSOR_ARCHITECTURE: AMD64
  PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
  PROCESSOR_LEVEL: "6"
  PROCESSOR_REVISION: b701
  ProgramData: C:\ProgramData
  ProgramFiles: C:\Program Files
  ProgramFiles(x86): C:\Program Files (x86)
  ProgramW6432: C:\Program Files
  PSModulePath: C:\Program
    Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
  PUBLIC: C:\Users\<USER>\Windows
  TEMP: C:\pinokio\cache\TEMP
  TMP: C:\pinokio\cache\TMP
  USERDOMAIN: CONQUEST
  USERDOMAIN_ROAMINGPROFILE: CONQUEST
  USERNAME: ullin
  USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
  windir: C:\Windows
  CMAKE_OBJECT_PATH_MAX: 1024
  PYTORCH_ENABLE_MPS_FALLBACK: 1
  TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD: 1
  HOMEBREW_CACHE: C:\pinokio\bin\homebrew\cache
  XDG_CACHE_HOME: C:\pinokio\cache\XDG_CACHE_HOME
  PIP_CACHE_DIR: C:\pinokio\cache\PIP_CACHE_DIR
  UV_CACHE_DIR: C:\pinokio\cache\UV_CACHE_DIR
  PIP_TMPDIR: C:\pinokio\cache\PIP_TMPDIR
  TMPDIR: C:\pinokio\cache\TMPDIR
  XDG_DATA_HOME: C:\pinokio\cache\XDG_DATA_HOME
  XDG_CONFIG_HOME: C:\pinokio\cache\XDG_CONFIG_HOME
  XDG_STATE_HOME: C:\pinokio\cache\XDG_STATE_HOME
  PIP_CONFIG_FILE: C:\pinokio\pipconfig
  CONDARC: C:\pinokio\condarc
  PS1: <<PINOKIO_SHELL>>
  GRADIO_ANALYTICS_ENABLED: "False"
  GRADIO_ALLOWED_PATHS: C:\pinokio
  PINOKIO_SHARE_VAR: url
  PINOKIO_SHARE_CLOUDFLARE: "false"
  PINOKIO_SCRIPT_DEFAULT: "false"
  PINOKIO_DRIVE: C:\pinokio\drive
  GRADIO_TEMP_DIR: C:\pinokio\api\facefusion-pinokio.git\cache\GRADIO_TEMP_DIR
  HF_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\HF_HOME
  TORCH_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\TORCH_HOME
  PINOKIO_SHARE_LOCAL: "false"
  HOMEBREW_PREFIX: C:\pinokio\bin\homebrew
  HOMEBREW_CELLAR: C:\pinokio\bin\homebrew\Cellar
  HOMEBREW_REPOSITORY: C:\pinokio\bin\homebrew
  CONDA_PREFIX: C:\pinokio\bin\miniconda
  CONDA_ENVS_PATH: C:\pinokio\bin\miniconda\envs
  CONDA_PKGS_DIRS: C:\pinokio\bin\miniconda\pkgs
  PYTHON: C:\pinokio\bin\miniconda\python
  CONDA_BAT: C:\pinokio\bin\miniconda\condabin\conda.bat
  CONDA_EXE: C:\pinokio\bin\miniconda\Scripts\conda.exe
  CONDA_PYTHON_EXE: C:\pinokio\bin\miniconda\Scripts\python
  CUDA_HOME: C:\pinokio\bin\miniconda
  GIT_CONFIG_GLOBAL: C:\pinokio\gitconfig
  CONDA_AUTO_ACTIVATE_BASE: "false"
  PYTHONNOUSERSITE: "1"
  PIP_REQUIRE_VIRTUALENV: "true"
  UV_PYTHON_PREFERENCE: only-managed
done: true
ready: false
id: a9c67c6d-79ee-41cf-ae3c-6d06be90ed2b
ts: 1753305465835


######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 82d2c3c7-7420-4990-addd-7f88a58431cd
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install conda-forge::ffmpeg=7.0.2 conda-forge::libvorbis=1.3.7 --yes
# timestamp: 7/23/2025, 2:18:20 PM (1753305500425)

path: C:\pinokio\api\facefusion-pinokio.git
cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate &&
  conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install
  conda-forge::ffmpeg=7.0.2 conda-forge::libvorbis=1.3.7 --yes
index: 1
group: C:\pinokio\api\facefusion-pinokio.git\install.js
env:
  ALLUSERSPROFILE: C:\ProgramData
  APPDATA: C:\Users\<USER>\AppData\Roaming
  ChocolateyInstall: C:\ProgramData\chocolatey
  ChocolateyLastPathUpdate: "133579645654484976"
  CommonProgramFiles: C:\Program Files\Common Files
  CommonProgramW6432: C:\Program Files\Common Files
  COMPUTERNAME: CONQUEST
  ComSpec: C:\Windows\system32\cmd.exe
  DriverData: C:\Windows\System32\Drivers\DriverData
  EFC_10960: "1"
  FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
  FPS_BROWSER_USER_PROFILE_STRING: Default
  GOOGLE_APPLICATION_CREDENTIALS: C:\Users\<USER>\OneDrive\Documents\Local
    Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API
    Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
  GROQ_API_KEY: ********************************************************
  HOMEDRIVE: "C:"
  HOMEPATH: \Users\ullin
  LOCALAPPDATA: C:\Users\<USER>\AppData\Local
  LOGONSERVER: \\CONQUEST
  MSMPI_BENCHMARKS: C:\Program Files\Microsoft MPI\Benchmarks\
  MSMPI_BIN: C:\Program Files\Microsoft MPI\Bin\
  NUMBER_OF_PROCESSORS: "24"
  OculusBase: C:\Program Files\Oculus\
  OneDrive: C:\Users\<USER>\OneDrive
  OneDriveConsumer: C:\Users\<USER>\OneDrive
  ORIGINAL_XDG_CURRENT_DESKTOP: undefined
  OS: Windows_NT
  Path: C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program
    Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA
    GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft
    MPI\Bin\;C:\Program
    Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program
    Files\dotnet\;C:\Program Files (x86)\NVIDIA
    Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program
    Files\NVIDIA Corporation\Nsight Compute
    2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual
    Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program
    Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program
    Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program
    Files\Docker\Docker\resources\bin;C:\Program
    Files\nodejs\;C:\Users\<USER>\AppData\Local\Muse
    Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft
    VS
    Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
  PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
  PLAYWRIGHT_BROWSERS_PATH: C:\pinokio\bin\playwright\browsers
  PROCESSOR_ARCHITECTURE: AMD64
  PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
  PROCESSOR_LEVEL: "6"
  PROCESSOR_REVISION: b701
  ProgramData: C:\ProgramData
  ProgramFiles: C:\Program Files
  ProgramFiles(x86): C:\Program Files (x86)
  ProgramW6432: C:\Program Files
  PSModulePath: C:\Program
    Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
  PUBLIC: C:\Users\<USER>\Windows
  TEMP: C:\pinokio\cache\TEMP
  TMP: C:\pinokio\cache\TMP
  USERDOMAIN: CONQUEST
  USERDOMAIN_ROAMINGPROFILE: CONQUEST
  USERNAME: ullin
  USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
  windir: C:\Windows
  CMAKE_OBJECT_PATH_MAX: 1024
  PYTORCH_ENABLE_MPS_FALLBACK: 1
  TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD: 1
  HOMEBREW_CACHE: C:\pinokio\bin\homebrew\cache
  XDG_CACHE_HOME: C:\pinokio\cache\XDG_CACHE_HOME
  PIP_CACHE_DIR: C:\pinokio\cache\PIP_CACHE_DIR
  UV_CACHE_DIR: C:\pinokio\cache\UV_CACHE_DIR
  PIP_TMPDIR: C:\pinokio\cache\PIP_TMPDIR
  TMPDIR: C:\pinokio\cache\TMPDIR
  XDG_DATA_HOME: C:\pinokio\cache\XDG_DATA_HOME
  XDG_CONFIG_HOME: C:\pinokio\cache\XDG_CONFIG_HOME
  XDG_STATE_HOME: C:\pinokio\cache\XDG_STATE_HOME
  PIP_CONFIG_FILE: C:\pinokio\pipconfig
  CONDARC: C:\pinokio\condarc
  PS1: <<PINOKIO_SHELL>>
  GRADIO_ANALYTICS_ENABLED: "False"
  GRADIO_ALLOWED_PATHS: C:\pinokio
  PINOKIO_SHARE_VAR: url
  PINOKIO_SHARE_CLOUDFLARE: "false"
  PINOKIO_SCRIPT_DEFAULT: "false"
  PINOKIO_DRIVE: C:\pinokio\drive
  GRADIO_TEMP_DIR: C:\pinokio\api\facefusion-pinokio.git\cache\GRADIO_TEMP_DIR
  HF_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\HF_HOME
  TORCH_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\TORCH_HOME
  PINOKIO_SHARE_LOCAL: "false"
  HOMEBREW_PREFIX: C:\pinokio\bin\homebrew
  HOMEBREW_CELLAR: C:\pinokio\bin\homebrew\Cellar
  HOMEBREW_REPOSITORY: C:\pinokio\bin\homebrew
  CONDA_PREFIX: C:\pinokio\bin\miniconda
  CONDA_ENVS_PATH: C:\pinokio\bin\miniconda\envs
  CONDA_PKGS_DIRS: C:\pinokio\bin\miniconda\pkgs
  PYTHON: C:\pinokio\bin\miniconda\python
  CONDA_BAT: C:\pinokio\bin\miniconda\condabin\conda.bat
  CONDA_EXE: C:\pinokio\bin\miniconda\Scripts\conda.exe
  CONDA_PYTHON_EXE: C:\pinokio\bin\miniconda\Scripts\python
  CUDA_HOME: C:\pinokio\bin\miniconda
  GIT_CONFIG_GLOBAL: C:\pinokio\gitconfig
  CONDA_AUTO_ACTIVATE_BASE: "false"
  PYTHONNOUSERSITE: "1"
  UV_PYTHON_PREFERENCE: only-managed
done: true
ready: false
id: 82d2c3c7-7420-4990-addd-7f88a58431cd
ts: 1753305500425


######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 0a8db740-9710-4752-918c-b807ed311cec
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
# timestamp: 7/23/2025, 2:18:40 PM (1753305520393)

path: C:\pinokio\api\facefusion-pinokio.git
cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate &&
  conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install
  nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
index: 1
group: C:\pinokio\api\facefusion-pinokio.git\install.js
env:
  ALLUSERSPROFILE: C:\ProgramData
  APPDATA: C:\Users\<USER>\AppData\Roaming
  ChocolateyInstall: C:\ProgramData\chocolatey
  ChocolateyLastPathUpdate: "133579645654484976"
  CommonProgramFiles: C:\Program Files\Common Files
  CommonProgramW6432: C:\Program Files\Common Files
  COMPUTERNAME: CONQUEST
  ComSpec: C:\Windows\system32\cmd.exe
  DriverData: C:\Windows\System32\Drivers\DriverData
  EFC_10960: "1"
  FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
  FPS_BROWSER_USER_PROFILE_STRING: Default
  GOOGLE_APPLICATION_CREDENTIALS: C:\Users\<USER>\OneDrive\Documents\Local
    Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API
    Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
  GROQ_API_KEY: ********************************************************
  HOMEDRIVE: "C:"
  HOMEPATH: \Users\ullin
  LOCALAPPDATA: C:\Users\<USER>\AppData\Local
  LOGONSERVER: \\CONQUEST
  MSMPI_BENCHMARKS: C:\Program Files\Microsoft MPI\Benchmarks\
  MSMPI_BIN: C:\Program Files\Microsoft MPI\Bin\
  NUMBER_OF_PROCESSORS: "24"
  OculusBase: C:\Program Files\Oculus\
  OneDrive: C:\Users\<USER>\OneDrive
  OneDriveConsumer: C:\Users\<USER>\OneDrive
  ORIGINAL_XDG_CURRENT_DESKTOP: undefined
  OS: Windows_NT
  Path: C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program
    Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA
    GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft
    MPI\Bin\;C:\Program
    Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program
    Files\dotnet\;C:\Program Files (x86)\NVIDIA
    Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program
    Files\NVIDIA Corporation\Nsight Compute
    2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual
    Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program
    Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program
    Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program
    Files\Docker\Docker\resources\bin;C:\Program
    Files\nodejs\;C:\Users\<USER>\AppData\Local\Muse
    Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft
    VS
    Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
  PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
  PLAYWRIGHT_BROWSERS_PATH: C:\pinokio\bin\playwright\browsers
  PROCESSOR_ARCHITECTURE: AMD64
  PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
  PROCESSOR_LEVEL: "6"
  PROCESSOR_REVISION: b701
  ProgramData: C:\ProgramData
  ProgramFiles: C:\Program Files
  ProgramFiles(x86): C:\Program Files (x86)
  ProgramW6432: C:\Program Files
  PSModulePath: C:\Program
    Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
  PUBLIC: C:\Users\<USER>\Windows
  TEMP: C:\pinokio\cache\TEMP
  TMP: C:\pinokio\cache\TMP
  USERDOMAIN: CONQUEST
  USERDOMAIN_ROAMINGPROFILE: CONQUEST
  USERNAME: ullin
  USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
  windir: C:\Windows
  CMAKE_OBJECT_PATH_MAX: 1024
  PYTORCH_ENABLE_MPS_FALLBACK: 1
  TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD: 1
  HOMEBREW_CACHE: C:\pinokio\bin\homebrew\cache
  XDG_CACHE_HOME: C:\pinokio\cache\XDG_CACHE_HOME
  PIP_CACHE_DIR: C:\pinokio\cache\PIP_CACHE_DIR
  UV_CACHE_DIR: C:\pinokio\cache\UV_CACHE_DIR
  PIP_TMPDIR: C:\pinokio\cache\PIP_TMPDIR
  TMPDIR: C:\pinokio\cache\TMPDIR
  XDG_DATA_HOME: C:\pinokio\cache\XDG_DATA_HOME
  XDG_CONFIG_HOME: C:\pinokio\cache\XDG_CONFIG_HOME
  XDG_STATE_HOME: C:\pinokio\cache\XDG_STATE_HOME
  PIP_CONFIG_FILE: C:\pinokio\pipconfig
  CONDARC: C:\pinokio\condarc
  PS1: <<PINOKIO_SHELL>>
  GRADIO_ANALYTICS_ENABLED: "False"
  GRADIO_ALLOWED_PATHS: C:\pinokio
  PINOKIO_SHARE_VAR: url
  PINOKIO_SHARE_CLOUDFLARE: "false"
  PINOKIO_SCRIPT_DEFAULT: "false"
  PINOKIO_DRIVE: C:\pinokio\drive
  GRADIO_TEMP_DIR: C:\pinokio\api\facefusion-pinokio.git\cache\GRADIO_TEMP_DIR
  HF_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\HF_HOME
  TORCH_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\TORCH_HOME
  PINOKIO_SHARE_LOCAL: "false"
  HOMEBREW_PREFIX: C:\pinokio\bin\homebrew
  HOMEBREW_CELLAR: C:\pinokio\bin\homebrew\Cellar
  HOMEBREW_REPOSITORY: C:\pinokio\bin\homebrew
  CONDA_PREFIX: C:\pinokio\bin\miniconda
  CONDA_ENVS_PATH: C:\pinokio\bin\miniconda\envs
  CONDA_PKGS_DIRS: C:\pinokio\bin\miniconda\pkgs
  PYTHON: C:\pinokio\bin\miniconda\python
  CONDA_BAT: C:\pinokio\bin\miniconda\condabin\conda.bat
  CONDA_EXE: C:\pinokio\bin\miniconda\Scripts\conda.exe
  CONDA_PYTHON_EXE: C:\pinokio\bin\miniconda\Scripts\python
  CUDA_HOME: C:\pinokio\bin\miniconda
  GIT_CONFIG_GLOBAL: C:\pinokio\gitconfig
  CONDA_AUTO_ACTIVATE_BASE: "false"
  PYTHONNOUSERSITE: "1"
  UV_PYTHON_PREFERENCE: only-managed
done: true
ready: false
id: 0a8db740-9710-4752-918c-b807ed311cec
ts: 1753305520393


######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 4cf9b268-6472-493c-9fb6-15d071ac7087
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install tensorrt==********** --extra-index-url https://pypi.nvidia.com
# timestamp: 7/23/2025, 2:18:53 PM (1753305533410)

path: C:\pinokio\api\facefusion-pinokio.git
cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate &&
  conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install
  tensorrt==********** --extra-index-url https://pypi.nvidia.com
index: 1
group: C:\pinokio\api\facefusion-pinokio.git\install.js
env:
  ALLUSERSPROFILE: C:\ProgramData
  APPDATA: C:\Users\<USER>\AppData\Roaming
  ChocolateyInstall: C:\ProgramData\chocolatey
  ChocolateyLastPathUpdate: "133579645654484976"
  CommonProgramFiles: C:\Program Files\Common Files
  CommonProgramW6432: C:\Program Files\Common Files
  COMPUTERNAME: CONQUEST
  ComSpec: C:\Windows\system32\cmd.exe
  DriverData: C:\Windows\System32\Drivers\DriverData
  EFC_10960: "1"
  FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
  FPS_BROWSER_USER_PROFILE_STRING: Default
  GOOGLE_APPLICATION_CREDENTIALS: C:\Users\<USER>\OneDrive\Documents\Local
    Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API
    Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
  GROQ_API_KEY: ********************************************************
  HOMEDRIVE: "C:"
  HOMEPATH: \Users\ullin
  LOCALAPPDATA: C:\Users\<USER>\AppData\Local
  LOGONSERVER: \\CONQUEST
  MSMPI_BENCHMARKS: C:\Program Files\Microsoft MPI\Benchmarks\
  MSMPI_BIN: C:\Program Files\Microsoft MPI\Bin\
  NUMBER_OF_PROCESSORS: "24"
  OculusBase: C:\Program Files\Oculus\
  OneDrive: C:\Users\<USER>\OneDrive
  OneDriveConsumer: C:\Users\<USER>\OneDrive
  ORIGINAL_XDG_CURRENT_DESKTOP: undefined
  OS: Windows_NT
  Path: C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program
    Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA
    GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft
    MPI\Bin\;C:\Program
    Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program
    Files\dotnet\;C:\Program Files (x86)\NVIDIA
    Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program
    Files\NVIDIA Corporation\Nsight Compute
    2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual
    Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program
    Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program
    Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program
    Files\Docker\Docker\resources\bin;C:\Program
    Files\nodejs\;C:\Users\<USER>\AppData\Local\Muse
    Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft
    VS
    Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
  PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
  PLAYWRIGHT_BROWSERS_PATH: C:\pinokio\bin\playwright\browsers
  PROCESSOR_ARCHITECTURE: AMD64
  PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
  PROCESSOR_LEVEL: "6"
  PROCESSOR_REVISION: b701
  ProgramData: C:\ProgramData
  ProgramFiles: C:\Program Files
  ProgramFiles(x86): C:\Program Files (x86)
  ProgramW6432: C:\Program Files
  PSModulePath: C:\Program
    Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
  PUBLIC: C:\Users\<USER>\Windows
  TEMP: C:\pinokio\cache\TEMP
  TMP: C:\pinokio\cache\TMP
  USERDOMAIN: CONQUEST
  USERDOMAIN_ROAMINGPROFILE: CONQUEST
  USERNAME: ullin
  USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
  windir: C:\Windows
  CMAKE_OBJECT_PATH_MAX: 1024
  PYTORCH_ENABLE_MPS_FALLBACK: 1
  TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD: 1
  HOMEBREW_CACHE: C:\pinokio\bin\homebrew\cache
  XDG_CACHE_HOME: C:\pinokio\cache\XDG_CACHE_HOME
  PIP_CACHE_DIR: C:\pinokio\cache\PIP_CACHE_DIR
  UV_CACHE_DIR: C:\pinokio\cache\UV_CACHE_DIR
  PIP_TMPDIR: C:\pinokio\cache\PIP_TMPDIR
  TMPDIR: C:\pinokio\cache\TMPDIR
  XDG_DATA_HOME: C:\pinokio\cache\XDG_DATA_HOME
  XDG_CONFIG_HOME: C:\pinokio\cache\XDG_CONFIG_HOME
  XDG_STATE_HOME: C:\pinokio\cache\XDG_STATE_HOME
  PIP_CONFIG_FILE: C:\pinokio\pipconfig
  CONDARC: C:\pinokio\condarc
  PS1: <<PINOKIO_SHELL>>
  GRADIO_ANALYTICS_ENABLED: "False"
  GRADIO_ALLOWED_PATHS: C:\pinokio
  PINOKIO_SHARE_VAR: url
  PINOKIO_SHARE_CLOUDFLARE: "false"
  PINOKIO_SCRIPT_DEFAULT: "false"
  PINOKIO_DRIVE: C:\pinokio\drive
  GRADIO_TEMP_DIR: C:\pinokio\api\facefusion-pinokio.git\cache\GRADIO_TEMP_DIR
  HF_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\HF_HOME
  TORCH_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\TORCH_HOME
  PINOKIO_SHARE_LOCAL: "false"
  HOMEBREW_PREFIX: C:\pinokio\bin\homebrew
  HOMEBREW_CELLAR: C:\pinokio\bin\homebrew\Cellar
  HOMEBREW_REPOSITORY: C:\pinokio\bin\homebrew
  CONDA_PREFIX: C:\pinokio\bin\miniconda
  CONDA_ENVS_PATH: C:\pinokio\bin\miniconda\envs
  CONDA_PKGS_DIRS: C:\pinokio\bin\miniconda\pkgs
  PYTHON: C:\pinokio\bin\miniconda\python
  CONDA_BAT: C:\pinokio\bin\miniconda\condabin\conda.bat
  CONDA_EXE: C:\pinokio\bin\miniconda\Scripts\conda.exe
  CONDA_PYTHON_EXE: C:\pinokio\bin\miniconda\Scripts\python
  CUDA_HOME: C:\pinokio\bin\miniconda
  GIT_CONFIG_GLOBAL: C:\pinokio\gitconfig
  CONDA_AUTO_ACTIVATE_BASE: "false"
  PYTHONNOUSERSITE: "1"
  UV_PYTHON_PREFERENCE: only-managed
done: true
ready: false
id: 4cf9b268-6472-493c-9fb6-15d071ac7087
ts: 1753305533410


######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 239c5871-4ee3-4265-bd6f-52b6b8c9bbe2
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
# timestamp: 7/23/2025, 2:19:53 PM (1753305593200)

path: C:\pinokio\api\facefusion-pinokio.git
cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate &&
  conda activate C:\pinokio\api\facefusion-pinokio.git\.env && conda install
  nvidia/label/cuda-12.9.1::cuda-runtime nvidia/label/cudnn-9.10.0::cudnn --yes
index: 1
group: C:\pinokio\api\facefusion-pinokio.git\install.js
env:
  ALLUSERSPROFILE: C:\ProgramData
  APPDATA: C:\Users\<USER>\AppData\Roaming
  ChocolateyInstall: C:\ProgramData\chocolatey
  ChocolateyLastPathUpdate: "133579645654484976"
  CommonProgramFiles: C:\Program Files\Common Files
  CommonProgramW6432: C:\Program Files\Common Files
  COMPUTERNAME: CONQUEST
  ComSpec: C:\Windows\system32\cmd.exe
  DriverData: C:\Windows\System32\Drivers\DriverData
  EFC_10960: "1"
  FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
  FPS_BROWSER_USER_PROFILE_STRING: Default
  GOOGLE_APPLICATION_CREDENTIALS: C:\Users\<USER>\OneDrive\Documents\Local
    Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API
    Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
  GROQ_API_KEY: ********************************************************
  HOMEDRIVE: "C:"
  HOMEPATH: \Users\ullin
  LOCALAPPDATA: C:\Users\<USER>\AppData\Local
  LOGONSERVER: \\CONQUEST
  MSMPI_BENCHMARKS: C:\Program Files\Microsoft MPI\Benchmarks\
  MSMPI_BIN: C:\Program Files\Microsoft MPI\Bin\
  NUMBER_OF_PROCESSORS: "24"
  OculusBase: C:\Program Files\Oculus\
  OneDrive: C:\Users\<USER>\OneDrive
  OneDriveConsumer: C:\Users\<USER>\OneDrive
  ORIGINAL_XDG_CURRENT_DESKTOP: undefined
  OS: Windows_NT
  Path: C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program
    Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA
    GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft
    MPI\Bin\;C:\Program
    Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program
    Files\dotnet\;C:\Program Files (x86)\NVIDIA
    Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program
    Files\NVIDIA Corporation\Nsight Compute
    2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual
    Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program
    Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program
    Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program
    Files\Docker\Docker\resources\bin;C:\Program
    Files\nodejs\;C:\Users\<USER>\AppData\Local\Muse
    Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft
    VS
    Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
  PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
  PLAYWRIGHT_BROWSERS_PATH: C:\pinokio\bin\playwright\browsers
  PROCESSOR_ARCHITECTURE: AMD64
  PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
  PROCESSOR_LEVEL: "6"
  PROCESSOR_REVISION: b701
  ProgramData: C:\ProgramData
  ProgramFiles: C:\Program Files
  ProgramFiles(x86): C:\Program Files (x86)
  ProgramW6432: C:\Program Files
  PSModulePath: C:\Program
    Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
  PUBLIC: C:\Users\<USER>\Windows
  TEMP: C:\pinokio\cache\TEMP
  TMP: C:\pinokio\cache\TMP
  USERDOMAIN: CONQUEST
  USERDOMAIN_ROAMINGPROFILE: CONQUEST
  USERNAME: ullin
  USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
  windir: C:\Windows
  CMAKE_OBJECT_PATH_MAX: 1024
  PYTORCH_ENABLE_MPS_FALLBACK: 1
  TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD: 1
  HOMEBREW_CACHE: C:\pinokio\bin\homebrew\cache
  XDG_CACHE_HOME: C:\pinokio\cache\XDG_CACHE_HOME
  PIP_CACHE_DIR: C:\pinokio\cache\PIP_CACHE_DIR
  UV_CACHE_DIR: C:\pinokio\cache\UV_CACHE_DIR
  PIP_TMPDIR: C:\pinokio\cache\PIP_TMPDIR
  TMPDIR: C:\pinokio\cache\TMPDIR
  XDG_DATA_HOME: C:\pinokio\cache\XDG_DATA_HOME
  XDG_CONFIG_HOME: C:\pinokio\cache\XDG_CONFIG_HOME
  XDG_STATE_HOME: C:\pinokio\cache\XDG_STATE_HOME
  PIP_CONFIG_FILE: C:\pinokio\pipconfig
  CONDARC: C:\pinokio\condarc
  PS1: <<PINOKIO_SHELL>>
  GRADIO_ANALYTICS_ENABLED: "False"
  GRADIO_ALLOWED_PATHS: C:\pinokio
  PINOKIO_SHARE_VAR: url
  PINOKIO_SHARE_CLOUDFLARE: "false"
  PINOKIO_SCRIPT_DEFAULT: "false"
  PINOKIO_DRIVE: C:\pinokio\drive
  GRADIO_TEMP_DIR: C:\pinokio\api\facefusion-pinokio.git\cache\GRADIO_TEMP_DIR
  HF_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\HF_HOME
  TORCH_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\TORCH_HOME
  PINOKIO_SHARE_LOCAL: "false"
  HOMEBREW_PREFIX: C:\pinokio\bin\homebrew
  HOMEBREW_CELLAR: C:\pinokio\bin\homebrew\Cellar
  HOMEBREW_REPOSITORY: C:\pinokio\bin\homebrew
  CONDA_PREFIX: C:\pinokio\bin\miniconda
  CONDA_ENVS_PATH: C:\pinokio\bin\miniconda\envs
  CONDA_PKGS_DIRS: C:\pinokio\bin\miniconda\pkgs
  PYTHON: C:\pinokio\bin\miniconda\python
  CONDA_BAT: C:\pinokio\bin\miniconda\condabin\conda.bat
  CONDA_EXE: C:\pinokio\bin\miniconda\Scripts\conda.exe
  CONDA_PYTHON_EXE: C:\pinokio\bin\miniconda\Scripts\python
  CUDA_HOME: C:\pinokio\bin\miniconda
  GIT_CONFIG_GLOBAL: C:\pinokio\gitconfig
  CONDA_AUTO_ACTIVATE_BASE: "false"
  PYTHONNOUSERSITE: "1"
  UV_PYTHON_PREFERENCE: only-managed
done: true
ready: false
id: 239c5871-4ee3-4265-bd6f-52b6b8c9bbe2
ts: 1753305593200


######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: 0ab8cd9b-56b9-47c3-a629-6fee7a8f4789
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install tensorrt==********** --extra-index-url https://pypi.nvidia.com
# timestamp: 7/23/2025, 2:19:58 PM (1753305598198)

path: C:\pinokio\api\facefusion-pinokio.git
cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate &&
  conda activate C:\pinokio\api\facefusion-pinokio.git\.env && pip install
  tensorrt==********** --extra-index-url https://pypi.nvidia.com
index: 1
group: C:\pinokio\api\facefusion-pinokio.git\install.js
env:
  ALLUSERSPROFILE: C:\ProgramData
  APPDATA: C:\Users\<USER>\AppData\Roaming
  ChocolateyInstall: C:\ProgramData\chocolatey
  ChocolateyLastPathUpdate: "133579645654484976"
  CommonProgramFiles: C:\Program Files\Common Files
  CommonProgramW6432: C:\Program Files\Common Files
  COMPUTERNAME: CONQUEST
  ComSpec: C:\Windows\system32\cmd.exe
  DriverData: C:\Windows\System32\Drivers\DriverData
  EFC_10960: "1"
  FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
  FPS_BROWSER_USER_PROFILE_STRING: Default
  GOOGLE_APPLICATION_CREDENTIALS: C:\Users\<USER>\OneDrive\Documents\Local
    Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API
    Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
  GROQ_API_KEY: ********************************************************
  HOMEDRIVE: "C:"
  HOMEPATH: \Users\ullin
  LOCALAPPDATA: C:\Users\<USER>\AppData\Local
  LOGONSERVER: \\CONQUEST
  MSMPI_BENCHMARKS: C:\Program Files\Microsoft MPI\Benchmarks\
  MSMPI_BIN: C:\Program Files\Microsoft MPI\Bin\
  NUMBER_OF_PROCESSORS: "24"
  OculusBase: C:\Program Files\Oculus\
  OneDrive: C:\Users\<USER>\OneDrive
  OneDriveConsumer: C:\Users\<USER>\OneDrive
  ORIGINAL_XDG_CURRENT_DESKTOP: undefined
  OS: Windows_NT
  Path: C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program
    Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA
    GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft
    MPI\Bin\;C:\Program
    Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program
    Files\dotnet\;C:\Program Files (x86)\NVIDIA
    Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program
    Files\NVIDIA Corporation\Nsight Compute
    2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual
    Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program
    Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program
    Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program
    Files\Docker\Docker\resources\bin;C:\Program
    Files\nodejs\;C:\Users\<USER>\AppData\Local\Muse
    Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft
    VS
    Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
  PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
  PLAYWRIGHT_BROWSERS_PATH: C:\pinokio\bin\playwright\browsers
  PROCESSOR_ARCHITECTURE: AMD64
  PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
  PROCESSOR_LEVEL: "6"
  PROCESSOR_REVISION: b701
  ProgramData: C:\ProgramData
  ProgramFiles: C:\Program Files
  ProgramFiles(x86): C:\Program Files (x86)
  ProgramW6432: C:\Program Files
  PSModulePath: C:\Program
    Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
  PUBLIC: C:\Users\<USER>\Windows
  TEMP: C:\pinokio\cache\TEMP
  TMP: C:\pinokio\cache\TMP
  USERDOMAIN: CONQUEST
  USERDOMAIN_ROAMINGPROFILE: CONQUEST
  USERNAME: ullin
  USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
  windir: C:\Windows
  CMAKE_OBJECT_PATH_MAX: 1024
  PYTORCH_ENABLE_MPS_FALLBACK: 1
  TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD: 1
  HOMEBREW_CACHE: C:\pinokio\bin\homebrew\cache
  XDG_CACHE_HOME: C:\pinokio\cache\XDG_CACHE_HOME
  PIP_CACHE_DIR: C:\pinokio\cache\PIP_CACHE_DIR
  UV_CACHE_DIR: C:\pinokio\cache\UV_CACHE_DIR
  PIP_TMPDIR: C:\pinokio\cache\PIP_TMPDIR
  TMPDIR: C:\pinokio\cache\TMPDIR
  XDG_DATA_HOME: C:\pinokio\cache\XDG_DATA_HOME
  XDG_CONFIG_HOME: C:\pinokio\cache\XDG_CONFIG_HOME
  XDG_STATE_HOME: C:\pinokio\cache\XDG_STATE_HOME
  PIP_CONFIG_FILE: C:\pinokio\pipconfig
  CONDARC: C:\pinokio\condarc
  PS1: <<PINOKIO_SHELL>>
  GRADIO_ANALYTICS_ENABLED: "False"
  GRADIO_ALLOWED_PATHS: C:\pinokio
  PINOKIO_SHARE_VAR: url
  PINOKIO_SHARE_CLOUDFLARE: "false"
  PINOKIO_SCRIPT_DEFAULT: "false"
  PINOKIO_DRIVE: C:\pinokio\drive
  GRADIO_TEMP_DIR: C:\pinokio\api\facefusion-pinokio.git\cache\GRADIO_TEMP_DIR
  HF_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\HF_HOME
  TORCH_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\TORCH_HOME
  PINOKIO_SHARE_LOCAL: "false"
  HOMEBREW_PREFIX: C:\pinokio\bin\homebrew
  HOMEBREW_CELLAR: C:\pinokio\bin\homebrew\Cellar
  HOMEBREW_REPOSITORY: C:\pinokio\bin\homebrew
  CONDA_PREFIX: C:\pinokio\bin\miniconda
  CONDA_ENVS_PATH: C:\pinokio\bin\miniconda\envs
  CONDA_PKGS_DIRS: C:\pinokio\bin\miniconda\pkgs
  PYTHON: C:\pinokio\bin\miniconda\python
  CONDA_BAT: C:\pinokio\bin\miniconda\condabin\conda.bat
  CONDA_EXE: C:\pinokio\bin\miniconda\Scripts\conda.exe
  CONDA_PYTHON_EXE: C:\pinokio\bin\miniconda\Scripts\python
  CUDA_HOME: C:\pinokio\bin\miniconda
  GIT_CONFIG_GLOBAL: C:\pinokio\gitconfig
  CONDA_AUTO_ACTIVATE_BASE: "false"
  PYTHONNOUSERSITE: "1"
  UV_PYTHON_PREFERENCE: only-managed
done: true
ready: false
id: 0ab8cd9b-56b9-47c3-a629-6fee7a8f4789
ts: 1753305598198


######################################################################
#
# group: C:\pinokio\api\facefusion-pinokio.git\install.js
# id: b10e49d4-37cd-42b5-8643-776489d98fb7
# index: 1
# cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate && conda activate C:\pinokio\api\facefusion-pinokio.git\.env && python install.py --onnxruntime cuda
# timestamp: 7/23/2025, 2:24:40 PM (1753305880667)

path: C:\pinokio\api\facefusion-pinokio.git\facefusion
cmd: conda_hook && conda deactivate && conda deactivate && conda deactivate &&
  conda activate C:\pinokio\api\facefusion-pinokio.git\.env && python install.py
  --onnxruntime cuda
index: 1
group: C:\pinokio\api\facefusion-pinokio.git\install.js
env:
  ALLUSERSPROFILE: C:\ProgramData
  APPDATA: C:\Users\<USER>\AppData\Roaming
  ChocolateyInstall: C:\ProgramData\chocolatey
  ChocolateyLastPathUpdate: "133579645654484976"
  CommonProgramFiles: C:\Program Files\Common Files
  CommonProgramW6432: C:\Program Files\Common Files
  COMPUTERNAME: CONQUEST
  ComSpec: C:\Windows\system32\cmd.exe
  DriverData: C:\Windows\System32\Drivers\DriverData
  EFC_10960: "1"
  FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
  FPS_BROWSER_USER_PROFILE_STRING: Default
  GOOGLE_APPLICATION_CREDENTIALS: C:\Users\<USER>\OneDrive\Documents\Local
    Automation\eBay Dropshipping Automation Project\Google Sheets OAuth API
    Credentials\ebay-dropshipping-automation-sa-credentials-df490ffa2b33.json
  GROQ_API_KEY: ********************************************************
  HOMEDRIVE: "C:"
  HOMEPATH: \Users\ullin
  LOCALAPPDATA: C:\Users\<USER>\AppData\Local
  LOGONSERVER: \\CONQUEST
  MSMPI_BENCHMARKS: C:\Program Files\Microsoft MPI\Benchmarks\
  MSMPI_BIN: C:\Program Files\Microsoft MPI\Bin\
  NUMBER_OF_PROCESSORS: "24"
  OculusBase: C:\Program Files\Oculus\
  OneDrive: C:\Users\<USER>\OneDrive
  OneDriveConsumer: C:\Users\<USER>\OneDrive
  ORIGINAL_XDG_CURRENT_DESKTOP: undefined
  OS: Windows_NT
  Path: C:\pinokio\bin\miniconda\etc\profile.d;C:\pinokio\bin\miniconda\bin;C:\pinokio\bin\miniconda\Scripts;C:\pinokio\bin\miniconda\condabin;C:\pinokio\bin\miniconda\lib;C:\pinokio\bin\miniconda\Library\bin;C:\pinokio\bin\miniconda\pkgs;C:\pinokio\bin\miniconda;C:\pinokio\bin\homebrew\bin;C:\pinokio\bin\homebrew\Cellar;C:\pinokio\bin\aria2;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program
    Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA
    GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft
    MPI\Bin\;C:\Program
    Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program
    Files\dotnet\;C:\Program Files (x86)\NVIDIA
    Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program
    Files\NVIDIA Corporation\Nsight Compute
    2024.1.1\;C:\Users\<USER>\miniconda3;C:\Program Files (x86)\Microsoft Visual
    Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program
    Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program
    Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;;C:\Program
    Files\Docker\Docker\resources\bin;C:\Program
    Files\nodejs\;C:\Users\<USER>\AppData\Local\Muse
    Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft
    VS
    Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\pinokio\bin
  PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
  PLAYWRIGHT_BROWSERS_PATH: C:\pinokio\bin\playwright\browsers
  PROCESSOR_ARCHITECTURE: AMD64
  PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
  PROCESSOR_LEVEL: "6"
  PROCESSOR_REVISION: b701
  ProgramData: C:\ProgramData
  ProgramFiles: C:\Program Files
  ProgramFiles(x86): C:\Program Files (x86)
  ProgramW6432: C:\Program Files
  PSModulePath: C:\Program
    Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
  PUBLIC: C:\Users\<USER>\Windows
  TEMP: C:\pinokio\cache\TEMP
  TMP: C:\pinokio\cache\TMP
  USERDOMAIN: CONQUEST
  USERDOMAIN_ROAMINGPROFILE: CONQUEST
  USERNAME: ullin
  USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
  windir: C:\Windows
  CMAKE_OBJECT_PATH_MAX: 1024
  PYTORCH_ENABLE_MPS_FALLBACK: 1
  TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD: 1
  HOMEBREW_CACHE: C:\pinokio\bin\homebrew\cache
  XDG_CACHE_HOME: C:\pinokio\cache\XDG_CACHE_HOME
  PIP_CACHE_DIR: C:\pinokio\cache\PIP_CACHE_DIR
  UV_CACHE_DIR: C:\pinokio\cache\UV_CACHE_DIR
  PIP_TMPDIR: C:\pinokio\cache\PIP_TMPDIR
  TMPDIR: C:\pinokio\cache\TMPDIR
  XDG_DATA_HOME: C:\pinokio\cache\XDG_DATA_HOME
  XDG_CONFIG_HOME: C:\pinokio\cache\XDG_CONFIG_HOME
  XDG_STATE_HOME: C:\pinokio\cache\XDG_STATE_HOME
  PIP_CONFIG_FILE: C:\pinokio\pipconfig
  CONDARC: C:\pinokio\condarc
  PS1: <<PINOKIO_SHELL>>
  GRADIO_ANALYTICS_ENABLED: "False"
  GRADIO_ALLOWED_PATHS: C:\pinokio
  PINOKIO_SHARE_VAR: url
  PINOKIO_SHARE_CLOUDFLARE: "false"
  PINOKIO_SCRIPT_DEFAULT: "false"
  PINOKIO_DRIVE: C:\pinokio\drive
  GRADIO_TEMP_DIR: C:\pinokio\api\facefusion-pinokio.git\cache\GRADIO_TEMP_DIR
  HF_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\HF_HOME
  TORCH_HOME: C:\pinokio\api\facefusion-pinokio.git\cache\TORCH_HOME
  PINOKIO_SHARE_LOCAL: "false"
  HOMEBREW_PREFIX: C:\pinokio\bin\homebrew
  HOMEBREW_CELLAR: C:\pinokio\bin\homebrew\Cellar
  HOMEBREW_REPOSITORY: C:\pinokio\bin\homebrew
  CONDA_PREFIX: C:\pinokio\bin\miniconda
  CONDA_ENVS_PATH: C:\pinokio\bin\miniconda\envs
  CONDA_PKGS_DIRS: C:\pinokio\bin\miniconda\pkgs
  PYTHON: C:\pinokio\bin\miniconda\python
  CONDA_BAT: C:\pinokio\bin\miniconda\condabin\conda.bat
  CONDA_EXE: C:\pinokio\bin\miniconda\Scripts\conda.exe
  CONDA_PYTHON_EXE: C:\pinokio\bin\miniconda\Scripts\python
  CUDA_HOME: C:\pinokio\bin\miniconda
  GIT_CONFIG_GLOBAL: C:\pinokio\gitconfig
  CONDA_AUTO_ACTIVATE_BASE: "false"
  PYTHONNOUSERSITE: "1"
  UV_PYTHON_PREFERENCE: only-managed
done: true
ready: false
id: b10e49d4-37cd-42b5-8643-776489d98fb7
ts: 1753305880667


