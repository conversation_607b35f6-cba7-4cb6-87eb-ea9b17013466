<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityInstantaneousMetricInstance Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityInstantaneousMetricInstance Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityInstantaneousMetricInstance" -->The instantaneous activity record for a CUPTI metric with instance information.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#66327c3f6821c3f610bddab5a99b1d99">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#cd28ed15e564582a3b58d0ef614eb950">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#b0a626e732a31d5c8d71ff10c81beb1f">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#c11bcf54d6e90fdede46f920aef2b8b8">instance</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#637a3f20d57093e73ca793ab7f4c13f4">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#6de13d072f5acb585184a3d1fea1ab97">pad</a> [2]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#01dfb11ab9544de0e53d1f7d2ea29255">timestamp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#cbaea24e7e5cf2af5706aab2a5265c45">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a CUPTI metric value for a specific metric domain instance (CUPTI_ACTIVITY_KIND_METRIC_INSTANCE) sampled at a particular time. This activity record kind is not produced by the activity API but is included for completeness and ease-of-use. Profiler frameworks built on top of CUPTI that collect metric data may choose to use this type to store the collected metric data. This activity record should be used when metric domain instance information needs to be associated with the metric. <hr><h2>Field Documentation</h2>
<a class="anchor" name="66327c3f6821c3f610bddab5a99b1d99"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::deviceId" ref="66327c3f6821c3f610bddab5a99b1d99" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#66327c3f6821c3f610bddab5a99b1d99">CUpti_ActivityInstantaneousMetricInstance::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device id 
</div>
</div><p>
<a class="anchor" name="cd28ed15e564582a3b58d0ef614eb950"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::flags" ref="cd28ed15e564582a3b58d0ef614eb950" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#cd28ed15e564582a3b58d0ef614eb950">CUpti_ActivityInstantaneousMetricInstance::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this metric. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="b0a626e732a31d5c8d71ff10c81beb1f"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::id" ref="b0a626e732a31d5c8d71ff10c81beb1f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#b0a626e732a31d5c8d71ff10c81beb1f">CUpti_ActivityInstantaneousMetricInstance::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The metric ID. 
</div>
</div><p>
<a class="anchor" name="c11bcf54d6e90fdede46f920aef2b8b8"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::instance" ref="c11bcf54d6e90fdede46f920aef2b8b8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#c11bcf54d6e90fdede46f920aef2b8b8">CUpti_ActivityInstantaneousMetricInstance::instance</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The metric domain instance 
</div>
</div><p>
<a class="anchor" name="637a3f20d57093e73ca793ab7f4c13f4"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::kind" ref="637a3f20d57093e73ca793ab7f4c13f4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#637a3f20d57093e73ca793ab7f4c13f4">CUpti_ActivityInstantaneousMetricInstance::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC_INSTANCE. 
</div>
</div><p>
<a class="anchor" name="6de13d072f5acb585184a3d1fea1ab97"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::pad" ref="6de13d072f5acb585184a3d1fea1ab97" args="[2]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#6de13d072f5acb585184a3d1fea1ab97">CUpti_ActivityInstantaneousMetricInstance::pad</a>[2]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. reserved for internal use 
</div>
</div><p>
<a class="anchor" name="01dfb11ab9544de0e53d1f7d2ea29255"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::timestamp" ref="01dfb11ab9544de0e53d1f7d2ea29255" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#01dfb11ab9544de0e53d1f7d2ea29255">CUpti_ActivityInstantaneousMetricInstance::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp at which metric is sampled 
</div>
</div><p>
<a class="anchor" name="cbaea24e7e5cf2af5706aab2a5265c45"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetricInstance::value" ref="cbaea24e7e5cf2af5706aab2a5265c45" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a> <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#cbaea24e7e5cf2af5706aab2a5265c45">CUpti_ActivityInstantaneousMetricInstance::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The metric value. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
