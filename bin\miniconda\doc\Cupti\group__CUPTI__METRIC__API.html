<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Metric API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Metric API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">union &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A metric value.  <a href="unionCUpti__MetricValue.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">ID for a metric.  <a href="#g0d2977d7b73cd78f216d5526998a92d4"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g66397dfdbcf6ebf23d8cdf656c35fd52">CUpti_MetricAttribute</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg66397dfdbcf6ebf23d8cdf656c35fd528fb716739d69666134870d79c4b42ecf">CUPTI_METRIC_ATTR_NAME</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg66397dfdbcf6ebf23d8cdf656c35fd52955f07f56afa0873d04b6eaa80c1141a">CUPTI_METRIC_ATTR_SHORT_DESCRIPTION</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg66397dfdbcf6ebf23d8cdf656c35fd52cdf0d385cee6cdd967cfeeb6f9b14554">CUPTI_METRIC_ATTR_LONG_DESCRIPTION</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg66397dfdbcf6ebf23d8cdf656c35fd52f108e9777e35a7a71d8e0ebd92917f58">CUPTI_METRIC_ATTR_CATEGORY</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg66397dfdbcf6ebf23d8cdf656c35fd52f08e7aaee1a2134a88039b85f113bf27">CUPTI_METRIC_ATTR_VALUE_KIND</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg66397dfdbcf6ebf23d8cdf656c35fd52bd352ebdb48081e8cdeacb9038f2943e">CUPTI_METRIC_ATTR_EVALUATION_MODE</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Metric attributes.  <a href="group__CUPTI__METRIC__API.html#g66397dfdbcf6ebf23d8cdf656c35fd52">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g6394f0e2d13535f97bfba7554ffd3f83">CUpti_MetricCategory</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg6394f0e2d13535f97bfba7554ffd3f8331e7dce80562e54effa93e024650333a">CUPTI_METRIC_CATEGORY_MEMORY</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg6394f0e2d13535f97bfba7554ffd3f83e0a1c9d57c720e8763e8fb234bfee604">CUPTI_METRIC_CATEGORY_INSTRUCTION</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg6394f0e2d13535f97bfba7554ffd3f83c18381eaece17a3c297c6d47dc16fed1">CUPTI_METRIC_CATEGORY_MULTIPROCESSOR</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg6394f0e2d13535f97bfba7554ffd3f83c89334d790d1993f91f43b5d96c2b37c">CUPTI_METRIC_CATEGORY_CACHE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg6394f0e2d13535f97bfba7554ffd3f83afaa5e3c4f5ec0d1f828216fe3ca0963">CUPTI_METRIC_CATEGORY_TEXTURE</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg6394f0e2d13535f97bfba7554ffd3f83a2b6ff156ee0c92acb10ec294fb5c747">CUPTI_METRIC_CATEGORY_NVLINK</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg6394f0e2d13535f97bfba7554ffd3f833002802a0fe1aa590c91d005ca2d49e8">CUPTI_METRIC_CATEGORY_PCIE</a> =  6
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A metric category.  <a href="group__CUPTI__METRIC__API.html#g6394f0e2d13535f97bfba7554ffd3f83">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g59396bc237d98ee0595e5743bde89b9d">CUpti_MetricEvaluationMode</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg59396bc237d98ee0595e5743bde89b9db67ce2cfb1b1f50bc30ef1aee9fa3de9">CUPTI_METRIC_EVALUATION_MODE_PER_INSTANCE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg59396bc237d98ee0595e5743bde89b9d4a02ff640639c45e9becfbf087b30bef">CUPTI_METRIC_EVALUATION_MODE_AGGREGATE</a> =  1 &lt;&lt; 1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A metric evaluation mode.  <a href="group__CUPTI__METRIC__API.html#g59396bc237d98ee0595e5743bde89b9d">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g8205fe38839f3804696bc7e68640cd95">CUpti_MetricPropertyDeviceClass</a> </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Device class.  <a href="group__CUPTI__METRIC__API.html#g8205fe38839f3804696bc7e68640cd95">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g5bafb626ac31ac98400d9191316871f7">CUpti_MetricPropertyID</a> </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Metric device properties.  <a href="group__CUPTI__METRIC__API.html#g5bafb626ac31ac98400d9191316871f7">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g7ed011480e0cfd81d50cb0e508f6cc16">CUpti_MetricValueKind</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg7ed011480e0cfd81d50cb0e508f6cc163491fde36f93719b505716aa0cc2e1d4">CUPTI_METRIC_VALUE_KIND_DOUBLE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg7ed011480e0cfd81d50cb0e508f6cc163f0def845685cfa02cff57a4bb113f9d">CUPTI_METRIC_VALUE_KIND_UINT64</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg7ed011480e0cfd81d50cb0e508f6cc169a67e0faf264af1142ec70cf447be1ee">CUPTI_METRIC_VALUE_KIND_PERCENT</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg7ed011480e0cfd81d50cb0e508f6cc1684aa43466bca21e23a61764456a163c0">CUPTI_METRIC_VALUE_KIND_THROUGHPUT</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg7ed011480e0cfd81d50cb0e508f6cc1687d2df4cc54ef0583de003bccd910d70">CUPTI_METRIC_VALUE_KIND_INT64</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__METRIC__API.html#gg7ed011480e0cfd81d50cb0e508f6cc16651fe23d5ebbee64b4c1d5a5a5268f81">CUPTI_METRIC_VALUE_KIND_UTILIZATION_LEVEL</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Kinds of metric values.  <a href="group__CUPTI__METRIC__API.html#g7ed011480e0cfd81d50cb0e508f6cc16">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g60cd86ce5f31121816ea1e3f965ac669">CUpti_MetricValueUtilizationLevel</a> </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enumeration of utilization levels for metrics values of kind CUPTI_METRIC_VALUE_KIND_UTILIZATION_LEVEL. Utilization values can vary from IDLE (0) to MAX (10) but the enumeration only provides specific names for a few values. <br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#gb9b4e76fc9a5a7b53e201838a82cb01b">cuptiDeviceEnumMetrics</a> (CUdevice device, size_t *arraySizeBytes, <a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *metricArray)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the metrics for a device.  <a href="#gb9b4e76fc9a5a7b53e201838a82cb01b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g324da71188baec5939c57c1f24a8773b">cuptiDeviceGetNumMetrics</a> (CUdevice device, uint32_t *numMetrics)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the number of metrics for a device.  <a href="#g324da71188baec5939c57c1f24a8773b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g6edf9f093d3e05644821c0a1548ebe22">cuptiEnumMetrics</a> (size_t *arraySizeBytes, <a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *metricArray)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get all the metrics available on any device.  <a href="#g6edf9f093d3e05644821c0a1548ebe22"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#ga977bb7bfa362130f2aeb7bcdba57fe8">cuptiGetNumMetrics</a> (uint32_t *numMetrics)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the total number of metrics available on any device.  <a href="#ga977bb7bfa362130f2aeb7bcdba57fe8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#gffab8f5a74b98e7680710203e3932450">cuptiMetricCreateEventGroupSets</a> (CUcontext context, size_t metricIdArraySizeBytes, <a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *metricIdArray, <a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> **eventGroupPasses)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">For a set of metrics, get the grouping that indicates the number of passes and the event groups necessary to collect the events required for those metrics.  <a href="#gffab8f5a74b98e7680710203e3932450"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g7b6aa1f0bb09627f89bbbeb0300bc67a">cuptiMetricEnumEvents</a> (<a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, size_t *eventIdArraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *eventIdArray)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the events required to calculating a metric.  <a href="#g7b6aa1f0bb09627f89bbbeb0300bc67a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g5bbb72ce28d7f2a6cd02607379c72bc9">cuptiMetricEnumProperties</a> (<a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, size_t *propIdArraySizeBytes, <a class="el" href="group__CUPTI__METRIC__API.html#g5bafb626ac31ac98400d9191316871f7">CUpti_MetricPropertyID</a> *propIdArray)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the properties required to calculating a metric.  <a href="#g5bbb72ce28d7f2a6cd02607379c72bc9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#gad77ae5441ca5c8cee0156b4ecc4d3f5">cuptiMetricGetAttribute</a> (<a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, <a class="el" href="group__CUPTI__METRIC__API.html#g66397dfdbcf6ebf23d8cdf656c35fd52">CUpti_MetricAttribute</a> attrib, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get a metric attribute.  <a href="#gad77ae5441ca5c8cee0156b4ecc4d3f5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g8f6838118a1782e1aec6c3ffae1df81d">cuptiMetricGetIdFromName</a> (CUdevice device, const char *metricName, <a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *metric)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Find an metric by name.  <a href="#g8f6838118a1782e1aec6c3ffae1df81d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g2e278d4e7e4237f863ed70facfa9c771">cuptiMetricGetNumEvents</a> (<a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, uint32_t *numEvents)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get number of events required to calculate a metric.  <a href="#g2e278d4e7e4237f863ed70facfa9c771"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#gbdf8e8a3c0e561e8d85604669682b4ed">cuptiMetricGetNumProperties</a> (<a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, uint32_t *numProp)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get number of properties required to calculate a metric.  <a href="#gbdf8e8a3c0e561e8d85604669682b4ed"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#g831ee4cb8bd1756020d294c4740f5981">cuptiMetricGetRequiredEventGroupSets</a> (CUcontext context, <a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, <a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> **eventGroupSets)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">For a metric get the groups of events that must be collected in the same pass.  <a href="#g831ee4cb8bd1756020d294c4740f5981"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#gf42dcb1d349f91265e7809bbba2fc01e">cuptiMetricGetValue</a> (CUdevice device, <a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, size_t eventIdArraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *eventIdArray, size_t eventValueArraySizeBytes, uint64_t *eventValueArray, uint64_t timeDuration, <a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a> *metricValue)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Calculate the value for a metric.  <a href="#gf42dcb1d349f91265e7809bbba2fc01e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__METRIC__API.html#gc3ec4701e9878845536282a14ed27245">cuptiMetricGetValue2</a> (<a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> metric, size_t eventIdArraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *eventIdArray, size_t eventValueArraySizeBytes, uint64_t *eventValueArray, size_t propIdArraySizeBytes, <a class="el" href="group__CUPTI__METRIC__API.html#g5bafb626ac31ac98400d9191316871f7">CUpti_MetricPropertyID</a> *propIdArray, size_t propValueArraySizeBytes, uint64_t *propValueArray, <a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a> *metricValue)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Calculate the value for a metric.  <a href="#gc3ec4701e9878845536282a14ed27245"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI Metric API.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd>CUPTI metric API from the header cupti_metrics.h are not supported on devices with compute capability 7.5 and higher (i.e. Turing and later GPU architectures). These API will be deprecated in a future CUDA release. These are replaced by Profiling API in the header cupti_profiler_target.h and Perfworks metrics API in the headers nvperf_host.h and nvperf_target.h which are supported on devices with compute capability 7.0 and higher (i.e. Volta and later GPU architectures). </dd></dl>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g0d2977d7b73cd78f216d5526998a92d4"></a><!-- doxytag: member="cupti_metrics.h::CUpti_MetricID" ref="g0d2977d7b73cd78f216d5526998a92d4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint32_t <a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A metric provides a measure of some aspect of the device. 
</div>
</div><p>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g66397dfdbcf6ebf23d8cdf656c35fd52"></a><!-- doxytag: member="cupti_metrics.h::CUpti_MetricAttribute" ref="g66397dfdbcf6ebf23d8cdf656c35fd52" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__METRIC__API.html#g66397dfdbcf6ebf23d8cdf656c35fd52">CUpti_MetricAttribute</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Metric attributes describe properties of a metric. These attributes can be read using <a class="el" href="group__CUPTI__METRIC__API.html#gad77ae5441ca5c8cee0156b4ecc4d3f5">cuptiMetricGetAttribute</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg66397dfdbcf6ebf23d8cdf656c35fd528fb716739d69666134870d79c4b42ecf"></a><!-- doxytag: member="CUPTI_METRIC_ATTR_NAME" ref="gg66397dfdbcf6ebf23d8cdf656c35fd528fb716739d69666134870d79c4b42ecf" args="" -->CUPTI_METRIC_ATTR_NAME</em>&nbsp;</td><td>
Metric name. Value is a null terminated const c-string. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg66397dfdbcf6ebf23d8cdf656c35fd52955f07f56afa0873d04b6eaa80c1141a"></a><!-- doxytag: member="CUPTI_METRIC_ATTR_SHORT_DESCRIPTION" ref="gg66397dfdbcf6ebf23d8cdf656c35fd52955f07f56afa0873d04b6eaa80c1141a" args="" -->CUPTI_METRIC_ATTR_SHORT_DESCRIPTION</em>&nbsp;</td><td>
Short description of metric. Value is a null terminated const c-string. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg66397dfdbcf6ebf23d8cdf656c35fd52cdf0d385cee6cdd967cfeeb6f9b14554"></a><!-- doxytag: member="CUPTI_METRIC_ATTR_LONG_DESCRIPTION" ref="gg66397dfdbcf6ebf23d8cdf656c35fd52cdf0d385cee6cdd967cfeeb6f9b14554" args="" -->CUPTI_METRIC_ATTR_LONG_DESCRIPTION</em>&nbsp;</td><td>
Long description of metric. Value is a null terminated const c-string. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg66397dfdbcf6ebf23d8cdf656c35fd52f108e9777e35a7a71d8e0ebd92917f58"></a><!-- doxytag: member="CUPTI_METRIC_ATTR_CATEGORY" ref="gg66397dfdbcf6ebf23d8cdf656c35fd52f108e9777e35a7a71d8e0ebd92917f58" args="" -->CUPTI_METRIC_ATTR_CATEGORY</em>&nbsp;</td><td>
Category of the metric. Value is of type CUpti_MetricCategory. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg66397dfdbcf6ebf23d8cdf656c35fd52f08e7aaee1a2134a88039b85f113bf27"></a><!-- doxytag: member="CUPTI_METRIC_ATTR_VALUE_KIND" ref="gg66397dfdbcf6ebf23d8cdf656c35fd52f08e7aaee1a2134a88039b85f113bf27" args="" -->CUPTI_METRIC_ATTR_VALUE_KIND</em>&nbsp;</td><td>
Value type of the metric. Value is of type CUpti_MetricValueKind. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg66397dfdbcf6ebf23d8cdf656c35fd52bd352ebdb48081e8cdeacb9038f2943e"></a><!-- doxytag: member="CUPTI_METRIC_ATTR_EVALUATION_MODE" ref="gg66397dfdbcf6ebf23d8cdf656c35fd52bd352ebdb48081e8cdeacb9038f2943e" args="" -->CUPTI_METRIC_ATTR_EVALUATION_MODE</em>&nbsp;</td><td>
Metric evaluation mode. Value is of type CUpti_MetricEvaluationMode. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6394f0e2d13535f97bfba7554ffd3f83"></a><!-- doxytag: member="cupti_metrics.h::CUpti_MetricCategory" ref="g6394f0e2d13535f97bfba7554ffd3f83" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__METRIC__API.html#g6394f0e2d13535f97bfba7554ffd3f83">CUpti_MetricCategory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Each metric is assigned to a category that represents the general type of the metric. A metric's category is accessed using <a class="el" href="group__CUPTI__METRIC__API.html#gad77ae5441ca5c8cee0156b4ecc4d3f5">cuptiMetricGetAttribute</a> and the CUPTI_METRIC_ATTR_CATEGORY attribute. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg6394f0e2d13535f97bfba7554ffd3f8331e7dce80562e54effa93e024650333a"></a><!-- doxytag: member="CUPTI_METRIC_CATEGORY_MEMORY" ref="gg6394f0e2d13535f97bfba7554ffd3f8331e7dce80562e54effa93e024650333a" args="" -->CUPTI_METRIC_CATEGORY_MEMORY</em>&nbsp;</td><td>
A memory related metric. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6394f0e2d13535f97bfba7554ffd3f83e0a1c9d57c720e8763e8fb234bfee604"></a><!-- doxytag: member="CUPTI_METRIC_CATEGORY_INSTRUCTION" ref="gg6394f0e2d13535f97bfba7554ffd3f83e0a1c9d57c720e8763e8fb234bfee604" args="" -->CUPTI_METRIC_CATEGORY_INSTRUCTION</em>&nbsp;</td><td>
An instruction related metric. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6394f0e2d13535f97bfba7554ffd3f83c18381eaece17a3c297c6d47dc16fed1"></a><!-- doxytag: member="CUPTI_METRIC_CATEGORY_MULTIPROCESSOR" ref="gg6394f0e2d13535f97bfba7554ffd3f83c18381eaece17a3c297c6d47dc16fed1" args="" -->CUPTI_METRIC_CATEGORY_MULTIPROCESSOR</em>&nbsp;</td><td>
A multiprocessor related metric. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6394f0e2d13535f97bfba7554ffd3f83c89334d790d1993f91f43b5d96c2b37c"></a><!-- doxytag: member="CUPTI_METRIC_CATEGORY_CACHE" ref="gg6394f0e2d13535f97bfba7554ffd3f83c89334d790d1993f91f43b5d96c2b37c" args="" -->CUPTI_METRIC_CATEGORY_CACHE</em>&nbsp;</td><td>
A cache related metric. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6394f0e2d13535f97bfba7554ffd3f83afaa5e3c4f5ec0d1f828216fe3ca0963"></a><!-- doxytag: member="CUPTI_METRIC_CATEGORY_TEXTURE" ref="gg6394f0e2d13535f97bfba7554ffd3f83afaa5e3c4f5ec0d1f828216fe3ca0963" args="" -->CUPTI_METRIC_CATEGORY_TEXTURE</em>&nbsp;</td><td>
A texture related metric. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6394f0e2d13535f97bfba7554ffd3f83a2b6ff156ee0c92acb10ec294fb5c747"></a><!-- doxytag: member="CUPTI_METRIC_CATEGORY_NVLINK" ref="gg6394f0e2d13535f97bfba7554ffd3f83a2b6ff156ee0c92acb10ec294fb5c747" args="" -->CUPTI_METRIC_CATEGORY_NVLINK</em>&nbsp;</td><td>
A Nvlink related metric. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg6394f0e2d13535f97bfba7554ffd3f833002802a0fe1aa590c91d005ca2d49e8"></a><!-- doxytag: member="CUPTI_METRIC_CATEGORY_PCIE" ref="gg6394f0e2d13535f97bfba7554ffd3f833002802a0fe1aa590c91d005ca2d49e8" args="" -->CUPTI_METRIC_CATEGORY_PCIE</em>&nbsp;</td><td>
A PCIe related metric. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g59396bc237d98ee0595e5743bde89b9d"></a><!-- doxytag: member="cupti_metrics.h::CUpti_MetricEvaluationMode" ref="g59396bc237d98ee0595e5743bde89b9d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__METRIC__API.html#g59396bc237d98ee0595e5743bde89b9d">CUpti_MetricEvaluationMode</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A metric can be evaluated per hardware instance to know the load balancing across instances of a domain or the metric can be evaluated in aggregate mode when the events involved in metric evaluation are from different event domains. It might be possible to evaluate some metrics in both modes for convenience. A metric's evaluation mode is accessed using <a class="el" href="group__CUPTI__METRIC__API.html#g59396bc237d98ee0595e5743bde89b9d">CUpti_MetricEvaluationMode</a> and the CUPTI_METRIC_ATTR_EVALUATION_MODE attribute. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg59396bc237d98ee0595e5743bde89b9db67ce2cfb1b1f50bc30ef1aee9fa3de9"></a><!-- doxytag: member="CUPTI_METRIC_EVALUATION_MODE_PER_INSTANCE" ref="gg59396bc237d98ee0595e5743bde89b9db67ce2cfb1b1f50bc30ef1aee9fa3de9" args="" -->CUPTI_METRIC_EVALUATION_MODE_PER_INSTANCE</em>&nbsp;</td><td>
If this bit is set, the metric can be profiled for each instance of the domain. The event values passed to <a class="el" href="group__CUPTI__METRIC__API.html#gf42dcb1d349f91265e7809bbba2fc01e">cuptiMetricGetValue</a> can contain values for one instance of the domain. And <a class="el" href="group__CUPTI__METRIC__API.html#gf42dcb1d349f91265e7809bbba2fc01e">cuptiMetricGetValue</a> can be called for each instance. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg59396bc237d98ee0595e5743bde89b9d4a02ff640639c45e9becfbf087b30bef"></a><!-- doxytag: member="CUPTI_METRIC_EVALUATION_MODE_AGGREGATE" ref="gg59396bc237d98ee0595e5743bde89b9d4a02ff640639c45e9becfbf087b30bef" args="" -->CUPTI_METRIC_EVALUATION_MODE_AGGREGATE</em>&nbsp;</td><td>
If this bit is set, the metric can be profiled over all instances. The event values passed to <a class="el" href="group__CUPTI__METRIC__API.html#gf42dcb1d349f91265e7809bbba2fc01e">cuptiMetricGetValue</a> can be aggregated values of events for all instances of the domain. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g8205fe38839f3804696bc7e68640cd95"></a><!-- doxytag: member="cupti_metrics.h::CUpti_MetricPropertyDeviceClass" ref="g8205fe38839f3804696bc7e68640cd95" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__METRIC__API.html#g8205fe38839f3804696bc7e68640cd95">CUpti_MetricPropertyDeviceClass</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enumeration of device classes for metric property CUPTI_METRIC_PROPERTY_DEVICE_CLASS. 
</div>
</div><p>
<a class="anchor" name="g5bafb626ac31ac98400d9191316871f7"></a><!-- doxytag: member="cupti_metrics.h::CUpti_MetricPropertyID" ref="g5bafb626ac31ac98400d9191316871f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__METRIC__API.html#g5bafb626ac31ac98400d9191316871f7">CUpti_MetricPropertyID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Metric device properties describe device properties which are needed for a metric. Some of these properties can be collected using cuDeviceGetAttribute. 
</div>
</div><p>
<a class="anchor" name="g7ed011480e0cfd81d50cb0e508f6cc16"></a><!-- doxytag: member="cupti_metrics.h::CUpti_MetricValueKind" ref="g7ed011480e0cfd81d50cb0e508f6cc16" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__METRIC__API.html#g7ed011480e0cfd81d50cb0e508f6cc16">CUpti_MetricValueKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Metric values can be one of several different kinds. Corresponding to each kind is a member of the <a class="el" href="unionCUpti__MetricValue.html" title="A metric value.">CUpti_MetricValue</a> union. The metric value returned by <a class="el" href="group__CUPTI__METRIC__API.html#gf42dcb1d349f91265e7809bbba2fc01e">cuptiMetricGetValue</a> should be accessed using the appropriate member of that union based on its value kind. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg7ed011480e0cfd81d50cb0e508f6cc163491fde36f93719b505716aa0cc2e1d4"></a><!-- doxytag: member="CUPTI_METRIC_VALUE_KIND_DOUBLE" ref="gg7ed011480e0cfd81d50cb0e508f6cc163491fde36f93719b505716aa0cc2e1d4" args="" -->CUPTI_METRIC_VALUE_KIND_DOUBLE</em>&nbsp;</td><td>
The metric value is a 64-bit double. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7ed011480e0cfd81d50cb0e508f6cc163f0def845685cfa02cff57a4bb113f9d"></a><!-- doxytag: member="CUPTI_METRIC_VALUE_KIND_UINT64" ref="gg7ed011480e0cfd81d50cb0e508f6cc163f0def845685cfa02cff57a4bb113f9d" args="" -->CUPTI_METRIC_VALUE_KIND_UINT64</em>&nbsp;</td><td>
The metric value is a 64-bit unsigned integer. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7ed011480e0cfd81d50cb0e508f6cc169a67e0faf264af1142ec70cf447be1ee"></a><!-- doxytag: member="CUPTI_METRIC_VALUE_KIND_PERCENT" ref="gg7ed011480e0cfd81d50cb0e508f6cc169a67e0faf264af1142ec70cf447be1ee" args="" -->CUPTI_METRIC_VALUE_KIND_PERCENT</em>&nbsp;</td><td>
The metric value is a percentage represented by a 64-bit double. For example, 57.5% is represented by the value 57.5. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7ed011480e0cfd81d50cb0e508f6cc1684aa43466bca21e23a61764456a163c0"></a><!-- doxytag: member="CUPTI_METRIC_VALUE_KIND_THROUGHPUT" ref="gg7ed011480e0cfd81d50cb0e508f6cc1684aa43466bca21e23a61764456a163c0" args="" -->CUPTI_METRIC_VALUE_KIND_THROUGHPUT</em>&nbsp;</td><td>
The metric value is a throughput represented by a 64-bit integer. The unit for throughput values is bytes/second. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7ed011480e0cfd81d50cb0e508f6cc1687d2df4cc54ef0583de003bccd910d70"></a><!-- doxytag: member="CUPTI_METRIC_VALUE_KIND_INT64" ref="gg7ed011480e0cfd81d50cb0e508f6cc1687d2df4cc54ef0583de003bccd910d70" args="" -->CUPTI_METRIC_VALUE_KIND_INT64</em>&nbsp;</td><td>
The metric value is a 64-bit signed integer. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7ed011480e0cfd81d50cb0e508f6cc16651fe23d5ebbee64b4c1d5a5a5268f81"></a><!-- doxytag: member="CUPTI_METRIC_VALUE_KIND_UTILIZATION_LEVEL" ref="gg7ed011480e0cfd81d50cb0e508f6cc16651fe23d5ebbee64b4c1d5a5a5268f81" args="" -->CUPTI_METRIC_VALUE_KIND_UTILIZATION_LEVEL</em>&nbsp;</td><td>
The metric value is a utilization level, as represented by CUpti_MetricValueUtilizationLevel. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="gb9b4e76fc9a5a7b53e201838a82cb01b"></a><!-- doxytag: member="cupti_metrics.h::cuptiDeviceEnumMetrics" ref="gb9b4e76fc9a5a7b53e201838a82cb01b" args="(CUdevice device, size_t *arraySizeBytes, CUpti_MetricID *metricArray)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceEnumMetrics           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>arraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *&nbsp;</td>
          <td class="paramname"> <em>metricArray</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the metric IDs in <code>metricArray</code> for a device. The size of the <code>metricArray</code> buffer is given by <code>*arraySizeBytes</code>. The size of the <code>metricArray</code> buffer must be at least <code>numMetrics</code> * sizeof(CUpti_MetricID) or else all metric IDs will not be returned. The value returned in <code>*arraySizeBytes</code> contains the number of bytes returned in <code>metricArray</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>arraySizeBytes</em>&nbsp;</td><td>The size of <code>metricArray</code> in bytes, and returns the number of bytes written to <code>metricArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metricArray</em>&nbsp;</td><td>Returns the IDs of the metrics for the device</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>arraySizeBytes</code> or <code>metricArray</code> are NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g324da71188baec5939c57c1f24a8773b"></a><!-- doxytag: member="cupti_metrics.h::cuptiDeviceGetNumMetrics" ref="g324da71188baec5939c57c1f24a8773b" args="(CUdevice device, uint32_t *numMetrics)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceGetNumMetrics           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numMetrics</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of metrics available for a device.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>numMetrics</em>&nbsp;</td><td>Returns the number of metrics available for the device</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>numMetrics</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6edf9f093d3e05644821c0a1548ebe22"></a><!-- doxytag: member="cupti_metrics.h::cuptiEnumMetrics" ref="g6edf9f093d3e05644821c0a1548ebe22" args="(size_t *arraySizeBytes, CUpti_MetricID *metricArray)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEnumMetrics           </td>
          <td>(</td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>arraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *&nbsp;</td>
          <td class="paramname"> <em>metricArray</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the metric IDs in <code>metricArray</code> for all CUDA-capable devices. The size of the <code>metricArray</code> buffer is given by <code>*arraySizeBytes</code>. The size of the <code>metricArray</code> buffer must be at least <code>numMetrics</code> * sizeof(CUpti_MetricID) or all metric IDs will not be returned. The value returned in <code>*arraySizeBytes</code> contains the number of bytes returned in <code>metricArray</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>arraySizeBytes</em>&nbsp;</td><td>The size of <code>metricArray</code> in bytes, and returns the number of bytes written to <code>metricArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metricArray</em>&nbsp;</td><td>Returns the IDs of the metrics</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>arraySizeBytes</code> or <code>metricArray</code> are NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="ga977bb7bfa362130f2aeb7bcdba57fe8"></a><!-- doxytag: member="cupti_metrics.h::cuptiGetNumMetrics" ref="ga977bb7bfa362130f2aeb7bcdba57fe8" args="(uint32_t *numMetrics)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetNumMetrics           </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numMetrics</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the total number of metrics available on any CUDA-capable devices.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>numMetrics</em>&nbsp;</td><td>Returns the number of metrics</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>numMetrics</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gffab8f5a74b98e7680710203e3932450"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricCreateEventGroupSets" ref="gffab8f5a74b98e7680710203e3932450" args="(CUcontext context, size_t metricIdArraySizeBytes, CUpti_MetricID *metricIdArray, CUpti_EventGroupSets **eventGroupPasses)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricCreateEventGroupSets           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>metricIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *&nbsp;</td>
          <td class="paramname"> <em>metricIdArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> **&nbsp;</td>
          <td class="paramname"> <em>eventGroupPasses</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For a set of metrics, get the grouping that indicates the number of passes and the event groups necessary to collect the events required for those metrics.<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__EVENT__API.html#g0fd307d429d4e37f61f45472de069910" title="For a set of events, get the grouping that indicates the number of passes and the...">cuptiEventGroupSetsCreate</a> for details on event group set creation.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context for event collection </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metricIdArraySizeBytes</em>&nbsp;</td><td>Size of the metricIdArray in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metricIdArray</em>&nbsp;</td><td>Array of metric IDs </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventGroupPasses</em>&nbsp;</td><td>Returns a <a class="el" href="structCUpti__EventGroupSets.html" title="A set of event group sets.">CUpti_EventGroupSets</a> object that indicates the number of passes required to collect the events and the events to collect on each pass</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>metricIdArray</code> or <code>eventGroupPasses</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g7b6aa1f0bb09627f89bbbeb0300bc67a"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricEnumEvents" ref="g7b6aa1f0bb09627f89bbbeb0300bc67a" args="(CUpti_MetricID metric, size_t *eventIdArraySizeBytes, CUpti_EventID *eventIdArray)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricEnumEvents           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>eventIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *&nbsp;</td>
          <td class="paramname"> <em>eventIdArray</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Gets the event IDs in <code>eventIdArray</code> required to calculate a <code>metric</code>. The size of the <code>eventIdArray</code> buffer is given by <code>*eventIdArraySizeBytes</code> and must be at least <code>numEvents</code> * sizeof(CUpti_EventID) or all events will not be returned. The value returned in <code>*eventIdArraySizeBytes</code> contains the number of bytes returned in <code>eventIdArray</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>ID of the metric </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArraySizeBytes</em>&nbsp;</td><td>The size of <code>eventIdArray</code> in bytes, and returns the number of bytes written to <code>eventIdArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArray</em>&nbsp;</td><td>Returns the IDs of the events required to calculate <code>metric</code> </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventIdArraySizeBytes</code> or <code>eventIdArray</code> are NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g5bbb72ce28d7f2a6cd02607379c72bc9"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricEnumProperties" ref="g5bbb72ce28d7f2a6cd02607379c72bc9" args="(CUpti_MetricID metric, size_t *propIdArraySizeBytes, CUpti_MetricPropertyID *propIdArray)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricEnumProperties           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>propIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g5bafb626ac31ac98400d9191316871f7">CUpti_MetricPropertyID</a> *&nbsp;</td>
          <td class="paramname"> <em>propIdArray</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Gets the property IDs in <code>propIdArray</code> required to calculate a <code>metric</code>. The size of the <code>propIdArray</code> buffer is given by <code>*propIdArraySizeBytes</code> and must be at least <code>numProp</code> * sizeof(CUpti_DeviceAttribute) or all properties will not be returned. The value returned in <code>*propIdArraySizeBytes</code> contains the number of bytes returned in <code>propIdArray</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>ID of the metric </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>propIdArraySizeBytes</em>&nbsp;</td><td>The size of <code>propIdArray</code> in bytes, and returns the number of bytes written to <code>propIdArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>propIdArray</em>&nbsp;</td><td>Returns the IDs of the properties required to calculate <code>metric</code> </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>propIdArraySizeBytes</code> or <code>propIdArray</code> are NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gad77ae5441ca5c8cee0156b4ecc4d3f5"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricGetAttribute" ref="gad77ae5441ca5c8cee0156b4ecc4d3f5" args="(CUpti_MetricID metric, CUpti_MetricAttribute attrib, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricGetAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g66397dfdbcf6ebf23d8cdf656c35fd52">CUpti_MetricAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns a metric attribute in <code>*value</code>. The size of the <code>value</code> buffer is given by <code>*valueSize</code>. The value returned in <code>*valueSize</code> contains the number of bytes returned in <code>value</code>.<p>
If the attribute value is a c-string that is longer than <code>*valueSize</code>, then only the first <code>*valueSize</code> characters will be returned and there will be no terminating null byte.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>ID of the metric </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>attrib</em>&nbsp;</td><td>The metric attribute to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>The size of the <code>value</code> buffer in bytes, and returns the number of bytes written to <code>value</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>Returns the attribute's value</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attrib</code> is not a metric attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>For non-c-string attribute values, indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g8f6838118a1782e1aec6c3ffae1df81d"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricGetIdFromName" ref="g8f6838118a1782e1aec6c3ffae1df81d" args="(CUdevice device, const char *metricName, CUpti_MetricID *metric)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricGetIdFromName           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>metricName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> *&nbsp;</td>
          <td class="paramname"> <em>metric</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Find a metric by name and return the metric ID in <code>*metric</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metricName</em>&nbsp;</td><td>The name of metric to find </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>Returns the ID of the found metric or undefined if unable to find the metric</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_NAME</em>&nbsp;</td><td>if unable to find a metric with name <code>metricName</code>. In this case <code>*metric</code> is undefined </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>metricName</code> or <code>metric</code> are NULL. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2e278d4e7e4237f863ed70facfa9c771"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricGetNumEvents" ref="g2e278d4e7e4237f863ed70facfa9c771" args="(CUpti_MetricID metric, uint32_t *numEvents)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricGetNumEvents           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numEvents</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of events in <code>numEvents</code> that are required to calculate a metric.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>ID of the metric </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>numEvents</em>&nbsp;</td><td>Returns the number of events required for the metric</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>numEvents</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gbdf8e8a3c0e561e8d85604669682b4ed"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricGetNumProperties" ref="gbdf8e8a3c0e561e8d85604669682b4ed" args="(CUpti_MetricID metric, uint32_t *numProp)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricGetNumProperties           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numProp</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of properties in <code>numProp</code> that are required to calculate a metric.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>ID of the metric </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>numProp</em>&nbsp;</td><td>Returns the number of properties required for the metric</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>numProp</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g831ee4cb8bd1756020d294c4740f5981"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricGetRequiredEventGroupSets" ref="g831ee4cb8bd1756020d294c4740f5981" args="(CUcontext context, CUpti_MetricID metric, CUpti_EventGroupSets **eventGroupSets)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricGetRequiredEventGroupSets           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> **&nbsp;</td>
          <td class="paramname"> <em>eventGroupSets</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For a metric get the groups of events that must be collected in the same pass to ensure that the metric is calculated correctly. If the events are not collected as specified then the metric value may be inaccurate.<p>
The function returns NULL if a metric does not have any required event group. In this case the events needed for the metric can be grouped in any manner for collection.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context for event collection </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>The metric ID </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventGroupSets</em>&nbsp;</td><td>Returns a <a class="el" href="structCUpti__EventGroupSets.html" title="A set of event group sets.">CUpti_EventGroupSets</a> object that indicates the events that must be collected in the same pass to ensure the metric is calculated correctly. Returns NULL if no grouping is required for metric </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gf42dcb1d349f91265e7809bbba2fc01e"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricGetValue" ref="gf42dcb1d349f91265e7809bbba2fc01e" args="(CUdevice device, CUpti_MetricID metric, size_t eventIdArraySizeBytes, CUpti_EventID *eventIdArray, size_t eventValueArraySizeBytes, uint64_t *eventValueArray, uint64_t timeDuration, CUpti_MetricValue *metricValue)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricGetValue           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>eventIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *&nbsp;</td>
          <td class="paramname"> <em>eventIdArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>eventValueArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>eventValueArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&nbsp;</td>
          <td class="paramname"> <em>timeDuration</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a> *&nbsp;</td>
          <td class="paramname"> <em>metricValue</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Use the events collected for a metric to calculate the metric value. Metric value evaluation depends on the evaluation mode <a class="el" href="group__CUPTI__METRIC__API.html#g59396bc237d98ee0595e5743bde89b9d">CUpti_MetricEvaluationMode</a> that the metric supports. If a metric has evaluation mode as CUPTI_METRIC_EVALUATION_MODE_PER_INSTANCE, then it assumes that the input event value is for one domain instance. If a metric has evaluation mode as CUPTI_METRIC_EVALUATION_MODE_AGGREGATE, it assumes that input event values are normalized to represent all domain instances on a device. For the most accurate metric collection, the events required for the metric should be collected for all profiled domain instances. For example, to collect all instances of an event, set the CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES attribute on the group containing the event to 1. The normalized value for the event is then: (<code>sum_event_values</code> * <code>totalInstanceCount</code>) / <code>instanceCount</code>, where <code>sum_event_values</code> is the summation of the event values across all profiled domain instances, <code>totalInstanceCount</code> is obtained from querying CUPTI_EVENT_DOMAIN_ATTR_TOTAL_INSTANCE_COUNT and <code>instanceCount</code> is obtained from querying CUPTI_EVENT_GROUP_ATTR_INSTANCE_COUNT (or CUPTI_EVENT_DOMAIN_ATTR_INSTANCE_COUNT).<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device that the metric is being calculated for </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>The metric ID </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArraySizeBytes</em>&nbsp;</td><td>The size of <code>eventIdArray</code> in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArray</em>&nbsp;</td><td>The event IDs required to calculate <code>metric</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueArraySizeBytes</em>&nbsp;</td><td>The size of <code>eventValueArray</code> in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueArray</em>&nbsp;</td><td>The normalized event values required to calculate <code>metric</code>. The values must be order to match the order of events in <code>eventIdArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>timeDuration</em>&nbsp;</td><td>The duration over which the events were collected, in ns </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metricValue</em>&nbsp;</td><td>Returns the value for the metric</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>if the eventIdArray does not contain all the events needed for metric </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_VALUE</em>&nbsp;</td><td>if any of the event values required for the metric is CUPTI_EVENT_OVERFLOW </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_VALUE</em>&nbsp;</td><td>if the computed metric value cannot be represented in the metric's value type. For example, if the metric value type is unsigned and the computed metric value is negative </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>metricValue</code>, <code>eventIdArray</code> or <code>eventValueArray</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc3ec4701e9878845536282a14ed27245"></a><!-- doxytag: member="cupti_metrics.h::cuptiMetricGetValue2" ref="gc3ec4701e9878845536282a14ed27245" args="(CUpti_MetricID metric, size_t eventIdArraySizeBytes, CUpti_EventID *eventIdArray, size_t eventValueArraySizeBytes, uint64_t *eventValueArray, size_t propIdArraySizeBytes, CUpti_MetricPropertyID *propIdArray, size_t propValueArraySizeBytes, uint64_t *propValueArray, CUpti_MetricValue *metricValue)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiMetricGetValue2           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td>
          <td class="paramname"> <em>metric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>eventIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *&nbsp;</td>
          <td class="paramname"> <em>eventIdArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>eventValueArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>eventValueArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>propIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__METRIC__API.html#g5bafb626ac31ac98400d9191316871f7">CUpti_MetricPropertyID</a> *&nbsp;</td>
          <td class="paramname"> <em>propIdArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>propValueArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>propValueArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a> *&nbsp;</td>
          <td class="paramname"> <em>metricValue</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Use the events and properties collected for a metric to calculate the metric value. Metric value evaluation depends on the evaluation mode <a class="el" href="group__CUPTI__METRIC__API.html#g59396bc237d98ee0595e5743bde89b9d">CUpti_MetricEvaluationMode</a> that the metric supports. If a metric has evaluation mode as CUPTI_METRIC_EVALUATION_MODE_PER_INSTANCE, then it assumes that the input event value is for one domain instance. If a metric has evaluation mode as CUPTI_METRIC_EVALUATION_MODE_AGGREGATE, it assumes that input event values are normalized to represent all domain instances on a device. For the most accurate metric collection, the events required for the metric should be collected for all profiled domain instances. For example, to collect all instances of an event, set the CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES attribute on the group containing the event to 1. The normalized value for the event is then: (<code>sum_event_values</code> * <code>totalInstanceCount</code>) / <code>instanceCount</code>, where <code>sum_event_values</code> is the summation of the event values across all profiled domain instances, <code>totalInstanceCount</code> is obtained from querying CUPTI_EVENT_DOMAIN_ATTR_TOTAL_INSTANCE_COUNT and <code>instanceCount</code> is obtained from querying CUPTI_EVENT_GROUP_ATTR_INSTANCE_COUNT (or CUPTI_EVENT_DOMAIN_ATTR_INSTANCE_COUNT).<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>metric</em>&nbsp;</td><td>The metric ID </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArraySizeBytes</em>&nbsp;</td><td>The size of <code>eventIdArray</code> in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArray</em>&nbsp;</td><td>The event IDs required to calculate <code>metric</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueArraySizeBytes</em>&nbsp;</td><td>The size of <code>eventValueArray</code> in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueArray</em>&nbsp;</td><td>The normalized event values required to calculate <code>metric</code>. The values must be order to match the order of events in <code>eventIdArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>propIdArraySizeBytes</em>&nbsp;</td><td>The size of <code>propIdArray</code> in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>propIdArray</em>&nbsp;</td><td>The metric property IDs required to calculate <code>metric</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>propValueArraySizeBytes</em>&nbsp;</td><td>The size of <code>propValueArray</code> in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>propValueArray</em>&nbsp;</td><td>The metric property values required to calculate <code>metric</code>. The values must be order to match the order of metric properties in <code>propIdArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>metricValue</em>&nbsp;</td><td>Returns the value for the metric</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_METRIC_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>if the eventIdArray does not contain all the events needed for metric </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_VALUE</em>&nbsp;</td><td>if any of the event values required for the metric is CUPTI_EVENT_OVERFLOW </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>if the computed metric value cannot be represented in the metric's value type. For example, if the metric value type is unsigned and the computed metric value is negative </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>metricValue</code>, <code>eventIdArray</code> or <code>eventValueArray</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
