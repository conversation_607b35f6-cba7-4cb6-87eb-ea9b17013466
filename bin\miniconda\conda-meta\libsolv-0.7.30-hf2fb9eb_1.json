{"arch": "x86_64", "build": "hf2fb9eb_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["pcre2 >=10.42,<10.43.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libsolv-0.7.30-hf2fb9eb_1", "files": ["Library/bin/conda2solv.exe", "Library/bin/dumpsolv.exe", "Library/bin/installcheck.exe", "Library/bin/mergesolv.exe", "Library/bin/solv.dll", "Library/bin/solvext.dll", "Library/bin/testsolv.exe", "Library/include/solv/bitmap.h", "Library/include/solv/chksum.h", "Library/include/solv/conda.h", "Library/include/solv/config.h", "Library/include/solv/dataiterator.h", "Library/include/solv/dirpool.h", "Library/include/solv/evr.h", "Library/include/solv/hash.h", "Library/include/solv/knownid.h", "Library/include/solv/policy.h", "Library/include/solv/pool.h", "Library/include/solv/poolarch.h", "Library/include/solv/poolid.h", "Library/include/solv/pooltypes.h", "Library/include/solv/poolvendor.h", "Library/include/solv/problems.h", "Library/include/solv/queue.h", "Library/include/solv/repo.h", "Library/include/solv/repo_conda.h", "Library/include/solv/repo_solv.h", "Library/include/solv/repo_write.h", "Library/include/solv/repodata.h", "Library/include/solv/rules.h", "Library/include/solv/selection.h", "Library/include/solv/solv_xfopen.h", "Library/include/solv/solvable.h", "Library/include/solv/solver.h", "Library/include/solv/solverdebug.h", "Library/include/solv/solvversion.h", "Library/include/solv/strpool.h", "Library/include/solv/testcase.h", "Library/include/solv/tools_util.h", "Library/include/solv/transaction.h", "Library/include/solv/util.h", "Library/lib/pkgconfig/libsolv.pc", "Library/lib/pkgconfig/libsolvext.pc", "Library/lib/solv.lib", "Library/lib/solvext.lib", "Library/share/cmake/Modules/FindLibSolv.cmake", "Library/share/man/man1/dumpsolv.1", "Library/share/man/man1/installcheck.1", "Library/share/man/man1/mergesolv.1", "Library/share/man/man1/repo2solv.1", "Library/share/man/man1/solv.1", "Library/share/man/man1/testsolv.1", "Library/share/man/man3/libsolv-bindings.3", "Library/share/man/man3/libsolv-constantids.3", "Library/share/man/man3/libsolv-history.3", "Library/share/man/man3/libsolv-pool.3", "Library/share/man/man3/libsolv.3"], "fn": "libsolv-0.7.30-hf2fb9eb_1.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libsolv-0.7.30-hf2fb9eb_1", "type": 1}, "md5": "54ba4c8b16cf67cedc98b6cb77ba4471", "name": "libsolv", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libsolv-0.7.30-hf2fb9eb_1.conda", "paths_data": {"paths": [{"_path": "Library/lib/pkgconfig/libsolv.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_03d0scfsmg/croot/libsolv-suite_1734374059512/_h_env", "sha256": "d5baede04c04e7af5cd268da438f6b6b9afec67bb124f214506f61cdf6b990ea", "sha256_in_prefix": "566cc67cbed7e4eda46b624779f68dfa203750c6b08ad66740b2806d16698a6d", "size_in_bytes": 298}, {"_path": "Library/lib/pkgconfig/libsolvext.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_03d0scfsmg/croot/libsolv-suite_1734374059512/_h_env", "sha256": "3b11937136ecb89094bb579228dd0cfb1ddb58442df0c8f580f57771f68ac986", "sha256_in_prefix": "cc215b360707bfa36eac22b2a88808e1b800e2257df5b2b9cc16b7ccf90714a0", "size_in_bytes": 327}], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::libsolv==0.7.30=hf2fb9eb_1[md5=54ba4c8b16cf67cedc98b6cb77ba4471]", "sha256": "7d3fe3659a6a096303ff6aa0310a0b0111562c79daa22a4c00f764de9bc97153", "size": 484422, "subdir": "win-64", "timestamp": 1734374201000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libsolv-0.7.30-hf2fb9eb_1.conda", "version": "0.7.30"}