<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingConfigurationInfoParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingConfigurationInfoParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingConfigurationInfoParams" -->PC sampling configuration structure.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#34358d33d46e5316fe2daa6c2af541b6">ctx</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#6b007258d8944934683cff267ca212df">numAttributes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#bfd81ba5c146175f45f7e8c14457fcd6">pPCSamplingConfigurationInfo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#738bd9fbcf869e4cdf96cd4516739bf8">pPriv</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#6a5ae5acab25aa0b994dcfb2c2804a60">size</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure configures PC sampling using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gf3e8842fda99b4c1659c2d2f1c18674e">cuptiPCSamplingSetConfigurationAttribute</a> and queries PC sampling default configuration using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gda8f3c53c8673e56a689a67cae7e8df2">cuptiPCSamplingGetConfigurationAttribute</a> <hr><h2>Field Documentation</h2>
<a class="anchor" name="34358d33d46e5316fe2daa6c2af541b6"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfoParams::ctx" ref="34358d33d46e5316fe2daa6c2af541b6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#34358d33d46e5316fe2daa6c2af541b6">CUpti_PCSamplingConfigurationInfoParams::ctx</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] CUcontext 
</div>
</div><p>
<a class="anchor" name="6b007258d8944934683cff267ca212df"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfoParams::numAttributes" ref="6b007258d8944934683cff267ca212df" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#6b007258d8944934683cff267ca212df">CUpti_PCSamplingConfigurationInfoParams::numAttributes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Number of attributes to configure using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gf3e8842fda99b4c1659c2d2f1c18674e">cuptiPCSamplingSetConfigurationAttribute</a> or query using <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gda8f3c53c8673e56a689a67cae7e8df2">cuptiPCSamplingGetConfigurationAttribute</a> 
</div>
</div><p>
<a class="anchor" name="bfd81ba5c146175f45f7e8c14457fcd6"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfoParams::pPCSamplingConfigurationInfo" ref="bfd81ba5c146175f45f7e8c14457fcd6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a>* <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#bfd81ba5c146175f45f7e8c14457fcd6">CUpti_PCSamplingConfigurationInfoParams::pPCSamplingConfigurationInfo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a> 
</div>
</div><p>
<a class="anchor" name="738bd9fbcf869e4cdf96cd4516739bf8"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfoParams::pPriv" ref="738bd9fbcf869e4cdf96cd4516739bf8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#738bd9fbcf869e4cdf96cd4516739bf8">CUpti_PCSamplingConfigurationInfoParams::pPriv</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Assign to NULL 
</div>
</div><p>
<a class="anchor" name="6a5ae5acab25aa0b994dcfb2c2804a60"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfoParams::size" ref="6a5ae5acab25aa0b994dcfb2c2804a60" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#6a5ae5acab25aa0b994dcfb2c2804a60">CUpti_PCSamplingConfigurationInfoParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure i.e. CUpti_PCSamplingConfigurationInfoParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
