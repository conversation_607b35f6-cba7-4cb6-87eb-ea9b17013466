{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-libraries-dev >=12.1.0", "cuda-nsight-compute >=12.1.0", "cuda-nvml-dev >=12.1.55", "cuda-nvvp >=12.1.55"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-visual-tools-12.1.0-0", "features": "", "files": [], "fn": "cuda-visual-tools-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-visual-tools-12.1.0-0", "type": 1}, "md5": "7cab9124fef984c538009a66735f0c31", "name": "cuda-visual-tools", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-visual-tools-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1438, "subdir": "win-64", "timestamp": 1677130049000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-visual-tools-12.1.0-0.tar.bz2", "version": "12.1.0"}