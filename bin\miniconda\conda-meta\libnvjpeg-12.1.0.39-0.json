{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjpeg-12.1.0.39-0", "features": "", "files": ["lib/x64/nvjpeg.lib"], "fn": "libnvjpeg-12.1.0.39-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjpeg-12.1.0.39-0", "type": 1}, "md5": "b1beeb120e37653158541edece334dff", "name": "libnvjpeg", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjpeg-12.1.0.39-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/nvjpeg.lib", "path_type": "hardlink", "sha256": "d13d37c4df24e3dd9dbfdec616b35ecf80b966367c55ace2768132b08386e273", "sha256_in_prefix": "d13d37c4df24e3dd9dbfdec616b35ecf80b966367c55ace2768132b08386e273", "size_in_bytes": 21230}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 4758, "subdir": "win-64", "timestamp": 1674623601000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libnvjpeg-12.1.0.39-0.tar.bz2", "version": "12.1.0.39"}