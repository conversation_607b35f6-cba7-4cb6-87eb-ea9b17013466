<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-us" xml:lang="en-us">
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8"></meta>
      <meta http-equiv="X-UA-Compatible" content="IE=edge"></meta>
      <meta name="copyright" content="(C) Copyright 2005"></meta>
      <meta name="DC.rights.owner" content="(C) Copyright 2005"></meta>
      <meta name="DC.Type" content="concept"></meta>
      <meta name="DC.Title" content="Sanitizer Api"></meta>
      <meta name="abstract" content="The reference guide for the NVIDIA Compute Sanitizer Api."></meta>
      <meta name="description" content=""></meta>
      <meta name="DC.Coverage" content="Developer Interfaces"></meta>
      <meta name="DC.subject" content="Sanitizer Api"></meta>
      <meta name="keywords" content="Sanitizer Api"></meta>
      <meta name="DC.Format" content="XHTML"></meta>
      <meta name="DC.Identifier" content="abstract"></meta>
      <link rel="stylesheet" type="text/css" href="../common/formatting/commonltr.css"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/site.css"></link>
      <title>Sanitizer Api :: Compute Sanitizer Documentation</title>
      <!--[if lt IE 9]>
      <script src="../common/formatting/html5shiv-printshiv.min.js"></script>
      <![endif]--><script type="text/javascript" charset="utf-8" src="../common/scripts/tynt/tynt.js" xml:space="preserve"></script>
      --&gt;<script src="https://assets.adobedtm.com/5d4962a43b79/c1061d2c5e7b/launch-191c2462b890.min.js" xml:space="preserve"></script><script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.min.js" xml:space="preserve"></script><script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.ba-hashchange.min.js" xml:space="preserve"></script><script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.scrollintoview.min.js" xml:space="preserve"></script><script type="text/javascript" src="../search/htmlFileList.js" xml:space="preserve"></script><script type="text/javascript" src="../search/htmlFileInfoList.js" xml:space="preserve"></script><script type="text/javascript" src="../search/nwSearchFnt.min.js" xml:space="preserve"></script><script type="text/javascript" src="../search/stemmers/en_stemmer.min.js" xml:space="preserve"></script><script type="text/javascript" src="../search/index-1.js" xml:space="preserve"></script><script type="text/javascript" src="../search/index-2.js" xml:space="preserve"></script><script type="text/javascript" src="../search/index-3.js" xml:space="preserve"></script><link rel="canonical" href="https://docs.nvidia.com/compute-sanitizer/SanitizerApi/index.html"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/qwcode.highlight.css"></link>
   </head>
   <body>
      <header id="header"><span id="company">NVIDIA</span><span id="site-title">Compute Sanitizer Documentation</span><form id="search" method="get" action="search" enctype="application/x-www-form-urlencoded"><input type="text" name="search-text"></input><fieldset id="search-location">
               <legend>Search In:</legend><label><input type="radio" name="search-type" value="site"></input>Entire Site</label><label><input type="radio" name="search-type" value="document"></input>Just This Document</label></fieldset><button type="reset">clear search</button><button id="submit" type="submit">search</button></form>
      </header>
      <div id="site-content">
         <nav id="site-nav">
            <div class="category closed"><a href="../index.html" title="The root of the site." shape="rect">Compute Sanitizer
                  v2023.1.0</a></div>
            <div class="category"><a href="index.html" title="Sanitizer Api" shape="rect">Sanitizer Api</a></div>
            <ul>
               <li>
                  <div class="section-link"><a href="modules.html#modules" shape="rect">1.&nbsp;Modules</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="modules.html#group__SANITIZER__BARRIER__API" shape="rect">1.1.&nbsp;Sanitizer Barrier API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__SANITIZER__CALLBACK__API" shape="rect">1.2.&nbsp;Sanitizer Callback API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__SANITIZER__MEMORY__API" shape="rect">1.3.&nbsp;Sanitizer Memory API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__SANITIZER__PATCHING__API" shape="rect">1.4.&nbsp;Sanitizer Patching API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__SANITIZER__RESULT__API" shape="rect">1.5.&nbsp;Sanitizer Result Codes</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__SANITIZER__STREAM__API" shape="rect">1.6.&nbsp;Sanitizer Stream API</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="annotated.html#annotated" shape="rect">2.&nbsp;Data Structures</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__BatchMemopData" shape="rect">2.1.&nbsp;Sanitizer_BatchMemopData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__CallbackData" shape="rect">2.2.&nbsp;Sanitizer_CallbackData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__EventData" shape="rect">2.3.&nbsp;Sanitizer_EventData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__GraphExecData" shape="rect">2.4.&nbsp;Sanitizer_GraphExecData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__GraphLaunchData" shape="rect">2.5.&nbsp;Sanitizer_GraphLaunchData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__GraphNodeLaunchData" shape="rect">2.6.&nbsp;Sanitizer_GraphNodeLaunchData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__LaunchData" shape="rect">2.7.&nbsp;Sanitizer_LaunchData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__MemcpyData" shape="rect">2.8.&nbsp;Sanitizer_MemcpyData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__MemsetData" shape="rect">2.9.&nbsp;Sanitizer_MemsetData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__ResourceArrayData" shape="rect">2.10.&nbsp;Sanitizer_ResourceArrayData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__ResourceContextData" shape="rect">2.11.&nbsp;Sanitizer_ResourceContextData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__ResourceFunctionsLazyLoadedData" shape="rect">2.12.&nbsp;Sanitizer_ResourceFunctionsLazyLoadedData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__ResourceMemoryData" shape="rect">2.13.&nbsp;Sanitizer_ResourceMemoryData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__ResourceMempoolData" shape="rect">2.14.&nbsp;Sanitizer_ResourceMempoolData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__ResourceModuleData" shape="rect">2.15.&nbsp;Sanitizer_ResourceModuleData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__ResourceStreamData" shape="rect">2.16.&nbsp;Sanitizer_ResourceStreamData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__SynchronizeData" shape="rect">2.17.&nbsp;Sanitizer_SynchronizeData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structSanitizer__UvmData" shape="rect">2.18.&nbsp;Sanitizer_UvmData</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="functions.html#functions" shape="rect">3.&nbsp;Data Fields</a></div>
               </li>
               <li>
                  <div class="section-link"><a href="notices-header.html#notices-header" shape="rect">Notices</a></div>
                  <ul></ul>
               </li>
            </ul>
         </nav>
         <div id="resize-nav"></div>
         <nav id="search-results">
            <h2>Search Results</h2>
            <ol></ol>
         </nav>
         <div id="contents-container">
            <div id="breadcrumbs-container">
               <div id="breadcrumbs">&lt; Previous | <a href="modules.html" shape="rect">Next &gt;</a></div>
               <div id="release-info">Sanitizer Api
                  -
                  
                  v2023.1.0
                  (<a href="https://developer.nvidia.com/cuda-toolkit-archive" shape="rect">older</a>)
                  -
                  Last updated February 23, 2023
                  -
                  <a href="mailto:<EMAIL>?subject=Compute Sanitizer Documentation Feedback: Sanitizer Api" shape="rect">Send Feedback</a></div>
            </div>
            <article id="contents">
               <div class="topic nested1" id="abstract"><a name="abstract" shape="rect">
                     <!-- --></a><h2 class="topictitle2">Sanitizer Api</h2>
                  <div class="body conbody">
                     <p class="shortdesc">The reference guide for the NVIDIA Compute Sanitizer Api.</p>
                     <p class="p">The NVIDIA Compute Sanitizer Api.</p>
                  </div>
               </div>
               <div class="topic nested0" id="toc">
                  <h1 class="title topictitle1">Table of Contents</h1>
                  <div class="body">
                     <ul>
                        <li><a href="modules.html#modules" shape="rect">1.&nbsp;Modules</a><ul>
                              <li><a href="modules.html#group__SANITIZER__BARRIER__API" shape="rect">1.1.&nbsp;Sanitizer Barrier API</a></li>
                              <li><a href="modules.html#group__SANITIZER__CALLBACK__API" shape="rect">1.2.&nbsp;Sanitizer Callback API</a></li>
                              <li><a href="modules.html#group__SANITIZER__MEMORY__API" shape="rect">1.3.&nbsp;Sanitizer Memory API</a></li>
                              <li><a href="modules.html#group__SANITIZER__PATCHING__API" shape="rect">1.4.&nbsp;Sanitizer Patching API</a></li>
                              <li><a href="modules.html#group__SANITIZER__RESULT__API" shape="rect">1.5.&nbsp;Sanitizer Result Codes</a></li>
                              <li><a href="modules.html#group__SANITIZER__STREAM__API" shape="rect">1.6.&nbsp;Sanitizer Stream API</a></li>
                           </ul>
                        </li>
                        <li><a href="annotated.html#annotated" shape="rect">2.&nbsp;Data Structures</a><ul>
                              <li><a href="annotated.html#structSanitizer__BatchMemopData" shape="rect">2.1.&nbsp;Sanitizer_BatchMemopData</a></li>
                              <li><a href="annotated.html#structSanitizer__CallbackData" shape="rect">2.2.&nbsp;Sanitizer_CallbackData</a></li>
                              <li><a href="annotated.html#structSanitizer__EventData" shape="rect">2.3.&nbsp;Sanitizer_EventData</a></li>
                              <li><a href="annotated.html#structSanitizer__GraphExecData" shape="rect">2.4.&nbsp;Sanitizer_GraphExecData</a></li>
                              <li><a href="annotated.html#structSanitizer__GraphLaunchData" shape="rect">2.5.&nbsp;Sanitizer_GraphLaunchData</a></li>
                              <li><a href="annotated.html#structSanitizer__GraphNodeLaunchData" shape="rect">2.6.&nbsp;Sanitizer_GraphNodeLaunchData</a></li>
                              <li><a href="annotated.html#structSanitizer__LaunchData" shape="rect">2.7.&nbsp;Sanitizer_LaunchData</a></li>
                              <li><a href="annotated.html#structSanitizer__MemcpyData" shape="rect">2.8.&nbsp;Sanitizer_MemcpyData</a></li>
                              <li><a href="annotated.html#structSanitizer__MemsetData" shape="rect">2.9.&nbsp;Sanitizer_MemsetData</a></li>
                              <li><a href="annotated.html#structSanitizer__ResourceArrayData" shape="rect">2.10.&nbsp;Sanitizer_ResourceArrayData</a></li>
                              <li><a href="annotated.html#structSanitizer__ResourceContextData" shape="rect">2.11.&nbsp;Sanitizer_ResourceContextData</a></li>
                              <li><a href="annotated.html#structSanitizer__ResourceFunctionsLazyLoadedData" shape="rect">2.12.&nbsp;Sanitizer_ResourceFunctionsLazyLoadedData</a></li>
                              <li><a href="annotated.html#structSanitizer__ResourceMemoryData" shape="rect">2.13.&nbsp;Sanitizer_ResourceMemoryData</a></li>
                              <li><a href="annotated.html#structSanitizer__ResourceMempoolData" shape="rect">2.14.&nbsp;Sanitizer_ResourceMempoolData</a></li>
                              <li><a href="annotated.html#structSanitizer__ResourceModuleData" shape="rect">2.15.&nbsp;Sanitizer_ResourceModuleData</a></li>
                              <li><a href="annotated.html#structSanitizer__ResourceStreamData" shape="rect">2.16.&nbsp;Sanitizer_ResourceStreamData</a></li>
                              <li><a href="annotated.html#structSanitizer__SynchronizeData" shape="rect">2.17.&nbsp;Sanitizer_SynchronizeData</a></li>
                              <li><a href="annotated.html#structSanitizer__UvmData" shape="rect">2.18.&nbsp;Sanitizer_UvmData</a></li>
                           </ul>
                        </li>
                        <li><a href="functions.html#functions" shape="rect">3.&nbsp;Data Fields</a></li>
                        <li><a href="notices-header.html#notices-header" shape="rect">Notices</a><ul></ul>
                        </li>
                     </ul>
                  </div>
               </div>
               <hr id="contents-end"></hr>
            </article>
         </div>
      </div><script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/formatting/common.min.js" xml:space="preserve"></script><script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-write.js" xml:space="preserve"></script><script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-tracker.js" xml:space="preserve"></script><script type="text/javascript" xml:space="preserve">_satellite.pageBottom();</script><script type="text/javascript" xml:space="preserve">var switchTo5x=true;</script><script type="text/javascript" xml:space="preserve">stLight.options({publisher: "998dc202-a267-4d8e-bce9-14debadb8d92", doNotHash: false, doNotCopy: false, hashAddressBar: false});</script></body>
</html>