<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityOverhead Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityOverhead Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityOverhead" -->The activity record for CUPTI and driver overheads.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOverhead.html#220db95d05426890eea2c9d54dcddcf5">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOverhead.html#a219eae296f412503d092e750cab2484">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="unionCUpti__ActivityObjectKindId.html">CUpti_ActivityObjectKindId</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOverhead.html#9295888f85da6d51ab2f0b8e0a0a23bc">objectId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOverhead.html#412ff0b2e814b0b86c9b0a43c9239380">objectKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcc8863d79c939f6bb57324db4503b265">CUpti_ActivityOverheadKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOverhead.html#4c7e3fda3ba2e374f3b62f1ab15b272f">overheadKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOverhead.html#efa4d16d033eddc0e586981fcf13198b">start</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record provides CUPTI and driver overhead information (CUPTI_ACTIVITY_OVERHEAD). <hr><h2>Field Documentation</h2>
<a class="anchor" name="220db95d05426890eea2c9d54dcddcf5"></a><!-- doxytag: member="CUpti_ActivityOverhead::end" ref="220db95d05426890eea2c9d54dcddcf5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOverhead.html#220db95d05426890eea2c9d54dcddcf5">CUpti_ActivityOverhead::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the overhead, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the overhead. 
</div>
</div><p>
<a class="anchor" name="a219eae296f412503d092e750cab2484"></a><!-- doxytag: member="CUpti_ActivityOverhead::kind" ref="a219eae296f412503d092e750cab2484" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityOverhead.html#a219eae296f412503d092e750cab2484">CUpti_ActivityOverhead::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_OVERHEAD. 
</div>
</div><p>
<a class="anchor" name="9295888f85da6d51ab2f0b8e0a0a23bc"></a><!-- doxytag: member="CUpti_ActivityOverhead::objectId" ref="9295888f85da6d51ab2f0b8e0a0a23bc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionCUpti__ActivityObjectKindId.html">CUpti_ActivityObjectKindId</a> <a class="el" href="structCUpti__ActivityOverhead.html#9295888f85da6d51ab2f0b8e0a0a23bc">CUpti_ActivityOverhead::objectId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The identifier for the activity object. 'objectKind' indicates which ID is valid for this record. 
</div>
</div><p>
<a class="anchor" name="412ff0b2e814b0b86c9b0a43c9239380"></a><!-- doxytag: member="CUpti_ActivityOverhead::objectKind" ref="412ff0b2e814b0b86c9b0a43c9239380" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a> <a class="el" href="structCUpti__ActivityOverhead.html#412ff0b2e814b0b86c9b0a43c9239380">CUpti_ActivityOverhead::objectKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of activity object that the overhead is associated with. 
</div>
</div><p>
<a class="anchor" name="4c7e3fda3ba2e374f3b62f1ab15b272f"></a><!-- doxytag: member="CUpti_ActivityOverhead::overheadKind" ref="4c7e3fda3ba2e374f3b62f1ab15b272f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcc8863d79c939f6bb57324db4503b265">CUpti_ActivityOverheadKind</a> <a class="el" href="structCUpti__ActivityOverhead.html#4c7e3fda3ba2e374f3b62f1ab15b272f">CUpti_ActivityOverhead::overheadKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of overhead, CUPTI, DRIVER, COMPILER etc. 
</div>
</div><p>
<a class="anchor" name="efa4d16d033eddc0e586981fcf13198b"></a><!-- doxytag: member="CUpti_ActivityOverhead::start" ref="efa4d16d033eddc0e586981fcf13198b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOverhead.html#efa4d16d033eddc0e586981fcf13198b">CUpti_ActivityOverhead::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the overhead, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the overhead. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
