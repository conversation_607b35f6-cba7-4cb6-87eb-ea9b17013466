//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_CASSERT
#define _CUDA_CASSERT

#ifndef __CUDACC_RTC__

#include <cassert>
#include <assert.h>

#include "detail/__config"

#include "detail/__pragma_push"

#include "detail/libcxx/include/cassert"

#include "detail/__pragma_pop"

#endif //__CUDACC_RTC__

#endif //_CUDA_CASSERT
