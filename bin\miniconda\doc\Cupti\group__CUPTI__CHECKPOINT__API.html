<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Checkpoint API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Checkpoint API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Configuration and handle for a CUPTI Checkpoint.  <a href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CHECKPOINT__API.html#g4d49e28d43be23e549b827292271b56d">NV::Cupti::Checkpoint::CUpti_CheckpointOptimizations</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CHECKPOINT__API.html#gg4d49e28d43be23e549b827292271b56d34ddc9bb42fe2f6d94bc8b8d8b418b81">NV::Cupti::Checkpoint::CUPTI_CHECKPOINT_OPT_NONE</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CHECKPOINT__API.html#gg4d49e28d43be23e549b827292271b56d2de3733a0e7e789f28b7ca280f13484e">NV::Cupti::Checkpoint::CUPTI_CHECKPOINT_OPT_TRANSFER</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Specifies optimization options for a checkpoint, may be OR'd together to specify multiple options.  <a href="group__CUPTI__CHECKPOINT__API.html#g4d49e28d43be23e549b827292271b56d">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CHECKPOINT__API.html#g8aaeedd9f88919cdc35baa0a10492c09">NV::Cupti::Checkpoint::cuptiCheckpointFree</a> (CUpti_Checkpoint *const handle)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Free the backing data for a checkpoint.  <a href="#g8aaeedd9f88919cdc35baa0a10492c09"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CHECKPOINT__API.html#g21cdc23dcb04894c0a59cb1fd4da430f">NV::Cupti::Checkpoint::cuptiCheckpointRestore</a> (CUpti_Checkpoint *const handle)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Restore a checkpoint to the device associated with its context.  <a href="#g21cdc23dcb04894c0a59cb1fd4da430f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CHECKPOINT__API.html#g90ea5f5068de6135df6b409fa09f947b">NV::Cupti::Checkpoint::cuptiCheckpointSave</a> (CUpti_Checkpoint *const handle)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize and save a checkpoint of the device state associated with the handle context.  <a href="#g90ea5f5068de6135df6b409fa09f947b"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI Checkpoint API. <hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g4d49e28d43be23e549b827292271b56d"></a><!-- doxytag: member="NV::Cupti::Checkpoint::CUpti_CheckpointOptimizations" ref="g4d49e28d43be23e549b827292271b56d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__CHECKPOINT__API.html#g4d49e28d43be23e549b827292271b56d">NV::Cupti::Checkpoint::CUpti_CheckpointOptimizations</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg4d49e28d43be23e549b827292271b56d34ddc9bb42fe2f6d94bc8b8d8b418b81"></a><!-- doxytag: member="CUPTI_CHECKPOINT_OPT_NONE" ref="gg4d49e28d43be23e549b827292271b56d34ddc9bb42fe2f6d94bc8b8d8b418b81" args="" -->CUPTI_CHECKPOINT_OPT_NONE</em>&nbsp;</td><td>
Default behavior. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg4d49e28d43be23e549b827292271b56d2de3733a0e7e789f28b7ca280f13484e"></a><!-- doxytag: member="CUPTI_CHECKPOINT_OPT_TRANSFER" ref="gg4d49e28d43be23e549b827292271b56d2de3733a0e7e789f28b7ca280f13484e" args="" -->CUPTI_CHECKPOINT_OPT_TRANSFER</em>&nbsp;</td><td>
Determine which mem blocks have changed, and only restore those. This optimization is cached, which means cuptiCheckpointRestore must always be called at the same point in the application when this option is enabled, or the result may be incorrect. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g8aaeedd9f88919cdc35baa0a10492c09"></a><!-- doxytag: member="NV::Cupti::Checkpoint::cuptiCheckpointFree" ref="g8aaeedd9f88919cdc35baa0a10492c09" args="(CUpti_Checkpoint *const handle)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> NV::Cupti::Checkpoint::cuptiCheckpointFree           </td>
          <td>(</td>
          <td class="paramtype">CUpti_Checkpoint *const &nbsp;</td>
          <td class="paramname"> <em>handle</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Frees all associated device, host memory and filesystem storage used for this context. After freeing a handle, it may be re-used as if it was new - options may be re-configured and will take effect on the next call to <code>cuptiCheckpointSave</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>handle</em>&nbsp;</td><td>A pointer to a previously saved <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html" title="Configuration and handle for a CUPTI Checkpoint.">CUpti_Checkpoint</a> object</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>if the handle was successfully freed </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if the handle was already freed or appears invalid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td>if the context is no longer valid </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g21cdc23dcb04894c0a59cb1fd4da430f"></a><!-- doxytag: member="NV::Cupti::Checkpoint::cuptiCheckpointRestore" ref="g21cdc23dcb04894c0a59cb1fd4da430f" args="(CUpti_Checkpoint *const handle)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> NV::Cupti::Checkpoint::cuptiCheckpointRestore           </td>
          <td>(</td>
          <td class="paramtype">CUpti_Checkpoint *const &nbsp;</td>
          <td class="paramname"> <em>handle</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Restores device, pinned, and allocated memory to the state when the checkpoint was saved<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>handle</em>&nbsp;</td><td>A pointer to a previously saved <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html" title="Configuration and handle for a CUPTI Checkpoint.">CUpti_Checkpoint</a> object</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUTPI_SUCCESS</em>&nbsp;</td><td>if the checkpoint was successfully restored </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if the checkpoint was not previously initialized </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if the handle appears invalid </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_UNKNOWN</em>&nbsp;</td><td>if the restore or optimization operation fails </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g90ea5f5068de6135df6b409fa09f947b"></a><!-- doxytag: member="NV::Cupti::Checkpoint::cuptiCheckpointSave" ref="g90ea5f5068de6135df6b409fa09f947b" args="(CUpti_Checkpoint *const handle)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> NV::Cupti::Checkpoint::cuptiCheckpointSave           </td>
          <td>(</td>
          <td class="paramtype">CUpti_Checkpoint *const &nbsp;</td>
          <td class="paramname"> <em>handle</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Uses the handle options to configure and save a checkpoint of the device state associated with the specified context.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>handle</em>&nbsp;</td><td>A pointer to a <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html" title="Configuration and handle for a CUPTI Checkpoint.">CUpti_Checkpoint</a> object</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>if a checkpoint was successfully initialized and saved </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>handle</code> does not appear to refer to a valid <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html" title="Configuration and handle for a CUPTI Checkpoint.">CUpti_Checkpoint</a> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td>if device associated with context is not compatible with checkpoint API </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if Save is requested over an existing checkpoint, but <code>allowOverwrite</code> was not originally specified </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td>if as configured, not enough backing storage space to save the checkpoint </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
