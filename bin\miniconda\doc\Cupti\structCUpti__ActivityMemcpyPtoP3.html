<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpyPtoP3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpyPtoP3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpyPtoP3" -->The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#36d54a7ee2da83a790d3a4e5ce84af4c">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#45aacf73cd2394b8e22d8c1e44d315bb">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ff70e9d897d3435ba1ffb988d36a1729">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ca9598dd7d04e303095949f66b595b2b">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#09565a3ad22ade4979385e55921a71ce">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#e45e157ae1bd5099584c919de75b87a4">dstContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#b88a42f5ff80d09210514b364c07ef2c">dstDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#067a3eebe9d3840f9eb2fd2a61b01104">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#5f358be82cf08920253169515a3e78bd">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#7d7feac72f3c890e69654c08e1d1ca85">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#d1e918ff77c90dbee0bcde392b48abbb">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#9ed3fd2b7d3348407cc7c3f769213f76">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#2b847b7fe6358950207fc1f9ed55bc4a">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#52190fa815bdc469582feb58d5f2fa01">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#1bd1c9c2c8f217040878dbd0dcbd39fa">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ceb10f6ae885b88390ee4af67230d26f">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#27e1c54c04f8159ad9869914eef33eca">srcContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#dd8ab5abf544830fa2cc883637d13d33">srcDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#df9d55bd09afe7491a05d8dec94618d0">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#405e29ca7cd6ece7e30f29d8d10d3d13">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ffa5a1fe02293174d6e04b165c7b0d60">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a peer-to-peer memory copy (CUPTI_ACTIVITY_KIND_MEMCPY2). <hr><h2>Field Documentation</h2>
<a class="anchor" name="36d54a7ee2da83a790d3a4e5ce84af4c"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::bytes" ref="36d54a7ee2da83a790d3a4e5ce84af4c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#36d54a7ee2da83a790d3a4e5ce84af4c">CUpti_ActivityMemcpyPtoP3::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="45aacf73cd2394b8e22d8c1e44d315bb"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::contextId" ref="45aacf73cd2394b8e22d8c1e44d315bb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#45aacf73cd2394b8e22d8c1e44d315bb">CUpti_ActivityMemcpyPtoP3::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="ff70e9d897d3435ba1ffb988d36a1729"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::copyKind" ref="ff70e9d897d3435ba1ffb988d36a1729" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ff70e9d897d3435ba1ffb988d36a1729">CUpti_ActivityMemcpyPtoP3::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="ca9598dd7d04e303095949f66b595b2b"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::correlationId" ref="ca9598dd7d04e303095949f66b595b2b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ca9598dd7d04e303095949f66b595b2b">CUpti_ActivityMemcpyPtoP3::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="09565a3ad22ade4979385e55921a71ce"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::deviceId" ref="09565a3ad22ade4979385e55921a71ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#09565a3ad22ade4979385e55921a71ce">CUpti_ActivityMemcpyPtoP3::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="e45e157ae1bd5099584c919de75b87a4"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::dstContextId" ref="e45e157ae1bd5099584c919de75b87a4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#e45e157ae1bd5099584c919de75b87a4">CUpti_ActivityMemcpyPtoP3::dstContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied to. 
</div>
</div><p>
<a class="anchor" name="b88a42f5ff80d09210514b364c07ef2c"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::dstDeviceId" ref="b88a42f5ff80d09210514b364c07ef2c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#b88a42f5ff80d09210514b364c07ef2c">CUpti_ActivityMemcpyPtoP3::dstDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied to. 
</div>
</div><p>
<a class="anchor" name="067a3eebe9d3840f9eb2fd2a61b01104"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::dstKind" ref="067a3eebe9d3840f9eb2fd2a61b01104" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#067a3eebe9d3840f9eb2fd2a61b01104">CUpti_ActivityMemcpyPtoP3::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="5f358be82cf08920253169515a3e78bd"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::end" ref="5f358be82cf08920253169515a3e78bd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#5f358be82cf08920253169515a3e78bd">CUpti_ActivityMemcpyPtoP3::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="7d7feac72f3c890e69654c08e1d1ca85"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::flags" ref="7d7feac72f3c890e69654c08e1d1ca85" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#7d7feac72f3c890e69654c08e1d1ca85">CUpti_ActivityMemcpyPtoP3::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="d1e918ff77c90dbee0bcde392b48abbb"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::graphId" ref="d1e918ff77c90dbee0bcde392b48abbb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#d1e918ff77c90dbee0bcde392b48abbb">CUpti_ActivityMemcpyPtoP3::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that executed this memcpy through graph launch. This field will be 0 if the memcpy is not done through graph launch. 
</div>
</div><p>
<a class="anchor" name="9ed3fd2b7d3348407cc7c3f769213f76"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::graphNodeId" ref="9ed3fd2b7d3348407cc7c3f769213f76" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#9ed3fd2b7d3348407cc7c3f769213f76">CUpti_ActivityMemcpyPtoP3::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed the memcpy through graph launch. This field will be 0 if memcpy is not done using graph launch. 
</div>
</div><p>
<a class="anchor" name="2b847b7fe6358950207fc1f9ed55bc4a"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::kind" ref="2b847b7fe6358950207fc1f9ed55bc4a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#2b847b7fe6358950207fc1f9ed55bc4a">CUpti_ActivityMemcpyPtoP3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY2. 
</div>
</div><p>
<a class="anchor" name="52190fa815bdc469582feb58d5f2fa01"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::pad" ref="52190fa815bdc469582feb58d5f2fa01" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#52190fa815bdc469582feb58d5f2fa01">CUpti_ActivityMemcpyPtoP3::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="1bd1c9c2c8f217040878dbd0dcbd39fa"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::padding" ref="1bd1c9c2c8f217040878dbd0dcbd39fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#1bd1c9c2c8f217040878dbd0dcbd39fa">CUpti_ActivityMemcpyPtoP3::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="ceb10f6ae885b88390ee4af67230d26f"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::reserved0" ref="ceb10f6ae885b88390ee4af67230d26f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ceb10f6ae885b88390ee4af67230d26f">CUpti_ActivityMemcpyPtoP3::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="27e1c54c04f8159ad9869914eef33eca"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::srcContextId" ref="27e1c54c04f8159ad9869914eef33eca" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#27e1c54c04f8159ad9869914eef33eca">CUpti_ActivityMemcpyPtoP3::srcContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied from. 
</div>
</div><p>
<a class="anchor" name="dd8ab5abf544830fa2cc883637d13d33"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::srcDeviceId" ref="dd8ab5abf544830fa2cc883637d13d33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#dd8ab5abf544830fa2cc883637d13d33">CUpti_ActivityMemcpyPtoP3::srcDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied from. 
</div>
</div><p>
<a class="anchor" name="df9d55bd09afe7491a05d8dec94618d0"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::srcKind" ref="df9d55bd09afe7491a05d8dec94618d0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#df9d55bd09afe7491a05d8dec94618d0">CUpti_ActivityMemcpyPtoP3::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="405e29ca7cd6ece7e30f29d8d10d3d13"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::start" ref="405e29ca7cd6ece7e30f29d8d10d3d13" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#405e29ca7cd6ece7e30f29d8d10d3d13">CUpti_ActivityMemcpyPtoP3::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="ffa5a1fe02293174d6e04b165c7b0d60"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP3::streamId" ref="ffa5a1fe02293174d6e04b165c7b0d60" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ffa5a1fe02293174d6e04b165c7b0d60">CUpti_ActivityMemcpyPtoP3::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
