<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li class="current"><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_c">- c -</a></h3><ul>
<li>cacheConfig
: <a class="el" href="structCUpti__ActivityKernel4.html#cfda7534ae14275cdfc5d0d22e15ac55">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#fc634185bab3ea0079207778fd8878c5">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#3a7f2c68e1e2e60f2b4d048779a96768">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#6394ab3ec7641b21d715fe4ed12cc04e">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#8df69227d19afd04d9f87ca8c394ba0b">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#484e2e6fee4b52ac80c8f82600b14c05">CUpti_ActivityKernel9</a>
<li>cacheConfigExecuted
: <a class="el" href="structCUpti__ActivityKernel.html#48c622133fb1572c4f5772c613d12cdc">CUpti_ActivityKernel</a>
<li>cacheConfigRequested
: <a class="el" href="structCUpti__ActivityKernel.html#613844ef0471a6e4bf8ff990840d2eb4">CUpti_ActivityKernel</a>
<li>cachePath
: <a class="el" href="structCUpti__ActivityJit.html#5a79bcb1b9718b714be007e9f5b15a9e">CUpti_ActivityJit</a>
<li>cacheSize
: <a class="el" href="structCUpti__ActivityJit.html#0b97b0e162c2e018235a8f48fcd47b6d">CUpti_ActivityJit</a>
<li>callbackSite
: <a class="el" href="structCUpti__CallbackData.html#01337ce329bea0e08d803cb99c1f1f01">CUpti_CallbackData</a>
<li>category
: <a class="el" href="structCUpti__ActivityMarkerData.html#a6e61f69a30709152205f71c5b6317f4">CUpti_ActivityMarkerData</a>
<li>cbid
: <a class="el" href="structCUpti__ActivityAPI.html#c353a4397e623f75a96124d937be29cd">CUpti_ActivityAPI</a>
<li>channelID
: <a class="el" href="structCUpti__ActivityMemset4.html#74834306366b5e925f5e9d0cf0ec28a1">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#6ecb807c8e48852cec46c08f845409f9">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#ee43d05e2fba6a8b7f3be1dc35179392">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#2701b9391c7df708cab1ace32f2f82c3">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#f0589495f2a300dc86df57dafa718fe4">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#109fb2f6d8aa27363c3ab130f8e76c91">CUpti_ActivityMemcpyPtoP4</a>
<li>channelType
: <a class="el" href="structCUpti__ActivityMemcpy5.html#4b57159d552af325a7d1e7ddd0faf2dc">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#f18141681e85457682ff9eaa615ec6ad">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#c7686745e86c9ae6bbf9e94830e24cd1">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#1b0dab60670b8fc8fd06c17a857bd383">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#239b1c98181f664f25d5c5636be3f84d">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#b2e006dae1847d08fe6383522965de17">CUpti_ActivityKernel9</a>
<li>clocksThrottleReasons
: <a class="el" href="structCUpti__ActivityEnvironment.html#40fcdb37ecf84c43601a6830c3104822">CUpti_ActivityEnvironment</a>
<li>clusterSchedulingPolicy
: <a class="el" href="structCUpti__ActivityKernel8.html#1edd454aa50166f8d4959e7b3ff2d7b0">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#1627f9801e194c2953bb8cef9e0d2413">CUpti_ActivityKernel9</a>
<li>clusterX
: <a class="el" href="structCUpti__ActivityKernel8.html#79cee473cbbf21a019ad3d92c195740a">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#c6cd17b05835250e63194e34cbc01ec0">CUpti_ActivityKernel9</a>
<li>clusterY
: <a class="el" href="structCUpti__ActivityKernel9.html#f778d76a60d7644eacbffc751675e4fd">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#acfe25401868454b7e48555e41cf2dac">CUpti_ActivityKernel8</a>
<li>clusterZ
: <a class="el" href="structCUpti__ActivityKernel8.html#52cc4fa87852c16ed1400c5263936550">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#229d7bbb49b5969b41298b0729ac1cc4">CUpti_ActivityKernel9</a>
<li>cmp
: <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#9f89cbf5bfd929a933356d13a13de501">CUpti_Profiler_DeviceSupported_Params</a>
<li>collectionModeData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#55b8045c38d989095c50d2ac7c5f3fd2">CUpti_PCSamplingConfigurationInfo</a>
<li>collectNumPcs
: <a class="el" href="structCUpti__PCSamplingData.html#4d2032f7507ca7729183a8972ae6e74e">CUpti_PCSamplingData</a>
<li>color
: <a class="el" href="structCUpti__ActivityMarkerData.html#a5f7f71a30990458126140a45d0dc002">CUpti_ActivityMarkerData</a>
<li>completed
: <a class="el" href="structCUpti__ActivityKernel2.html#92e74324daa8665d573e5014f72ccb8c">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#71db41749d522c1c3784ec96767301e0">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#a38a2f171e8ada8373e5f785ecb62e87">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#8756eeb571e45af21137c300098e96c6">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#10217cdc4f154b8ec276101a1975644f">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#67233708cd369fcf514868ccd8d2446a">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#f4a5b9bfa8a477d391c6ac1e17803963">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#f5ebb35b29cb59bbd816c07186eadd25">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#702c0d2e853e232ec4250609c917006d">CUpti_ActivityCdpKernel</a>
<li>computeApiKind
: <a class="el" href="structCUpti__ActivityContext.html#485dbd99cd57e151f1428ff4732f7f52">CUpti_ActivityContext</a>
<li>computeCapabilityMajor
: <a class="el" href="structCUpti__ActivityDevice.html#86f3de26fc67e56f6c518cb9a18890ce">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#de5cda35f337bd40d03091bf08358a7d">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#0aa6c5b25d6e6550e36b171c477cea4a">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#b486f6b884f1064179fc892eebe69bea">CUpti_ActivityDevice4</a>
<li>computeCapabilityMinor
: <a class="el" href="structCUpti__ActivityDevice.html#d2cab4240a6b1efa050041398801337c">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#eb658183ce1b87385dd1904f6b82182e">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#3a1163d2ebf2baec2f3eb4b9dad22c77">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#3b7e328f0bf453a96a8c3961d43bbaa9">CUpti_ActivityDevice4</a>
<li>computeInstanceId
: <a class="el" href="structCUpti__ActivityDevice4.html#3684f9752a3b1cd8956ad6e38e66b1a0">CUpti_ActivityDevice4</a>
<li>confidentialCompute
: <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#37583e75ba89a506594a9ce4f2932221">CUpti_Profiler_DeviceSupported_Params</a>
<li>configSize
: <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#b573048f9dfb3f88eecff556c5267fac">CUpti_Profiler_SetConfig_Params</a>
<li>constantMemorySize
: <a class="el" href="structCUpti__ActivityDevice.html#d5ef8f094da64ffcc3117369c31a4e8c">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#efe624a3b40bffa03f38818cd7153d31">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#18145ed01cdf41a71476fc5acd582964">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#06692a5be5bdeb9c304822ea329dbb13">CUpti_ActivityDevice4</a>
<li>context
: <a class="el" href="structCUpti__CallbackData.html#536ba0e56c569cba46be71ff06d34927">CUpti_CallbackData</a>
, <a class="el" href="structCUpti__ResourceData.html#a3d5a7b566d4bf6dc4ce57872d9d72a8">CUpti_ResourceData</a>
, <a class="el" href="structCUpti__SynchronizeData.html#5aad00715e047c03d1e77806a7e93321">CUpti_SynchronizeData</a>
<li>contextId
: <a class="el" href="structCUpti__ActivityMemcpy.html#d3697623a1fac6c2cd54805a201a70b1">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#40eba2a48618f18174116f317bb51ead">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#6b1f30cafe0f89f62f335848283620a3">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#48824985ad20df1d0560522476dae349">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#c2c9a5cf0e23b37eae4ba963956d2a2c">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#a6c6d3a1c5d50d3248ff43dc401d6e6d">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#45aacf73cd2394b8e22d8c1e44d315bb">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#08eff845e98619368e438c50fa5b4916">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityMemset.html#75c712107f6583925de1709e2de757dd">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#5eab4e4796b95a2290ee535f40c5df18">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#751135ae9813d7418610450db86416f2">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#8e857e1dc2bbd7f76a8040ba2f0747e3">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityMemory.html#cc2706a3423d065de869b589f705d759">CUpti_ActivityMemory</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#fe095f4500de8bb38feb52a26559468c">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#ee22ff7491df85a7b2dcc03dcd35d36e">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityKernel.html#45e5c6ebf342ea9973d5440d873f362d">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#3d2195b1665d6da8e83a3463f3977bc7">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#5aeab8003fdd6d65f06114ea56a619e1">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#61b625b5ad46c8e84dfe741187191263">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#cc9bd28490660434741a9dd627737c72">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#89a181dc4733a4c3bfc74f1eaedd111e">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#39e86b623aed7aa660b6fe1f2d67e61d">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#7ab3d6908500e5998d253e3e5ff91c7c">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#6dcfb7ced4d8cbe54ec02a140ab4820a">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#99430f438ac98686edde4fa2fe358586">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityContext.html#f5326dcba590f615f17b1a3df548f5ec">CUpti_ActivityContext</a>
, <a class="el" href="structCUpti__ActivityFunction.html#7b3ccbadecf78f48a31e43adbbfc40a8">CUpti_ActivityFunction</a>
, <a class="el" href="structCUpti__ActivityModule.html#21c8a353e98f2a401464419f25f8b109">CUpti_ActivityModule</a>
, <a class="el" href="structCUpti__ActivityCudaEvent.html#efbbd8468e123afeb696c206b0c9fddf">CUpti_ActivityCudaEvent</a>
, <a class="el" href="structCUpti__ActivityStream.html#77199545cd1731eb0eed799e4c8d2bc0">CUpti_ActivityStream</a>
, <a class="el" href="structCUpti__ActivitySynchronization.html#fc87e818eb828419da1c69e8f62d3426">CUpti_ActivitySynchronization</a>
, <a class="el" href="structCUpti__ActivityGraphTrace.html#e434e3b6d0b445397268c8acfb4dbad2">CUpti_ActivityGraphTrace</a>
<li>contextUid
: <a class="el" href="structCUpti__CallbackData.html#49f73c9d3877114e592f3266224062ed">CUpti_CallbackData</a>
<li>cooling
: <a class="el" href="structCUpti__ActivityEnvironment.html#ce7841a7064386ec4cac0a2e65952048">CUpti_ActivityEnvironment</a>
<li>copyKind
: <a class="el" href="structCUpti__ActivityMemcpy.html#54db77c0e4c71dc7f4b30213ff9f6f3b">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#de788e44dac2230c5b9bc296f22f1a8d">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#79565723da305c877f8fad81b3c4c4cf">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#f475f4ca796c8d69bc718bbf7d415b0a">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#af181be654f6a3701fc543378b1126e8">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#7a6e67c7b0e830508dcbc189ff1fd096">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ff70e9d897d3435ba1ffb988d36a1729">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#e2b7b2410638d1c9e2cc9662da6efb76">CUpti_ActivityMemcpyPtoP4</a>
<li>coreClockRate
: <a class="el" href="structCUpti__ActivityDevice.html#9f9cc4a4e357ee6ab879545ed89194e3">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#198ed6aa6d5de484796b3437a49ea0aa">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#1c32001a11787b74e8e5e05e785de766">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#13f173a627df6380783988d625197a20">CUpti_ActivityDevice4</a>
<li>correlationData
: <a class="el" href="structCUpti__CallbackData.html#4a1f2884b8d54d9771d6ee32df82c9f7">CUpti_CallbackData</a>
<li>correlationId
: <a class="el" href="structCUpti__ActivityPCSampling.html#3115fbabfebcd652c6de89e137b08acf">CUpti_ActivityPCSampling</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#aa3950c8b0948cd7695d056d6085ca23">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#7395211d58aafd2bb77d1d291db60b8f">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityKernel.html#2636d706fa71c3fe58f9448a6e2147f1">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#aa2420d2c56d463b3428f317b9f86ae2">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#80eb7835cb991157cd4561714e375b36">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#027755b451c467fd9a4ebf6b139e0e50">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#306490b1d2df0eacafa80f052fbc3020">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#d951fba194d863142500e4fae13f0a04">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#6452f4580f8ff6dda02ad598cd70ec5c">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#ceab5916ba047e3c97e104723e5510d7">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityAPI.html#31ebcf7b922b23850c6c85a9d5157b0d">CUpti_ActivityAPI</a>
, <a class="el" href="structCUpti__ActivityEvent.html#4361b2360c456229fd542e8ef70fcbe9">CUpti_ActivityEvent</a>
, <a class="el" href="structCUpti__ActivityEventInstance.html#3b5d7cd32de7b1734fdbe65caa230834">CUpti_ActivityEventInstance</a>
, <a class="el" href="structCUpti__ActivityStream.html#412ab3d99bb7040fe62114a539ded462">CUpti_ActivityStream</a>
, <a class="el" href="structCUpti__ActivityMetricInstance.html#07300ca46117d1b8faa60d88f4a8f9dd">CUpti_ActivityMetricInstance</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess.html#49fb23371936b61f7c01c43b9a711aa4">CUpti_ActivityGlobalAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#df3d2db4274bf1b565cd356774c0cf5f">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivitySynchronization.html#8cd8e5f8bd4f08fa554f968d58e95312">CUpti_ActivitySynchronization</a>
, <a class="el" href="structCUpti__ActivityBranch.html#a48e08fd6eff88ab2eb3b15a70005951">CUpti_ActivityBranch</a>
, <a class="el" href="structCUpti__ActivityExternalCorrelation.html#e0599ab202c22adb38fbe3d9c6dd8f95">CUpti_ActivityExternalCorrelation</a>
, <a class="el" href="structCUpti__ActivityPCSampling2.html#644805aa5b91ab6dcedfef2fe78ac155">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#9a21cfac5591387bce8a5a3b87dd09b6">CUpti_ActivityPCSampling3</a>
, <a class="el" href="structCUpti__ActivityPCSamplingRecordInfo.html#044490f0f419480c726bf98511190df5">CUpti_ActivityPCSamplingRecordInfo</a>
, <a class="el" href="structCUpti__ActivityCudaEvent.html#a89bf12152a100e012ff745f154733bb">CUpti_ActivityCudaEvent</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#68f4573a206d6e8fef2bb2268a12369b">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#5bb21c9050a325332bad376f935c6f6c">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityBranch2.html#6e9a8f7001f8c0a8ff83ee1611b450dc">CUpti_ActivityBranch2</a>
, <a class="el" href="structCUpti__ActivityJit.html#e2f63d8035076a80c9fe51c822bf78bc">CUpti_ActivityJit</a>
, <a class="el" href="structCUpti__ActivityGraphTrace.html#8e38e9e4c87cbbc5d0bd13280eb8b7d9">CUpti_ActivityGraphTrace</a>
, <a class="el" href="structCUpti__CallbackData.html#49f5003be9fb00a0593fdf7160c7beb4">CUpti_CallbackData</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#59b7a22a0e3c059a55940329ce6258e5">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#c778fe944693e558ad06264d94352701">CUpti_ActivityGlobalAccess3</a>
, <a class="el" href="structCUpti__ActivityMetric.html#b85de904d8d5ee8ee4fe0da422c2804e">CUpti_ActivityMetric</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#13f6fe80ac3995700fccfdaa12ca7b64">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#4319db4c6f7d6bd328cf1d4c6e69aa0b">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#ac86e1ca86d56826fbfe2ce3d4bbda5e">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#910198afe3cf3846e048b1d5219f19c7">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#8715619a0555e7e75e4f96890aad4af4">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#eb80c0867c8b9f38ac0b0bcbacf83c20">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#cc6298d3e24628e6e7b02d972970727f">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#7007a4b690ac5f19dc1115acbb735a1b">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#e72a3d638e2ed03b81f308cd69f53662">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ca9598dd7d04e303095949f66b595b2b">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#695effe20df007c58ec7af02677ae810">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityMemset.html#af27c20e42193ae3fa29aca352c49d10">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#d5db4db588c270483d226067b265f66f">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#613ca4ac2e8b2152f800e43338a858ef">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#ba753043faca43e903ab8dfcd068759f">CUpti_ActivityMemory2</a>
<li>counterAvailabilityImageSize
: <a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#dcbe4af6b1837d337ac83cc37798805f">CUpti_Profiler_GetCounterAvailability_Params</a>
<li>counterDataImageSize
: <a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#3134dc93a434d47f809effeeafd6cdb9">CUpti_Profiler_CounterDataImage_Initialize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#c4338dd6cbda228f6383465e156ac764">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#f6d86033296f95d41dba6bd8713993b4">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a>
, <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#6cbe6ce5fea2b09bdaf0a22a1c562d89">CUpti_Profiler_BeginSession_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#c5761b37242e63389003fdd4ce1281f3">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a>
<li>counterDataPrefixSize
: <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#0b22a7b53ab96aaa4a45f719d3b5f2b2">CUpti_Profiler_CounterDataImageOptions</a>
<li>counterDataScratchBufferSize
: <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#88eef26c1d443fa13803c775d84078e3">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a>
, <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#1712c6a4834d647ca20d204da810fc78">CUpti_Profiler_BeginSession_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#837ad10a6070eca5355dd24d53a4bba9">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a>
<li>counterKind
: <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#995e75718002dd0073994d2ef3ece4fe">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#f63374de2f160ffce57e0e49ae08b3b2">CUpti_ActivityUnifiedMemoryCounter</a>
<li>ctx
: <a class="el" href="structCUpti__PCSamplingStartParams.html#d794b1b1850fd0856c4bc4cd9f382a4d">CUpti_PCSamplingStartParams</a>
, <a class="el" href="structCUpti__PCSamplingGetDataParams.html#e26c6d443716d882f691f8efe24db9a1">CUpti_PCSamplingGetDataParams</a>
, <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#ace2f2f52ed397e873e18716a7bafe28">CUpti_Profiler_BeginSession_Params</a>
, <a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#7f83c9050db37e01bd25ca3da63d5b69">CUpti_Profiler_GetCounterAvailability_Params</a>
, <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#188817a75a763bcbd74c9b07520ca692">CUpti_PCSamplingGetNumStallReasonsParams</a>
, <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#34358d33d46e5316fe2daa6c2af541b6">CUpti_PCSamplingConfigurationInfoParams</a>
, <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#cdc97ff4f8a09a0ee0fbada64fa9814f">CUpti_Profiler_SetConfig_Params</a>
, <a class="el" href="structCUpti__Profiler__EndSession__Params.html#6278bc2fadcd1c6dfa9e4fa3a8c6bea4">CUpti_Profiler_EndSession_Params</a>
, <a class="el" href="structCUpti__Profiler__EndPass__Params.html#2dd6da8a0094da5ab474c10265d1ce3a">CUpti_Profiler_EndPass_Params</a>
, <a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#cfe11e827beb601c75d2c05852b50a11">CUpti_Profiler_FlushCounterData_Params</a>
, <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#f63a4b73a33d10e12a5eab95ef666bb9">NV::Cupti::Checkpoint::CUpti_Checkpoint</a>
, <a class="el" href="structCUpti__Profiler__DisableProfiling__Params.html#2d7f6a2e313e6ff68489ec4a1b754037">CUpti_Profiler_DisableProfiling_Params</a>
, <a class="el" href="structCUpti__PCSamplingStopParams.html#e93dde78e76645cd8bf43e84292d957e">CUpti_PCSamplingStopParams</a>
, <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#009ec28dba027a8d0a541d049603d7bd">CUpti_PCSamplingGetStallReasonsParams</a>
, <a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#731255ee7f1855d8e4af7d57f49536f1">CUpti_Profiler_IsPassCollected_Params</a>
, <a class="el" href="structCUpti__Profiler__EnableProfiling__Params.html#0414125262aa58ac1c04533c2ed6c7e1">CUpti_Profiler_EnableProfiling_Params</a>
, <a class="el" href="structCUpti__Profiler__BeginPass__Params.html#7c6c916db7a46816116f31968497641b">CUpti_Profiler_BeginPass_Params</a>
, <a class="el" href="structCUpti__PCSamplingEnableParams.html#bec3da3a3f9d2b8e17d990f11861d1ca">CUpti_PCSamplingEnableParams</a>
, <a class="el" href="structCUpti__Profiler__UnsetConfig__Params.html#09dc76a7eec3c8c051bf90e1e75e5b93">CUpti_Profiler_UnsetConfig_Params</a>
, <a class="el" href="structCUpti__PCSamplingDisableParams.html#ef6dbda762488c8a0da2475b87628464">CUpti_PCSamplingDisableParams</a>
<li>cubin
: <a class="el" href="structCUpti__GetCubinCrcParams.html#795bf869a594cf619d53cb0f1745da33">CUpti_GetCubinCrcParams</a>
, <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#20ae4d251f00a9e518b3c43078a1de75">CUpti_GetSassToSourceCorrelationParams</a>
, <a class="el" href="structCUpti__ActivityModule.html#75bdbc5fc0c610fcfc4c2240ee35ed9f">CUpti_ActivityModule</a>
<li>cubinCrc
: <a class="el" href="structCUpti__GetCubinCrcParams.html#5a4ba4f434ae84142462969517c79393">CUpti_GetCubinCrcParams</a>
, <a class="el" href="structCUpti__PCSamplingPCData.html#c70f1869eddeb333ed864d4836afc645">CUpti_PCSamplingPCData</a>
<li>cubinSize
: <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#61f5e6b65d3cf01a24ec0fc6b6e66ef9">CUpti_GetSassToSourceCorrelationParams</a>
, <a class="el" href="structCUpti__GetCubinCrcParams.html#7b94af2b1abe10da1d495b242e645a65">CUpti_GetCubinCrcParams</a>
, <a class="el" href="structCUpti__ActivityModule.html#484c4f871d4640080f1423116dcfdd35">CUpti_ActivityModule</a>
, <a class="el" href="structCUpti__ModuleResourceData.html#be33f366f20f4de770a8f71821c70175">CUpti_ModuleResourceData</a>
<li>cuContextId
: <a class="el" href="structCUpti__ActivityOpenAccOther.html#a3bbac0d04d417352041f1d0e8110ab6">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#81d02af0a1a0c452cf06941885070b34">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#b0a74b8822815cbd13e98264dfb505f6">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#b0010f5478fe49a056db03acde332778">CUpti_ActivityOpenAccData</a>
<li>cudaEventId
: <a class="el" href="structCUpti__ActivitySynchronization.html#083e4344decfea8b7d8b1ee45c822c52">CUpti_ActivitySynchronization</a>
<li>cuDevice
: <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#3edec41549077cfbc87da9f5d2333e4b">CUpti_Profiler_DeviceSupported_Params</a>
<li>cuDeviceId
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#47d2055d7659060858a3639e66552d88">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#f4a231cab24d941f13ba728f0a8b3109">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#05595dd4ed260e4cde2cb3457e569377">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#ffe555c3d60dcb8770e3052b74b30f0b">CUpti_ActivityOpenAccOther</a>
<li>cuProcessId
: <a class="el" href="structCUpti__ActivityOpenAccData.html#8191f559463d3c9010ccc9f8e7152617">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenMp.html#db6237b2a554d4ad52a6744de746c0db">CUpti_ActivityOpenMp</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#2de64d3c200f75a7db15677956ecc966">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#294ffb92ef4cf0e943867ac55fd73a82">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#83c7c475f684690388443bc3dd488271">CUpti_ActivityOpenAccLaunch</a>
<li>cuStreamId
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#8491d24973366a3d333360ba0651b2f9">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#eb71e6f91975fb8818856ed196c046b6">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#ad7fcaeec49a422e87bef2071cda098e">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#176ab2a2c03001fc1ac89376cc7e6eaf">CUpti_ActivityOpenAcc</a>
<li>cuThreadId
: <a class="el" href="structCUpti__ActivityOpenAccOther.html#b6786ac497f391b3090d164376a53151">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenMp.html#6aa63fab93c46b7be6ecd1dca36256c8">CUpti_ActivityOpenMp</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#bff7482d2fa33f1b81f90e93e6c96ca8">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#cb345df2cc10791c0caefb14b25c44fe">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#34dd59dff4b6fb79749c6210163b40a9">CUpti_ActivityOpenAcc</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
