/*
 * MIT License
 * 
 * Copyright (c) Microsoft Corporation.
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE
 * 
 */
CSSDocumentation={"-webkit-animation":{"description":"Shorthand property combines six of the animation properties into a single property."},"-webkit-animation-delay":{"description":"Defines when the animation will start."},"-webkit-animation-direction":{"description":"Defines whether or not the animation should play in reverse on alternate cycles."},"-webkit-animation-duration":{"description":"Defines the length of time that an animation takes to complete one cycle."},"-webkit-animation-fill-mode":{"description":"Defines what values are applied by the animation outside the time it is executing."},"-webkit-animation-iteration-count":{"description":"Defines the number of times an animation cycle is played. The default value is one, meaning the animation will play from beginning to end once."},"-webkit-animation-name":{"description":"Defines a list of animations that apply. Each name is used to select the keyframe at-rule that provides the property values for the animation."},"-webkit-animation-play-state":{"description":"Defines whether the animation is running or paused."},"-webkit-animation-timing-function":{"description":"Describes how the animation will progress over one cycle of its duration. See the 'transition-timing-function'."},"-webkit-appearance":{"description":"Changes the appearance of buttons and other controls to resemble native controls.","syntax":"none | button | checkbox | listbox | menulist | menulist-button | meter | progress-bar | push-button | radio | searchfield | slider-horizontal | slider-vertical | square-button | textarea | textfield | -apple-pay-button"},"-webkit-backdrop-filter":{"description":"Applies a filter effect where the first filter in the list takes the element's background image as the input image."},"-webkit-backface-visibility":{"description":"Determines whether or not the 'back' side of a transformed element is visible when facing the viewer. With an identity transform, the front side of an element faces the viewer."},"-webkit-background-clip":{"description":"Determines the background painting area."},"-webkit-background-origin":{"description":"For elements rendered as a single box, specifies the background positioning area. For elements rendered as multiple boxes (e.g., inline boxes on several lines, boxes on several pages) specifies which boxes 'box-decoration-break' operates on to determine the background positioning area(s)."},"-webkit-border-before":{"description":"The -webkit-border-before CSS property is a shorthand property for setting the individual logical block start border property values in a single place in the style sheet.","syntax":"<'border-width'> || <'border-style'> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-border-before"},"-webkit-border-before-color":{"description":"The -webkit-border-before-color CSS property sets the color of the individual logical block start border in a single place in the style sheet.","syntax":"<color>"},"-webkit-border-before-style":{"description":"The -webkit-border-before-style CSS property sets the style of the individual logical block start border in a single place in the style sheet.","syntax":"<'border-style'>"},"-webkit-border-before-width":{"description":"The -webkit-border-before-width CSS property sets the width of the individual logical block start border in a single place in the style sheet.","syntax":"<'border-width'>"},"-webkit-border-image":{"description":"Shorthand property for setting 'border-image-source', 'border-image-slice', 'border-image-width', 'border-image-outset' and 'border-image-repeat'. Omitted values are set to their initial values."},"-webkit-box-align":{"description":"Specifies the alignment of nested elements within an outer flexible box element."},"-webkit-box-direction":{"description":"In webkit applications, -webkit-box-direction specifies whether a box lays out its contents normally (from the top or left edge), or in reverse (from the bottom or right edge)."},"-webkit-box-flex":{"description":"Specifies an element's flexibility."},"-webkit-box-flex-group":{"description":"Flexible elements can be assigned to flex groups using the 'box-flex-group' property."},"-webkit-box-ordinal-group":{"description":"Indicates the ordinal group the element belongs to. Elements with a lower ordinal group are displayed before those with a higher ordinal group."},"-webkit-box-orient":{"description":"In webkit applications, -webkit-box-orient specifies whether a box lays out its contents horizontally or vertically."},"-webkit-box-pack":{"description":"Specifies alignment of child elements within the current element in the direction of orientation."},"-webkit-box-reflect":{"description":"Defines a reflection of a border box.","syntax":"[ above | below | right | left ]? <length>? <image>?","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-box-reflect"},"-webkit-box-sizing":{"description":"Box Model addition in CSS3."},"-webkit-break-after":{"description":"Describes the page/column break behavior before the generated box."},"-webkit-break-before":{"description":"Describes the page/column break behavior before the generated box."},"-webkit-break-inside":{"description":"Describes the page/column break behavior inside the generated box."},"-webkit-column-break-after":{"description":"Describes the page/column break behavior before the generated box."},"-webkit-column-break-before":{"description":"Describes the page/column break behavior before the generated box."},"-webkit-column-break-inside":{"description":"Describes the page/column break behavior inside the generated box."},"-webkit-column-count":{"description":"Describes the optimal number of columns into which the content of the element will be flowed."},"-webkit-column-gap":{"description":"Sets the gap between columns. If there is a column rule between columns, it will appear in the middle of the gap."},"-webkit-column-rule":{"description":"This property is a shorthand for setting 'column-rule-width', 'column-rule-style', and 'column-rule-color' at the same place in the style sheet. Omitted values are set to their initial values."},"-webkit-column-rule-color":{"description":"Sets the color of the column rule"},"-webkit-column-rule-style":{"description":"Sets the style of the rule between columns of an element."},"-webkit-column-rule-width":{"description":"Sets the width of the rule between columns. Negative values are not allowed."},"-webkit-column-span":{"description":"Describes the page/column break behavior after the generated box."},"-webkit-column-width":{"description":"This property describes the width of columns in multicol elements."},"-webkit-columns":{"description":"A shorthand property which sets both 'column-width' and 'column-count'."},"-webkit-filter":{"description":"Processes an element's rendering before it is displayed in the document, by applying one or more filter effects."},"-webkit-flow-from":{"description":"Makes a block container a region and associates it with a named flow."},"-webkit-flow-into":{"description":"Places an element or its contents into a named flow."},"-webkit-font-feature-settings":{"description":"This property provides low-level control over OpenType font features. It is intended as a way of providing access to font features that are not widely used but are needed for a particular use case."},"-webkit-hyphens":{"description":"Controls whether hyphenation is allowed to create more break opportunities within a line of text."},"-webkit-line-break":{"description":"Specifies line-breaking rules for CJK (Chinese, Japanese, and Korean) text."},"-webkit-line-clamp":{"description":"The -webkit-line-clamp CSS property allows limiting of the contents of a block container to the specified number of lines.","syntax":"none | <integer>"},"-webkit-mask":{"description":"The mask CSS property alters the visibility of an element by either partially or fully hiding it. This is accomplished by either masking or clipping the image at specific points.","syntax":"[ <mask-reference> || <position> [ / <bg-size> ]? || <repeat-style> || [ <box> | border | padding | content | text ] || [ <box> | border | padding | content ] ]#"},"-webkit-mask-attachment":{"description":"If a -webkit-mask-image is specified, -webkit-mask-attachment determines whether the mask image's position is fixed within the viewport, or scrolls along with its containing block.","syntax":"<attachment>#"},"-webkit-mask-clip":{"description":"Determines the mask painting area, which determines the area that is affected by the mask.","syntax":"[ <box> | border | padding | content | text ]#"},"-webkit-mask-composite":{"description":"The -webkit-mask-composite property specifies the manner in which multiple mask images applied to the same element are composited with one another. Mask images are composited in the opposite order that they are declared with the -webkit-mask-image property.","syntax":"<composite-style>#","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-mask-composite"},"-webkit-mask-image":{"description":"Sets the mask layer image of an element.","syntax":"<mask-reference>#"},"-webkit-mask-origin":{"description":"Specifies the mask positioning area.","syntax":"[ <box> | border | padding | content ]#"},"-webkit-mask-position":{"description":"The mask-position CSS property sets the initial position, relative to the mask position layer defined by mask-origin, for each defined mask image.","syntax":"<position>#"},"-webkit-mask-position-x":{"description":"The -webkit-mask-position-x CSS property sets the initial horizontal position of a mask image.","syntax":"[ <length-percentage> | left | center | right ]#","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-mask-position-x"},"-webkit-mask-position-y":{"description":"The -webkit-mask-position-y CSS property sets the initial vertical position of a mask image.","syntax":"[ <length-percentage> | top | center | bottom ]#","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-mask-position-y"},"-webkit-mask-repeat":{"description":"Specifies how mask layer images are tiled after they have been sized and positioned.","syntax":"<repeat-style>#"},"-webkit-mask-repeat-x":{"description":"The -webkit-mask-repeat-x property specifies whether and how a mask image is repeated (tiled) horizontally.","syntax":"repeat | no-repeat | space | round","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-mask-repeat-x"},"-webkit-mask-repeat-y":{"description":"The -webkit-mask-repeat-y property specifies whether and how a mask image is repeated (tiled) vertically.","syntax":"repeat | no-repeat | space | round","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-mask-repeat-y"},"-webkit-mask-size":{"description":"Specifies the size of the mask layer images.","syntax":"<bg-size>#"},"-webkit-nbsp-mode":{"description":"Defines the behavior of nonbreaking spaces within text."},"-webkit-overflow-scrolling":{"description":"Specifies whether to use native-style scrolling in an overflow:scroll element.","syntax":"auto | touch"},"-webkit-perspective":{"description":"Applies the same transform as the perspective(<number>) transform function, except that it applies only to the positioned or transformed children of the element, not to the transform on the element itself."},"-webkit-perspective-origin":{"description":"Establishes the origin for the perspective property. It effectively sets the X and Y position at which the viewer appears to be looking at the children of the element."},"-webkit-region-fragment":{"description":"The 'region-fragment' property controls the behavior of the last region associated with a named flow."},"-webkit-tap-highlight-color":{"syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-tap-highlight-color"},"-webkit-text-fill-color":{"syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-text-fill-color"},"-webkit-text-size-adjust":{"description":"Specifies a size adjustment for displaying text content in mobile browsers."},"-webkit-text-stroke":{"syntax":"<length> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-text-stroke"},"-webkit-text-stroke-color":{"syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-text-stroke-color"},"-webkit-text-stroke-width":{"syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-text-stroke-width"},"-webkit-touch-callout":{"syntax":"default | none","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-touch-callout"},"-webkit-transform":{"description":"A two-dimensional transformation is applied to an element through the 'transform' property. This property contains a list of transform functions similar to those allowed by SVG."},"-webkit-transform-origin":{"description":"Establishes the origin of transformation for an element."},"-webkit-transform-origin-x":{"description":"The x coordinate of the origin for transforms applied to an element with respect to its border box."},"-webkit-transform-origin-y":{"description":"The y coordinate of the origin for transforms applied to an element with respect to its border box."},"-webkit-transform-origin-z":{"description":"The z coordinate of the origin for transforms applied to an element with respect to its border box."},"-webkit-transform-style":{"description":"Defines how nested elements are rendered in 3D space."},"-webkit-transition":{"description":"Shorthand property combines four of the transition properties into a single property."},"-webkit-transition-delay":{"description":"Defines when the transition will start. It allows a transition to begin execution some period of time from when it is applied."},"-webkit-transition-duration":{"description":"Specifies how long the transition from the old value to the new value should take."},"-webkit-transition-property":{"description":"Specifies the name of the CSS property to which the transition is applied."},"-webkit-transition-timing-function":{"description":"Describes how the intermediate values used during a transition will be calculated."},"-webkit-user-modify":{"description":"Determines whether a user can edit the content of an element.","syntax":"read-only | read-write | read-write-plaintext-only"},"-webkit-user-select":{"description":"Controls the appearance of selection."},"accent-color":{"description":"Sets the color of the elements accent","syntax":"auto | <color>","url":"https://developer.mozilla.org/docs/Web/CSS/accent-color"},"additive-symbols":{"description":"@counter-style descriptor. Specifies the symbols used by the marker-construction algorithm specified by the system descriptor. Needs to be specified if the counter system is 'additive'.","syntax":"[ <integer> && <symbol> ]#"},"align-content":{"description":"Aligns a flex container's lines within the flex container when there is extra space in the cross-axis, similar to how 'justify-content' aligns individual items within the main-axis.","syntax":"normal | <baseline-position> | <content-distribution> | <overflow-position>? <content-position>","url":"https://developer.mozilla.org/docs/Web/CSS/align-content"},"align-items":{"description":"Aligns flex items along the cross axis of the current line of the flex container.","syntax":"normal | stretch | <baseline-position> | [ <overflow-position>? <self-position> ]","url":"https://developer.mozilla.org/docs/Web/CSS/align-items"},"align-self":{"description":"Allows the default alignment along the cross axis to be overridden for individual flex items.","syntax":"auto | normal | stretch | <baseline-position> | <overflow-position>? <self-position>","url":"https://developer.mozilla.org/docs/Web/CSS/align-self"},"align-tracks":{"description":"The align-tracks CSS property sets the alignment in the masonry axis for grid containers that have masonry in their block axis.","syntax":"[ normal | <baseline-position> | <content-distribution> | <overflow-position>? <content-position> ]#"},"all":{"description":"Shorthand that resets all properties except 'direction' and 'unicode-bidi'.","syntax":"initial | inherit | unset | revert | revert-layer","url":"https://developer.mozilla.org/docs/Web/CSS/all"},"alt":{"description":"Provides alternative text for assistive technology to replace the generated content of a ::before or ::after element."},"anchor-name":{"description":"The anchor-name property declares that an element is an anchor element, and gives it a list of anchor names to be targeted by.","syntax":"none | <dashed-ident>#","url":"https://developer.mozilla.org/docs/Web/CSS/anchor-name"},"anchor-scope":{"description":"This property scopes the specified anchor names, and lookups for these anchor names, to this element\u2019s subtree","syntax":"none | all | <dashed-ident>#"},"animation":{"description":"Shorthand property combines six of the animation properties into a single property.","syntax":"<single-animation>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation"},"animation-composition":{"description":"The composite operation to use when multiple animations affect the same property.","syntax":"<single-animation-composition>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-composition"},"animation-delay":{"description":"Defines when the animation will start.","syntax":"<time>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-delay"},"animation-direction":{"description":"Defines whether or not the animation should play in reverse on alternate cycles.","syntax":"<single-animation-direction>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-direction"},"animation-duration":{"description":"Defines the length of time that an animation takes to complete one cycle.","syntax":"<time>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-duration"},"animation-fill-mode":{"description":"Defines what values are applied by the animation outside the time it is executing.","syntax":"<single-animation-fill-mode>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-fill-mode"},"animation-iteration-count":{"description":"Defines the number of times an animation cycle is played. The default value is one, meaning the animation will play from beginning to end once.","syntax":"<single-animation-iteration-count>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-iteration-count"},"animation-name":{"description":"Defines a list of animations that apply. Each name is used to select the keyframe at-rule that provides the property values for the animation.","syntax":"[ none | <keyframes-name> ]#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-name"},"animation-play-state":{"description":"Defines whether the animation is running or paused.","syntax":"<single-animation-play-state>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-play-state"},"animation-range":{"description":"The animation-range CSS shorthand property is used to set the start and end of an animation's attachment range along its timeline, i.e. where along the timeline an animation will start and end.","syntax":"[ <'animation-range-start'> <'animation-range-end'>? ]#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-range"},"animation-range-end":{"description":"The animation-range-end CSS property is used to set the end of an animation's attachment range along its timeline, i.e. where along the timeline an animation will end.","syntax":"[ normal | <length-percentage> | <timeline-range-name> <length-percentage>? ]#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-range-end"},"animation-range-start":{"description":"The animation-range-start CSS property is used to set the start of an animation's attachment range along its timeline, i.e. where along the timeline an animation will start.","syntax":"[ normal | <length-percentage> | <timeline-range-name> <length-percentage>? ]#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-range-start"},"animation-timeline":{"description":"Specifies the names of one or more @scroll-timeline at-rules to describe the element's scroll animations.","syntax":"<single-animation-timeline>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-timeline"},"animation-timing-function":{"description":"Describes how the animation will progress over one cycle of its duration.","syntax":"<easing-function>#","url":"https://developer.mozilla.org/docs/Web/CSS/animation-timing-function"},"appearance":{"description":"Changes the appearance of buttons and other controls to resemble native controls.","syntax":"none | auto | textfield | menulist-button | <compat-auto>","url":"https://developer.mozilla.org/docs/Web/CSS/appearance"},"ascent-override":{"description":"Describes the ascent metric of a font.","syntax":"normal | <percentage>"},"aspect-ratio":{"description":"The aspect-ratio   CSS property sets a preferred aspect ratio for the box, which will be used in the calculation of auto sizes and some other layout functions.","syntax":"auto || <ratio>","url":"https://developer.mozilla.org/docs/Web/CSS/aspect-ratio"},"azimuth":{"description":"In combination with elevation, the azimuth CSS property enables different audio sources to be positioned spatially for aural presentation. This is important in that it provides a natural way to tell several voices apart, as each can be positioned to originate at a different location on the sound stage. Stereo output produce a lateral sound stage, while binaural headphones and multi-speaker setups allow for a fully three-dimensional stage.","syntax":"<angle> | [ [ left-side | far-left | left | center-left | center | center-right | right | far-right | right-side ] || behind ] | leftwards | rightwards"},"backdrop-filter":{"description":"The backdrop-filter CSS property lets you apply graphical effects such as blurring or color shifting to the area behind an element. Because it applies to everything behind the element, to see the effect you must make the element or its background at least partially transparent.","syntax":"none | <filter-function-list>","url":"https://developer.mozilla.org/docs/Web/CSS/backdrop-filter"},"backface-visibility":{"description":"Determines whether or not the 'back' side of a transformed element is visible when facing the viewer. With an identity transform, the front side of an element faces the viewer.","syntax":"visible | hidden","url":"https://developer.mozilla.org/docs/Web/CSS/backface-visibility"},"background":{"description":"Shorthand property for setting most background properties at the same place in the style sheet.","syntax":"[ <bg-layer> , ]* <final-bg-layer>","url":"https://developer.mozilla.org/docs/Web/CSS/background"},"background-attachment":{"description":"Specifies whether the background images are fixed with regard to the viewport ('fixed') or scroll along with the element ('scroll') or its contents ('local').","syntax":"<attachment>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-attachment"},"background-blend-mode":{"description":"Defines the blending mode of each background layer.","syntax":"<blend-mode>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-blend-mode"},"background-clip":{"description":"Determines the background painting area.","syntax":"<box>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-clip"},"background-color":{"description":"Sets the background color of an element.","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/background-color"},"background-image":{"description":"Sets the background image(s) of an element.","syntax":"<bg-image>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-image"},"background-origin":{"description":"For elements rendered as a single box, specifies the background positioning area. For elements rendered as multiple boxes (e.g., inline boxes on several lines, boxes on several pages) specifies which boxes 'box-decoration-break' operates on to determine the background positioning area(s).","syntax":"<box>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-origin"},"background-position":{"description":"Specifies the initial position of the background image(s) (after any resizing) within their corresponding background positioning area.","syntax":"<bg-position>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-position"},"background-position-x":{"description":"If background images have been specified, this property specifies their initial position (after any resizing) within their corresponding background positioning area.","syntax":"[ center | [ [ left | right | x-start | x-end ]? <length-percentage>? ]! ]#","url":"https://developer.mozilla.org/docs/Web/CSS/background-position-x"},"background-position-y":{"description":"If background images have been specified, this property specifies their initial position (after any resizing) within their corresponding background positioning area.","syntax":"[ center | [ [ top | bottom | y-start | y-end ]? <length-percentage>? ]! ]#","url":"https://developer.mozilla.org/docs/Web/CSS/background-position-y"},"background-repeat":{"description":"Specifies how background images are tiled after they have been sized and positioned.","syntax":"<repeat-style>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-repeat"},"background-size":{"description":"Specifies the size of the background images.","syntax":"<bg-size>#","url":"https://developer.mozilla.org/docs/Web/CSS/background-size"},"base-palette":{"description":"The base-palette CSS descriptor is used to specify the name or index of a pre-defined palette to be used for creating a new palette. If the specified base-palette does not exist, then the palette defined at index 0 will be used.","syntax":"light | dark | <integer [0,\u221e]>"},"behavior":{"description":"IE only. Used to extend behaviors of the browser."},"bleed":{"description":"The bleed CSS at-rule descriptor, used with the @page at-rule, specifies the extent of the page bleed area outside the page box. This property only has effect if crop marks are enabled using the marks property.","syntax":"auto | <length>"},"block-size":{"description":"Size of an element in the direction opposite that of the direction specified by 'writing-mode'.","syntax":"<'width'>","url":"https://developer.mozilla.org/docs/Web/CSS/block-size"},"border":{"description":"Shorthand property for setting border width, style, and color.","syntax":"<line-width> || <line-style> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border"},"border-block":{"description":"The border-block CSS property is a shorthand property for setting the individual logical block border property values in a single place in the style sheet.","syntax":"<'border-top-width'> || <'border-top-style'> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block"},"border-block-color":{"description":"The border-block-color CSS property defines the color of the logical block borders of an element, which maps to a physical border color depending on the element's writing mode, directionality, and text orientation. It corresponds to the border-top-color and border-bottom-color, or border-right-color and border-left-color property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'border-top-color'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-color"},"border-block-end":{"description":"Logical 'border-bottom'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'> || <'border-top-style'> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-end"},"border-block-end-color":{"description":"Logical 'border-bottom-color'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-color'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-end-color"},"border-block-end-style":{"description":"Logical 'border-bottom-style'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-end-style"},"border-block-end-width":{"description":"Logical 'border-bottom-width'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-end-width"},"border-block-start":{"description":"Logical 'border-top'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'> || <'border-top-style'> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-start"},"border-block-start-color":{"description":"Logical 'border-top-color'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-color'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-start-color"},"border-block-start-style":{"description":"Logical 'border-top-style'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-start-style"},"border-block-start-width":{"description":"Logical 'border-top-width'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-start-width"},"border-block-style":{"description":"The border-block-style CSS property defines the style of the logical block borders of an element, which maps to a physical border style depending on the element's writing mode, directionality, and text orientation. It corresponds to the border-top-style and border-bottom-style, or border-left-style and border-right-style properties depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'border-top-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-style"},"border-block-width":{"description":"The border-block-width CSS property defines the width of the logical block borders of an element, which maps to a physical border width depending on the element's writing mode, directionality, and text orientation. It corresponds to the border-top-width and border-bottom-width, or border-left-width, and border-right-width property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'border-top-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-block-width"},"border-bottom":{"description":"Shorthand property for setting border width, style and color.","syntax":"<line-width> || <line-style> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-bottom"},"border-bottom-color":{"description":"Sets the color of the bottom border.","syntax":"<'border-top-color'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-bottom-color"},"border-bottom-left-radius":{"description":"Defines the radii of the bottom left outer border edge.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-bottom-left-radius"},"border-bottom-right-radius":{"description":"Defines the radii of the bottom right outer border edge.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-bottom-right-radius"},"border-bottom-style":{"description":"Sets the style of the bottom border.","syntax":"<line-style>","url":"https://developer.mozilla.org/docs/Web/CSS/border-bottom-style"},"border-bottom-width":{"description":"Sets the thickness of the bottom border.","syntax":"<line-width>","url":"https://developer.mozilla.org/docs/Web/CSS/border-bottom-width"},"border-collapse":{"description":"Selects a table's border model.","syntax":"collapse | separate","url":"https://developer.mozilla.org/docs/Web/CSS/border-collapse"},"border-color":{"description":"The color of the border around all four edges of an element.","syntax":"<color>{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/border-color"},"border-end-end-radius":{"description":"The border-end-end-radius CSS property defines a logical border radius on an element, which maps to a physical border radius that depends on on the element's writing-mode, direction, and text-orientation.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-end-end-radius"},"border-end-start-radius":{"description":"The border-end-start-radius CSS property defines a logical border radius on an element, which maps to a physical border radius depending on the element's writing-mode, direction, and text-orientation.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-end-start-radius"},"border-image":{"description":"Shorthand property for setting 'border-image-source', 'border-image-slice', 'border-image-width', 'border-image-outset' and 'border-image-repeat'. Omitted values are set to their initial values.","syntax":"<'border-image-source'> || <'border-image-slice'> [ / <'border-image-width'> | / <'border-image-width'>? / <'border-image-outset'> ]? || <'border-image-repeat'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-image"},"border-image-outset":{"description":"The values specify the amount by which the border image area extends beyond the border box on the top, right, bottom, and left sides respectively. If the fourth value is absent, it is the same as the second. If the third one is also absent, it is the same as the first. If the second one is also absent, it is the same as the first. Numbers represent multiples of the corresponding border-width.","syntax":"[ <length> | <number> ]{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/border-image-outset"},"border-image-repeat":{"description":"Specifies how the images for the sides and the middle part of the border image are scaled and tiled. If the second keyword is absent, it is assumed to be the same as the first.","syntax":"[ stretch | repeat | round | space ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-image-repeat"},"border-image-slice":{"description":"Specifies inward offsets from the top, right, bottom, and left edges of the image, dividing it into nine regions: four corners, four edges and a middle.","syntax":"<number-percentage>{1,4} && fill?","url":"https://developer.mozilla.org/docs/Web/CSS/border-image-slice"},"border-image-source":{"description":"Specifies an image to use instead of the border styles given by the 'border-style' properties and as an additional background layer for the element. If the value is 'none' or if the image cannot be displayed, the border styles will be used.","syntax":"none | <image>","url":"https://developer.mozilla.org/docs/Web/CSS/border-image-source"},"border-image-width":{"description":"The four values of 'border-image-width' specify offsets that are used to divide the border image area into nine parts. They represent inward distances from the top, right, bottom, and left sides of the area, respectively.","syntax":"[ <length-percentage> | <number> | auto ]{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/border-image-width"},"border-inline":{"description":"The border-inline CSS property is a shorthand property for setting the individual logical inline border property values in a single place in the style sheet.","syntax":"<'border-top-width'> || <'border-top-style'> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline"},"border-inline-color":{"description":"The border-inline-color CSS property defines the color of the logical inline borders of an element, which maps to a physical border color depending on the element's writing mode, directionality, and text orientation. It corresponds to the border-top-color and border-bottom-color, or border-right-color and border-left-color property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'border-top-color'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-color"},"border-inline-end":{"description":"Logical 'border-right'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'> || <'border-top-style'> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-end"},"border-inline-end-color":{"description":"Logical 'border-right-color'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-color'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-end-color"},"border-inline-end-style":{"description":"Logical 'border-right-style'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-end-style"},"border-inline-end-width":{"description":"Logical 'border-right-width'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-end-width"},"border-inline-start":{"description":"Logical 'border-left'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'> || <'border-top-style'> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-start"},"border-inline-start-color":{"description":"Logical 'border-left-color'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-color'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-start-color"},"border-inline-start-style":{"description":"Logical 'border-left-style'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-start-style"},"border-inline-start-width":{"description":"Logical 'border-left-width'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'border-top-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-start-width"},"border-inline-style":{"description":"The border-inline-style CSS property defines the style of the logical inline borders of an element, which maps to a physical border style depending on the element's writing mode, directionality, and text orientation. It corresponds to the border-top-style and border-bottom-style, or border-left-style and border-right-style properties depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'border-top-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-style"},"border-inline-width":{"description":"The border-inline-width CSS property defines the width of the logical inline borders of an element, which maps to a physical border width depending on the element's writing mode, directionality, and text orientation. It corresponds to the border-top-width and border-bottom-width, or border-left-width, and border-right-width property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'border-top-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/border-inline-width"},"border-left":{"description":"Shorthand property for setting border width, style and color","syntax":"<line-width> || <line-style> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-left"},"border-left-color":{"description":"Sets the color of the left border.","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-left-color"},"border-left-style":{"description":"Sets the style of the left border.","syntax":"<line-style>","url":"https://developer.mozilla.org/docs/Web/CSS/border-left-style"},"border-left-width":{"description":"Sets the thickness of the left border.","syntax":"<line-width>","url":"https://developer.mozilla.org/docs/Web/CSS/border-left-width"},"border-radius":{"description":"Defines the radii of the outer border edge.","syntax":"<length-percentage>{1,4} [ / <length-percentage>{1,4} ]?","url":"https://developer.mozilla.org/docs/Web/CSS/border-radius"},"border-right":{"description":"Shorthand property for setting border width, style and color","syntax":"<line-width> || <line-style> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-right"},"border-right-color":{"description":"Sets the color of the right border.","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-right-color"},"border-right-style":{"description":"Sets the style of the right border.","syntax":"<line-style>","url":"https://developer.mozilla.org/docs/Web/CSS/border-right-style"},"border-right-width":{"description":"Sets the thickness of the right border.","syntax":"<line-width>","url":"https://developer.mozilla.org/docs/Web/CSS/border-right-width"},"border-spacing":{"description":"The lengths specify the distance that separates adjoining cell borders. If one length is specified, it gives both the horizontal and vertical spacing. If two are specified, the first gives the horizontal spacing and the second the vertical spacing. Lengths may not be negative.","syntax":"<length> <length>?","url":"https://developer.mozilla.org/docs/Web/CSS/border-spacing"},"border-start-end-radius":{"description":"The border-start-end-radius CSS property defines a logical border radius on an element, which maps to a physical border radius depending on the element's writing-mode, direction, and text-orientation.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-start-end-radius"},"border-start-start-radius":{"description":"The border-start-start-radius CSS property defines a logical border radius on an element, which maps to a physical border radius that depends on the element's writing-mode, direction, and text-orientation.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-start-start-radius"},"border-style":{"description":"The style of the border around edges of an element.","syntax":"<line-style>{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/border-style"},"border-top":{"description":"Shorthand property for setting border width, style and color","syntax":"<line-width> || <line-style> || <color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-top"},"border-top-color":{"description":"Sets the color of the top border.","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/border-top-color"},"border-top-left-radius":{"description":"Defines the radii of the top left outer border edge.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-top-left-radius"},"border-top-right-radius":{"description":"Defines the radii of the top right outer border edge.","syntax":"<length-percentage>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/border-top-right-radius"},"border-top-style":{"description":"Sets the style of the top border.","syntax":"<line-style>","url":"https://developer.mozilla.org/docs/Web/CSS/border-top-style"},"border-top-width":{"description":"Sets the thickness of the top border.","syntax":"<line-width>","url":"https://developer.mozilla.org/docs/Web/CSS/border-top-width"},"border-width":{"description":"Shorthand that sets the four 'border-*-width' properties. If it has four values, they set top, right, bottom and left in that order. If left is missing, it is the same as right; if bottom is missing, it is the same as top; if right is missing, it is the same as top.","syntax":"<line-width>{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/border-width"},"bottom":{"description":"Specifies how far an absolutely positioned box's bottom margin edge is offset above the bottom edge of the box's 'containing block'.","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/bottom"},"box-align":{"description":"The box-align CSS property specifies how an element aligns its contents across its layout in a perpendicular direction. The effect of the property is only visible if there is extra space in the box.","syntax":"start | center | end | baseline | stretch","url":"https://developer.mozilla.org/docs/Web/CSS/box-align"},"box-decoration-break":{"description":"Specifies whether individual boxes are treated as broken pieces of one continuous box, or whether each box is individually wrapped with the border and padding.","syntax":"slice | clone","url":"https://developer.mozilla.org/docs/Web/CSS/box-decoration-break"},"box-direction":{"description":"The box-direction CSS property specifies whether a box lays out its contents normally (from the top or left edge), or in reverse (from the bottom or right edge).","syntax":"normal | reverse | inherit","url":"https://developer.mozilla.org/docs/Web/CSS/box-direction"},"box-flex":{"description":"The -moz-box-flex and -webkit-box-flex CSS properties specify how a -moz-box or -webkit-box grows to fill the box that contains it, in the direction of the containing box's layout.","syntax":"<number>","url":"https://developer.mozilla.org/docs/Web/CSS/box-flex"},"box-flex-group":{"description":"The box-flex-group CSS property assigns the flexbox's child elements to a flex group.","syntax":"<integer>","url":"https://developer.mozilla.org/docs/Web/CSS/box-flex-group"},"box-lines":{"description":"The box-lines CSS property determines whether the box may have a single or multiple lines (rows for horizontally oriented boxes, columns for vertically oriented boxes).","syntax":"single | multiple","url":"https://developer.mozilla.org/docs/Web/CSS/box-lines"},"box-ordinal-group":{"description":"The box-ordinal-group CSS property assigns the flexbox's child elements to an ordinal group.","syntax":"<integer>","url":"https://developer.mozilla.org/docs/Web/CSS/box-ordinal-group"},"box-orient":{"description":"The box-orient CSS property specifies whether an element lays out its contents horizontally or vertically.","syntax":"horizontal | vertical | inline-axis | block-axis | inherit","url":"https://developer.mozilla.org/docs/Web/CSS/box-orient"},"box-pack":{"description":"The -moz-box-pack and -webkit-box-pack CSS properties specify how a -moz-box or -webkit-box packs its contents in the direction of its layout. The effect of this is only visible if there is extra space in the box.","syntax":"start | center | end | justify","url":"https://developer.mozilla.org/docs/Web/CSS/box-pack"},"box-shadow":{"description":"Attaches one or more drop-shadows to the box. The property is a comma-separated list of shadows, each specified by 2-4 length values, an optional color, and an optional 'inset' keyword. Omitted lengths are 0; omitted colors are a user agent chosen color.","syntax":"none | <shadow>#","url":"https://developer.mozilla.org/docs/Web/CSS/box-shadow"},"box-sizing":{"description":"Specifies the behavior of the 'width' and 'height' properties.","syntax":"content-box | border-box","url":"https://developer.mozilla.org/docs/Web/CSS/box-sizing"},"break-after":{"description":"Describes the page/column/region break behavior after the generated box.","syntax":"auto | avoid | always | all | avoid-page | page | left | right | recto | verso | avoid-column | column | avoid-region | region","url":"https://developer.mozilla.org/docs/Web/CSS/break-after"},"break-before":{"description":"Describes the page/column/region break behavior before the generated box.","syntax":"auto | avoid | always | all | avoid-page | page | left | right | recto | verso | avoid-column | column | avoid-region | region","url":"https://developer.mozilla.org/docs/Web/CSS/break-before"},"break-inside":{"description":"Describes the page/column/region break behavior inside the principal box.","syntax":"auto | avoid | avoid-page | avoid-column | avoid-region","url":"https://developer.mozilla.org/docs/Web/CSS/break-inside"},"caption-side":{"description":"Specifies the position of the caption box with respect to the table box.","syntax":"top | bottom | block-start | block-end | inline-start | inline-end","url":"https://developer.mozilla.org/docs/Web/CSS/caption-side"},"caret":{"description":"Shorthand for setting caret-color and caret-shape.","syntax":"<'caret-color'> || <'caret-shape'>"},"caret-color":{"description":"Controls the color of the text insertion indicator.","syntax":"auto | <color>","url":"https://developer.mozilla.org/docs/Web/CSS/caret-color"},"caret-shape":{"description":"Specifies the desired shape of the text insertion caret.","syntax":"auto | bar | block | underscore"},"clear":{"description":"Indicates which sides of an element's box(es) may not be adjacent to an earlier floating box. The 'clear' property does not consider floats inside the element itself or in other block formatting contexts.","syntax":"none | left | right | both | inline-start | inline-end","url":"https://developer.mozilla.org/docs/Web/CSS/clear"},"clip":{"description":"Deprecated. Use the 'clip-path' property when support allows. Defines the visible portion of an element's box.","syntax":"<shape> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/clip"},"clip-path":{"description":"Specifies a clipping path where everything inside the path is visible and everything outside is clipped out.","syntax":"<clip-source> | [ <basic-shape> || <geometry-box> ] | none","url":"https://developer.mozilla.org/docs/Web/CSS/clip-path"},"clip-rule":{"description":"Indicates the algorithm which is to be used to determine what parts of the canvas are included inside the shape.","syntax":"nonzero | evenodd","url":"https://developer.mozilla.org/docs/Web/CSS/clip-rule"},"color":{"description":"Sets the color of an element's text","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/color"},"color-interpolation-filters":{"description":"Specifies the color space for imaging operations performed via filter effects.","syntax":"auto | sRGB | linearRGB","url":"https://developer.mozilla.org/docs/Web/CSS/color-interpolation-filters"},"color-scheme":{"description":"The color-scheme CSS property allows an element to indicate which color schemes it can comfortably be rendered in.","syntax":"normal | [ light | dark | <custom-ident> ]+ && only?","url":"https://developer.mozilla.org/docs/Web/CSS/color-scheme"},"column-count":{"description":"Describes the optimal number of columns into which the content of the element will be flowed.","syntax":"<integer> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/column-count"},"column-fill":{"description":"In continuous media, this property will only be consulted if the length of columns has been constrained. Otherwise, columns will automatically be balanced.","syntax":"auto | balance","url":"https://developer.mozilla.org/docs/Web/CSS/column-fill"},"column-gap":{"description":"Sets the gap between columns. If there is a column rule between columns, it will appear in the middle of the gap.","syntax":"normal | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/column-gap"},"column-rule":{"description":"Shorthand for setting 'column-rule-width', 'column-rule-style', and 'column-rule-color' at the same place in the style sheet. Omitted values are set to their initial values.","syntax":"<'column-rule-width'> || <'column-rule-style'> || <'column-rule-color'>","url":"https://developer.mozilla.org/docs/Web/CSS/column-rule"},"column-rule-color":{"description":"Sets the color of the column rule","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/column-rule-color"},"column-rule-style":{"description":"Sets the style of the rule between columns of an element.","syntax":"<'border-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/column-rule-style"},"column-rule-width":{"description":"Sets the width of the rule between columns. Negative values are not allowed.","syntax":"<'border-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/column-rule-width"},"column-span":{"description":"Describes the page/column break behavior after the generated box.","syntax":"none | all","url":"https://developer.mozilla.org/docs/Web/CSS/column-span"},"column-width":{"description":"Describes the width of columns in multicol elements.","syntax":"<length> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/column-width"},"columns":{"description":"A shorthand property which sets both 'column-width' and 'column-count'.","syntax":"<'column-width'> || <'column-count'>","url":"https://developer.mozilla.org/docs/Web/CSS/columns"},"contain":{"description":"Indicates that an element and its contents are, as much as possible, independent of the rest of the document tree.","syntax":"none | strict | content | [ [ size || inline-size ] || layout || style || paint ]","url":"https://developer.mozilla.org/docs/Web/CSS/contain"},"contain-intrinsic-block-size":{"description":"Block size of an element when the element is subject to size containment.","syntax":"auto? [ none | <length> ]","url":"https://developer.mozilla.org/docs/Web/CSS/contain-intrinsic-block-size"},"contain-intrinsic-height":{"description":"Height of an element when the element is subject to size containment.","syntax":"auto? [ none | <length> ]","url":"https://developer.mozilla.org/docs/Web/CSS/contain-intrinsic-height"},"contain-intrinsic-inline-size":{"description":"Inline size of an element when the element is subject to size containment.","syntax":"auto? [ none | <length> ]","url":"https://developer.mozilla.org/docs/Web/CSS/contain-intrinsic-inline-size"},"contain-intrinsic-size":{"description":"Size of an element when the element is subject to size containment.","syntax":"[ auto? [ none | <length> ] ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/contain-intrinsic-size"},"contain-intrinsic-width":{"description":"Width of an element when the element is subject to size containment.","syntax":"auto? [ none | <length> ]","url":"https://developer.mozilla.org/docs/Web/CSS/contain-intrinsic-width"},"container":{"description":"The container shorthand CSS property establishes the element as a query container and specifies the name or name for the containment context used in a container query.","syntax":"<'container-name'> [ / <'container-type'> ]?","url":"https://developer.mozilla.org/docs/Web/CSS/container"},"container-name":{"description":"The container-name CSS property specifies a list of query container names used by the @container at-rule in a container query.","syntax":"none | <custom-ident>+","url":"https://developer.mozilla.org/docs/Web/CSS/container-name"},"container-type":{"description":"The container-type CSS property is used to define the type of containment used in a container query.","syntax":"normal | size | inline-size","url":"https://developer.mozilla.org/docs/Web/CSS/container-type"},"content":{"description":"Determines which page-based occurrence of a given element is applied to a counter or string value.","syntax":"normal | none | [ <content-replacement> | <content-list> ] [/ [ <string> | <counter> ]+ ]?","url":"https://developer.mozilla.org/docs/Web/CSS/content"},"content-visibility":{"description":"Controls whether or not an element renders its contents at all, along with forcing a strong set of containments, allowing user agents to potentially omit large swathes of layout and rendering work until it becomes needed.","syntax":"visible | auto | hidden","url":"https://developer.mozilla.org/docs/Web/CSS/content-visibility"},"counter-increment":{"description":"Manipulate the value of existing counters.","syntax":"[ <counter-name> <integer>? ]+ | none","url":"https://developer.mozilla.org/docs/Web/CSS/counter-increment"},"counter-reset":{"description":"Property accepts one or more names of counters (identifiers), each one optionally followed by an integer. The integer gives the value that the counter is set to on each occurrence of the element.","syntax":"[ <counter-name> <integer>? | <reversed-counter-name> <integer>? ]+ | none","url":"https://developer.mozilla.org/docs/Web/CSS/counter-reset"},"counter-set":{"description":"The counter-set CSS property sets a CSS counter to a given value. It manipulates the value of existing counters, and will only create new counters if there isn't already a counter of the given name on the element.","syntax":"[ <counter-name> <integer>? ]+ | none","url":"https://developer.mozilla.org/docs/Web/CSS/counter-set"},"cursor":{"description":"Allows control over cursor appearance in an element","syntax":"[ [ <url> [ <x> <y> ]? , ]* [ auto | default | none | context-menu | help | pointer | progress | wait | cell | crosshair | text | vertical-text | alias | copy | move | no-drop | not-allowed | e-resize | n-resize | ne-resize | nw-resize | s-resize | se-resize | sw-resize | w-resize | ew-resize | ns-resize | nesw-resize | nwse-resize | col-resize | row-resize | all-scroll | zoom-in | zoom-out | grab | grabbing ] ]","url":"https://developer.mozilla.org/docs/Web/CSS/cursor"},"cx":{"description":"The cx CSS property defines the x-axis center point of an SVG circle or ellipse element. If present, it overrides the element's cx attribute.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/cx"},"cy":{"description":"The cy CSS property defines the y-axis center point of an SVG circle or ellipse elements. If present, it overrides the element's cy attribute.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/cy"},"d":{"description":"The d CSS property defines a path to be drawn by the SVG path element. If present, it overrides the element's d attribute.","syntax":"none | path(<string>)","url":"https://developer.mozilla.org/docs/Web/CSS/d"},"descent-override":{"description":"Describes the descent metric of a font.","syntax":"normal | <percentage>"},"direction":{"description":"Specifies the inline base direction or directionality of any bidi paragraph, embedding, isolate, or override established by the box. Note: for HTML content use the 'dir' attribute and 'bdo' element rather than this property.","syntax":"ltr | rtl","url":"https://developer.mozilla.org/docs/Web/CSS/direction"},"display":{"description":"In combination with 'float' and 'position', determines the type of box or boxes that are generated for an element.","syntax":"[ <display-outside> || <display-inside> ] | <display-listitem> | <display-internal> | <display-box> | <display-legacy>","url":"https://developer.mozilla.org/docs/Web/CSS/display"},"dominant-baseline":{"description":"The dominant-baseline CSS property specifies the specific baseline used to align the box's text and inline-level contents. It also indicates the default alignment baseline of any boxes participating in baseline alignment in the box's alignment context. If present, it overrides the shape's dominant-baseline attribute.","syntax":"auto | text-bottom | alphabetic | ideographic | middle | central | mathematical | hanging | text-top","url":"https://developer.mozilla.org/docs/Web/CSS/dominant-baseline"},"empty-cells":{"description":"In the separated borders model, this property controls the rendering of borders and backgrounds around cells that have no visible content.","syntax":"show | hide","url":"https://developer.mozilla.org/docs/Web/CSS/empty-cells"},"enable-background":{"description":"Deprecated. Use 'isolation' property instead when support allows. Specifies how the accumulation of the background image is managed."},"fallback":{"description":"@counter-style descriptor. Specifies a fallback counter style to be used when the current counter style can't create a representation for a given counter value.","syntax":"<counter-style-name>"},"field-sizing":{"description":"The field-sizing CSS property enables you to control the sizing behavior of elements that are given a default preferred size, such as form control elements. This property enables you to override the default sizing behavior, allowing form controls to adjust in size to fit their contents.","syntax":"content | fixed","url":"https://developer.mozilla.org/docs/Web/CSS/field-sizing"},"fill":{"description":"Paints the interior of the given graphical element.","syntax":"none | <color> | <url> [none | <color>]? | context-fill | context-stroke","url":"https://developer.mozilla.org/docs/Web/CSS/fill"},"fill-opacity":{"description":"Specifies the opacity of the painting operation used to paint the interior the current object.","syntax":"<alpha-value>","url":"https://developer.mozilla.org/docs/Web/CSS/fill-opacity"},"fill-rule":{"description":"Indicates the algorithm (or winding rule) which is to be used to determine what parts of the canvas are included inside the shape.","syntax":"nonzero | evenodd","url":"https://developer.mozilla.org/docs/Web/CSS/fill-rule"},"filter":{"description":"Processes an element's rendering before it is displayed in the document, by applying one or more filter effects.","syntax":"none | <filter-function-list>","url":"https://developer.mozilla.org/docs/Web/CSS/filter"},"flex":{"description":"Specifies the components of a flexible length: the flex grow factor and flex shrink factor, and the flex basis.","syntax":"none | [ <'flex-grow'> <'flex-shrink'>? || <'flex-basis'> ]","url":"https://developer.mozilla.org/docs/Web/CSS/flex"},"flex-basis":{"description":"Sets the flex basis.","syntax":"content | <'width'>","url":"https://developer.mozilla.org/docs/Web/CSS/flex-basis"},"flex-direction":{"description":"Specifies how flex items are placed in the flex container, by setting the direction of the flex container's main axis.","syntax":"row | row-reverse | column | column-reverse","url":"https://developer.mozilla.org/docs/Web/CSS/flex-direction"},"flex-flow":{"description":"Specifies how flexbox items are placed in the flexbox.","syntax":"<'flex-direction'> || <'flex-wrap'>","url":"https://developer.mozilla.org/docs/Web/CSS/flex-flow"},"flex-grow":{"description":"Sets the flex grow factor. Negative numbers are invalid.","syntax":"<number>","url":"https://developer.mozilla.org/docs/Web/CSS/flex-grow"},"flex-shrink":{"description":"Sets the flex shrink factor. Negative numbers are invalid.","syntax":"<number>","url":"https://developer.mozilla.org/docs/Web/CSS/flex-shrink"},"flex-wrap":{"description":"Controls whether the flex container is single-line or multi-line, and the direction of the cross-axis, which determines the direction new lines are stacked in.","syntax":"nowrap | wrap | wrap-reverse","url":"https://developer.mozilla.org/docs/Web/CSS/flex-wrap"},"float":{"description":"Specifies how a box should be floated. It may be set for any element, but only applies to elements that generate boxes that are not absolutely positioned.","syntax":"left | right | none | inline-start | inline-end","url":"https://developer.mozilla.org/docs/Web/CSS/float"},"flood-color":{"description":"Indicates what color to use to flood the current filter primitive subregion."},"flood-opacity":{"description":"Indicates what opacity to use to flood the current filter primitive subregion."},"font":{"description":"Shorthand property for setting 'font-style', 'font-variant', 'font-weight', 'font-size', 'line-height', and 'font-family', at the same place in the style sheet. The syntax of this property is based on a traditional typographical shorthand notation to set multiple properties related to fonts.","syntax":"[ [ <'font-style'> || <font-variant-css21> || <'font-weight'> || <'font-stretch'> ]? <'font-size'> [ / <'line-height'> ]? <'font-family'> ] | caption | icon | menu | message-box | small-caption | status-bar","url":"https://developer.mozilla.org/docs/Web/CSS/font"},"font-display":{"description":"The font-display descriptor determines how a font face is displayed based on whether and when it is downloaded and ready to use.","syntax":"[ auto | block | swap | fallback | optional ]"},"font-family":{"description":"Specifies a prioritized list of font family names or generic family names. A user agent iterates through the list of family names until it matches an available font that contains a glyph for the character to be rendered.","syntax":"<family-name>","url":"https://developer.mozilla.org/docs/Web/CSS/font-family"},"font-feature-settings":{"description":"Provides low-level control over OpenType font features. It is intended as a way of providing access to font features that are not widely used but are needed for a particular use case.","syntax":"normal | <feature-tag-value>#","url":"https://developer.mozilla.org/docs/Web/CSS/font-feature-settings"},"font-kerning":{"description":"Kerning is the contextual adjustment of inter-glyph spacing. This property controls metric kerning, kerning that utilizes adjustment data contained in the font.","syntax":"auto | normal | none","url":"https://developer.mozilla.org/docs/Web/CSS/font-kerning"},"font-language-override":{"description":"The value of 'normal' implies that when rendering with OpenType fonts the language of the document is used to infer the OpenType language system, used to select language specific features when rendering.","syntax":"normal | <string>","url":"https://developer.mozilla.org/docs/Web/CSS/font-language-override"},"font-optical-sizing":{"description":"The font-optical-sizing CSS property allows developers to control whether browsers render text with slightly differing visual representations to optimize viewing at different sizes, or not. This only works for fonts that have an optical size variation axis.","syntax":"auto | none","url":"https://developer.mozilla.org/docs/Web/CSS/font-optical-sizing"},"font-palette":{"description":"The font-palette CSS property allows specifying one of the many palettes contained in a font that a user agent should use for the font. Users can also override the values in a palette or create a new palette by using the @font-palette-values at-rule.","syntax":"normal | light | dark | <palette-identifier>","url":"https://developer.mozilla.org/docs/Web/CSS/font-palette"},"font-size":{"description":"Indicates the desired height of glyphs from the font. For scalable fonts, the font-size is a scale factor applied to the EM unit of the font. (Note that certain glyphs may bleed outside their EM box.) For non-scalable fonts, the font-size is converted into absolute units and matched against the declared font-size of the font, using the same absolute coordinate space for both of the matched values.","syntax":"<absolute-size> | <relative-size> | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/font-size"},"font-size-adjust":{"description":"Preserves the readability of text when font fallback occurs by adjusting the font-size so that the x-height is the same regardless of the font used.","syntax":"none | [ ex-height | cap-height | ch-width | ic-width | ic-height ]? [ from-font | <number> ]","url":"https://developer.mozilla.org/docs/Web/CSS/font-size-adjust"},"font-smooth":{"description":"The font-smooth CSS property controls the application of anti-aliasing when fonts are rendered.","syntax":"auto | never | always | <absolute-size> | <length>","url":"https://developer.mozilla.org/docs/Web/CSS/font-smooth"},"font-stretch":{"description":"Selects a normal, condensed, or expanded face from a font family.","syntax":"<font-stretch-absolute>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/font-stretch"},"font-style":{"description":"Allows italic or oblique faces to be selected. Italic forms are generally cursive in nature while oblique faces are typically sloped versions of the regular face.","syntax":"normal | italic | oblique <angle>{0,2}","url":"https://developer.mozilla.org/docs/Web/CSS/font-style"},"font-synthesis":{"description":"Controls whether user agents are allowed to synthesize bold or oblique font faces when a font family lacks bold or italic faces.","syntax":"none | [ weight || style || small-caps || position]","url":"https://developer.mozilla.org/docs/Web/CSS/font-synthesis"},"font-synthesis-position":{"description":"The font-synthesis-position CSS property lets you specify whether or not a browser may synthesize the subscript and superscript \"position\" typefaces when they are missing in a font family, while using font-variant-position to set the positions.","syntax":"auto | none","url":"https://developer.mozilla.org/docs/Web/CSS/font-synthesis-position"},"font-synthesis-small-caps":{"description":"The font-synthesis-small-caps CSS property lets you specify whether or not the browser may synthesize small-caps typeface when it is missing in a font family. Small-caps glyphs typically use the form of uppercase letters but are reduced to the size of lowercase letters.","syntax":"auto | none","url":"https://developer.mozilla.org/docs/Web/CSS/font-synthesis-small-caps"},"font-synthesis-style":{"description":"The font-synthesis-style CSS property lets you specify whether or not the browser may synthesize the oblique typeface when it is missing in a font family.","syntax":"auto | none","url":"https://developer.mozilla.org/docs/Web/CSS/font-synthesis-style"},"font-synthesis-weight":{"description":"The font-synthesis-weight CSS property lets you specify whether or not the browser may synthesize the bold typeface when it is missing in a font family.","syntax":"auto | none","url":"https://developer.mozilla.org/docs/Web/CSS/font-synthesis-weight"},"font-variant":{"description":"Specifies variant representations of the font","syntax":"normal | none | [ <common-lig-values> || <discretionary-lig-values> || <historical-lig-values> || <contextual-alt-values> || stylistic( <feature-value-name> ) || historical-forms || styleset( <feature-value-name># ) || character-variant( <feature-value-name># ) || swash( <feature-value-name> ) || ornaments( <feature-value-name> ) || annotation( <feature-value-name> ) || [ small-caps | all-small-caps | petite-caps | all-petite-caps | unicase | titling-caps ] || <numeric-figure-values> || <numeric-spacing-values> || <numeric-fraction-values> || ordinal || slashed-zero || <east-asian-variant-values> || <east-asian-width-values> || ruby ]","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant"},"font-variant-alternates":{"description":"For any given character, fonts can provide a variety of alternate glyphs in addition to the default glyph for that character. This property provides control over the selection of these alternate glyphs.","syntax":"normal | [ stylistic( <feature-value-name> ) || historical-forms || styleset( <feature-value-name># ) || character-variant( <feature-value-name># ) || swash( <feature-value-name> ) || ornaments( <feature-value-name> ) || annotation( <feature-value-name> ) ]","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant-alternates"},"font-variant-caps":{"description":"Specifies control over capitalized forms.","syntax":"normal | small-caps | all-small-caps | petite-caps | all-petite-caps | unicase | titling-caps","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant-caps"},"font-variant-east-asian":{"description":"Allows control of glyph substitute and positioning in East Asian text.","syntax":"normal | [ <east-asian-variant-values> || <east-asian-width-values> || ruby ]","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant-east-asian"},"font-variant-emoji":{"description":"The font-variant-emoji CSS property specifies the default presentation style for displaying emojis.","syntax":"normal | text | emoji | unicode","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant-emoji"},"font-variant-ligatures":{"description":"Specifies control over which ligatures are enabled or disabled. A value of 'normal' implies that the defaults set by the font are used.","syntax":"normal | none | [ <common-lig-values> || <discretionary-lig-values> || <historical-lig-values> || <contextual-alt-values> ]","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant-ligatures"},"font-variant-numeric":{"description":"Specifies control over numerical forms.","syntax":"normal | [ <numeric-figure-values> || <numeric-spacing-values> || <numeric-fraction-values> || ordinal || slashed-zero ]","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant-numeric"},"font-variant-position":{"description":"Specifies the vertical position","syntax":"normal | sub | super","url":"https://developer.mozilla.org/docs/Web/CSS/font-variant-position"},"font-variation-settings":{"description":"The font-variation-settings CSS property provides low-level control over OpenType or TrueType font variations, by specifying the four letter axis names of the features you want to vary, along with their variation values.","syntax":"normal | [ <string> <number> ]#","url":"https://developer.mozilla.org/docs/Web/CSS/font-variation-settings"},"font-weight":{"description":"Specifies weight of glyphs in the font, their degree of blackness or stroke thickness.","syntax":"<font-weight-absolute>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/font-weight"},"forced-color-adjust":{"description":"Allows authors to opt certain elements out of forced colors mode. This then restores the control of those values to CSS","syntax":"auto | none | preserve-parent-color","url":"https://developer.mozilla.org/docs/Web/CSS/forced-color-adjust"},"gap":{"description":"The gap CSS property is a shorthand property for row-gap and column-gap specifying the gutters between grid rows and columns.","syntax":"<'row-gap'> <'column-gap'>?","url":"https://developer.mozilla.org/docs/Web/CSS/gap"},"glyph-orientation-horizontal":{"description":"Controls glyph orientation when the inline-progression-direction is horizontal."},"glyph-orientation-vertical":{"description":"Controls glyph orientation when the inline-progression-direction is vertical."},"grid":{"description":"The grid CSS property is a shorthand property that sets all of the explicit grid properties ('grid-template-rows', 'grid-template-columns', and 'grid-template-areas'), and all the implicit grid properties ('grid-auto-rows', 'grid-auto-columns', and 'grid-auto-flow'), in a single declaration.","syntax":"<'grid-template'> | <'grid-template-rows'> / [ auto-flow && dense? ] <'grid-auto-columns'>? | [ auto-flow && dense? ] <'grid-auto-rows'>? / <'grid-template-columns'>","url":"https://developer.mozilla.org/docs/Web/CSS/grid"},"grid-area":{"description":"Determine a grid item's size and location within the grid by contributing a line, a span, or nothing (automatic) to its grid placement. Shorthand for 'grid-row-start', 'grid-column-start', 'grid-row-end', and 'grid-column-end'.","syntax":"<grid-line> [ / <grid-line> ]{0,3}","url":"https://developer.mozilla.org/docs/Web/CSS/grid-area"},"grid-auto-columns":{"description":"Specifies the size of implicitly created columns.","syntax":"<track-size>+","url":"https://developer.mozilla.org/docs/Web/CSS/grid-auto-columns"},"grid-auto-flow":{"description":"Controls how the auto-placement algorithm works, specifying exactly how auto-placed items get flowed into the grid.","syntax":"[ row | column ] || dense","url":"https://developer.mozilla.org/docs/Web/CSS/grid-auto-flow"},"grid-auto-rows":{"description":"Specifies the size of implicitly created rows.","syntax":"<track-size>+","url":"https://developer.mozilla.org/docs/Web/CSS/grid-auto-rows"},"grid-column":{"description":"Shorthand for 'grid-column-start' and 'grid-column-end'.","syntax":"<grid-line> [ / <grid-line> ]?","url":"https://developer.mozilla.org/docs/Web/CSS/grid-column"},"grid-column-end":{"description":"Determine a grid item's size and location within the grid by contributing a line, a span, or nothing (automatic) to its grid placement.","syntax":"<grid-line>","url":"https://developer.mozilla.org/docs/Web/CSS/grid-column-end"},"grid-column-gap":{"description":"Specifies the gutters between grid columns. Replaced by 'column-gap' property.","syntax":"<length-percentage>"},"grid-column-start":{"description":"Determine a grid item's size and location within the grid by contributing a line, a span, or nothing (automatic) to its grid placement.","syntax":"<grid-line>","url":"https://developer.mozilla.org/docs/Web/CSS/grid-column-start"},"grid-gap":{"description":"Shorthand that specifies the gutters between grid columns and grid rows in one declaration. Replaced by 'gap' property.","syntax":"<'grid-row-gap'> <'grid-column-gap'>?"},"grid-row":{"description":"Shorthand for 'grid-row-start' and 'grid-row-end'.","syntax":"<grid-line> [ / <grid-line> ]?","url":"https://developer.mozilla.org/docs/Web/CSS/grid-row"},"grid-row-end":{"description":"Determine a grid item's size and location within the grid by contributing a line, a span, or nothing (automatic) to its grid placement.","syntax":"<grid-line>","url":"https://developer.mozilla.org/docs/Web/CSS/grid-row-end"},"grid-row-gap":{"description":"Specifies the gutters between grid rows. Replaced by 'row-gap' property.","syntax":"<length-percentage>"},"grid-row-start":{"description":"Determine a grid item's size and location within the grid by contributing a line, a span, or nothing (automatic) to its grid placement.","syntax":"<grid-line>","url":"https://developer.mozilla.org/docs/Web/CSS/grid-row-start"},"grid-template":{"description":"Shorthand for setting grid-template-columns, grid-template-rows, and grid-template-areas in a single declaration.","syntax":"none | [ <'grid-template-rows'> / <'grid-template-columns'> ] | [ <line-names>? <string> <track-size>? <line-names>? ]+ [ / <explicit-track-list> ]?","url":"https://developer.mozilla.org/docs/Web/CSS/grid-template"},"grid-template-areas":{"description":"Specifies named grid areas, which are not associated with any particular grid item, but can be referenced from the grid-placement properties.","syntax":"none | <string>+","url":"https://developer.mozilla.org/docs/Web/CSS/grid-template-areas"},"grid-template-columns":{"description":"specifies, as a space-separated track list, the line names and track sizing functions of the grid.","syntax":"none | <track-list> | <auto-track-list> | subgrid <line-name-list>?","url":"https://developer.mozilla.org/docs/Web/CSS/grid-template-columns"},"grid-template-rows":{"description":"specifies, as a space-separated track list, the line names and track sizing functions of the grid.","syntax":"none | <track-list> | <auto-track-list> | subgrid <line-name-list>?","url":"https://developer.mozilla.org/docs/Web/CSS/grid-template-rows"},"hanging-punctuation":{"description":"The hanging-punctuation CSS property specifies whether a punctuation mark should hang at the start or end of a line of text. Hanging punctuation may be placed outside the line box.","syntax":"none | [ first || [ force-end | allow-end ] || last ]","url":"https://developer.mozilla.org/docs/Web/CSS/hanging-punctuation"},"height":{"description":"Specifies the height of the content area, padding area or border area (depending on 'box-sizing') of certain boxes.","syntax":"auto | <length> | <percentage> | min-content | max-content | fit-content | fit-content(<length-percentage>)","url":"https://developer.mozilla.org/docs/Web/CSS/height"},"hyphenate-character":{"description":"A hyphenate character used at the end of a line.","syntax":"auto | <string>","url":"https://developer.mozilla.org/docs/Web/CSS/hyphenate-character"},"hyphenate-limit-chars":{"description":"The hyphenate-limit-chars CSS property specifies the minimum word length to allow hyphenation of words as well as the minimum number of characters before and after the hyphen.","syntax":"[ auto | <integer> ]{1,3}","url":"https://developer.mozilla.org/docs/Web/CSS/hyphenate-limit-chars"},"hyphens":{"description":"Controls whether hyphenation is allowed to create more break opportunities within a line of text.","syntax":"none | manual | auto","url":"https://developer.mozilla.org/docs/Web/CSS/hyphens"},"image-orientation":{"description":"Specifies an orthogonal rotation to be applied to an image before it is laid out.","syntax":"from-image | <angle> | [ <angle>? flip ]","url":"https://developer.mozilla.org/docs/Web/CSS/image-orientation"},"image-rendering":{"description":"Provides a hint to the user-agent about what aspects of an image are most important to preserve when the image is scaled, to aid the user-agent in the choice of an appropriate scaling algorithm.","syntax":"auto | crisp-edges | pixelated","url":"https://developer.mozilla.org/docs/Web/CSS/image-rendering"},"image-resolution":{"description":"The image-resolution property specifies the intrinsic resolution of all raster images used in or on the element. It affects both content images (e.g. replaced elements and generated content) and decorative images (such as background-image). The intrinsic resolution of an image is used to determine the image\u2019s intrinsic dimensions.","syntax":"[ from-image || <resolution> ] && snap?"},"ime-mode":{"description":"Controls the state of the input method editor for text fields.","syntax":"auto | normal | active | inactive | disabled"},"inherits":{"description":"Specifies the inherit flag of the custom property registration represented by the @property rule, controlling whether or not the property inherits by default.","syntax":"true | false"},"initial-letter":{"description":"The initial-letter CSS property specifies styling for dropped, raised, and sunken initial letters.","syntax":"normal | [ <number> <integer>? ]","url":"https://developer.mozilla.org/docs/Web/CSS/initial-letter"},"initial-letter-align":{"description":"The initial-letter-align CSS property specifies the alignment of initial letters within a paragraph.","syntax":"[ auto | alphabetic | hanging | ideographic ]"},"initial-value":{"description":"Specifies the initial value of the custom property registration represented by the @property rule, controlling the property\u2019s initial value.","syntax":"<declaration-value>?"},"inline-size":{"description":"Size of an element in the direction specified by 'writing-mode'.","syntax":"<'width'>","url":"https://developer.mozilla.org/docs/Web/CSS/inline-size"},"input-security":{"description":"Enables or disables the obscuring a sensitive test input.","syntax":"auto | none"},"inset":{"description":"The inset CSS property defines the logical block and inline start and end offsets of an element, which map to physical offsets depending on the element's writing mode, directionality, and text orientation. It corresponds to the top and bottom, or right and left properties depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'top'>{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/inset"},"inset-block":{"description":"The inset-block CSS property defines the logical block start and end offsets of an element, which maps to physical offsets depending on the element's writing mode, directionality, and text orientation. It corresponds to the top and bottom, or right and left properties depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'top'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/inset-block"},"inset-block-end":{"description":"The inset-block-end CSS property defines the logical block end offset of an element, which maps to a physical offset depending on the element's writing mode, directionality, and text orientation. It corresponds to the top, right, bottom, or left property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'top'>","url":"https://developer.mozilla.org/docs/Web/CSS/inset-block-end"},"inset-block-start":{"description":"The inset-block-start CSS property defines the logical block start offset of an element, which maps to a physical offset depending on the element's writing mode, directionality, and text orientation. It corresponds to the top, right, bottom, or left property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'top'>","url":"https://developer.mozilla.org/docs/Web/CSS/inset-block-start"},"inset-inline":{"description":"The inset-inline CSS property defines the logical block start and end offsets of an element, which maps to physical offsets depending on the element's writing mode, directionality, and text orientation. It corresponds to the top and bottom, or right and left properties depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'top'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/inset-inline"},"inset-inline-end":{"description":"The inset-inline-end CSS property defines the logical inline end inset of an element, which maps to a physical inset depending on the element's writing mode, directionality, and text orientation. It corresponds to the top, right, bottom, or left property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'top'>","url":"https://developer.mozilla.org/docs/Web/CSS/inset-inline-end"},"inset-inline-start":{"description":"The inset-inline-start CSS property defines the logical inline start inset of an element, which maps to a physical offset depending on the element's writing mode, directionality, and text orientation. It corresponds to the top, right, bottom, or left property depending on the values defined for writing-mode, direction, and text-orientation.","syntax":"<'top'>","url":"https://developer.mozilla.org/docs/Web/CSS/inset-inline-start"},"interpolate-size":{"description":"The interpolate-size CSS property allows you to enable animations and transitions between a <length-percentage> value and an intrinsic size value such as auto, fit-content, or max-content.","syntax":"numeric-only | allow-keywords","url":"https://developer.mozilla.org/docs/Web/CSS/interpolate-size"},"isolation":{"description":"In CSS setting to 'isolate' will turn the element into a stacking context. In SVG, it defines whether an element is isolated or not.","syntax":"auto | isolate","url":"https://developer.mozilla.org/docs/Web/CSS/isolation"},"justify-content":{"description":"Aligns flex items along the main axis of the current line of the flex container.","syntax":"normal | <content-distribution> | <overflow-position>? [ <content-position> | left | right ]","url":"https://developer.mozilla.org/docs/Web/CSS/justify-content"},"justify-items":{"description":"Defines the default justify-self for all items of the box, giving them the default way of justifying each box along the appropriate axis","syntax":"normal | stretch | <baseline-position> | <overflow-position>? [ <self-position> | left | right ] | legacy | legacy && [ left | right | center ]","url":"https://developer.mozilla.org/docs/Web/CSS/justify-items"},"justify-self":{"description":"Defines the way of justifying a box inside its container along the appropriate axis.","syntax":"auto | normal | stretch | <baseline-position> | <overflow-position>? [ <self-position> | left | right ]","url":"https://developer.mozilla.org/docs/Web/CSS/justify-self"},"justify-tracks":{"description":"The justify-tracks CSS property sets the alignment in the masonry axis for grid containers that have masonry in their inline axis","syntax":"[ normal | <content-distribution> | <overflow-position>? [ <content-position> | left | right ] ]#"},"kerning":{"description":"Indicates whether the user agent should adjust inter-glyph spacing based on kerning tables that are included in the relevant font or instead disable auto-kerning and set inter-character spacing to a specific length."},"left":{"description":"Specifies how far an absolutely positioned box's left margin edge is offset to the right of the left edge of the box's 'containing block'.","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/left"},"letter-spacing":{"description":"Specifies the minimum, maximum, and optimal spacing between grapheme clusters.","syntax":"normal | <length>","url":"https://developer.mozilla.org/docs/Web/CSS/letter-spacing"},"lighting-color":{"description":"Defines the color of the light source for filter primitives 'feDiffuseLighting' and 'feSpecularLighting'."},"line-break":{"description":"Specifies what set of line breaking restrictions are in effect within the element.","syntax":"auto | loose | normal | strict | anywhere","url":"https://developer.mozilla.org/docs/Web/CSS/line-break"},"line-clamp":{"description":"The line-clamp property allows limiting the contents of a block container to the specified number of lines; remaining content is fragmented away and neither rendered nor measured. Optionally, it also allows inserting content into the last line box to indicate the continuity of truncated/interrupted content.","syntax":"none | <integer>","url":"https://developer.mozilla.org/docs/Web/CSS/-webkit-line-clamp"},"line-gap-override":{"description":"Describes the line-gap metric of a font.","syntax":"normal | <percentage>"},"line-height":{"description":"Determines the block-progression dimension of the text content area of an inline box.","syntax":"normal | <number> | <length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/line-height"},"line-height-step":{"description":"The line-height-step CSS property defines the step units for line box heights. When the step unit is positive, line box heights are rounded up to the closest multiple of the unit. Negative values are invalid.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/line-height-step"},"list-style":{"description":"Shorthand for setting 'list-style-type', 'list-style-position' and 'list-style-image'","syntax":"<'list-style-type'> || <'list-style-position'> || <'list-style-image'>","url":"https://developer.mozilla.org/docs/Web/CSS/list-style"},"list-style-image":{"description":"Sets the image that will be used as the list item marker. When the image is available, it will replace the marker set with the 'list-style-type' marker.","syntax":"<image> | none","url":"https://developer.mozilla.org/docs/Web/CSS/list-style-image"},"list-style-position":{"description":"Specifies the position of the '::marker' pseudo-element's box in the list item.","syntax":"inside | outside","url":"https://developer.mozilla.org/docs/Web/CSS/list-style-position"},"list-style-type":{"description":"Used to construct the default contents of a list item's marker","syntax":"<counter-style> | <string> | none","url":"https://developer.mozilla.org/docs/Web/CSS/list-style-type"},"margin":{"description":"Shorthand property to set values for the thickness of the margin area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. Negative values for margin properties are allowed, but there may be implementation-specific limits.","syntax":"[ <length> | <percentage> | auto ]{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/margin"},"margin-block":{"description":"The margin-block CSS property defines the logical block start and end margins of an element, which maps to physical margins depending on the element's writing mode, directionality, and text orientation.","syntax":"<'margin-left'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/margin-block"},"margin-block-end":{"description":"Logical 'margin-bottom'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'margin-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/margin-block-end"},"margin-block-start":{"description":"Logical 'margin-top'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'margin-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/margin-block-start"},"margin-bottom":{"description":"Shorthand property to set values for the thickness of the margin area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. Negative values for margin properties are allowed, but there may be implementation-specific limits..","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/margin-bottom"},"margin-inline":{"description":"The margin-inline CSS property defines the logical inline start and end margins of an element, which maps to physical margins depending on the element's writing mode, directionality, and text orientation.","syntax":"<'margin-left'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/margin-inline"},"margin-inline-end":{"description":"Logical 'margin-right'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'margin-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/margin-inline-end"},"margin-inline-start":{"description":"Logical 'margin-left'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'margin-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/margin-inline-start"},"margin-left":{"description":"Shorthand property to set values for the thickness of the margin area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. Negative values for margin properties are allowed, but there may be implementation-specific limits..","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/margin-left"},"margin-right":{"description":"Shorthand property to set values for the thickness of the margin area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. Negative values for margin properties are allowed, but there may be implementation-specific limits..","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/margin-right"},"margin-top":{"description":"Shorthand property to set values for the thickness of the margin area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. Negative values for margin properties are allowed, but there may be implementation-specific limits..","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/margin-top"},"margin-trim":{"description":"The margin-trim property allows the container to trim the margins of its children where they adjoin the container\u2019s edges.","syntax":"none | in-flow | all","url":"https://developer.mozilla.org/docs/Web/CSS/margin-trim"},"marker":{"description":"Specifies the marker symbol that shall be used for all points on the sets the value for all vertices on the given 'path' element or basic shape.","syntax":"none | <url>","url":"https://developer.mozilla.org/docs/Web/CSS/marker"},"marker-end":{"description":"Specifies the marker that will be drawn at the last vertices of the given markable element.","syntax":"none | <url>","url":"https://developer.mozilla.org/docs/Web/CSS/marker-end"},"marker-mid":{"description":"Specifies the marker that will be drawn at all vertices except the first and last.","syntax":"none | <url>","url":"https://developer.mozilla.org/docs/Web/CSS/marker-mid"},"marker-start":{"description":"Specifies the marker that will be drawn at the first vertices of the given markable element.","syntax":"none | <url>","url":"https://developer.mozilla.org/docs/Web/CSS/marker-start"},"marks":{"description":"The marks CSS at-rule descriptor, used with the @page at-rule, adds crop and/or cross marks to the presentation of the document. Crop marks indicate where the page should be cut. Cross marks are used to align sheets.","syntax":"none | [ crop || cross ]"},"mask":{"description":"The mask CSS property alters the visibility of an element by either partially or fully hiding it. This is accomplished by either masking or clipping the image at specific points.","syntax":"<mask-layer>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask"},"mask-border":{"description":"The mask-border CSS property lets you create a mask along the edge of an element's border.\n\nThis property is a shorthand for mask-border-source, mask-border-slice, mask-border-width, mask-border-outset, mask-border-repeat, and mask-border-mode. As with all shorthand properties, any omitted sub-values will be set to their initial value.","syntax":"<'mask-border-source'> || <'mask-border-slice'> [ / <'mask-border-width'>? [ / <'mask-border-outset'> ]? ]? || <'mask-border-repeat'> || <'mask-border-mode'>","url":"https://developer.mozilla.org/docs/Web/CSS/mask-border"},"mask-border-mode":{"description":"The mask-border-mode CSS property specifies the blending mode used in a mask border.","syntax":"luminance | alpha"},"mask-border-outset":{"description":"The mask-border-outset CSS property specifies the distance by which an element's mask border is set out from its border box.","syntax":"[ <length> | <number> ]{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/mask-border-outset"},"mask-border-repeat":{"description":"The mask-border-repeat CSS property defines how the edge regions of a source image are adjusted to fit the dimensions of an element's mask border.","syntax":"[ stretch | repeat | round | space ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/mask-border-repeat"},"mask-border-slice":{"description":"The mask-border-slice CSS property divides the image specified by mask-border-source into regions. These regions are used to form the components of an element's mask border.","syntax":"<number-percentage>{1,4} fill?","url":"https://developer.mozilla.org/docs/Web/CSS/mask-border-slice"},"mask-border-source":{"description":"The mask-border-source CSS property specifies the source image used to create an element's mask border.\n\nThe mask-border-slice property is used to divide the source image into regions, which are then dynamically applied to the final mask border.","syntax":"none | <image>","url":"https://developer.mozilla.org/docs/Web/CSS/mask-border-source"},"mask-border-width":{"description":"The mask-border-width CSS property specifies the width of an element's mask border.","syntax":"[ <length-percentage> | <number> | auto ]{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/mask-border-width"},"mask-clip":{"description":"The mask-clip CSS property determines the area, which is affected by a mask. The painted content of an element must be restricted to this area.","syntax":"[ <geometry-box> | no-clip ]#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-clip"},"mask-composite":{"description":"The mask-composite CSS property represents a compositing operation used on the current mask layer with the mask layers below it.","syntax":"<compositing-operator>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-composite"},"mask-image":{"description":"Sets the mask layer image of an element.","syntax":"<mask-reference>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-image"},"mask-mode":{"description":"Indicates whether the mask layer image is treated as luminance mask or alpha mask.","syntax":"<masking-mode>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-mode"},"mask-origin":{"description":"Specifies the mask positioning area.","syntax":"<geometry-box>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-origin"},"mask-position":{"description":"Specifies how mask layer images are positioned.","syntax":"<position>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-position"},"mask-repeat":{"description":"Specifies how mask layer images are tiled after they have been sized and positioned.","syntax":"<repeat-style>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-repeat"},"mask-size":{"description":"Specifies the size of the mask layer images.","syntax":"<bg-size>#","url":"https://developer.mozilla.org/docs/Web/CSS/mask-size"},"mask-type":{"description":"Defines whether the content of the <mask> element is treated as as luminance mask or alpha mask.","syntax":"luminance | alpha","url":"https://developer.mozilla.org/docs/Web/CSS/mask-type"},"masonry-auto-flow":{"description":"The masonry-auto-flow CSS property modifies how items are placed when using masonry in CSS Grid Layout.","syntax":"[ pack | next ] || [ definite-first | ordered ]"},"math-depth":{"description":"Describe a notion of \"depth\" for each element of a mathematical formula, with respect to the top-level container of that formula.","syntax":"auto-add | add(<integer>) | <integer>","url":"https://developer.mozilla.org/docs/Web/CSS/math-depth"},"math-shift":{"description":"Used for positioning superscript during the layout of MathML scripted elements.","syntax":"normal | compact","url":"https://developer.mozilla.org/docs/Web/CSS/math-shift"},"math-style":{"description":"The math-style property indicates whether MathML equations should render with normal or compact height.","syntax":"normal | compact","url":"https://developer.mozilla.org/docs/Web/CSS/math-style"},"max-block-size":{"description":"Maximum size of an element in the direction opposite that of the direction specified by 'writing-mode'.","syntax":"<'max-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/max-block-size"},"max-height":{"description":"Allows authors to constrain content height to a certain range.","syntax":"none | <length-percentage> | min-content | max-content | fit-content | fit-content(<length-percentage>)","url":"https://developer.mozilla.org/docs/Web/CSS/max-height"},"max-inline-size":{"description":"Maximum size of an element in the direction specified by 'writing-mode'.","syntax":"<'max-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/max-inline-size"},"max-lines":{"description":"The max-lines property forces a break after a set number of lines","syntax":"none | <integer>"},"max-width":{"description":"Allows authors to constrain content width to a certain range.","syntax":"none | <length-percentage> | min-content | max-content | fit-content | fit-content(<length-percentage>)","url":"https://developer.mozilla.org/docs/Web/CSS/max-width"},"min-block-size":{"description":"Minimal size of an element in the direction opposite that of the direction specified by 'writing-mode'.","syntax":"<'min-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/min-block-size"},"min-height":{"description":"Allows authors to constrain content height to a certain range.","syntax":"auto | <length> | <percentage> | min-content | max-content | fit-content | fit-content(<length-percentage>)","url":"https://developer.mozilla.org/docs/Web/CSS/min-height"},"min-inline-size":{"description":"Minimal size of an element in the direction specified by 'writing-mode'.","syntax":"<'min-width'>","url":"https://developer.mozilla.org/docs/Web/CSS/min-inline-size"},"min-width":{"description":"Allows authors to constrain content width to a certain range.","syntax":"auto | <length> | <percentage> | min-content | max-content | fit-content | fit-content(<length-percentage>)","url":"https://developer.mozilla.org/docs/Web/CSS/min-width"},"mix-blend-mode":{"description":"Defines the formula that must be used to mix the colors with the backdrop.","syntax":"<blend-mode> | plus-lighter","url":"https://developer.mozilla.org/docs/Web/CSS/mix-blend-mode"},"motion":{"description":"Shorthand property for setting 'motion-path', 'motion-offset' and 'motion-rotation'."},"motion-offset":{"description":"A distance that describes the position along the specified motion path."},"motion-path":{"description":"Specifies the motion path the element gets positioned at."},"motion-rotation":{"description":"Defines the direction of the element while positioning along the motion path."},"nav-down":{"description":"Provides an way to control directional focus navigation."},"nav-index":{"description":"Provides an input-method-neutral way of specifying the sequential navigation order (also known as 'tabbing order')."},"nav-left":{"description":"Provides an way to control directional focus navigation."},"nav-right":{"description":"Provides an way to control directional focus navigation."},"nav-up":{"description":"Provides an way to control directional focus navigation."},"negative":{"description":"@counter-style descriptor. Defines how to alter the representation when the counter value is negative.","syntax":"<symbol> <symbol>?"},"object-fit":{"description":"Specifies how the contents of a replaced element should be scaled relative to the box established by its used height and width.","syntax":"fill | contain | cover | none | scale-down","url":"https://developer.mozilla.org/docs/Web/CSS/object-fit"},"object-position":{"description":"Determines the alignment of the replaced element inside its box.","syntax":"<position>","url":"https://developer.mozilla.org/docs/Web/CSS/object-position"},"offset":{"description":"The offset CSS property is a shorthand property for animating an element along a defined path.","syntax":"[ <'offset-position'>? [ <'offset-path'> [ <'offset-distance'> || <'offset-rotate'> ]? ]? ]! [ / <'offset-anchor'> ]?","url":"https://developer.mozilla.org/docs/Web/CSS/offset"},"offset-anchor":{"description":"Defines an anchor point of the box positioned along the path. The anchor point specifies the point of the box which is to be considered as the point that is moved along the path.","syntax":"auto | <position>","url":"https://developer.mozilla.org/docs/Web/CSS/offset-anchor"},"offset-block-end":{"description":"Logical 'bottom'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'."},"offset-block-start":{"description":"Logical 'top'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'."},"offset-distance":{"description":"The offset-distance CSS property specifies a position along an offset-path.","syntax":"<length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/offset-distance"},"offset-inline-end":{"description":"Logical 'right'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'."},"offset-inline-start":{"description":"Logical 'left'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'."},"offset-path":{"description":"The offset-path CSS property specifies the offset path where the element gets positioned. The exact element\u2019s position on the offset path is determined by the offset-distance property. An offset path is either a specified path with one or multiple sub-paths or the geometry of a not-styled basic shape. Each shape or path must define an initial position for the computed value of \"0\" for offset-distance and an initial direction which specifies the rotation of the object to the initial position.\n\nIn this specification, a direction (or rotation) of 0 degrees is equivalent to the direction of the positive x-axis in the object\u2019s local coordinate system. In other words, a rotation of 0 degree points to the right side of the UA if the object and its ancestors have no transformation applied.","syntax":"none | <offset-path> || <coord-box>","url":"https://developer.mozilla.org/docs/Web/CSS/offset-path"},"offset-position":{"description":"Specifies the initial position of the offset path. If position is specified with static, offset-position would be ignored.","syntax":"normal | auto | <position>","url":"https://developer.mozilla.org/docs/Web/CSS/offset-position"},"offset-rotate":{"description":"The offset-rotate CSS property defines the direction of the element while positioning along the offset path.","syntax":"[ auto | reverse ] || <angle>","url":"https://developer.mozilla.org/docs/Web/CSS/offset-rotate"},"opacity":{"description":"Opacity of an element's text, where 1 is opaque and 0 is entirely transparent.","syntax":"<alpha-value>","url":"https://developer.mozilla.org/docs/Web/CSS/opacity"},"order":{"description":"Controls the order in which children of a flex container appear within the flex container, by assigning them to ordinal groups.","syntax":"<integer>","url":"https://developer.mozilla.org/docs/Web/CSS/order"},"orphans":{"description":"Specifies the minimum number of line boxes in a block container that must be left in a fragment before a fragmentation break.","syntax":"<integer>","url":"https://developer.mozilla.org/docs/Web/CSS/orphans"},"outline":{"description":"Shorthand property for 'outline-style', 'outline-width', and 'outline-color'.","syntax":"[ <'outline-width'> || <'outline-style'> || <'outline-color'> ]","url":"https://developer.mozilla.org/docs/Web/CSS/outline"},"outline-color":{"description":"The color of the outline.","syntax":"auto | <color>","url":"https://developer.mozilla.org/docs/Web/CSS/outline-color"},"outline-offset":{"description":"Offset the outline and draw it beyond the border edge.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/outline-offset"},"outline-style":{"description":"Style of the outline.","syntax":"auto | <'border-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/outline-style"},"outline-width":{"description":"Width of the outline.","syntax":"<line-width>","url":"https://developer.mozilla.org/docs/Web/CSS/outline-width"},"overflow":{"description":"Shorthand for setting 'overflow-x' and 'overflow-y'.","syntax":"[ visible | hidden | clip | scroll | auto ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/overflow"},"overflow-anchor":{"description":"The overflow-anchor CSS property provides a way to opt out browser scroll anchoring behavior which adjusts scroll position to minimize content shifts.","syntax":"auto | none","url":"https://developer.mozilla.org/docs/Web/CSS/overflow-anchor"},"overflow-block":{"description":"The overflow-block CSS media feature can be used to test how the output device handles content that overflows the initial containing block along the block axis.","syntax":"visible | hidden | clip | scroll | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overflow-block"},"overflow-clip-box":{"description":"The overflow-clip-box CSS property specifies relative to which box the clipping happens when there is an overflow. It is short hand for the overflow-clip-box-inline and overflow-clip-box-block properties.","syntax":"padding-box | content-box"},"overflow-clip-margin":{"description":"The overflow-clip-margin CSS property determines how far outside its bounds an element with overflow: clip may be painted before being clipped.","syntax":"<visual-box> || <length [0,\u221e]>","url":"https://developer.mozilla.org/docs/Web/CSS/overflow-clip-margin"},"overflow-inline":{"description":"The overflow-inline CSS media feature can be used to test how the output device handles content that overflows the initial containing block along the inline axis.","syntax":"visible | hidden | clip | scroll | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overflow-inline"},"overflow-wrap":{"description":"Specifies whether the UA may break within a word to prevent overflow when an otherwise-unbreakable string is too long to fit within the line box.","syntax":"normal | break-word | anywhere","url":"https://developer.mozilla.org/docs/Web/CSS/overflow-wrap"},"overflow-x":{"description":"Specifies the handling of overflow in the horizontal direction.","syntax":"visible | hidden | clip | scroll | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overflow-x"},"overflow-y":{"description":"Specifies the handling of overflow in the vertical direction.","syntax":"visible | hidden | clip | scroll | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overflow-y"},"overlay":{"description":"The overlay CSS property specifies whether an element appearing in the top layer (for example, a shown popover or modal <dialog> element) is actually rendered in the top layer. This property is only relevant within a list of transition-property values, and only if allow-discrete is set as the transition-behavior.","syntax":"none | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overlay"},"override-colors":{"description":"The override-colors CSS descriptor is used to override colors in the chosen base-palette for a color font.","syntax":"[ <integer [0,\u221e]> <absolute-color-base> ]#"},"overscroll-behavior":{"description":"The overscroll-behavior CSS property is shorthand for the overscroll-behavior-x and overscroll-behavior-y properties, which allow you to control the browser's scroll overflow behavior \u2014 what happens when the boundary of a scrolling area is reached.","syntax":"[ contain | none | auto ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/overscroll-behavior"},"overscroll-behavior-block":{"description":"The overscroll-behavior-block CSS property sets the browser's behavior when the block direction boundary of a scrolling area is reached.","syntax":"contain | none | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overscroll-behavior-block"},"overscroll-behavior-inline":{"description":"The overscroll-behavior-inline CSS property sets the browser's behavior when the inline direction boundary of a scrolling area is reached.","syntax":"contain | none | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overscroll-behavior-inline"},"overscroll-behavior-x":{"description":"The overscroll-behavior-x CSS property is allows you to control the browser's scroll overflow behavior \u2014 what happens when the boundary of a scrolling area is reached \u2014 in the x axis direction.","syntax":"contain | none | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overscroll-behavior-x"},"overscroll-behavior-y":{"description":"The overscroll-behavior-y CSS property is allows you to control the browser's scroll overflow behavior \u2014 what happens when the boundary of a scrolling area is reached \u2014 in the y axis direction.","syntax":"contain | none | auto","url":"https://developer.mozilla.org/docs/Web/CSS/overscroll-behavior-y"},"pad":{"description":"@counter-style descriptor. Specifies a \"fixed-width\" counter style, where representations shorter than the pad value are padded with a particular <symbol>","syntax":"<integer> && <symbol>"},"padding":{"description":"Shorthand property to set values for the thickness of the padding area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. The value may not be negative.","syntax":"[ <length> | <percentage> ]{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/padding"},"padding-block":{"description":"The padding-block CSS property defines the logical block start and end padding of an element, which maps to physical padding properties depending on the element's writing mode, directionality, and text orientation.","syntax":"<'padding-left'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/padding-block"},"padding-block-end":{"description":"Logical 'padding-bottom'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'padding-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-block-end"},"padding-block-start":{"description":"Logical 'padding-top'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'padding-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-block-start"},"padding-bottom":{"description":"Shorthand property to set values for the thickness of the padding area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. The value may not be negative.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-bottom"},"padding-inline":{"description":"The padding-inline CSS property defines the logical inline start and end padding of an element, which maps to physical padding properties depending on the element's writing mode, directionality, and text orientation.","syntax":"<'padding-left'>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/padding-inline"},"padding-inline-end":{"description":"Logical 'padding-right'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'padding-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-inline-end"},"padding-inline-start":{"description":"Logical 'padding-left'. Mapping depends on the parent element's 'writing-mode', 'direction', and 'text-orientation'.","syntax":"<'padding-left'>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-inline-start"},"padding-left":{"description":"Shorthand property to set values for the thickness of the padding area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. The value may not be negative.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-left"},"padding-right":{"description":"Shorthand property to set values for the thickness of the padding area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. The value may not be negative.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-right"},"padding-top":{"description":"Shorthand property to set values for the thickness of the padding area. If left is omitted, it is the same as right. If bottom is omitted it is the same as top, if right is omitted it is the same as top. The value may not be negative.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/padding-top"},"page":{"description":"The page CSS property is used to specify the named page, a specific type of page defined by the @page at-rule.","syntax":"auto | <custom-ident>","url":"https://developer.mozilla.org/docs/Web/CSS/page"},"page-break-after":{"description":"Defines rules for page breaks after an element.","syntax":"auto | always | avoid | left | right | recto | verso","url":"https://developer.mozilla.org/docs/Web/CSS/page-break-after"},"page-break-before":{"description":"Defines rules for page breaks before an element.","syntax":"auto | always | avoid | left | right | recto | verso","url":"https://developer.mozilla.org/docs/Web/CSS/page-break-before"},"page-break-inside":{"description":"Defines rules for page breaks inside an element.","syntax":"auto | avoid","url":"https://developer.mozilla.org/docs/Web/CSS/page-break-inside"},"page-orientation":{"description":"The page-orientation CSS descriptor for the @page at-rule controls the rotation of a printed page. It handles the flow of content across pages when the orientation of a page is changed. This behavior differs from the size descriptor in that a user can define the direction in which to rotate the page.","syntax":"upright | rotate-left | rotate-right "},"paint-order":{"description":"Controls the order that the three paint operations that shapes and text are rendered with: their fill, their stroke and any markers they might have.","syntax":"normal | [ fill || stroke || markers ]","url":"https://developer.mozilla.org/docs/Web/CSS/paint-order"},"perspective":{"description":"Applies the same transform as the perspective(<number>) transform function, except that it applies only to the positioned or transformed children of the element, not to the transform on the element itself.","syntax":"none | <length>","url":"https://developer.mozilla.org/docs/Web/CSS/perspective"},"perspective-origin":{"description":"Establishes the origin for the perspective property. It effectively sets the X and Y position at which the viewer appears to be looking at the children of the element.","syntax":"<position>","url":"https://developer.mozilla.org/docs/Web/CSS/perspective-origin"},"place-content":{"description":"The place-content CSS shorthand property sets both the align-content and justify-content properties.","syntax":"<'align-content'> <'justify-content'>?","url":"https://developer.mozilla.org/docs/Web/CSS/place-content"},"place-items":{"description":"The CSS place-items shorthand property sets both the align-items and justify-items properties. The first value is the align-items property value, the second the justify-items one. If the second value is not present, the first value is also used for it.","syntax":"<'align-items'> <'justify-items'>?","url":"https://developer.mozilla.org/docs/Web/CSS/place-items"},"place-self":{"description":"The place-self CSS property is a shorthand property sets both the align-self and justify-self properties. The first value is the align-self property value, the second the justify-self one. If the second value is not present, the first value is also used for it.","syntax":"<'align-self'> <'justify-self'>?","url":"https://developer.mozilla.org/docs/Web/CSS/place-self"},"pointer-events":{"description":"Specifies under what circumstances a given element can be the target element for a pointer event.","syntax":"auto | none | visiblePainted | visibleFill | visibleStroke | visible | painted | fill | stroke | all | inherit","url":"https://developer.mozilla.org/docs/Web/CSS/pointer-events"},"position":{"description":"The position CSS property sets how an element is positioned in a document. The top, right, bottom, and left properties determine the final location of positioned elements.","syntax":"static | relative | absolute | sticky | fixed","url":"https://developer.mozilla.org/docs/Web/CSS/position"},"position-anchor":{"description":"The position-anchor property defines the default anchor specifier for all anchor functions on the element, allowing multiple elements to use the same set of anchor functions (and position options lists!) while changing which anchor element each is referring to.","syntax":"auto | <anchor-name>","url":"https://developer.mozilla.org/docs/Web/CSS/position-anchor"},"position-area":{"description":"The position-area CSS property enables an anchor-positioned element to be positioned relative to the edges of its associated anchor element by placing the positioned element on one or more tiles of an implicit 3x3 grid, where the anchoring element is the center cell.","syntax":"none | <position-area>","url":"https://developer.mozilla.org/docs/Web/CSS/position-area"},"position-try":{"description":"This shorthand sets both position-try-options and position-try-order. If <'position-try-order'> is omitted, it\u2019s set to the property\u2019s initial value.","syntax":"<'position-try-order'>? <'position-try-fallbacks'>","url":"https://developer.mozilla.org/docs/Web/CSS/position-try"},"position-try-fallbacks":{"description":"The position-try-fallbacks CSS property enables you to specify a list of one or more alternative position try fallback options for anchor-positioned elements to be placed relative to their associated anchor elements. When the element would otherwise overflow its inset-modified containing block, the browser will try placing the positioned element in these different fallback positions, in the order provided, until it finds a value that stops it from overflowing its container or the viewport.","syntax":"none | [ [<dashed-ident> || <try-tactic>] | <'position-area'> ]#","url":"https://developer.mozilla.org/docs/Web/CSS/position-try-fallbacks"},"position-try-order":{"description":"This property specifies the order in which the position options list will be tried.","syntax":"normal | <try-size>","url":"https://developer.mozilla.org/docs/Web/CSS/position-try-order"},"position-visibility":{"description":"There are times when an element\u2019s anchors are not appropriate for positioning the element with, and it would be better to simply not display the element at all. position-visibility provides several conditions where this could be the case.","syntax":"always | [ anchors-valid || anchors-visible || no-overflow ]","url":"https://developer.mozilla.org/docs/Web/CSS/position-visibility"},"prefix":{"description":"@counter-style descriptor. Specifies a <symbol> that is prepended to the marker representation.","syntax":"<symbol>"},"print-color-adjust":{"description":"Defines what optimization the user agent is allowed to do when adjusting the appearance for an output device.","syntax":"economy | exact","url":"https://developer.mozilla.org/docs/Web/CSS/print-color-adjust"},"quotes":{"description":"Specifies quotation marks for any number of embedded quotations.","syntax":"none | auto | [ <string> <string> ]+","url":"https://developer.mozilla.org/docs/Web/CSS/quotes"},"r":{"description":"The r CSS property defines the radius of a circle. It can only be used with the SVG circle element. If present, it overrides the circle's r attribute.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/r"},"range":{"description":"@counter-style descriptor. Defines the ranges over which the counter style is defined.","syntax":"[ [ <integer> | infinite ]{2} ]# | auto"},"resize":{"description":"Specifies whether or not an element is resizable by the user, and if so, along which axis/axes.","syntax":"none | both | horizontal | vertical | block | inline","url":"https://developer.mozilla.org/docs/Web/CSS/resize"},"right":{"description":"Specifies how far an absolutely positioned box's right margin edge is offset to the left of the right edge of the box's 'containing block'.","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/right"},"rotate":{"description":"The rotate CSS property allows you to specify rotation transforms individually and independently of the transform property. This maps better to typical user interface usage, and saves having to remember the exact order of transform functions to specify in the transform value.","syntax":"none | <angle> | [ x | y | z | <number>{3} ] && <angle>","url":"https://developer.mozilla.org/docs/Web/CSS/rotate"},"row-gap":{"description":"The row-gap CSS property specifies the gutter between grid rows.","syntax":"normal | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/row-gap"},"ruby-align":{"description":"Specifies how text is distributed within the various ruby boxes when their contents do not exactly fill their respective boxes.","syntax":"start | center | space-between | space-around","url":"https://developer.mozilla.org/docs/Web/CSS/ruby-align"},"ruby-merge":{"description":"This property controls how ruby annotation boxes should be rendered when there are more than one in a ruby container box: whether each pair should be kept separate, the annotations should be collapsed and rendered as a group, or the separation should be determined based on the space available.","syntax":"separate | collapse | auto"},"ruby-overhang":{"description":"Determines whether, and on which side, ruby text is allowed to partially overhang any adjacent text in addition to its own base, when the ruby text is wider than the ruby base."},"ruby-position":{"description":"Used by the parent of elements with display: ruby-text to control the position of the ruby text with respect to its base.","syntax":"[ alternate || [ over | under ] ] | inter-character","url":"https://developer.mozilla.org/docs/Web/CSS/ruby-position"},"ruby-span":{"description":"Determines whether, and on which side, ruby text is allowed to partially overhang any adjacent text in addition to its own base, when the ruby text is wider than the ruby base."},"rx":{"description":"The rx CSS property defines the x-axis, or horizontal, radius of an SVG ellipse and the horizontal curve of the corners of an SVG rect rectangle. If present, it overrides the shape's rx attribute.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/rx"},"ry":{"description":"The ry CSS property defines the y-axis, or vertical, radius of an SVG ellipse and the vertical curve of the corners of an SVG rect rectangle. If present, it overrides the shape's ry attribute.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/ry"},"scale":{"description":"The scale CSS property allows you to specify scale transforms individually and independently of the transform property. This maps better to typical user interface usage, and saves having to remember the exact order of transform functions to specify in the transform value.","syntax":"none | [ <number> | <percentage> ]{1,3}","url":"https://developer.mozilla.org/docs/Web/CSS/scale"},"scroll-behavior":{"description":"Specifies the scrolling behavior for a scrolling box, when scrolling happens due to navigation or CSSOM scrolling APIs.","syntax":"auto | smooth","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-behavior"},"scroll-margin":{"description":"The scroll-margin property is a shorthand property which sets all of the scroll-margin longhands, assigning values much like the margin property does for the margin-* longhands.","syntax":"<length>{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin"},"scroll-margin-block":{"description":"The scroll-margin-block property is a shorthand property which sets the scroll-margin longhands in the block dimension.","syntax":"<length>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-block"},"scroll-margin-block-end":{"description":"The scroll-margin-block-end property defines the margin of the scroll snap area at the end of the block dimension that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-block-end"},"scroll-margin-block-start":{"description":"The scroll-margin-block-start property defines the margin of the scroll snap area at the start of the block dimension that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-block-start"},"scroll-margin-bottom":{"description":"The scroll-margin-bottom property defines the bottom margin of the scroll snap area that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-bottom"},"scroll-margin-inline":{"description":"The scroll-margin-inline property is a shorthand property which sets the scroll-margin longhands in the inline dimension.","syntax":"<length>{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-inline"},"scroll-margin-inline-end":{"description":"The scroll-margin-inline-end property defines the margin of the scroll snap area at the end of the inline dimension that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-inline-end"},"scroll-margin-inline-start":{"description":"The scroll-margin-inline-start property defines the margin of the scroll snap area at the start of the inline dimension that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-inline-start"},"scroll-margin-left":{"description":"The scroll-margin-left property defines the left margin of the scroll snap area that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-left"},"scroll-margin-right":{"description":"The scroll-margin-right property defines the right margin of the scroll snap area that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-right"},"scroll-margin-top":{"description":"The scroll-margin-top property defines the top margin of the scroll snap area that is used for snapping this box to the snapport. The scroll snap area is determined by taking the transformed border box, finding its rectangular bounding box (axis-aligned in the scroll container\u2019s coordinate space), then adding the specified outsets.","syntax":"<length>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-margin-top"},"scroll-padding":{"description":"The scroll-padding property is a shorthand property which sets all of the scroll-padding longhands, assigning values much like the padding property does for the padding-* longhands.","syntax":"[ auto | <length-percentage> ]{1,4}","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding"},"scroll-padding-block":{"description":"The scroll-padding-block property is a shorthand property which sets the scroll-padding longhands for the block dimension.","syntax":"[ auto | <length-percentage> ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-block"},"scroll-padding-block-end":{"description":"The scroll-padding-block-end property defines offsets for the end edge in the block dimension of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-block-end"},"scroll-padding-block-start":{"description":"The scroll-padding-block-start property defines offsets for the start edge in the block dimension of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-block-start"},"scroll-padding-bottom":{"description":"The scroll-padding-bottom property defines offsets for the bottom of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-bottom"},"scroll-padding-inline":{"description":"The scroll-padding-inline property is a shorthand property which sets the scroll-padding longhands for the inline dimension.","syntax":"[ auto | <length-percentage> ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-inline"},"scroll-padding-inline-end":{"description":"The scroll-padding-inline-end property defines offsets for the end edge in the inline dimension of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-inline-end"},"scroll-padding-inline-start":{"description":"The scroll-padding-inline-start property defines offsets for the start edge in the inline dimension of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-inline-start"},"scroll-padding-left":{"description":"The scroll-padding-left property defines offsets for the left of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-left"},"scroll-padding-right":{"description":"The scroll-padding-right property defines offsets for the right of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-right"},"scroll-padding-top":{"description":"The scroll-padding-top property defines offsets for the top of the optimal viewing region of the scrollport: the region used as the target region for placing things in view of the user. This allows the author to exclude regions of the scrollport that are obscured by other content (such as fixed-positioned toolbars or sidebars) or simply to put more breathing room between a targeted element and the edges of the scrollport.","syntax":"auto | <length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-padding-top"},"scroll-snap-align":{"description":"The scroll-snap-align property specifies the box\u2019s snap position as an alignment of its snap area (as the alignment subject) within its snap container\u2019s snapport (as the alignment container). The two values specify the snapping alignment in the block axis and inline axis, respectively. If only one value is specified, the second value defaults to the same value.","syntax":"[ none | start | end | center ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-snap-align"},"scroll-snap-coordinate":{"description":"Defines the x and y coordinate within the element which will align with the nearest ancestor scroll container's snap-destination for the respective axis.","syntax":"none | <position>#"},"scroll-snap-destination":{"description":"Define the x and y coordinate within the scroll container's visual viewport which element snap points will align with.","syntax":"<position>"},"scroll-snap-points-x":{"description":"Defines the positioning of snap points along the x axis of the scroll container it is applied to.","syntax":"none | repeat( <length-percentage> )"},"scroll-snap-points-y":{"description":"Defines the positioning of snap points along the y axis of the scroll container it is applied to.","syntax":"none | repeat( <length-percentage> )"},"scroll-snap-stop":{"description":"The scroll-snap-stop CSS property defines whether the scroll container is allowed to \"pass over\" possible snap positions.","syntax":"normal | always","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-snap-stop"},"scroll-snap-type":{"description":"Defines how strictly snap points are enforced on the scroll container.","syntax":"none | [ x | y | block | inline | both ] [ mandatory | proximity ]?","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-snap-type"},"scroll-snap-type-x":{"description":"The scroll-snap-type-x CSS property defines how strictly snap points are enforced on the horizontal axis of the scroll container in case there is one.\n\nSpecifying any precise animations or physics used to enforce those snap points is not covered by this property but instead left up to the user agent.","syntax":"none | mandatory | proximity"},"scroll-snap-type-y":{"description":"The scroll-snap-type-y CSS property defines how strictly snap points are enforced on the vertical axis of the scroll container in case there is one.\n\nSpecifying any precise animations or physics used to enforce those snap points is not covered by this property but instead left up to the user agent.","syntax":"none | mandatory | proximity"},"scroll-timeline":{"description":"Defines a name that can be used to identify the source element of a scroll timeline, along with the scrollbar axis that should provide the timeline.","syntax":"[ <'scroll-timeline-name'> <'scroll-timeline-axis'>? ]#","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-timeline"},"scroll-timeline-axis":{"description":"Specifies the scrollbar that will be used to provide the timeline for a scroll-timeline animation","syntax":"[ block | inline | x | y ]#","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-timeline-axis"},"scroll-timeline-name":{"description":"Defines a name that can be used to identify an element as the source of a scroll-timeline.","syntax":"none | <dashed-ident>#","url":"https://developer.mozilla.org/docs/Web/CSS/scroll-timeline-name"},"scrollbar-3dlight-color":{"description":"Determines the color of the top and left edges of the scroll box and scroll arrows of a scroll bar."},"scrollbar-arrow-color":{"description":"Determines the color of the arrow elements of a scroll arrow."},"scrollbar-base-color":{"description":"Determines the color of the main elements of a scroll bar, which include the scroll box, track, and scroll arrows."},"scrollbar-color":{"description":"The scrollbar-color CSS property sets the color of the scrollbar track and thumb.","syntax":"auto | <color>{2}","url":"https://developer.mozilla.org/docs/Web/CSS/scrollbar-color"},"scrollbar-darkshadow-color":{"description":"Determines the color of the gutter of a scroll bar."},"scrollbar-face-color":{"description":"Determines the color of the scroll box and scroll arrows of a scroll bar."},"scrollbar-gutter":{"description":"The scrollbar-gutter CSS property allows authors to reserve space for the scrollbar, preventing unwanted layout changes as the content grows while also avoiding unnecessary visuals when scrolling isn't needed.","syntax":"auto | stable && both-edges?","url":"https://developer.mozilla.org/docs/Web/CSS/scrollbar-gutter"},"scrollbar-highlight-color":{"description":"Determines the color of the top and left edges of the scroll box and scroll arrows of a scroll bar."},"scrollbar-shadow-color":{"description":"Determines the color of the bottom and right edges of the scroll box and scroll arrows of a scroll bar."},"scrollbar-track-color":{"description":"Determines the color of the track element of a scroll bar."},"scrollbar-width":{"description":"The scrollbar-width property allows the author to set the maximum thickness of an element\u2019s scrollbars when they are shown. ","syntax":"auto | thin | none","url":"https://developer.mozilla.org/docs/Web/CSS/scrollbar-width"},"shape-image-threshold":{"description":"Defines the alpha channel threshold used to extract the shape using an image. A value of 0.5 means that the shape will enclose all the pixels that are more than 50% opaque.","syntax":"<alpha-value>","url":"https://developer.mozilla.org/docs/Web/CSS/shape-image-threshold"},"shape-margin":{"description":"Adds a margin to a 'shape-outside'. This defines a new shape that is the smallest contour that includes all the points that are the 'shape-margin' distance outward in the perpendicular direction from a point on the underlying shape.","syntax":"<length-percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/shape-margin"},"shape-outside":{"description":"Defines a shape around which adjacent inline content should wrap.","syntax":"none | [ <shape-box> || <basic-shape> ] | <image>","url":"https://developer.mozilla.org/docs/Web/CSS/shape-outside"},"shape-rendering":{"description":"Provides hints about what tradeoffs to make as it renders vector graphics elements such as <path> elements and basic shapes such as circles and rectangles.","syntax":"auto | optimizeSpeed | crispEdges | geometricPrecision","url":"https://developer.mozilla.org/docs/Web/CSS/shape-rendering"},"size":{"description":"The size CSS at-rule descriptor, used with the @page at-rule, defines the size and orientation of the box which is used to represent a page. Most of the time, this size corresponds to the target size of the printed page if applicable.","syntax":"<length>{1,2} | auto | [ <page-size> || [ portrait | landscape ] ]"},"size-adjust":{"description":"A multiplier for glyph outlines and metrics of a font.","syntax":"<percentage>"},"speak-as":{"description":"The speak-as descriptor specifies how a counter symbol constructed with a given @counter-style will be represented in the spoken form. For example, an author can specify a counter symbol to be either spoken as its numerical value or just represented with an audio cue.","syntax":"auto | bullets | numbers | words | spell-out | <counter-style-name>"},"src":{"description":"@font-face descriptor. Specifies the resource containing font data. It is required, whether the font is downloadable or locally installed.","syntax":"[ <url> [ format( <string># ) ]? | local( <family-name> ) ]#"},"stop-color":{"description":"Indicates what color to use at that gradient stop.","url":"https://developer.mozilla.org/docs/Web/CSS/stop-color"},"stop-opacity":{"description":"Defines the opacity of a given gradient stop.","url":"https://developer.mozilla.org/docs/Web/CSS/stop-opacity"},"stroke":{"description":"Paints along the outline of the given graphical element.","url":"https://developer.mozilla.org/docs/Web/CSS/stroke"},"stroke-dasharray":{"description":"Controls the pattern of dashes and gaps used to stroke paths.","syntax":"none | <dasharray>","url":"https://developer.mozilla.org/docs/Web/CSS/stroke-dasharray"},"stroke-dashoffset":{"description":"Specifies the distance into the dash pattern to start the dash.","syntax":"<length-percentage> | <number>","url":"https://developer.mozilla.org/docs/Web/CSS/stroke-dashoffset"},"stroke-linecap":{"description":"Specifies the shape to be used at the end of open subpaths when they are stroked.","syntax":"butt | round | square","url":"https://developer.mozilla.org/docs/Web/CSS/stroke-linecap"},"stroke-linejoin":{"description":"Specifies the shape to be used at the corners of paths or basic shapes when they are stroked.","syntax":"miter | miter-clip | round | bevel | arcs","url":"https://developer.mozilla.org/docs/Web/CSS/stroke-linejoin"},"stroke-miterlimit":{"description":"When two line segments meet at a sharp angle and miter joins have been specified for 'stroke-linejoin', it is possible for the miter to extend far beyond the thickness of the line stroking the path.","syntax":"<number>","url":"https://developer.mozilla.org/docs/Web/CSS/stroke-miterlimit"},"stroke-opacity":{"description":"Specifies the opacity of the painting operation used to stroke the current object.","syntax":"<'opacity'>","url":"https://developer.mozilla.org/docs/Web/CSS/stroke-opacity"},"stroke-width":{"description":"Specifies the width of the stroke on the current object.","syntax":"<length-percentage> | <number>","url":"https://developer.mozilla.org/docs/Web/CSS/stroke-width"},"suffix":{"description":"@counter-style descriptor. Specifies a <symbol> that is appended to the marker representation.","syntax":"<symbol>"},"symbols":{"description":"@counter-style descriptor. Specifies the symbols used by the marker-construction algorithm specified by the system descriptor.","syntax":"<symbol>+"},"syntax":{"description":"Specifies the syntax of the custom property registration represented by the @property rule, controlling how the property\u2019s value is parsed at computed value time.","syntax":"<string>"},"system":{"description":"@counter-style descriptor. Specifies which algorithm will be used to construct the counter's representation based on the counter value.","syntax":"cyclic | numeric | alphabetic | symbolic | additive | [ fixed <integer>? ] | [ extends <counter-style-name> ]"},"tab-size":{"description":"Determines the width of the tab character (U+0009), in space characters (U+0020), when rendered.","syntax":"<integer> | <length>","url":"https://developer.mozilla.org/docs/Web/CSS/tab-size"},"table-layout":{"description":"Controls the algorithm used to lay out the table cells, rows, and columns.","syntax":"auto | fixed","url":"https://developer.mozilla.org/docs/Web/CSS/table-layout"},"text-align":{"description":"Describes how inline contents of a block are horizontally aligned if the contents do not completely fill the line box.","syntax":"start | end | left | right | center | justify | match-parent","url":"https://developer.mozilla.org/docs/Web/CSS/text-align"},"text-align-last":{"description":"Describes how the last line of a block or a line right before a forced line break is aligned when 'text-align' is set to 'justify'.","syntax":"auto | start | end | left | right | center | justify | match-parent","url":"https://developer.mozilla.org/docs/Web/CSS/text-align-last"},"text-anchor":{"description":"Used to align (start-, middle- or end-alignment) a string of text relative to a given point.","syntax":"start | middle | end","url":"https://developer.mozilla.org/docs/Web/CSS/text-anchor"},"text-combine-upright":{"description":"The text-combine-upright CSS property specifies the combination of multiple characters into the space of a single character. If the combined text is wider than 1em, the user agent must fit the contents within 1em. The resulting composition is treated as a single upright glyph for layout and decoration. This property only has an effect in vertical writing modes.\n\nThis is used to produce an effect that is known as tate-ch\u016b-yoko (\u7e26\u4e2d\u6a2a) in Japanese, or as \u76f4\u66f8\u6a6b\u5411 in Chinese.","syntax":"none | all | [ digits <integer>? ]","url":"https://developer.mozilla.org/docs/Web/CSS/text-combine-upright"},"text-decoration":{"description":"Decorations applied to font used for an element's text.","syntax":"<'text-decoration-line'> || <'text-decoration-style'> || <'text-decoration-color'> || <'text-decoration-thickness'>","url":"https://developer.mozilla.org/docs/Web/CSS/text-decoration"},"text-decoration-color":{"description":"Specifies the color of text decoration (underlines overlines, and line-throughs) set on the element with text-decoration-line.","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/text-decoration-color"},"text-decoration-line":{"description":"Specifies what line decorations, if any, are added to the element.","syntax":"none | [ underline || overline || line-through || blink ] | spelling-error | grammar-error","url":"https://developer.mozilla.org/docs/Web/CSS/text-decoration-line"},"text-decoration-skip":{"description":"The text-decoration-skip CSS property specifies what parts of the element\u2019s content any text decoration affecting the element must skip over. It controls all text decoration lines drawn by the element and also any text decoration lines drawn by its ancestors.","syntax":"none | [ objects || [ spaces | [ leading-spaces || trailing-spaces ] ] || edges || box-decoration ]","url":"https://developer.mozilla.org/docs/Web/CSS/text-decoration-skip"},"text-decoration-skip-ink":{"description":"The text-decoration-skip-ink CSS property specifies how overlines and underlines are drawn when they pass over glyph ascenders and descenders.","syntax":"auto | all | none","url":"https://developer.mozilla.org/docs/Web/CSS/text-decoration-skip-ink"},"text-decoration-style":{"description":"Specifies the line style for underline, line-through and overline text decoration.","syntax":"solid | double | dotted | dashed | wavy","url":"https://developer.mozilla.org/docs/Web/CSS/text-decoration-style"},"text-decoration-thickness":{"description":"The text-decoration-thickness CSS property sets the thickness, or width, of the decoration line that is used on text in an element, such as a line-through, underline, or overline.","syntax":"auto | from-font | <length> | <percentage> ","url":"https://developer.mozilla.org/docs/Web/CSS/text-decoration-thickness"},"text-emphasis":{"description":"The text-emphasis CSS property is a shorthand property for setting text-emphasis-style and text-emphasis-color in one declaration. This property will apply the specified emphasis mark to each character of the element's text, except separator characters, like spaces,  and control characters.","syntax":"<'text-emphasis-style'> || <'text-emphasis-color'>","url":"https://developer.mozilla.org/docs/Web/CSS/text-emphasis"},"text-emphasis-color":{"description":"The text-emphasis-color CSS property defines the color used to draw emphasis marks on text being rendered in the HTML document. This value can also be set and reset using the text-emphasis shorthand.","syntax":"<color>","url":"https://developer.mozilla.org/docs/Web/CSS/text-emphasis-color"},"text-emphasis-position":{"description":"The text-emphasis-position CSS property describes where emphasis marks are drawn at. The effect of emphasis marks on the line height is the same as for ruby text: if there isn't enough place, the line height is increased.","syntax":"auto | [ over | under ] && [ right | left ]?","url":"https://developer.mozilla.org/docs/Web/CSS/text-emphasis-position"},"text-emphasis-style":{"description":"The text-emphasis-style CSS property defines the type of emphasis used. It can also be set, and reset, using the text-emphasis shorthand.","syntax":"none | [ [ filled | open ] || [ dot | circle | double-circle | triangle | sesame ] ] | <string>","url":"https://developer.mozilla.org/docs/Web/CSS/text-emphasis-style"},"text-indent":{"description":"Specifies the indentation applied to lines of inline content in a block. The indentation only affects the first line of inline content in the block unless the 'hanging' keyword is specified, in which case it affects all lines except the first.","syntax":"<length-percentage> && hanging? && each-line?","url":"https://developer.mozilla.org/docs/Web/CSS/text-indent"},"text-justify":{"description":"Selects the justification algorithm used when 'text-align' is set to 'justify'. The property applies to block containers, but the UA may (but is not required to) also support it on inline elements.","syntax":"auto | inter-character | inter-word | none","url":"https://developer.mozilla.org/docs/Web/CSS/text-justify"},"text-orientation":{"description":"Specifies the orientation of text within a line.","syntax":"mixed | upright | sideways","url":"https://developer.mozilla.org/docs/Web/CSS/text-orientation"},"text-overflow":{"description":"Text can overflow for example when it is prevented from wrapping.","syntax":"[ clip | ellipsis | <string> ]{1,2}","url":"https://developer.mozilla.org/docs/Web/CSS/text-overflow"},"text-rendering":{"description":"The creator of SVG content might want to provide a hint to the implementation about what tradeoffs to make as it renders text. The 'text-rendering' property provides these hints.","syntax":"auto | optimizeSpeed | optimizeLegibility | geometricPrecision","url":"https://developer.mozilla.org/docs/Web/CSS/text-rendering"},"text-shadow":{"description":"Enables shadow effects to be applied to the text of the element.","syntax":"none | <shadow-t>#","url":"https://developer.mozilla.org/docs/Web/CSS/text-shadow"},"text-size-adjust":{"description":"The text-size-adjust CSS property controls the text inflation algorithm used on some smartphones and tablets. Other browsers will ignore this property.","syntax":"none | auto | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/text-size-adjust"},"text-spacing-trim":{"description":"The text-spacing-trim CSS property controls the internal spacing set on Chinese/Japanese/Korean (CJK) punctuation characters between adjacent characters (kerning) and at the start or end of text lines.","syntax":"space-all | normal | space-first | trim-start | trim-both | trim-all | auto","url":"https://developer.mozilla.org/docs/Web/CSS/text-spacing-trim"},"text-transform":{"description":"Controls capitalization effects of an element's text.","syntax":"none | capitalize | uppercase | lowercase | full-width | full-size-kana","url":"https://developer.mozilla.org/docs/Web/CSS/text-transform"},"text-underline-offset":{"description":"The text-underline-offset CSS property sets the offset distance of an underline text decoration line (applied using text-decoration) from its original position.","syntax":"auto | <length> | <percentage> ","url":"https://developer.mozilla.org/docs/Web/CSS/text-underline-offset"},"text-underline-position":{"description":"Sets the position of an underline specified on the same element: it does not affect underlines specified by ancestor elements. This property is typically used in vertical writing contexts such as in Japanese documents where it often desired to have the underline appear 'over' (to the right of) the affected run of text","syntax":"auto | from-font | [ under || [ left | right ] ]","url":"https://developer.mozilla.org/docs/Web/CSS/text-underline-position"},"text-wrap":{"description":"The text-wrap CSS property controls how text inside an element is wrapped.","syntax":"<'text-wrap-mode> || <'text-wrap-style'>","url":"https://developer.mozilla.org/docs/Web/CSS/text-wrap"},"text-wrap-mode":{"description":"The text-wrap-mode CSS property controls whether the text inside an element is wrapped. The different values provide alternate ways of wrapping the content of a block element. It can also be set, and reset, using the text-wrap shorthand.","syntax":"auto | wrap | nowrap","url":"https://developer.mozilla.org/docs/Web/CSS/text-wrap-mode"},"text-wrap-style":{"description":"The text-wrap-style CSS property controls how text inside an element is wrapped. The different values provide alternate ways of wrapping the content of a block element. It can also be set, and reset, using the text-wrap shorthand.","syntax":"auto | balance | stable | pretty","url":"https://developer.mozilla.org/docs/Web/CSS/text-wrap-style"},"timeline-scope":{"description":"The timeline-scope CSS property modifies the scope of a named animation timeline.","syntax":"none | <dashed-ident>#","url":"https://developer.mozilla.org/docs/Web/CSS/timeline-scope"},"top":{"description":"Specifies how far an absolutely positioned box's top margin edge is offset below the top edge of the box's 'containing block'.","syntax":"<length> | <percentage> | auto","url":"https://developer.mozilla.org/docs/Web/CSS/top"},"touch-action":{"description":"Determines whether touch input may trigger default behavior supplied by user agent.","syntax":"auto | none | [ [ pan-x | pan-left | pan-right ] || [ pan-y | pan-up | pan-down ] || pinch-zoom ] | manipulation","url":"https://developer.mozilla.org/docs/Web/CSS/touch-action"},"transform":{"description":"A two-dimensional transformation is applied to an element through the 'transform' property. This property contains a list of transform functions similar to those allowed by SVG.","syntax":"none | <transform-list>","url":"https://developer.mozilla.org/docs/Web/CSS/transform"},"transform-box":{"description":"The transform-box CSS property defines the layout box to which the transform and transform-origin properties relate.","syntax":"content-box | border-box | fill-box | stroke-box | view-box","url":"https://developer.mozilla.org/docs/Web/CSS/transform-box"},"transform-origin":{"description":"Establishes the origin of transformation for an element.","syntax":"[ <length-percentage> | left | center | right | top | bottom ] | [ [ <length-percentage> | left | center | right ] && [ <length-percentage> | top | center | bottom ] ] <length>?","url":"https://developer.mozilla.org/docs/Web/CSS/transform-origin"},"transform-style":{"description":"Defines how nested elements are rendered in 3D space.","syntax":"flat | preserve-3d","url":"https://developer.mozilla.org/docs/Web/CSS/transform-style"},"transition":{"description":"Shorthand property combines four of the transition properties into a single property.","syntax":"<single-transition>#","url":"https://developer.mozilla.org/docs/Web/CSS/transition"},"transition-behavior":{"description":"The transition-behavior CSS property specifies whether transitions will be started for properties whose animation behavior is discrete.","syntax":"<transition-behavior-value>#","url":"https://developer.mozilla.org/docs/Web/CSS/transition-behavior"},"transition-delay":{"description":"Defines when the transition will start. It allows a transition to begin execution some period of time from when it is applied.","syntax":"<time>#","url":"https://developer.mozilla.org/docs/Web/CSS/transition-delay"},"transition-duration":{"description":"Specifies how long the transition from the old value to the new value should take.","syntax":"<time>#","url":"https://developer.mozilla.org/docs/Web/CSS/transition-duration"},"transition-property":{"description":"Specifies the name of the CSS property to which the transition is applied.","syntax":"none | <single-transition-property>#","url":"https://developer.mozilla.org/docs/Web/CSS/transition-property"},"transition-timing-function":{"description":"Describes how the intermediate values used during a transition will be calculated.","syntax":"<easing-function>#","url":"https://developer.mozilla.org/docs/Web/CSS/transition-timing-function"},"translate":{"description":"The translate CSS property allows you to specify translation transforms individually and independently of the transform property. This maps better to typical user interface usage, and saves having to remember the exact order of transform functions to specify in the transform value.","syntax":"none | <length-percentage> [ <length-percentage> <length>? ]?","url":"https://developer.mozilla.org/docs/Web/CSS/translate"},"unicode-bidi":{"description":"The level of embedding with respect to the bidirectional algorithm.","syntax":"normal | embed | isolate | bidi-override | isolate-override | plaintext","url":"https://developer.mozilla.org/docs/Web/CSS/unicode-bidi"},"unicode-range":{"description":"@font-face descriptor. Defines the set of Unicode codepoints that may be supported by the font face for which it is declared.","syntax":"<unicode-range>#"},"user-select":{"description":"Controls the appearance of selection.","syntax":"auto | text | none | contain | all","url":"https://developer.mozilla.org/docs/Web/CSS/user-select"},"vector-effect":{"description":"The vector-effect CSS property suppresses specific transformation effects in SVG, thus permitting effects like a road on a map staying the same width no matter how the map is zoomed, or allowing a diagram key to retain its position and size regardless of other transforms. It can only be used with SVG elements that accept the vector-effect attribute. When used, the CSS value overrides any values of the element's vector-effect attribute.","syntax":"none | non-scaling-stroke | non-scaling-size | non-rotation | fixed-position","url":"https://developer.mozilla.org/docs/Web/CSS/vector-effect"},"vertical-align":{"description":"Affects the vertical positioning of the inline boxes generated by an inline-level element inside a line box.","syntax":"baseline | sub | super | text-top | text-bottom | middle | top | bottom | <percentage> | <length>","url":"https://developer.mozilla.org/docs/Web/CSS/vertical-align"},"view-timeline":{"description":"The view-timeline CSS shorthand property is used to define a named view progress timeline, which is progressed through based on the change in visibility of an element (known as the subject) inside a scrollable element (scroller). view-timeline is set on the subject.","syntax":"[ <'view-timeline-name'> <'view-timeline-axis'>? ]#","url":"https://developer.mozilla.org/docs/Web/CSS/view-timeline"},"view-timeline-axis":{"description":"The view-timeline-axis CSS property is used to specify the scrollbar direction that will be used to provide the timeline for a named view progress timeline animation, which is progressed through based on the change in visibility of an element (known as the subject) inside a scrollable element (scroller). view-timeline-axis is set on the subject. See CSS scroll-driven animations for more details.","syntax":"[ block | inline | x | y ]#","url":"https://developer.mozilla.org/docs/Web/CSS/view-timeline-axis"},"view-timeline-inset":{"description":"The view-timeline-inset CSS property is used to specify one or two values representing an adjustment to the position of the scrollport (see Scroll container for more details) in which the subject element of a named view progress timeline animation is deemed to be visible. Put another way, this allows you to specify start and/or end inset (or outset) values that offset the position of the timeline.","syntax":"[ [ auto | <length-percentage> ]{1,2} ]#","url":"https://developer.mozilla.org/docs/Web/CSS/view-timeline-inset"},"view-timeline-name":{"description":"The view-timeline-name CSS property is used to define the name of a named view progress timeline, which is progressed through based on the change in visibility of an element (known as the subject) inside a scrollable element (scroller). view-timeline is set on the subject.","syntax":"none | <dashed-ident>#","url":"https://developer.mozilla.org/docs/Web/CSS/view-timeline-name"},"view-transition-name":{"description":"The view-transition-name CSS property provides the selected element with a distinct identifying name (a custom-ident) and causes it to participate in a separate view transition from the root view transition \u2014 or no view transition if the none value is specified.","syntax":"none | <custom-ident>","url":"https://developer.mozilla.org/docs/Web/CSS/view-transition-name"},"visibility":{"description":"Specifies whether the boxes generated by an element are rendered. Invisible boxes still affect layout (set the 'display' property to 'none' to suppress box generation altogether).","syntax":"visible | hidden | collapse","url":"https://developer.mozilla.org/docs/Web/CSS/visibility"},"white-space":{"description":"Specifies how whitespace is handled in an element.","syntax":"normal | pre | nowrap | pre-wrap | pre-line | break-spaces | [ <'white-space-collapse'> || <'text-wrap'> || <'white-space-trim'> ]","url":"https://developer.mozilla.org/docs/Web/CSS/white-space"},"white-space-collapse":{"description":"The white-space-collapse CSS property controls how white space inside an element is collapsed.","syntax":"collapse | discard | preserve | preserve-breaks | preserve-spaces | break-spaces","url":"https://developer.mozilla.org/docs/Web/CSS/white-space-collapse"},"widows":{"description":"Specifies the minimum number of line boxes of a block container that must be left in a fragment after a break.","syntax":"<integer>","url":"https://developer.mozilla.org/docs/Web/CSS/widows"},"width":{"description":"Specifies the width of the content area, padding area or border area (depending on 'box-sizing') of certain boxes.","syntax":"auto | <length> | <percentage> | min-content | max-content | fit-content | fit-content(<length-percentage>)","url":"https://developer.mozilla.org/docs/Web/CSS/width"},"will-change":{"description":"Provides a rendering hint to the user agent, stating what kinds of changes the author expects to perform on the element.","syntax":"auto | <animateable-feature>#","url":"https://developer.mozilla.org/docs/Web/CSS/will-change"},"word-break":{"description":"Specifies line break opportunities for non-CJK scripts.","syntax":"normal | break-all | keep-all | break-word","url":"https://developer.mozilla.org/docs/Web/CSS/word-break"},"word-spacing":{"description":"Specifies additional spacing between \"words\".","syntax":"normal | <length>","url":"https://developer.mozilla.org/docs/Web/CSS/word-spacing"},"word-wrap":{"description":"Specifies whether the UA may break within a word to prevent overflow when an otherwise-unbreakable string is too long to fit.","syntax":"normal | break-word"},"writing-mode":{"description":"This is a shorthand property for both 'direction' and 'block-progression'.","syntax":"horizontal-tb | vertical-rl | vertical-lr | sideways-rl | sideways-lr","url":"https://developer.mozilla.org/docs/Web/CSS/writing-mode"},"x":{"description":"The x CSS property defines the x-axis coordinate of the top left corner of the SVG rect shape, image image, foreignObject viewport or nested svg viewport relative to the nearest <svg> ancestor's user coordinate system. If present, it overrides the element's x attribute.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/x"},"y":{"description":"The y CSS property defines the y-axis coordinate of the top left corner of the SVG rect shape, image image, foreignObject viewport and nested svg viewport relative to the nearest <svg> ancestor's user coordinate system. If present, it overrides the element's y attribute.","syntax":"<length> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/y"},"z-index":{"description":"For a positioned box, the 'z-index' property specifies the stack level of the box in the current stacking context and whether the box establishes a local stacking context.","syntax":"auto | <integer>","url":"https://developer.mozilla.org/docs/Web/CSS/z-index"},"zoom":{"description":"Non-standard. Specifies the magnification scale of the object. See 'transform: scale()' for a standards-based alternative.","syntax":"normal | reset | <number> | <percentage>","url":"https://developer.mozilla.org/docs/Web/CSS/zoom"}};
