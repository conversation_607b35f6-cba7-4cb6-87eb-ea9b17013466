{"build": "ha925a31_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\ffmpeg-4.3.1-ha925a31_0", "features": "", "files": ["Library/bin/avcodec-58.dll", "Library/bin/avdevice-58.dll", "Library/bin/avfilter-7.dll", "Library/bin/avformat-58.dll", "Library/bin/avutil-56.dll", "Library/bin/ffmpeg.exe", "Library/bin/ffplay.exe", "Library/bin/ffprobe.exe", "Library/bin/postproc-55.dll", "Library/bin/swresample-3.dll", "Library/bin/swscale-5.dll", "Library/include/libavcodec/ac3_parser.h", "Library/include/libavcodec/adts_parser.h", "Library/include/libavcodec/avcodec.h", "Library/include/libavcodec/avdct.h", "Library/include/libavcodec/avfft.h", "Library/include/libavcodec/bsf.h", "Library/include/libavcodec/codec.h", "Library/include/libavcodec/codec_desc.h", "Library/include/libavcodec/codec_id.h", "Library/include/libavcodec/codec_par.h", "Library/include/libavcodec/d3d11va.h", "Library/include/libavcodec/dirac.h", "Library/include/libavcodec/dv_profile.h", "Library/include/libavcodec/dxva2.h", "Library/include/libavcodec/jni.h", "Library/include/libavcodec/mediacodec.h", "Library/include/libavcodec/packet.h", "Library/include/libavcodec/qsv.h", "Library/include/libavcodec/vaapi.h", "Library/include/libavcodec/vdpau.h", "Library/include/libavcodec/version.h", "Library/include/libavcodec/videotoolbox.h", "Library/include/libavcodec/vorbis_parser.h", "Library/include/libavcodec/xvmc.h", "Library/include/libavdevice/avdevice.h", "Library/include/libavdevice/version.h", "Library/include/libavfilter/avfilter.h", "Library/include/libavfilter/buffersink.h", "Library/include/libavfilter/buffersrc.h", "Library/include/libavfilter/version.h", "Library/include/libavformat/avformat.h", "Library/include/libavformat/avio.h", "Library/include/libavformat/version.h", "Library/include/libavutil/adler32.h", "Library/include/libavutil/aes.h", "Library/include/libavutil/aes_ctr.h", "Library/include/libavutil/attributes.h", "Library/include/libavutil/audio_fifo.h", "Library/include/libavutil/avassert.h", "Library/include/libavutil/avconfig.h", "Library/include/libavutil/avstring.h", "Library/include/libavutil/avutil.h", "Library/include/libavutil/base64.h", "Library/include/libavutil/blowfish.h", "Library/include/libavutil/bprint.h", "Library/include/libavutil/bswap.h", "Library/include/libavutil/buffer.h", "Library/include/libavutil/camellia.h", "Library/include/libavutil/cast5.h", "Library/include/libavutil/channel_layout.h", "Library/include/libavutil/common.h", "Library/include/libavutil/cpu.h", "Library/include/libavutil/crc.h", "Library/include/libavutil/des.h", "Library/include/libavutil/dict.h", "Library/include/libavutil/display.h", "Library/include/libavutil/dovi_meta.h", "Library/include/libavutil/downmix_info.h", "Library/include/libavutil/encryption_info.h", "Library/include/libavutil/error.h", "Library/include/libavutil/eval.h", "Library/include/libavutil/ffversion.h", "Library/include/libavutil/fifo.h", "Library/include/libavutil/file.h", "Library/include/libavutil/frame.h", "Library/include/libavutil/hash.h", "Library/include/libavutil/hdr_dynamic_metadata.h", "Library/include/libavutil/hmac.h", "Library/include/libavutil/hwcontext.h", "Library/include/libavutil/hwcontext_cuda.h", "Library/include/libavutil/hwcontext_d3d11va.h", "Library/include/libavutil/hwcontext_drm.h", "Library/include/libavutil/hwcontext_dxva2.h", "Library/include/libavutil/hwcontext_mediacodec.h", "Library/include/libavutil/hwcontext_opencl.h", "Library/include/libavutil/hwcontext_qsv.h", "Library/include/libavutil/hwcontext_vaapi.h", "Library/include/libavutil/hwcontext_vdpau.h", "Library/include/libavutil/hwcontext_videotoolbox.h", "Library/include/libavutil/hwcontext_vulkan.h", "Library/include/libavutil/imgutils.h", "Library/include/libavutil/intfloat.h", "Library/include/libavutil/intreadwrite.h", "Library/include/libavutil/lfg.h", "Library/include/libavutil/log.h", "Library/include/libavutil/lzo.h", "Library/include/libavutil/macros.h", "Library/include/libavutil/mastering_display_metadata.h", "Library/include/libavutil/mathematics.h", "Library/include/libavutil/md5.h", "Library/include/libavutil/mem.h", "Library/include/libavutil/motion_vector.h", "Library/include/libavutil/murmur3.h", "Library/include/libavutil/opt.h", "Library/include/libavutil/parseutils.h", "Library/include/libavutil/pixdesc.h", "Library/include/libavutil/pixelutils.h", "Library/include/libavutil/pixfmt.h", "Library/include/libavutil/random_seed.h", "Library/include/libavutil/rational.h", "Library/include/libavutil/rc4.h", "Library/include/libavutil/replaygain.h", "Library/include/libavutil/ripemd.h", "Library/include/libavutil/samplefmt.h", "Library/include/libavutil/sha.h", "Library/include/libavutil/sha512.h", "Library/include/libavutil/spherical.h", "Library/include/libavutil/stereo3d.h", "Library/include/libavutil/tea.h", "Library/include/libavutil/threadmessage.h", "Library/include/libavutil/time.h", "Library/include/libavutil/timecode.h", "Library/include/libavutil/timestamp.h", "Library/include/libavutil/tree.h", "Library/include/libavutil/twofish.h", "Library/include/libavutil/tx.h", "Library/include/libavutil/version.h", "Library/include/libavutil/video_enc_params.h", "Library/include/libavutil/xtea.h", "Library/include/libpostproc/postprocess.h", "Library/include/libpostproc/version.h", "Library/include/libswresample/swresample.h", "Library/include/libswresample/version.h", "Library/include/libswscale/swscale.h", "Library/include/libswscale/version.h", "Library/lib/avcodec-58.def", "Library/lib/avcodec.lib", "Library/lib/avdevice-58.def", "Library/lib/avdevice.lib", "Library/lib/avfilter-7.def", "Library/lib/avfilter.lib", "Library/lib/avformat-58.def", "Library/lib/avformat.lib", "Library/lib/avutil-56.def", "Library/lib/avutil.lib", "Library/lib/libavcodec.dll.a", "Library/lib/libavdevice.dll.a", "Library/lib/libavfilter.dll.a", "Library/lib/libavformat.dll.a", "Library/lib/libavutil.dll.a", "Library/lib/libpostproc.dll.a", "Library/lib/libswresample.dll.a", "Library/lib/libswscale.dll.a", "Library/lib/postproc-55.def", "Library/lib/postproc.lib", "Library/lib/swresample-3.def", "Library/lib/swresample.lib", "Library/lib/swscale-5.def", "Library/lib/swscale.lib"], "fn": "ffmpeg-4.3.1-ha925a31_0.tar.bz2", "license": "GPL-3.0-or-later", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\ffmpeg-4.3.1-ha925a31_0", "type": 1}, "md5": "858eb8e0630eab350939a189963051e8", "name": "ffmpeg", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\ffmpeg-4.3.1-ha925a31_0.tar.bz2", "paths_data": {"paths": [{"_path": "Library/bin/avcodec-58.dll", "path_type": "hardlink", "sha256": "62bbf2e24641232530f9dd72854df6c31c1148dc2441e53b1ef2ffb016e98ac6", "sha256_in_prefix": "62bbf2e24641232530f9dd72854df6c31c1148dc2441e53b1ef2ffb016e98ac6", "size_in_bytes": 50103296}, {"_path": "Library/bin/avdevice-58.dll", "path_type": "hardlink", "sha256": "5602ebbf9356483e2efa194a1e44c4e5c88b314353c656658cb6af47d0254e44", "sha256_in_prefix": "5602ebbf9356483e2efa194a1e44c4e5c88b314353c656658cb6af47d0254e44", "size_in_bytes": 2760192}, {"_path": "Library/bin/avfilter-7.dll", "path_type": "hardlink", "sha256": "70ac0deb20c6ce67fa5c1e475ccd50555e1f73f1df86301dddd06f57961575b9", "sha256_in_prefix": "70ac0deb20c6ce67fa5c1e475ccd50555e1f73f1df86301dddd06f57961575b9", "size_in_bytes": 10957312}, {"_path": "Library/bin/avformat-58.dll", "path_type": "hardlink", "sha256": "3c1e5b0f89aaeb6bc1058f2d75a82b19a603d804236b3dcb81ceb0a69d1422bd", "sha256_in_prefix": "3c1e5b0f89aaeb6bc1058f2d75a82b19a603d804236b3dcb81ceb0a69d1422bd", "size_in_bytes": 11094016}, {"_path": "Library/bin/avutil-56.dll", "path_type": "hardlink", "sha256": "56c02192f7ae75ba31f4104cb8b1239c9eb2ce8c890e8cb64b380f9cc80cba0d", "sha256_in_prefix": "56c02192f7ae75ba31f4104cb8b1239c9eb2ce8c890e8cb64b380f9cc80cba0d", "size_in_bytes": 866304}, {"_path": "Library/bin/ffmpeg.exe", "path_type": "hardlink", "sha256": "70bd98ff8155a77243a1ef9031284ece6fc3c8c843975db1ef5d3382ce34f375", "sha256_in_prefix": "70bd98ff8155a77243a1ef9031284ece6fc3c8c843975db1ef5d3382ce34f375", "size_in_bytes": 355840}, {"_path": "Library/bin/ffplay.exe", "path_type": "hardlink", "sha256": "8f1d2a310ebf3c749d2cb2d3075cb8e681269a976de956b8c4a821197963dd38", "sha256_in_prefix": "8f1d2a310ebf3c749d2cb2d3075cb8e681269a976de956b8c4a821197963dd38", "size_in_bytes": 155648}, {"_path": "Library/bin/ffprobe.exe", "path_type": "hardlink", "sha256": "8a0ab08337c8c9a36aada8d77ccade1dfc340deaec8390e8c0b74a839f963f72", "sha256_in_prefix": "8a0ab08337c8c9a36aada8d77ccade1dfc340deaec8390e8c0b74a839f963f72", "size_in_bytes": 187392}, {"_path": "Library/bin/postproc-55.dll", "path_type": "hardlink", "sha256": "0f07c21f0c5bb99884cd171f6e1e5a6bee40c77c48b1693110dd1a72106f24f5", "sha256_in_prefix": "0f07c21f0c5bb99884cd171f6e1e5a6bee40c77c48b1693110dd1a72106f24f5", "size_in_bytes": 133120}, {"_path": "Library/bin/swresample-3.dll", "path_type": "hardlink", "sha256": "865cfd11827cfc38e1a8661c7af8480f033ff6a4ae6450312b83207807ada84e", "sha256_in_prefix": "865cfd11827cfc38e1a8661c7af8480f033ff6a4ae6450312b83207807ada84e", "size_in_bytes": 433664}, {"_path": "Library/bin/swscale-5.dll", "path_type": "hardlink", "sha256": "b35223235f88683558898dcff53a2ec12d0e411086eb6d3217e90b90a322cfbd", "sha256_in_prefix": "b35223235f88683558898dcff53a2ec12d0e411086eb6d3217e90b90a322cfbd", "size_in_bytes": 552960}, {"_path": "Library/include/libavcodec/ac3_parser.h", "path_type": "hardlink", "sha256": "200c6d2e96975196e8ba5f5716223dc9dda999d51578dabce2fca93175a05252", "sha256_in_prefix": "200c6d2e96975196e8ba5f5716223dc9dda999d51578dabce2fca93175a05252", "size_in_bytes": 1207}, {"_path": "Library/include/libavcodec/adts_parser.h", "path_type": "hardlink", "sha256": "d466583a8dc1260b015e588adbe3abd45f3f8ca0e43722f3088b472e80492a15", "sha256_in_prefix": "d466583a8dc1260b015e588adbe3abd45f3f8ca0e43722f3088b472e80492a15", "size_in_bytes": 1354}, {"_path": "Library/include/libavcodec/avcodec.h", "path_type": "hardlink", "sha256": "1ebbc709184d61d4d7d7681748e30e5e7b6756ed532a370a0e4105161ba7c699", "sha256_in_prefix": "1ebbc709184d61d4d7d7681748e30e5e7b6756ed532a370a0e4105161ba7c699", "size_in_bytes": 148465}, {"_path": "Library/include/libavcodec/avdct.h", "path_type": "hardlink", "sha256": "7c125edc1985ec078f99abb1c9044c4fc76b06a03eb02c026cb968fb9d41fcca", "sha256_in_prefix": "7c125edc1985ec078f99abb1c9044c4fc76b06a03eb02c026cb968fb9d41fcca", "size_in_bytes": 2726}, {"_path": "Library/include/libavcodec/avfft.h", "path_type": "hardlink", "sha256": "a0918d3d682f40bd65bbdc60ad0cdca08675a0067cd3e92581342b045fb0cc78", "sha256_in_prefix": "a0918d3d682f40bd65bbdc60ad0cdca08675a0067cd3e92581342b045fb0cc78", "size_in_bytes": 3111}, {"_path": "Library/include/libavcodec/bsf.h", "path_type": "hardlink", "sha256": "a11903285ca80db2ea798dc385ad111bddf8541ece22c8e939065d22085a7a65", "sha256_in_prefix": "a11903285ca80db2ea798dc385ad111bddf8541ece22c8e939065d22085a7a65", "size_in_bytes": 10942}, {"_path": "Library/include/libavcodec/codec.h", "path_type": "hardlink", "sha256": "a3b8cc84fd028212ac785e19e7e3d9a3ab88992f2cd20b0147096edb31b70d9a", "sha256_in_prefix": "a3b8cc84fd028212ac785e19e7e3d9a3ab88992f2cd20b0147096edb31b70d9a", "size_in_bytes": 16622}, {"_path": "Library/include/libavcodec/codec_desc.h", "path_type": "hardlink", "sha256": "30027aa432ae4ec3c10805850c9ba6880cf78b0e6c7f4e9198f6844481f3338b", "sha256_in_prefix": "30027aa432ae4ec3c10805850c9ba6880cf78b0e6c7f4e9198f6844481f3338b", "size_in_bytes": 3847}, {"_path": "Library/include/libavcodec/codec_id.h", "path_type": "hardlink", "sha256": "97332a972247d4336773eb89394d1a817b17aa34e288d41d8e25136c40de8bd4", "sha256_in_prefix": "97332a972247d4336773eb89394d1a817b17aa34e288d41d8e25136c40de8bd4", "size_in_bytes": 15219}, {"_path": "Library/include/libavcodec/codec_par.h", "path_type": "hardlink", "sha256": "164e5e5a12b28b5e58bf81546ea0c1f55acde461bf18509d654b04f3cd8d17ca", "sha256_in_prefix": "164e5e5a12b28b5e58bf81546ea0c1f55acde461bf18509d654b04f3cd8d17ca", "size_in_bytes": 7090}, {"_path": "Library/include/libavcodec/d3d11va.h", "path_type": "hardlink", "sha256": "74a55a2e3f19ce797e99624a224302f25efa89a115b9bf2e932c8fa179b0cc66", "sha256_in_prefix": "74a55a2e3f19ce797e99624a224302f25efa89a115b9bf2e932c8fa179b0cc66", "size_in_bytes": 2853}, {"_path": "Library/include/libavcodec/dirac.h", "path_type": "hardlink", "sha256": "b248987f650dd7a110c942ad67351dc920e115248c54532b29381c4c64be9905", "sha256_in_prefix": "b248987f650dd7a110c942ad67351dc920e115248c54532b29381c4c64be9905", "size_in_bytes": 4044}, {"_path": "Library/include/libavcodec/dv_profile.h", "path_type": "hardlink", "sha256": "efedccc613db41c8d5a68bf9c1b81ab8b0e13137ac85f5f7e75f0a08381fea75", "sha256_in_prefix": "efedccc613db41c8d5a68bf9c1b81ab8b0e13137ac85f5f7e75f0a08381fea75", "size_in_bytes": 3715}, {"_path": "Library/include/libavcodec/dxva2.h", "path_type": "hardlink", "sha256": "e69dc45a7d5a9206b3bfead10caf3117117005cd532c4fc599d9976637c1b9e3", "sha256_in_prefix": "e69dc45a7d5a9206b3bfead10caf3117117005cd532c4fc599d9976637c1b9e3", "size_in_bytes": 2361}, {"_path": "Library/include/libavcodec/jni.h", "path_type": "hardlink", "sha256": "18ca8eae5bce081b4eef1b1f61a62aefed1d4e6e3cde41487c81fb96ee709e51", "sha256_in_prefix": "18ca8eae5bce081b4eef1b1f61a62aefed1d4e6e3cde41487c81fb96ee709e51", "size_in_bytes": 1650}, {"_path": "Library/include/libavcodec/mediacodec.h", "path_type": "hardlink", "sha256": "0047c6e4a1c3a42160c1677ec76db3ff953ef62d3aaf2cd87c3a79eb73780bb7", "sha256_in_prefix": "0047c6e4a1c3a42160c1677ec76db3ff953ef62d3aaf2cd87c3a79eb73780bb7", "size_in_bytes": 3450}, {"_path": "Library/include/libavcodec/packet.h", "path_type": "hardlink", "sha256": "e7a2e1d37aa37f8e489b7122882c1052b7a82c4439cc1e0e9eadda3a7eee271f", "sha256_in_prefix": "e7a2e1d37aa37f8e489b7122882c1052b7a82c4439cc1e0e9eadda3a7eee271f", "size_in_bytes": 22930}, {"_path": "Library/include/libavcodec/qsv.h", "path_type": "hardlink", "sha256": "0a21a0e4770ce91802c3dea5623c2a4858e1480e664df51e2b1af0db829bff2f", "sha256_in_prefix": "0a21a0e4770ce91802c3dea5623c2a4858e1480e664df51e2b1af0db829bff2f", "size_in_bytes": 3763}, {"_path": "Library/include/libavcodec/vaapi.h", "path_type": "hardlink", "sha256": "17dc7116c4a79f1925457917e5b47ccf2ce546c47357241f77155fe25caebacf", "sha256_in_prefix": "17dc7116c4a79f1925457917e5b47ccf2ce546c47357241f77155fe25caebacf", "size_in_bytes": 2297}, {"_path": "Library/include/libavcodec/vdpau.h", "path_type": "hardlink", "sha256": "406d626f19911d6c4bef4afc77c469e49369b05ea0f87dbcf49fb3034400494e", "sha256_in_prefix": "406d626f19911d6c4bef4afc77c469e49369b05ea0f87dbcf49fb3034400494e", "size_in_bytes": 5796}, {"_path": "Library/include/libavcodec/version.h", "path_type": "hardlink", "sha256": "24a66ac9009d371d638457994fb531517b8e3e685b70f694cc83472ab324813c", "sha256_in_prefix": "24a66ac9009d371d638457994fb531517b8e3e685b70f694cc83472ab324813c", "size_in_bytes": 5216}, {"_path": "Library/include/libavcodec/videotoolbox.h", "path_type": "hardlink", "sha256": "3545b1add1d97a99f3646f3e07baa76b59ec0b6abb6a7db1b190472161ca25e6", "sha256_in_prefix": "3545b1add1d97a99f3646f3e07baa76b59ec0b6abb6a7db1b190472161ca25e6", "size_in_bytes": 4029}, {"_path": "Library/include/libavcodec/vorbis_parser.h", "path_type": "hardlink", "sha256": "57077b2e1d28d42636cab0f69e4b92b1ad64ac2eaa2843c270a6afaf308a76ae", "sha256_in_prefix": "57077b2e1d28d42636cab0f69e4b92b1ad64ac2eaa2843c270a6afaf308a76ae", "size_in_bytes": 2285}, {"_path": "Library/include/libavcodec/xvmc.h", "path_type": "hardlink", "sha256": "51987ad98b125aedc395360fa29b8f4f7029e8d98abe9999def9f5216f8bc2a9", "sha256_in_prefix": "51987ad98b125aedc395360fa29b8f4f7029e8d98abe9999def9f5216f8bc2a9", "size_in_bytes": 6062}, {"_path": "Library/include/libavdevice/avdevice.h", "path_type": "hardlink", "sha256": "da68bd84322575ba8cf2800a0c26e031e74ede44c07922fa3cb7ec745a33079c", "sha256_in_prefix": "da68bd84322575ba8cf2800a0c26e031e74ede44c07922fa3cb7ec745a33079c", "size_in_bytes": 17918}, {"_path": "Library/include/libavdevice/version.h", "path_type": "hardlink", "sha256": "09ccb919f4b3feb7734f9c5a1b74245fb51995146ab4607c750a977e939700c8", "sha256_in_prefix": "09ccb919f4b3feb7734f9c5a1b74245fb51995146ab4607c750a977e939700c8", "size_in_bytes": 1861}, {"_path": "Library/include/libavfilter/avfilter.h", "path_type": "hardlink", "sha256": "013883f46b5f382320ae939452971cb36e7c90ddd5d5c7acf1fd70e343ffe85f", "sha256_in_prefix": "013883f46b5f382320ae939452971cb36e7c90ddd5d5c7acf1fd70e343ffe85f", "size_in_bytes": 42280}, {"_path": "Library/include/libavfilter/buffersink.h", "path_type": "hardlink", "sha256": "1b4beb9118b3da3325cc8b52f5bceb4e77c90dba9d37517316c33dc3e60eb143", "sha256_in_prefix": "1b4beb9118b3da3325cc8b52f5bceb4e77c90dba9d37517316c33dc3e60eb143", "size_in_bytes": 6379}, {"_path": "Library/include/libavfilter/buffersrc.h", "path_type": "hardlink", "sha256": "f92fe96caf3757ecc8ccb140036e08b975e1e6ddf25ea588eff7c910f78b5c4b", "sha256_in_prefix": "f92fe96caf3757ecc8ccb140036e08b975e1e6ddf25ea588eff7c910f78b5c4b", "size_in_bytes": 6609}, {"_path": "Library/include/libavfilter/version.h", "path_type": "hardlink", "sha256": "68ca61d765f47a5711e06a55487744651a73c90e0651b5d70eaba0c048cf8cea", "sha256_in_prefix": "68ca61d765f47a5711e06a55487744651a73c90e0651b5d70eaba0c048cf8cea", "size_in_bytes": 2450}, {"_path": "Library/include/libavformat/avformat.h", "path_type": "hardlink", "sha256": "e76fac9fbea5927640dd79fff3cfcc745b65c8c255fbb5f2c3847b355e059fca", "sha256_in_prefix": "e76fac9fbea5927640dd79fff3cfcc745b65c8c255fbb5f2c3847b355e059fca", "size_in_bytes": 118149}, {"_path": "Library/include/libavformat/avio.h", "path_type": "hardlink", "sha256": "7d5d7c7a52a0e1f304c4e72d0e4429319e5749aecbb4ae3b5b315d83dde49715", "sha256_in_prefix": "7d5d7c7a52a0e1f304c4e72d0e4429319e5749aecbb4ae3b5b315d83dde49715", "size_in_bytes": 32546}, {"_path": "Library/include/libavformat/version.h", "path_type": "hardlink", "sha256": "1ab1c0249bca87f88a3a89299230926d19934c6c7fdd9977c2b6295b1c5501d3", "sha256_in_prefix": "1ab1c0249bca87f88a3a89299230926d19934c6c7fdd9977c2b6295b1c5501d3", "size_in_bytes": 4227}, {"_path": "Library/include/libavutil/adler32.h", "path_type": "hardlink", "sha256": "2e1fe97266ac1fe84693f2a837f57e48fcf0582d242f13d7034d4443e9eccfca", "sha256_in_prefix": "2e1fe97266ac1fe84693f2a837f57e48fcf0582d242f13d7034d4443e9eccfca", "size_in_bytes": 1673}, {"_path": "Library/include/libavutil/aes.h", "path_type": "hardlink", "sha256": "36644cefd472da0aa2f88871ded8949ec91eb4ec252fd7d25e5d94c4db229139", "sha256_in_prefix": "36644cefd472da0aa2f88871ded8949ec91eb4ec252fd7d25e5d94c4db229139", "size_in_bytes": 1834}, {"_path": "Library/include/libavutil/aes_ctr.h", "path_type": "hardlink", "sha256": "3687d7cc50d48d3c74c7bd4cdb4ea89b5d947fdb9d5e66e7eacef656a41cec3a", "sha256_in_prefix": "3687d7cc50d48d3c74c7bd4cdb4ea89b5d947fdb9d5e66e7eacef656a41cec3a", "size_in_bytes": 2269}, {"_path": "Library/include/libavutil/attributes.h", "path_type": "hardlink", "sha256": "5579591da44aa6d523ae4211d157abb503075ea818e49a8dc4e31c5933f79a58", "sha256_in_prefix": "5579591da44aa6d523ae4211d157abb503075ea818e49a8dc4e31c5933f79a58", "size_in_bytes": 4828}, {"_path": "Library/include/libavutil/audio_fifo.h", "path_type": "hardlink", "sha256": "a11be7a6e84da1f82e2c771faffe4d20ba79d11299ee32fe8667d005c305dd2e", "sha256_in_prefix": "a11be7a6e84da1f82e2c771faffe4d20ba79d11299ee32fe8667d005c305dd2e", "size_in_bytes": 5914}, {"_path": "Library/include/libavutil/avassert.h", "path_type": "hardlink", "sha256": "a067aceabf8c8998393bbd7ab3c3949e041948c6e9672dc22295b975a177c634", "sha256_in_prefix": "a067aceabf8c8998393bbd7ab3c3949e041948c6e9672dc22295b975a177c634", "size_in_bytes": 2354}, {"_path": "Library/include/libavutil/avconfig.h", "path_type": "hardlink", "sha256": "975611ad5eba15212d9e1d5fca9d4fdf0daec6d2269b2fcab8e29af8667164bc", "sha256_in_prefix": "975611ad5eba15212d9e1d5fca9d4fdf0daec6d2269b2fcab8e29af8667164bc", "size_in_bytes": 180}, {"_path": "Library/include/libavutil/avstring.h", "path_type": "hardlink", "sha256": "10f984e6f1b6e93883b5604b0a9b2833b84939d38c50d75581b79f7088ae9de8", "sha256_in_prefix": "10f984e6f1b6e93883b5604b0a9b2833b84939d38c50d75581b79f7088ae9de8", "size_in_bytes": 14580}, {"_path": "Library/include/libavutil/avutil.h", "path_type": "hardlink", "sha256": "5d868e4d3340fccd511662b67eb4e28febd23e4eb28466d57a7e277228d53baf", "sha256_in_prefix": "5d868e4d3340fccd511662b67eb4e28febd23e4eb28466d57a7e277228d53baf", "size_in_bytes": 9576}, {"_path": "Library/include/libavutil/base64.h", "path_type": "hardlink", "sha256": "81ac13d23f3744fe85ea2651ce903e201cd55fc63fcdd899d2cfe5560d50ef3d", "sha256_in_prefix": "81ac13d23f3744fe85ea2651ce903e201cd55fc63fcdd899d2cfe5560d50ef3d", "size_in_bytes": 2285}, {"_path": "Library/include/libavutil/blowfish.h", "path_type": "hardlink", "sha256": "b955a63c60c8b3be0203ec6c3973f9084d848cf884fe56cd56088301aeef7992", "sha256_in_prefix": "b955a63c60c8b3be0203ec6c3973f9084d848cf884fe56cd56088301aeef7992", "size_in_bytes": 2394}, {"_path": "Library/include/libavutil/bprint.h", "path_type": "hardlink", "sha256": "c4040dabbc4310b612834e6884f73119850704431a320e9b157275fca99b7066", "sha256_in_prefix": "c4040dabbc4310b612834e6884f73119850704431a320e9b157275fca99b7066", "size_in_bytes": 7797}, {"_path": "Library/include/libavutil/bswap.h", "path_type": "hardlink", "sha256": "7d6432122fcb3a261f037a4323738301a5a6b50ebe80940749a9c9704a56bc3c", "sha256_in_prefix": "7d6432122fcb3a261f037a4323738301a5a6b50ebe80940749a9c9704a56bc3c", "size_in_bytes": 2858}, {"_path": "Library/include/libavutil/buffer.h", "path_type": "hardlink", "sha256": "b09c97cf2920c90b8440f281ac74ea034c567139bbcd3f899e39b6c38c0323a2", "sha256_in_prefix": "b09c97cf2920c90b8440f281ac74ea034c567139bbcd3f899e39b6c38c0323a2", "size_in_bytes": 11264}, {"_path": "Library/include/libavutil/camellia.h", "path_type": "hardlink", "sha256": "78e8569378757df3f33b5217b2b3bcace1abd970e56e97fa9855e22c86a13822", "sha256_in_prefix": "78e8569378757df3f33b5217b2b3bcace1abd970e56e97fa9855e22c86a13822", "size_in_bytes": 2139}, {"_path": "Library/include/libavutil/cast5.h", "path_type": "hardlink", "sha256": "05b2e13aecaa0adbb470081a689f45baffb8e03a71997c31f37a22ea4e383a60", "sha256_in_prefix": "05b2e13aecaa0adbb470081a689f45baffb8e03a71997c31f37a22ea4e383a60", "size_in_bytes": 2561}, {"_path": "Library/include/libavutil/channel_layout.h", "path_type": "hardlink", "sha256": "dbd46ceb8bf6a74f41fb3ea21b064c808e6f4572467c99310111915d1b34b570", "sha256_in_prefix": "dbd46ceb8bf6a74f41fb3ea21b064c808e6f4572467c99310111915d1b34b570", "size_in_bytes": 9613}, {"_path": "Library/include/libavutil/common.h", "path_type": "hardlink", "sha256": "da8719a082955972d0259477b4d07c53019a84897b1bf37d39510313e0a7b162", "sha256_in_prefix": "da8719a082955972d0259477b4d07c53019a84897b1bf37d39510313e0a7b162", "size_in_bytes": 18054}, {"_path": "Library/include/libavutil/cpu.h", "path_type": "hardlink", "sha256": "568994c2574ad6a8a9322eca98034c8a64cf3e3d0f1ed94b77d792060eea01db", "sha256_in_prefix": "568994c2574ad6a8a9322eca98034c8a64cf3e3d0f1ed94b77d792060eea01db", "size_in_bytes": 5682}, {"_path": "Library/include/libavutil/crc.h", "path_type": "hardlink", "sha256": "8c9d577abb491f244c1f2c2d46b5bb25a216ea4b3826f2b3d727f956edf0a155", "sha256_in_prefix": "8c9d577abb491f244c1f2c2d46b5bb25a216ea4b3826f2b3d727f956edf0a155", "size_in_bytes": 3137}, {"_path": "Library/include/libavutil/des.h", "path_type": "hardlink", "sha256": "aff467bd102466db2f73ba2fa9c652a02b7c80daa5d7906c8dfa2dee9448d469", "sha256_in_prefix": "aff467bd102466db2f73ba2fa9c652a02b7c80daa5d7906c8dfa2dee9448d469", "size_in_bytes": 2333}, {"_path": "Library/include/libavutil/dict.h", "path_type": "hardlink", "sha256": "b8f3d9d4f4e59c77fede23eef817e5821608151f86cd8dfffb4b81c877d973c8", "sha256_in_prefix": "b8f3d9d4f4e59c77fede23eef817e5821608151f86cd8dfffb4b81c877d973c8", "size_in_bytes": 8275}, {"_path": "Library/include/libavutil/display.h", "path_type": "hardlink", "sha256": "c1f7f1151190de1e96fe998e245aae498e8f776f658ac3e566b24fc56de43866", "sha256_in_prefix": "c1f7f1151190de1e96fe998e245aae498e8f776f658ac3e566b24fc56de43866", "size_in_bytes": 3527}, {"_path": "Library/include/libavutil/dovi_meta.h", "path_type": "hardlink", "sha256": "fea1246ff049fe55c80dbf4fc149736640c9ecb3f750ce13dac748ddb41482d0", "sha256_in_prefix": "fea1246ff049fe55c80dbf4fc149736640c9ecb3f750ce13dac748ddb41482d0", "size_in_bytes": 2265}, {"_path": "Library/include/libavutil/downmix_info.h", "path_type": "hardlink", "sha256": "2fc23ad8f0750d82fcd6aa3b653998e2ea9721f9d1664df7b6cb80e93d7fa3aa", "sha256_in_prefix": "2fc23ad8f0750d82fcd6aa3b653998e2ea9721f9d1664df7b6cb80e93d7fa3aa", "size_in_bytes": 3235}, {"_path": "Library/include/libavutil/encryption_info.h", "path_type": "hardlink", "sha256": "ccc3a4a889b8a3c5aaf37b9fb2407bcdf23a065487c7cba718518a517c463b18", "sha256_in_prefix": "ccc3a4a889b8a3c5aaf37b9fb2407bcdf23a065487c7cba718518a517c463b18", "size_in_bytes": 7056}, {"_path": "Library/include/libavutil/error.h", "path_type": "hardlink", "sha256": "cd530a990c8949e867f05e08e4de07c2df170d435707a73679fe5fe53bb3389e", "sha256_in_prefix": "cd530a990c8949e867f05e08e4de07c2df170d435707a73679fe5fe53bb3389e", "size_in_bytes": 5468}, {"_path": "Library/include/libavutil/eval.h", "path_type": "hardlink", "sha256": "3b720c39b0ce8bb2342d8b224c4bc20d8e07e20de73d4292b1f6d279ba1da300", "sha256_in_prefix": "3b720c39b0ce8bb2342d8b224c4bc20d8e07e20de73d4292b1f6d279ba1da300", "size_in_bytes": 6327}, {"_path": "Library/include/libavutil/ffversion.h", "path_type": "hardlink", "sha256": "21835b08de5a59cfc8d3c8e1f43be3dd22fda9984da3491a148130ce8b65abb2", "sha256_in_prefix": "21835b08de5a59cfc8d3c8e1f43be3dd22fda9984da3491a148130ce8b65abb2", "size_in_bytes": 184}, {"_path": "Library/include/libavutil/fifo.h", "path_type": "hardlink", "sha256": "d8837e2675b3e4380e60857e94150713e612ea34f3da1868515d7449f05f1fbc", "sha256_in_prefix": "d8837e2675b3e4380e60857e94150713e612ea34f3da1868515d7449f05f1fbc", "size_in_bytes": 5899}, {"_path": "Library/include/libavutil/file.h", "path_type": "hardlink", "sha256": "2041d9df9579033df30b93d0a9d7eff2d7c8b684026d8741abca62f386646fc8", "sha256_in_prefix": "2041d9df9579033df30b93d0a9d7eff2d7c8b684026d8741abca62f386646fc8", "size_in_bytes": 2698}, {"_path": "Library/include/libavutil/frame.h", "path_type": "hardlink", "sha256": "b19b20f4869d74b69db1ce1dcec41928350edbadd68d1ada90879e2708dc0655", "sha256_in_prefix": "b19b20f4869d74b69db1ce1dcec41928350edbadd68d1ada90879e2708dc0655", "size_in_bytes": 32374}, {"_path": "Library/include/libavutil/hash.h", "path_type": "hardlink", "sha256": "22973e98797c24e281827b0671300a54676134873a096d1a0cc6dcbcf778d44d", "sha256_in_prefix": "22973e98797c24e281827b0671300a54676134873a096d1a0cc6dcbcf778d44d", "size_in_bytes": 8574}, {"_path": "Library/include/libavutil/hdr_dynamic_metadata.h", "path_type": "hardlink", "sha256": "9a6ff1740d7c46575e19ac812615522001d44d47617e0e55e1515f2367dfa10d", "sha256_in_prefix": "9a6ff1740d7c46575e19ac812615522001d44d47617e0e55e1515f2367dfa10d", "size_in_bytes": 12638}, {"_path": "Library/include/libavutil/hmac.h", "path_type": "hardlink", "sha256": "47dcbfb72349596901070b2ed57facf1e02efd9fc91e8633f93a392ede671e2e", "sha256_in_prefix": "47dcbfb72349596901070b2ed57facf1e02efd9fc91e8633f93a392ede671e2e", "size_in_bytes": 2886}, {"_path": "Library/include/libavutil/hwcontext.h", "path_type": "hardlink", "sha256": "3c16d0ae1d7c52e82c6cdef20c530d10f401b56d16462339aa9256ea7aee2bee", "sha256_in_prefix": "3c16d0ae1d7c52e82c6cdef20c530d10f401b56d16462339aa9256ea7aee2bee", "size_in_bytes": 24083}, {"_path": "Library/include/libavutil/hwcontext_cuda.h", "path_type": "hardlink", "sha256": "75f30a392dda1d8f43561f436a0fc35cd43264d2f6ab412ae2f98fd1cd738bae", "sha256_in_prefix": "75f30a392dda1d8f43561f436a0fc35cd43264d2f6ab412ae2f98fd1cd738bae", "size_in_bytes": 1728}, {"_path": "Library/include/libavutil/hwcontext_d3d11va.h", "path_type": "hardlink", "sha256": "71b43c95ec6ef2eabe6858d7cef5f7e734e551b045fe6b0d7e1717499e15bec2", "sha256_in_prefix": "71b43c95ec6ef2eabe6858d7cef5f7e734e551b045fe6b0d7e1717499e15bec2", "size_in_bytes": 6225}, {"_path": "Library/include/libavutil/hwcontext_drm.h", "path_type": "hardlink", "sha256": "b598f37f40cf1342f923c0b97784a6f2830b543868eccee046375e096fbd5f24", "sha256_in_prefix": "b598f37f40cf1342f923c0b97784a6f2830b543868eccee046375e096fbd5f24", "size_in_bytes": 4673}, {"_path": "Library/include/libavutil/hwcontext_dxva2.h", "path_type": "hardlink", "sha256": "73a0333b65e99675834dcb1b63a5e9339638ccc619f1a2fcba85cdd0e179ade0", "sha256_in_prefix": "73a0333b65e99675834dcb1b63a5e9339638ccc619f1a2fcba85cdd0e179ade0", "size_in_bytes": 2411}, {"_path": "Library/include/libavutil/hwcontext_mediacodec.h", "path_type": "hardlink", "sha256": "782ffc4acb547481295419cec98e036290f555483ec374972db129299c9ecec3", "sha256_in_prefix": "782ffc4acb547481295419cec98e036290f555483ec374972db129299c9ecec3", "size_in_bytes": 1203}, {"_path": "Library/include/libavutil/hwcontext_opencl.h", "path_type": "hardlink", "sha256": "ad521fa2fd015cb1aba962468ca4ac176f5fc6b2c4b7be28f05e1c03d89e1b31", "sha256_in_prefix": "ad521fa2fd015cb1aba962468ca4ac176f5fc6b2c4b7be28f05e1c03d89e1b31", "size_in_bytes": 3097}, {"_path": "Library/include/libavutil/hwcontext_qsv.h", "path_type": "hardlink", "sha256": "28c54c9ddb4bd8e8c47b11b1f44dffde40eac604b2b6ec6f1448f7d298780878", "sha256_in_prefix": "28c54c9ddb4bd8e8c47b11b1f44dffde40eac604b2b6ec6f1448f7d298780878", "size_in_bytes": 1557}, {"_path": "Library/include/libavutil/hwcontext_vaapi.h", "path_type": "hardlink", "sha256": "6f6c6a5250dd0f901cdc7de8b9b3db26102719b7e056cd17500009096bfd9b39", "sha256_in_prefix": "6f6c6a5250dd0f901cdc7de8b9b3db26102719b7e056cd17500009096bfd9b39", "size_in_bytes": 3787}, {"_path": "Library/include/libavutil/hwcontext_vdpau.h", "path_type": "hardlink", "sha256": "6c96373d9e5deb2c500004f3f55ee1d2cea0f76cdfaeabaf5a3ad3e4938e8252", "sha256_in_prefix": "6c96373d9e5deb2c500004f3f55ee1d2cea0f76cdfaeabaf5a3ad3e4938e8252", "size_in_bytes": 1360}, {"_path": "Library/include/libavutil/hwcontext_videotoolbox.h", "path_type": "hardlink", "sha256": "d08e53470895fc73d7a4ab717491e11e17d71105776d9c9a3d6acb810a2ca72e", "sha256_in_prefix": "d08e53470895fc73d7a4ab717491e11e17d71105776d9c9a3d6acb810a2ca72e", "size_in_bytes": 2110}, {"_path": "Library/include/libavutil/hwcontext_vulkan.h", "path_type": "hardlink", "sha256": "5308ae17a75c211e3c6b01becaa803179b7b814d2092e75d356d2423beab4b1e", "sha256_in_prefix": "5308ae17a75c211e3c6b01becaa803179b7b814d2092e75d356d2423beab4b1e", "size_in_bytes": 7120}, {"_path": "Library/include/libavutil/imgutils.h", "path_type": "hardlink", "sha256": "0d2ddf88e72b4f6f2aa880399faf495e3789f64a3d1e81273d73fd146e5118aa", "sha256_in_prefix": "0d2ddf88e72b4f6f2aa880399faf495e3789f64a3d1e81273d73fd146e5118aa", "size_in_bytes": 11530}, {"_path": "Library/include/libavutil/intfloat.h", "path_type": "hardlink", "sha256": "3a29e4eebc8c269cfd867b96de91d8231773d392c12a8820e46eaba96d2b4ca1", "sha256_in_prefix": "3a29e4eebc8c269cfd867b96de91d8231773d392c12a8820e46eaba96d2b4ca1", "size_in_bytes": 1726}, {"_path": "Library/include/libavutil/intreadwrite.h", "path_type": "hardlink", "sha256": "61f5c70a02b95624756cc191c0c9d1c232219d37c051b4f33d662dfd33233958", "sha256_in_prefix": "61f5c70a02b95624756cc191c0c9d1c232219d37c051b4f33d662dfd33233958", "size_in_bytes": 18763}, {"_path": "Library/include/libavutil/lfg.h", "path_type": "hardlink", "sha256": "e2b65dc748b82ac20d298809cb1f839a4532b59b8acc78a64f0f9f80348ad516", "sha256_in_prefix": "e2b65dc748b82ac20d298809cb1f839a4532b59b8acc78a64f0f9f80348ad516", "size_in_bytes": 2498}, {"_path": "Library/include/libavutil/log.h", "path_type": "hardlink", "sha256": "8df54c26df239c7c8e72a242f96dc0e4fc97567e0e4f125729e7f1bcca4847f4", "sha256_in_prefix": "8df54c26df239c7c8e72a242f96dc0e4fc97567e0e4f125729e7f1bcca4847f4", "size_in_bytes": 12511}, {"_path": "Library/include/libavutil/lzo.h", "path_type": "hardlink", "sha256": "61e89928dee9d83030adececac06aa6c1ae2aada06c5682fde52c52015c53556", "sha256_in_prefix": "61e89928dee9d83030adececac06aa6c1ae2aada06c5682fde52c52015c53556", "size_in_bytes": 2048}, {"_path": "Library/include/libavutil/macros.h", "path_type": "hardlink", "sha256": "7fd8514cbe4b6ef025b0f5cc3b752e2c3940e22f4945cf6e6372653b6e3d2bca", "sha256_in_prefix": "7fd8514cbe4b6ef025b0f5cc3b752e2c3940e22f4945cf6e6372653b6e3d2bca", "size_in_bytes": 1249}, {"_path": "Library/include/libavutil/mastering_display_metadata.h", "path_type": "hardlink", "sha256": "9d5743a42306ac0158e26248a1281ae8be9ebfeb02e67f14e0e6ae770a543a65", "sha256_in_prefix": "9d5743a42306ac0158e26248a1281ae8be9ebfeb02e67f14e0e6ae770a543a65", "size_in_bytes": 3944}, {"_path": "Library/include/libavutil/mathematics.h", "path_type": "hardlink", "sha256": "a4cd34f7c19d82c4e58349f0c4987f5b3829dad15755f4eca166b492506cd37c", "sha256_in_prefix": "a4cd34f7c19d82c4e58349f0c4987f5b3829dad15755f4eca166b492506cd37c", "size_in_bytes": 7945}, {"_path": "Library/include/libavutil/md5.h", "path_type": "hardlink", "sha256": "435edd1b3c07f8afb9f73c0a80b362a8cf439049ae3aba55b106edb055aac10d", "sha256_in_prefix": "435edd1b3c07f8afb9f73c0a80b362a8cf439049ae3aba55b106edb055aac10d", "size_in_bytes": 2323}, {"_path": "Library/include/libavutil/mem.h", "path_type": "hardlink", "sha256": "86e8321a725ad3762f17babdc73cea675a94eb6514aa1f29f649668d4b5a6652", "sha256_in_prefix": "86e8321a725ad3762f17babdc73cea675a94eb6514aa1f29f649668d4b5a6652", "size_in_bytes": 23619}, {"_path": "Library/include/libavutil/motion_vector.h", "path_type": "hardlink", "sha256": "dc0b0a15a638c8b91df95a418c5951ee5e787d518f22b6e3d70094922536e8bb", "sha256_in_prefix": "dc0b0a15a638c8b91df95a418c5951ee5e787d518f22b6e3d70094922536e8bb", "size_in_bytes": 1770}, {"_path": "Library/include/libavutil/murmur3.h", "path_type": "hardlink", "sha256": "412eade9534f79127b104385f2941d0c0dc1c609b05624599742ecdaf16bbe64", "sha256_in_prefix": "412eade9534f79127b104385f2941d0c0dc1c609b05624599742ecdaf16bbe64", "size_in_bytes": 3621}, {"_path": "Library/include/libavutil/opt.h", "path_type": "hardlink", "sha256": "747f391d39df456ec6f7eba289c066441266577388b533011818c285a72ad4b9", "sha256_in_prefix": "747f391d39df456ec6f7eba289c066441266577388b533011818c285a72ad4b9", "size_in_bytes": 36374}, {"_path": "Library/include/libavutil/parseutils.h", "path_type": "hardlink", "sha256": "dafb4321ecbd52930d3900d8d22b2d1714b0375622aeea4acfdf8a0a4b6ee03b", "sha256_in_prefix": "dafb4321ecbd52930d3900d8d22b2d1714b0375622aeea4acfdf8a0a4b6ee03b", "size_in_bytes": 7575}, {"_path": "Library/include/libavutil/pixdesc.h", "path_type": "hardlink", "sha256": "8fb7a741f77a9e5e3f2842d045c3ad64197f077acc8114af49257e94faef990f", "sha256_in_prefix": "8fb7a741f77a9e5e3f2842d045c3ad64197f077acc8114af49257e94faef990f", "size_in_bytes": 16277}, {"_path": "Library/include/libavutil/pixelutils.h", "path_type": "hardlink", "sha256": "c011fc734af1dc54ee933456b15f84ecee65cfe1a24ee16f90b41e4ed98e55c1", "sha256_in_prefix": "c011fc734af1dc54ee933456b15f84ecee65cfe1a24ee16f90b41e4ed98e55c1", "size_in_bytes": 2071}, {"_path": "Library/include/libavutil/pixfmt.h", "path_type": "hardlink", "sha256": "12d401e4f717d460cd30d3b3c527d6d04cf6ad0745f39d3743ac8d9517218df5", "sha256_in_prefix": "12d401e4f717d460cd30d3b3c527d6d04cf6ad0745f39d3743ac8d9517218df5", "size_in_bytes": 34610}, {"_path": "Library/include/libavutil/random_seed.h", "path_type": "hardlink", "sha256": "78b238cd2dacd863879052ca3b1405c314aab220f63b076602f10bf321974ac5", "sha256_in_prefix": "78b238cd2dacd863879052ca3b1405c314aab220f63b076602f10bf321974ac5", "size_in_bytes": 1400}, {"_path": "Library/include/libavutil/rational.h", "path_type": "hardlink", "sha256": "6efac8849324779e78f9a08f468a2bcb7d3386834539af4f7b13ce88d1e66f4d", "sha256_in_prefix": "6efac8849324779e78f9a08f468a2bcb7d3386834539af4f7b13ce88d1e66f4d", "size_in_bytes": 6063}, {"_path": "Library/include/libavutil/rc4.h", "path_type": "hardlink", "sha256": "74610fdb46f20ba5dac1edd1da2f2d6d837ac4c5746aa9efb16c9c147f39ffe8", "sha256_in_prefix": "74610fdb46f20ba5dac1edd1da2f2d6d837ac4c5746aa9efb16c9c147f39ffe8", "size_in_bytes": 1882}, {"_path": "Library/include/libavutil/replaygain.h", "path_type": "hardlink", "sha256": "4ec82edbdc4e5493fba3cae6a27566f0f15d1399ccf16e25073ffd50ba8187ea", "sha256_in_prefix": "4ec82edbdc4e5493fba3cae6a27566f0f15d1399ccf16e25073ffd50ba8187ea", "size_in_bytes": 1607}, {"_path": "Library/include/libavutil/ripemd.h", "path_type": "hardlink", "sha256": "5aaabbbb3a38b615ab892e10d7edd872cf755bf08ca270607add98a42752c337", "sha256_in_prefix": "5aaabbbb3a38b615ab892e10d7edd872cf755bf08ca270607add98a42752c337", "size_in_bytes": 2285}, {"_path": "Library/include/libavutil/samplefmt.h", "path_type": "hardlink", "sha256": "e92c47e4aedb439ccb630e46e80ef884c848ca2b7baeb64b1050802312751e0d", "sha256_in_prefix": "e92c47e4aedb439ccb630e46e80ef884c848ca2b7baeb64b1050802312751e0d", "size_in_bytes": 10318}, {"_path": "Library/include/libavutil/sha.h", "path_type": "hardlink", "sha256": "7ae6876df76d49be52b7257a6bcc03406bb44946a302588774ce11aed25b3bff", "sha256_in_prefix": "7ae6876df76d49be52b7257a6bcc03406bb44946a302588774ce11aed25b3bff", "size_in_bytes": 2505}, {"_path": "Library/include/libavutil/sha512.h", "path_type": "hardlink", "sha256": "1f3c46f73d81df4bf5724c0892cf8ef9bbfe93a408c5719c14f044c59744e7e9", "sha256_in_prefix": "1f3c46f73d81df4bf5724c0892cf8ef9bbfe93a408c5719c14f044c59744e7e9", "size_in_bytes": 2560}, {"_path": "Library/include/libavutil/spherical.h", "path_type": "hardlink", "sha256": "e8e02f113e48665bbe3fd610617b4ac0dc945fffca78d50fa81bd22e725e5576", "sha256_in_prefix": "e8e02f113e48665bbe3fd610617b4ac0dc945fffca78d50fa81bd22e725e5576", "size_in_bytes": 8024}, {"_path": "Library/include/libavutil/stereo3d.h", "path_type": "hardlink", "sha256": "1b841852957b05963deefaee454c5adef1a58e049bbca3cce4888dc3c09b7359", "sha256_in_prefix": "1b841852957b05963deefaee454c5adef1a58e049bbca3cce4888dc3c09b7359", "size_in_bytes": 5250}, {"_path": "Library/include/libavutil/tea.h", "path_type": "hardlink", "sha256": "3c1e93c566630bb4eeedad3ef3c8719bd6050081ac1c764b1fde81aba4969076", "sha256_in_prefix": "3c1e93c566630bb4eeedad3ef3c8719bd6050081ac1c764b1fde81aba4969076", "size_in_bytes": 2035}, {"_path": "Library/include/libavutil/threadmessage.h", "path_type": "hardlink", "sha256": "9bb242d7adc48662b947726843108aff7c34547d7a4a0d0e6f58f54a00fc4c9f", "sha256_in_prefix": "9bb242d7adc48662b947726843108aff7c34547d7a4a0d0e6f58f54a00fc4c9f", "size_in_bytes": 3910}, {"_path": "Library/include/libavutil/time.h", "path_type": "hardlink", "sha256": "40e11fa242e0585996753affb054443e78be25919b7c3063042d0aaff1656760", "sha256_in_prefix": "40e11fa242e0585996753affb054443e78be25919b7c3063042d0aaff1656760", "size_in_bytes": 1800}, {"_path": "Library/include/libavutil/timecode.h", "path_type": "hardlink", "sha256": "b3a67f8f052a7a29f4fd2fd9623e5d3e632d556bc515f5c2b99efdfc2e86ec9b", "sha256_in_prefix": "b3a67f8f052a7a29f4fd2fd9623e5d3e632d556bc515f5c2b99efdfc2e86ec9b", "size_in_bytes": 5325}, {"_path": "Library/include/libavutil/timestamp.h", "path_type": "hardlink", "sha256": "c4753df37c8b0e63e99042007d5836b954ce825913642c57cc7c58b4a52d0273", "sha256_in_prefix": "c4753df37c8b0e63e99042007d5836b954ce825913642c57cc7c58b4a52d0273", "size_in_bytes": 2617}, {"_path": "Library/include/libavutil/tree.h", "path_type": "hardlink", "sha256": "fb371063da19c9052aba46b24ac62b1b9dfaed7911bf7408d3ae6916a6e9dbc4", "sha256_in_prefix": "fb371063da19c9052aba46b24ac62b1b9dfaed7911bf7408d3ae6916a6e9dbc4", "size_in_bytes": 5429}, {"_path": "Library/include/libavutil/twofish.h", "path_type": "hardlink", "sha256": "f6823a45af3a076bcf3f3f3361f1fa6b7aa0ee85a75ca0e8480b9b27b4532189", "sha256_in_prefix": "f6823a45af3a076bcf3f3f3361f1fa6b7aa0ee85a75ca0e8480b9b27b4532189", "size_in_bytes": 2245}, {"_path": "Library/include/libavutil/tx.h", "path_type": "hardlink", "sha256": "c68477f8514ceb18b6353665b91d66e9f6e84197b522e076791543c66136d227", "sha256_in_prefix": "c68477f8514ceb18b6353665b91d66e9f6e84197b522e076791543c66136d227", "size_in_bytes": 4070}, {"_path": "Library/include/libavutil/version.h", "path_type": "hardlink", "sha256": "dab9113e7ea0632bc7d1bec79f71ecdb19689acbc3c97dbe76b8f17fce11ada8", "sha256_in_prefix": "dab9113e7ea0632bc7d1bec79f71ecdb19689acbc3c97dbe76b8f17fce11ada8", "size_in_bytes": 4669}, {"_path": "Library/include/libavutil/video_enc_params.h", "path_type": "hardlink", "sha256": "3d61c33e189f990abb345c2dc8ad606e27e459eb4a760ee15610e3e9ea2a8d2d", "sha256_in_prefix": "3d61c33e189f990abb345c2dc8ad606e27e459eb4a760ee15610e3e9ea2a8d2d", "size_in_bytes": 5777}, {"_path": "Library/include/libavutil/xtea.h", "path_type": "hardlink", "sha256": "2eb91f780cc4ad86095e4ebbce453475d40f4e9b8737d52bdf20a068dfafcdf0", "sha256_in_prefix": "2eb91f780cc4ad86095e4ebbce453475d40f4e9b8737d52bdf20a068dfafcdf0", "size_in_bytes": 2834}, {"_path": "Library/include/libpostproc/postprocess.h", "path_type": "hardlink", "sha256": "ec163d6a1de0e9fb8083e01a1ad760514a9948cdbe9a9289b5525ba0aaaca9a4", "sha256_in_prefix": "ec163d6a1de0e9fb8083e01a1ad760514a9948cdbe9a9289b5525ba0aaaca9a4", "size_in_bytes": 2936}, {"_path": "Library/include/libpostproc/version.h", "path_type": "hardlink", "sha256": "72d7fc9e8b38838ee1f584cf437e2ef02f58eac5b535bb6b4f92ee37b769302e", "sha256_in_prefix": "72d7fc9e8b38838ee1f584cf437e2ef02f58eac5b535bb6b4f92ee37b769302e", "size_in_bytes": 1642}, {"_path": "Library/include/libswresample/swresample.h", "path_type": "hardlink", "sha256": "50e101a21eb6854cdcda08a39afb7f97f5780445753fd8e7260a08e0ef96b138", "sha256_in_prefix": "50e101a21eb6854cdcda08a39afb7f97f5780445753fd8e7260a08e0ef96b138", "size_in_bytes": 21785}, {"_path": "Library/include/libswresample/version.h", "path_type": "hardlink", "sha256": "91d00791474f8ea8abceb227456ca17780fc7db7e7f4237dc26bf536606a51df", "sha256_in_prefix": "91d00791474f8ea8abceb227456ca17780fc7db7e7f4237dc26bf536606a51df", "size_in_bytes": 1718}, {"_path": "Library/include/libswscale/swscale.h", "path_type": "hardlink", "sha256": "10edc3471a22d9395d1f496689436b516572ad373fa13c24511e4a8019aa2188", "sha256_in_prefix": "10edc3471a22d9395d1f496689436b516572ad373fa13c24511e4a8019aa2188", "size_in_bytes": 12015}, {"_path": "Library/include/libswscale/version.h", "path_type": "hardlink", "sha256": "679c9a0560d5ea9fe425e67e1df3b843029d5f9a6edf28df406748e1a7760a97", "sha256_in_prefix": "679c9a0560d5ea9fe425e67e1df3b843029d5f9a6edf28df406748e1a7760a97", "size_in_bytes": 1927}, {"_path": "Library/lib/avcodec-58.def", "path_type": "hardlink", "sha256": "77edfd3a111f855c307b257a919a14638a2c05c5042d6ec32836929d2ab76018", "sha256_in_prefix": "77edfd3a111f855c307b257a919a14638a2c05c5042d6ec32836929d2ab76018", "size_in_bytes": 6240}, {"_path": "Library/lib/avcodec.lib", "path_type": "hardlink", "sha256": "6eecfe4d1c27c087578b1d2669b50886ba09f3f573c79cca26776500094a96e9", "sha256_in_prefix": "6eecfe4d1c27c087578b1d2669b50886ba09f3f573c79cca26776500094a96e9", "size_in_bytes": 186320}, {"_path": "Library/lib/avdevice-58.def", "path_type": "hardlink", "sha256": "87a2f2e5740ac6cf53ca7072c8886b27c2acd2f89a06ae614da8151d1a22911b", "sha256_in_prefix": "87a2f2e5740ac6cf53ca7072c8886b27c2acd2f89a06ae614da8151d1a22911b", "size_in_bytes": 544}, {"_path": "Library/lib/avdevice.lib", "path_type": "hardlink", "sha256": "be619f51b9a2f7499635481c9c3253c93f885d1c59a740a136a133285119f0b9", "sha256_in_prefix": "be619f51b9a2f7499635481c9c3253c93f885d1c59a740a136a133285119f0b9", "size_in_bytes": 15874}, {"_path": "Library/lib/avfilter-7.def", "path_type": "hardlink", "sha256": "0621904e535bad5b0ab4ade9a8d0283ee65a5e89e3a3bd167a36031a17fc2f77", "sha256_in_prefix": "0621904e535bad5b0ab4ade9a8d0283ee65a5e89e3a3bd167a36031a17fc2f77", "size_in_bytes": 1898}, {"_path": "Library/lib/avfilter.lib", "path_type": "hardlink", "sha256": "45ab4faf49933736d5478dc1a01510f38a75c8a77bb2146b0b974f5067d43d8e", "sha256_in_prefix": "45ab4faf49933736d5478dc1a01510f38a75c8a77bb2146b0b974f5067d43d8e", "size_in_bytes": 55002}, {"_path": "Library/lib/avformat-58.def", "path_type": "hardlink", "sha256": "b97a9772092686d173bbe0886929464fefc925e6a353631feba47d7f37af7ffd", "sha256_in_prefix": "b97a9772092686d173bbe0886929464fefc925e6a353631feba47d7f37af7ffd", "size_in_bytes": 4232}, {"_path": "Library/lib/avformat.lib", "path_type": "hardlink", "sha256": "99ade8b0501ad5ad068dceb0698e5ea957cec71ca64530e830abac2c2c0899c3", "sha256_in_prefix": "99ade8b0501ad5ad068dceb0698e5ea957cec71ca64530e830abac2c2c0899c3", "size_in_bytes": 136558}, {"_path": "Library/lib/avutil-56.def", "path_type": "hardlink", "sha256": "60a309b25898fb9b8d55da3b0065f14c9a8a7a33641e20399c21e474acbec629", "sha256_in_prefix": "60a309b25898fb9b8d55da3b0065f14c9a8a7a33641e20399c21e474acbec629", "size_in_bytes": 12651}, {"_path": "Library/lib/avutil.lib", "path_type": "hardlink", "sha256": "749fcc602bc95e96117e91fe9eb16ec1cd5b73f6042b0198f118dc2d37aaa441", "sha256_in_prefix": "749fcc602bc95e96117e91fe9eb16ec1cd5b73f6042b0198f118dc2d37aaa441", "size_in_bytes": 414514}, {"_path": "Library/lib/libavcodec.dll.a", "path_type": "hardlink", "sha256": "e917acac0badd23c7e0839dae330e54807addda6383bf158389c5e3ae885a4e0", "sha256_in_prefix": "e917acac0badd23c7e0839dae330e54807addda6383bf158389c5e3ae885a4e0", "size_in_bytes": 920260}, {"_path": "Library/lib/libavdevice.dll.a", "path_type": "hardlink", "sha256": "307b4e67205ba53592495eb8621a816d69f2653da61cd85a7306b1da736ff95d", "sha256_in_prefix": "307b4e67205ba53592495eb8621a816d69f2653da61cd85a7306b1da736ff95d", "size_in_bytes": 468408}, {"_path": "Library/lib/libavfilter.dll.a", "path_type": "hardlink", "sha256": "3ec1b24691776c121ccad5ae455821e749840d722a6cbdf80b59347710e49450", "sha256_in_prefix": "3ec1b24691776c121ccad5ae455821e749840d722a6cbdf80b59347710e49450", "size_in_bytes": 45742}, {"_path": "Library/lib/libavformat.dll.a", "path_type": "hardlink", "sha256": "bf808844886e3d1a6c6002c27a28de85c7f545954de4ea653001b6773b3273c5", "sha256_in_prefix": "bf808844886e3d1a6c6002c27a28de85c7f545954de4ea653001b6773b3273c5", "size_in_bytes": 137478}, {"_path": "Library/lib/libavutil.dll.a", "path_type": "hardlink", "sha256": "5c7a00f2755ff3c0987ed2c0682984498c9fe1733b9ae8bb9ad6b3de7b6e9558", "sha256_in_prefix": "5c7a00f2755ff3c0987ed2c0682984498c9fe1733b9ae8bb9ad6b3de7b6e9558", "size_in_bytes": 346420}, {"_path": "Library/lib/libpostproc.dll.a", "path_type": "hardlink", "sha256": "3992f3831833066c5b1637fdadecaddc8a0eca5f9d6255bf8ab5353bc7d6095c", "sha256_in_prefix": "3992f3831833066c5b1637fdadecaddc8a0eca5f9d6255bf8ab5353bc7d6095c", "size_in_bytes": 7130}, {"_path": "Library/lib/libswresample.dll.a", "path_type": "hardlink", "sha256": "00106e78a17087c3633e8b0b41fc3f704cad9a1311a682e9627b3e446e9bb35e", "sha256_in_prefix": "00106e78a17087c3633e8b0b41fc3f704cad9a1311a682e9627b3e446e9bb35e", "size_in_bytes": 15196}, {"_path": "Library/lib/libswscale.dll.a", "path_type": "hardlink", "sha256": "377092186d6e7d0146cf23dc30687dd0fad82df9c224a6ccfe62c97584eefec6", "sha256_in_prefix": "377092186d6e7d0146cf23dc30687dd0fad82df9c224a6ccfe62c97584eefec6", "size_in_bytes": 22232}, {"_path": "Library/lib/postproc-55.def", "path_type": "hardlink", "sha256": "02ac70e452e238c4ec5b191af1479cbaabe03d7f153c9176421af57d40a0f68f", "sha256_in_prefix": "02ac70e452e238c4ec5b191af1479cbaabe03d7f153c9176421af57d40a0f68f", "size_in_bytes": 223}, {"_path": "Library/lib/postproc.lib", "path_type": "hardlink", "sha256": "1d6027de53c79aa69c3b884f0596bb413f7fd5b485db3de82e6cab7ab3a547b1", "sha256_in_prefix": "1d6027de53c79aa69c3b884f0596bb413f7fd5b485db3de82e6cab7ab3a547b1", "size_in_bytes": 9078}, {"_path": "Library/lib/swresample-3.def", "path_type": "hardlink", "sha256": "b249a1cbdf2867bcbbd5a8f194d14be0c4e8b4b570ec8895cbf1ff0f372039ec", "sha256_in_prefix": "b249a1cbdf2867bcbbd5a8f194d14be0c4e8b4b570ec8895cbf1ff0f372039ec", "size_in_bytes": 473}, {"_path": "Library/lib/swresample.lib", "path_type": "hardlink", "sha256": "08acc6926da16bfa86d51c14bc8163966e931d3d40498670abdc20272956ea32", "sha256_in_prefix": "08acc6926da16bfa86d51c14bc8163966e931d3d40498670abdc20272956ea32", "size_in_bytes": 18842}, {"_path": "Library/lib/swscale-5.def", "path_type": "hardlink", "sha256": "e328a940ceb4d4624a5a27fb76259b84a84d7ff29fa6754c2f339ebfc19d7c1a", "sha256_in_prefix": "e328a940ceb4d4624a5a27fb76259b84a84d7ff29fa6754c2f339ebfc19d7c1a", "size_in_bytes": 756}, {"_path": "Library/lib/swscale.lib", "path_type": "hardlink", "sha256": "017aded308c41677450c5db7e6e079a7c49ee89c99aa23149cd94cb97e105a76", "sha256_in_prefix": "017aded308c41677450c5db7e6e079a7c49ee89c99aa23149cd94cb97e105a76", "size_in_bytes": 27204}], "paths_version": 1}, "requested_spec": "ffmpeg", "sha256": "ff3bd6b2d0fbc23ebb19028251a519f187d0c54a8d6fe70105d6240cc82a1651", "size": 27427857, "subdir": "win-64", "timestamp": 1596712949000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/ffmpeg-4.3.1-ha925a31_0.tar.bz2", "version": "4.3.1"}