<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityModule Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityModule Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityModule" -->The activity record for a CUDA module.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityModule.html#21c8a353e98f2a401464419f25f8b109">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityModule.html#75bdbc5fc0c610fcfc4c2240ee35ed9f">cubin</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityModule.html#484c4f871d4640080f1423116dcfdd35">cubinSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityModule.html#5b7d253f82f5027e051a82a61228118e">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityModule.html#290b8117ac4d4988dd0a8e2d743c9034">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityModule.html#a5b435c84b08be50d7fe5edf4b321cf0">pad</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a CUDA module (CUPTI_ACTIVITY_KIND_MODULE). This activity record kind is not produced by the activity API but is included for completeness and ease-of-use. Profile frameworks built on top of CUPTI that collect module data from the module callback may choose to use this type to store the collected module data. <hr><h2>Field Documentation</h2>
<a class="anchor" name="21c8a353e98f2a401464419f25f8b109"></a><!-- doxytag: member="CUpti_ActivityModule::contextId" ref="21c8a353e98f2a401464419f25f8b109" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityModule.html#21c8a353e98f2a401464419f25f8b109">CUpti_ActivityModule::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the module is loaded. 
</div>
</div><p>
<a class="anchor" name="75bdbc5fc0c610fcfc4c2240ee35ed9f"></a><!-- doxytag: member="CUpti_ActivityModule::cubin" ref="75bdbc5fc0c610fcfc4c2240ee35ed9f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="structCUpti__ActivityModule.html#75bdbc5fc0c610fcfc4c2240ee35ed9f">CUpti_ActivityModule::cubin</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pointer to cubin. 
</div>
</div><p>
<a class="anchor" name="484c4f871d4640080f1423116dcfdd35"></a><!-- doxytag: member="CUpti_ActivityModule::cubinSize" ref="484c4f871d4640080f1423116dcfdd35" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityModule.html#484c4f871d4640080f1423116dcfdd35">CUpti_ActivityModule::cubinSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cubin size. 
</div>
</div><p>
<a class="anchor" name="5b7d253f82f5027e051a82a61228118e"></a><!-- doxytag: member="CUpti_ActivityModule::id" ref="5b7d253f82f5027e051a82a61228118e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityModule.html#5b7d253f82f5027e051a82a61228118e">CUpti_ActivityModule::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The module ID. 
</div>
</div><p>
<a class="anchor" name="290b8117ac4d4988dd0a8e2d743c9034"></a><!-- doxytag: member="CUpti_ActivityModule::kind" ref="290b8117ac4d4988dd0a8e2d743c9034" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityModule.html#290b8117ac4d4988dd0a8e2d743c9034">CUpti_ActivityModule::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MODULE. 
</div>
</div><p>
<a class="anchor" name="a5b435c84b08be50d7fe5edf4b321cf0"></a><!-- doxytag: member="CUpti_ActivityModule::pad" ref="a5b435c84b08be50d7fe5edf4b321cf0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityModule.html#a5b435c84b08be50d7fe5edf4b321cf0">CUpti_ActivityModule::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
