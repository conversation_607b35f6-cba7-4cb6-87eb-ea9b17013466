<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel5 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel5 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel5" -->The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#fb3dba2058832b1b85dd57b46134634c">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#1fcffd0addc89247e05b5fff9f6e2acd">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#9396c9fd7f4e636805daf9d5f2835127">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#fc634185bab3ea0079207778fd8878c5">cacheConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#8756eeb571e45af21137c300098e96c6">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#cc9bd28490660434741a9dd627737c72">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#306490b1d2df0eacafa80f052fbc3020">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#a651297acef554868f220bfcd92e00b5">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#50442d4e776675f09b06d8f98dfedd80">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#8b31b2a5d3549e8fdcd7fe307bc1d9bd">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#3f314bdb614fa874e020051442c4d66d">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#c02d2832b5952030a15945878aacc53e">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#6b706326f142d648051039e0633bdd30">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#73eb97d871c5ad3c6a3854a09e31d0a2">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#fa256ef8bec9fd9d7b6fd699ccf731b7">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#6321fcd57b370b54f3a3d5f06d00cb72">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#e370d8359558263835b1677db32687b7">isSharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#5bc489a8248ae448f8b03db884bab70c">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#f0f7f737518b6d22db58014239d74738">launchType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#7cfe4c3e94a626447c3d546188ad637d">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#85339bc4b57ad0de49e4af733554d0dd">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#02898b016ca764a8fb5b10102de544e7">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#83be78d563ab6303c4714537c99cb472">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#b0851f858df09cb67f04e9e962fd18c9">partitionedGlobalCacheExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#5719804df55222ff1c9430903b94165f">partitionedGlobalCacheRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#00f47bb41198d04ed8d275a8431a8810">queued</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#c0d69567bba06869994e5c543af5e5ee">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#429dd00e64b2cb834dccdc76abccabac">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#1e930f5a20e6ef3337c8e81a69432f03">sharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#f92bb2ca9a9f489a26bd8a6ab11a0370">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#3cd8ba3fbe71eae8670142b19afe560a">sharedMemoryExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#4472a37d059787668ab98f29e012bd94">shmemLimitConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#1e06bc296d674e602ff1684976309d4a">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#0f74a2c66412a58e9f93a32c8e3641b8">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#b3cdd2044aaab415fe927bcef43f8408">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#c4f3e84370dbfa4eb6ef447e83b4d156">submitted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#b025e85744e58f9c44e0ea1f651a4456">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel5.html#88fac5be5e7d2dc8c65bd617c08bc300">requested</a>:4</td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="fb3dba2058832b1b85dd57b46134634c"></a><!-- doxytag: member="CUpti_ActivityKernel5::blockX" ref="fb3dba2058832b1b85dd57b46134634c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#fb3dba2058832b1b85dd57b46134634c">CUpti_ActivityKernel5::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="1fcffd0addc89247e05b5fff9f6e2acd"></a><!-- doxytag: member="CUpti_ActivityKernel5::blockY" ref="1fcffd0addc89247e05b5fff9f6e2acd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#1fcffd0addc89247e05b5fff9f6e2acd">CUpti_ActivityKernel5::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="9396c9fd7f4e636805daf9d5f2835127"></a><!-- doxytag: member="CUpti_ActivityKernel5::blockZ" ref="9396c9fd7f4e636805daf9d5f2835127" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#9396c9fd7f4e636805daf9d5f2835127">CUpti_ActivityKernel5::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="fc634185bab3ea0079207778fd8878c5"></a><!-- doxytag: member="CUpti_ActivityKernel5::cacheConfig" ref="fc634185bab3ea0079207778fd8878c5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityKernel5.html#fc634185bab3ea0079207778fd8878c5">CUpti_ActivityKernel5::cacheConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For devices with compute capability 7.0+ cacheConfig values are not updated in case field isSharedMemoryCarveoutRequested is set 
</div>
</div><p>
<a class="anchor" name="8756eeb571e45af21137c300098e96c6"></a><!-- doxytag: member="CUpti_ActivityKernel5::completed" ref="8756eeb571e45af21137c300098e96c6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel5.html#8756eeb571e45af21137c300098e96c6">CUpti_ActivityKernel5::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="cc9bd28490660434741a9dd627737c72"></a><!-- doxytag: member="CUpti_ActivityKernel5::contextId" ref="cc9bd28490660434741a9dd627737c72" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#cc9bd28490660434741a9dd627737c72">CUpti_ActivityKernel5::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="306490b1d2df0eacafa80f052fbc3020"></a><!-- doxytag: member="CUpti_ActivityKernel5::correlationId" ref="306490b1d2df0eacafa80f052fbc3020" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#306490b1d2df0eacafa80f052fbc3020">CUpti_ActivityKernel5::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="a651297acef554868f220bfcd92e00b5"></a><!-- doxytag: member="CUpti_ActivityKernel5::deviceId" ref="a651297acef554868f220bfcd92e00b5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#a651297acef554868f220bfcd92e00b5">CUpti_ActivityKernel5::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="50442d4e776675f09b06d8f98dfedd80"></a><!-- doxytag: member="CUpti_ActivityKernel5::dynamicSharedMemory" ref="50442d4e776675f09b06d8f98dfedd80" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#50442d4e776675f09b06d8f98dfedd80">CUpti_ActivityKernel5::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="8b31b2a5d3549e8fdcd7fe307bc1d9bd"></a><!-- doxytag: member="CUpti_ActivityKernel5::end" ref="8b31b2a5d3549e8fdcd7fe307bc1d9bd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel5.html#8b31b2a5d3549e8fdcd7fe307bc1d9bd">CUpti_ActivityKernel5::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="b025e85744e58f9c44e0ea1f651a4456"></a><!-- doxytag: member="CUpti_ActivityKernel5::executed" ref="b025e85744e58f9c44e0ea1f651a4456" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel5.html#b025e85744e58f9c44e0ea1f651a4456">CUpti_ActivityKernel5::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="3f314bdb614fa874e020051442c4d66d"></a><!-- doxytag: member="CUpti_ActivityKernel5::graphId" ref="3f314bdb614fa874e020051442c4d66d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#3f314bdb614fa874e020051442c4d66d">CUpti_ActivityKernel5::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="c02d2832b5952030a15945878aacc53e"></a><!-- doxytag: member="CUpti_ActivityKernel5::graphNodeId" ref="c02d2832b5952030a15945878aacc53e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel5.html#c02d2832b5952030a15945878aacc53e">CUpti_ActivityKernel5::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="6b706326f142d648051039e0633bdd30"></a><!-- doxytag: member="CUpti_ActivityKernel5::gridId" ref="6b706326f142d648051039e0633bdd30" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel5.html#6b706326f142d648051039e0633bdd30">CUpti_ActivityKernel5::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="73eb97d871c5ad3c6a3854a09e31d0a2"></a><!-- doxytag: member="CUpti_ActivityKernel5::gridX" ref="73eb97d871c5ad3c6a3854a09e31d0a2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#73eb97d871c5ad3c6a3854a09e31d0a2">CUpti_ActivityKernel5::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="fa256ef8bec9fd9d7b6fd699ccf731b7"></a><!-- doxytag: member="CUpti_ActivityKernel5::gridY" ref="fa256ef8bec9fd9d7b6fd699ccf731b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#fa256ef8bec9fd9d7b6fd699ccf731b7">CUpti_ActivityKernel5::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="6321fcd57b370b54f3a3d5f06d00cb72"></a><!-- doxytag: member="CUpti_ActivityKernel5::gridZ" ref="6321fcd57b370b54f3a3d5f06d00cb72" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#6321fcd57b370b54f3a3d5f06d00cb72">CUpti_ActivityKernel5::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="e370d8359558263835b1677db32687b7"></a><!-- doxytag: member="CUpti_ActivityKernel5::isSharedMemoryCarveoutRequested" ref="e370d8359558263835b1677db32687b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel5.html#e370d8359558263835b1677db32687b7">CUpti_ActivityKernel5::isSharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This indicates if CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT was updated for the kernel launch 
</div>
</div><p>
<a class="anchor" name="5bc489a8248ae448f8b03db884bab70c"></a><!-- doxytag: member="CUpti_ActivityKernel5::kind" ref="5bc489a8248ae448f8b03db884bab70c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel5.html#5bc489a8248ae448f8b03db884bab70c">CUpti_ActivityKernel5::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="f0f7f737518b6d22db58014239d74738"></a><!-- doxytag: member="CUpti_ActivityKernel5::launchType" ref="f0f7f737518b6d22db58014239d74738" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel5.html#f0f7f737518b6d22db58014239d74738">CUpti_ActivityKernel5::launchType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The indicates if the kernel was executed via a regular launch or via a single/multi device cooperative launch. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c" title="The type of the CUDA kernel launch.">CUpti_ActivityLaunchType</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="7cfe4c3e94a626447c3d546188ad637d"></a><!-- doxytag: member="CUpti_ActivityKernel5::localMemoryPerThread" ref="7cfe4c3e94a626447c3d546188ad637d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#7cfe4c3e94a626447c3d546188ad637d">CUpti_ActivityKernel5::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="85339bc4b57ad0de49e4af733554d0dd"></a><!-- doxytag: member="CUpti_ActivityKernel5::localMemoryTotal" ref="85339bc4b57ad0de49e4af733554d0dd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#85339bc4b57ad0de49e4af733554d0dd">CUpti_ActivityKernel5::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="02898b016ca764a8fb5b10102de544e7"></a><!-- doxytag: member="CUpti_ActivityKernel5::name" ref="02898b016ca764a8fb5b10102de544e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel5.html#02898b016ca764a8fb5b10102de544e7">CUpti_ActivityKernel5::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="83be78d563ab6303c4714537c99cb472"></a><!-- doxytag: member="CUpti_ActivityKernel5::padding" ref="83be78d563ab6303c4714537c99cb472" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel5.html#83be78d563ab6303c4714537c99cb472">CUpti_ActivityKernel5::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="b0851f858df09cb67f04e9e962fd18c9"></a><!-- doxytag: member="CUpti_ActivityKernel5::partitionedGlobalCacheExecuted" ref="b0851f858df09cb67f04e9e962fd18c9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel5.html#b0851f858df09cb67f04e9e962fd18c9">CUpti_ActivityKernel5::partitionedGlobalCacheExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching executed for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. Partitioned global caching can be automatically disabled if the occupancy requirement of the launch cannot support caching. 
</div>
</div><p>
<a class="anchor" name="5719804df55222ff1c9430903b94165f"></a><!-- doxytag: member="CUpti_ActivityKernel5::partitionedGlobalCacheRequested" ref="5719804df55222ff1c9430903b94165f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel5.html#5719804df55222ff1c9430903b94165f">CUpti_ActivityKernel5::partitionedGlobalCacheRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching requested for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. 
</div>
</div><p>
<a class="anchor" name="00f47bb41198d04ed8d275a8431a8810"></a><!-- doxytag: member="CUpti_ActivityKernel5::queued" ref="00f47bb41198d04ed8d275a8431a8810" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel5.html#00f47bb41198d04ed8d275a8431a8810">CUpti_ActivityKernel5::queued</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the kernel is queued up in the command buffer, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the queued time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection.<p>
Command buffer is a buffer written by CUDA driver to send commands like kernel launch, memory copy etc to the GPU. All launches of CUDA kernels are asynchrnous with respect to the host, the host requests the launch by writing commands into the command buffer, then returns without checking the GPU's progress. 
</div>
</div><p>
<a class="anchor" name="c0d69567bba06869994e5c543af5e5ee"></a><!-- doxytag: member="CUpti_ActivityKernel5::registersPerThread" ref="c0d69567bba06869994e5c543af5e5ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel5.html#c0d69567bba06869994e5c543af5e5ee">CUpti_ActivityKernel5::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="88fac5be5e7d2dc8c65bd617c08bc300"></a><!-- doxytag: member="CUpti_ActivityKernel5::requested" ref="88fac5be5e7d2dc8c65bd617c08bc300" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel5.html#88fac5be5e7d2dc8c65bd617c08bc300">CUpti_ActivityKernel5::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="429dd00e64b2cb834dccdc76abccabac"></a><!-- doxytag: member="CUpti_ActivityKernel5::reserved0" ref="429dd00e64b2cb834dccdc76abccabac" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel5.html#429dd00e64b2cb834dccdc76abccabac">CUpti_ActivityKernel5::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="1e930f5a20e6ef3337c8e81a69432f03"></a><!-- doxytag: member="CUpti_ActivityKernel5::sharedMemoryCarveoutRequested" ref="1e930f5a20e6ef3337c8e81a69432f03" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel5.html#1e930f5a20e6ef3337c8e81a69432f03">CUpti_ActivityKernel5::sharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory carveout value requested for the function in percentage of the total resource. The value will be updated only if field isSharedMemoryCarveoutRequested is set. 
</div>
</div><p>
<a class="anchor" name="f92bb2ca9a9f489a26bd8a6ab11a0370"></a><!-- doxytag: member="CUpti_ActivityKernel5::sharedMemoryConfig" ref="f92bb2ca9a9f489a26bd8a6ab11a0370" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel5.html#f92bb2ca9a9f489a26bd8a6ab11a0370">CUpti_ActivityKernel5::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="3cd8ba3fbe71eae8670142b19afe560a"></a><!-- doxytag: member="CUpti_ActivityKernel5::sharedMemoryExecuted" ref="3cd8ba3fbe71eae8670142b19afe560a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#3cd8ba3fbe71eae8670142b19afe560a">CUpti_ActivityKernel5::sharedMemoryExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory size set by the driver. 
</div>
</div><p>
<a class="anchor" name="4472a37d059787668ab98f29e012bd94"></a><!-- doxytag: member="CUpti_ActivityKernel5::shmemLimitConfig" ref="4472a37d059787668ab98f29e012bd94" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a> <a class="el" href="structCUpti__ActivityKernel5.html#4472a37d059787668ab98f29e012bd94">CUpti_ActivityKernel5::shmemLimitConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory limit config for the kernel. This field shows whether user has opted for a higher per block limit of dynamic shared memory. 
</div>
</div><p>
<a class="anchor" name="1e06bc296d674e602ff1684976309d4a"></a><!-- doxytag: member="CUpti_ActivityKernel5::start" ref="1e06bc296d674e602ff1684976309d4a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel5.html#1e06bc296d674e602ff1684976309d4a">CUpti_ActivityKernel5::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="0f74a2c66412a58e9f93a32c8e3641b8"></a><!-- doxytag: member="CUpti_ActivityKernel5::staticSharedMemory" ref="0f74a2c66412a58e9f93a32c8e3641b8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel5.html#0f74a2c66412a58e9f93a32c8e3641b8">CUpti_ActivityKernel5::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="b3cdd2044aaab415fe927bcef43f8408"></a><!-- doxytag: member="CUpti_ActivityKernel5::streamId" ref="b3cdd2044aaab415fe927bcef43f8408" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel5.html#b3cdd2044aaab415fe927bcef43f8408">CUpti_ActivityKernel5::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="c4f3e84370dbfa4eb6ef447e83b4d156"></a><!-- doxytag: member="CUpti_ActivityKernel5::submitted" ref="c4f3e84370dbfa4eb6ef447e83b4d156" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel5.html#c4f3e84370dbfa4eb6ef447e83b4d156">CUpti_ActivityKernel5::submitted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the command buffer containing the kernel launch is submitted to the GPU, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the submitted time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
