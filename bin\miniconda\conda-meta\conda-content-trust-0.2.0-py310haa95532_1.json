{"arch": "x86_64", "build": "py310haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["cryptography >=41", "python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-content-trust-0.2.0-py310haa95532_1", "files": ["Lib/site-packages/conda_content_trust-0.2.0.dist-info/INSTALLER", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/METADATA", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/RECORD", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/REQUESTED", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/WHEEL", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/direct_url.json", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/entry_points.txt", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/licenses/AUTHORS.md", "Lib/site-packages/conda_content_trust-0.2.0.dist-info/licenses/LICENSE", "Lib/site-packages/conda_content_trust/__init__.py", "Lib/site-packages/conda_content_trust/__main__.py", "Lib/site-packages/conda_content_trust/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/__version__.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/authentication.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/cli.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/common.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/metadata_construction.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/plugin.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/root_signing.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__pycache__/signing.cpython-310.pyc", "Lib/site-packages/conda_content_trust/__version__.py", "Lib/site-packages/conda_content_trust/authentication.py", "Lib/site-packages/conda_content_trust/cli.py", "Lib/site-packages/conda_content_trust/common.py", "Lib/site-packages/conda_content_trust/metadata_construction.py", "Lib/site-packages/conda_content_trust/plugin.py", "Lib/site-packages/conda_content_trust/root_signing.py", "Lib/site-packages/conda_content_trust/signing.py", "Scripts/conda-content-trust-script.py", "Scripts/conda-content-trust.exe"], "fn": "conda-content-trust-0.2.0-py310haa95532_1.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-content-trust-0.2.0-py310haa95532_1", "type": 1}, "md5": "9ce0f57a77311eb3aa6b8dbf7f46c35e", "name": "conda-content-trust", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-content-trust-0.2.0-py310haa95532_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::conda-content-trust==0.2.0=py310haa95532_1[md5=9ce0f57a77311eb3aa6b8dbf7f46c35e]", "sha256": "25692286ed8a6943c14a14f47fa2ae68ef6061ffd41fd1b9543004004c8a9a93", "size": 83652, "subdir": "win-64", "timestamp": 1714483544000, "url": "https://repo.anaconda.com/pkgs/main/win-64/conda-content-trust-0.2.0-py310haa95532_1.conda", "version": "0.2.0"}