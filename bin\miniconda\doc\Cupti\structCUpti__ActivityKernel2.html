<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel2" -->The activity record for kernel. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#bb2b68cdbb218f18820abf88b638e5a7">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#824ff4aeeefbf9b746d8f39d22a96cd1">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#dfe0deee290c171ccd6e84bd6192d5ee">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#92e74324daa8665d573e5014f72ccb8c">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#3d2195b1665d6da8e83a3463f3977bc7">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#aa2420d2c56d463b3428f317b9f86ae2">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#18bf4a19250b8bf7d9d19cebf7f43c3d">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#028451077da650771c5df045f0b555b2">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#5919087d294027fdb577f0a49126a8d4">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#4b65d82e9688fbe1ae1022b5b03b7a02">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#9cd27a855d920a9b47e1df977aab3edd">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#7c93f2dc0c225915feb8b3b74b3c4e84">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#635faef74322db6307988a7e45e3cfea">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#0224258421f5cb560cc21fb820b966a6">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#dc1a70760a7957bfa794ae9b150d61b2">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#a18e593a822ea6e6eec2e24782a45691">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#79a76e374b09d20bfdf2474c6f9fa6b3">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#a4b039cdaf7e6d9bac28d6594d5fa156">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#7ce9734227bbb8f72f0e4ea5ee3d96c9">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#3bf3e0af4293a057012726a11fdc487d">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#26fa72bce71b25d187492a43e188d772">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#e3b43ccafa257038e17520642c294150">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#c8de749b5fa90977bb2b7e493d60dfed">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#a4e77c8d77fd7865212b38c06efe2295">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel2.html#caa17e8a99abc44e2a82ce3563614f00">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="bb2b68cdbb218f18820abf88b638e5a7"></a><!-- doxytag: member="CUpti_ActivityKernel2::blockX" ref="bb2b68cdbb218f18820abf88b638e5a7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#bb2b68cdbb218f18820abf88b638e5a7">CUpti_ActivityKernel2::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="824ff4aeeefbf9b746d8f39d22a96cd1"></a><!-- doxytag: member="CUpti_ActivityKernel2::blockY" ref="824ff4aeeefbf9b746d8f39d22a96cd1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#824ff4aeeefbf9b746d8f39d22a96cd1">CUpti_ActivityKernel2::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="dfe0deee290c171ccd6e84bd6192d5ee"></a><!-- doxytag: member="CUpti_ActivityKernel2::blockZ" ref="dfe0deee290c171ccd6e84bd6192d5ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#dfe0deee290c171ccd6e84bd6192d5ee">CUpti_ActivityKernel2::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="92e74324daa8665d573e5014f72ccb8c"></a><!-- doxytag: member="CUpti_ActivityKernel2::completed" ref="92e74324daa8665d573e5014f72ccb8c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel2.html#92e74324daa8665d573e5014f72ccb8c">CUpti_ActivityKernel2::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="3d2195b1665d6da8e83a3463f3977bc7"></a><!-- doxytag: member="CUpti_ActivityKernel2::contextId" ref="3d2195b1665d6da8e83a3463f3977bc7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel2.html#3d2195b1665d6da8e83a3463f3977bc7">CUpti_ActivityKernel2::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="aa2420d2c56d463b3428f317b9f86ae2"></a><!-- doxytag: member="CUpti_ActivityKernel2::correlationId" ref="aa2420d2c56d463b3428f317b9f86ae2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel2.html#aa2420d2c56d463b3428f317b9f86ae2">CUpti_ActivityKernel2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="18bf4a19250b8bf7d9d19cebf7f43c3d"></a><!-- doxytag: member="CUpti_ActivityKernel2::deviceId" ref="18bf4a19250b8bf7d9d19cebf7f43c3d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel2.html#18bf4a19250b8bf7d9d19cebf7f43c3d">CUpti_ActivityKernel2::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="028451077da650771c5df045f0b555b2"></a><!-- doxytag: member="CUpti_ActivityKernel2::dynamicSharedMemory" ref="028451077da650771c5df045f0b555b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#028451077da650771c5df045f0b555b2">CUpti_ActivityKernel2::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="5919087d294027fdb577f0a49126a8d4"></a><!-- doxytag: member="CUpti_ActivityKernel2::end" ref="5919087d294027fdb577f0a49126a8d4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel2.html#5919087d294027fdb577f0a49126a8d4">CUpti_ActivityKernel2::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="a4e77c8d77fd7865212b38c06efe2295"></a><!-- doxytag: member="CUpti_ActivityKernel2::executed" ref="a4e77c8d77fd7865212b38c06efe2295" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel2.html#a4e77c8d77fd7865212b38c06efe2295">CUpti_ActivityKernel2::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="4b65d82e9688fbe1ae1022b5b03b7a02"></a><!-- doxytag: member="CUpti_ActivityKernel2::gridId" ref="4b65d82e9688fbe1ae1022b5b03b7a02" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel2.html#4b65d82e9688fbe1ae1022b5b03b7a02">CUpti_ActivityKernel2::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="9cd27a855d920a9b47e1df977aab3edd"></a><!-- doxytag: member="CUpti_ActivityKernel2::gridX" ref="9cd27a855d920a9b47e1df977aab3edd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#9cd27a855d920a9b47e1df977aab3edd">CUpti_ActivityKernel2::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="7c93f2dc0c225915feb8b3b74b3c4e84"></a><!-- doxytag: member="CUpti_ActivityKernel2::gridY" ref="7c93f2dc0c225915feb8b3b74b3c4e84" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#7c93f2dc0c225915feb8b3b74b3c4e84">CUpti_ActivityKernel2::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="635faef74322db6307988a7e45e3cfea"></a><!-- doxytag: member="CUpti_ActivityKernel2::gridZ" ref="635faef74322db6307988a7e45e3cfea" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#635faef74322db6307988a7e45e3cfea">CUpti_ActivityKernel2::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="0224258421f5cb560cc21fb820b966a6"></a><!-- doxytag: member="CUpti_ActivityKernel2::kind" ref="0224258421f5cb560cc21fb820b966a6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel2.html#0224258421f5cb560cc21fb820b966a6">CUpti_ActivityKernel2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="dc1a70760a7957bfa794ae9b150d61b2"></a><!-- doxytag: member="CUpti_ActivityKernel2::localMemoryPerThread" ref="dc1a70760a7957bfa794ae9b150d61b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel2.html#dc1a70760a7957bfa794ae9b150d61b2">CUpti_ActivityKernel2::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="a18e593a822ea6e6eec2e24782a45691"></a><!-- doxytag: member="CUpti_ActivityKernel2::localMemoryTotal" ref="a18e593a822ea6e6eec2e24782a45691" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel2.html#a18e593a822ea6e6eec2e24782a45691">CUpti_ActivityKernel2::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="79a76e374b09d20bfdf2474c6f9fa6b3"></a><!-- doxytag: member="CUpti_ActivityKernel2::name" ref="79a76e374b09d20bfdf2474c6f9fa6b3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel2.html#79a76e374b09d20bfdf2474c6f9fa6b3">CUpti_ActivityKernel2::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="a4b039cdaf7e6d9bac28d6594d5fa156"></a><!-- doxytag: member="CUpti_ActivityKernel2::registersPerThread" ref="a4b039cdaf7e6d9bac28d6594d5fa156" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel2.html#a4b039cdaf7e6d9bac28d6594d5fa156">CUpti_ActivityKernel2::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="caa17e8a99abc44e2a82ce3563614f00"></a><!-- doxytag: member="CUpti_ActivityKernel2::requested" ref="caa17e8a99abc44e2a82ce3563614f00" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel2.html#caa17e8a99abc44e2a82ce3563614f00">CUpti_ActivityKernel2::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="7ce9734227bbb8f72f0e4ea5ee3d96c9"></a><!-- doxytag: member="CUpti_ActivityKernel2::reserved0" ref="7ce9734227bbb8f72f0e4ea5ee3d96c9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel2.html#7ce9734227bbb8f72f0e4ea5ee3d96c9">CUpti_ActivityKernel2::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="3bf3e0af4293a057012726a11fdc487d"></a><!-- doxytag: member="CUpti_ActivityKernel2::sharedMemoryConfig" ref="3bf3e0af4293a057012726a11fdc487d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel2.html#3bf3e0af4293a057012726a11fdc487d">CUpti_ActivityKernel2::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="26fa72bce71b25d187492a43e188d772"></a><!-- doxytag: member="CUpti_ActivityKernel2::start" ref="26fa72bce71b25d187492a43e188d772" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel2.html#26fa72bce71b25d187492a43e188d772">CUpti_ActivityKernel2::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="e3b43ccafa257038e17520642c294150"></a><!-- doxytag: member="CUpti_ActivityKernel2::staticSharedMemory" ref="e3b43ccafa257038e17520642c294150" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel2.html#e3b43ccafa257038e17520642c294150">CUpti_ActivityKernel2::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="c8de749b5fa90977bb2b7e493d60dfed"></a><!-- doxytag: member="CUpti_ActivityKernel2::streamId" ref="c8de749b5fa90977bb2b7e493d60dfed" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel2.html#c8de749b5fa90977bb2b7e493d60dfed">CUpti_ActivityKernel2::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
