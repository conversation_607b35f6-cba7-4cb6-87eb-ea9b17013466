{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-profiler-api-12.1.55-0", "features": "", "files": ["LICENSE", "include/cudaProfiler.h", "include/cuda_profiler_api.h"], "fn": "cuda-profiler-api-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-profiler-api-12.1.55-0", "type": 1}, "md5": "4af5a3cff74094be6c69eba29a177e77", "name": "cuda-profiler-api", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-profiler-api-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "include/cudaProfiler.h", "path_type": "hardlink", "sha256": "cb1eb61ea355aac7bd0ba4a4baef987cfd233b1309227fe3b88bb37b58eedadc", "sha256_in_prefix": "cb1eb61ea355aac7bd0ba4a4baef987cfd233b1309227fe3b88bb37b58eedadc", "size_in_bytes": 7235}, {"_path": "include/cuda_profiler_api.h", "path_type": "hardlink", "sha256": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "sha256_in_prefix": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "size_in_bytes": 4700}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 18770, "subdir": "win-64", "timestamp": 1674623637000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-profiler-api-12.1.55-0.tar.bz2", "version": "12.1.55"}