{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cuobjdump-12.1.55-0", "features": "", "files": ["LICENSE", "bin/cuobjdump.exe"], "fn": "cuda-cuobjdump-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cuobjdump-12.1.55-0", "type": 1}, "md5": "8f0fff29b483891b4438f9bdd6118284", "name": "cuda-cuob<PERSON><PERSON><PERSON>", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cuobjdump-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "bin/cuobjdump.exe", "path_type": "hardlink", "sha256": "24762817601e25af1fdbcf878dbb41299c89880792016162d60c0689c818081d", "sha256_in_prefix": "24762817601e25af1fdbcf878dbb41299c89880792016162d60c0689c818081d", "size_in_bytes": 8711168}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 3888794, "subdir": "win-64", "timestamp": 1674622739000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-cuobjdump-12.1.55-0.tar.bz2", "version": "12.1.55"}