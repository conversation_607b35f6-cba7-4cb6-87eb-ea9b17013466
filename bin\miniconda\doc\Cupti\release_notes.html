<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-us" xml:lang="en-us">
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8"></meta>
      <meta http-equiv="X-UA-Compatible" content="IE=edge"></meta>
      <meta name="copyright" content="(C) Copyright 2005"></meta>
      <meta name="DC.rights.owner" content="(C) Copyright 2005"></meta>
      <meta name="DC.Type" content="concept"></meta>
      <meta name="DC.Title" content="Release Notes"></meta>
      <meta name="abstract" content="CUPTI Release Notes."></meta>
      <meta name="description" content=""></meta>
      <meta name="DC.Coverage" content="CUPTI"></meta>
      <meta name="DC.subject" content="Release Notes"></meta>
      <meta name="keywords" content="Release Notes"></meta>
      <meta name="DC.Format" content="XHTML"></meta>
      <meta name="DC.Identifier" content="release_notes"></meta>
      <link rel="stylesheet" type="text/css" href="../common/formatting/commonltr.css"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/site.css"></link>
      <title>CUPTI :: CUPTI Documentation</title>
      <!--[if lt IE 9]>
      <script src="../common/formatting/html5shiv-printshiv.min.js"></script>
      <![endif]-->
      <script type="text/javascript" charset="utf-8" src="../common/scripts/tynt/tynt.js"></script>
      --&gt;
      
      <script src="https://assets.adobedtm.com/5d4962a43b79/c1061d2c5e7b/launch-191c2462b890.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.ba-hashchange.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.scrollintoview.min.js"></script>
      <script type="text/javascript" src="../search/htmlFileList.js"></script>
      <script type="text/javascript" src="../search/htmlFileInfoList.js"></script>
      <script type="text/javascript" src="../search/nwSearchFnt.min.js"></script>
      <script type="text/javascript" src="../search/stemmers/en_stemmer.min.js"></script>
      <script type="text/javascript" src="../search/index-1.js"></script>
      <script type="text/javascript" src="../search/index-2.js"></script>
      <script type="text/javascript" src="../search/index-3.js"></script>
      <link rel="canonical" href="https://docs.nvidia.com/cupti/Cupti/index.html"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/qwcode.highlight.css"></link>
   </head>
   <body>
      
      <header id="header"><span id="company">NVIDIA</span><span id="site-title">CUPTI Documentation</span><form id="search" method="get" action="search">
            <input type="text" name="search-text"></input><fieldset id="search-location">
               <legend>Search In:</legend>
               <label><input type="radio" name="search-type" value="site"></input>Entire Site</label>
               <label><input type="radio" name="search-type" value="document"></input>Just This Document</label></fieldset>
            <button type="reset">clear search</button>
            <button id="submit" type="submit">search</button></form>
      </header>
      <div id="site-content">
         <nav id="site-nav">
            <div class="category closed"><a href="../index.html" title="The root of the site.">CUPTI
                  v12.1</a></div>
            <div class="category"><a href="index.html" title="CUPTI">CUPTI</a></div>
            <ul>
               <li>
                  <div class="section-link"><a href="r_overview.html#r_overview">Overview</a></div>
               </li>
               <li>
                  <div class="section-link"><a href="release_notes.html#release_notes">1.&nbsp;Release Notes</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="release_notes.html#whats-new">1.1.&nbsp;Release Notes</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.1">1.1.1.&nbsp;Updates in CUDA 12.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.0.1">1.1.2.&nbsp;Updates in CUDA 12.0 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.0">1.1.3.&nbsp;Updates in CUDA 12.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.8">1.1.4.&nbsp;Updates in CUDA 11.8</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.7.1">1.1.5.&nbsp;Updates in CUDA 11.7 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.7">1.1.6.&nbsp;Updates in CUDA 11.7</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.6.1">1.1.7.&nbsp;Updates in CUDA 11.6 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.6">1.1.8.&nbsp;Updates in CUDA 11.6</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.5.1">1.1.9.&nbsp;Updates in CUDA 11.5 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.5">1.1.10.&nbsp;Updates in CUDA 11.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.4.1">1.1.11.&nbsp;Updates in CUDA 11.4 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.4">1.1.12.&nbsp;Updates in CUDA 11.4</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.3">1.1.13.&nbsp;Updates in CUDA 11.3</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.2">1.1.14.&nbsp;Updates in CUDA 11.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.1">1.1.15.&nbsp;Updates in CUDA 11.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.0">1.1.16.&nbsp;Updates in CUDA 11.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.2">1.1.17.&nbsp;Updates in CUDA 10.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1.2">1.1.18.&nbsp;Updates in CUDA 10.1 Update 2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1.1">1.1.19.&nbsp;Updates in CUDA 10.1 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1">1.1.20.&nbsp;Updates in CUDA 10.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.0">1.1.21.&nbsp;Updates in CUDA 10.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.2">1.1.22.&nbsp;Updates in CUDA 9.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.1">1.1.23.&nbsp;Updates in CUDA 9.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.0">1.1.24.&nbsp;Updates in CUDA 9.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_8.0">1.1.25.&nbsp;Updates in CUDA 8.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_7.5">1.1.26.&nbsp;Updates in CUDA 7.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_7.0">1.1.27.&nbsp;Updates in CUDA 7.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_6.5">1.1.28.&nbsp;Updates in CUDA 6.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_6.0">1.1.29.&nbsp;Updates in CUDA 6.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_5.5">1.1.30.&nbsp;Updates in CUDA 5.5</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="release_notes.html#known_issues">1.2.&nbsp;Known Issues</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#known_issues_profiling">1.2.1.&nbsp;Profiling</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="release_notes.html#known_issues_event_and_metric">1.2.1.1.&nbsp;Event and Metric API</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="release_notes.html#known_issues_profiling_and_perfworks">1.2.1.2.&nbsp;Profiling and Perfworks Metric API</a></div>
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="release_notes.html#support">1.3.&nbsp;Support</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#platform_support">1.3.1.&nbsp;Platform Support</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#gpu_support">1.3.2.&nbsp;GPU Support</a></div>
                           </li>
                        </ul>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_main.html#r_main">2.&nbsp;Usage</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_compatibility_requirements">2.1.&nbsp;CUPTI Compatibility and Requirements</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_initialization">2.2.&nbsp;CUPTI Initialization</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_activity">2.3.&nbsp;CUPTI Activity API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_source_correlation">2.3.1.&nbsp;SASS Source Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling">2.3.2.&nbsp;PC Sampling</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_nvlink">2.3.3.&nbsp;NVLink</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_openacc">2.3.4.&nbsp;OpenACC</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_graphs">2.3.5.&nbsp;CUDA Graphs</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_ext_correlation">2.3.6.&nbsp;External Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_dynamic_detach">2.3.7.&nbsp;Dynamic Attach and Detach</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_callback_api">2.4.&nbsp;CUPTI Callback API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_driver_runtime_api_callback">2.4.1.&nbsp;Driver and Runtime API Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_resource_callbacks">2.4.2.&nbsp;Resource Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_synchronization_callbacks">2.4.3.&nbsp;Synchronization Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_nvtx_callbacks">2.4.4.&nbsp;NVIDIA Tools Extension Callbacks</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_event_api">2.5.&nbsp;CUPTI Event API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_collecting_kernel_execution_events">2.5.1.&nbsp;Collecting Kernel Execution Events</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_sampling_events">2.5.2.&nbsp;Sampling Events</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_metric_api">2.6.&nbsp;CUPTI Metric API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#metrics-reference">2.6.1.&nbsp;Metrics Reference</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-5x">2.6.1.1.&nbsp;Metrics for Capability 5.x</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-6x">2.6.1.2.&nbsp;Metrics for Capability 6.x</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-7x">2.6.1.3.&nbsp;Metrics for Capability 7.0</a></div>
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_profiler">2.7.&nbsp;CUPTI Profiling API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_multi_pass_collection">2.7.1.&nbsp;Multi Pass Collection</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_range_profiling">2.7.2.&nbsp;Range Profiling</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_profiler_auto_range">2.7.2.1.&nbsp;Auto Range</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_profiler_user_range">2.7.2.2.&nbsp;User Range</a></div>
                                 </li>
                              </ul>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_profiler_definitions">2.7.3.&nbsp;CUPTI Profiler Definitions</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_profiling_missing_features">2.7.4.&nbsp;Differences from event and metric APIs</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_host_metrics_api">2.8.&nbsp;Perfworks Metric API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_host_derived_metrics_api">2.8.1.&nbsp;Derived metrics</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_host_raw_metrics_api">2.8.2.&nbsp;Raw Metrics</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#metrics_map_table_70">2.8.3.&nbsp;Metrics Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#events_map_table_70">2.8.4.&nbsp;Events Mapping Table</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_profiling_migration">2.9.&nbsp;Migration to the Profiling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_pc_sampling_api">2.10.&nbsp;CUPTI PC Sampling API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_config_attrib">2.10.1.&nbsp;Configuration Attributes</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_stall_reasons_mapping">2.10.2.&nbsp;Stall Reasons Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_struct_mapping">2.10.3.&nbsp;Data Structure Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_flush">2.10.4.&nbsp;Data flushing</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_source_correlation">2.10.5.&nbsp;SASS Source Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_usage">2.10.6.&nbsp;API Usage</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_limitations">2.10.7.&nbsp;Limitations</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_checkpoint_api">2.11.&nbsp;CUPTI Checkpoint API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_usage">2.11.1.&nbsp;Usage</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_restrictions">2.11.2.&nbsp;Restrictions</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_samples">2.11.3.&nbsp;Examples</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_overhead">2.12.&nbsp;CUPTI overhead</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_overhead_tracing">2.12.1.&nbsp;Tracing Overhead</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_overhead_tracing_execution">2.12.1.1.&nbsp;Execution overhead</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_overhead_tracing_memory">2.12.1.2.&nbsp;Memory overhead</a></div>
                                 </li>
                              </ul>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_overhead_profiling">2.12.2.&nbsp;Profiling Overhead</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_samples">2.13.&nbsp;Samples</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_library_support.html#r_library_support">3.&nbsp;Library Support</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_library_support.html#r_library_support_optix">3.1.&nbsp;OptiX</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_special_configurations.html#r_special_configurations">4.&nbsp;Special Configurations</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_mig">4.1.&nbsp;Multi-Instance GPU (MIG)</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_vgpu">4.2.&nbsp;NVIDIA Virtual GPU (vGPU)</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_wsl">4.3.&nbsp;Windows Subsystem for Linux (WSL)</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="modules.html#modules">5.&nbsp;Modules</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__RESULT__API">5.1.&nbsp;CUPTI Result Codes</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__VERSION__API">5.2.&nbsp;CUPTI Version</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__ACTIVITY__API">5.3.&nbsp;CUPTI Activity API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__CALLBACK__API">5.4.&nbsp;CUPTI Callback API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__EVENT__API">5.5.&nbsp;CUPTI Event API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__METRIC__API">5.6.&nbsp;CUPTI Metric API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PROFILER__API">5.7.&nbsp;CUPTI Profiling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__CHECKPOINT__API">5.8.&nbsp;CUPTI Checkpoint API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PCSAMPLING__API">5.9.&nbsp;CUPTI PC Sampling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PCSAMPLING__UTILITY">5.10.&nbsp;CUPTI PC Sampling Utility API</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="annotated.html#annotated">6.&nbsp;Data Structures</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="annotated.html#structBufferInfo">6.1.&nbsp;BufferInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams">6.2.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams">6.3.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams">6.4.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams">6.5.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams">6.6.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Activity">6.7.&nbsp;CUpti_Activity</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityAPI">6.8.&nbsp;CUpti_ActivityAPI</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityAutoBoostState">6.9.&nbsp;CUpti_ActivityAutoBoostState</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityBranch">6.10.&nbsp;CUpti_ActivityBranch</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityBranch2">6.11.&nbsp;CUpti_ActivityBranch2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityCdpKernel">6.12.&nbsp;CUpti_ActivityCdpKernel</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityContext">6.13.&nbsp;CUpti_ActivityContext</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityCudaEvent">6.14.&nbsp;CUpti_ActivityCudaEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice">6.15.&nbsp;CUpti_ActivityDevice</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice2">6.16.&nbsp;CUpti_ActivityDevice2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice3">6.17.&nbsp;CUpti_ActivityDevice3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice4">6.18.&nbsp;CUpti_ActivityDevice4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDeviceAttribute">6.19.&nbsp;CUpti_ActivityDeviceAttribute</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEnvironment">6.20.&nbsp;CUpti_ActivityEnvironment</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEvent">6.21.&nbsp;CUpti_ActivityEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEventInstance">6.22.&nbsp;CUpti_ActivityEventInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityExternalCorrelation">6.23.&nbsp;CUpti_ActivityExternalCorrelation</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityFunction">6.24.&nbsp;CUpti_ActivityFunction</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess">6.25.&nbsp;CUpti_ActivityGlobalAccess</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess2">6.26.&nbsp;CUpti_ActivityGlobalAccess2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess3">6.27.&nbsp;CUpti_ActivityGlobalAccess3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGraphTrace">6.28.&nbsp;CUpti_ActivityGraphTrace</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousEvent">6.29.&nbsp;CUpti_ActivityInstantaneousEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousEventInstance">6.30.&nbsp;CUpti_ActivityInstantaneousEventInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousMetric">6.31.&nbsp;CUpti_ActivityInstantaneousMetric</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance">6.32.&nbsp;CUpti_ActivityInstantaneousMetricInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstructionCorrelation">6.33.&nbsp;CUpti_ActivityInstructionCorrelation</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstructionExecution">6.34.&nbsp;CUpti_ActivityInstructionExecution</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityJit">6.35.&nbsp;CUpti_ActivityJit</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel">6.36.&nbsp;CUpti_ActivityKernel</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel2">6.37.&nbsp;CUpti_ActivityKernel2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel3">6.38.&nbsp;CUpti_ActivityKernel3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel4">6.39.&nbsp;CUpti_ActivityKernel4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel5">6.40.&nbsp;CUpti_ActivityKernel5</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel6">6.41.&nbsp;CUpti_ActivityKernel6</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel7">6.42.&nbsp;CUpti_ActivityKernel7</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel8">6.43.&nbsp;CUpti_ActivityKernel8</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel9">6.44.&nbsp;CUpti_ActivityKernel9</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarker">6.45.&nbsp;CUpti_ActivityMarker</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarker2">6.46.&nbsp;CUpti_ActivityMarker2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarkerData">6.47.&nbsp;CUpti_ActivityMarkerData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy">6.48.&nbsp;CUpti_ActivityMemcpy</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy3">6.49.&nbsp;CUpti_ActivityMemcpy3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy4">6.50.&nbsp;CUpti_ActivityMemcpy4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy5">6.51.&nbsp;CUpti_ActivityMemcpy5</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP">6.52.&nbsp;CUpti_ActivityMemcpyPtoP</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP2">6.53.&nbsp;CUpti_ActivityMemcpyPtoP2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP3">6.54.&nbsp;CUpti_ActivityMemcpyPtoP3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP4">6.55.&nbsp;CUpti_ActivityMemcpyPtoP4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory">6.56.&nbsp;CUpti_ActivityMemory</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory2">6.57.&nbsp;CUpti_ActivityMemory2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory3">6.58.&nbsp;CUpti_ActivityMemory3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT">6.59.&nbsp;CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemoryPool">6.60.&nbsp;CUpti_ActivityMemoryPool</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemoryPool2">6.61.&nbsp;CUpti_ActivityMemoryPool2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset">6.62.&nbsp;CUpti_ActivityMemset</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset2">6.63.&nbsp;CUpti_ActivityMemset2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset3">6.64.&nbsp;CUpti_ActivityMemset3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset4">6.65.&nbsp;CUpti_ActivityMemset4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMetric">6.66.&nbsp;CUpti_ActivityMetric</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMetricInstance">6.67.&nbsp;CUpti_ActivityMetricInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityModule">6.68.&nbsp;CUpti_ActivityModule</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityName">6.69.&nbsp;CUpti_ActivityName</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink">6.70.&nbsp;CUpti_ActivityNvLink</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink2">6.71.&nbsp;CUpti_ActivityNvLink2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink3">6.72.&nbsp;CUpti_ActivityNvLink3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink4">6.73.&nbsp;CUpti_ActivityNvLink4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#unionCUpti__ActivityObjectKindId">6.74.&nbsp;CUpti_ActivityObjectKindId</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAcc">6.75.&nbsp;CUpti_ActivityOpenAcc</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccData">6.76.&nbsp;CUpti_ActivityOpenAccData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccLaunch">6.77.&nbsp;CUpti_ActivityOpenAccLaunch</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccOther">6.78.&nbsp;CUpti_ActivityOpenAccOther</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenMp">6.79.&nbsp;CUpti_ActivityOpenMp</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOverhead">6.80.&nbsp;CUpti_ActivityOverhead</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPcie">6.81.&nbsp;CUpti_ActivityPcie</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling">6.82.&nbsp;CUpti_ActivityPCSampling</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling2">6.83.&nbsp;CUpti_ActivityPCSampling2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling3">6.84.&nbsp;CUpti_ActivityPCSampling3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSamplingConfig">6.85.&nbsp;CUpti_ActivityPCSamplingConfig</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo">6.86.&nbsp;CUpti_ActivityPCSamplingRecordInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPreemption">6.87.&nbsp;CUpti_ActivityPreemption</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySharedAccess">6.88.&nbsp;CUpti_ActivitySharedAccess</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySourceLocator">6.89.&nbsp;CUpti_ActivitySourceLocator</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityStream">6.90.&nbsp;CUpti_ActivityStream</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySynchronization">6.91.&nbsp;CUpti_ActivitySynchronization</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter">6.92.&nbsp;CUpti_ActivityUnifiedMemoryCounter</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2">6.93.&nbsp;CUpti_ActivityUnifiedMemoryCounter2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounterConfig">6.94.&nbsp;CUpti_ActivityUnifiedMemoryCounterConfig</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__CallbackData">6.95.&nbsp;CUpti_CallbackData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__EventGroupSet">6.96.&nbsp;CUpti_EventGroupSet</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__EventGroupSets">6.97.&nbsp;CUpti_EventGroupSets</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GetCubinCrcParams">6.98.&nbsp;CUpti_GetCubinCrcParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GetSassToSourceCorrelationParams">6.99.&nbsp;CUpti_GetSassToSourceCorrelationParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GraphData">6.100.&nbsp;CUpti_GraphData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#unionCUpti__MetricValue">6.101.&nbsp;CUpti_MetricValue</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ModuleResourceData">6.102.&nbsp;CUpti_ModuleResourceData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__NvtxData">6.103.&nbsp;CUpti_NvtxData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingConfigurationInfo">6.104.&nbsp;CUpti_PCSamplingConfigurationInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams">6.105.&nbsp;CUpti_PCSamplingConfigurationInfoParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingData">6.106.&nbsp;CUpti_PCSamplingData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingDisableParams">6.107.&nbsp;CUpti_PCSamplingDisableParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingEnableParams">6.108.&nbsp;CUpti_PCSamplingEnableParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetDataParams">6.109.&nbsp;CUpti_PCSamplingGetDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetNumStallReasonsParams">6.110.&nbsp;CUpti_PCSamplingGetNumStallReasonsParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams">6.111.&nbsp;CUpti_PCSamplingGetStallReasonsParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingPCData">6.112.&nbsp;CUpti_PCSamplingPCData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStallReason">6.113.&nbsp;CUpti_PCSamplingStallReason</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStartParams">6.114.&nbsp;CUpti_PCSamplingStartParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStopParams">6.115.&nbsp;CUpti_PCSamplingStopParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__BeginPass__Params">6.116.&nbsp;CUpti_Profiler_BeginPass_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__BeginSession__Params">6.117.&nbsp;CUpti_Profiler_BeginSession_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params">6.118.&nbsp;CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params">6.119.&nbsp;CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params">6.120.&nbsp;CUpti_Profiler_CounterDataImage_Initialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params">6.121.&nbsp;CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImageOptions">6.122.&nbsp;CUpti_Profiler_CounterDataImageOptions</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DeInitialize__Params">6.123.&nbsp;CUpti_Profiler_DeInitialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DeviceSupported__Params">6.124.&nbsp;CUpti_Profiler_DeviceSupported_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DisableProfiling__Params">6.125.&nbsp;CUpti_Profiler_DisableProfiling_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EnableProfiling__Params">6.126.&nbsp;CUpti_Profiler_EnableProfiling_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EndPass__Params">6.127.&nbsp;CUpti_Profiler_EndPass_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EndSession__Params">6.128.&nbsp;CUpti_Profiler_EndSession_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__FlushCounterData__Params">6.129.&nbsp;CUpti_Profiler_FlushCounterData_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params">6.130.&nbsp;CUpti_Profiler_GetCounterAvailability_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__Initialize__Params">6.131.&nbsp;CUpti_Profiler_Initialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__IsPassCollected__Params">6.132.&nbsp;CUpti_Profiler_IsPassCollected_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__SetConfig__Params">6.133.&nbsp;CUpti_Profiler_SetConfig_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__UnsetConfig__Params">6.134.&nbsp;CUpti_Profiler_UnsetConfig_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ResourceData">6.135.&nbsp;CUpti_ResourceData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__SynchronizeData">6.136.&nbsp;CUpti_SynchronizeData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structHeader">6.137.&nbsp;Header</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint">6.138.&nbsp;NV::Cupti::Checkpoint::CUpti_Checkpoint</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structPcSamplingStallReasons">6.139.&nbsp;PcSamplingStallReasons</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="functions.html#functions">7.&nbsp;Data Fields</a></div>
               </li>
               <li>
                  <div class="section-link"><a href="notices-header.html#notices-header">Notices</a></div>
                  <ul></ul>
               </li>
            </ul>
         </nav>
         <div id="resize-nav"></div>
         <nav id="search-results">
            <h2>Search Results</h2>
            <ol></ol>
         </nav>
         
         <div id="contents-container">
            <div id="breadcrumbs-container">
               <div id="breadcrumbs"><a href="r_overview.html" shape="rect">&lt; Previous</a> | <a href="r_main.html" shape="rect">Next &gt;</a></div>
               <div id="release-info">CUPTI
                  (<a href="../pdf/Cupti.pdf">PDF</a>)
                  
                  -
                  
                  v12.1
                  (<a href="https://developer.nvidia.com/cuda-toolkit-archive">older</a>)
                  -
                  Last updated Feb 28, 2023
                  -
                  <a href="mailto:<EMAIL>?subject=CUPTI Documentation Feedback: CUPTI">Send Feedback</a></div>
            </div>
            <article id="contents">
               <div class="topic nested1" id="release_notes"><a name="release_notes" shape="rect">
                     <!-- --></a><h2 class="topictitle2">1.&nbsp;Release Notes</h2>
                  <div class="body conbody">
                     <p class="shortdesc">CUPTI Release Notes.</p>
                     <p class="p">
                        Release notes, including new features and important bug fixes.
                        Supported platforms and GPUs.
                        
                     </p>
                  </div>
                  <div class="topic concept nested1" id="whats-new"><a name="whats-new" shape="rect">
                        <!-- --></a><h3 class="topictitle3">Release Notes</h3>
                     <div class="body conbody"></div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_12.1"><a name="release_notes_12.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.1.&nbsp;Updates in CUDA 12.1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Field <samp class="ph codeph">wsl</samp> is added in the struct <samp class="ph codeph">CUpti_Profiler_DeviceSupported_Params</samp>
                                       to indicate whether Profiling API is supported on Windows Subsystem for Linux (WSL) system or not.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_12.0.1"><a name="release_notes_12.0.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.2.&nbsp;Updates in CUDA 12.0 Update 1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Reduced the host memory overhead by avoiding caching copies of cubin images at the time of
                                       loading CUDA modules. Copies of cubin images are now created only when profiling features that need it are enabled.
                                    </li>
                                    <li class="li">By default CUPTI switches back to the device memory, instead of the pinned host memory, 
                                       for allocation of the profiling buffer for concurrent kernel tracing. This might help in improving the performance 
                                       of the tracing run. Memory location can be controlled using the attribute 
                                       <tt class="ph tt">CUPTI_ACTIVITY_ATTR_MEM_ALLOCATION_TYPE_HOST_PINNED</tt> of the activity attribute enum <tt class="ph tt">CUpti_ActivityAttribute</tt>.
                                    </li>
                                    <li class="li">CUPTI now captures the <samp class="ph codeph">cudaGraphLaunch</samp> API and its kernels when CUPTI is attached 
                                       after the graph is instantiated using the API <samp class="ph codeph">cudaGraphInstantiate</samp> but it is attached before the graph
                                       is launched using the API <samp class="ph codeph">cudaGraphLaunch</samp>. Some data in the kernel record would be missing i.e.
                                       <tt class="ph tt">cacheConfig</tt>, <tt class="ph tt">sharedMemoryExecuted</tt>, <tt class="ph tt">partitionedGlobalCacheRequested</tt>,
                                       <tt class="ph tt">partitionedGlobalCacheExecuted</tt>, <tt class="ph tt">sharedMemoryCarveoutRequested</tt> etc.
                                       This fix requires the matching CUDA driver which ships with the CUDA 12.0 Update 1 release.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_12.0"><a name="release_notes_12.0" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.3.&nbsp;Updates in CUDA 12.0</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Added new fields <samp class="ph codeph">maxPotentialClusterSize</samp> and <samp class="ph codeph">maxActiveClusters</samp> to help in
                                       calculating the cluster occupancy correctly. These fields are valid for devices with compute capability 9.0 and higher.
                                       To accomodate this change, activity record <samp class="ph codeph">CUpti_ActivityKernel8</samp> is deprecated and
                                       replaced by a new activity record <samp class="ph codeph">CUpti_ActivityKernel9</samp>.
                                    </li>
                                    <li class="li">Enhancements for PC Sampling APIs:
                                       
                                       <ul class="ul">
                                          <li class="li">CUPTI creates few worker threads to offload certain operations like decoding of the hardware data to
                                             the CUPTI PC sampling data and correlation of the PC data to the SASS instructions. CUPTI wakes up these
                                             threads periodically. To control the sleep time of the worker threads, a new attribute
                                             <samp class="ph codeph">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_WORKER_THREAD_PERIODIC_SLEEP_SPAN</samp> is added in
                                             the enum <samp class="ph codeph">CUpti_PCSamplingConfigurationAttributeType</samp>.
                                          </li>
                                          <li class="li">Improved error reporting for hardware buffer overflow. When hardware buffer overflows, CUPTI
                                             returns the out of memory error code. And a new field <samp class="ph codeph">hardwareBufferFull</samp> added in the
                                             struct <samp class="ph codeph">CUpti_PCSamplingData</samp> is set to differentiate it from other out of memory cases.
                                             User can either increase the hardware buffer size or flush the hardware buffer at a higher frequency to
                                             avoid overflow.
                                          </li>
                                       </ul>
                                    </li>
                                    <li class="li">Profiling APIs are supported on Windows Subsystem for Linux (WSL) with WSL version 2, NVIDIA display driver
                                       version 525 or higher and Windows 11.
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Removed minor CUDA version from the SONAME of the CUPTI shared library for compatibility reasons.
                                       For example, SONAME of CUPTI library is libcupti.so.12 instead of libcupti.so.12.0 in CUDA 12.0 release.
                                    </li>
                                    <li class="li">Activity kinds <samp class="ph codeph">CUPTI_ACTIVITY_KIND_MARKER</samp> and
                                       <samp class="ph codeph">CUPTI_ACTIVITY_KIND_MARKER_DATA</samp> can be enabled together.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.8"><a name="release_notes_11.8" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.4.&nbsp;Updates in CUDA 11.8</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">CUPTI adds tracing and profiling support for Hopper and Ada Lovelace GPU families.</li>
                                    <li class="li">Added new fields <samp class="ph codeph">clusterX</samp>, <samp class="ph codeph">clusterY</samp>, <samp class="ph codeph">clusterZ</samp> and
                                       <samp class="ph codeph">clusterSchedulingPolicy</samp> to output the Thread Block Cluster dimensions and scheduling
                                       policy. These fields are valid for devices with compute capability 9.0 and higher.
                                       To accomodate this change, activity record <samp class="ph codeph">CUpti_ActivityKernel7</samp> is deprecated and 
                                       replaced by a new activity record <samp class="ph codeph">CUpti_ActivityKernel8</samp>.
                                    </li>
                                    <li class="li">A new activity kind <samp class="ph codeph">CUPTI_ACTIVITY_KIND_JIT</samp> and corresponding activity record
                                       <samp class="ph codeph">CUpti_ActivityJit</samp> are introduced to capture the overhead involved in the JIT (just-in-time)
                                       compilation and caching of the PTX or NVVM IR code to the binary code. New record also provides the information
                                       about the size and path of the compute cache where the binary code is stored.
                                       
                                    </li>
                                    <li class="li">PC Sampling API is supported on Tegra platforms - QNX, Linux (aarch64) and Linux (x86_64) (Drive SDK).</li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved an issue that might cause crash when the size of the device buffer is
                                       changed, using the attribute <samp class="ph codeph">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_SIZE</samp>, after creation of
                                       the CUDA context.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.7.1"><a name="release_notes_11.7.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.5.&nbsp;Updates in CUDA 11.7 Update 1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved an issue for PC Sampling API <samp class="ph codeph">cuptiPCSamplingGetData</samp>
                                       which might not always return all the samples when called after the PC sampling range defined by using
                                       the APIs <samp class="ph codeph">cuptiPCSamplingStart</samp> and <samp class="ph codeph">cuptiPCSamplingStop</samp>.
                                       Remaining samples were delivered in the successive call of the API <samp class="ph codeph">cuptiPCSamplingGetData</samp>
                                       after the next range.
                                    </li>
                                    <li class="li">Disabled tracing of nodes in the CUDA Graph when user enables tracing at the Graph
                                       level using the activity kind <samp class="ph codeph">CUPTI_ACTIVITY_KIND_GRAPH_TRACE</samp>.
                                    </li>
                                    <li class="li">Fixed missing <samp class="ph codeph">channelID</samp> and <samp class="ph codeph">channelType</samp> information
                                       for kernel records. Earlier these fields were populated for CUDA Graph launches only.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.7"><a name="release_notes_11.7" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.6.&nbsp;Updates in CUDA 11.7</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">A new activity kind <samp class="ph codeph">CUPTI_ACTIVITY_KIND_GRAPH_TRACE</samp> and activity record
                                       <samp class="ph codeph">CUpti_ActivityGraphTrace</samp> are introduced to represent the execution for a graph without
                                       giving visibility about the execution of its nodes. This is intended to reduce overheads involved in
                                       tracing each node separately. This activity can only be enabled for drivers of version 515 and above.
                                    </li>
                                    <li class="li">A new API <samp class="ph codeph">cuptiActivityEnableAndDump</samp> is added to provide snapshot of certain activities
                                       like device, context, stream, NVLink and PCIe at any point during the profiling session.
                                    </li>
                                    <li class="li">Added sample <a class="xref" href="r_main.html#r_samples__cupti_correlation" shape="rect">cupti_correlation</a>
                                       to show correlation between CUDA APIs and corresponding GPU activities.
                                    </li>
                                    <li class="li">Added sample <a class="xref" href="r_main.html#r_samples__cupti_trace_injection" shape="rect">cupti_trace_injection</a>
                                       to show how to build an injection library using the activity and callback APIs which can be used to trace
                                       any CUDA application.
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Fixed corruption in the function name for PC Sampling API records.</li>
                                    <li class="li">Fixed incorrect timestamps for GPU activities when user calls the API
                                       <samp class="ph codeph">cuptiActivityRegisterTimestampCallback</samp> in the late CUPTI attach scenario.
                                    </li>
                                    <li class="li">Fixed incomplete records for device to device memcopies in the late CUPTI attach scenario.
                                       This issue manifests mainly when application has a mix of CUDA graph and normal kernel launches.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.6.1"><a name="release_notes_11.6.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.7.&nbsp;Updates in CUDA 11.6 Update 1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Fixed hang for the PC Sampling API <samp class="ph codeph">cuptiPCSamplingStop</samp>. This issue
                                       is seen for the PC sampling start and stop resulting in generation of large number of sampling records.
                                    </li>
                                    <li class="li">Fixed timing issue for specific device to device memcpy operations.</li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.6"><a name="release_notes_11.6" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.8.&nbsp;Updates in CUDA 11.6</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Two new fields <samp class="ph codeph">channelID</samp> and <samp class="ph codeph">channelType</samp> are added in the activity records
                                       for kernel, memcpy, peer-to-peer memcpy and memset to output the ID and type of the hardware channel on which
                                       these activities happen.
                                       Activity records <tt class="ph tt">CUpti_ActivityKernel6</tt>, <tt class="ph tt">CUpti_ActivityMemcpy4</tt>, <tt class="ph tt">CUpti_ActivityMemcpyPtoP3</tt>
                                       and <tt class="ph tt">CUpti_ActivityMemset3</tt> are deprecated and replaced by new activity records <tt class="ph tt">CUpti_ActivityKernel7</tt>,
                                       <tt class="ph tt">CUpti_ActivityMemcpy5</tt>, <tt class="ph tt">CUpti_ActivityMemcpyPtoP4</tt> and <tt class="ph tt">CUpti_ActivityMemset4</tt>.
                                       
                                    </li>
                                    <li class="li">New fields <samp class="ph codeph">isMigEnabled</samp>, <samp class="ph codeph">gpuInstanceId</samp>, <samp class="ph codeph">computeInstanceId</samp> and
                                       <samp class="ph codeph">migUuid</samp> are added in the device activity record to provide MIG information for the MIG enabled GPU.
                                       Activity record <samp class="ph codeph">CUpti_ActivityDevice3</samp> is deprecated and replaced by a new activity record
                                       <samp class="ph codeph">CUpti_ActivityDevice4</samp>.
                                    </li>
                                    <li class="li">A new field <samp class="ph codeph">utilizedSize</samp> is added in the memory pool and memory activity record to provide the utilized size
                                       of the memory pool. Activity record <samp class="ph codeph">CUpti_ActivityMemoryPool</samp> and <samp class="ph codeph">CUpti_ActivityMemory2</samp> are deprecated
                                       and replaced by a new activity record <samp class="ph codeph">CUpti_ActivityMemoryPool2</samp> and <samp class="ph codeph">CUpti_ActivityMemory3</samp> respectively.
                                    </li>
                                    <li class="li">API <samp class="ph codeph">cuptiActivityRegisterTimestampCallback</samp> and callback function <samp class="ph codeph">CUpti_TimestampCallbackFunc</samp>
                                       are added to register a callback function to obtain timestamp of user's choice instead of using CUPTI provided timestamp
                                       in activity records.
                                       
                                    </li>
                                    <li class="li">Profiling API supports profiling OptiX application.</li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Fixed multi-pass metric collection using the Profiling API in the auto range and kernel replay mode
                                       for Cuda Graph.
                                    </li>
                                    <li class="li">Fixed the performance issue for the PC sampling API <samp class="ph codeph">cuptiPCSamplingStop</samp>.
                                    </li>
                                    <li class="li">Fixed corruption in variable names for OpenACC activity records.</li>
                                    <li class="li">Fixed corruption in the fields of the struct <samp class="ph codeph">memoryPoolConfig</samp> in the activity record <samp class="ph codeph">CUpti_ActivityMemory3</samp>.
                                    </li>
                                    <li class="li">Filled the fields of the struct <samp class="ph codeph">memoryPoolConfig</samp> in the activity record <samp class="ph codeph">CUpti_ActivityMemory3</samp>
                                       when a memory pointer allocated via memory pool is released using <samp class="ph codeph">cudaFree</samp> CUDA API.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.5.1"><a name="release_notes_11.5.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.9.&nbsp;Updates in CUDA 11.5 Update 1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved an issue that causes incorrect range name for NVTX event attributes.
                                       The issue was introduced in CUDA 11.4.
                                    </li>
                                    <li class="li">Made NVTX initialization APIs <samp class="ph codeph">InitializeInjectionNvtx</samp> and
                                       <samp class="ph codeph">InitializeInjectionNvtx2</samp> thread-safe.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.5"><a name="release_notes_11.5" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.10.&nbsp;Updates in CUDA 11.5</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">A new API <samp class="ph codeph">cuptiProfilerDeviceSupported</samp> is introduced to expose overall Profiling API
                                       support and specific requirements for a given device. Profiling API must be initialized by calling
                                       <samp class="ph codeph">cuptiProfilerInitialize</samp> before testing device support.
                                    </li>
                                    <li class="li">PC Sampling struct <samp class="ph codeph">CUpti_PCSamplingData</samp> introduces a new field <samp class="ph codeph">nonUsrKernelsTotalSamples</samp>
                                       to provide information about the number of samples collected for all non-user kernels.
                                    </li>
                                    <li class="li">Activity record <samp class="ph codeph">CUpti_ActivityDevice2</samp> for device information has been deprecated and
                                       replaced by a new activity record <samp class="ph codeph">CUpti_ActivityDevice3</samp>. New record adds a flag <samp class="ph codeph">isCudaVisible</samp>
                                       to indicate whether device is visible to CUDA.
                                    </li>
                                    <li class="li">Activity record <samp class="ph codeph">CUpti_ActivityNvLink3</samp> for NVLink information has been deprecated and
                                       replaced by a new activity record <samp class="ph codeph">CUpti_ActivityNvLink4</samp>. New record can accommodate NVLink port
                                       information upto a maximum of 32 ports.
                                    </li>
                                    <li class="li">A new <a class="xref" href="r_main.html#r_checkpoint_api" shape="rect">CUPTI Checkpoint API</a> is introduced, enabling automatic saving
                                       and restoring of device state, and facilitating development of kernel replay tools. This is helpful for User
                                       Replay mode of the CUPTI Profiling API, but is not limited to use with CUPTI.
                                    </li>
                                    <li class="li">Tracing is supported on the Windows Subsystem for Linux version 2 (WSL 2).</li>
                                    <li class="li">CUPTI is not supported on NVIDIA Crypto Mining Processors (CMP). A new error code
                                       <samp class="ph codeph">CUPTI_ERROR_CMP_DEVICE_NOT_SUPPORTED</samp> is introduced to indicate it.
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved an issue that causes crash for tracing of device to device memcopy operations.</li>
                                    <li class="li">Resolved an issue that causes crash for OpenACC activity when it is enabled before other activities.</li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.4.1"><a name="release_notes_11.4.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.11.&nbsp;Updates in CUDA 11.4 Update 1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved serialization of CUDA Graph launches for applications which use multiple
                                       threads to launch work.
                                    </li>
                                    <li class="li">Previously, for applications that use CUDA Dynamic Parallelism (CDP), CUPTI detects the
                                       presence of the CDP kernels in the CUDA module. Even if CDP kernels are not called, it fails to trace
                                       the application. There is a change in the behavior, CUPTI now traces all the host launched kernels until
                                       it encounters a host launched kernel which launches child kernels. Subsequent kernels are not traced.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.4"><a name="release_notes_11.4" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.12.&nbsp;Updates in CUDA 11.4</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Profiling APIs support profiling of the CUDA kernel nodes launched by a CUDA Graph.
                                       Auto range profiling with kernel replay mode and user range profiling with user replay and
                                       application replay modes are supported. Other combinations of range profiling and replay modes
                                       are not supported.
                                    </li>
                                    <li class="li">Added support for tracing and profiling on
                                       <a class="xref" href="https://www.nvidia.com/en-us/data-center/virtual-gpu-technology/" target="_blank" shape="rect">NVIDIA virtual GPUs</a>
                                       (vGPUs) on an upcoming GRID/vGPU release.
                                    </li>
                                    <li class="li">Added sample <a class="xref" href="r_main.html#r_samples__profiling_injection" shape="rect">profiling_injection</a>
                                       to show how to build injection library using the Profiling API.
                                    </li>
                                    <li class="li">Added sample <a class="xref" href="r_main.html#r_samples__concurrent_profiling" shape="rect">concurrent_profiling</a>
                                       to show how to retain the kernel concurrency across streams and devices using the Profiling API.
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved the issue of not tracing the device to device memcopy nodes in a CUDA Graph.</li>
                                    <li class="li">Fixed the issue of reporting zero size for local memory pool for mempool creation record.</li>
                                    <li class="li">Resolved the issue of non-collection of samples for the default CUDA context for PC Sampling API.</li>
                                    <li class="li">Enabled tracking of all domains and registered strings in NVTX irrespective of
                                       whether the NVTX activity kind or callbacks are enabled. This state tracking is needed for proper working of the tool
                                       which creates these NVTX objects before enabling the NVTX activity kind or callback.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.3"><a name="release_notes_11.3" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.13.&nbsp;Updates in CUDA 11.3</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">A new set of CUPTI APIs for PC sampling data collection are provided in the header file <tt class="ph tt">cupti_pcsampling.h</tt>
                                       which support continuous mode data collection without serializing kernel execution and have a lower runtime overhead.
                                       Along with these a utility library is provided in the header file <tt class="ph tt">cupti_pcsampling_util.h</tt> which has APIs for GPU
                                       assembly to CUDA-C source correlation and for reading and writing the PC sampling data from/to files.
                                       Refer to the section <a class="xref" href="r_main.html#r_pc_sampling_api" shape="rect">CUPTI PC Sampling API</a> for more details.
                                    </li>
                                    <li class="li">Enum <tt class="ph tt">CUpti_PcieGen</tt> is extended to include PCIe Gen 5.
                                    </li>
                                    <li class="li">The following functions are deprecated and will be removed in a future release:
                                       
                                       <ul class="ul">
                                          <li class="li">Struct <tt class="ph tt">NVPA_MetricsContext</tt> and related APIs <tt class="ph tt">NVPW_MetricsContext_*</tt> from the header <tt class="ph tt">nvperf_host.h</tt>.
                                             It is recommended to use the struct <tt class="ph tt">NVPW_MetricsEvaluator</tt> and related APIs <tt class="ph tt">NVPW_MetricsEvaluator_*</tt>
                                             instead. Profiling API samples have been updated to show how to use these APIs.
                                          </li>
                                          <li class="li"><tt class="ph tt">cuptiDeviceGetTimestamp</tt> from the header <tt class="ph tt">cupti_events.h</tt>.
                                          </li>
                                       </ul>
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Overhead reduction for tracing of CUDA memcopies.</li>
                                    <li class="li">To provide normalized timestamps for all activities, CUPTI uses linear interpolation for
                                       conversion from GPU timestamps to CPU timestamps. This method can cause spurious gaps or overlap on the timeline.
                                       CUPTI improves the conversion function to provide more precise timestamps.
                                    </li>
                                    <li class="li">Generate overhead activity record for semaphore pool allocation.</li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.2"><a name="release_notes_11.2" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.14.&nbsp;Updates in CUDA 11.2</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">A new activity kind <tt class="ph tt">CUPTI_ACTIVITY_KIND_MEMORY_POOL</tt> and activity record <tt class="ph tt">CUpti_ActivityMemoryPool</tt>
                                       are introduced to represent the creation, destruction and trimming of a memory pool. Enum <tt class="ph tt">CUpti_ActivityMemoryPoolType</tt>
                                       lists types of memory pool.
                                    </li>
                                    <li class="li">A new activity kind <tt class="ph tt">CUPTI_ACTIVITY_KIND_MEMORY2</tt> and activity record <tt class="ph tt">CUpti_ActivityMemory2</tt>
                                       are introduced to provide separate records for memory allocation and release operations. This helps in correlation
                                       of records of these operations to the corresponding CUDA APIs, which otherwise is not possible using the existing
                                       activity record <tt class="ph tt">CUpti_ActivityMemory</tt> which provides a single record for both the memory operations.
                                    </li>
                                    <li class="li">Added a new pointer field of type <tt class="ph tt">CUaccessPolicyWindow</tt> in the kernel activity record to provide the
                                       access policy window which specifies a contiguous region of global memory and a persistence property in the L2 cache
                                       for accesses within that region. To accomodate this change, activity record <tt class="ph tt">CUpti_ActivityKernel5</tt> is
                                       deprecated and replaced by a new activity record <tt class="ph tt">CUpti_ActivityKernel6</tt>.
                                       This attribute is not collected by default. To control the collection of launch attributes, a new API 
                                       <tt class="ph tt">cuptiActivityEnableLaunchAttributes</tt> is introdcued.
                                    </li>
                                    <li class="li">New attributes <tt class="ph tt">CUPTI_ACTIVITY_ATTR_DEVICE_BUFFER_PRE_ALLOCATE_VALUE</tt> and
                                       <tt class="ph tt">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_PRE_ALLOCATE_VALUE</tt> are added in the activity attribute
                                       enum <tt class="ph tt">CUpti_ActivityAttribute</tt> to set and get the number of device buffers and profiling semaphore pools
                                       which are preallocated for the context.
                                    </li>
                                    <li class="li">CUPTI now allocates profiling buffer for concurrent kernel tracing in the pinned host memory in place of
                                       device memory. This might help in improving the performance of the tracing run. Memory location can be controlled
                                       using the attribute <tt class="ph tt">CUPTI_ACTIVITY_ATTR_MEM_ALLOCATION_TYPE_HOST_PINNED</tt> of the activity attribute enum
                                       <tt class="ph tt">CUpti_ActivityAttribute</tt>.
                                    </li>
                                    <li class="li">The compiler generated line information for inlined functions is improved due to which CUPTI can associate
                                       inlined functions with the line information of the function call site that has been inlined.
                                    </li>
                                    <li class="li">Removed support for NVLink performance metrics (<samp class="ph codeph">nvlrx__*</samp> and <samp class="ph codeph">nvltx__*</samp>) from
                                       the Profiling API due to a potential application hang during data collection. The metrics will be added back in a
                                       future CUDA release.
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Execution overheads introduced by CUPTI in the tracing path is reduced.</li>
                                    <li class="li">For the concurrent kernel activity kind <tt class="ph tt">CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL</tt>,
                                       CUPTI instruments the kernel code to collect the timing information. Previously, every kernel in the CUDA module
                                       was instrumented, thus the overhead is proportional to the number of different kernels in the module. This is a
                                       static overhead which happens at the time of loading the CUDA module.
                                       To reduce this overhead, kernels are not instrumented at the module load time, instead a single
                                       instrumentation code is generated at the time of loading the CUDA module and it is applied to each kernel during
                                       the kernel execution, thus avoiding most of the static overhead at the CUDA module load time.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.1"><a name="release_notes_11.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.15.&nbsp;Updates in CUDA 11.1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">CUPTI adds tracing and profiling support for the NVIDIA Ampere GPUs with compute capability 8.6.</li>
                                    <li class="li">Added a new field <tt class="ph tt">graphId</tt> in the activity records for kernel, memcpy, peer-to-peer memcpy and memset
                                       to output the unique ID of the CUDA graph that launches the activity through CUDA graph APIs.
                                       To accomodate this change, activity records <tt class="ph tt">CUpti_ActivityMemcpy3</tt>, <tt class="ph tt">CUpti_ActivityMemcpyPtoP2</tt> and
                                       <tt class="ph tt">CUpti_ActivityMemset2</tt> are deprecated and replaced by new activity records <tt class="ph tt">CUpti_ActivityMemcpy4</tt>,
                                       <tt class="ph tt">CUpti_ActivityMemcpyPtoP3</tt> and <tt class="ph tt">CUpti_ActivityMemset3</tt>.
                                       And kernel activity record <tt class="ph tt">CUpti_ActivityKernel5</tt> replaces the padding field with <tt class="ph tt">graphId</tt>.
                                       Added a new API <tt class="ph tt">cuptiGetGraphId</tt> to query the unique ID of the CUDA graph.
                                    </li>
                                    <li class="li">Added a new API <tt class="ph tt">cuptiActivityFlushPeriod</tt> to set the flush period for the worker thread.
                                    </li>
                                    <li class="li">Added support for profiling cooperative kernels using Profiling APIs.</li>
                                    <li class="li">Added NVLink performance metrics (nvlrx__* and nvltx__*) using the Profiling APIs. These metrics are available
                                       on devices with compute capability 7.0, 7.5 and 8.0, and these can be collected at the context level.
                                       Refer to the table <a class="xref" href="r_main.html#metrics_map_table_70" shape="rect">Metrics Mapping Table</a> for mapping between earlier CUPTI metrics and the
                                       Perfworks NVLink metrics for devices with compute capability 7.0.
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved an issue that causes CUPTI to not return full and completed activity buffers for a long
                                       time, CUPTI now attempts to return buffers early.
                                    </li>
                                    <li class="li">To reduce the runtime overhead, CUPTI wakes up the worker thread based on certain heuristics
                                       instead of waking it up at a regular interval.
                                       New API <tt class="ph tt">cuptiActivityFlushPeriod</tt> can be used to control the flush period of the worker thread.
                                       This setting overrides the CUPTI heurtistics.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_11.0"><a name="release_notes_11.0" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.16.&nbsp;Updates in CUDA 11.0</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">CUPTI adds tracing and profiling support for devices with compute capability 8.0 i.e. NVIDIA A100 GPUs
                                       and systems that are based on A100.
                                    </li>
                                    <li class="li">Enhancements for CUDA Graph:
                                       
                                       <ul class="ul">
                                          <li class="li">Support to correlate the CUDA Graph node with the GPU activities: kernel, memcpy, memset.
                                             
                                             <ul class="ul">
                                                <li class="li">Added a new field <tt class="ph tt">graphNodeId</tt> for Node Id in the activity records for kernel, memcpy, memset and P2P transfers.
                                                   Activity records <tt class="ph tt">CUpti_ActivityKernel4</tt>, <tt class="ph tt">CUpti_ActivityMemcpy2</tt>, <tt class="ph tt">CUpti_ActivityMemset</tt> and
                                                   <tt class="ph tt">CUpti_ActivityMemcpyPtoP</tt> are deprecated and replaced by new activity records <tt class="ph tt">CUpti_ActivityKernel5</tt>,
                                                   <tt class="ph tt">CUpti_ActivityMemcpy3</tt>, <tt class="ph tt">CUpti_ActivityMemset2</tt> and <tt class="ph tt">CUpti_ActivityMemcpyPtoP2</tt>.
                                                </li>
                                                <li class="li"><tt class="ph tt">graphNodeId</tt> is the unique ID for the graph node.
                                                </li>
                                                <li class="li"><tt class="ph tt">graphNodeId</tt> can be queried using the new CUPTI API <tt class="ph tt">cuptiGetGraphNodeId()</tt>.
                                                </li>
                                                <li class="li">Callback <tt class="ph tt">CUPTI_CBID_RESOURCE_GRAPHNODE_CREATED</tt> is issued between a pair of the API enter and exit callbacks.
                                                </li>
                                             </ul>
                                          </li>
                                          <li class="li">Introduced new callback <tt class="ph tt">CUPTI_CBID_RESOURCE_GRAPHNODE_CLONED</tt> to indicate the cloning of the CUDA Graph node.
                                          </li>
                                          <li class="li">Retain CUDA driver performance optimization in case memset node is sandwiched between kernel nodes.
                                             CUPTI no longer disables the conversion of memset nodes into kernel nodes for CUDA graphs.
                                          </li>
                                          <li class="li">Added support for cooperative kernels in CUDA graphs.</li>
                                       </ul>
                                    </li>
                                    <li class="li">Added support to trace Optix applications. Refer the <a class="xref" href="r_library_support.html#r_library_support_optix" shape="rect">Optix Profiling</a> section.
                                    </li>
                                    <li class="li">CUPTI overhead is associated with the thread rather than process. Object kind of the overhead record
                                       <tt class="ph tt">CUpti_ActivityOverhead</tt> is switched to <tt class="ph tt">CUPTI_ACTIVITY_OBJECT_THREAD</tt>.
                                    </li>
                                    <li class="li">Added error code <tt class="ph tt">CUPTI_ERROR_MULTIPLE_SUBSCRIBERS_NOT_SUPPORTED</tt> to indicate the presense of another
                                       CUPTI subscriber. API <tt class="ph tt">cuptiSubscribe()</tt> returns the new error code than <tt class="ph tt">CUPTI_ERROR_MAX_LIMIT_REACHED</tt>.
                                    </li>
                                    <li class="li">Added a new enum <tt class="ph tt">CUpti_FuncShmemLimitConfig</tt> to indicate whether user has opted in for maximun dynamic shared memory size
                                       on devices with compute capability 7.x by using function attributes <tt class="ph tt">CU_FUNC_ATTRIBUTE_MAX_DYNAMIC_SHARED_SIZE_BYTES</tt>
                                       or <tt class="ph tt">cudaFuncAttributeMaxDynamicSharedMemorySize</tt> with CUDA driver and runtime respectively.
                                       Field <tt class="ph tt">shmemLimitConfig</tt> in the kernel activity record <tt class="ph tt">CUpti_ActivityKernel5</tt> shows the user choice.
                                       This helps in correct occupancy calulation. Value <tt class="ph tt">FUNC_SHMEM_LIMIT_OPTIN</tt> in the enum <tt class="ph tt">cudaOccFuncShmemConfig</tt>
                                       is the corresponding option in the CUDA occupancy calculator.
                                    </li>
                                 </ul><strong class="ph b">Resolved Issues</strong><ul class="ul">
                                    <li class="li">Resolved an issue that causes incorrect or stale timing for memcopy and serial kernel activities.</li>
                                    <li class="li">Overhead for PC Sampling Activity APIs is reduced by avoiding the reconfiguration of the GPU when PC sampling period doesn't
                                       change between successive kernels. This is applicable for devices with compute capability 7.0 and higher.
                                    </li>
                                    <li class="li">Fixed issues in the API <tt class="ph tt">cuptiFinalize()</tt> including the issue which may cause the application to crash.
                                       This API provides ability for safe and full detach of CUPTI during the execution of the application.
                                       More details in the section <a class="xref" href="r_main.html#r_dynamic_detach" shape="rect">Dynamic Detach</a>.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_10.2"><a name="release_notes_10.2" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.17.&nbsp;Updates in CUDA 10.2</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">CUPTI allows tracing features for non-root and non-admin users on desktop platforms.
                                       Note that events and metrics profiling is still restricted for non-root and non-admin users.
                                       More details about the issue and the solutions can be found on this
                                       <a class="xref" href="https://developer.nvidia.com/nvidia-development-tools-solutions-ERR_NVGPUCTRPERM-permission-issue-performance-counters" target="_blank" shape="rect">web page</a>.
                                    </li>
                                    <li class="li">CUPTI no longer turns off the performance characteristics of CUDA Graph when tracing
                                       the application.
                                    </li>
                                    <li class="li">CUPTI now shows memset nodes in the CUDA graph.</li>
                                    <li class="li">Fixed the incorrect timing issue for the asynchronous cuMemset/cudaMemset activity.</li>
                                    <li class="li">Several performance improvements are done in the tracing path.</li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_10.1.2"><a name="release_notes_10.1.2" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.18.&nbsp;Updates in CUDA 10.1 Update 2</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">This release is focused on bug fixes and stability of the CUPTI.</li>
                                    <li class="li">A security vulnerability issue required profiling tools to disable all the features
                                       for non-root or non-admin users. As a result, CUPTI cannot profile the application when
                                       using a Windows 419.17 or Linux 418.43 or later driver. More details about the issue
                                       and the solutions can be found on this
                                       <a class="xref" href="https://developer.nvidia.com/nvidia-development-tools-solutions-ERR_NVGPUCTRPERM-permission-issue-performance-counters" target="_blank" shape="rect">web page</a>.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_10.1.1"><a name="release_notes_10.1.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.19.&nbsp;Updates in CUDA 10.1 Update 1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Support for the IBM POWER platform is added for the
                                       
                                       <ul class="ul">
                                          <li class="li">Profiling APIs in the header <tt class="ph tt">cupti_profiler_target.h</tt></li>
                                          <li class="li">Perfworks metric APIs in the headers <tt class="ph tt">nvperf_host.h</tt> and <tt class="ph tt">nvperf_target.h</tt></li>
                                       </ul>
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_10.1"><a name="release_notes_10.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.20.&nbsp;Updates in CUDA 10.1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">This release is focused on bug fixes and performance improvements.
                                       
                                    </li>
                                    <li class="li">The new set of profiling APIs and Perfworks metric APIs which were introduced
                                       in the CUDA Toolkit 10.0 are now integrated into the CUPTI library distributed in
                                       the CUDA Toolkit. Refer to the sections
                                       <a class="xref" href="r_main.html#r_profiler" shape="rect">CUPTI Profiling API</a> and
                                       <a class="xref" href="r_main.html#r_host_metrics_api" shape="rect">Perfworks Metric APIs</a> for documentation
                                       of the new APIs.
                                       
                                    </li>
                                    <li class="li">Event collection mode <tt class="ph tt">CUPTI_EVENT_COLLECTION_MODE_CONTINUOUS</tt> is now
                                       supported on all device classes including Geforce and Quadro.
                                       
                                    </li>
                                    <li class="li">Support for the NVTX string registration API <tt class="ph tt">nvtxDomainRegisterStringA().</tt></li>
                                    <li class="li">Added enum <tt class="ph tt">CUpti_PcieGen</tt> to list PCIe generations.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_10.0"><a name="release_notes_10.0" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.21.&nbsp;Updates in CUDA 10.0</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Added tracing support for devices with compute capability 7.5.</li>
                                    <li class="li">A new set of metric APIs are added for devices with compute capability 7.0 and higher.
                                       These provide low and deterministic profiling overhead on the target system. These APIs
                                       are currently supported only on Linux x86 64-bit and Windows 64-bit platforms.
                                       Refer to the <a class="xref" href="https://developer.nvidia.com/cupti" target="_blank" shape="rect">
                                          CUPTI web page</a> for documentation and details to download the package with support for
                                       these new APIs.
                                       Note that both the old and new metric APIs are supported for compute capability 7.0. This is
                                       to enable transition of code to the new metric APIs. But one cannot mix the usage of the old
                                       and new metric APIs.
                                       
                                    </li>
                                    <li class="li">CUPTI supports profiling of OpenMP applications. OpenMP profiling
                                       information is provided in the form of new activity records <tt class="ph tt">CUpti_ActivityOpenMp</tt>.
                                       New API <tt class="ph tt">cuptiOpenMpInitialize</tt> is used to initialize profiling for supported
                                       OpenMP runtimes.
                                       
                                    </li>
                                    <li class="li">Activity record for kernel <tt class="ph tt">CUpti_ActivityKernel4</tt> provides shared memory size set
                                       by the CUDA driver.
                                       
                                    </li>
                                    <li class="li">Tracing support for CUDA kernels, memcpy and memset nodes launched by a CUDA Graph.
                                       
                                    </li>
                                    <li class="li">Added support for resource callbacks for resources associated with the CUDA Graph.
                                       Refer enum <tt class="ph tt">CUpti_CallbackIdResource</tt> for new callback IDs.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_9.2"><a name="release_notes_9.2" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.22.&nbsp;Updates in CUDA 9.2</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Added support to query PCI devices information which can be used to construct the PCIe topology.
                                       See activity kind <tt class="ph tt">CUPTI_ACTIVITY_KIND_PCIE</tt> and related activity record <tt class="ph tt">CUpti_ActivityPcie</tt>.
                                       
                                    </li>
                                    <li class="li">To view and analyze bandwidth of memory transfers over PCIe topologies, new set of metrics
                                       to collect total data bytes transmitted and recieved through PCIe are added. Those give
                                       accumulated count for all devices in the system. These metrics are collected at the device level
                                       for the entire application. And those are made available for devices with compute capability 5.2
                                       and higher.
                                       
                                    </li>
                                    <li class="li">CUPTI added support for new metrics:
                                       
                                       <ul class="ul">
                                          <li class="li">Instruction executed for different types of load and store</li>
                                          <li class="li">Total number of cached global/local load requests from SM to texture cache</li>
                                          <li class="li">Global atomic/non-atomic/reduction bytes written to L2 cache from texture cache</li>
                                          <li class="li">Surface atomic/non-atomic/reduction bytes written to L2 cache from texture cache</li>
                                          <li class="li">Hit rate at L2 cache for all requests from texture cache</li>
                                          <li class="li">Device memory (DRAM) read and write bytes</li>
                                          <li class="li">The utilization level of the multiprocessor function units that execute tensor core
                                             instructions for devices with compute capability 7.0
                                          </li>
                                       </ul>
                                    </li>
                                    <li class="li">A new attribute <tt class="ph tt">CUPTI_EVENT_ATTR_PROFILING_SCOPE</tt> is added under
                                       enum <tt class="ph tt">CUpti_EventAttribute</tt> to query the profiling scope of a event.
                                       Profiling scope indicates if the event can be collected at the context
                                       level or device level or both. See Enum <tt class="ph tt">CUpti_EventProfilingScope</tt> for
                                       avaiable profiling scopes.
                                       
                                    </li>
                                    <li class="li">A new error code <tt class="ph tt">CUPTI_ERROR_VIRTUALIZED_DEVICE_NOT_SUPPORTED</tt> is added to
                                       indicate that tracing and profiling on virtualized GPU is not supported.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_9.1"><a name="release_notes_9.1" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.23.&nbsp;Updates in CUDA 9.1</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Added a field for correlation ID in the activity record
                                       <tt class="ph tt">CUpti_ActivityStream</tt>.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_9.0"><a name="release_notes_9.0" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.24.&nbsp;Updates in CUDA 9.0</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">CUPTI extends tracing and profiling support for devices with compute
                                       capability 7.0.
                                       
                                    </li>
                                    <li class="li">Usage of compute device memory can be tracked through CUPTI. A new activity record
                                       <tt class="ph tt">CUpti_ActivityMemory</tt> and activity kind <tt class="ph tt">CUPTI_ACTIVITY_KIND_MEMORY</tt> are
                                       added to track the allocation and freeing of memory. This activity record includes
                                       fields like virtual base address, size, PC (program counter), timestamps for memory
                                       allocation and free calls.
                                       
                                    </li>
                                    <li class="li">Unified memory profiling adds new events for thrashing, throttling, remote map and
                                       device-to-device migration on 64 bit Linux platforms. New events are added
                                       under enum <tt class="ph tt">CUpti_ActivityUnifiedMemoryCounterKind</tt>.
                                       Enum <tt class="ph tt">CUpti_ActivityUnifiedMemoryRemoteMapCause</tt> lists possible causes for remote map events.
                                       
                                    </li>
                                    <li class="li">PC sampling supports wide range of sampling periods ranging from 2^5 cycles to 2^31 cycles
                                       per sample. This can be controlled through new field <tt class="ph tt">samplingPeriod2</tt> in the PC sampling
                                       configuration struct <tt class="ph tt">CUpti_ActivityPCSamplingConfig</tt>.
                                       
                                    </li>
                                    <li class="li">Added API <tt class="ph tt">cuptiDeviceSupported()</tt> to check support for a compute device.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityKernel3</tt> for kernel execution has been deprecated
                                       and replaced by new activity record <tt class="ph tt">CUpti_ActivityKernel4</tt>. New record gives
                                       information about queued and submit timestamps which can help to determine software
                                       and hardware latencies associated with the kernel launch. These timestamps are not collected
                                       by default. Use API <tt class="ph tt">cuptiActivityEnableLatencyTimestamps()</tt> to enable collection.
                                       New field <tt class="ph tt">launchType</tt> of type <tt class="ph tt">CUpti_ActivityLaunchType</tt> can be used to
                                       determine if it is a cooperative CUDA kernel launch.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityPCSampling2</tt> for PC sampling has been deprecated
                                       and replaced by new activity record <tt class="ph tt">CUpti_ActivityPCSampling3</tt>. New record accomodates
                                       64-bit PC Offset supported on devices of compute capability 7.0 and higher.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityNvLink</tt> for NVLink attributes has been deprecated
                                       and replaced by new activity record <tt class="ph tt">CUpti_ActivityNvLink2</tt>. New record accomodates
                                       increased port numbers between two compute devices.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityGlobalAccess2</tt> for source level global accesses
                                       has been deprecated and replaced by new activity record <tt class="ph tt">CUpti_ActivityGlobalAccess3</tt>.
                                       New record accomodates 64-bit PC Offset supported on devices of compute capability 7.0 and higher.
                                       
                                    </li>
                                    <li class="li">New attributes <tt class="ph tt">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_SIZE</tt> and
                                       <tt class="ph tt">CUPTI_ACTIVITY_ATTR_PROFILING_SEMAPHORE_POOL_LIMIT</tt> are added in the activity attribute
                                       enum <tt class="ph tt">CUpti_ActivityAttribute</tt> to set and get the profiling semaphore pool size and
                                       the pool limit.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_8.0"><a name="release_notes_8.0" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.25.&nbsp;Updates in CUDA 8.0</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Sampling of the program counter (PC) is enhanced to point out the true
                                       latency issues, it indicates if the stall reasons for warps are actually causing
                                       stalls in the issue pipeline. Field <tt class="ph tt">latencySamples</tt> of new activity record
                                       <tt class="ph tt">CUpti_ActivityPCSampling2</tt> provides true latency samples. This field is valid
                                       for devices with compute capability 6.0 and higher.
                                       See section <a class="xref" href="r_main.html#r_pc_sampling" shape="rect">PC Sampling</a> for more details.
                                       
                                    </li>
                                    <li class="li">Support for NVLink topology information such as the pair of devices connected via NVLink,
                                       peak bandwidth, memory access permissions etc is provided through new activity record
                                       <tt class="ph tt">CUpti_ActivityNvLink</tt>. NVLink performance metrics for data transmitted/received,
                                       transmit/receive throughput and respective header overhead for each physical link.
                                       See section <a class="xref" href="r_main.html#r_nvlink" shape="rect">NVLink</a> for more details.
                                       
                                    </li>
                                    <li class="li">CUPTI supports profiling of OpenACC applications. OpenACC profiling
                                       information is provided in the form of new activity records <tt class="ph tt">CUpti_ActivityOpenAccData</tt>,
                                       <tt class="ph tt">CUpti_ActivityOpenAccLaunch</tt> and <tt class="ph tt">CUpti_ActivityOpenAccOther</tt>.
                                       This aids in correlating OpenACC constructs on the CPU with the corresponding 
                                       activity taking place on the GPU, and mapping it back to the source code.
                                       New API <tt class="ph tt">cuptiOpenACCInitialize</tt> is used to initialize profiling for supported OpenACC runtimes.
                                       See section <a class="xref" href="r_main.html#r_openacc" shape="rect">OpenACC</a> for more details.
                                       
                                    </li>
                                    <li class="li">Unified memory profiling provides GPU page fault events on devices with
                                       compute capability 6.0 and 64 bit Linux platforms. Enum <tt class="ph tt">CUpti_ActivityUnifiedMemoryAccessType</tt>
                                       lists memory access types for GPU page fault events and enum <tt class="ph tt">CUpti_ActivityUnifiedMemoryMigrationCause</tt>
                                       lists migration causes for data transfer events.
                                       
                                    </li>
                                    <li class="li">Unified Memory profiling support is extended to Mac platform.
                                       
                                    </li>
                                    <li class="li">Support for 16-bit floating point (FP16) data format profiling. New metrics inst_fp_16,
                                       flop_count_hp_add, flop_count_hp_mul, flop_count_hp_fma, flop_count_hp, flop_hp_efficiency, 
                                       half_precision_fu_utilization are supported.
                                       Peak FP16 flops per cycle for device can be queried using the enum
                                       <tt class="ph tt">CUPTI_DEVICE_ATTR_FLOP_HP_PER_CYCLE</tt> added to <tt class="ph tt">CUpti_DeviceAttribute</tt>.
                                       
                                    </li>
                                    <li class="li">Added new activity kinds <tt class="ph tt">CUPTI_ACTIVITY_KIND_SYNCHRONIZATION</tt>,
                                       <tt class="ph tt">CUPTI_ACTIVITY_KIND_STREAM</tt> and <tt class="ph tt">CUPTI_ACTIVITY_KIND_CUDA_EVENT</tt>,
                                       to support the tracing of CUDA synchronization constructs such as context, stream and
                                       CUDA event synchronization. Synchronization details are provided in the form of new activity
                                       record <tt class="ph tt">CUpti_ActivitySynchronization</tt>. Enum <tt class="ph tt">CUpti_ActivitySynchronizationType</tt>
                                       lists different types of CUDA synchronization constructs.
                                       
                                    </li>
                                    <li class="li">APIs <tt class="ph tt">cuptiSetThreadIdType()</tt>/<tt class="ph tt">cuptiGetThreadIdType()</tt> to set/get
                                       the mechanism used to fetch the thread-id used in CUPTI records.
                                       Enum <tt class="ph tt">CUpti_ActivityThreadIdType</tt> lists all supported mechanisms.
                                       
                                    </li>
                                    <li class="li">Added API <tt class="ph tt">cuptiComputeCapabilitySupported()</tt> to check the support for a specific
                                       compute capability by the CUPTI.
                                       
                                    </li>
                                    <li class="li">Added support to establish correlation between an external API (such as OpenACC, OpenMP)
                                       and CUPTI API activity records. APIs <tt class="ph tt">cuptiActivityPushExternalCorrelationId()</tt> and
                                       <tt class="ph tt">cuptiActivityPopExternalCorrelationId()</tt> should be used to push and pop external
                                       correlation ids for the calling thread. Generated records of type
                                       <tt class="ph tt">CUpti_ActivityExternalCorrelation</tt> contain both external and CUPTI assigned correlation ids.
                                       
                                    </li>
                                    <li class="li">Added containers to store the information of events and metrics in the form of activity records
                                       <tt class="ph tt">CUpti_ActivityInstantaneousEvent</tt>, <tt class="ph tt">CUpti_ActivityInstantaneousEventInstance</tt>,
                                       <tt class="ph tt">CUpti_ActivityInstantaneousMetric</tt> and <tt class="ph tt">CUpti_ActivityInstantaneousMetricInstance</tt>.
                                       These activity records are not produced by the CUPTI, these are included for completeness and
                                       ease-of-use. Profilers built on top of CUPTI that sample events may choose to use these records
                                       to store the collected event data.
                                       
                                    </li>
                                    <li class="li">Support for domains and annotation of synchronization objects added in NVTX v2.
                                       New activity record <tt class="ph tt">CUpti_ActivityMarker2</tt> and enums to indicate various
                                       stages of synchronization object i.e.
                                       <tt class="ph tt">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE</tt>,
                                       <tt class="ph tt">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_SUCCESS</tt>,
                                       <tt class="ph tt">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_ACQUIRE_FAILED</tt> and
                                       <tt class="ph tt">CUPTI_ACTIVITY_FLAG_MARKER_SYNC_RELEASE</tt> are added.
                                       
                                    </li>
                                    <li class="li">Unused field <tt class="ph tt">runtimeCorrelationId</tt> of the activity record <tt class="ph tt">CUpti_ActivityMemset</tt>
                                       is broken into two fields <tt class="ph tt">flags</tt> and <tt class="ph tt">memoryKind</tt> to indicate the asynchronous
                                       behaviour and the kind of the memory used for the memset operation.
                                       It is supported by the new flag <tt class="ph tt">CUPTI_ACTIVITY_FLAG_MEMSET_ASYNC</tt> added in
                                       the enum <tt class="ph tt">CUpti_ActivityFlag</tt>.
                                       
                                    </li>
                                    <li class="li">Added flag <tt class="ph tt">CUPTI_ACTIVITY_MEMORY_KIND_MANAGED</tt> in the enum <tt class="ph tt">CUpti_ActivityMemoryKind</tt>
                                       to indicate managed memory.
                                       
                                    </li>
                                    <li class="li">API <tt class="ph tt">cuptiGetStreamId</tt> has been deprecated. A new API <tt class="ph tt">cuptiGetStreamIdEx</tt> is
                                       introduced to provide the stream id based on the legacy or per-thread default stream flag.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_7.5"><a name="release_notes_7.5" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.26.&nbsp;Updates in CUDA 7.5</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Device-wide sampling of the program counter (PC) is enabled by default.
                                       This was a preview feature in the CUDA Toolkit 7.0 release and it was not enabled by default.
                                       
                                    </li>
                                    <li class="li">Ability to collect all events and metrics accurately in presence of multiple contexts on
                                       the GPU is extended for devices with compute capability 5.x.
                                       
                                    </li>
                                    <li class="li">API <tt class="ph tt">cuptiGetLastError</tt> is introduced to return the last error that has been
                                       produced by any of the CUPTI API calls or the callbacks in the same host thread.
                                       
                                    </li>
                                    <li class="li">Unified memory profiling is supported with MPS (Multi-Process Service)
                                       
                                    </li>
                                    <li class="li">Callback is provided to collect replay information after every kernel run during 
                                       kernel replay. See API <tt class="ph tt">cuptiKernelReplaySubscribeUpdate</tt> and callback type
                                       <tt class="ph tt">CUpti_KernelReplayUpdateFunc</tt>.
                                       
                                    </li>
                                    <li class="li">Added new attributes in enum <tt class="ph tt">CUpti_DeviceAttribute</tt> to query maximum shared
                                       memory size for different cache preferences for a device function.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_7.0"><a name="release_notes_7.0" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.27.&nbsp;Updates in CUDA 7.0</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">CUPTI supports device-wide sampling of the program counter (PC). Program counters along
                                       with the stall reasons from all active warps are sampled at a fixed frequency in the round robin order.
                                       Activity record <tt class="ph tt">CUpti_ActivityPCSampling</tt> enabled using activity kind
                                       <tt class="ph tt">CUPTI_ACTIVITY_KIND_PC_SAMPLING</tt> outputs stall reason along with PC and other related information.
                                       Enum <tt class="ph tt">CUpti_ActivityPCSamplingStallReason</tt> lists all the stall reasons. Sampling period is
                                       configurable and can be tuned using API <tt class="ph tt">cuptiActivityConfigurePCSampling</tt>.
                                       This feature is available on devices with compute capability 5.2.
                                       
                                    </li>
                                    <li class="li">Added new activity record <tt class="ph tt">CUpti_ActivityInstructionCorrelation</tt>
                                       which can be used to dump source locator records for all the PCs of the function.
                                       
                                    </li>
                                    <li class="li">All events and metrics for devices with compute capability 3.x and 5.0 can be
                                       collected accurately in presence of multiple contexts on the GPU. In previous releases only some
                                       events and metrics could be collected accurately when multiple contexts were executing on the GPU.
                                       
                                    </li>
                                    <li class="li">Unified memory profiling is enhanced by providing fine grain data transfers to and from the GPU,
                                       coupled with more accurate timestamps with each transfer. This information is provided through
                                       new activity record <tt class="ph tt">CUpti_ActivityUnifiedMemoryCounter2</tt>, deprecating old record
                                       <tt class="ph tt">CUpti_ActivityUnifiedMemoryCounter</tt>.
                                       
                                    </li>
                                    <li class="li">MPS tracing and profiling support is extended on multi-gpu setups.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityDevice</tt> for device information has been deprecated
                                       and replaced by new activity record <tt class="ph tt">CUpti_ActivityDevice2</tt>. New record adds device UUID
                                       which can be used to uniquely identify the device across profiler runs.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityKernel2</tt> for kernel execution has been deprecated
                                       and replaced by new activity record <tt class="ph tt">CUpti_ActivityKernel3</tt>. New record gives
                                       information about Global Partitioned Cache Configuration requested and executed. Partitioned
                                       global caching has an impact on occupancy calculation. If it is ON, then a CTA can only use a
                                       half SM, and thus a half of the registers available per SM. The new fields apply for devices
                                       with compute capability 5.2 and higher. Note that this change was done in CUDA 6.5 release
                                       with support for compute capabilty 5.2.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_6.5"><a name="release_notes_6.5" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.28.&nbsp;Updates in CUDA 6.5</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Instruction classification is done for source-correlated Instruction Execution
                                       activity <tt class="ph tt">CUpti_ActivityInstructionExecution</tt>.
                                       See <tt class="ph tt">CUpti_ActivityInstructionClass</tt> for instruction classes.
                                       
                                    </li>
                                    <li class="li">Two new device attributes are added to the activity <tt class="ph tt">CUpti_DeviceAttribute</tt>:
                                       
                                       <ul class="ul">
                                          <li class="li"><tt class="ph tt">CUPTI_DEVICE_ATTR_FLOP_SP_PER_CYCLE</tt> gives peak single precision
                                             flop per cycle for the GPU.
                                             
                                          </li>
                                          <li class="li"><tt class="ph tt">CUPTI_DEVICE_ATTR_FLOP_DP_PER_CYCLE</tt> gives peak double precision
                                             flop per cycle for the GPU.
                                             
                                          </li>
                                       </ul>
                                    </li>
                                    <li class="li">Two new metric properties are added:
                                       
                                       <ul class="ul">
                                          <li class="li"><tt class="ph tt">CUPTI_METRIC_PROPERTY_FLOP_SP_PER_CYCLE</tt> gives peak single precision
                                             flop per cycle for the GPU.
                                             
                                          </li>
                                          <li class="li"><tt class="ph tt">CUPTI_METRIC_PROPERTY_FLOP_DP_PER_CYCLE</tt> gives peak double precision
                                             flop per cycle for the GPU.
                                             
                                          </li>
                                       </ul>
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityGlobalAccess</tt> for source level global access
                                       information has been deprecated and replaced by new activity record
                                       <tt class="ph tt">CUpti_ActivityGlobalAccess2</tt>. New record additionally gives information needed
                                       to map SASS assembly instructions to CUDA C source code. And it also provides ideal L2
                                       transactions count based on the access pattern.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityBranch</tt> for source level branch information
                                       has been deprecated and replaced by new activity record <tt class="ph tt">CUpti_ActivityBranch2</tt>.
                                       New record additionally gives information needed to map SASS assembly instructions
                                       to CUDA C source code.
                                       
                                    </li>
                                    <li class="li">Sample <tt class="ph tt">sass_source_map</tt> is added to demonstrate the mapping of SASS
                                       assembly instructions to CUDA C source code.
                                       
                                    </li>
                                    <li class="li">Default event collection mode is changed to Kernel (<tt class="ph tt">CUPTI_EVENT_COLLECTION_MODE_KERNEL</tt>)
                                       from Continuous (<tt class="ph tt">CUPTI_EVENT_COLLECTION_MODE_CONTINUOUS</tt>). Also Continuous mode
                                       is supported only on Tesla devices.
                                       
                                    </li>
                                    <li class="li">Profiling results might be inconsistent when auto boost is enabled. Profiler tries to
                                       disable auto boost by default, it might fail to do so in some conditions, but profiling
                                       will continue. A new API <tt class="ph tt">cuptiGetAutoBoostState</tt> is added to query the auto boost
                                       state of the device. This API returns error <tt class="ph tt">CUPTI_ERROR_NOT_SUPPORTED</tt>
                                       on devices that don't support auto boost. Note that auto boost is supported only on certain
                                       Tesla devices from the Kepler+ family.
                                       
                                    </li>
                                    <li class="li">Activity record <tt class="ph tt">CUpti_ActivityKernel2</tt> for kernel execution has been deprecated
                                       and replaced by new activity record <tt class="ph tt">CUpti_ActivityKernel3</tt>. New record additionally gives
                                       information about Global Partitioned Cache Configuration requested and executed. The new fields
                                       apply for devices with 5.2 Compute Capability.
                                       
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_6.0"><a name="release_notes_6.0" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.29.&nbsp;Updates in CUDA 6.0</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Two new CUPTI activity kinds have been introduced to
                                       enable two new types of source-correlated data
                                       collection. The <tt class="ph tt">Instruction Execution</tt> kind
                                       collects SASS-level instruction execution counts,
                                       divergence data, and predication data. The <tt class="ph tt">Shared
                                          Access</tt> kind collects source correlated data
                                       indication inefficient shared memory accesses.
                                       
                                    </li>
                                    <li class="li">CUPTI provides support for CUDA applications
                                       using Unified Memory. A new activity record reports Unified Memory activity
                                       such as transfers to and from a GPU and the number of Unified Memory
                                       related page faults.
                                       
                                    </li>
                                    <li class="li">CUPTI recognized and reports the special MPS
                                       context that is used by CUDA applications running on a
                                       system with MPS enabled.
                                       
                                    </li>
                                    <li class="li">The CUpti_ActivityContext activity record
                                       <tt class="ph tt">CUpti_ActivityContext</tt> has been updated to
                                       introduce a new field into the structure in a
                                       backwards compatible manner.
                                       The 32-bit <tt class="ph tt">computeApiKind</tt> field was replaced with
                                       two 16 bit fields, <tt class="ph tt">computeApiKind</tt> and
                                       <tt class="ph tt">defaultStreamId</tt>. Because all valid
                                       <tt class="ph tt">computeApiKind</tt> values fit within 16 bits, and because
                                       all supported CUDA platforms are little-endian, persisted
                                       context record data read with the new structure will have the
                                       correct value for <tt class="ph tt">computeApiKind</tt> and have a value of
                                       zero for <tt class="ph tt">defaultStreamId</tt>. The CUPTI client is
                                       responsible for versioning the persisted context data to
                                       recognize when the <tt class="ph tt">defaultStreamId</tt> field is valid.
                                       
                                    </li>
                                    <li class="li">To ensure that metric values are calculated as
                                       accurately as possible, a new metric API is introduced.
                                       Function <tt class="ph tt">cuptiMetricGetRequiredEventGroupSets</tt>
                                       can be used to get the groups of events that should be
                                       collected at the same time.
                                       
                                    </li>
                                    <li class="li">Execution overheads introduced by CUPTI have been
                                       dramatically decreased.
                                       
                                    </li>
                                    <li class="li">The new activity buffer API introduced in CUDA
                                       Toolkit 5.5 is required. The legacy
                                       <tt class="ph tt">cuptiActivityEnqueueBuffer</tt> and
                                       <tt class="ph tt">cuptiActivityDequeueBuffer</tt> functions have been
                                       removed.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="release_notes_5.5"><a name="release_notes_5.5" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.1.30.&nbsp;Updates in CUDA 5.5</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p"><strong class="ph b">New Features</strong><ul class="ul">
                                    <li class="li">Applications that use CUDA Dynamic Parallelism can
                                       be profiled using CUPTI. Device-side kernel launches
                                       are reported using a new activity kind.
                                    </li>
                                    <li class="li">Device attributes such as power usage, clocks,
                                       thermals, etc. are reported via a new activity
                                       kind.
                                    </li>
                                    <li class="li">A new activity buffer API uses callbacks to request
                                       and return buffers of activity records. The existing
                                       <tt class="ph tt">cuptiActivityEnqueueBuffer</tt> and
                                       <tt class="ph tt">cuptiActivityDequeueBuffer</tt> functions are still
                                       supported but are deprecated and will be removed in a
                                       future release.
                                    </li>
                                    <li class="li">The Event API supports kernel replay so that any
                                       number of events can be collected during a single run of
                                       the application.
                                    </li>
                                    <li class="li">A new metric API <tt class="ph tt">cuptiMetricGetValue2</tt> allows
                                       metric values to be calculated for any device, even if
                                       that device is not available on the system.
                                    </li>
                                    <li class="li">CUDA peer-to-peer memory copies are reported
                                       explicitly via the activity API. In previous releases
                                       these memory copies were only partially reported.
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="topic reference cuda_reference nested1" id="known_issues"><a name="known_issues" shape="rect">
                        <!-- --></a><h3 class="topictitle3">1.2.&nbsp;Known Issues</h3>
                     <div class="body refbody">
                        <div class="section">
                           <div class="p">The following are known issues with the current release.
                              
                              <ul class="ul">
                                 <li class="li">A security vulnerability issue required profiling tools to disable features using GPU performance counters
                                    for non-root or non-admin users when using a Windows 419.17 or Linux 418.43 or later driver.
                                    By default, NVIDIA drivers require elevated permissions to access GPU performance counters.
                                    On Tegra platforms, profile as root or using sudo.
                                    On other platforms, you can either start profiling as root or using sudo, or by enabling non-admin profiling.
                                    More details about the issue and the solutions can be found on the ERR_NVGPUCTRPERM
                                    <a class="xref" href="https://developer.nvidia.com/ERR_NVGPUCTRPERM" target="_blank" shape="rect">web page</a>.
                                    
                                    <div class="note note"><span class="notetitle">Note:</span> CUPTI allows tracing features for non-root and non-admin users on desktop platforms only,
                                       Tegra platforms require root or sudo access.
                                    </div>
                                 </li>
                                 <li class="li">Profiling results might be inconsistent when auto boost is enabled.
                                    Profiler tries to disable auto boost by default. But it might fail to do
                                    so in some conditions and profiling will continue and results will be
                                    inconsistent. API <samp class="ph codeph">cuptiGetAutoBoostState()</samp> can be used
                                    to query the auto boost state of the device. This API returns error
                                    <tt class="ph tt">CUPTI_ERROR_NOT_SUPPORTED</tt> on devices that don't support auto boost.
                                    Note that auto boost is supported only on certain Tesla devices with
                                    compute capability 3.0 and higher.
                                    
                                 </li>
                                 <li class="li">CUPTI doesn't populate the activity structures which are deprecated,
                                    instead the newer version of the activity structure is filled with the information.
                                    
                                 </li>
                                 <li class="li">Because of the low resolution of the timer on Windows, the start and end timestamps can be same
                                    for activities having short execution duration on Windows.
                                 </li>
                                 <li class="li">The application which calls CUPTI APIs cannot be used with Nvidia tools like <tt class="ph tt">nvprof</tt>,
                                    <tt class="ph tt">Nvidia Visual Profiler</tt>, <tt class="ph tt">Nsight Compute</tt>, <tt class="ph tt">Nsight Systems</tt>,
                                    <tt class="ph tt">Nvidia Nsight Visual Studio Edition</tt>, <tt class="ph tt">cuda-gdb</tt> and <tt class="ph tt">cuda-memcheck</tt>.
                                 </li>
                                 <li class="li">PCIe and NVLink records, when enabled using the API <samp class="ph codeph">cuptiActivityEnable</samp>, are not
                                    captured when CUPTI is initialized lazily after the CUDA initialization.
                                    API <samp class="ph codeph">cuptiActivityEnableAndDump</samp> can be used to dump the records for these activities
                                    at any point during the profiling session.
                                 </li>
                                 <li class="li">CUPTI fails to profile the OpenACC application when the OpenACC
                                    library linked with the application has missing definition of the OpenACC API routine/s.
                                    This is indicated by the error code <tt class="ph tt">CUPTI_ERROR_OPENACC_UNDEFINED_ROUTINE</tt>.
                                 </li>
                                 <li class="li">OpenACC profiling might fail when OpenACC library is linked statically in the
                                    user application. This happens due to the missing definition of the OpenACC API
                                    routines needed for the OpenACC profiling, as compiler might ignore definitions
                                    for the functions not used in the application. This issue can be mitigated by
                                    linking the OpenACC library dynamically.
                                 </li>
                                 <li class="li">Unified memory profiling is not supported on the ARM architecture.</li>
                                 <li class="li">Profiling a C++ application which overloads the new operator at the global scope and uses
                                    any CUDA APIs like cudaMalloc() or cudaMallocManaged() inside the overloaded new operator will
                                    result in a hang.
                                 </li>
                                 <li class="li"> Devices with compute capability 6.0 and higher introduce a new feature, compute
                                    preemption, to give fair chance for all compute contexts while running long tasks.
                                    With compute preemption feature-
                                    <a name="known_issues__ul_nsf_43t_tgb" shape="rect">
                                       <!-- --></a><ul class="ul" id="known_issues__ul_nsf_43t_tgb">
                                       <li class="li liexpand"> If multiple contexts are running in parallel it is possible that long kernels
                                          will get preempted. 
                                       </li>
                                       <li class="li liexpand"> Some kernels may get preempted occasionally due to timeslice expiry for the
                                          context. 
                                       </li>
                                    </ul>
                                    
                                    
                                    If kernel has been preempted, the time the kernel spends preempted is still
                                    counted towards kernel duration.
                                    
                                    <div class="p">Compute preemption can affect events and metrics collection. The
                                       following are known issues with the current release:
                                       <a name="known_issues__ul_osf_43t_tgb" shape="rect">
                                          <!-- --></a><ul class="ul" id="known_issues__ul_osf_43t_tgb">
                                          <li class="li liexpand">Events and metrics collection for a MPS client can result in higher counts
                                             than expected on devices with compute capability 7.0 and higher, since MPS
                                             client may get preempted due to termination of another MPS client.
                                          </li>
                                          <li class="li liexpand">Events warps_launched and sm_cta_launched and metric inst_per_warp might
                                             provide higher counts than expected on devices with compute capability 6.0
                                             and higher. Metric unique_warps_launched can be used in place of warps_launched
                                             to get correct count of actual warps launched as it is not affected by
                                             compute preemption.
                                          </li>
                                       </ul>
                                    </div>
                                    <p class="p">To avoid compute preemption affecting profiler results try to isolate the context
                                       being profiled:
                                    </p><a name="known_issues__ul_psf_43t_tgb" shape="rect">
                                       <!-- --></a><ul class="ul" id="known_issues__ul_psf_43t_tgb">
                                       <li class="li liexpand"> Run the application on secondary GPU where display is not connected. </li>
                                       <li class="li liexpand"> On Linux if the application is running on the primary GPU where the display
                                          driver is connected then unload the display driver. 
                                       </li>
                                       <li class="li liexpand"> Run only one process that uses GPU at one time. </li>
                                    </ul>
                                 </li>
                                 <li class="li"> Devices with compute capability 6.0 and higher support demand paging. When the
                                    kernel is scheduled for the first time, all the pages allocated using
                                    cudaMallocManaged and that are required for execution of the kernel are fetched in
                                    the global memory when GPU faults are generated. Profiler requires multiple passes to
                                    collect all the metrics required for kernel analysis. The kernel state needs to be
                                    saved and restored for each kernel replay pass. For devices with compute capability
                                    6.0 and higher and platforms supporting Unified memory, in the first kernel iteration
                                    the GPU faults will be generated and all pages will be fetched in the global memory.
                                    Second iteration onwards GPU page faults will not occur. This will significantly
                                    affect the memory related events and timing. The time taken from trace will include
                                    the time required to fetch the pages but most of the metrics profiled in multiple
                                    iterations will not include time/cycles required to fetch the pages. This causes
                                    inconsistency in the profiler results. 
                                 </li>
                                 <li class="li">When profiling an application that uses CUDA Dynamic Parallelism (CDP) there are
                                    several limitations to the profiling tools. CUDA 12.0 adds support for revamped
                                    CUDA Dynamic Parallelism APIs (referred to as CDP2), offering substantial performance
                                    improvements vs. the legacy CUDA Dynamic Parallelism APIs (referred to as CDP1).
                                    <a name="known_issues__ul_msf_43t_tgb" shape="rect">
                                       <!-- --></a><ul class="ul" id="known_issues__ul_msf_43t_tgb">
                                       <li class="li liexpand">For Legacy CUDA Dynamic Parallelism (CDP1), CUPTI supports tracing of all host
                                          and device kernels for devices with compute capability 5.x and 6.x. For devices with
                                          compute capability 7.0 and higher, CUPTI traces all the host launched kernels until
                                          it encounters a host launched kernel which launches child kernels; subsequent
                                          kernels are not traced.
                                       </li>
                                       <li class="li liexpand">For CUDA Dynamic Parallelism (CDP2), CUPTI supports tracing of host launched kernels
                                          only, it can't trace device launched kernels.
                                       </li>
                                       <li class="li liexpand">CUPTI doesn't report CUDA API calls for device launched kernels.</li>
                                       <li class="li liexpand">CUPTI doesn't support profiling of device launched kernels i.e. it doesn't report
                                          detailed event, metric, and source-level results for
                                          device launched kernels. Event, metric, and source-level results collected for
                                          CPU-launched kernels will include event, metric, and source-level results for the
                                          entire call-tree of kernels launched from within that kernel. 
                                       </li>
                                    </ul>
                                 </li>
                                 <li class="li">When profiling an application that uses CUDA Device Graphs, there are several
                                    limitations to the profiling tools.
                                    <a name="known_issues__ul_qsf_43t_tgb" shape="rect">
                                       <!-- --></a><ul class="ul" id="known_issues__ul_qsf_43t_tgb">
                                       <li class="li liexpand">CUPTI traces the device graph when it is launched from the host. Tracing of the graph which is launched from
                                          the device is skipped.
                                       </li>
                                       <li class="li liexpand"> CUPTI does not support profiling of device graphs and host graphs that launch device graphs.</li>
                                    </ul>
                                 </li>
                                 <li class="li">Compilation of samples autorange_profiling and userrange_profiling requires a host
                                    compiler which supports C++11 features. For some g++ compilers, it is required to use
                                    the flag -std=c++11 to turn on C++11 features.
                                 </li>
                                 <li class="li">PC Sampling Activity API is not supported on Tegra platforms, while PC Sampling API is supported on Tegra platforms.</li>
                                 <li class="li">As of CUDA 11.4 and R470 TRD1 driver release, CUPTI is supported in a vGPU environment
                                    which requires a vGPU license. If the license is not obtained after 20 minutes, the
                                    reported performance data including metrics from the GPU will be inaccurate.
                                    This is because of a feature in vGPU environment which reduces performance but retains
                                    functionality as specified
                                    <a class="xref" href="https://docs.nvidia.com/grid/latest/grid-licensing-user-guide/index.html#software-enforcement-grid-licensing" target="_blank" shape="rect">here</a>.
                                    
                                 </li>
                                 <li class="li">CUPTI is not supported on NVIDIA Crypto Mining Processors (CMP). This is reported
                                    using the error code <samp class="ph codeph">CUPTI_ERROR_CMP_DEVICE_NOT_SUPPORTED</samp>.
                                    For more information, please visit the
                                    <a class="xref" href="https://developer.nvidia.com/ERR_NVCMPGPU" target="_blank" shape="rect">web page</a>.
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="topic reference cuda_reference nested2" id="known_issues_profiling"><a name="known_issues_profiling" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.2.1.&nbsp;Profiling</h4>
                        <div class="body refbody">
                           <div class="section">
                              <div class="p">The following are common known issues for both the event and metric APIs and the profiling APIs:
                                 <a name="known_issues_profiling__ul_tsf_43t_tgb" shape="rect">
                                    <!-- --></a><ul class="ul" id="known_issues_profiling__ul_tsf_43t_tgb">
                                    <li class="li liexpand">Profiling may significantly change the overall performance characteristics of the application.
                                       Refer to the section <a class="xref" href="r_main.html#r_overhead" shape="rect">CUPTI Overhead</a> for more details.
                                    </li>
                                    <li class="li liexpand">Profiling a kernel while other contexts are active on the same device (e.g. X server, or
                                       secondary CUDA or graphics application) can result in varying metric values for L2/FB
                                       (Device Memory) related metrics.
                                       Specifically, L2/FB traffic from non-profiled contexts cannot be excluded from the metric results.
                                       To completely avoid this issue, profile the application on a GPU without secondary contexts
                                       accessing the same device (e.g. no X server on Linux).
                                       
                                    </li>
                                    <li class="li liexpand">Profiling is not supported for multidevice cooperative kernels, that is, kernels
                                       launched by using the API functions <tt class="ph tt">cudaLaunchCooperativeKernelMultiDevice</tt> or
                                       <tt class="ph tt">cuLaunchCooperativeKernelMultiDevice</tt>.
                                    </li>
                                    <li class="li liexpand">Enabling certain events can cause GPU kernels to run longer than the driver's
                                       watchdog time-out limit. In these cases the driver will terminate the GPU
                                       kernel resulting in an application error and profiling data will not be
                                       available. Please disable the driver watchdog time out before profiling such
                                       long running CUDA kernels
                                       <a name="known_issues_profiling__ul_vsf_43t_tgb" shape="rect">
                                          <!-- --></a><ul class="ul" id="known_issues_profiling__ul_vsf_43t_tgb">
                                          <li class="li liexpand">On Linux, setting the X Config option Interactive to false is
                                             recommended.
                                          </li>
                                          <li class="li liexpand">For Windows, detailed information about TDR (Timeout Detection and Recovery)
                                             and how to disable it is available at
                                             https://docs.microsoft.com/en-us/windows-hardware/drivers/display/timeout-detection-and-recovery
                                          </li>
                                       </ul>
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                        <div class="topic reference cuda_reference nested3" id="known_issues_event_and_metric"><a name="known_issues_event_and_metric" shape="rect">
                              <!-- --></a><h5 class="topictitle5">1.2.1.1.&nbsp;Event and Metric API</h5>
                           <div class="body refbody">
                              <div class="section">
                                 <div class="p">The following are known issues related to Event and Metric API:
                                    <a name="known_issues_event_and_metric__ul_tsf_43t_tgb" shape="rect">
                                       <!-- --></a><ul class="ul" id="known_issues_event_and_metric__ul_tsf_43t_tgb">
                                       <li class="li liexpand">The CUPTI <a class="xref" href="r_main.html#r_event_api" shape="rect">event APIs</a> from the header <tt class="ph tt">cupti_events.h</tt>
                                          and <a class="xref" href="r_main.html#r_metric_api" shape="rect">metric APIs</a> from the header <tt class="ph tt">cupti_metrics.h</tt> are not
                                          supported for the devices with compute capability 7.5 and higher.
                                          These are replaced by <a class="xref" href="r_main.html#r_profiler" shape="rect">Profiling API</a>
                                          and <a class="xref" href="r_main.html#r_host_metrics_api" shape="rect">Perfworks metric API</a>.
                                          Refer to the section <a class="xref" href="r_main.html#r_profiling_migration" shape="rect">Migration to the Profiling API</a>.
                                          
                                       </li>
                                       <li class="li liexpand">While collecting events in continuous mode, event reporting may be delayed
                                          i.e. event values may be returned by a later call to readEvent(s) API and the
                                          event values for the last readEvent(s) API may get lost.
                                          
                                       </li>
                                       <li class="li liexpand">When profiling events, it is possible that the domain instance that gets
                                          profiled gives event value 0 due to absence of workload on the domain instance
                                          since CUPTI profiles one instance of the domain by default.
                                          To profile all instances of the domain, user can set event group attribute
                                          <tt class="ph tt">CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES</tt> through API
                                          <samp class="ph codeph">cuptiEventGroupSetAttribute()</samp>.
                                          
                                       </li>
                                       <li class="li liexpand">Profiling results might be incorrect for CUDA applications compiled with nvcc version
                                          older than 9.0 for devices with compute capability 6.0 and 6.1. Profiling session will
                                          continue and CUPTI will notify it using error code
                                          <tt class="ph tt">CUPTI_ERROR_CUDA_COMPILER_NOT_COMPATIBLE</tt>. It is advised to recompile the
                                          application code with nvcc version 9.0 or later.
                                          Ignore this warning if code is already compiled with the recommended nvcc version.
                                       </li>
                                       <li class="li liexpand">For some metrics, the required events can only be collected for a single CUDA
                                          context. For an application that uses multiple CUDA contexts, these metrics
                                          will only be collected for one of the contexts. The metrics that can be
                                          collected only for a single CUDA context are indicated in the <a class="xref" href="r_main.html#metrics-reference" shape="rect">metric reference tables</a>.
                                       </li>
                                       <li class="li liexpand">Some metric values are calculated assuming a kernel is large enough to occupy
                                          all device multiprocessors with approximately the same amount of work. If a
                                          kernel launch does not have this characteristic, then those metric values may
                                          not be accurate.
                                       </li>
                                       <li class="li liexpand">Some events and metrics are not available on all devices. For list of metrics,
                                          you can refer to the <a class="xref" href="r_main.html#metrics-reference" shape="rect">metric reference tables</a>.
                                       </li>
                                       <li class="li liexpand">CUPTI can give out of memory error for event and metrics profiling, it could be due
                                          to large number of instructions in the kernel.
                                       </li>
                                       <li class="li liexpand">Profiling is not supported for CUDA kernel nodes launched by a CUDA Graph.</li>
                                       <li class="li liexpand">These APIs are not supported on below system configurations:
                                          <a name="known_issues_event_and_metric__ul_msf_43t_tgb" shape="rect">
                                             <!-- --></a><ul class="ul" id="known_issues_event_and_metric__ul_msf_43t_tgb">
                                             <li class="li liexpand">64-bit ARM Server CPU architecture (arm64 SBSA).</li>
                                             <li class="li liexpand">Virtual GPUs (vGPU).</li>
                                             <li class="li liexpand">Windows Subsystem for Linux (WSL).</li>
                                          </ul>
                                       </li>
                                    </ul>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="topic reference cuda_reference nested3" id="known_issues_profiling_and_perfworks"><a name="known_issues_profiling_and_perfworks" shape="rect">
                              <!-- --></a><h5 class="topictitle5">1.2.1.2.&nbsp;Profiling and Perfworks Metric API</h5>
                           <div class="body refbody">
                              <div class="section">
                                 <div class="p">The following are known issues related to the Profiling and Perfworks Metric API:
                                    <a name="known_issues_profiling_and_perfworks__ul_tsf_43t_tgb" shape="rect">
                                       <!-- --></a><ul class="ul" id="known_issues_profiling_and_perfworks__ul_tsf_43t_tgb">
                                       <li class="li liexpand">Profiling a kernel while any other GPU work is executing on the same
                                          MIG compute instance can result in varying metric values for all units. Care should be taken to
                                          serialize, or otherwise prevent concurrent CUDA launches within the target application to ensure
                                          those kernels do not influence each other. Be aware that GPU work issued through other APIs in the
                                          target process or workloads created by non-target processes running simultaneously in the same MIG
                                          compute instance will influence the collected metrics. Note that it is acceptable to run CUDA
                                          processes in other MIG compute instances as they will not influence the profiled MIG compute instance.
                                          
                                       </li>
                                       <li class="li liexpand">For devices with compute capability 8.0, the NVLink topology information is available
                                          but NVLink performance metrics (<samp class="ph codeph">nvlrx__*</samp> and <samp class="ph codeph">nvltx__*</samp>)
                                          are not supported due to a potential application hang during data collection.
                                       </li>
                                       <li class="li liexpand">Profiling is not supported under MPS (Multi-Process Service).</li>
                                       <li class="li liexpand">For profiling the CUDA kernel nodes launched by a CUDA Graph, not all combinations
                                          of range profiling and replay modes are supported. User replay and application replay modes
                                          with auto range are not supported. In the user range mode, all the kernel nodes launched
                                          by the CUDA Graph will be profiled, user can't do the profiling for a range of kernels.
                                       </li>
                                       <li class="li liexpand">Profiling kernels executed on a device that is part of an SLI group is not supported.</li>
                                       <li class="li liexpand">Profiling is not supported for OptiX applications.</li>
                                       <li class="li liexpand">Refer to the section for <a class="xref" href="r_main.html#r_profiling_missing_features" shape="rect">differences from event and metric APIs</a>.
                                       </li>
                                       <li class="li liexpand">Profiling on Windows Subsystem for Linux (WSL) is only supported with WSL version 2, NVIDIA display driver
                                          version 525 or higher and Windows 11.
                                       </li>
                                       <li class="li liexpand">On NVIDIA H800 GPUs, the reported peak values for FP64 pipelines are not scaled to the actual maximum achievable throughput
                                          of these pipelines.
                                          This affects the raw peak values as well as all derived metrics, such as peak utilization percentages.
                                          Correct scaling for these metrics will be introduced in an upcoming release.
                                          
                                       </li>
                                    </ul>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="topic concept nested1" id="support"><a name="support" shape="rect">
                        <!-- --></a><h3 class="topictitle3">1.3.&nbsp;Support</h3>
                     <div class="body conbody">
                        <p class="p">
                           Information on supported platforms and GPUs.
                           
                        </p>
                     </div>
                     <div class="topic concept nested2" id="platform_support"><a name="platform_support" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.3.1.&nbsp;Platform Support</h4>
                        <div class="body conbody">
                           <div class="tablenoborder"><a name="platform_support__supported-platforms" shape="rect">
                                 <!-- --></a><table cellpadding="4" cellspacing="0" summary="" id="platform_support__supported-platforms" class="table" frame="border" border="1" rules="all">
                                 <caption><span class="tablecap">Table 1. Platforms supported by CUPTI</span></caption>
                                 <thead class="thead" align="left">
                                    <tr class="row">
                                       <th class="entry" valign="top" width="50%" id="d168297e2694" rowspan="1" colspan="1">Platform</th>
                                       <th class="entry" valign="top" width="50%" id="d168297e2697" rowspan="1" colspan="1">Support</th>
                                    </tr>
                                 </thead>
                                 <tbody class="tbody">
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Windows</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Windows Subsystem for Linux version 2 (WSL 2)</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes*</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Linux (x86_64)</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Linux (ppc64le)</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Linux (aarch64 sbsa)</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes*</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Linux (x86_64) (Drive SDK)</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes*</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Linux (aarch64)</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes*</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">QNX</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">Yes*</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Mac OSX</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">No</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="50%" headers="d168297e2694" rowspan="1" colspan="1">Android</td>
                                       <td class="entry" valign="top" width="50%" headers="d168297e2697" rowspan="1" colspan="1">No</td>
                                    </tr>
                                 </tbody>
                              </table>
                           </div>
                           <p class="p">
                              Tracing and profiling of 32-bit processes is not supported.
                              
                           </p>
                           <p class="p">
                              Event and Metric APIs are not supported on Linux (aarch64 sbsa) and WSL 2 platforms.
                              
                           </p>
                        </div>
                     </div>
                     <div class="topic concept nested2" id="gpu_support"><a name="gpu_support" shape="rect">
                           <!-- --></a><h4 class="topictitle4">1.3.2.&nbsp;GPU Support</h4>
                        <div class="body conbody">
                           <div class="tablenoborder"><a name="gpu_support__cupti-api-gpu-arch-support-matrix" shape="rect">
                                 <!-- --></a><table cellpadding="4" cellspacing="0" summary="" id="gpu_support__cupti-api-gpu-arch-support-matrix" class="table" frame="border" border="1" rules="all">
                                 <caption><span class="tablecap">Table 2. GPU architectures supported by different CUPTI APIs</span></caption>
                                 <thead class="thead" align="left">
                                    <tr class="row">
                                       <th class="entry" valign="top" width="20%" id="d168297e2830" rowspan="1" colspan="1">CUPTI API</th>
                                       <th class="entry" valign="top" width="40%" id="d168297e2833" rowspan="1" colspan="1">Supported GPU architectures</th>
                                       <th class="entry" valign="top" width="40%" id="d168297e2836" rowspan="1" colspan="1">Notes</th>
                                    </tr>
                                 </thead>
                                 <tbody class="tbody">
                                    <tr class="row">
                                       <td class="entry" valign="top" width="20%" headers="d168297e2830" rowspan="1" colspan="1">Activity</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2833" rowspan="1" colspan="1">Maxwell and later GPU architectures, i.e. devices with
                                          compute capability 5.0 and higher
                                       </td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2836" rowspan="1" colspan="1">&nbsp;</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="20%" headers="d168297e2830" rowspan="1" colspan="1">Callback</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2833" rowspan="1" colspan="1">Maxwell and later GPU architectures, i.e. devices with
                                          compute capability 5.0 and higher
                                       </td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2836" rowspan="1" colspan="1">&nbsp;</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="20%" headers="d168297e2830" rowspan="1" colspan="1">Event</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2833" rowspan="1" colspan="1">Maxwell, Pascal, Volta</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2836" rowspan="1" colspan="1">Not supported on Turing and later GPU architectures,
                                          i.e. devices with compute capability 7.5 and higher
                                       </td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="20%" headers="d168297e2830" rowspan="1" colspan="1">Metric</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2833" rowspan="1" colspan="1">Maxwell, Pascal, Volta</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2836" rowspan="1" colspan="1">Not supported on Turing and later GPU architectures,
                                          i.e. devices with compute capability 7.5 and higher
                                       </td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="20%" headers="d168297e2830" rowspan="1" colspan="1">Profiling</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2833" rowspan="1" colspan="1">Volta and later GPU architectures, i.e. devices with
                                          compute capability 7.0 and higher
                                       </td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2836" rowspan="1" colspan="1">Not supported on Maxwell and Pascal GPUs</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="20%" headers="d168297e2830" rowspan="1" colspan="1">PC Sampling</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2833" rowspan="1" colspan="1">Volta and later GPU architectures, i.e. devices with
                                          compute capability 7.0 and higher
                                       </td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2836" rowspan="1" colspan="1">Not supported on Maxwell and Pascal GPUs</td>
                                    </tr>
                                    <tr class="row">
                                       <td class="entry" valign="top" width="20%" headers="d168297e2830" rowspan="1" colspan="1">Checkpoint</td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2833" rowspan="1" colspan="1">Volta and later GPU architectures, i.e. devices with
                                          compute capability 7.0 and higher
                                       </td>
                                       <td class="entry" valign="top" width="40%" headers="d168297e2836" rowspan="1" colspan="1"> Not supported on Maxwell and Pascal GPUs</td>
                                    </tr>
                                 </tbody>
                              </table>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               
               <hr id="contents-end"></hr>
               
            </article>
         </div>
      </div>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/formatting/common.min.js"></script>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-write.js"></script>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-tracker.js"></script>
      <script type="text/javascript">_satellite.pageBottom();</script>
      <script type="text/javascript">var switchTo5x=true;</script><script type="text/javascript">stLight.options({publisher: "998dc202-a267-4d8e-bce9-14debadb8d92", doNotHash: false, doNotCopy: false, hashAddressBar: false});</script></body>
</html>