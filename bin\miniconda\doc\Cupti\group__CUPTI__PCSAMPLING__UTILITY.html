<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI PC Sampling Utility API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI PC Sampling Utility API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structBufferInfo.html">BufferInfo</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight"><a class="el" href="structBufferInfo.html" title="BufferInfo will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBuffe...">BufferInfo</a> will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBufferInFile() API.  <a href="structBufferInfo.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for CuptiUtilGetBufferInfo.  <a href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for CuptiUtilGetHeaderData.  <a href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for CuptiUtilGetPcSampData.  <a href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for CuptiUtilMergePcSampData.  <a href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for CuptiUtilPutPcSampData.  <a href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structHeader.html">Header</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight"><a class="el" href="structHeader.html" title="Header info will be stored in file.">Header</a> info will be stored in file.  <a href="structHeader.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">All available stall reasons name and respective indexes will be stored in it.  <a href="structPcSamplingStallReasons.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g14b3e4f5bd78f1abf3b16884c7dfd755">CUPTI::PcSamplingUtil::CUptiUtilResult</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd755cae048aaab0c0089ebf745abe68ee22d">CUPTI::PcSamplingUtil::CUPTI_UTIL_SUCCESS</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd75573250c1e21a383116f4f9d4e6bc5d4f0">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_INVALID_PARAMETER</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd75590ac2672e7fb1cb990068555dead28cd">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_UNABLE_TO_CREATE_FILE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd755ac3255b6e3726887b0e0e7b79b7508fc">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_UNABLE_TO_OPEN_FILE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd7552dde9c7142a4c67385f8bce78fc5f454">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_READ_WRITE_OPERATION_FAILED</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd755a6b78e0475a9e02b5c68f81da10ceddd">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_FILE_HANDLE_CORRUPTED</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd755e54c69d9ae3c692cf68e59ff3427dae4">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_SEEK_OPERATION_FAILED</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd7554543c8330a7e8bc91de312ed8856de15">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_OUT_OF_MEMORY</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg14b3e4f5bd78f1abf3b16884c7dfd755469a7a344774f708cf39be88a34d6add">CUPTI::PcSamplingUtil::CUPTI_UTIL_ERROR_UNKNOWN</a> =  999
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">CUPTI PC sampling utility API result codes.  <a href="group__CUPTI__PCSAMPLING__UTILITY.html#g14b3e4f5bd78f1abf3b16884c7dfd755">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g1dc90a8b2a7ee09a0c230aa6256bba5e">CUPTI::PcSamplingUtil::PcSamplingBufferType</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg1dc90a8b2a7ee09a0c230aa6256bba5e8dc947a769dae0795850d6cd02c1a172">CUPTI::PcSamplingUtil::PC_SAMPLING_BUFFER_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gg1dc90a8b2a7ee09a0c230aa6256bba5e459b86b18a1ad61bee3a04f456145d00">CUPTI::PcSamplingUtil::PC_SAMPLING_BUFFER_PC_TO_COUNTER_DATA</a> =  1
<br>
 }</td></tr>

<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUptiUtilResult CUPTIUTILAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gfb8e2fd5654563e8397e5548f279bb4d">CUPTI::PcSamplingUtil::CuptiUtilGetBufferInfo</a> (CUptiUtil_GetBufferInfoParams *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get buffer info data of file.  <a href="#gfb8e2fd5654563e8397e5548f279bb4d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUptiUtilResult CUPTIUTILAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g21d7bd803326aba646425a32e9bf899d">CUPTI::PcSamplingUtil::CuptiUtilGetHeaderData</a> (CUptiUtil_GetHeaderDataParams *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get header data of file.  <a href="#g21d7bd803326aba646425a32e9bf899d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUptiUtilResult CUPTIUTILAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g81cce7f8035fdd9d1a8294731a8bc4b8">CUPTI::PcSamplingUtil::CuptiUtilGetPcSampData</a> (CUptiUtil_GetPcSampDataParams *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Retrieve PC sampling data from file into allocated buffer.  <a href="#g81cce7f8035fdd9d1a8294731a8bc4b8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUptiUtilResult CUPTIUTILAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g3154650d8b6311b6b4a2b502996a13c0">CUPTI::PcSamplingUtil::CuptiUtilMergePcSampData</a> (CUptiUtil_MergePcSampDataParams *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Merge PC sampling data range id wise.  <a href="#g3154650d8b6311b6b4a2b502996a13c0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUptiUtilResult CUPTIUTILAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#gce0188016b40b723b97eca1c2187addc">CUPTI::PcSamplingUtil::CuptiUtilPutPcSampData</a> (CUptiUtil_PutPcSampDataParams *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Dump PC sampling data into the file.  <a href="#gce0188016b40b723b97eca1c2187addc"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI PC Sampling Utility API. <hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g14b3e4f5bd78f1abf3b16884c7dfd755"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtilResult" ref="g14b3e4f5bd78f1abf3b16884c7dfd755" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g14b3e4f5bd78f1abf3b16884c7dfd755">CUPTI::PcSamplingUtil::CUptiUtilResult</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Error and result codes returned by CUPTI PC sampling utility API. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd755cae048aaab0c0089ebf745abe68ee22d"></a><!-- doxytag: member="CUPTI_UTIL_SUCCESS" ref="gg14b3e4f5bd78f1abf3b16884c7dfd755cae048aaab0c0089ebf745abe68ee22d" args="" -->CUPTI_UTIL_SUCCESS</em>&nbsp;</td><td>
No error </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd75573250c1e21a383116f4f9d4e6bc5d4f0"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_INVALID_PARAMETER" ref="gg14b3e4f5bd78f1abf3b16884c7dfd75573250c1e21a383116f4f9d4e6bc5d4f0" args="" -->CUPTI_UTIL_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>
One or more of the parameters are invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd75590ac2672e7fb1cb990068555dead28cd"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_UNABLE_TO_CREATE_FILE" ref="gg14b3e4f5bd78f1abf3b16884c7dfd75590ac2672e7fb1cb990068555dead28cd" args="" -->CUPTI_UTIL_ERROR_UNABLE_TO_CREATE_FILE</em>&nbsp;</td><td>
Unable to create a new file </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd755ac3255b6e3726887b0e0e7b79b7508fc"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_UNABLE_TO_OPEN_FILE" ref="gg14b3e4f5bd78f1abf3b16884c7dfd755ac3255b6e3726887b0e0e7b79b7508fc" args="" -->CUPTI_UTIL_ERROR_UNABLE_TO_OPEN_FILE</em>&nbsp;</td><td>
Unable to open a file </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd7552dde9c7142a4c67385f8bce78fc5f454"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_READ_WRITE_OPERATION_FAILED" ref="gg14b3e4f5bd78f1abf3b16884c7dfd7552dde9c7142a4c67385f8bce78fc5f454" args="" -->CUPTI_UTIL_ERROR_READ_WRITE_OPERATION_FAILED</em>&nbsp;</td><td>
Read or write operation failed </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd755a6b78e0475a9e02b5c68f81da10ceddd"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_FILE_HANDLE_CORRUPTED" ref="gg14b3e4f5bd78f1abf3b16884c7dfd755a6b78e0475a9e02b5c68f81da10ceddd" args="" -->CUPTI_UTIL_ERROR_FILE_HANDLE_CORRUPTED</em>&nbsp;</td><td>
Provided file handle is corrupted. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd755e54c69d9ae3c692cf68e59ff3427dae4"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_SEEK_OPERATION_FAILED" ref="gg14b3e4f5bd78f1abf3b16884c7dfd755e54c69d9ae3c692cf68e59ff3427dae4" args="" -->CUPTI_UTIL_ERROR_SEEK_OPERATION_FAILED</em>&nbsp;</td><td>
seek operation failed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd7554543c8330a7e8bc91de312ed8856de15"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_OUT_OF_MEMORY" ref="gg14b3e4f5bd78f1abf3b16884c7dfd7554543c8330a7e8bc91de312ed8856de15" args="" -->CUPTI_UTIL_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td>
Unable to allocate enough memory to perform the requested operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg14b3e4f5bd78f1abf3b16884c7dfd755469a7a344774f708cf39be88a34d6add"></a><!-- doxytag: member="CUPTI_UTIL_ERROR_UNKNOWN" ref="gg14b3e4f5bd78f1abf3b16884c7dfd755469a7a344774f708cf39be88a34d6add" args="" -->CUPTI_UTIL_ERROR_UNKNOWN</em>&nbsp;</td><td>
An unknown internal error has occurred. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g1dc90a8b2a7ee09a0c230aa6256bba5e"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::PcSamplingBufferType" ref="g1dc90a8b2a7ee09a0c230aa6256bba5e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g1dc90a8b2a7ee09a0c230aa6256bba5e">CUPTI::PcSamplingUtil::PcSamplingBufferType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg1dc90a8b2a7ee09a0c230aa6256bba5e8dc947a769dae0795850d6cd02c1a172"></a><!-- doxytag: member="PC_SAMPLING_BUFFER_INVALID" ref="gg1dc90a8b2a7ee09a0c230aa6256bba5e8dc947a769dae0795850d6cd02c1a172" args="" -->PC_SAMPLING_BUFFER_INVALID</em>&nbsp;</td><td>
Invalid buffer type. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg1dc90a8b2a7ee09a0c230aa6256bba5e459b86b18a1ad61bee3a04f456145d00"></a><!-- doxytag: member="PC_SAMPLING_BUFFER_PC_TO_COUNTER_DATA" ref="gg1dc90a8b2a7ee09a0c230aa6256bba5e459b86b18a1ad61bee3a04f456145d00" args="" -->PC_SAMPLING_BUFFER_PC_TO_COUNTER_DATA</em>&nbsp;</td><td>
Refers to <a class="el" href="structCUpti__PCSamplingData.html" title="Collected PC Sampling data.">CUpti_PCSamplingData</a> buffer. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="gfb8e2fd5654563e8397e5548f279bb4d"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CuptiUtilGetBufferInfo" ref="gfb8e2fd5654563e8397e5548f279bb4d" args="(CUptiUtil_GetBufferInfoParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUptiUtilResult CUPTIUTILAPI CUPTI::PcSamplingUtil::CuptiUtilGetBufferInfo           </td>
          <td>(</td>
          <td class="paramtype">CUptiUtil_GetBufferInfoParams *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API must be called every time before calling CuptiUtilGetPcSampData API. <a class="el" href="structBufferInfo.html">BufferInfo</a> structure, it gives info about recordCount and stallReasonCount of every record in the buffer. This will help to allocate exact buffer to retrieve data into it.<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>error out if either of pParam or fileHandle is NULL or param struct size is incorrect. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_FILE_HANDLE_CORRUPTED</em>&nbsp;</td><td>file handle is not in good state to read data from file. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_READ_WRITE_OPERATION_FAILED</em>&nbsp;</td><td>failed to read data from file. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g21d7bd803326aba646425a32e9bf899d"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CuptiUtilGetHeaderData" ref="g21d7bd803326aba646425a32e9bf899d" args="(CUptiUtil_GetHeaderDataParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUptiUtilResult CUPTIUTILAPI CUPTI::PcSamplingUtil::CuptiUtilGetHeaderData           </td>
          <td>(</td>
          <td class="paramtype">CUptiUtil_GetHeaderDataParams *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API must be called once initially while retrieving data from file. <a class="el" href="structHeader.html">Header</a> structure, it gives info about total number of buffers present in the file.<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>error out if either of pParam or fileHandle is NULL or param struct size is incorrect. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_FILE_HANDLE_CORRUPTED</em>&nbsp;</td><td>file handle is not in good state to read data from file </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_READ_WRITE_OPERATION_FAILED</em>&nbsp;</td><td>failed to read data from file. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g81cce7f8035fdd9d1a8294731a8bc4b8"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CuptiUtilGetPcSampData" ref="g81cce7f8035fdd9d1a8294731a8bc4b8" args="(CUptiUtil_GetPcSampDataParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUptiUtilResult CUPTIUTILAPI CUPTI::PcSamplingUtil::CuptiUtilGetPcSampData           </td>
          <td>(</td>
          <td class="paramtype">CUptiUtil_GetPcSampDataParams *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API must be called after CuptiUtilGetBufferInfo API. It will retrieve data from file into allocated buffer.<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>error out if buffer type is invalid or if either of pSampData, pParams is NULL. If pPcSamplingStallReasons is not NULL then error out if either of stallReasonIndex, stallReasons or stallReasons array element pointer is NULL. or filename is empty. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_READ_WRITE_OPERATION_FAILED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_FILE_HANDLE_CORRUPTED</em>&nbsp;</td><td>file handle is not in good state to read data from file. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g3154650d8b6311b6b4a2b502996a13c0"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CuptiUtilMergePcSampData" ref="g3154650d8b6311b6b4a2b502996a13c0" args="(CUptiUtil_MergePcSampDataParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUptiUtilResult CUPTIUTILAPI CUPTI::PcSamplingUtil::CuptiUtilMergePcSampData           </td>
          <td>(</td>
          <td class="paramtype">CUptiUtil_MergePcSampDataParams *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API merge PC sampling data range id wise. It allocates memory for merged data and fill data in it and provide buffer pointer in MergedPcSampDataBuffers field. It is expected from user to free merge data buffers after use.<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>error out if param struct size is invalid or count of buffers to merge is invalid i.e less than 1 or either of PcSampDataBuffer, MergedPcSampDataBuffers, numMergedBuffer is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td>Unable to allocate memory for merged buffer. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gce0188016b40b723b97eca1c2187addc"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CuptiUtilPutPcSampData" ref="gce0188016b40b723b97eca1c2187addc" args="(CUptiUtil_PutPcSampDataParams *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUptiUtilResult CUPTIUTILAPI CUPTI::PcSamplingUtil::CuptiUtilPutPcSampData           </td>
          <td>(</td>
          <td class="paramtype">CUptiUtil_PutPcSampDataParams *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This API can be called multiple times. It will append buffer in the file. For every buffer it will store <a class="el" href="structBufferInfo.html" title="BufferInfo will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBuffe...">BufferInfo</a> so that before retrieving data it will help to allocate buffer to store retrieved data. This API creates file if file does not present. If stallReasonIndex or stallReasons pointer of <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html">CUptiUtil_PutPcSampDataParams</a> is NULL then stall reasons data will not be stored in file. It is expected to store all available stall reason data at least once to refer it during offline correlation.<p>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>error out if buffer type is invalid or if either of pSamplingData, pParams pointer is NULL or stall reason configuration details not provided or filename is empty. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_UNABLE_TO_CREATE_FILE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_UNABLE_TO_OPEN_FILE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_UTIL_ERROR_READ_WRITE_OPERATION_FAILED</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
