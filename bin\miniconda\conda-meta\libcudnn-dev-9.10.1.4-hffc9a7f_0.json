{"build": "hffc9a7f_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": ["libcudnn-jit-dev <0a"], "depends": ["cuda-version >=12,<13.0a0", "libcudnn 9.10.1.4 hffc9a7f_0", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcudnn-dev-9.10.1.4-hffc9a7f_0", "features": "", "files": ["Library/include/cudnn.h", "Library/include/cudnn_adv.h", "Library/include/cudnn_backend.h", "Library/include/cudnn_cnn.h", "Library/include/cudnn_graph.h", "Library/include/cudnn_ops.h", "Library/include/cudnn_version.h", "Library/lib/cudnn.lib", "Library/lib/cudnn64_9.lib", "Library/lib/cudnn_adv.lib", "Library/lib/cudnn_adv64_9.lib", "Library/lib/cudnn_cnn.lib", "Library/lib/cudnn_cnn64_9.lib", "Library/lib/cudnn_engines_precompiled.lib", "Library/lib/cudnn_engines_precompiled64_9.lib", "Library/lib/cudnn_engines_runtime_compiled.lib", "Library/lib/cudnn_engines_runtime_compiled64_9.lib", "Library/lib/cudnn_graph.lib", "Library/lib/cudnn_graph64_9.lib", "Library/lib/cudnn_heuristic.lib", "Library/lib/cudnn_heuristic64_9.lib", "Library/lib/cudnn_ops.lib", "Library/lib/cudnn_ops64_9.lib"], "fn": "libcudnn-dev-9.10.1.4-hffc9a7f_0.conda", "license": "LicenseRef-cuDNN-Software-License-Agreement", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcudnn-dev-9.10.1.4-hffc9a7f_0", "type": 1}, "md5": "81ae4771239f63d4f0aebad63de544f6", "name": "libcudnn-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcudnn-dev-9.10.1.4-hffc9a7f_0.conda", "paths_data": {"paths": [{"_path": "Library/include/cudnn.h", "path_type": "hardlink", "sha256": "1082d51d3b564bace8ef6fc6ee335b668b2bfa517f57c06efd428263e5c21855", "sha256_in_prefix": "1082d51d3b564bace8ef6fc6ee335b668b2bfa517f57c06efd428263e5c21855", "size_in_bytes": 2841}, {"_path": "Library/include/cudnn_adv.h", "path_type": "hardlink", "sha256": "f5efac958e3f5452921c22ebc35591d4fcccd3a8bfd5bf949a549baef5960dd0", "sha256_in_prefix": "f5efac958e3f5452921c22ebc35591d4fcccd3a8bfd5bf949a549baef5960dd0", "size_in_bytes": 30855}, {"_path": "Library/include/cudnn_backend.h", "path_type": "hardlink", "sha256": "add4f24c61d07edb2b8567894ba6d0c190d00b3addc80161a23d206d1927d45f", "sha256_in_prefix": "add4f24c61d07edb2b8567894ba6d0c190d00b3addc80161a23d206d1927d45f", "size_in_bytes": 2751}, {"_path": "Library/include/cudnn_cnn.h", "path_type": "hardlink", "sha256": "08ff3a221155832a6d9e5587430046097c7c4c359d8f5c0df8bd54d332ce8c9e", "sha256_in_prefix": "08ff3a221155832a6d9e5587430046097c7c4c359d8f5c0df8bd54d332ce8c9e", "size_in_bytes": 36700}, {"_path": "Library/include/cudnn_graph.h", "path_type": "hardlink", "sha256": "46d1b1c1bd117a20e201442f0276100d4c464e716321ff17847e00c126d0f732", "sha256_in_prefix": "46d1b1c1bd117a20e201442f0276100d4c464e716321ff17847e00c126d0f732", "size_in_bytes": 44680}, {"_path": "Library/include/cudnn_ops.h", "path_type": "hardlink", "sha256": "6b6ad52aa407de89bee25d5deacb22298c1794f26602fd4036c2edc614b6e5ce", "sha256_in_prefix": "6b6ad52aa407de89bee25d5deacb22298c1794f26602fd4036c2edc614b6e5ce", "size_in_bytes": 63635}, {"_path": "Library/include/cudnn_version.h", "path_type": "hardlink", "sha256": "c1d9d85150494f530f47782d2e930a4335f43eb6b14684c2bd0e1a5cdbcd4a1c", "sha256_in_prefix": "c1d9d85150494f530f47782d2e930a4335f43eb6b14684c2bd0e1a5cdbcd4a1c", "size_in_bytes": 3114}, {"_path": "Library/lib/cudnn.lib", "path_type": "hardlink", "sha256": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "sha256_in_prefix": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "size_in_bytes": 61694}, {"_path": "Library/lib/cudnn64_9.lib", "path_type": "hardlink", "sha256": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "sha256_in_prefix": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "size_in_bytes": 61694}, {"_path": "Library/lib/cudnn_adv.lib", "path_type": "hardlink", "sha256": "fe721daecb1bec2473ab7b54b5034566c1289e61536eec38bfefbc1afc868530", "sha256_in_prefix": "fe721daecb1bec2473ab7b54b5034566c1289e61536eec38bfefbc1afc868530", "size_in_bytes": 65494}, {"_path": "Library/lib/cudnn_adv64_9.lib", "path_type": "hardlink", "sha256": "fe721daecb1bec2473ab7b54b5034566c1289e61536eec38bfefbc1afc868530", "sha256_in_prefix": "fe721daecb1bec2473ab7b54b5034566c1289e61536eec38bfefbc1afc868530", "size_in_bytes": 65494}, {"_path": "Library/lib/cudnn_cnn.lib", "path_type": "hardlink", "sha256": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "sha256_in_prefix": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "size_in_bytes": 20946}, {"_path": "Library/lib/cudnn_cnn64_9.lib", "path_type": "hardlink", "sha256": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "sha256_in_prefix": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "size_in_bytes": 20946}, {"_path": "Library/lib/cudnn_engines_precompiled.lib", "path_type": "hardlink", "sha256": "c77b859997ddbe5d4c0eb8dc4a6517dbcbac53ac67f986e4f343ab3eed232dad", "sha256_in_prefix": "c77b859997ddbe5d4c0eb8dc4a6517dbcbac53ac67f986e4f343ab3eed232dad", "size_in_bytes": 503916}, {"_path": "Library/lib/cudnn_engines_precompiled64_9.lib", "path_type": "hardlink", "sha256": "c77b859997ddbe5d4c0eb8dc4a6517dbcbac53ac67f986e4f343ab3eed232dad", "sha256_in_prefix": "c77b859997ddbe5d4c0eb8dc4a6517dbcbac53ac67f986e4f343ab3eed232dad", "size_in_bytes": 503916}, {"_path": "Library/lib/cudnn_engines_runtime_compiled.lib", "path_type": "hardlink", "sha256": "4fff79affea8af70a9dc450e90f91ee4f184fd1b9d62bc4cefc9f3af099ba64d", "sha256_in_prefix": "4fff79affea8af70a9dc450e90f91ee4f184fd1b9d62bc4cefc9f3af099ba64d", "size_in_bytes": 6572}, {"_path": "Library/lib/cudnn_engines_runtime_compiled64_9.lib", "path_type": "hardlink", "sha256": "4fff79affea8af70a9dc450e90f91ee4f184fd1b9d62bc4cefc9f3af099ba64d", "sha256_in_prefix": "4fff79affea8af70a9dc450e90f91ee4f184fd1b9d62bc4cefc9f3af099ba64d", "size_in_bytes": 6572}, {"_path": "Library/lib/cudnn_graph.lib", "path_type": "hardlink", "sha256": "35e60cfeb60d55917e4e238a38307571e07481ce5a5110ce3a1f5032aac7baa1", "sha256_in_prefix": "35e60cfeb60d55917e4e238a38307571e07481ce5a5110ce3a1f5032aac7baa1", "size_in_bytes": 1028956}, {"_path": "Library/lib/cudnn_graph64_9.lib", "path_type": "hardlink", "sha256": "35e60cfeb60d55917e4e238a38307571e07481ce5a5110ce3a1f5032aac7baa1", "sha256_in_prefix": "35e60cfeb60d55917e4e238a38307571e07481ce5a5110ce3a1f5032aac7baa1", "size_in_bytes": 1028956}, {"_path": "Library/lib/cudnn_heuristic.lib", "path_type": "hardlink", "sha256": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "sha256_in_prefix": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "size_in_bytes": 4436}, {"_path": "Library/lib/cudnn_heuristic64_9.lib", "path_type": "hardlink", "sha256": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "sha256_in_prefix": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "size_in_bytes": 4436}, {"_path": "Library/lib/cudnn_ops.lib", "path_type": "hardlink", "sha256": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "sha256_in_prefix": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "size_in_bytes": 45720}, {"_path": "Library/lib/cudnn_ops64_9.lib", "path_type": "hardlink", "sha256": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "sha256_in_prefix": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "size_in_bytes": 45720}], "paths_version": 1}, "requested_spec": "None", "sha256": "66ea382c3fa5b3a5fa448166865b154a7d6d263f981243a96ddb045278a75bb2", "size": 154532, "subdir": "win-64", "timestamp": 1747774979000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/libcudnn-dev-9.10.1.4-hffc9a7f_0.conda", "version": "9.10.1.4"}