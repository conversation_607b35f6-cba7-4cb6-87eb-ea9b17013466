{"arch": "x86_64", "build": "hcd6fe79_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["bzip2 >=1.0.8,<2.0a0", "cpp-expected", "fmt >=9.1.0,<10.0a0", "libarchive >=3.7.7,<3.8.0a0", "libcurl >=8.4.0,<9.0a0", "libsolv >=0.7.30,<0.8.0a0", "n<PERSON><PERSON>_json", "openssl >=3.0.15,<4.0a0", "reproc >=14.2,<15.0a0", "reproc-cpp >=14.2,<15.0a0", "simdjson >=3.10.1,<3.11.0a0", "spdlog", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "yaml-cpp >=0.8.0,<0.9.0a0", "zstd >=1.5.2,<1.6.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libmamba-2.0.5-hcd6fe79_1", "files": ["Library/bin/libmamba.dll", "Library/bin/mamba-package.exe", "Library/etc/profile.d/mamba.sh", "Library/include/mamba/api/c_api.h", "Library/include/mamba/api/channel_loader.hpp", "Library/include/mamba/api/clean.hpp", "Library/include/mamba/api/config.hpp", "Library/include/mamba/api/configuration.hpp", "Library/include/mamba/api/configuration_impl.hpp", "Library/include/mamba/api/constants.hpp", "Library/include/mamba/api/create.hpp", "Library/include/mamba/api/info.hpp", "Library/include/mamba/api/install.hpp", "Library/include/mamba/api/list.hpp", "Library/include/mamba/api/remove.hpp", "Library/include/mamba/api/repoquery.hpp", "Library/include/mamba/api/shell.hpp", "Library/include/mamba/api/update.hpp", "Library/include/mamba/core/activation.hpp", "Library/include/mamba/core/channel_context.hpp", "Library/include/mamba/core/common_types.hpp", "Library/include/mamba/core/context.hpp", "Library/include/mamba/core/download_progress_bar.hpp", "Library/include/mamba/core/env_lockfile.hpp", "Library/include/mamba/core/environments_manager.hpp", "Library/include/mamba/core/error_handling.hpp", "Library/include/mamba/core/execution.hpp", "Library/include/mamba/core/fsutil.hpp", "Library/include/mamba/core/history.hpp", "Library/include/mamba/core/invoke.hpp", "Library/include/mamba/core/link.hpp", "Library/include/mamba/core/menuinst.hpp", "Library/include/mamba/core/output.hpp", "Library/include/mamba/core/package_cache.hpp", "Library/include/mamba/core/package_database_loader.hpp", "Library/include/mamba/core/package_fetcher.hpp", "Library/include/mamba/core/package_handling.hpp", "Library/include/mamba/core/package_paths.hpp", "Library/include/mamba/core/palette.hpp", "Library/include/mamba/core/pinning.hpp", "Library/include/mamba/core/prefix_data.hpp", "Library/include/mamba/core/progress_bar.hpp", "Library/include/mamba/core/query.hpp", "Library/include/mamba/core/repo_checker_store.hpp", "Library/include/mamba/core/run.hpp", "Library/include/mamba/core/shell_init.hpp", "Library/include/mamba/core/subdirdata.hpp", "Library/include/mamba/core/tasksync.hpp", "Library/include/mamba/core/thread_utils.hpp", "Library/include/mamba/core/timeref.hpp", "Library/include/mamba/core/transaction.hpp", "Library/include/mamba/core/transaction_context.hpp", "Library/include/mamba/core/util.hpp", "Library/include/mamba/core/util_os.hpp", "Library/include/mamba/core/util_scope.hpp", "Library/include/mamba/core/virtual_packages.hpp", "Library/include/mamba/download/downloader.hpp", "Library/include/mamba/download/mirror.hpp", "Library/include/mamba/download/mirror_map.hpp", "Library/include/mamba/download/request.hpp", "Library/include/mamba/fs/filesystem.hpp", "Library/include/mamba/solver/libsolv/database.hpp", "Library/include/mamba/solver/libsolv/parameters.hpp", "Library/include/mamba/solver/libsolv/repo_info.hpp", "Library/include/mamba/solver/libsolv/solver.hpp", "Library/include/mamba/solver/libsolv/unsolvable.hpp", "Library/include/mamba/solver/problems_graph.hpp", "Library/include/mamba/solver/request.hpp", "Library/include/mamba/solver/solution.hpp", "Library/include/mamba/specs/archive.hpp", "Library/include/mamba/specs/authentication_info.hpp", "Library/include/mamba/specs/build_number_spec.hpp", "Library/include/mamba/specs/channel.hpp", "Library/include/mamba/specs/chimera_string_spec.hpp", "Library/include/mamba/specs/conda_url.hpp", "Library/include/mamba/specs/error.hpp", "Library/include/mamba/specs/glob_spec.hpp", "Library/include/mamba/specs/match_spec.hpp", "Library/include/mamba/specs/package_info.hpp", "Library/include/mamba/specs/platform.hpp", "Library/include/mamba/specs/regex_spec.hpp", "Library/include/mamba/specs/repo_data.hpp", "Library/include/mamba/specs/unresolved_channel.hpp", "Library/include/mamba/specs/version.hpp", "Library/include/mamba/specs/version_spec.hpp", "Library/include/mamba/util/build.hpp", "Library/include/mamba/util/cast.hpp", "Library/include/mamba/util/cfile.hpp", "Library/include/mamba/util/compare.hpp", "Library/include/mamba/util/conditional.hpp", "Library/include/mamba/util/cryptography.hpp", "Library/include/mamba/util/deprecation.hpp", "Library/include/mamba/util/encoding.hpp", "Library/include/mamba/util/environment.hpp", "Library/include/mamba/util/flat_binary_tree.hpp", "Library/include/mamba/util/flat_bool_expr_tree.hpp", "Library/include/mamba/util/flat_set.hpp", "Library/include/mamba/util/functional.hpp", "Library/include/mamba/util/graph.hpp", "Library/include/mamba/util/heap_optional.hpp", "Library/include/mamba/util/iterator.hpp", "Library/include/mamba/util/json.hpp", "Library/include/mamba/util/loop_control.hpp", "Library/include/mamba/util/os.hpp", "Library/include/mamba/util/os_linux.hpp", "Library/include/mamba/util/os_osx.hpp", "Library/include/mamba/util/os_unix.hpp", "Library/include/mamba/util/os_win.hpp", "Library/include/mamba/util/parsers.hpp", "Library/include/mamba/util/path_manip.hpp", "Library/include/mamba/util/random.hpp", "Library/include/mamba/util/string.hpp", "Library/include/mamba/util/tuple_hash.hpp", "Library/include/mamba/util/type_traits.hpp", "Library/include/mamba/util/url.hpp", "Library/include/mamba/util/url_manip.hpp", "Library/include/mamba/util/variant_cmp.hpp", "Library/include/mamba/util/weakening_map.hpp", "Library/include/mamba/validation/errors.hpp", "Library/include/mamba/validation/keys.hpp", "Library/include/mamba/validation/repo_checker.hpp", "Library/include/mamba/validation/tools.hpp", "Library/include/mamba/validation/update_framework.hpp", "Library/include/mamba/validation/update_framework_v0_6.hpp", "Library/include/mamba/validation/update_framework_v1.hpp", "Library/include/mamba/version.hpp", "Library/lib/cmake/libmamba/libmambaConfig.cmake", "Library/lib/cmake/libmamba/libmambaConfigVersion.cmake", "Library/lib/cmake/libmamba/libmambaTargets-release.cmake", "Library/lib/cmake/libmamba/libmambaTargets.cmake", "Library/lib/libmamba.lib"], "fn": "libmamba-2.0.5-hcd6fe79_1.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libmamba-2.0.5-hcd6fe79_1", "type": 1}, "md5": "ecac2cc6e1ce9d3229a58b083bc8bd79", "name": "libmamba", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libmamba-2.0.5-hcd6fe79_1.conda", "paths_data": {"paths": [{"_path": "Library/etc/profile.d/mamba.sh", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_627vsv8bhu/croot/mamba-split_1734469608328/_h_env", "sha256": "10625f6a6f77d38e1c6af948f2efa3f9d9c8f50dd2e60e64073818bc9bd46a2f", "sha256_in_prefix": "ba3fbee1b03af8478b70ec7a1c9fb84a00b57dfb606eefad6cf7942495c24b85", "size_in_bytes": 449}], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::libmamba==2.0.5=hcd6fe79_1[md5=ecac2cc6e1ce9d3229a58b083bc8bd79]", "sha256": "2f5e6488fa335b5d398d9674c54bb00b08f909f6a17cecef2f7d5d40f0d702a4", "size": 4949589, "subdir": "win-64", "timestamp": 1734470137000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libmamba-2.0.5-hcd6fe79_1.conda", "version": "2.0.5"}