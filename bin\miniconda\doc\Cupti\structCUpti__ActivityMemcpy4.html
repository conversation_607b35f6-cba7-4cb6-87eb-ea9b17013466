<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpy4 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpy4 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpy4" -->The activity record for memory copies. (deprecated in CUDA 11.6).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#b548aedee9ae31910b21e61d88e3505a">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#6b1f30cafe0f89f62f335848283620a3">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#79565723da305c877f8fad81b3c4c4cf">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#cc6298d3e24628e6e7b02d972970727f">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#dff06c1564e22cefdb2b026436d53420">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#2a55a57eb160e8519f11eb761d6d92cb">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#9e7622e9401b477f8c0159b9134d0a53">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#7fa84a0116642c7960a8cf4066daefd5">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#ec99268a44b98e7889d98fda87218b94">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#7eaf6c88caea3b6454072c041784754e">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#b7d453693a876e6f40262c0a21d4ad30">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#523c2e4d5e5fd50ea2ce7b8ec24d89ff">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#7680663b2361acb6ee9d213981b3bb51">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#a7c454e0bb17446cbda983ee6cf4920a">runtimeCorrelationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#3caeffd2f7e67b198888edfe37af176b">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#e078e7e93991fd57ae7fa5fd8d449fe7">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy4.html#60ab70288f5d57d036c590659d49df99">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory copy (CUPTI_ACTIVITY_KIND_MEMCPY). <hr><h2>Field Documentation</h2>
<a class="anchor" name="b548aedee9ae31910b21e61d88e3505a"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::bytes" ref="b548aedee9ae31910b21e61d88e3505a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy4.html#b548aedee9ae31910b21e61d88e3505a">CUpti_ActivityMemcpy4::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="6b1f30cafe0f89f62f335848283620a3"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::contextId" ref="6b1f30cafe0f89f62f335848283620a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy4.html#6b1f30cafe0f89f62f335848283620a3">CUpti_ActivityMemcpy4::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="79565723da305c877f8fad81b3c4c4cf"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::copyKind" ref="79565723da305c877f8fad81b3c4c4cf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy4.html#79565723da305c877f8fad81b3c4c4cf">CUpti_ActivityMemcpy4::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="cc6298d3e24628e6e7b02d972970727f"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::correlationId" ref="cc6298d3e24628e6e7b02d972970727f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy4.html#cc6298d3e24628e6e7b02d972970727f">CUpti_ActivityMemcpy4::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="dff06c1564e22cefdb2b026436d53420"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::deviceId" ref="dff06c1564e22cefdb2b026436d53420" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy4.html#dff06c1564e22cefdb2b026436d53420">CUpti_ActivityMemcpy4::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="2a55a57eb160e8519f11eb761d6d92cb"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::dstKind" ref="2a55a57eb160e8519f11eb761d6d92cb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy4.html#2a55a57eb160e8519f11eb761d6d92cb">CUpti_ActivityMemcpy4::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="9e7622e9401b477f8c0159b9134d0a53"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::end" ref="9e7622e9401b477f8c0159b9134d0a53" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy4.html#9e7622e9401b477f8c0159b9134d0a53">CUpti_ActivityMemcpy4::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="7fa84a0116642c7960a8cf4066daefd5"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::flags" ref="7fa84a0116642c7960a8cf4066daefd5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy4.html#7fa84a0116642c7960a8cf4066daefd5">CUpti_ActivityMemcpy4::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="ec99268a44b98e7889d98fda87218b94"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::graphId" ref="ec99268a44b98e7889d98fda87218b94" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy4.html#ec99268a44b98e7889d98fda87218b94">CUpti_ActivityMemcpy4::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that executed this memcpy through graph launch. This field will be 0 if the memcpy is not done through graph launch. 
</div>
</div><p>
<a class="anchor" name="7eaf6c88caea3b6454072c041784754e"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::graphNodeId" ref="7eaf6c88caea3b6454072c041784754e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy4.html#7eaf6c88caea3b6454072c041784754e">CUpti_ActivityMemcpy4::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed this memcpy through graph launch. This field will be 0 if the memcpy is not done through graph launch. 
</div>
</div><p>
<a class="anchor" name="b7d453693a876e6f40262c0a21d4ad30"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::kind" ref="b7d453693a876e6f40262c0a21d4ad30" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpy4.html#b7d453693a876e6f40262c0a21d4ad30">CUpti_ActivityMemcpy4::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY. 
</div>
</div><p>
<a class="anchor" name="523c2e4d5e5fd50ea2ce7b8ec24d89ff"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::padding" ref="523c2e4d5e5fd50ea2ce7b8ec24d89ff" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy4.html#523c2e4d5e5fd50ea2ce7b8ec24d89ff">CUpti_ActivityMemcpy4::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="7680663b2361acb6ee9d213981b3bb51"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::reserved0" ref="7680663b2361acb6ee9d213981b3bb51" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpy4.html#7680663b2361acb6ee9d213981b3bb51">CUpti_ActivityMemcpy4::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="a7c454e0bb17446cbda983ee6cf4920a"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::runtimeCorrelationId" ref="a7c454e0bb17446cbda983ee6cf4920a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy4.html#a7c454e0bb17446cbda983ee6cf4920a">CUpti_ActivityMemcpy4::runtimeCorrelationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The runtime correlation ID of the memory copy. Each memory copy is assigned a unique runtime correlation ID that is identical to the correlation ID in the runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="3caeffd2f7e67b198888edfe37af176b"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::srcKind" ref="3caeffd2f7e67b198888edfe37af176b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy4.html#3caeffd2f7e67b198888edfe37af176b">CUpti_ActivityMemcpy4::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="e078e7e93991fd57ae7fa5fd8d449fe7"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::start" ref="e078e7e93991fd57ae7fa5fd8d449fe7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy4.html#e078e7e93991fd57ae7fa5fd8d449fe7">CUpti_ActivityMemcpy4::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="60ab70288f5d57d036c590659d49df99"></a><!-- doxytag: member="CUpti_ActivityMemcpy4::streamId" ref="60ab70288f5d57d036c590659d49df99" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy4.html#60ab70288f5d57d036c590659d49df99">CUpti_ActivityMemcpy4::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
