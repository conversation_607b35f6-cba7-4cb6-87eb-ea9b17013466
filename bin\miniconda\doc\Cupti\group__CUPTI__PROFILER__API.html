<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Profiling API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Profiling API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginPass__Params.html">CUpti_Profiler_BeginPass_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerBeginPass.  <a href="structCUpti__Profiler__BeginPass__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html">CUpti_Profiler_BeginSession_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerBeginSession.  <a href="structCUpti__Profiler__BeginSession__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerCounterDataImageCalculateScratchBufferSize.  <a href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerCounterDataImageCalculateSize.  <a href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html">CUpti_Profiler_CounterDataImage_Initialize_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerCounterDataImageInitialize.  <a href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerCounterDataImageInitializeScratchBuffer.  <a href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html">CUpti_Profiler_CounterDataImageOptions</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Input parameter to define the counterDataImage.  <a href="structCUpti__Profiler__CounterDataImageOptions.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeInitialize__Params.html">CUpti_Profiler_DeInitialize_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Default parameter for cuptiProfilerDeInitialize.  <a href="structCUpti__Profiler__DeInitialize__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html">CUpti_Profiler_DeviceSupported_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerDeviceSupported.  <a href="structCUpti__Profiler__DeviceSupported__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DisableProfiling__Params.html">CUpti_Profiler_DisableProfiling_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerDisableProfiling.  <a href="structCUpti__Profiler__DisableProfiling__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EnableProfiling__Params.html">CUpti_Profiler_EnableProfiling_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerEnableProfiling.  <a href="structCUpti__Profiler__EnableProfiling__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EndPass__Params.html">CUpti_Profiler_EndPass_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerEndPass.  <a href="structCUpti__Profiler__EndPass__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EndSession__Params.html">CUpti_Profiler_EndSession_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerEndSession.  <a href="structCUpti__Profiler__EndSession__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html">CUpti_Profiler_FlushCounterData_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerFlushCounterData.  <a href="structCUpti__Profiler__FlushCounterData__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html">CUpti_Profiler_GetCounterAvailability_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerGetCounterAvailability.  <a href="structCUpti__Profiler__GetCounterAvailability__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__Initialize__Params.html">CUpti_Profiler_Initialize_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Default parameter for cuptiProfilerInitialize.  <a href="structCUpti__Profiler__Initialize__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html">CUpti_Profiler_IsPassCollected_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerIsPassCollected.  <a href="structCUpti__Profiler__IsPassCollected__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html">CUpti_Profiler_SetConfig_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerSetConfig.  <a href="structCUpti__Profiler__SetConfig__Params.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__UnsetConfig__Params.html">CUpti_Profiler_UnsetConfig_Params</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Params for cuptiProfilerUnsetConfig.  <a href="structCUpti__Profiler__UnsetConfig__Params.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gge9c7f68d6d33973878b56e39fb9cfa521531a22440546f0922fb6896c4ee03ca">CUPTI_PROFILER_CONFIGURATION_UNKNOWN</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gge9c7f68d6d33973878b56e39fb9cfa527d0d69f3739dd9b9a3cfde9c525b67cd">CUPTI_PROFILER_CONFIGURATION_UNSUPPORTED</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gge9c7f68d6d33973878b56e39fb9cfa526186602beff3f8d1a3b2ad605007f5e6">CUPTI_PROFILER_CONFIGURATION_DISABLED</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gge9c7f68d6d33973878b56e39fb9cfa5293bf24837eb116ff291d565e63b86eec">CUPTI_PROFILER_CONFIGURATION_SUPPORTED</a>
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Generic support level enum for CUPTI.  <a href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#ga6faef578b53da403b15878e05d1b395">CUpti_ProfilerRange</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b3953a0ae3eb8dfc68f682cb4dc48891c1c3">CUPTI_Range_INVALID</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b395ee196550a6a859439d2a4aa4ab531cf0">CUPTI_AutoRange</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b3953e0453d960147ca83509d1cf065fd130">CUPTI_UserRange</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b39567699d7d36b4984204519c6ce9da2fc3">CUPTI_Range_COUNT</a>
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Profiler range attribute.  <a href="group__CUPTI__PROFILER__API.html#ga6faef578b53da403b15878e05d1b395">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#gd6960f26de20f317a784ee565743e457">CUpti_ProfilerReplayMode</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e4579131a584b3b6d3f74912bf8364b2d54e">CUPTI_Replay_INVALID</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457298b65c9526dabe7113fdb5b9e45c957">CUPTI_ApplicationReplay</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457c2e3d8f8e8ad65399ae39f6d0e266bc8">CUPTI_KernelReplay</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457a052699f076ad798166b7b653b0ce14e">CUPTI_UserReplay</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e4576908be49535e25864fe614d148be00ba">CUPTI_Replay_COUNT</a>
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Profiler replay attribute.  <a href="group__CUPTI__PROFILER__API.html#gd6960f26de20f317a784ee565743e457">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g8c96f8bcc440b84a243369d1e41d1520">cuptiProfilerBeginPass</a> (<a class="el" href="structCUpti__Profiler__BeginPass__Params.html">CUpti_Profiler_BeginPass_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Replay API: used for multipass collection.  <a href="#g8c96f8bcc440b84a243369d1e41d1520"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g19d3db3d5081911499ee8884f8a858ab">cuptiProfilerBeginSession</a> (<a class="el" href="structCUpti__Profiler__BeginSession__Params.html">CUpti_Profiler_BeginSession_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Begin profiling session sets up the profiling on the device.  <a href="#g19d3db3d5081911499ee8884f8a858ab"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g21e53b286d5fcca64ef63b92694deb17">cuptiProfilerCounterDataImageCalculateScratchBufferSize</a> (<a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A temporary storage for CounterData image needed for internal operations.  <a href="#g21e53b286d5fcca64ef63b92694deb17"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g5bb34e2042b3675158f2d5a922f34125">cuptiProfilerCounterDataImageCalculateSize</a> (<a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A CounterData image allocates space for values for each counter for each range.  <a href="#g5bb34e2042b3675158f2d5a922f34125"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g9d49c7e7e0707ad0260effdc5ab9021b"></a><!-- doxytag: member="CUPTI_PROFILER_API::cuptiProfilerDeInitialize" ref="g9d49c7e7e0707ad0260effdc5ab9021b" args="(CUpti_Profiler_DeInitialize_Params *pParams)" -->
<a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g9d49c7e7e0707ad0260effdc5ab9021b">cuptiProfilerDeInitialize</a> (<a class="el" href="structCUpti__Profiler__DeInitialize__Params.html">CUpti_Profiler_DeInitialize_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">DeInitializes the profiler interface. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#gef5552aa70dede36361da992a82f2cb4">cuptiProfilerDeviceSupported</a> (<a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html">CUpti_Profiler_DeviceSupported_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Query device compatibility with Profiling API.  <a href="#gef5552aa70dede36361da992a82f2cb4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#ga64dfbcde27a202af83e1795fdd4a484">cuptiProfilerDisableProfiling</a> (<a class="el" href="structCUpti__Profiler__DisableProfiling__Params.html">CUpti_Profiler_DisableProfiling_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Disable Profiling.  <a href="#ga64dfbcde27a202af83e1795fdd4a484"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g287bf4dc8e3acb547a1d082c8b337837">cuptiProfilerEnableProfiling</a> (<a class="el" href="structCUpti__Profiler__EnableProfiling__Params.html">CUpti_Profiler_EnableProfiling_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enables Profiling.  <a href="#g287bf4dc8e3acb547a1d082c8b337837"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#gaab3e227e392cd92da172d49506f6cd8">cuptiProfilerEndPass</a> (<a class="el" href="structCUpti__Profiler__EndPass__Params.html">CUpti_Profiler_EndPass_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Replay API: used for multipass collection.  <a href="#gaab3e227e392cd92da172d49506f6cd8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#gbe577146ea3e1f187913ae906260530d">cuptiProfilerEndSession</a> (<a class="el" href="structCUpti__Profiler__EndSession__Params.html">CUpti_Profiler_EndSession_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Ends profiling session.  <a href="#gbe577146ea3e1f187913ae906260530d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#gd2c936e79a90a46a29dcfc409166c39b">cuptiProfilerFlushCounterData</a> (<a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html">CUpti_Profiler_FlushCounterData_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Decode all the submitted passes.  <a href="#gd2c936e79a90a46a29dcfc409166c39b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#ge88e65505f6f863ac5b85026cd12da5c">cuptiProfilerGetCounterAvailability</a> (<a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html">CUpti_Profiler_GetCounterAvailability_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Query counter availibility.  <a href="#ge88e65505f6f863ac5b85026cd12da5c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#gd64f44975cdfa93a1c179eb8c8d51b53">cuptiProfilerInitialize</a> (<a class="el" href="structCUpti__Profiler__Initialize__Params.html">CUpti_Profiler_Initialize_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initializes the profiler interface.  <a href="#gd64f44975cdfa93a1c179eb8c8d51b53"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g4f57f687f8837e74e73668cd8412259b"></a><!-- doxytag: member="CUPTI_PROFILER_API::cuptiProfilerIsPassCollected" ref="g4f57f687f8837e74e73668cd8412259b" args="(CUpti_Profiler_IsPassCollected_Params *pParams)" -->
<a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g4f57f687f8837e74e73668cd8412259b">cuptiProfilerIsPassCollected</a> (<a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html">CUpti_Profiler_IsPassCollected_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Asynchronous call to query if the submitted pass to GPU is collected. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g4fb53a9736ed17cd8910dc491b958f9a">cuptiProfilerPopRange</a> (CUpti_Profiler_PopRange_Params *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Range API's : Pop user range.  <a href="#g4fb53a9736ed17cd8910dc491b958f9a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#gb300cc3e24b408e4c5cfed310fed084f">cuptiProfilerPushRange</a> (CUpti_Profiler_PushRange_Params *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Range API's : Push user range.  <a href="#gb300cc3e24b408e4c5cfed310fed084f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#ga1ff2f08b95cf9534b3ec1e73dcfda5b">cuptiProfilerSetConfig</a> (<a class="el" href="structCUpti__Profiler__SetConfig__Params.html">CUpti_Profiler_SetConfig_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set metrics configuration to be profiled.  <a href="#ga1ff2f08b95cf9534b3ec1e73dcfda5b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g20449fe304f3f3cd0b174f63ab868054"></a><!-- doxytag: member="CUPTI_PROFILER_API::cuptiProfilerUnsetConfig" ref="g20449fe304f3f3cd0b174f63ab868054" args="(CUpti_Profiler_UnsetConfig_Params *pParams)" -->
<a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__PROFILER__API.html#g20449fe304f3f3cd0b174f63ab868054">cuptiProfilerUnsetConfig</a> (<a class="el" href="structCUpti__Profiler__UnsetConfig__Params.html">CUpti_Profiler_UnsetConfig_Params</a> *pParams)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Unset metrics configuration profiled. <br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI Profiling API. <hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="ge9c7f68d6d33973878b56e39fb9cfa52"></a><!-- doxytag: member="cupti_profiler_target.h::CUpti_Profiler_Support_Level" ref="ge9c7f68d6d33973878b56e39fb9cfa52" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gge9c7f68d6d33973878b56e39fb9cfa521531a22440546f0922fb6896c4ee03ca"></a><!-- doxytag: member="CUPTI_PROFILER_CONFIGURATION_UNKNOWN" ref="gge9c7f68d6d33973878b56e39fb9cfa521531a22440546f0922fb6896c4ee03ca" args="" -->CUPTI_PROFILER_CONFIGURATION_UNKNOWN</em>&nbsp;</td><td>
Configuration support level unknown - either detection code errored out before setting this value, or unable to determine it. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge9c7f68d6d33973878b56e39fb9cfa527d0d69f3739dd9b9a3cfde9c525b67cd"></a><!-- doxytag: member="CUPTI_PROFILER_CONFIGURATION_UNSUPPORTED" ref="gge9c7f68d6d33973878b56e39fb9cfa527d0d69f3739dd9b9a3cfde9c525b67cd" args="" -->CUPTI_PROFILER_CONFIGURATION_UNSUPPORTED</em>&nbsp;</td><td>
Profiling is unavailable. For specific feature fields, this means that the current configuration of this feature does not work with profiling. For instance, SLI-enabled devices do not support profiling, and this value would be returned for SLI on an SLI-enabled device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge9c7f68d6d33973878b56e39fb9cfa526186602beff3f8d1a3b2ad605007f5e6"></a><!-- doxytag: member="CUPTI_PROFILER_CONFIGURATION_DISABLED" ref="gge9c7f68d6d33973878b56e39fb9cfa526186602beff3f8d1a3b2ad605007f5e6" args="" -->CUPTI_PROFILER_CONFIGURATION_DISABLED</em>&nbsp;</td><td>
Profiling would be available for this configuration, but was disabled by the system. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge9c7f68d6d33973878b56e39fb9cfa5293bf24837eb116ff291d565e63b86eec"></a><!-- doxytag: member="CUPTI_PROFILER_CONFIGURATION_SUPPORTED" ref="gge9c7f68d6d33973878b56e39fb9cfa5293bf24837eb116ff291d565e63b86eec" args="" -->CUPTI_PROFILER_CONFIGURATION_SUPPORTED</em>&nbsp;</td><td>
Profiling is supported. For specific feature fields, this means that the current configuration of this feature works with profiling. For instance, SLI-enabled devices do not support profiling, and this value would only be returned for devices which are not SLI-enabled. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="ga6faef578b53da403b15878e05d1b395"></a><!-- doxytag: member="cupti_profiler_target.h::CUpti_ProfilerRange" ref="ga6faef578b53da403b15878e05d1b395" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PROFILER__API.html#ga6faef578b53da403b15878e05d1b395">CUpti_ProfilerRange</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A metric enabled in the session's configuration is collected separately per unique range-stack in the pass. This is an attribute to collect metrics around each kernel in a profiling session or in an user defined range. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gga6faef578b53da403b15878e05d1b3953a0ae3eb8dfc68f682cb4dc48891c1c3"></a><!-- doxytag: member="CUPTI_Range_INVALID" ref="gga6faef578b53da403b15878e05d1b3953a0ae3eb8dfc68f682cb4dc48891c1c3" args="" -->CUPTI_Range_INVALID</em>&nbsp;</td><td>
Invalid value </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga6faef578b53da403b15878e05d1b395ee196550a6a859439d2a4aa4ab531cf0"></a><!-- doxytag: member="CUPTI_AutoRange" ref="gga6faef578b53da403b15878e05d1b395ee196550a6a859439d2a4aa4ab531cf0" args="" -->CUPTI_AutoRange</em>&nbsp;</td><td>
Ranges are auto defined around each kernel in a profiling session </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga6faef578b53da403b15878e05d1b3953e0453d960147ca83509d1cf065fd130"></a><!-- doxytag: member="CUPTI_UserRange" ref="gga6faef578b53da403b15878e05d1b3953e0453d960147ca83509d1cf065fd130" args="" -->CUPTI_UserRange</em>&nbsp;</td><td>
A range in which metric data to be collected is defined by the user </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga6faef578b53da403b15878e05d1b39567699d7d36b4984204519c6ce9da2fc3"></a><!-- doxytag: member="CUPTI_Range_COUNT" ref="gga6faef578b53da403b15878e05d1b39567699d7d36b4984204519c6ce9da2fc3" args="" -->CUPTI_Range_COUNT</em>&nbsp;</td><td>
Range count </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd6960f26de20f317a784ee565743e457"></a><!-- doxytag: member="cupti_profiler_target.h::CUpti_ProfilerReplayMode" ref="gd6960f26de20f317a784ee565743e457" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__PROFILER__API.html#gd6960f26de20f317a784ee565743e457">CUpti_ProfilerReplayMode</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For metrics which require multipass collection, a replay of the GPU kernel(s) is required. This is an attribute which specify how the replay of the kernel(s) to be measured is done. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd6960f26de20f317a784ee565743e4579131a584b3b6d3f74912bf8364b2d54e"></a><!-- doxytag: member="CUPTI_Replay_INVALID" ref="ggd6960f26de20f317a784ee565743e4579131a584b3b6d3f74912bf8364b2d54e" args="" -->CUPTI_Replay_INVALID</em>&nbsp;</td><td>
Invalid Value </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd6960f26de20f317a784ee565743e457298b65c9526dabe7113fdb5b9e45c957"></a><!-- doxytag: member="CUPTI_ApplicationReplay" ref="ggd6960f26de20f317a784ee565743e457298b65c9526dabe7113fdb5b9e45c957" args="" -->CUPTI_ApplicationReplay</em>&nbsp;</td><td>
Replay is done by CUPTI user around the process </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd6960f26de20f317a784ee565743e457c2e3d8f8e8ad65399ae39f6d0e266bc8"></a><!-- doxytag: member="CUPTI_KernelReplay" ref="ggd6960f26de20f317a784ee565743e457c2e3d8f8e8ad65399ae39f6d0e266bc8" args="" -->CUPTI_KernelReplay</em>&nbsp;</td><td>
Replay is done around kernel implicitly by CUPTI </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd6960f26de20f317a784ee565743e457a052699f076ad798166b7b653b0ce14e"></a><!-- doxytag: member="CUPTI_UserReplay" ref="ggd6960f26de20f317a784ee565743e457a052699f076ad798166b7b653b0ce14e" args="" -->CUPTI_UserReplay</em>&nbsp;</td><td>
Replay is done by CUPTI user within a process </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd6960f26de20f317a784ee565743e4576908be49535e25864fe614d148be00ba"></a><!-- doxytag: member="CUPTI_Replay_COUNT" ref="ggd6960f26de20f317a784ee565743e4576908be49535e25864fe614d148be00ba" args="" -->CUPTI_Replay_COUNT</em>&nbsp;</td><td>
Replay count </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g8c96f8bcc440b84a243369d1e41d1520"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerBeginPass" ref="g8c96f8bcc440b84a243369d1e41d1520" args="(CUpti_Profiler_BeginPass_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerBeginPass           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__BeginPass__Params.html">CUpti_Profiler_BeginPass_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
These APIs are used if user chooses to replay by itself <a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457a052699f076ad798166b7b653b0ce14e">CUPTI_UserReplay</a> or <a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457298b65c9526dabe7113fdb5b9e45c957">CUPTI_ApplicationReplay</a> for multipass collection of the metrics configurations. It's a no-op in case of <a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457c2e3d8f8e8ad65399ae39f6d0e266bc8">CUPTI_KernelReplay</a>. 
</div>
</div><p>
<a class="anchor" name="g19d3db3d5081911499ee8884f8a858ab"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerBeginSession" ref="g19d3db3d5081911499ee8884f8a858ab" args="(CUpti_Profiler_BeginSession_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerBeginSession           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html">CUpti_Profiler_BeginSession_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Although, it doesn't start the profiling but GPU resources needed for profiling are allocated. Outside of a session, the GPU will return to its normal operating state. 
</div>
</div><p>
<a class="anchor" name="g21e53b286d5fcca64ef63b92694deb17"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerCounterDataImageCalculateScratchBufferSize" ref="g21e53b286d5fcca64ef63b92694deb17" args="(CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerCounterDataImageCalculateScratchBufferSize           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Use these APIs to calculate the allocation size and initialize counterData image scratch buffer. 
</div>
</div><p>
<a class="anchor" name="g5bb34e2042b3675158f2d5a922f34125"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerCounterDataImageCalculateSize" ref="g5bb34e2042b3675158f2d5a922f34125" args="(CUpti_Profiler_CounterDataImage_CalculateSize_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerCounterDataImageCalculateSize           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
User borne the resposibility of managing the counterDataImage allocations. CounterDataPrefix contains meta data about the metrics that will be stored in counterDataImage. Use these APIs to calculate the allocation size and initialize counterData image. 
</div>
</div><p>
<a class="anchor" name="gef5552aa70dede36361da992a82f2cb4"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerDeviceSupported" ref="gef5552aa70dede36361da992a82f2cb4" args="(CUpti_Profiler_DeviceSupported_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerDeviceSupported           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html">CUpti_Profiler_DeviceSupported_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Use this call to determine whether a compute device and configuration are compatible with the Profiling API. If the configuration does not support profiling, one of several flags will indicate why. 
</div>
</div><p>
<a class="anchor" name="ga64dfbcde27a202af83e1795fdd4a484"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerDisableProfiling" ref="ga64dfbcde27a202af83e1795fdd4a484" args="(CUpti_Profiler_DisableProfiling_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerDisableProfiling           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__DisableProfiling__Params.html">CUpti_Profiler_DisableProfiling_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
In <a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b395ee196550a6a859439d2a4aa4ab531cf0">CUPTI_AutoRange</a>, these APIs are used to enable/disable profiling for the kernels to be executed in a profiling session. 
</div>
</div><p>
<a class="anchor" name="g287bf4dc8e3acb547a1d082c8b337837"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerEnableProfiling" ref="g287bf4dc8e3acb547a1d082c8b337837" args="(CUpti_Profiler_EnableProfiling_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerEnableProfiling           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__EnableProfiling__Params.html">CUpti_Profiler_EnableProfiling_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
In <a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b395ee196550a6a859439d2a4aa4ab531cf0">CUPTI_AutoRange</a>, these APIs are used to enable/disable profiling for the kernels to be executed in a profiling session. 
</div>
</div><p>
<a class="anchor" name="gaab3e227e392cd92da172d49506f6cd8"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerEndPass" ref="gaab3e227e392cd92da172d49506f6cd8" args="(CUpti_Profiler_EndPass_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerEndPass           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__EndPass__Params.html">CUpti_Profiler_EndPass_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
These APIs are used if user chooses to replay by itself <a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457a052699f076ad798166b7b653b0ce14e">CUPTI_UserReplay</a> or <a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457298b65c9526dabe7113fdb5b9e45c957">CUPTI_ApplicationReplay</a> for multipass collection of the metrics configurations. Its a no-op in case of <a class="el" href="group__CUPTI__PROFILER__API.html#ggd6960f26de20f317a784ee565743e457c2e3d8f8e8ad65399ae39f6d0e266bc8">CUPTI_KernelReplay</a>. Returns information for next pass. 
</div>
</div><p>
<a class="anchor" name="gbe577146ea3e1f187913ae906260530d"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerEndSession" ref="gbe577146ea3e1f187913ae906260530d" args="(CUpti_Profiler_EndSession_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerEndSession           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__EndSession__Params.html">CUpti_Profiler_EndSession_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Frees up the GPU resources acquired for profiling. Outside of a session, the GPU will return to it's normal operating state. 
</div>
</div><p>
<a class="anchor" name="gd2c936e79a90a46a29dcfc409166c39b"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerFlushCounterData" ref="gd2c936e79a90a46a29dcfc409166c39b" args="(CUpti_Profiler_FlushCounterData_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerFlushCounterData           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html">CUpti_Profiler_FlushCounterData_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flush Counter data API to ensure every pass is decoded into the counterDataImage passed at beginSession. This will cause the CPU/GPU sync to collect all the undecoded pass. 
</div>
</div><p>
<a class="anchor" name="ge88e65505f6f863ac5b85026cd12da5c"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerGetCounterAvailability" ref="ge88e65505f6f863ac5b85026cd12da5c" args="(CUpti_Profiler_GetCounterAvailability_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerGetCounterAvailability           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html">CUpti_Profiler_GetCounterAvailability_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Use this API to query counter availability information in a buffer which can be used to filter unavailable raw metrics on host. Note: This API may fail, if any profiling or sampling session is active on the specified context or its device. 
</div>
</div><p>
<a class="anchor" name="gd64f44975cdfa93a1c179eb8c8d51b53"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerInitialize" ref="gd64f44975cdfa93a1c179eb8c8d51b53" args="(CUpti_Profiler_Initialize_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerInitialize           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__Initialize__Params.html">CUpti_Profiler_Initialize_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Loads the required libraries in the process address space. Sets up the hooks with the CUDA driver. 
</div>
</div><p>
<a class="anchor" name="g4fb53a9736ed17cd8910dc491b958f9a"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerPopRange" ref="g4fb53a9736ed17cd8910dc491b958f9a" args="(CUpti_Profiler_PopRange_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerPopRange           </td>
          <td>(</td>
          <td class="paramtype">CUpti_Profiler_PopRange_Params *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Counter data is collected per unique range-stack. Identified by a string label passsed by the user. It's an invalid operation in case of <a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b395ee196550a6a859439d2a4aa4ab531cf0">CUPTI_AutoRange</a>. 
</div>
</div><p>
<a class="anchor" name="gb300cc3e24b408e4c5cfed310fed084f"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerPushRange" ref="gb300cc3e24b408e4c5cfed310fed084f" args="(CUpti_Profiler_PushRange_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerPushRange           </td>
          <td>(</td>
          <td class="paramtype">CUpti_Profiler_PushRange_Params *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Counter data is collected per unique range-stack. Identified by a string label passsed by the user. It's an invalid operation in case of <a class="el" href="group__CUPTI__PROFILER__API.html#gga6faef578b53da403b15878e05d1b395ee196550a6a859439d2a4aa4ab531cf0">CUPTI_AutoRange</a>. 
</div>
</div><p>
<a class="anchor" name="ga1ff2f08b95cf9534b3ec1e73dcfda5b"></a><!-- doxytag: member="cupti_profiler_target.h::cuptiProfilerSetConfig" ref="ga1ff2f08b95cf9534b3ec1e73dcfda5b" args="(CUpti_Profiler_SetConfig_Params *pParams)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiProfilerSetConfig           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__Profiler__SetConfig__Params.html">CUpti_Profiler_SetConfig_Params</a> *&nbsp;</td>
          <td class="paramname"> <em>pParams</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Use these APIs to set the config to profile in a session. It can be used for advanced cases such as where multiple configurations are collected into a single CounterData Image on the need basis, without restarting the session. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
