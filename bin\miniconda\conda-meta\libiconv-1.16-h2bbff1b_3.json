{"arch": "x86_64", "build": "h2bbff1b_3", "build_number": 3, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libiconv-1.16-h2bbff1b_3", "files": ["Library/bin/charset.dll", "Library/bin/iconv.dll", "Library/include/iconv.h", "Library/include/libcharset.h", "Library/include/localcharset.h", "Library/lib/charset.lib", "Library/lib/iconv.lib"], "fn": "libiconv-1.16-h2bbff1b_3.conda", "license": "GPL-3.0-or-later", "license_family": "GPL3", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libiconv-1.16-h2bbff1b_3", "type": 1}, "md5": "1564a57e99efb880d46002dc825d07c1", "name": "libiconv", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libiconv-1.16-h2bbff1b_3.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::libiconv==1.16=h2bbff1b_3[md5=1564a57e99efb880d46002dc825d07c1]", "sha256": "5007a6224c5b80e68d01bb04628ecc6a1e2a64a634857ec89ddb1e1f7b3329e4", "size": 701524, "subdir": "win-64", "timestamp": 1714484485000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libiconv-1.16-h2bbff1b_3.conda", "version": "1.16"}