<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityUnifiedMemoryCounterConfig Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityUnifiedMemoryCounterConfig Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityUnifiedMemoryCounterConfig" -->Unified Memory counters configuration structure.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#b7d481f9c5aa55ef2bbb9ece4f4d9df4">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#e7b6312b500a6b58a01c9d6ef85eacfc">enable</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#ace3fa8c2a5b7446a09f459fa8e689d3">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">CUpti_ActivityUnifiedMemoryCounterScope</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#98ab2d234d466672b548a08abe57827e">scope</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure controls the enable/disable of the various Unified Memory counters consisting of scope, kind and other parameters. See function <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8ec9b1229ba07a98aac9db4600d4325c">cuptiActivityConfigureUnifiedMemoryCounter</a> <hr><h2>Field Documentation</h2>
<a class="anchor" name="b7d481f9c5aa55ef2bbb9ece4f4d9df4"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounterConfig::deviceId" ref="b7d481f9c5aa55ef2bbb9ece4f4d9df4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#b7d481f9c5aa55ef2bbb9ece4f4d9df4">CUpti_ActivityUnifiedMemoryCounterConfig::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device id of the traget device. This is relevant only for single device scopes. (deprecated in CUDA 7.0) 
</div>
</div><p>
<a class="anchor" name="e7b6312b500a6b58a01c9d6ef85eacfc"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounterConfig::enable" ref="e7b6312b500a6b58a01c9d6ef85eacfc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#e7b6312b500a6b58a01c9d6ef85eacfc">CUpti_ActivityUnifiedMemoryCounterConfig::enable</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Control to enable/disable the counter. To enable the counter set it to non-zero value while disable is indicated by zero. 
</div>
</div><p>
<a class="anchor" name="ace3fa8c2a5b7446a09f459fa8e689d3"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounterConfig::kind" ref="ace3fa8c2a5b7446a09f459fa8e689d3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a> <a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#ace3fa8c2a5b7446a09f459fa8e689d3">CUpti_ActivityUnifiedMemoryCounterConfig::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unified Memory counter Counter kind 
</div>
</div><p>
<a class="anchor" name="98ab2d234d466672b548a08abe57827e"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounterConfig::scope" ref="98ab2d234d466672b548a08abe57827e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">CUpti_ActivityUnifiedMemoryCounterScope</a> <a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#98ab2d234d466672b548a08abe57827e">CUpti_ActivityUnifiedMemoryCounterConfig::scope</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unified Memory counter Counter scope. (deprecated in CUDA 7.0) 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
