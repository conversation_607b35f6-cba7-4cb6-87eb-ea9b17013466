<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityUnifiedMemoryCounter Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityUnifiedMemoryCounter Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityUnifiedMemoryCounter" -->The activity record for Unified Memory counters (deprecated in CUDA 7.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#f63374de2f160ffce57e0e49ae08b3b2">counterKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#25097a2724606127a446e9a6150f9609">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#fc84de22002af5fd1b8afd99592fb9b4">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#346aff93788fecb31aad5d197c23e1ee">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#5380e7b6529aaaef5314dbe0113c3c28">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">CUpti_ActivityUnifiedMemoryCounterScope</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#a007aa3f6a9becfca4fe5d4b02319cd1">scope</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#a2d7ab77c0793a3f46e1790d0a5ab78e">timestamp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#fe0e3db9b555e7bd7a9654085644aa24">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a Unified Memory counter (CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER). <hr><h2>Field Documentation</h2>
<a class="anchor" name="f63374de2f160ffce57e0e49ae08b3b2"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::counterKind" ref="f63374de2f160ffce57e0e49ae08b3b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a> <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#f63374de2f160ffce57e0e49ae08b3b2">CUpti_ActivityUnifiedMemoryCounter::counterKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Unified Memory counter kind. See <a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a> 
</div>
</div><p>
<a class="anchor" name="25097a2724606127a446e9a6150f9609"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::deviceId" ref="25097a2724606127a446e9a6150f9609" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#25097a2724606127a446e9a6150f9609">CUpti_ActivityUnifiedMemoryCounter::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device involved in the memory transfer operation. It is not relevant if the scope of the counter is global (all devices). 
</div>
</div><p>
<a class="anchor" name="fc84de22002af5fd1b8afd99592fb9b4"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::kind" ref="fc84de22002af5fd1b8afd99592fb9b4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#fc84de22002af5fd1b8afd99592fb9b4">CUpti_ActivityUnifiedMemoryCounter::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER 
</div>
</div><p>
<a class="anchor" name="346aff93788fecb31aad5d197c23e1ee"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::pad" ref="346aff93788fecb31aad5d197c23e1ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#346aff93788fecb31aad5d197c23e1ee">CUpti_ActivityUnifiedMemoryCounter::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="5380e7b6529aaaef5314dbe0113c3c28"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::processId" ref="5380e7b6529aaaef5314dbe0113c3c28" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#5380e7b6529aaaef5314dbe0113c3c28">CUpti_ActivityUnifiedMemoryCounter::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process to which this record belongs to. In case of global scope, processId is undefined. 
</div>
</div><p>
<a class="anchor" name="a007aa3f6a9becfca4fe5d4b02319cd1"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::scope" ref="a007aa3f6a9becfca4fe5d4b02319cd1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">CUpti_ActivityUnifiedMemoryCounterScope</a> <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#a007aa3f6a9becfca4fe5d4b02319cd1">CUpti_ActivityUnifiedMemoryCounter::scope</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Scope of the Unified Memory counter. See <a class="el" href="group__CUPTI__ACTIVITY__API.html#gcf829db187f1553461cea1a6c9e6748b">CUpti_ActivityUnifiedMemoryCounterScope</a> 
</div>
</div><p>
<a class="anchor" name="a2d7ab77c0793a3f46e1790d0a5ab78e"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::timestamp" ref="a2d7ab77c0793a3f46e1790d0a5ab78e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#a2d7ab77c0793a3f46e1790d0a5ab78e">CUpti_ActivityUnifiedMemoryCounter::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when this sample was retrieved, in ns. A value of 0 indicates that timestamp information could not be collected 
</div>
</div><p>
<a class="anchor" name="fe0e3db9b555e7bd7a9654085644aa24"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter::value" ref="fe0e3db9b555e7bd7a9654085644aa24" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#fe0e3db9b555e7bd7a9654085644aa24">CUpti_ActivityUnifiedMemoryCounter::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Value of the counter 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
