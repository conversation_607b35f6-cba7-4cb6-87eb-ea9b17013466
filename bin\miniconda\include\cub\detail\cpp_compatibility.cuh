/*
*  Copyright 2022 NVIDIA Corporation
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/


#pragma once

#include <cub/util_cpp_dialect.cuh>

#if CUB_CPP_DIALECT >= 2017 && __cpp_if_constexpr
#  define CUB_IF_CONSTEXPR if constexpr
#  define CUB_ELSE_IF_CONSTEXPR else if constexpr
#else
#  define CUB_IF_CONSTEXPR if
#  define CUB_ELSE_IF_CONSTEXPR else if
#endif
