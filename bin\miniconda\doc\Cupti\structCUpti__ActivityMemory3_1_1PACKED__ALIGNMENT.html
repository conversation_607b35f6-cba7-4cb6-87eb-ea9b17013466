<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="navpath"><a class="el" href="structCUpti__ActivityMemory3.html">CUpti_ActivityMemory3</a>::<a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html">PACKED_ALIGNMENT</a>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT Struct Reference</h1><!-- doxytag: class="CUpti_ActivityMemory3::PACKED_ALIGNMENT" --><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#c9d2edeeaa902737723687409b8063c7">address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#f9f0d1c966e279e36115e018e005063c">memoryPoolType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#a440564d6df427c241c6f93d2246187c">releaseThreshold</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#3aa017c01273b814bf1be1696b743ce0">utilizedSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#513cf3ab1a411930759ba371c7416d6a">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#fcd6da7fa06dd3837b4f56f5c895aa5b">size</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
The memory pool configuration used for the memory operations. <hr><h2>Field Documentation</h2>
<a class="anchor" name="c9d2edeeaa902737723687409b8063c7"></a><!-- doxytag: member="CUpti_ActivityMemory3::PACKED_ALIGNMENT::address" ref="c9d2edeeaa902737723687409b8063c7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT::address          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The base address of the memory pool. 
</div>
</div><p>
<a class="anchor" name="f9f0d1c966e279e36115e018e005063c"></a><!-- doxytag: member="CUpti_ActivityMemory3::PACKED_ALIGNMENT::memoryPoolType" ref="f9f0d1c966e279e36115e018e005063c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT::memoryPoolType          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the memory pool, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> 
</div>
</div><p>
<a class="anchor" name="513cf3ab1a411930759ba371c7416d6a"></a><!-- doxytag: member="CUpti_ActivityMemory3::PACKED_ALIGNMENT::processId" ref="513cf3ab1a411930759ba371c7416d6a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT::processId          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The processId of the memory pool. <code>processId</code> is valid if <code>memoryPoolType</code> is CUPTI_ACTIVITY_MEMORY_POOL_TYPE_IMPORTED, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="a440564d6df427c241c6f93d2246187c"></a><!-- doxytag: member="CUpti_ActivityMemory3::PACKED_ALIGNMENT::releaseThreshold" ref="a440564d6df427c241c6f93d2246187c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT::releaseThreshold          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The release threshold of the memory pool in bytes. <code>releaseThreshold</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="fcd6da7fa06dd3837b4f56f5c895aa5b"></a><!-- doxytag: member="CUpti_ActivityMemory3::PACKED_ALIGNMENT::size" ref="fcd6da7fa06dd3837b4f56f5c895aa5b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT::size          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the memory pool in bytes. <code>size</code> is valid if <code>memoryPoolType</code> is CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="3aa017c01273b814bf1be1696b743ce0"></a><!-- doxytag: member="CUpti_ActivityMemory3::PACKED_ALIGNMENT::utilizedSize" ref="3aa017c01273b814bf1be1696b743ce0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT::utilizedSize          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The utilized size of the memory pool. <code>utilizedSize</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
