{"arch": "x86_64", "build": "h214f63a_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cpp-expected-1.1.0-h214f63a_0", "files": ["Library/include/tl/expected.hpp", "Library/share/cmake/tl-expected/tl-expected-config-version.cmake", "Library/share/cmake/tl-expected/tl-expected-config.cmake", "Library/share/cmake/tl-expected/tl-expected-targets.cmake"], "fn": "cpp-expected-1.1.0-h214f63a_0.conda", "license": "CC0-1.0", "license_family": "CC", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cpp-expected-1.1.0-h214f63a_0", "type": 1}, "md5": "67a55401db50a1d6bde2b153a74bcb07", "name": "cpp-expected", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cpp-expected-1.1.0-h214f63a_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::cpp-expected==1.1.0=h214f63a_0[md5=67a55401db50a1d6bde2b153a74bcb07]", "sha256": "73e2ef3800ff5659c111f073e8e2f802d48483c78ea8781cdf5ed34fae6de2a2", "size": 133944, "subdir": "win-64", "timestamp": 1734039396000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cpp-expected-1.1.0-h214f63a_0.conda", "version": "1.1.0"}