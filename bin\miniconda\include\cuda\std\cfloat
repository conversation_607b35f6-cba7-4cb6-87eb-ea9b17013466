//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_CFLOAT
#define _CUDA_CFLOAT

#ifndef __CUDACC_RTC__
    #include <cfloat>
    #include <float.h>
#endif //__CUDACC_RTC__

#include "detail/__config"

#include "detail/__pragma_push"

#include "detail/libcxx/include/cfloat"

#include "detail/__pragma_pop"

#endif //_CUDA_CFLOAT
