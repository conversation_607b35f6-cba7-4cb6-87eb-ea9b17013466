{"build": "h2466b09_6", "build_number": 6, "channel": "https://conda.anaconda.org/conda-forge", "constrains": ["zlib-wapi 1.2.13 *_6", "zlib 1.2.13 *_6"], "depends": ["ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libzlib-wapi-1.2.13-h2466b09_6", "features": "", "files": ["Library/bin/zlibwapi.dll"], "fn": "libzlib-wapi-1.2.13-h2466b09_6.conda", "license": "<PERSON><PERSON><PERSON>", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libzlib-wapi-1.2.13-h2466b09_6", "type": 1}, "md5": "84ac5ada002445227139f8f659cf6d93", "name": "libzlib-wapi", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libzlib-wapi-1.2.13-h2466b09_6.conda", "paths_data": {"paths": [{"_path": "Library/bin/zlibwapi.dll", "path_type": "hardlink", "sha256": "cc08a78504d66ec9b932769c0ab747db6de91f59e14bb8dbf73f92462649a5b3", "sha256_in_prefix": "cc08a78504d66ec9b932769c0ab747db6de91f59e14bb8dbf73f92462649a5b3", "size_in_bytes": 89088}], "paths_version": 1}, "requested_spec": "libzlib-wapi", "sha256": "dd92ecd1f39e17623fd6149cf0dc7f675d2f9e091ef2b1774a85e9e545434102", "size": 56048, "subdir": "win-64", "timestamp": 1716874638000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/libzlib-wapi-1.2.13-h2466b09_6.conda", "version": "1.2.13"}