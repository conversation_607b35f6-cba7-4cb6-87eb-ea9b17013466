{"arch": "x86_64", "build": "py310h5da7b33_9", "build_number": 9, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\brotli-python-1.0.9-py310h5da7b33_9", "files": ["Lib/site-packages/Brotli-1.0.9.dist-info/INSTALLER", "Lib/site-packages/Brotli-1.0.9.dist-info/LICENSE", "Lib/site-packages/Brotli-1.0.9.dist-info/METADATA", "Lib/site-packages/Brotli-1.0.9.dist-info/RECORD", "Lib/site-packages/Brotli-1.0.9.dist-info/REQUESTED", "Lib/site-packages/Brotli-1.0.9.dist-info/WHEEL", "Lib/site-packages/Brotli-1.0.9.dist-info/direct_url.json", "Lib/site-packages/Brotli-1.0.9.dist-info/top_level.txt", "Lib/site-packages/__pycache__/brotli.cpython-310.pyc", "Lib/site-packages/__pycache__/brotli.cpython-313.pyc", "Lib/site-packages/_brotli.cp310-win_amd64.pyd", "Lib/site-packages/brotli.py"], "fn": "brotli-python-1.0.9-py310h5da7b33_9.conda", "license": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\brotli-python-1.0.9-py310h5da7b33_9", "type": 1}, "md5": "cc19737be15fbece822e5efc8f0778b5", "name": "brotli-python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\brotli-python-1.0.9-py310h5da7b33_9.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::brotli-python==1.0.9=py310h5da7b33_9[md5=cc19737be15fbece822e5efc8f0778b5]", "sha256": "5ddb979f755e9778e3791f9e6aa3e64ae0e9d845284e2cb123b6fa11367117d5", "size": 354269, "subdir": "win-64", "timestamp": 1736183829000, "url": "https://repo.anaconda.com/pkgs/main/win-64/brotli-python-1.0.9-py310h5da7b33_9.conda", "version": "1.0.9"}