_CONDA_SCRIPT=C:\pinokio\api\facefusion-pinokio.git\.env\etc\conda\deactivate.d\openssl_deactivate-win.bat
_CONDA_SCRIPT=C:\pinokio\api\facefusion-pinokio.git\.env\etc\conda\deactivate.d\libxml2_deactivate.bat
_CONDA_SCRIPT=C:\pinokio\api\facefusion-pinokio.git\.env\etc\conda\deactivate.d\khronos-opencl-icd-loader_deactivate.bat
_CE_M=
_CE_CONDA=
PROMPT=(C:\pinokio\api\facefusion-pinokio.git\.env) $P$G
PATH=C:\pinokio\api\facefusion-pinokio.git\.env;C:\pinokio\api\facefusion-pinokio.git\.env\Library\mingw-w64\bin;C:\pinokio\api\facefusion-pinokio.git\.env\Library\usr\bin;C:\pinokio\api\facefusion-pinokio.git\.env\Library\bin;C:\pinokio\api\facefusion-pinokio.git\.env\Scripts;C:\pinokio\api\facefusion-pinokio.git\.env\bin;C:\Users\<USER>\miniconda3\condabin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\libnvvp;C:\Program Files\Microsoft MPI\Bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\ExifTool;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.1.1;C:\Users\<USER>\miniconda3;C:\Users\<USER>\AppData\Local\NVIDIA\ChatRTX\env_nvd_rag\Lib\site-packages\torch\lib;C:\Program Files\Git\cmd;C:\Program Files\Inkscape\bin;C:\Program Files\FFmpeg\ffmpeg-n7.0-latest-win64-gpl-7.0\bin;.;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\pinokio\api\facefusion-pinokio.git\.env\Lib;C:\pinokio\api\facefusion-pinokio.git\.env\Lib\site-packages\tensorrt_libs
CONDA_SHLVL=1
CONDA_PROMPT_MODIFIER=(C:\pinokio\api\facefusion-pinokio.git\.env) 
CONDA_EXE=C:\pinokio\bin\miniconda\Scripts\conda.exe
CONDA_PYTHON_EXE=C:\pinokio\bin\miniconda\python.exe
_CONDA_SCRIPT=C:\pinokio\api\facefusion-pinokio.git\.env\etc\conda\activate.d\khronos-opencl-icd-loader_activate.bat
_CONDA_SCRIPT=C:\pinokio\api\facefusion-pinokio.git\.env\etc\conda\activate.d\libxml2_activate.bat
_CONDA_SCRIPT=C:\pinokio\api\facefusion-pinokio.git\.env\etc\conda\activate.d\openssl_activate-win.bat
