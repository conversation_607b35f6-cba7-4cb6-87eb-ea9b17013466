{"build": "hffc9a7f_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": ["libcudnn-jit <0a"], "depends": ["cuda-nvrtc", "cuda-version >=12,<13.0a0", "libcublas", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcudnn-9.10.1.4-hffc9a7f_0", "features": "", "files": ["Library/bin/cudnn64_9.dll", "Library/bin/cudnn_adv64_9.dll", "Library/bin/cudnn_cnn64_9.dll", "Library/bin/cudnn_engines_precompiled64_9.dll", "Library/bin/cudnn_engines_runtime_compiled64_9.dll", "Library/bin/cudnn_graph64_9.dll", "Library/bin/cudnn_heuristic64_9.dll", "Library/bin/cudnn_ops64_9.dll"], "fn": "libcudnn-9.10.1.4-hffc9a7f_0.conda", "license": "LicenseRef-cuDNN-Software-License-Agreement", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcudnn-9.10.1.4-hffc9a7f_0", "type": 1}, "md5": "5f3f28dcfb3f8c8cc6f826079923377f", "name": "libcudnn", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcudnn-9.10.1.4-hffc9a7f_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/cudnn64_9.dll", "path_type": "hardlink", "sha256": "1dc78e65ef8e202fb39607706f097e57134c826eb49d4ed12b3f9d00e8815e0a", "sha256_in_prefix": "1dc78e65ef8e202fb39607706f097e57134c826eb49d4ed12b3f9d00e8815e0a", "size_in_bytes": 265760}, {"_path": "Library/bin/cudnn_adv64_9.dll", "path_type": "hardlink", "sha256": "1d6160c468c26ca9c802b29630981a0771972e26c21cea63ea738e28720acb70", "sha256_in_prefix": "1d6160c468c26ca9c802b29630981a0771972e26c21cea63ea738e28720acb70", "size_in_bytes": 282445344}, {"_path": "Library/bin/cudnn_cnn64_9.dll", "path_type": "hardlink", "sha256": "d7fb62bf49568062ebb4860e3b8df7f8ff8f65647ba742a954829283065ffbcd", "sha256_in_prefix": "d7fb62bf49568062ebb4860e3b8df7f8ff8f65647ba742a954829283065ffbcd", "size_in_bytes": 4618272}, {"_path": "Library/bin/cudnn_engines_precompiled64_9.dll", "path_type": "hardlink", "sha256": "6e98e31b52a07798dd2ace99e17307ada83f16bf834d01c0b7bedc3226940284", "sha256_in_prefix": "6e98e31b52a07798dd2ace99e17307ada83f16bf834d01c0b7bedc3226940284", "size_in_bytes": 513927224}, {"_path": "Library/bin/cudnn_engines_runtime_compiled64_9.dll", "path_type": "hardlink", "sha256": "a1dffefb6329cbc0d380e891bd90afeb5e75e9abec56ca27bc69166aa474eb18", "sha256_in_prefix": "a1dffefb6329cbc0d380e891bd90afeb5e75e9abec56ca27bc69166aa474eb18", "size_in_bytes": 20200504}, {"_path": "Library/bin/cudnn_graph64_9.dll", "path_type": "hardlink", "sha256": "770626777592436d3f4655f7d0989d681b28bb03684bf05790c6583330b68aad", "sha256_in_prefix": "770626777592436d3f4655f7d0989d681b28bb03684bf05790c6583330b68aad", "size_in_bytes": 2419248}, {"_path": "Library/bin/cudnn_heuristic64_9.dll", "path_type": "hardlink", "sha256": "310168b0e92b0cd137fbe45a968e2de97e29ade742d03b776f990d971009a6e6", "sha256_in_prefix": "310168b0e92b0cd137fbe45a968e2de97e29ade742d03b776f990d971009a6e6", "size_in_bytes": 56823856}, {"_path": "Library/bin/cudnn_ops64_9.dll", "path_type": "hardlink", "sha256": "05df0f8a70ecc11b0f796c749ae8959c24d086810d689cff89d8416511a36b8a", "sha256_in_prefix": "05df0f8a70ecc11b0f796c749ae8959c24d086810d689cff89d8416511a36b8a", "size_in_bytes": 126509112}], "paths_version": 1}, "requested_spec": "None", "sha256": "26d399ecad571797b2600b0c6c955a6ce2f3372d71cca0c148c5867a4a52a377", "size": 509841043, "subdir": "win-64", "timestamp": 1747774328000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/libcudnn-9.10.1.4-hffc9a7f_0.conda", "version": "9.10.1.4"}