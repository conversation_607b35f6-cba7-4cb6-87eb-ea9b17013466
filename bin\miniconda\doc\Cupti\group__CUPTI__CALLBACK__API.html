<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Callback API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Callback API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html">CUpti_CallbackData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a runtime or driver API callback function.  <a href="structCUpti__CallbackData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__GraphData.html">CUpti_GraphData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">CUDA graphs data passed into a resource callback function.  <a href="structCUpti__GraphData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ModuleResourceData.html">CUpti_ModuleResourceData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Module data passed into a resource callback function.  <a href="structCUpti__ModuleResourceData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__NvtxData.html">CUpti_NvtxData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a NVTX callback function.  <a href="structCUpti__NvtxData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ResourceData.html">CUpti_ResourceData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a resource callback function.  <a href="structCUpti__ResourceData.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__SynchronizeData.html">CUpti_SynchronizeData</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Data passed into a synchronize callback function.  <a href="structCUpti__SynchronizeData.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef void(*&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a> )(void *userdata, <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> domain, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a> cbid, const void *cbdata)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for a callback.  <a href="#g84b7295694fda2bbfda931682a07bf4f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">An ID for a driver API, runtime API, resource or synchronization callback.  <a href="#g7fde6b76bdbcafbcf750b0f91a3484f8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g76b7ef0d7caebdcae0880e218185950b"></a><!-- doxytag: member="CUPTI_CALLBACK_API::CUpti_DomainTable" ref="g76b7ef0d7caebdcae0880e218185950b" args="" -->
typedef <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g76b7ef0d7caebdcae0880e218185950b">CUpti_DomainTable</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Pointer to an array of callback domains. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g7932f010e8b2c785d85f4048235afca3"></a><!-- doxytag: member="CUPTI_CALLBACK_API::CUpti_SubscriberHandle" ref="g7932f010e8b2c785d85f4048235afca3" args="" -->
typedef struct <br class="typebreak">
CUpti_Subscriber_st *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A callback subscriber. <br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7bd557c9b3084014c680b9925842be24">CUpti_ApiCallbackSite</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg7bd557c9b3084014c680b9925842be246b1e29cc790239e0c0f393791cd4ca0a">CUPTI_API_ENTER</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg7bd557c9b3084014c680b9925842be241df739da617612b774bb0a8895c15fab">CUPTI_API_EXIT</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Specifies the point in an API call that a callback is issued.  <a href="group__CUPTI__CALLBACK__API.html#g7bd557c9b3084014c680b9925842be24">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9e254f9b2ca3ce46af1c3801c91512dc64">CUPTI_CB_DOMAIN_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9e77c508fe29e5781fff2900676cd63213">CUPTI_CB_DOMAIN_DRIVER_API</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9e06a7d035b9d878cc4b43c7dc93b3c632">CUPTI_CB_DOMAIN_RUNTIME_API</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9eef8b9adbf66219e14fd5efe4d95f2dd8">CUPTI_CB_DOMAIN_RESOURCE</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9e8a7cb1b85712a67d11042cecef41ab6a">CUPTI_CB_DOMAIN_SYNCHRONIZE</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg9e77154dbe0cc07fb9332f95a83d6d9e1c32d00925a82f91321d8f4255109b4c">CUPTI_CB_DOMAIN_NVTX</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback domains.  <a href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g690fb2a42aefe39f00033c957ce211b2">CUpti_CallbackIdResource</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b297eef59d0abb5fb3bc4615ef5dedc79d">CUPTI_CBID_RESOURCE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b22e6ddca485e2a49e44965eea62c4dedd">CUPTI_CBID_RESOURCE_CONTEXT_CREATED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2792c08361eacf82ee0168b20a1056ba9">CUPTI_CBID_RESOURCE_CONTEXT_DESTROY_STARTING</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2c2b9f0465e39f28e5262f8dcc21a32f2">CUPTI_CBID_RESOURCE_STREAM_CREATED</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b28b7dbb4502ec13c1c1e7b75fc2fd1bc3">CUPTI_CBID_RESOURCE_STREAM_DESTROY_STARTING</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2d8e1e55b0322e194035ccc10b9ec69eb">CUPTI_CBID_RESOURCE_CU_INIT_FINISHED</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2581d8e8224e9634c10be2d2e8a58735b">CUPTI_CBID_RESOURCE_MODULE_LOADED</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2794a819f149bf776844f1222f93b2b57">CUPTI_CBID_RESOURCE_MODULE_UNLOAD_STARTING</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b224176e4521d615dd5e80a9e13165a79c">CUPTI_CBID_RESOURCE_MODULE_PROFILED</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2858bcdb19761d2de275b5d9e424fbb75">CUPTI_CBID_RESOURCE_GRAPH_CREATED</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2c45ccdeadae3836c9ee7d5802b621e73">CUPTI_CBID_RESOURCE_GRAPH_DESTROY_STARTING</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b25aac97ce2930ef232473e6cce230ff7e">CUPTI_CBID_RESOURCE_GRAPH_CLONED</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b27d5a7fb50e67fc63b588aeedb44035fb">CUPTI_CBID_RESOURCE_GRAPHNODE_CREATE_STARTING</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b24f6c21c14aa32e3b3526aaa0a35cb3d4">CUPTI_CBID_RESOURCE_GRAPHNODE_CREATED</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2edd6a0bb5b2f70d7262e8f973e2847dc">CUPTI_CBID_RESOURCE_GRAPHNODE_DESTROY_STARTING</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b22a856a1825b32d89ed7ee8dc863e054b">CUPTI_CBID_RESOURCE_GRAPHNODE_DEPENDENCY_CREATED</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b227a3d4247a2e437b4170ed38e6471262">CUPTI_CBID_RESOURCE_GRAPHNODE_DEPENDENCY_DESTROY_STARTING</a> =  16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2da4b8510bc6420193e0d6dd6fafc98f1">CUPTI_CBID_RESOURCE_GRAPHEXEC_CREATE_STARTING</a> =  17, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2471a1a00b37b3cb56b796a095c0fb93e">CUPTI_CBID_RESOURCE_GRAPHEXEC_CREATED</a> =  18, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b24e73fcf1883f507f2451c96ae2dd9c76">CUPTI_CBID_RESOURCE_GRAPHEXEC_DESTROY_STARTING</a> =  19, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#gg690fb2a42aefe39f00033c957ce211b2888c713cc24de13847a9bcbb2354870e">CUPTI_CBID_RESOURCE_GRAPHNODE_CLONED</a> =  20
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for resource domain.  <a href="group__CUPTI__CALLBACK__API.html#g690fb2a42aefe39f00033c957ce211b2">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#gcaeba9950bf4f48ea7ab9a0402dc7e6f">CUpti_CallbackIdSync</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#ggcaeba9950bf4f48ea7ab9a0402dc7e6f9f6517fe91c4340e584d65eebd85a17e">CUPTI_CBID_SYNCHRONIZE_INVALID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#ggcaeba9950bf4f48ea7ab9a0402dc7e6f90ee543086b48a83c4b08df845d75e01">CUPTI_CBID_SYNCHRONIZE_STREAM_SYNCHRONIZED</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__CALLBACK__API.html#ggcaeba9950bf4f48ea7ab9a0402dc7e6f4be043a702da1d8f89d0095a694d59db">CUPTI_CBID_SYNCHRONIZE_CONTEXT_SYNCHRONIZED</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Callback IDs for synchronization domain.  <a href="group__CUPTI__CALLBACK__API.html#gcaeba9950bf4f48ea7ab9a0402dc7e6f">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7dcebeb8ae4f79c90905a8f6befc51d7">cuptiEnableAllDomains</a> (uint32_t enable, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a> subscriber)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable or disable all callbacks in all domains.  <a href="#g7dcebeb8ae4f79c90905a8f6befc51d7"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#gce619a64b77d6533754de798b5e8263e">cuptiEnableCallback</a> (uint32_t enable, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a> subscriber, <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> domain, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a> cbid)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable or disabled callbacks for a specific domain and callback ID.  <a href="#gce619a64b77d6533754de798b5e8263e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g926699208431270d4197fcb639da6a5c">cuptiEnableDomain</a> (uint32_t enable, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a> subscriber, <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> domain)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable or disabled all callbacks for a specific domain.  <a href="#g926699208431270d4197fcb639da6a5c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g0fe2357995aa7861a37e5896c6a18635">cuptiGetCallbackName</a> (<a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> domain, uint32_t cbid, const char **name)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the name of a callback for a specific domain and callback ID.  <a href="#g0fe2357995aa7861a37e5896c6a18635"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#gf861e55f8d61286b97471e2bede138a6">cuptiGetCallbackState</a> (uint32_t *enable, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a> subscriber, <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> domain, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a> cbid)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the current enabled/disabled state of a callback for a specific domain and function ID.  <a href="#gf861e55f8d61286b97471e2bede138a6"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#gd2c32850b2e03b37e284df083dc9053f">cuptiSubscribe</a> (<a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a> *subscriber, <a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a> callback, void *userdata)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize a callback subscriber with a callback function and user data.  <a href="#gd2c32850b2e03b37e284df083dc9053f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g4526fa1776292fa325971e815e0c7dc2">cuptiSupportedDomains</a> (size_t *domainCount, <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_DomainTable</a> *domainTable)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the available callback domains.  <a href="#g4526fa1776292fa325971e815e0c7dc2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__CALLBACK__API.html#g20b68c9c33f129179b56687a17356682">cuptiUnsubscribe</a> (<a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a> subscriber)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Unregister a callback subscriber.  <a href="#g20b68c9c33f129179b56687a17356682"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI Callback API. <hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g84b7295694fda2bbfda931682a07bf4f"></a><!-- doxytag: member="cupti_callbacks.h::CUpti_CallbackFunc" ref="g84b7295694fda2bbfda931682a07bf4f" args=")(void *userdata, CUpti_CallbackDomain domain, CUpti_CallbackId cbid, const void *cbdata)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void( * <a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a>)(void *userdata, <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a> domain, <a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a> cbid, const void *cbdata)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Function type for a callback. The type of the data passed to the callback in <code>cbdata</code> depends on the <code>domain</code>. If <code>domain</code> is CUPTI_CB_DOMAIN_DRIVER_API or CUPTI_CB_DOMAIN_RUNTIME_API the type of <code>cbdata</code> will be <a class="el" href="structCUpti__CallbackData.html" title="Data passed into a runtime or driver API callback function.">CUpti_CallbackData</a>. If <code>domain</code> is CUPTI_CB_DOMAIN_RESOURCE the type of <code>cbdata</code> will be <a class="el" href="structCUpti__ResourceData.html" title="Data passed into a resource callback function.">CUpti_ResourceData</a>. If <code>domain</code> is CUPTI_CB_DOMAIN_SYNCHRONIZE the type of <code>cbdata</code> will be <a class="el" href="structCUpti__SynchronizeData.html" title="Data passed into a synchronize callback function.">CUpti_SynchronizeData</a>. If <code>domain</code> is CUPTI_CB_DOMAIN_NVTX the type of <code>cbdata</code> will be <a class="el" href="structCUpti__NvtxData.html" title="Data passed into a NVTX callback function.">CUpti_NvtxData</a>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>userdata</em>&nbsp;</td><td>User data supplied at subscription of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cbid</em>&nbsp;</td><td>The ID of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cbdata</em>&nbsp;</td><td>Data passed to the callback. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g7fde6b76bdbcafbcf750b0f91a3484f8"></a><!-- doxytag: member="cupti_callbacks.h::CUpti_CallbackId" ref="g7fde6b76bdbcafbcf750b0f91a3484f8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint32_t <a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An ID for a driver API, runtime API, resource or synchronization callback. Within a driver API callback this should be interpreted as a CUpti_driver_api_trace_cbid value (these values are defined in cupti_driver_cbid.h). Within a runtime API callback this should be interpreted as a CUpti_runtime_api_trace_cbid value (these values are defined in cupti_runtime_cbid.h). Within a resource API callback this should be interpreted as a <a class="el" href="group__CUPTI__CALLBACK__API.html#g690fb2a42aefe39f00033c957ce211b2">CUpti_CallbackIdResource</a> value. Within a synchronize API callback this should be interpreted as a <a class="el" href="group__CUPTI__CALLBACK__API.html#gcaeba9950bf4f48ea7ab9a0402dc7e6f">CUpti_CallbackIdSync</a> value. 
</div>
</div><p>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g7bd557c9b3084014c680b9925842be24"></a><!-- doxytag: member="cupti_callbacks.h::CUpti_ApiCallbackSite" ref="g7bd557c9b3084014c680b9925842be24" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__CALLBACK__API.html#g7bd557c9b3084014c680b9925842be24">CUpti_ApiCallbackSite</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Specifies the point in an API call that a callback is issued. This value is communicated to the callback function via <a class="el" href="structCUpti__CallbackData.html#01337ce329bea0e08d803cb99c1f1f01">CUpti_CallbackData::callbackSite</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg7bd557c9b3084014c680b9925842be246b1e29cc790239e0c0f393791cd4ca0a"></a><!-- doxytag: member="CUPTI_API_ENTER" ref="gg7bd557c9b3084014c680b9925842be246b1e29cc790239e0c0f393791cd4ca0a" args="" -->CUPTI_API_ENTER</em>&nbsp;</td><td>
The callback is at the entry of the API call. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg7bd557c9b3084014c680b9925842be241df739da617612b774bb0a8895c15fab"></a><!-- doxytag: member="CUPTI_API_EXIT" ref="gg7bd557c9b3084014c680b9925842be241df739da617612b774bb0a8895c15fab" args="" -->CUPTI_API_EXIT</em>&nbsp;</td><td>
The callback is at the exit of the API call. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g9e77154dbe0cc07fb9332f95a83d6d9e"></a><!-- doxytag: member="cupti_callbacks.h::CUpti_CallbackDomain" ref="g9e77154dbe0cc07fb9332f95a83d6d9e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback domains. Each domain represents callback points for a group of related API functions or CUDA driver activity. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg9e77154dbe0cc07fb9332f95a83d6d9e254f9b2ca3ce46af1c3801c91512dc64"></a><!-- doxytag: member="CUPTI_CB_DOMAIN_INVALID" ref="gg9e77154dbe0cc07fb9332f95a83d6d9e254f9b2ca3ce46af1c3801c91512dc64" args="" -->CUPTI_CB_DOMAIN_INVALID</em>&nbsp;</td><td>
Invalid domain. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9e77154dbe0cc07fb9332f95a83d6d9e77c508fe29e5781fff2900676cd63213"></a><!-- doxytag: member="CUPTI_CB_DOMAIN_DRIVER_API" ref="gg9e77154dbe0cc07fb9332f95a83d6d9e77c508fe29e5781fff2900676cd63213" args="" -->CUPTI_CB_DOMAIN_DRIVER_API</em>&nbsp;</td><td>
Domain containing callback points for all driver API functions. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9e77154dbe0cc07fb9332f95a83d6d9e06a7d035b9d878cc4b43c7dc93b3c632"></a><!-- doxytag: member="CUPTI_CB_DOMAIN_RUNTIME_API" ref="gg9e77154dbe0cc07fb9332f95a83d6d9e06a7d035b9d878cc4b43c7dc93b3c632" args="" -->CUPTI_CB_DOMAIN_RUNTIME_API</em>&nbsp;</td><td>
Domain containing callback points for all runtime API functions. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9e77154dbe0cc07fb9332f95a83d6d9eef8b9adbf66219e14fd5efe4d95f2dd8"></a><!-- doxytag: member="CUPTI_CB_DOMAIN_RESOURCE" ref="gg9e77154dbe0cc07fb9332f95a83d6d9eef8b9adbf66219e14fd5efe4d95f2dd8" args="" -->CUPTI_CB_DOMAIN_RESOURCE</em>&nbsp;</td><td>
Domain containing callback points for CUDA resource tracking. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9e77154dbe0cc07fb9332f95a83d6d9e8a7cb1b85712a67d11042cecef41ab6a"></a><!-- doxytag: member="CUPTI_CB_DOMAIN_SYNCHRONIZE" ref="gg9e77154dbe0cc07fb9332f95a83d6d9e8a7cb1b85712a67d11042cecef41ab6a" args="" -->CUPTI_CB_DOMAIN_SYNCHRONIZE</em>&nbsp;</td><td>
Domain containing callback points for CUDA synchronization. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg9e77154dbe0cc07fb9332f95a83d6d9e1c32d00925a82f91321d8f4255109b4c"></a><!-- doxytag: member="CUPTI_CB_DOMAIN_NVTX" ref="gg9e77154dbe0cc07fb9332f95a83d6d9e1c32d00925a82f91321d8f4255109b4c" args="" -->CUPTI_CB_DOMAIN_NVTX</em>&nbsp;</td><td>
Domain containing callback points for NVTX API functions. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g690fb2a42aefe39f00033c957ce211b2"></a><!-- doxytag: member="cupti_callbacks.h::CUpti_CallbackIdResource" ref="g690fb2a42aefe39f00033c957ce211b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__CALLBACK__API.html#g690fb2a42aefe39f00033c957ce211b2">CUpti_CallbackIdResource</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for resource domain, CUPTI_CB_DOMAIN_RESOURCE. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b297eef59d0abb5fb3bc4615ef5dedc79d"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_INVALID" ref="gg690fb2a42aefe39f00033c957ce211b297eef59d0abb5fb3bc4615ef5dedc79d" args="" -->CUPTI_CBID_RESOURCE_INVALID</em>&nbsp;</td><td>
Invalid resource callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b22e6ddca485e2a49e44965eea62c4dedd"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_CONTEXT_CREATED" ref="gg690fb2a42aefe39f00033c957ce211b22e6ddca485e2a49e44965eea62c4dedd" args="" -->CUPTI_CBID_RESOURCE_CONTEXT_CREATED</em>&nbsp;</td><td>
A new context has been created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2792c08361eacf82ee0168b20a1056ba9"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_CONTEXT_DESTROY_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b2792c08361eacf82ee0168b20a1056ba9" args="" -->CUPTI_CBID_RESOURCE_CONTEXT_DESTROY_STARTING</em>&nbsp;</td><td>
A context is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2c2b9f0465e39f28e5262f8dcc21a32f2"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_STREAM_CREATED" ref="gg690fb2a42aefe39f00033c957ce211b2c2b9f0465e39f28e5262f8dcc21a32f2" args="" -->CUPTI_CBID_RESOURCE_STREAM_CREATED</em>&nbsp;</td><td>
A new stream has been created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b28b7dbb4502ec13c1c1e7b75fc2fd1bc3"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_STREAM_DESTROY_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b28b7dbb4502ec13c1c1e7b75fc2fd1bc3" args="" -->CUPTI_CBID_RESOURCE_STREAM_DESTROY_STARTING</em>&nbsp;</td><td>
A stream is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2d8e1e55b0322e194035ccc10b9ec69eb"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_CU_INIT_FINISHED" ref="gg690fb2a42aefe39f00033c957ce211b2d8e1e55b0322e194035ccc10b9ec69eb" args="" -->CUPTI_CBID_RESOURCE_CU_INIT_FINISHED</em>&nbsp;</td><td>
The driver has finished initializing. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2581d8e8224e9634c10be2d2e8a58735b"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_MODULE_LOADED" ref="gg690fb2a42aefe39f00033c957ce211b2581d8e8224e9634c10be2d2e8a58735b" args="" -->CUPTI_CBID_RESOURCE_MODULE_LOADED</em>&nbsp;</td><td>
A module has been loaded. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2794a819f149bf776844f1222f93b2b57"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_MODULE_UNLOAD_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b2794a819f149bf776844f1222f93b2b57" args="" -->CUPTI_CBID_RESOURCE_MODULE_UNLOAD_STARTING</em>&nbsp;</td><td>
A module is about to be unloaded. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b224176e4521d615dd5e80a9e13165a79c"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_MODULE_PROFILED" ref="gg690fb2a42aefe39f00033c957ce211b224176e4521d615dd5e80a9e13165a79c" args="" -->CUPTI_CBID_RESOURCE_MODULE_PROFILED</em>&nbsp;</td><td>
The current module which is being profiled. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2858bcdb19761d2de275b5d9e424fbb75"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPH_CREATED" ref="gg690fb2a42aefe39f00033c957ce211b2858bcdb19761d2de275b5d9e424fbb75" args="" -->CUPTI_CBID_RESOURCE_GRAPH_CREATED</em>&nbsp;</td><td>
CUDA graph has been created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2c45ccdeadae3836c9ee7d5802b621e73"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPH_DESTROY_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b2c45ccdeadae3836c9ee7d5802b621e73" args="" -->CUPTI_CBID_RESOURCE_GRAPH_DESTROY_STARTING</em>&nbsp;</td><td>
CUDA graph is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b25aac97ce2930ef232473e6cce230ff7e"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPH_CLONED" ref="gg690fb2a42aefe39f00033c957ce211b25aac97ce2930ef232473e6cce230ff7e" args="" -->CUPTI_CBID_RESOURCE_GRAPH_CLONED</em>&nbsp;</td><td>
CUDA graph is cloned. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b27d5a7fb50e67fc63b588aeedb44035fb"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHNODE_CREATE_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b27d5a7fb50e67fc63b588aeedb44035fb" args="" -->CUPTI_CBID_RESOURCE_GRAPHNODE_CREATE_STARTING</em>&nbsp;</td><td>
CUDA graph node is about to be created </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b24f6c21c14aa32e3b3526aaa0a35cb3d4"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHNODE_CREATED" ref="gg690fb2a42aefe39f00033c957ce211b24f6c21c14aa32e3b3526aaa0a35cb3d4" args="" -->CUPTI_CBID_RESOURCE_GRAPHNODE_CREATED</em>&nbsp;</td><td>
CUDA graph node is created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2edd6a0bb5b2f70d7262e8f973e2847dc"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHNODE_DESTROY_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b2edd6a0bb5b2f70d7262e8f973e2847dc" args="" -->CUPTI_CBID_RESOURCE_GRAPHNODE_DESTROY_STARTING</em>&nbsp;</td><td>
CUDA graph node is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b22a856a1825b32d89ed7ee8dc863e054b"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHNODE_DEPENDENCY_CREATED" ref="gg690fb2a42aefe39f00033c957ce211b22a856a1825b32d89ed7ee8dc863e054b" args="" -->CUPTI_CBID_RESOURCE_GRAPHNODE_DEPENDENCY_CREATED</em>&nbsp;</td><td>
Dependency on a CUDA graph node is created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b227a3d4247a2e437b4170ed38e6471262"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHNODE_DEPENDENCY_DESTROY_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b227a3d4247a2e437b4170ed38e6471262" args="" -->CUPTI_CBID_RESOURCE_GRAPHNODE_DEPENDENCY_DESTROY_STARTING</em>&nbsp;</td><td>
Dependency on a CUDA graph node is destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2da4b8510bc6420193e0d6dd6fafc98f1"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHEXEC_CREATE_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b2da4b8510bc6420193e0d6dd6fafc98f1" args="" -->CUPTI_CBID_RESOURCE_GRAPHEXEC_CREATE_STARTING</em>&nbsp;</td><td>
An executable CUDA graph is about to be created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2471a1a00b37b3cb56b796a095c0fb93e"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHEXEC_CREATED" ref="gg690fb2a42aefe39f00033c957ce211b2471a1a00b37b3cb56b796a095c0fb93e" args="" -->CUPTI_CBID_RESOURCE_GRAPHEXEC_CREATED</em>&nbsp;</td><td>
An executable CUDA graph is created. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b24e73fcf1883f507f2451c96ae2dd9c76"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHEXEC_DESTROY_STARTING" ref="gg690fb2a42aefe39f00033c957ce211b24e73fcf1883f507f2451c96ae2dd9c76" args="" -->CUPTI_CBID_RESOURCE_GRAPHEXEC_DESTROY_STARTING</em>&nbsp;</td><td>
An executable CUDA graph is about to be destroyed. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg690fb2a42aefe39f00033c957ce211b2888c713cc24de13847a9bcbb2354870e"></a><!-- doxytag: member="CUPTI_CBID_RESOURCE_GRAPHNODE_CLONED" ref="gg690fb2a42aefe39f00033c957ce211b2888c713cc24de13847a9bcbb2354870e" args="" -->CUPTI_CBID_RESOURCE_GRAPHNODE_CLONED</em>&nbsp;</td><td>
CUDA graph node is cloned. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gcaeba9950bf4f48ea7ab9a0402dc7e6f"></a><!-- doxytag: member="cupti_callbacks.h::CUpti_CallbackIdSync" ref="gcaeba9950bf4f48ea7ab9a0402dc7e6f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__CALLBACK__API.html#gcaeba9950bf4f48ea7ab9a0402dc7e6f">CUpti_CallbackIdSync</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Callback IDs for synchronization domain, CUPTI_CB_DOMAIN_SYNCHRONIZE. This value is communicated to the callback function via the <code>cbid</code> parameter. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggcaeba9950bf4f48ea7ab9a0402dc7e6f9f6517fe91c4340e584d65eebd85a17e"></a><!-- doxytag: member="CUPTI_CBID_SYNCHRONIZE_INVALID" ref="ggcaeba9950bf4f48ea7ab9a0402dc7e6f9f6517fe91c4340e584d65eebd85a17e" args="" -->CUPTI_CBID_SYNCHRONIZE_INVALID</em>&nbsp;</td><td>
Invalid synchronize callback ID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcaeba9950bf4f48ea7ab9a0402dc7e6f90ee543086b48a83c4b08df845d75e01"></a><!-- doxytag: member="CUPTI_CBID_SYNCHRONIZE_STREAM_SYNCHRONIZED" ref="ggcaeba9950bf4f48ea7ab9a0402dc7e6f90ee543086b48a83c4b08df845d75e01" args="" -->CUPTI_CBID_SYNCHRONIZE_STREAM_SYNCHRONIZED</em>&nbsp;</td><td>
Stream synchronization has completed for the stream. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggcaeba9950bf4f48ea7ab9a0402dc7e6f4be043a702da1d8f89d0095a694d59db"></a><!-- doxytag: member="CUPTI_CBID_SYNCHRONIZE_CONTEXT_SYNCHRONIZED" ref="ggcaeba9950bf4f48ea7ab9a0402dc7e6f4be043a702da1d8f89d0095a694d59db" args="" -->CUPTI_CBID_SYNCHRONIZE_CONTEXT_SYNCHRONIZED</em>&nbsp;</td><td>
Context synchronization has completed for the context. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g7dcebeb8ae4f79c90905a8f6befc51d7"></a><!-- doxytag: member="cupti_callbacks.h::cuptiEnableAllDomains" ref="g7dcebeb8ae4f79c90905a8f6befc51d7" args="(uint32_t enable, CUpti_SubscriberHandle subscriber)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEnableAllDomains           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable or disable all callbacks in all domains.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to cuptiGetCallbackState, cuptiEnableCallback, cuptiEnableDomain, and cuptiEnableAllDomains. For example, if cuptiGetCallbackState(sub, d, *) and cuptiEnableAllDomains(sub) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>New enable state for all callbacks in all domain. Zero disables all callbacks, non-zero enables all callbacks. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>- Handle to callback subscription</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialized CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> is invalid </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gce619a64b77d6533754de798b5e8263e"></a><!-- doxytag: member="cupti_callbacks.h::cuptiEnableCallback" ref="gce619a64b77d6533754de798b5e8263e" args="(uint32_t enable, CUpti_SubscriberHandle subscriber, CUpti_CallbackDomain domain, CUpti_CallbackId cbid)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEnableCallback           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a>&nbsp;</td>
          <td class="paramname"> <em>domain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a>&nbsp;</td>
          <td class="paramname"> <em>cbid</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable or disabled callbacks for a subscriber for a specific domain and callback ID.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to cuptiGetCallbackState, cuptiEnableCallback, cuptiEnableDomain, and cuptiEnableAllDomains. For example, if cuptiGetCallbackState(sub, d, c) and cuptiEnableCallback(sub, d, c) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>New enable state for the callback. Zero disables the callback, non-zero enables the callback. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>- Handle to callback subscription </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cbid</em>&nbsp;</td><td>The ID of the callback</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialized CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code>, <code>domain</code> or <code>cbid</code> is invalid. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g926699208431270d4197fcb639da6a5c"></a><!-- doxytag: member="cupti_callbacks.h::cuptiEnableDomain" ref="g926699208431270d4197fcb639da6a5c" args="(uint32_t enable, CUpti_SubscriberHandle subscriber, CUpti_CallbackDomain domain)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEnableDomain           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a>&nbsp;</td>
          <td class="paramname"> <em>domain</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable or disabled all callbacks for a specific domain.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to cuptiGetCallbackState, cuptiEnableCallback, cuptiEnableDomain, and cuptiEnableAllDomains. For example, if cuptiGetCallbackEnabled(sub, d, *) and cuptiEnableDomain(sub, d) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>New enable state for all callbacks in the domain. Zero disables all callbacks, non-zero enables all callbacks. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>- Handle to callback subscription </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialized CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> or <code>domain</code> is invalid </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0fe2357995aa7861a37e5896c6a18635"></a><!-- doxytag: member="cupti_callbacks.h::cuptiGetCallbackName" ref="g0fe2357995aa7861a37e5896c6a18635" args="(CUpti_CallbackDomain domain, uint32_t cbid, const char **name)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetCallbackName           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a>&nbsp;</td>
          <td class="paramname"> <em>domain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>cbid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char **&nbsp;</td>
          <td class="paramname"> <em>name</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns a pointer to the name c_string in <code>**name</code>.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Names</b> are available only for the DRIVER and RUNTIME domains.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cbid</em>&nbsp;</td><td>The ID of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>name</em>&nbsp;</td><td>Returns pointer to the name string on success, NULL otherwise</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>name</code> is NULL, or if <code>domain</code> or <code>cbid</code> is invalid. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gf861e55f8d61286b97471e2bede138a6"></a><!-- doxytag: member="cupti_callbacks.h::cuptiGetCallbackState" ref="gf861e55f8d61286b97471e2bede138a6" args="(uint32_t *enable, CUpti_SubscriberHandle subscriber, CUpti_CallbackDomain domain, CUpti_CallbackId cbid)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetCallbackState           </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_CallbackDomain</a>&nbsp;</td>
          <td class="paramname"> <em>domain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7fde6b76bdbcafbcf750b0f91a3484f8">CUpti_CallbackId</a>&nbsp;</td>
          <td class="paramname"> <em>cbid</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns non-zero in <code>*enable</code> if the callback for a domain and callback ID is enabled, and zero if not enabled.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: a subscriber must serialize access to cuptiGetCallbackState, cuptiEnableCallback, cuptiEnableDomain, and cuptiEnableAllDomains. For example, if cuptiGetCallbackState(sub, d, c) and cuptiEnableCallback(sub, d, c) are called concurrently, the results are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>enable</em>&nbsp;</td><td>Returns non-zero if callback enabled, zero if not enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>Handle to the initialize subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domain</em>&nbsp;</td><td>The domain of the callback </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cbid</em>&nbsp;</td><td>The ID of the callback</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialized CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>enabled</code> is NULL, or if <code>subscriber</code>, <code>domain</code> or <code>cbid</code> is invalid. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd2c32850b2e03b37e284df083dc9053f"></a><!-- doxytag: member="cupti_callbacks.h::cuptiSubscribe" ref="gd2c32850b2e03b37e284df083dc9053f" args="(CUpti_SubscriberHandle *subscriber, CUpti_CallbackFunc callback, void *userdata)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiSubscribe           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a> *&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a>&nbsp;</td>
          <td class="paramname"> <em>callback</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>userdata</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Initializes a callback subscriber with a callback function and (optionally) a pointer to user data. The returned subscriber handle can be used to enable and disable the callback for specific domains and callback IDs. <dl class="note" compact><dt><b>Note:</b></dt><dd>Only a single subscriber can be registered at a time. To ensure that no other CUPTI client interrupts the profiling session, it's the responsibility of all the CUPTI clients to call this function before starting the profling session. In case profiling session is already started by another CUPTI client, this function returns the error code CUPTI_ERROR_MULTIPLE_SUBSCRIBERS_NOT_SUPPORTED. Note that this function returns the same error when application is launched using NVIDIA tools like nvprof, Visual Profiler, Nsight Systems, Nsight Compute, cuda-gdb and cuda-memcheck. <p>
This function does not enable any callbacks. <p>
<b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>Returns handle to initialize subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>callback</em>&nbsp;</td><td>The callback function </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>userdata</em>&nbsp;</td><td>A pointer to user data. This data will be passed to the callback function via the <code>userdata</code> paramater.</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_MULTIPLE_SUBSCRIBERS_NOT_SUPPORTED</em>&nbsp;</td><td>if there is already a CUPTI subscriber </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g4526fa1776292fa325971e815e0c7dc2"></a><!-- doxytag: member="cupti_callbacks.h::cuptiSupportedDomains" ref="g4526fa1776292fa325971e815e0c7dc2" args="(size_t *domainCount, CUpti_DomainTable *domainTable)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiSupportedDomains           </td>
          <td>(</td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>domainCount</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g9e77154dbe0cc07fb9332f95a83d6d9e">CUpti_DomainTable</a> *&nbsp;</td>
          <td class="paramname"> <em>domainTable</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns in <code>*domainTable</code> an array of size <code>*domainCount</code> of all the available callback domains. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>domainCount</em>&nbsp;</td><td>Returns number of callback domains </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domainTable</em>&nbsp;</td><td>Returns pointer to array of available callback domains</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialize CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>domainCount</code> or <code>domainTable</code> are NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g20b68c9c33f129179b56687a17356682"></a><!-- doxytag: member="cupti_callbacks.h::cuptiUnsubscribe" ref="g20b68c9c33f129179b56687a17356682" args="(CUpti_SubscriberHandle subscriber)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiUnsubscribe           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7932f010e8b2c785d85f4048235afca3">CUpti_SubscriberHandle</a>&nbsp;</td>
          <td class="paramname"> <em>subscriber</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Removes a callback subscriber so that no future callbacks will be issued to that subscriber. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>subscriber</em>&nbsp;</td><td>Handle to the initialize subscriber</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>if unable to initialized CUPTI </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>subscriber</code> is NULL or not initialized </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
