<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li class="current"><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_l">- l -</a></h3><ul>
<li>l2_transactions
: <a class="el" href="structCUpti__ActivityGlobalAccess.html#d4727bd5b04cb6fedf75982de05554b9">CUpti_ActivityGlobalAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#43f104be05f9edcf5c754f5c47e14db7">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#8d62607220bc310fbffeba2180005f80">CUpti_ActivityGlobalAccess3</a>
<li>l2CacheSize
: <a class="el" href="structCUpti__ActivityDevice.html#ad3a569e4232a91f063e4f8a487caddf">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#6c852b770f1565b50b72f98a01bfd71a">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#b42315071cf0a14b630ac360597cc5d2">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#e6649c324d5b3b654c43cf939fb3014b">CUpti_ActivityDevice4</a>
<li>latencySamples
: <a class="el" href="structCUpti__ActivityPCSampling2.html#961407bd5c98c35ea90f4b73ec9f6bf4">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#41dbe00ec06438094bde3a1402bc819a">CUpti_ActivityPCSampling3</a>
<li>launchType
: <a class="el" href="structCUpti__ActivityKernel4.html#247ccacf787fb2a55c77d96687fb3ee7">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#f0f7f737518b6d22db58014239d74738">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#cd98f6540868e1044d32182d97b8fbfc">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#b9a8a4ed9d4bb056483a5a1b41977b2e">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#d2a138322da251d4d9468ce2dbe8c8ad">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#f32b1561e7bf8757d0026aaa211171b0">CUpti_ActivityKernel9</a>
<li>lineNo
: <a class="el" href="structCUpti__ActivityOpenAcc.html#e35c6ba06465ae242fa2a40909eb2835">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#94009306a3216640ccdaee51d3c63278">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#958003e64ec16ea0b54d354b5cdeb7fa">CUpti_ActivityOpenAccOther</a>
<li>lineNumber
: <a class="el" href="structCUpti__ActivitySourceLocator.html#68b2213b1a17ee30ab91ccc432eb5a83">CUpti_ActivitySourceLocator</a>
, <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#b5f50cab1eb124a26a5b7a5907429906">CUpti_GetSassToSourceCorrelationParams</a>
<li>linkRate
: <a class="el" href="structCUpti__ActivityPcie.html#035f452cd8eccd4b70f0525b4b5106e3">CUpti_ActivityPcie</a>
<li>linkWidth
: <a class="el" href="structCUpti__ActivityPcie.html#c69d0b3eaa88e7d37f05c74252ce8be1">CUpti_ActivityPcie</a>
<li>localMemoryPerThread
: <a class="el" href="structCUpti__ActivityCdpKernel.html#65a2acdc9713cafed3f1e949ba9b2358">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#7cfe4c3e94a626447c3d546188ad637d">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel.html#9e97d54c211fb928e3f35e14d872f86f">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#b0d7eb482d3c018abe19797f4a83e9b2">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#dc1a70760a7957bfa794ae9b150d61b2">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#7eb69eef831122583850168410ef3c0d">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#0019e20da584a302b95d7310075e2ce0">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#385047d9da74891e88cf20c1a7ea6b90">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#756b5088d3b1384de0252c9419a82db0">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#de57e9ec6f18d39472d9d24bcc6f4bd7">CUpti_ActivityKernel8</a>
<li>localMemoryTotal
: <a class="el" href="structCUpti__ActivityKernel4.html#25b4acf0caeed888f082105f93a82736">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#37e98f953cc4b384e9bb383042843ccf">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#9be00dece5e343f16a1e876c0044d426">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#a18e593a822ea6e6eec2e24782a45691">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#aa207e6fbd4bb22a1d6d476d19b3e67b">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#7913766967c1a846b0ec1c43eb6177f8">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#7df5b16b858e0857de7509de0c8a5d22">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel.html#559eb15429cd3009d59a01e778406ca8">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#85339bc4b57ad0de49e4af733554d0dd">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#517cdc3a3e29d57e900a7c74eac1a018">CUpti_ActivityCdpKernel</a>
<li>localMemoryTotal_v2
: <a class="el" href="structCUpti__ActivityKernel9.html#b3f7f2cb12fe06a5f696d11077ddf4f4">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#ba7834abc71391760bff30d7b4182ac9">CUpti_ActivityKernel8</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
