{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-cudart >=12.1.55", "cuda-nvrtc >=12.1.55", "cuda-opencl >=12.1.56", "libcublas >=12.1.0.26", "libcufft >=11.0.2.4", "libcurand >=10.3.2.56", "libcusolver >=11.4.4.55", "libcusparse >=12.0.2.55", "libnpp >=12.0.2.50", "libnvjitlink >=12.1.55", "libnvjpeg >=12.1.0.39"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-libraries-12.1.0-0", "features": "", "files": [], "fn": "cuda-libraries-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-libraries-12.1.0-0", "type": 1}, "md5": "c05405ab40b0b61b9c016031ce93ccc4", "name": "cuda-libraries", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-libraries-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1477, "subdir": "win-64", "timestamp": 1677129968000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-libraries-12.1.0-0.tar.bz2", "version": "12.1.0"}