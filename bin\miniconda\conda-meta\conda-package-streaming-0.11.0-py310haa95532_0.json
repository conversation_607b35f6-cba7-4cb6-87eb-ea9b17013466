{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0", "requests", "zstandard >=0.15"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-package-streaming-0.11.0-py310haa95532_0", "files": ["Lib/site-packages/conda_package_streaming-0.11.0.dist-info/INSTALLER", "Lib/site-packages/conda_package_streaming-0.11.0.dist-info/LICENSE", "Lib/site-packages/conda_package_streaming-0.11.0.dist-info/METADATA", "Lib/site-packages/conda_package_streaming-0.11.0.dist-info/RECORD", "Lib/site-packages/conda_package_streaming-0.11.0.dist-info/REQUESTED", "Lib/site-packages/conda_package_streaming-0.11.0.dist-info/WHEEL", "Lib/site-packages/conda_package_streaming-0.11.0.dist-info/direct_url.json", "Lib/site-packages/conda_package_streaming/__init__.py", "Lib/site-packages/conda_package_streaming/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/create.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/extract.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/lazy_wheel.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/package_streaming.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/s3.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/transmute.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/__pycache__/url.cpython-310.pyc", "Lib/site-packages/conda_package_streaming/create.py", "Lib/site-packages/conda_package_streaming/exceptions.py", "Lib/site-packages/conda_package_streaming/extract.py", "Lib/site-packages/conda_package_streaming/lazy_wheel.py", "Lib/site-packages/conda_package_streaming/package_streaming.py", "Lib/site-packages/conda_package_streaming/s3.py", "Lib/site-packages/conda_package_streaming/transmute.py", "Lib/site-packages/conda_package_streaming/url.py"], "fn": "conda-package-streaming-0.11.0-py310haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-package-streaming-0.11.0-py310haa95532_0", "type": 1}, "md5": "79160f2b540ef91d4f251474c44b8281", "name": "conda-package-streaming", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-package-streaming-0.11.0-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::conda-package-streaming==0.11.0=py310haa95532_0[md5=79160f2b540ef91d4f251474c44b8281]", "sha256": "987cdf21bcbae95bf108c23bacd6326fd8fcf5ecd34f6f8752a0cf35c9ea1141", "size": 31269, "subdir": "win-64", "timestamp": 1731366607000, "url": "https://repo.anaconda.com/pkgs/main/win-64/conda-package-streaming-0.11.0-py310haa95532_0.conda", "version": "0.11.0"}