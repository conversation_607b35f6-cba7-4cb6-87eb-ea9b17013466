var $body=$(document.body),$search=$("#search input"),$reset=$('#search button[type="reset"]'),$contents_container=$("#contents-container"),$content=$("#contents"),$sections=$content.find(".topic"),$results=$("#search-results"),$resultOL=$results.find("ol"),$sitenav=$("#site-nav"),$resizenav=$("#resize-nav"),sitenav_default_width=parseInt($sitenav.css("width")),sitenav_min_width=100,sitenav_width,$cur_page_result=$('<li id="cur_page_result" class="category">Current Document <span id="num-in-top"></span></li>'),$other_page_results=$('<li id="other_page_results" class="category">Other Documents</li>');var max_highlight=20,hl_prefix='<span class="highlight">',hl_postfix="</span>";var page_title=document.getElementsByTagName("title")[0].innerHTML;var number_in_top_5=0;var unhighlight=(function(){var a=new RegExp(hl_prefix,"g");var b=new RegExp(hl_postfix,"g");var c=document.getElementById("contents");return function(e){var d=c.innerHTML;d=d.replace(a,"");c.innerHTML=d.replace(b,"")}})();var ajax_semaphore=(function(c){var b=0;function a(){b=c}a.prototype.acquire_resource=function(){if(this._available_resource>=0){--this._available_resource;return true}return false};a.prototype.release_resource=function(){if(this._available_resource<c){++this._available_resource;return true}return false};a.prototype.available_resource=function(){return this._available_resource};return a})(5);var parseXML;if(typeof window.DOMParser!="undefined"){parseXML=function(a){return(new window.DOMParser()).parseFromString(a,"text/xml")}}else{if(typeof window.ActiveXObject!="undefined"&&(new window.DOMParser())){parseXML=function(b){var a=new window.ActiveXObject("Microsoft.XMLDOM");a.async="false";a.loadXML(b);return a}}else{throw new Error("No XML Parser found")}}$search.on("change keydown keyup",showHideReset);$reset.on("click",resetAndHide);$("#submit").on("click",submitSearch);$("#search").on("submit",submitSearch);$(document).ready(function(){$('input[name="search-text"]').attr("placeholder","Search");var a=readCookie("search_query");if(a&&a!="undefined"&&a!=""){$search.val(a);showSearchResults();searchFor(a);highlight(a)}else{return}});if(/^http/i.test(location.protocol)){$body.addClass("online");$("#company").click(function(){location.href="http://developer.nvidia.com/"})}function submitSearch(){setTimeout(function(){searchNow()},1);return false}function showHideReset(a){if(a&&a.keyCode!=13){return}setTimeout(function(){searchNow(a)},1)}function resetAndHide(a){setTimeout(function(){hideSearchResults();setCookie("search_query","; expires=Thu, 01 Jan 1970 00:00:01 GMT;");setCookie("current_page","; expires=Thu, 01 Jan 1970 00:00:01 GMT;");number_in_top_5=0;return searchNow(a)},1)}function searchNow(b){if(b&&b.keyCode==27){$search.val("").blur()}var a=$search.val().replace(/^\s+|\s+$/g,"");clearSearchResults();number_in_top_5=0;if(a.length){showSearchResults();searchFor(a);setCookie("search_query",a);setCookie("current_page",page_title)}else{hideSearchResults();setCookie("search_query","; expires=Thu, 01 Jan 1970 00:00:01 GMT;")}}function searchFor(d){var b=SearchToc(d);for(var c=0,a=b.length;c<a;++c){addSearchResult(b[c],true)}}function showSearchResults(){$body.addClass("search-visible")}function hideSearchResults(){$body.removeClass("search-visible");unhighlight()}function clearSearchResults(){$resultOL.empty()}function getByClassName(a,b){if(typeof b.getElementsByClassName==="function"){return b.getElementsByClassName(a)}}function addSearchResult(g,f){var d=$search.val().replace(/^\s+|\s+$/g,"");var c=new RegExp($search.val().replace(/^\s+|\s+$/g,""),"gi");if(!g.href){return}if(!g.title){g.title=g.href}var b=readCookie("current_page")||page_title;if(number_in_top_5<5&&(g.title.indexOf(b)>=0||b.indexOf(g.title)>=0)){if($other_page_results.parent().length>0){var h=$('<li><h3><a href="'+g.href+'" class="foundResult">'+g.title+"</a></h3></li>").insertBefore($other_page_results)}else{var h=$('<li><h3><a href="'+g.href+'" class="foundResult">'+g.title+"</a></h3></li>");$resultOL.prepend($cur_page_result,h,$other_page_results)}++number_in_top_5}else{var h=$('<li><h3><a href="'+g.href+'" class="foundResult">'+g.title+"</a></h3></li>").appendTo($resultOL)}$("#num-in-top",$cur_page_result).text(number_in_top_5>=5?"(top 5)":"");if(g.text){var e=htmlEscape(g.text);if(g.bold){var i=new RegExp(g.bold,"gi");e=e.replace(i,"<strong>$&</strong>")}h.append('<p class="shortDescription">Loading...</p>')}if(f||e=="..."||e=="Loading..."||e==""){var a=new XMLHttpRequest();a.onreadystatechange=function(){if(a.readyState==4){var o=new RegExp(g.bold,"gi");var l=$(a.responseText);var n=$("article > div.topic",l);var k=n.text();k=k.replace(/<[^<>]*>/gi,"");k=k.replace(/(\s)\s*/g," ");var j=k.search(o);if(j<0){j=k.search(c)}var p=k.lastIndexOf(" ",j-10);var m=k.indexOf(" ",j+15);k=k.substring(p,m);if(j>=0){k=k.replace(o,"<strong>$&</strong>");k=k.replace(c,"<strong>$&</strong>")}if(j>0){k="..."+k}k=k+"...";$(".shortDescription",h).html(k)}};a.open("GET",g.href,true);a.send()}return h}function syncTOC(){var f=$contents_container[0].scrollTop,e=$("#site-nav .current"),c=e.find("a").attr("href"),h=Number.NEGATIVE_INFINITY,g;$sections.each(function(){var i=this.offsetTop-f-1;if(i<=0&&i>h){h=i;g=this}});if(!g){g=$sections.get(0)}if(!g){return}var d="#"+g.id;if(c!=d){e.removeClass("current");var b=$('a[href="'+d+'"]',$sitenav);if(!b.length){var a=location.pathname.match(/[^\/]+$/)[0];b=$('a[href="'+a+d+'"]')}b=b.closest("li:visible");if(!b.length){return}b.addClass("current");toggleNavSectionByName(d,true);b.scrollintoview({duration:0})}}function toggleNavSectionByHandle(d,a){var b=d.children(".section-link").first();if(!b.length){return}var c=b.next("ul");var a=a!==undefined?a:!c.is(":visible");c.toggle(a);b.children(".twiddle").html(a?"▽":"▷");if(a){toggleNavSectionByHandle(d.parent().parent(),a)}}function toggleNavSectionByName(b,a){var c=$('a[href$="'+b+'"]',$sitenav).parent().parent();toggleNavSectionByHandle(c,a)}function allowNavSectionCollapsing(){$(".section-link").parents().has("ul:parent").each(function(){var b=$(this);var a=b.children(".section-link").first();$('<span class="twiddle">▽</span>').prependTo(a).click(function(){toggleNavSectionByHandle(b)});a.children("a").click(function(){var e=a.next("ul");var c=$(this).attr("href");var d=location.href.match(/[^\/]+$/)[0];toggleNavSectionByHandle(b,(location.hash!=c&&d!=c)||!e.is(":visible"));if(d==c){return false}})})}function toggleNavSectionAll(a){$(".section-link").parents().has("ul").each(function(){toggleNavSectionByHandle($(this),a)})}function syncNavSectionCollapse(){var a=location.href.match(/([^\/]+)\.html$/);if(!location.hash&&(typeof a==="null"||!a[1])){return}var b=location.hash?location.hash:"#"+a[1];toggleNavSectionAll(false);toggleNavSectionByName(b,true)}function setSitenavWidth(a){a=Math.min($(document).width()/2,Math.max(0,a));if(a<sitenav_min_width){a=0}$sitenav.width(a).toggle(a!=0);$resizenav.toggleClass("nav-closed",!a);$("#toggle-nav").css("left",a).toggleClass("nav-closed",!a);if(localStorage){localStorage.setItem("navwidth",a)}}function getSitenavWidth(){sitenav_width=null;if(localStorage){sitenav_width=localStorage.getItem("navwidth")}return sitenav_width!==null?sitenav_width:sitenav_default_width}function allowNavResizing(){$resizenav.mousedown(function(f){var b=f.screenX,d=$sitenav.width(),c;$("*").attr("unselectable","on").addClass("unselectable");$(document.body).addClass("drag-active").mousemove(function a(g){c=d+g.screenX-b;setSitenavWidth(c)}).mouseup(function(){$(this).off("mousemove").off("mouseup").removeClass("drag-active");$("*").attr("unselectable","").removeClass("unselectable")})})}function allowNavToggling(){$('<div id="toggle-nav"/>').appendTo($resizenav).mousedown(function(a){if($sitenav.width()>0){setSitenavWidth(0)}else{setSitenavWidth(getSitenavWidth()>0?getSitenavWidth():sitenav_default_width)}})}function addEnoughPaddingAfterContent(){var b=$("#contents-end").css("margin",0),a=$(".topic:last");if($("dl.landing-page").length){return}$(window).on("resize",c);c();function c(){b.css("margin-top",Math.max($contents_container[0].offsetHeight-a.height()-15,0))}}function scrollIntoView(b){if((typeof b==="string")||(b=location.hash.substr(1))){var a=$sections.find('.topic[id="'+b+'"]').offsetRelativeTo($content);if(a){$content.scrollTop(a.top)}}else{$content.scrollTop(0)}}RegExp.escape=function(a){return a.replace("/[-[]{}()*+?.,\\^$|#s]/g","\\$&")};function htmlEscape(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function canonical(a){var b=/([^\/#]+)(?:#[^#\/]*)?$/.exec(a);b=b&&b[1];return b}function atRoot(){return canonical(location.href)=="docs.html"}function relative(a){return atRoot()?a:"../../"+a}jQuery.fn.offsetRelativeTo=function(b){var a=$(b),d=this.offset(),c=a.offset();if(d){d.top-=c.top-a.scrollTop();d.left-=c.left-a.scrollLeft()}return d};function setCookie(a,b){document.cookie=a+"="+b+"; path=/"}function readCookie(a){for(var c=document.cookie.split(/;\s*/),b=c.length;b--;){var d=c[b].split("=");if(d[0]==a){return d[1]}}}function supportsMathML(){var a=false;if(document.createElementNS){var b="http://www.w3.org/1998/Math/MathML";var d=document.createElement("div");d.style.position="absolute";var c=d.appendChild(document.createElementNS(b,"math")).appendChild(document.createElementNS(b,"mfrac"));c.appendChild(document.createElementNS(b,"mi")).appendChild(document.createTextNode("xx"));c.appendChild(document.createElementNS(b,"mi")).appendChild(document.createTextNode("yy"));document.body.appendChild(d);a=d.offsetHeight>d.offsetWidth;document.body.removeChild(d)}return a}function highlight(f){var c=false;var b;var g=document.getElementById("contents").children;var e=new RegExp("(?:>[^<]*)("+f+")(?:[^>]*<)","gi");var a=function(h,i){var j=h.replace(i,hl_prefix+i+hl_postfix);return j};for(b=0;b<3&&b<g.length;++b){g[b].innerHTML=g[b].innerHTML.replace(e,a)}var d=document.getElementsByClassName("highlight")[0];if(d){d.scrollIntoView();c=true}if(b<g.length){setTimeout(function(){for(b=3;b<g.length;++b){g[b].innerHTML=g[b].innerHTML.replace(e,a)}if(!c){var h=document.getElementsByClassName("highlight")[0];if(h){h.scrollIntoView()}}delete b;delete g;delete e;delete a},10)}}$(document).ready(function(){addEnoughPaddingAfterContent();syncTOC();$contents_container.on("scroll",syncTOC);$(window).hashchange(scrollIntoView);allowNavResizing();allowNavToggling();allowNavSectionCollapsing();syncNavSectionCollapse();setSitenavWidth(getSitenavWidth());if(!supportsMathML()){$("#eqn-warning").show();$("math").wrap('<span class="math-unsupported"/>')}});