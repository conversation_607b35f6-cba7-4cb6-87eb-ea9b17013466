{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["libcurand >=*********"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurand-dev-*********-0", "features": "", "files": ["bin/curand64_10.dll", "include/curand.h", "include/curand_discrete.h", "include/curand_discrete2.h", "include/curand_globals.h", "include/curand_kernel.h", "include/curand_lognormal.h", "include/curand_mrg32k3a.h", "include/curand_mtgp32.h", "include/curand_mtgp32_host.h", "include/curand_mtgp32_kernel.h", "include/curand_mtgp32dc_p_11213.h", "include/curand_normal.h", "include/curand_normal_static.h", "include/curand_philox4x32_x.h", "include/curand_poisson.h", "include/curand_precalc.h", "include/curand_uniform.h"], "fn": "libcurand-dev-*********-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurand-dev-*********-0", "type": 1}, "md5": "1ef8924a4928d6aec58ea2f73da69da4", "name": "libcurand-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurand-dev-*********-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/curand64_10.dll", "path_type": "hardlink", "sha256": "e996a2d72583eb5cb81cd145b718239fd3f7e0b84fd861f21c8e6793b649a221", "sha256_in_prefix": "e996a2d72583eb5cb81cd145b718239fd3f7e0b84fd861f21c8e6793b649a221", "size_in_bytes": 63505408}, {"_path": "include/curand.h", "path_type": "hardlink", "sha256": "539411184f10d0a5b95af9d07e27aefc226034ba6b71e60b30f859b93d5f2f6b", "sha256_in_prefix": "539411184f10d0a5b95af9d07e27aefc226034ba6b71e60b30f859b93d5f2f6b", "size_in_bytes": 45042}, {"_path": "include/curand_discrete.h", "path_type": "hardlink", "sha256": "b5ff00452127fead9867f5e9355510616b98cabed82b088ecf055e2946b839d2", "sha256_in_prefix": "b5ff00452127fead9867f5e9355510616b98cabed82b088ecf055e2946b839d2", "size_in_bytes": 3573}, {"_path": "include/curand_discrete2.h", "path_type": "hardlink", "sha256": "96212a4e22ea9bb81805e13c8c5b70f8a66326f03589784b3fed96602c5e6033", "sha256_in_prefix": "96212a4e22ea9bb81805e13c8c5b70f8a66326f03589784b3fed96602c5e6033", "size_in_bytes": 11136}, {"_path": "include/curand_globals.h", "path_type": "hardlink", "sha256": "36d0388b7336e05928e87590b75ff8954b8c98b1292ae82e89b00d72c2f4a2cd", "sha256_in_prefix": "36d0388b7336e05928e87590b75ff8954b8c98b1292ae82e89b00d72c2f4a2cd", "size_in_bytes": 3810}, {"_path": "include/curand_kernel.h", "path_type": "hardlink", "sha256": "f34e0cca3dfc2a4183d970854b5a4ed4464827033cee227a93198aa9e424c728", "sha256_in_prefix": "f34e0cca3dfc2a4183d970854b5a4ed4464827033cee227a93198aa9e424c728", "size_in_bytes": 54810}, {"_path": "include/curand_lognormal.h", "path_type": "hardlink", "sha256": "0f466c1c313091e692ec4d248f4bc694d4c1de1492ba2c26cb63b33945afe35e", "sha256_in_prefix": "0f466c1c313091e692ec4d248f4bc694d4c1de1492ba2c26cb63b33945afe35e", "size_in_bytes": 28839}, {"_path": "include/curand_mrg32k3a.h", "path_type": "hardlink", "sha256": "3abf04755a64953fd88d0f92839d09238c63da66fe56a5ec597017f6d7854d86", "sha256_in_prefix": "3abf04755a64953fd88d0f92839d09238c63da66fe56a5ec597017f6d7854d86", "size_in_bytes": 174017}, {"_path": "include/curand_mtgp32.h", "path_type": "hardlink", "sha256": "6e25999ffb0f236d28c16c578a529380bf3425e48bcb7cd1b126adbf69396767", "sha256_in_prefix": "6e25999ffb0f236d28c16c578a529380bf3425e48bcb7cd1b126adbf69396767", "size_in_bytes": 8055}, {"_path": "include/curand_mtgp32_host.h", "path_type": "hardlink", "sha256": "2775e273d89f3d50bfc398b8636a8c91e5372e7977ba6a16911d9d8398ac1a0f", "sha256_in_prefix": "2775e273d89f3d50bfc398b8636a8c91e5372e7977ba6a16911d9d8398ac1a0f", "size_in_bytes": 18790}, {"_path": "include/curand_mtgp32_kernel.h", "path_type": "hardlink", "sha256": "81174be52adf2cfc91679279ef9e974a9d3858aa162ab52723b5887bb101383c", "sha256_in_prefix": "81174be52adf2cfc91679279ef9e974a9d3858aa162ab52723b5887bb101383c", "size_in_bytes": 14117}, {"_path": "include/curand_mtgp32dc_p_11213.h", "path_type": "hardlink", "sha256": "0e0046f20aa47cb5be78ab6e0106f4da082b7c6430e093a1624fe8fcf9160513", "sha256_in_prefix": "0e0046f20aa47cb5be78ab6e0106f4da082b7c6430e093a1624fe8fcf9160513", "size_in_bytes": 288599}, {"_path": "include/curand_normal.h", "path_type": "hardlink", "sha256": "96fd3efb1dc6ee653ce0779edf1dd3ba6d000c86d4d7d6609736e6f8be3858ca", "sha256_in_prefix": "96fd3efb1dc6ee653ce0779edf1dd3ba6d000c86d4d7d6609736e6f8be3858ca", "size_in_bytes": 27793}, {"_path": "include/curand_normal_static.h", "path_type": "hardlink", "sha256": "f7f5ea154e68064442acc9885b161e4b19fe34b5b53e65dd4d32197484ac8d7f", "sha256_in_prefix": "f7f5ea154e68064442acc9885b161e4b19fe34b5b53e65dd4d32197484ac8d7f", "size_in_bytes": 4861}, {"_path": "include/curand_philox4x32_x.h", "path_type": "hardlink", "sha256": "84a5dd9ff4fbdb5b782b6b1aecb052dcb258ee4efe23cb250e08aa939eee106d", "sha256_in_prefix": "84a5dd9ff4fbdb5b782b6b1aecb052dcb258ee4efe23cb250e08aa939eee106d", "size_in_bytes": 7361}, {"_path": "include/curand_poisson.h", "path_type": "hardlink", "sha256": "8087c293816811a293ab6830214184513e4693d2c38de75d588404d745c43e39", "sha256_in_prefix": "8087c293816811a293ab6830214184513e4693d2c38de75d588404d745c43e39", "size_in_bytes": 26224}, {"_path": "include/curand_precalc.h", "path_type": "hardlink", "sha256": "ccc3ba083d873a034c544275b1eb769ebd91e7583e8652f7f0252f3892c30cfb", "sha256_in_prefix": "ccc3ba083d873a034c544275b1eb769ebd91e7583e8652f7f0252f3892c30cfb", "size_in_bytes": 1395941}, {"_path": "include/curand_uniform.h", "path_type": "hardlink", "sha256": "34ce6f6c3e4e83d6a7b12926331ec78c9f19ec7cda22d6b638a772123b63a1d5", "sha256_in_prefix": "34ce6f6c3e4e83d6a7b12926331ec78c9f19ec7cda22d6b638a772123b63a1d5", "size_in_bytes": 17970}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 52405934, "subdir": "win-64", "timestamp": 1674624737000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libcurand-dev-*********-0.tar.bz2", "version": "*********"}