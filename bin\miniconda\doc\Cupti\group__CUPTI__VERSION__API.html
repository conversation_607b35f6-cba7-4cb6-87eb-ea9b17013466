<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Version</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Version</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__VERSION__API.html#g162a01934e005491e65643ca63dbf6a3">CUPTI_API_VERSION</a>&nbsp;&nbsp;&nbsp;18</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The API version for this implementation of CUPTI.  <a href="#g162a01934e005491e65643ca63dbf6a3"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__VERSION__API.html#g0e63a0d235c5e7e5231cc78ec63fa981">cuptiGetVersion</a> (uint32_t *version)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the CUPTI API version.  <a href="#g0e63a0d235c5e7e5231cc78ec63fa981"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Function and macro to determine the CUPTI version. <hr><h2>Define Documentation</h2>
<a class="anchor" name="g162a01934e005491e65643ca63dbf6a3"></a><!-- doxytag: member="cupti_version.h::CUPTI_API_VERSION" ref="g162a01934e005491e65643ca63dbf6a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_API_VERSION&nbsp;&nbsp;&nbsp;18          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The API version for this implementation of CUPTI. This define along with <a class="el" href="group__CUPTI__VERSION__API.html#g0e63a0d235c5e7e5231cc78ec63fa981">cuptiGetVersion</a> can be used to dynamically detect if the version of CUPTI compiled against matches the version of the loaded CUPTI library.<p>
v1 : CUDAToolsSDK 4.0 v2 : CUDAToolsSDK 4.1 v3 : CUDA Toolkit 5.0 v4 : CUDA Toolkit 5.5 v5 : CUDA Toolkit 6.0 v6 : CUDA Toolkit 6.5 v7 : CUDA Toolkit 6.5(with sm_52 support) v8 : CUDA Toolkit 7.0 v9 : CUDA Toolkit 8.0 v10 : CUDA Toolkit 9.0 v11 : CUDA Toolkit 9.1 v12 : CUDA Toolkit 10.0, 10.1 and 10.2 v13 : CUDA Toolkit 11.0 v14 : CUDA Toolkit 11.1 v15 : CUDA Toolkit 11.2, 11.3 and 11.4 v16 : CUDA Toolkit 11.5 v17 : CUDA Toolkit 11.6 v18 : CUDA Toolkit 11.8 v19 : CUDA Toolkit 12.0 
</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g0e63a0d235c5e7e5231cc78ec63fa981"></a><!-- doxytag: member="cupti_version.h::cuptiGetVersion" ref="g0e63a0d235c5e7e5231cc78ec63fa981" args="(uint32_t *version)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetVersion           </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>version</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Return the API version in <code>*version</code>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>version</em>&nbsp;</td><td>Returns the version</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>version</code> is NULL </td></tr>
  </table>
</dl>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__VERSION__API.html#g162a01934e005491e65643ca63dbf6a3" title="The API version for this implementation of CUPTI.">CUPTI_API_VERSION</a> </dd></dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
