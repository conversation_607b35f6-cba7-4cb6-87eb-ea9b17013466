{"arch": "x86_64", "build": "haff574d_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["libssh2 >=1.10.0", "libssh2 >=1.11.1,<2.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "zlib >=1.2.13,<1.3.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurl-8.11.1-haff574d_0", "files": ["Library/bin/libcurl.dll", "Library/include/curl/curl.h", "Library/include/curl/curlver.h", "Library/include/curl/easy.h", "Library/include/curl/header.h", "Library/include/curl/mprintf.h", "Library/include/curl/multi.h", "Library/include/curl/options.h", "Library/include/curl/stdcheaders.h", "Library/include/curl/system.h", "Library/include/curl/typecheck-gcc.h", "Library/include/curl/urlapi.h", "Library/include/curl/websockets.h", "Library/lib/libcurl.exp", "Library/lib/libcurl.lib"], "fn": "libcurl-8.11.1-haff574d_0.conda", "license": "curl", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurl-8.11.1-haff574d_0", "type": 1}, "md5": "5f09844d411a19981d896e5cf7924765", "name": "libcurl", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurl-8.11.1-haff574d_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::libcurl==8.11.1=haff574d_0[md5=5f09844d411a19981d896e5cf7924765]", "sha256": "ba750670a916db5cc07f75db058cba2aebec460413d0d1ac40eedc622d066f4b", "size": 391654, "subdir": "win-64", "timestamp": 1734542322000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcurl-8.11.1-haff574d_0.conda", "version": "8.11.1"}