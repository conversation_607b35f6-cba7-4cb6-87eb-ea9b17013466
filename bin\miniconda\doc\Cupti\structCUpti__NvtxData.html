<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_NvtxData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_NvtxData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__CALLBACK__API.html">CUPTI Callback API</a>]</small>
</h1><!-- doxytag: class="CUpti_NvtxData" -->Data passed into a NVTX callback function.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__NvtxData.html#29b7697eacc3130d6e789441e880f56b">functionName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__NvtxData.html#f7e94572b94c6d872c6a402ec026feaa">functionParams</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__NvtxData.html#6ffd67804a43a3e3180b5127621f0214">functionReturnValue</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Data passed into a NVTX callback function as the <code>cbdata</code> argument to <a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a>. The <code>cbdata</code> will be this type for <code>domain</code> equal to CUPTI_CB_DOMAIN_NVTX. Unless otherwise notes, the callback data is valid only within the invocation of the callback function that is passed the data. If you need to retain some data for use outside of the callback, you must make a copy of that data. <hr><h2>Field Documentation</h2>
<a class="anchor" name="29b7697eacc3130d6e789441e880f56b"></a><!-- doxytag: member="CUpti_NvtxData::functionName" ref="29b7697eacc3130d6e789441e880f56b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__NvtxData.html#29b7697eacc3130d6e789441e880f56b">CUpti_NvtxData::functionName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Name of the NVTX API function which issued the callback. This string is a global constant and so may be accessed outside of the callback. 
</div>
</div><p>
<a class="anchor" name="f7e94572b94c6d872c6a402ec026feaa"></a><!-- doxytag: member="CUpti_NvtxData::functionParams" ref="f7e94572b94c6d872c6a402ec026feaa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="structCUpti__NvtxData.html#f7e94572b94c6d872c6a402ec026feaa">CUpti_NvtxData::functionParams</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the arguments passed to the NVTX API call. See generated_nvtx_meta.h for structure definitions for the parameters for each NVTX API function. 
</div>
</div><p>
<a class="anchor" name="6ffd67804a43a3e3180b5127621f0214"></a><!-- doxytag: member="CUpti_NvtxData::functionReturnValue" ref="6ffd67804a43a3e3180b5127621f0214" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="structCUpti__NvtxData.html#6ffd67804a43a3e3180b5127621f0214">CUpti_NvtxData::functionReturnValue</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the return value of the NVTX API call. See nvToolsExt.h for each NVTX API function's return value. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
