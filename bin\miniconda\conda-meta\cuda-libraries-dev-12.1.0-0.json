{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-cccl >=12.1.55", "cuda-cudart-dev >=12.1.55", "cuda-nvrtc-dev >=12.1.55", "cuda-opencl-dev >=12.1.56", "cuda-profiler-api >=12.1.55", "libcublas-dev >=12.1.0.26", "libcufft-dev >=11.0.2.4", "libcurand-dev >=10.3.2.56", "libcusolver-dev >=11.4.4.55", "libcusparse-dev >=12.0.2.55", "libnpp-dev >=12.0.2.50", "libnvjitlink-dev >=12.1.55", "libnvjpeg-dev >=12.1.0.39"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-libraries-dev-12.1.0-0", "features": "", "files": [], "fn": "cuda-libraries-dev-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-libraries-dev-12.1.0-0", "type": 1}, "md5": "209d96ee4429b472fc252c1386ddc720", "name": "cuda-libraries-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-libraries-dev-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1514, "subdir": "win-64", "timestamp": 1677129982000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-libraries-dev-12.1.0-0.tar.bz2", "version": "12.1.0"}