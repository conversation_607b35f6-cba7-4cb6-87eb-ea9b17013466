<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityNvLink3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityNvLink3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityNvLink3" -->NVLink information.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#a9e7e9d3ccd9b241fb56c5cbb9fabd99">bandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#fc057d60b7056996eb327282cbbcd42c">flag</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#4160274648b0899f2d5c5544e15c9c53">idDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#56298bdd4303f6cf3819d394fe6437c9">idDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#30da4806b268a5e2703c782335881364">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#9ac440e343e09e2a4fb211e9a2b4c11a">nvlinkVersion</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#29c22f74e9b4da2c357246f5350e7ab3">nvswitchConnected</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#15fd490db7d518b37bacf60d24b8a41f">pad</a> [7]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#e4c5f89a7da90c4bf44a35d8d2e613df">physicalNvLinkCount</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#2fdff4e57250cecc13c888d7e3cbeacd">portDev0</a> [CUPTI_MAX_NVLINK_PORTS]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#6da24893dd90a0883af0ef2f22d9aa50">portDev1</a> [CUPTI_MAX_NVLINK_PORTS]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#0078203dad557507bbb83034e4aaf6bd">typeDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#7a16913df232fb649af06eec8b71c3ea">typeDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#8a845bebf644f1d48c24691f325f3d2f">domainId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink3.html#bbc974610fa9c3668f0211bda30e660b">index</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure gives capabilities of each logical NVLink connection between two devices, gpu&lt;-&gt;gpu or gpu&lt;-&gt;CPU which can be used to understand the topology. NvLink information are now reported using the <a class="el" href="structCUpti__ActivityNvLink4.html" title="NVLink information.">CUpti_ActivityNvLink4</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="a9e7e9d3ccd9b241fb56c5cbb9fabd99"></a><!-- doxytag: member="CUpti_ActivityNvLink3::bandwidth" ref="a9e7e9d3ccd9b241fb56c5cbb9fabd99" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityNvLink3.html#a9e7e9d3ccd9b241fb56c5cbb9fabd99">CUpti_ActivityNvLink3::bandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Banwidth of NVLink in kbytes/sec 
</div>
</div><p>
<a class="anchor" name="8a845bebf644f1d48c24691f325f3d2f"></a><!-- doxytag: member="CUpti_ActivityNvLink3::domainId" ref="8a845bebf644f1d48c24691f325f3d2f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink3.html#8a845bebf644f1d48c24691f325f3d2f">CUpti_ActivityNvLink3::domainId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Domain ID of NPU. On Linux, this can be queried using lspci. 
</div>
</div><p>
<a class="anchor" name="fc057d60b7056996eb327282cbbcd42c"></a><!-- doxytag: member="CUpti_ActivityNvLink3::flag" ref="fc057d60b7056996eb327282cbbcd42c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink3.html#fc057d60b7056996eb327282cbbcd42c">CUpti_ActivityNvLink3::flag</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flag gives capabilities of the link <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g819d686d23d2a3dfed9cc1d3c4ecb162" title="Link flags.">CUpti_LinkFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="4160274648b0899f2d5c5544e15c9c53"></a><!-- doxytag: member="CUpti_ActivityNvLink3::idDev0" ref="4160274648b0899f2d5c5544e15c9c53" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink3.html#4160274648b0899f2d5c5544e15c9c53">CUpti_ActivityNvLink3::idDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev0 is CUPTI_DEV_TYPE_GPU, UUID for device 0. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev0 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="56298bdd4303f6cf3819d394fe6437c9"></a><!-- doxytag: member="CUpti_ActivityNvLink3::idDev1" ref="56298bdd4303f6cf3819d394fe6437c9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink3.html#56298bdd4303f6cf3819d394fe6437c9">CUpti_ActivityNvLink3::idDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev1 is CUPTI_DEV_TYPE_GPU, UUID for device 1. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev1 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="bbc974610fa9c3668f0211bda30e660b"></a><!-- doxytag: member="CUpti_ActivityNvLink3::index" ref="bbc974610fa9c3668f0211bda30e660b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink3.html#bbc974610fa9c3668f0211bda30e660b">CUpti_ActivityNvLink3::index</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Index of the NPU. First index will always be zero. 
</div>
</div><p>
<a class="anchor" name="30da4806b268a5e2703c782335881364"></a><!-- doxytag: member="CUpti_ActivityNvLink3::kind" ref="30da4806b268a5e2703c782335881364" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityNvLink3.html#30da4806b268a5e2703c782335881364">CUpti_ActivityNvLink3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_NVLINK. 
</div>
</div><p>
<a class="anchor" name="9ac440e343e09e2a4fb211e9a2b4c11a"></a><!-- doxytag: member="CUpti_ActivityNvLink3::nvlinkVersion" ref="9ac440e343e09e2a4fb211e9a2b4c11a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink3.html#9ac440e343e09e2a4fb211e9a2b4c11a">CUpti_ActivityNvLink3::nvlinkVersion</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
NvLink version. 
</div>
</div><p>
<a class="anchor" name="29c22f74e9b4da2c357246f5350e7ab3"></a><!-- doxytag: member="CUpti_ActivityNvLink3::nvswitchConnected" ref="29c22f74e9b4da2c357246f5350e7ab3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityNvLink3.html#29c22f74e9b4da2c357246f5350e7ab3">CUpti_ActivityNvLink3::nvswitchConnected</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
NVSwitch is connected as an intermediate node. 
</div>
</div><p>
<a class="anchor" name="15fd490db7d518b37bacf60d24b8a41f"></a><!-- doxytag: member="CUpti_ActivityNvLink3::pad" ref="15fd490db7d518b37bacf60d24b8a41f" args="[7]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityNvLink3.html#15fd490db7d518b37bacf60d24b8a41f">CUpti_ActivityNvLink3::pad</a>[7]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. reserved for internal use 
</div>
</div><p>
<a class="anchor" name="e4c5f89a7da90c4bf44a35d8d2e613df"></a><!-- doxytag: member="CUpti_ActivityNvLink3::physicalNvLinkCount" ref="e4c5f89a7da90c4bf44a35d8d2e613df" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink3.html#e4c5f89a7da90c4bf44a35d8d2e613df">CUpti_ActivityNvLink3::physicalNvLinkCount</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of physical NVLinks present between two devices. 
</div>
</div><p>
<a class="anchor" name="2fdff4e57250cecc13c888d7e3cbeacd"></a><!-- doxytag: member="CUpti_ActivityNvLink3::portDev0" ref="2fdff4e57250cecc13c888d7e3cbeacd" args="[CUPTI_MAX_NVLINK_PORTS]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink3.html#2fdff4e57250cecc13c888d7e3cbeacd">CUpti_ActivityNvLink3::portDev0</a>[CUPTI_MAX_NVLINK_PORTS]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 16 NVLinks connected to device 0. If typeDev0 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="6da24893dd90a0883af0ef2f22d9aa50"></a><!-- doxytag: member="CUpti_ActivityNvLink3::portDev1" ref="6da24893dd90a0883af0ef2f22d9aa50" args="[CUPTI_MAX_NVLINK_PORTS]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink3.html#6da24893dd90a0883af0ef2f22d9aa50">CUpti_ActivityNvLink3::portDev1</a>[CUPTI_MAX_NVLINK_PORTS]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 16 NVLinks connected to device 1. If typeDev1 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="0078203dad557507bbb83034e4aaf6bd"></a><!-- doxytag: member="CUpti_ActivityNvLink3::typeDev0" ref="0078203dad557507bbb83034e4aaf6bd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink3.html#0078203dad557507bbb83034e4aaf6bd">CUpti_ActivityNvLink3::typeDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 0 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
<a class="anchor" name="7a16913df232fb649af06eec8b71c3ea"></a><!-- doxytag: member="CUpti_ActivityNvLink3::typeDev1" ref="7a16913df232fb649af06eec8b71c3ea" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink3.html#7a16913df232fb649af06eec8b71c3ea">CUpti_ActivityNvLink3::typeDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 1 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
