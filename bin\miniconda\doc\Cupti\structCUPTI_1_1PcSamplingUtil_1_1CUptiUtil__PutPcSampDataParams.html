<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>CUPTI</b>::<b>PcSamplingUtil</b>::<a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html">CUptiUtil_PutPcSampDataParams</a>
  </div>
</div>
<div class="contents">
<h1>CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html">CUPTI PC Sampling Utility API</a>]</small>
</h1><!-- doxytag: class="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams" -->Params for CuptiUtilPutPcSampData.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g1dc90a8b2a7ee09a0c230aa6256bba5e">PcSamplingBufferType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#ea6f9e4693e3a4e1a3e655c89107b49f">bufferType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#696ede0d98ac0e500a4a74bbdc717de3">fileName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#6093fbb19d3a729a999adabc05181b87">numAttributes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#0d43759457b95653ca345958d488107b">pPCSamplingConfigurationInfo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#5e2fc251c5a6d8f8a4cf665a447350e1">pPcSamplingStallReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#74c6ec18e6b9c86469bb1f3b37aeb567">pSamplingData</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#2e013441478d453676527e02034c704c">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="ea6f9e4693e3a4e1a3e655c89107b49f"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::bufferType" ref="ea6f9e4693e3a4e1a3e655c89107b49f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g1dc90a8b2a7ee09a0c230aa6256bba5e">PcSamplingBufferType</a> <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#ea6f9e4693e3a4e1a3e655c89107b49f">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::bufferType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of buffer to store in file 
</div>
</div><p>
<a class="anchor" name="696ede0d98ac0e500a4a74bbdc717de3"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::fileName" ref="696ede0d98ac0e500a4a74bbdc717de3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#696ede0d98ac0e500a4a74bbdc717de3">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::fileName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
File name to store buffer into it. 
</div>
</div><p>
<a class="anchor" name="6093fbb19d3a729a999adabc05181b87"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::numAttributes" ref="6093fbb19d3a729a999adabc05181b87" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#6093fbb19d3a729a999adabc05181b87">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::numAttributes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of configured attributes 
</div>
</div><p>
<a class="anchor" name="0d43759457b95653ca345958d488107b"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::pPCSamplingConfigurationInfo" ref="0d43759457b95653ca345958d488107b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a>* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#0d43759457b95653ca345958d488107b">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::pPCSamplingConfigurationInfo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a> It is expected to provide configuration details of at least CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_STALL_REASON attribute. 
</div>
</div><p>
<a class="anchor" name="5e2fc251c5a6d8f8a4cf665a447350e1"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::pPcSamplingStallReasons" ref="5e2fc251c5a6d8f8a4cf665a447350e1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a>* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#5e2fc251c5a6d8f8a4cf665a447350e1">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::pPcSamplingStallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a>. 
</div>
</div><p>
<a class="anchor" name="74c6ec18e6b9c86469bb1f3b37aeb567"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::pSamplingData" ref="74c6ec18e6b9c86469bb1f3b37aeb567" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#74c6ec18e6b9c86469bb1f3b37aeb567">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::pSamplingData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
PC sampling buffer. 
</div>
</div><p>
<a class="anchor" name="2e013441478d453676527e02034c704c"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::size" ref="2e013441478d453676527e02034c704c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#2e013441478d453676527e02034c704c">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of the data structure i.e. CUpti_PCSamplingDisableParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:52 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
