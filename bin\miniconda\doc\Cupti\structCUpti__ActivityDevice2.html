<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityDevice2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityDevice2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityDevice2" -->The activity record for a device. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#de5cda35f337bd40d03091bf08358a7d">computeCapabilityMajor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#eb658183ce1b87385dd1904f6b82182e">computeCapabilityMinor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#efe624a3b40bffa03f38818cd7153d31">constantMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#198ed6aa6d5de484796b3437a49ea0aa">coreClockRate</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#dfb91054e5f21eecfade7c8a31a8531c">eccEnabled</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#79551d9408dd17a6c54668be2eeb2635">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#24d8fba86a6ca8bf48f38cd2ffcadc65">globalMemoryBandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#f38fdf322db43d06f9e6af1f86c1206f">globalMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#2d0aadd01aa7773f1748979e4257d088">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#eec71c14de58cc39d4816fecfacd1e02">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#6c852b770f1565b50b72f98a01bfd71a">l2CacheSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#66942f90da75b8351c4a4c9736a2ea4d">maxBlockDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#a881dce2a5fd83bb2e82cc20b0c40cf4">maxBlockDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#174b3067632e074b84336792a387d27c">maxBlockDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#f0e4c20ef8b7e4a7a221304a6e343349">maxBlocksPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#cbad7c01bc92efefb6b0aafd182c251b">maxGridDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#210ba3ec04728fb9d468eb6878fcf147">maxGridDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#09e95973b554657b5be3599c34da1932">maxGridDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#35a8c8f488780d5961dbd33fec6abff4">maxIPC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#fbef3f06e892f3a1048356f8db0ef7fa">maxRegistersPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#d3df1d079725e3de7dcc4abe07eecaaa">maxRegistersPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#1bfbdc19fe8fd739d8300a5a8f3b3df4">maxSharedMemoryPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#261a1be25a70bc42972c455d47f0742d">maxSharedMemoryPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#5eeeca728cd21483cc569784a5430276">maxThreadsPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#e0b7237ff11788e122227be94587ec60">maxWarpsPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#4b05a23714cb721352d4466f45eeb7d5">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#1558e53e287f66841dcfe1879b137251">numMemcpyEngines</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#4f6e49c188a1615bf416670956a570c7">numMultiprocessors</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#9a8755f3467aa55414dbe7b0ca4a95f7">numThreadsPerWarp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#24798611cc00faa4402b71e7d1da0c47">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUuuid&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice2.html#c8e13853bc71255dbb09268cfb6bcc2f">uuid</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents information about a GPU device (CUPTI_ACTIVITY_KIND_DEVICE). Device activity is now reported using the <a class="el" href="structCUpti__ActivityDevice4.html" title="The activity record for a device. (CUDA 11.6 onwards).">CUpti_ActivityDevice4</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="de5cda35f337bd40d03091bf08358a7d"></a><!-- doxytag: member="CUpti_ActivityDevice2::computeCapabilityMajor" ref="de5cda35f337bd40d03091bf08358a7d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#de5cda35f337bd40d03091bf08358a7d">CUpti_ActivityDevice2::computeCapabilityMajor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, major number. 
</div>
</div><p>
<a class="anchor" name="eb658183ce1b87385dd1904f6b82182e"></a><!-- doxytag: member="CUpti_ActivityDevice2::computeCapabilityMinor" ref="eb658183ce1b87385dd1904f6b82182e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#eb658183ce1b87385dd1904f6b82182e">CUpti_ActivityDevice2::computeCapabilityMinor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, minor number. 
</div>
</div><p>
<a class="anchor" name="efe624a3b40bffa03f38818cd7153d31"></a><!-- doxytag: member="CUpti_ActivityDevice2::constantMemorySize" ref="efe624a3b40bffa03f38818cd7153d31" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#efe624a3b40bffa03f38818cd7153d31">CUpti_ActivityDevice2::constantMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of constant memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="198ed6aa6d5de484796b3437a49ea0aa"></a><!-- doxytag: member="CUpti_ActivityDevice2::coreClockRate" ref="198ed6aa6d5de484796b3437a49ea0aa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#198ed6aa6d5de484796b3437a49ea0aa">CUpti_ActivityDevice2::coreClockRate</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The core clock rate of the device, in kHz. 
</div>
</div><p>
<a class="anchor" name="dfb91054e5f21eecfade7c8a31a8531c"></a><!-- doxytag: member="CUpti_ActivityDevice2::eccEnabled" ref="dfb91054e5f21eecfade7c8a31a8531c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#dfb91054e5f21eecfade7c8a31a8531c">CUpti_ActivityDevice2::eccEnabled</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ECC enabled flag for device 
</div>
</div><p>
<a class="anchor" name="79551d9408dd17a6c54668be2eeb2635"></a><!-- doxytag: member="CUpti_ActivityDevice2::flags" ref="79551d9408dd17a6c54668be2eeb2635" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityDevice2.html#79551d9408dd17a6c54668be2eeb2635">CUpti_ActivityDevice2::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the device. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="24d8fba86a6ca8bf48f38cd2ffcadc65"></a><!-- doxytag: member="CUpti_ActivityDevice2::globalMemoryBandwidth" ref="24d8fba86a6ca8bf48f38cd2ffcadc65" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice2.html#24d8fba86a6ca8bf48f38cd2ffcadc65">CUpti_ActivityDevice2::globalMemoryBandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The global memory bandwidth available on the device, in kBytes/sec. 
</div>
</div><p>
<a class="anchor" name="f38fdf322db43d06f9e6af1f86c1206f"></a><!-- doxytag: member="CUpti_ActivityDevice2::globalMemorySize" ref="f38fdf322db43d06f9e6af1f86c1206f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice2.html#f38fdf322db43d06f9e6af1f86c1206f">CUpti_ActivityDevice2::globalMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of global memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="2d0aadd01aa7773f1748979e4257d088"></a><!-- doxytag: member="CUpti_ActivityDevice2::id" ref="2d0aadd01aa7773f1748979e4257d088" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#2d0aadd01aa7773f1748979e4257d088">CUpti_ActivityDevice2::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device ID. 
</div>
</div><p>
<a class="anchor" name="eec71c14de58cc39d4816fecfacd1e02"></a><!-- doxytag: member="CUpti_ActivityDevice2::kind" ref="eec71c14de58cc39d4816fecfacd1e02" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityDevice2.html#eec71c14de58cc39d4816fecfacd1e02">CUpti_ActivityDevice2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_DEVICE. 
</div>
</div><p>
<a class="anchor" name="6c852b770f1565b50b72f98a01bfd71a"></a><!-- doxytag: member="CUpti_ActivityDevice2::l2CacheSize" ref="6c852b770f1565b50b72f98a01bfd71a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#6c852b770f1565b50b72f98a01bfd71a">CUpti_ActivityDevice2::l2CacheSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the L2 cache on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="66942f90da75b8351c4a4c9736a2ea4d"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxBlockDimX" ref="66942f90da75b8351c4a4c9736a2ea4d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#66942f90da75b8351c4a4c9736a2ea4d">CUpti_ActivityDevice2::maxBlockDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a block. 
</div>
</div><p>
<a class="anchor" name="a881dce2a5fd83bb2e82cc20b0c40cf4"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxBlockDimY" ref="a881dce2a5fd83bb2e82cc20b0c40cf4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#a881dce2a5fd83bb2e82cc20b0c40cf4">CUpti_ActivityDevice2::maxBlockDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a block. 
</div>
</div><p>
<a class="anchor" name="174b3067632e074b84336792a387d27c"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxBlockDimZ" ref="174b3067632e074b84336792a387d27c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#174b3067632e074b84336792a387d27c">CUpti_ActivityDevice2::maxBlockDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a block. 
</div>
</div><p>
<a class="anchor" name="f0e4c20ef8b7e4a7a221304a6e343349"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxBlocksPerMultiprocessor" ref="f0e4c20ef8b7e4a7a221304a6e343349" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#f0e4c20ef8b7e4a7a221304a6e343349">CUpti_ActivityDevice2::maxBlocksPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of blocks that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="cbad7c01bc92efefb6b0aafd182c251b"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxGridDimX" ref="cbad7c01bc92efefb6b0aafd182c251b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#cbad7c01bc92efefb6b0aafd182c251b">CUpti_ActivityDevice2::maxGridDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="210ba3ec04728fb9d468eb6878fcf147"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxGridDimY" ref="210ba3ec04728fb9d468eb6878fcf147" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#210ba3ec04728fb9d468eb6878fcf147">CUpti_ActivityDevice2::maxGridDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="09e95973b554657b5be3599c34da1932"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxGridDimZ" ref="09e95973b554657b5be3599c34da1932" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#09e95973b554657b5be3599c34da1932">CUpti_ActivityDevice2::maxGridDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="35a8c8f488780d5961dbd33fec6abff4"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxIPC" ref="35a8c8f488780d5961dbd33fec6abff4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#35a8c8f488780d5961dbd33fec6abff4">CUpti_ActivityDevice2::maxIPC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The maximum "instructions per cycle" possible on each device multiprocessor. 
</div>
</div><p>
<a class="anchor" name="fbef3f06e892f3a1048356f8db0ef7fa"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxRegistersPerBlock" ref="fbef3f06e892f3a1048356f8db0ef7fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#fbef3f06e892f3a1048356f8db0ef7fa">CUpti_ActivityDevice2::maxRegistersPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of registers that can be allocated to a block. 
</div>
</div><p>
<a class="anchor" name="d3df1d079725e3de7dcc4abe07eecaaa"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxRegistersPerMultiprocessor" ref="d3df1d079725e3de7dcc4abe07eecaaa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#d3df1d079725e3de7dcc4abe07eecaaa">CUpti_ActivityDevice2::maxRegistersPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of 32-bit registers available per multiprocessor. 
</div>
</div><p>
<a class="anchor" name="1bfbdc19fe8fd739d8300a5a8f3b3df4"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxSharedMemoryPerBlock" ref="1bfbdc19fe8fd739d8300a5a8f3b3df4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#1bfbdc19fe8fd739d8300a5a8f3b3df4">CUpti_ActivityDevice2::maxSharedMemoryPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum amount of shared memory that can be assigned to a block, in bytes. 
</div>
</div><p>
<a class="anchor" name="261a1be25a70bc42972c455d47f0742d"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxSharedMemoryPerMultiprocessor" ref="261a1be25a70bc42972c455d47f0742d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#261a1be25a70bc42972c455d47f0742d">CUpti_ActivityDevice2::maxSharedMemoryPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum amount of shared memory available per multiprocessor, in bytes. 
</div>
</div><p>
<a class="anchor" name="5eeeca728cd21483cc569784a5430276"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxThreadsPerBlock" ref="5eeeca728cd21483cc569784a5430276" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#5eeeca728cd21483cc569784a5430276">CUpti_ActivityDevice2::maxThreadsPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of threads allowed in a block. 
</div>
</div><p>
<a class="anchor" name="e0b7237ff11788e122227be94587ec60"></a><!-- doxytag: member="CUpti_ActivityDevice2::maxWarpsPerMultiprocessor" ref="e0b7237ff11788e122227be94587ec60" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#e0b7237ff11788e122227be94587ec60">CUpti_ActivityDevice2::maxWarpsPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of warps that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="4b05a23714cb721352d4466f45eeb7d5"></a><!-- doxytag: member="CUpti_ActivityDevice2::name" ref="4b05a23714cb721352d4466f45eeb7d5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityDevice2.html#4b05a23714cb721352d4466f45eeb7d5">CUpti_ActivityDevice2::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device name. This name is shared across all activity records representing instances of the device, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="1558e53e287f66841dcfe1879b137251"></a><!-- doxytag: member="CUpti_ActivityDevice2::numMemcpyEngines" ref="1558e53e287f66841dcfe1879b137251" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#1558e53e287f66841dcfe1879b137251">CUpti_ActivityDevice2::numMemcpyEngines</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of memory copy engines on the device. 
</div>
</div><p>
<a class="anchor" name="4f6e49c188a1615bf416670956a570c7"></a><!-- doxytag: member="CUpti_ActivityDevice2::numMultiprocessors" ref="4f6e49c188a1615bf416670956a570c7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#4f6e49c188a1615bf416670956a570c7">CUpti_ActivityDevice2::numMultiprocessors</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of multiprocessors on the device. 
</div>
</div><p>
<a class="anchor" name="9a8755f3467aa55414dbe7b0ca4a95f7"></a><!-- doxytag: member="CUpti_ActivityDevice2::numThreadsPerWarp" ref="9a8755f3467aa55414dbe7b0ca4a95f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#9a8755f3467aa55414dbe7b0ca4a95f7">CUpti_ActivityDevice2::numThreadsPerWarp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of threads per warp on the device. 
</div>
</div><p>
<a class="anchor" name="24798611cc00faa4402b71e7d1da0c47"></a><!-- doxytag: member="CUpti_ActivityDevice2::pad" ref="24798611cc00faa4402b71e7d1da0c47" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice2.html#24798611cc00faa4402b71e7d1da0c47">CUpti_ActivityDevice2::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="c8e13853bc71255dbb09268cfb6bcc2f"></a><!-- doxytag: member="CUpti_ActivityDevice2::uuid" ref="c8e13853bc71255dbb09268cfb6bcc2f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUuuid <a class="el" href="structCUpti__ActivityDevice2.html#c8e13853bc71255dbb09268cfb6bcc2f">CUpti_ActivityDevice2::uuid</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device UUID. This value is the globally unique immutable alphanumeric identifier of the device. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
