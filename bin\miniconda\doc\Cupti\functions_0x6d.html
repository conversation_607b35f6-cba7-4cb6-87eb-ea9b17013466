<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li class="current"><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_m">- m -</a></h3><ul>
<li>maxActiveClusters
: <a class="el" href="structCUpti__ActivityKernel9.html#329bb107d58507c58c53f02b306d74cd">CUpti_ActivityKernel9</a>
<li>maxBlockDimX
: <a class="el" href="structCUpti__ActivityDevice.html#1155a0e8c8c1b6a5759c6d0b96159965">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#44a151299c085d919966add3a6f47f87">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#1604f10e3e23230e64425cb4c3fd8dff">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#66942f90da75b8351c4a4c9736a2ea4d">CUpti_ActivityDevice2</a>
<li>maxBlockDimY
: <a class="el" href="structCUpti__ActivityDevice3.html#35e57e444c9f87c43d5d6413d80dd2c7">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#3042415a52810209895bedbbb24604cb">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityDevice.html#802490bb5d76fb092717376300d44756">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#a881dce2a5fd83bb2e82cc20b0c40cf4">CUpti_ActivityDevice2</a>
<li>maxBlockDimZ
: <a class="el" href="structCUpti__ActivityDevice.html#9e49d79190baba5dfd96d215d39bdd26">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#174b3067632e074b84336792a387d27c">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#3a76bb7e2ee4db21487dbba23755f789">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#c27990ba772d6427cd20310b27f990b9">CUpti_ActivityDevice4</a>
<li>maxBlocksPerMultiprocessor
: <a class="el" href="structCUpti__ActivityDevice3.html#0960c954286cab808c0b32c7c0761185">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#6135d1bf4955359585eeefa04a81993c">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityDevice.html#169bcfc973aa5e5fe96790582635eeb1">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#f0e4c20ef8b7e4a7a221304a6e343349">CUpti_ActivityDevice2</a>
<li>maxGridDimX
: <a class="el" href="structCUpti__ActivityDevice.html#70ab083c749ba036bb328519db0c283a">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#cbad7c01bc92efefb6b0aafd182c251b">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#a06f5afaad0337b0037564698895d79c">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#a4a54e3d8bf51d13c4767659fa6a4ce8">CUpti_ActivityDevice4</a>
<li>maxGridDimY
: <a class="el" href="structCUpti__ActivityDevice.html#5bf6166aaaf908f752352383ffad1650">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#210ba3ec04728fb9d468eb6878fcf147">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#302b76a3ab7ef5a1e68517a44eb41329">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#278c643ad3c3bf68176043eba552ab3c">CUpti_ActivityDevice4</a>
<li>maxGridDimZ
: <a class="el" href="structCUpti__ActivityDevice.html#047c4a3075cf74d5f8eda66deb698ab7">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#09e95973b554657b5be3599c34da1932">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#770c47aa48a2e4b6e0fc387fbfcdec05">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#a9895adfbe1d7a77066cc5f3d41c994d">CUpti_ActivityDevice4</a>
<li>maxIPC
: <a class="el" href="structCUpti__ActivityDevice3.html#3f1eed403962c75d88c4bcdbe53910bf">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#78f7448e51a6a48d12948e8a3a966520">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityDevice.html#3a0fa19d37bcd0780bb1ff64321f3655">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#35a8c8f488780d5961dbd33fec6abff4">CUpti_ActivityDevice2</a>
<li>maxLaunchesPerPass
: <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#aba56724442c1959f7a075a0e1b5a72b">CUpti_Profiler_BeginSession_Params</a>
<li>maxNumRanges
: <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#de01ce44b90670ee04849bd593e833d6">CUpti_Profiler_CounterDataImageOptions</a>
<li>maxNumRangeTreeNodes
: <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#41e525282b7eaec90f0532baeba403e5">CUpti_Profiler_CounterDataImageOptions</a>
<li>maxPotentialClusterSize
: <a class="el" href="structCUpti__ActivityKernel9.html#324c0a262fdb886f2ca68bebe3839ea3">CUpti_ActivityKernel9</a>
<li>maxRangeNameLength
: <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#8fae036081a86220be88f2eff60941aa">CUpti_Profiler_CounterDataImageOptions</a>
<li>maxRangesPerPass
: <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#1d2a72387e1edbcd8ed6eafb67975086">CUpti_Profiler_BeginSession_Params</a>
<li>maxRegistersPerBlock
: <a class="el" href="structCUpti__ActivityDevice.html#88012896e26d4ae2f72318b75ff23725">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#fbef3f06e892f3a1048356f8db0ef7fa">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#12e3beb24e73e88318ef6d962c8a708b">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#89b91cff6a48bc9e8b40fe485cafd335">CUpti_ActivityDevice4</a>
<li>maxRegistersPerMultiprocessor
: <a class="el" href="structCUpti__ActivityDevice2.html#d3df1d079725e3de7dcc4abe07eecaaa">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#a9c52b68bf83203dcbe1ceca58748e40">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#72c97a93e745ff3d5850350850090dbb">CUpti_ActivityDevice4</a>
<li>maxSharedMemoryPerBlock
: <a class="el" href="structCUpti__ActivityDevice.html#fbc51a5e5b66e7e0e11bb09602c9d717">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#1bfbdc19fe8fd739d8300a5a8f3b3df4">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#bbc309912776c74c5783d97a39ca9ec2">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#c8129e482993ab5ae51ee9d60161fd7b">CUpti_ActivityDevice4</a>
<li>maxSharedMemoryPerMultiprocessor
: <a class="el" href="structCUpti__ActivityDevice2.html#261a1be25a70bc42972c455d47f0742d">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#f39a9f5ed5cd17ecd60df8d57f2e3e89">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#7e2687c87eec55bf34f2d8753b3a9ae9">CUpti_ActivityDevice4</a>
<li>maxThreadsPerBlock
: <a class="el" href="structCUpti__ActivityDevice2.html#5eeeca728cd21483cc569784a5430276">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice.html#6fa193349a4614bdee39236295679980">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#a48515dc3e6d9eabb912c489aa4bd101">CUpti_ActivityDevice3</a>
, <a class="el" href="structCUpti__ActivityDevice4.html#f2f64d2367314bbd8fd746c952eaa62a">CUpti_ActivityDevice4</a>
<li>maxWarpsPerMultiprocessor
: <a class="el" href="structCUpti__ActivityDevice4.html#bf236a35f937e0526532c3d9a9d5033d">CUpti_ActivityDevice4</a>
, <a class="el" href="structCUpti__ActivityDevice.html#76b73260d48ecbe671f791ab82c5d15b">CUpti_ActivityDevice</a>
, <a class="el" href="structCUpti__ActivityDevice2.html#e0b7237ff11788e122227be94587ec60">CUpti_ActivityDevice2</a>
, <a class="el" href="structCUpti__ActivityDevice3.html#6124d277c15d9ee2a297f0d97c1b4ca8">CUpti_ActivityDevice3</a>
<li>memoryClock
: <a class="el" href="structCUpti__ActivityEnvironment.html#fe99db4bdefe7bb7a428b9b0173b649d">CUpti_ActivityEnvironment</a>
<li>memoryKind
: <a class="el" href="structCUpti__ActivityMemset2.html#601cc83c4fec414e90e6fa03420f5e87">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#2bc9a3dc38c0d58c8abcdd285078bcb9">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#e623e7cd202366cb171734f16247eafc">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#7857c71ffba0eaa9e02fe92c82a357da">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemset.html#0d34e34bd810931bd970a4c13a876f48">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#34f46f9937efc2906a6c822e15f4010f">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityMemory.html#665383a70b3dcfa9ff3feb81737dfaac">CUpti_ActivityMemory</a>
<li>memoryOperationType
: <a class="el" href="structCUpti__ActivityMemory3.html#c563b44178403adddff1f47e16a5ae55">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#db7078d199d29cca3f62c58759fc0689">CUpti_ActivityMemory2</a>
<li>memoryPoolConfig
: <a class="el" href="structCUpti__ActivityMemory2.html#dbe413e53c66e0e05526f25248084e19">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#d9450112dca9e61e1a34d3bbe2b35c85">CUpti_ActivityMemory3</a>
<li>memoryPoolOperationType
: <a class="el" href="structCUpti__ActivityMemoryPool2.html#dd537ef336b06369505b407d9fb9a007">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#c528fd8e8bbaee5a059f2bd37294be5b">CUpti_ActivityMemoryPool</a>
<li>memoryPoolType
: <a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#f9f0d1c966e279e36115e018e005063c">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#ef44346ffbd9c5d4453622d092ef09bb">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#48ddeaffc29cb6f86f37a359fe211a85">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#5f18419b83569564924b55177dc1fefb">CUpti_ActivityMemory2</a>
<li>MergedPcSampDataBuffers
: <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#a959dc172662357324cc09d3be1e914b">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a>
<li>migUuid
: <a class="el" href="structCUpti__ActivityDevice4.html#16b3123d076b965451fcbcd0e94d091c">CUpti_ActivityDevice4</a>
<li>minBytesToKeep
: <a class="el" href="structCUpti__ActivityMemoryPool2.html#b3b1055f571a23a88223a998c7db4f12">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#2edb7239b29bfa31423278dabcab29fb">CUpti_ActivityMemoryPool</a>
<li>minNestingLevel
: <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#a917987772c88234c6ffc47c408aeb56">CUpti_Profiler_SetConfig_Params</a>
<li>moduleId
: <a class="el" href="structCUpti__ActivityFunction.html#c710d09404423ecac58ced21e0c51479">CUpti_ActivityFunction</a>
, <a class="el" href="structCUpti__ModuleResourceData.html#783fac071da7924ac75739fec9a083c3">CUpti_ModuleResourceData</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
