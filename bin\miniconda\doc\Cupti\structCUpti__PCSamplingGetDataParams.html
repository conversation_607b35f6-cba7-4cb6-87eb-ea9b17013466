<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingGetDataParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingGetDataParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingGetDataParams" -->Params for cuptiPCSamplingEnable.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetDataParams.html#e26c6d443716d882f691f8efe24db9a1">ctx</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetDataParams.html#74fed7e7f23a6eb7300ae80de927140c">pcSamplingData</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetDataParams.html#1ea7a8f82c08de9c3d7a5f0ca63b40db">pPriv</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetDataParams.html#8bd57dfc9db2544bebc3716f355f3705">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="e26c6d443716d882f691f8efe24db9a1"></a><!-- doxytag: member="CUpti_PCSamplingGetDataParams::ctx" ref="e26c6d443716d882f691f8efe24db9a1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="structCUpti__PCSamplingGetDataParams.html#e26c6d443716d882f691f8efe24db9a1">CUpti_PCSamplingGetDataParams::ctx</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] CUcontext 
</div>
</div><p>
<a class="anchor" name="74fed7e7f23a6eb7300ae80de927140c"></a><!-- doxytag: member="CUpti_PCSamplingGetDataParams::pcSamplingData" ref="74fed7e7f23a6eb7300ae80de927140c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__PCSamplingGetDataParams.html#74fed7e7f23a6eb7300ae80de927140c">CUpti_PCSamplingGetDataParams::pcSamplingData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pcSamplingData</em>&nbsp;</td><td>Data buffer to hold collected PC Sampling data PARSED_DATA Buffer type is void * which can point to PARSED_DATA Refer <a class="el" href="structCUpti__PCSamplingData.html">CUpti_PCSamplingData</a> for buffer format for PARSED_DATA </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="1ea7a8f82c08de9c3d7a5f0ca63b40db"></a><!-- doxytag: member="CUpti_PCSamplingGetDataParams::pPriv" ref="1ea7a8f82c08de9c3d7a5f0ca63b40db" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__PCSamplingGetDataParams.html#1ea7a8f82c08de9c3d7a5f0ca63b40db">CUpti_PCSamplingGetDataParams::pPriv</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Assign to NULL 
</div>
</div><p>
<a class="anchor" name="8bd57dfc9db2544bebc3716f355f3705"></a><!-- doxytag: member="CUpti_PCSamplingGetDataParams::size" ref="8bd57dfc9db2544bebc3716f355f3705" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingGetDataParams.html#8bd57dfc9db2544bebc3716f355f3705">CUpti_PCSamplingGetDataParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure i.e. CUpti_PCSamplingGetDataParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
