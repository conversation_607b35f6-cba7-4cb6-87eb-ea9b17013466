{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvdisasm-12.1.55-0", "features": "", "files": ["LICENSE", "bin/nvdisasm.exe"], "fn": "cuda-nvdisasm-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvdisasm-12.1.55-0", "type": 1}, "md5": "f25a5b44d1a0297e2cff7f42f8b9c607", "name": "cuda-nvdisasm", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvdisasm-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "bin/nvdisasm.exe", "path_type": "hardlink", "sha256": "f9a72cf204a4ff50e0e5c4d8acc280b6882e23f49548f98c0e01739658d53a0a", "sha256_in_prefix": "f9a72cf204a4ff50e0e5c4d8acc280b6882e23f49548f98c0e01739658d53a0a", "size_in_bytes": 50651136}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 50352764, "subdir": "win-64", "timestamp": 1674624231000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-nvdisasm-12.1.55-0.tar.bz2", "version": "12.1.55"}