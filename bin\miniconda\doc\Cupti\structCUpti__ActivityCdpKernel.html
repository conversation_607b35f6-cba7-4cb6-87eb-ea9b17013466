<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityCdpKernel Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityCdpKernel Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityCdpKernel" -->The activity record for CDP (CUDA Dynamic Parallelism) kernel.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#1934e1ac072aed0007ee2406b65f6fe3">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#5ccf936b516a402ec93bcf8fb9e6501d">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#ae481fa77e0680b7545cd8662587f641">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#702c0d2e853e232ec4250609c917006d">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#99430f438ac98686edde4fa2fe358586">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#13f6fe80ac3995700fccfdaa12ca7b64">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#43de44b2969b99c384007d9b7a6c8bcf">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#877e48c3a1a97e614f5be471749f6aec">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#35c486131233face3ef7a73906136c4e">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#b878d387ad8cd16993348e39cac11114">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#f1e7be6652f7f7bca690b54886d0c7ae">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#f7c26aad3d7016bc332d6409af57a471">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#a170530ba2619b1358688f14de2e4358">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#c0743595f70f30389c030736921600a4">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#65a2acdc9713cafed3f1e949ba9b2358">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#517cdc3a3e29d57e900a7c74eac1a018">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#9300af95de27b8b6e3f57fd47b29f4c3">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#c4be3006104dc007653df49faed7224d">parentBlockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#891d2fe6af85cffe78e1f90d2e280f1f">parentBlockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#f4679f4685f646a64be682d90bb36331">parentBlockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#dcc5b312ecc3b0c7ba04a439704fd112">parentGridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#c0329f67f412ec8b5b83a36823b9f061">queued</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#73d9271315af6d4aac753507a4ed573c">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#a8b82d1cb39fee8cbd8b447a2ff8291b">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#dd2f6b817f8ad954e0c755fcb7a986d0">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#8656b76c619f8029f311ebdd87d56bcd">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#577d5a810459619ceaeedca87181b57a">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#b5176c22fd072f2a951c6c0278db169f">submitted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#bbfbbb7db5a351a67f81befc71a4efe4">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityCdpKernel.html#a5a6ed25881e9bd1ed3203e4260426f2">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a CDP kernel execution. <hr><h2>Field Documentation</h2>
<a class="anchor" name="1934e1ac072aed0007ee2406b65f6fe3"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::blockX" ref="1934e1ac072aed0007ee2406b65f6fe3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#1934e1ac072aed0007ee2406b65f6fe3">CUpti_ActivityCdpKernel::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="5ccf936b516a402ec93bcf8fb9e6501d"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::blockY" ref="5ccf936b516a402ec93bcf8fb9e6501d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#5ccf936b516a402ec93bcf8fb9e6501d">CUpti_ActivityCdpKernel::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="ae481fa77e0680b7545cd8662587f641"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::blockZ" ref="ae481fa77e0680b7545cd8662587f641" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#ae481fa77e0680b7545cd8662587f641">CUpti_ActivityCdpKernel::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="702c0d2e853e232ec4250609c917006d"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::completed" ref="702c0d2e853e232ec4250609c917006d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityCdpKernel.html#702c0d2e853e232ec4250609c917006d">CUpti_ActivityCdpKernel::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when kernel is marked as completed, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="99430f438ac98686edde4fa2fe358586"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::contextId" ref="99430f438ac98686edde4fa2fe358586" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#99430f438ac98686edde4fa2fe358586">CUpti_ActivityCdpKernel::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="13f6fe80ac3995700fccfdaa12ca7b64"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::correlationId" ref="13f6fe80ac3995700fccfdaa12ca7b64" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#13f6fe80ac3995700fccfdaa12ca7b64">CUpti_ActivityCdpKernel::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="43de44b2969b99c384007d9b7a6c8bcf"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::deviceId" ref="43de44b2969b99c384007d9b7a6c8bcf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#43de44b2969b99c384007d9b7a6c8bcf">CUpti_ActivityCdpKernel::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="877e48c3a1a97e614f5be471749f6aec"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::dynamicSharedMemory" ref="877e48c3a1a97e614f5be471749f6aec" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#877e48c3a1a97e614f5be471749f6aec">CUpti_ActivityCdpKernel::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="35c486131233face3ef7a73906136c4e"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::end" ref="35c486131233face3ef7a73906136c4e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityCdpKernel.html#35c486131233face3ef7a73906136c4e">CUpti_ActivityCdpKernel::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="bbfbbb7db5a351a67f81befc71a4efe4"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::executed" ref="bbfbbb7db5a351a67f81befc71a4efe4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityCdpKernel.html#bbfbbb7db5a351a67f81befc71a4efe4">CUpti_ActivityCdpKernel::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="b878d387ad8cd16993348e39cac11114"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::gridId" ref="b878d387ad8cd16993348e39cac11114" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityCdpKernel.html#b878d387ad8cd16993348e39cac11114">CUpti_ActivityCdpKernel::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel execution is assigned a unique grid ID. 
</div>
</div><p>
<a class="anchor" name="f1e7be6652f7f7bca690b54886d0c7ae"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::gridX" ref="f1e7be6652f7f7bca690b54886d0c7ae" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#f1e7be6652f7f7bca690b54886d0c7ae">CUpti_ActivityCdpKernel::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="f7c26aad3d7016bc332d6409af57a471"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::gridY" ref="f7c26aad3d7016bc332d6409af57a471" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#f7c26aad3d7016bc332d6409af57a471">CUpti_ActivityCdpKernel::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="a170530ba2619b1358688f14de2e4358"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::gridZ" ref="a170530ba2619b1358688f14de2e4358" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#a170530ba2619b1358688f14de2e4358">CUpti_ActivityCdpKernel::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="c0743595f70f30389c030736921600a4"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::kind" ref="c0743595f70f30389c030736921600a4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityCdpKernel.html#c0743595f70f30389c030736921600a4">CUpti_ActivityCdpKernel::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_CDP_KERNEL 
</div>
</div><p>
<a class="anchor" name="65a2acdc9713cafed3f1e949ba9b2358"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::localMemoryPerThread" ref="65a2acdc9713cafed3f1e949ba9b2358" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#65a2acdc9713cafed3f1e949ba9b2358">CUpti_ActivityCdpKernel::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="517cdc3a3e29d57e900a7c74eac1a018"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::localMemoryTotal" ref="517cdc3a3e29d57e900a7c74eac1a018" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#517cdc3a3e29d57e900a7c74eac1a018">CUpti_ActivityCdpKernel::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="9300af95de27b8b6e3f57fd47b29f4c3"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::name" ref="9300af95de27b8b6e3f57fd47b29f4c3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityCdpKernel.html#9300af95de27b8b6e3f57fd47b29f4c3">CUpti_ActivityCdpKernel::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="c4be3006104dc007653df49faed7224d"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::parentBlockX" ref="c4be3006104dc007653df49faed7224d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#c4be3006104dc007653df49faed7224d">CUpti_ActivityCdpKernel::parentBlockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension of the parent block. 
</div>
</div><p>
<a class="anchor" name="891d2fe6af85cffe78e1f90d2e280f1f"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::parentBlockY" ref="891d2fe6af85cffe78e1f90d2e280f1f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#891d2fe6af85cffe78e1f90d2e280f1f">CUpti_ActivityCdpKernel::parentBlockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension of the parent block. 
</div>
</div><p>
<a class="anchor" name="f4679f4685f646a64be682d90bb36331"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::parentBlockZ" ref="f4679f4685f646a64be682d90bb36331" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#f4679f4685f646a64be682d90bb36331">CUpti_ActivityCdpKernel::parentBlockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension of the parent block. 
</div>
</div><p>
<a class="anchor" name="dcc5b312ecc3b0c7ba04a439704fd112"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::parentGridId" ref="dcc5b312ecc3b0c7ba04a439704fd112" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityCdpKernel.html#dcc5b312ecc3b0c7ba04a439704fd112">CUpti_ActivityCdpKernel::parentGridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the parent kernel. 
</div>
</div><p>
<a class="anchor" name="c0329f67f412ec8b5b83a36823b9f061"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::queued" ref="c0329f67f412ec8b5b83a36823b9f061" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityCdpKernel.html#c0329f67f412ec8b5b83a36823b9f061">CUpti_ActivityCdpKernel::queued</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when kernel is queued up, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the queued time is unknown. 
</div>
</div><p>
<a class="anchor" name="73d9271315af6d4aac753507a4ed573c"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::registersPerThread" ref="73d9271315af6d4aac753507a4ed573c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityCdpKernel.html#73d9271315af6d4aac753507a4ed573c">CUpti_ActivityCdpKernel::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="a5a6ed25881e9bd1ed3203e4260426f2"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::requested" ref="a5a6ed25881e9bd1ed3203e4260426f2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityCdpKernel.html#a5a6ed25881e9bd1ed3203e4260426f2">CUpti_ActivityCdpKernel::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="a8b82d1cb39fee8cbd8b447a2ff8291b"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::sharedMemoryConfig" ref="a8b82d1cb39fee8cbd8b447a2ff8291b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityCdpKernel.html#a8b82d1cb39fee8cbd8b447a2ff8291b">CUpti_ActivityCdpKernel::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="dd2f6b817f8ad954e0c755fcb7a986d0"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::start" ref="dd2f6b817f8ad954e0c755fcb7a986d0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityCdpKernel.html#dd2f6b817f8ad954e0c755fcb7a986d0">CUpti_ActivityCdpKernel::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="8656b76c619f8029f311ebdd87d56bcd"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::staticSharedMemory" ref="8656b76c619f8029f311ebdd87d56bcd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#8656b76c619f8029f311ebdd87d56bcd">CUpti_ActivityCdpKernel::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="577d5a810459619ceaeedca87181b57a"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::streamId" ref="577d5a810459619ceaeedca87181b57a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityCdpKernel.html#577d5a810459619ceaeedca87181b57a">CUpti_ActivityCdpKernel::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="b5176c22fd072f2a951c6c0278db169f"></a><!-- doxytag: member="CUpti_ActivityCdpKernel::submitted" ref="b5176c22fd072f2a951c6c0278db169f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityCdpKernel.html#b5176c22fd072f2a951c6c0278db169f">CUpti_ActivityCdpKernel::submitted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when kernel is submitted to the gpu, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the submission time is unknown. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
