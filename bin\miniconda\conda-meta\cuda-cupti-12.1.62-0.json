{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cupti-12.1.62-0", "features": "", "files": ["LICENSE", "doc/Cupti/annotated.html", "doc/Cupti/classes.html", "doc/Cupti/doxygen.css", "doc/Cupti/doxygen.png", "doc/Cupti/ftv2blank.png", "doc/Cupti/ftv2doc.png", "doc/Cupti/ftv2folderclosed.png", "doc/Cupti/ftv2folderopen.png", "doc/Cupti/ftv2lastnode.png", "doc/Cupti/ftv2link.png", "doc/Cupti/ftv2mlastnode.png", "doc/Cupti/ftv2mnode.png", "doc/Cupti/ftv2node.png", "doc/Cupti/ftv2plastnode.png", "doc/Cupti/ftv2pnode.png", "doc/Cupti/ftv2vertline.png", "doc/Cupti/functions.html", "doc/Cupti/functions_0x62.html", "doc/Cupti/functions_0x63.html", "doc/Cupti/functions_0x64.html", "doc/Cupti/functions_0x65.html", "doc/Cupti/functions_0x66.html", "doc/Cupti/functions_0x67.html", "doc/Cupti/functions_0x68.html", "doc/Cupti/functions_0x69.html", "doc/Cupti/functions_0x6a.html", "doc/Cupti/functions_0x6b.html", "doc/Cupti/functions_0x6c.html", "doc/Cupti/functions_0x6d.html", "doc/Cupti/functions_0x6e.html", "doc/Cupti/functions_0x6f.html", "doc/Cupti/functions_0x70.html", "doc/Cupti/functions_0x71.html", "doc/Cupti/functions_0x72.html", "doc/Cupti/functions_0x73.html", "doc/Cupti/functions_0x74.html", "doc/Cupti/functions_0x75.html", "doc/Cupti/functions_0x76.html", "doc/Cupti/functions_0x77.html", "doc/Cupti/functions_vars.html", "doc/Cupti/functions_vars_0x62.html", "doc/Cupti/functions_vars_0x63.html", "doc/Cupti/functions_vars_0x64.html", "doc/Cupti/functions_vars_0x65.html", "doc/Cupti/functions_vars_0x66.html", "doc/Cupti/functions_vars_0x67.html", "doc/Cupti/functions_vars_0x68.html", "doc/Cupti/functions_vars_0x69.html", "doc/Cupti/functions_vars_0x6a.html", "doc/Cupti/functions_vars_0x6b.html", "doc/Cupti/functions_vars_0x6c.html", "doc/Cupti/functions_vars_0x6d.html", "doc/Cupti/functions_vars_0x6e.html", "doc/Cupti/functions_vars_0x6f.html", "doc/Cupti/functions_vars_0x70.html", "doc/Cupti/functions_vars_0x71.html", "doc/Cupti/functions_vars_0x72.html", "doc/Cupti/functions_vars_0x73.html", "doc/Cupti/functions_vars_0x74.html", "doc/Cupti/functions_vars_0x75.html", "doc/Cupti/functions_vars_0x76.html", "doc/Cupti/functions_vars_0x77.html", "doc/Cupti/group__CUPTI__ACTIVITY__API.html", "doc/Cupti/group__CUPTI__CALLBACK__API.html", "doc/Cupti/group__CUPTI__CHECKPOINT__API.html", "doc/Cupti/group__CUPTI__EVENT__API.html", "doc/Cupti/group__CUPTI__METRIC__API.html", "doc/Cupti/group__CUPTI__PCSAMPLING__API.html", "doc/Cupti/group__CUPTI__PCSAMPLING__UTILITY.html", "doc/Cupti/group__CUPTI__PROFILER__API.html", "doc/Cupti/group__CUPTI__RESULT__API.html", "doc/Cupti/group__CUPTI__VERSION__API.html", "doc/Cupti/index.html", "doc/Cupti/modules.html", "doc/Cupti/notices-header.html", "doc/<PERSON>ti/r_library_support.html", "doc/<PERSON>ti/r_main.html", "doc/<PERSON>ti/r_overview.html", "doc/<PERSON><PERSON>/r_profiler.html", "doc/<PERSON>ti/r_special_configurations.html", "doc/<PERSON>ti/release_notes.html", "doc/Cupti/structBufferInfo.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html", "doc/Cupti/structCUpti__Activity.html", "doc/Cupti/structCUpti__ActivityAPI.html", "doc/Cupti/structCUpti__ActivityAutoBoostState.html", "doc/Cupti/structCUpti__ActivityBranch.html", "doc/Cupti/structCUpti__ActivityBranch2.html", "doc/Cupti/structCUpti__ActivityCdpKernel.html", "doc/Cupti/structCUpti__ActivityContext.html", "doc/Cupti/structCUpti__ActivityCudaEvent.html", "doc/Cupti/structCUpti__ActivityDevice.html", "doc/Cupti/structCUpti__ActivityDevice2.html", "doc/Cupti/structCUpti__ActivityDevice3.html", "doc/Cupti/structCUpti__ActivityDevice4.html", "doc/Cupti/structCUpti__ActivityDeviceAttribute.html", "doc/Cupti/structCUpti__ActivityEnvironment.html", "doc/Cupti/structCUpti__ActivityEvent.html", "doc/Cupti/structCUpti__ActivityEventInstance.html", "doc/Cupti/structCUpti__ActivityExternalCorrelation.html", "doc/Cupti/structCUpti__ActivityFunction.html", "doc/Cupti/structCUpti__ActivityGlobalAccess.html", "doc/Cupti/structCUpti__ActivityGlobalAccess2.html", "doc/Cupti/structCUpti__ActivityGlobalAccess3.html", "doc/Cupti/structCUpti__ActivityGraphTrace.html", "doc/Cupti/structCUpti__ActivityInstantaneousEvent.html", "doc/Cupti/structCUpti__ActivityInstantaneousEventInstance.html", "doc/Cupti/structCUpti__ActivityInstantaneousMetric.html", "doc/Cupti/structCUpti__ActivityInstantaneousMetricInstance.html", "doc/Cupti/structCUpti__ActivityInstructionCorrelation.html", "doc/Cupti/structCUpti__ActivityInstructionExecution.html", "doc/Cupti/structCUpti__ActivityJit.html", "doc/Cupti/structCUpti__ActivityKernel.html", "doc/Cupti/structCUpti__ActivityKernel2.html", "doc/Cupti/structCUpti__ActivityKernel3.html", "doc/Cupti/structCUpti__ActivityKernel4.html", "doc/Cupti/structCUpti__ActivityKernel5.html", "doc/Cupti/structCUpti__ActivityKernel6.html", "doc/Cupti/structCUpti__ActivityKernel7.html", "doc/Cupti/structCUpti__ActivityKernel8.html", "doc/Cupti/structCUpti__ActivityKernel9.html", "doc/Cupti/structCUpti__ActivityMarker.html", "doc/Cupti/structCUpti__ActivityMarker2.html", "doc/Cupti/structCUpti__ActivityMarkerData.html", "doc/Cupti/structCUpti__ActivityMemcpy.html", "doc/Cupti/structCUpti__ActivityMemcpy3.html", "doc/Cupti/structCUpti__ActivityMemcpy4.html", "doc/Cupti/structCUpti__ActivityMemcpy5.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP2.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP3.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP4.html", "doc/Cupti/structCUpti__ActivityMemory.html", "doc/Cupti/structCUpti__ActivityMemory2.html", "doc/Cupti/structCUpti__ActivityMemory3.html", "doc/Cupti/structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html", "doc/Cupti/structCUpti__ActivityMemoryPool.html", "doc/Cupti/structCUpti__ActivityMemoryPool2.html", "doc/Cupti/structCUpti__ActivityMemset.html", "doc/Cupti/structCUpti__ActivityMemset2.html", "doc/Cupti/structCUpti__ActivityMemset3.html", "doc/Cupti/structCUpti__ActivityMemset4.html", "doc/Cupti/structCUpti__ActivityMetric.html", "doc/Cupti/structCUpti__ActivityMetricInstance.html", "doc/Cupti/structCUpti__ActivityModule.html", "doc/Cupti/structCUpti__ActivityName.html", "doc/Cupti/structCUpti__ActivityNvLink.html", "doc/Cupti/structCUpti__ActivityNvLink2.html", "doc/Cupti/structCUpti__ActivityNvLink3.html", "doc/Cupti/structCUpti__ActivityNvLink4.html", "doc/Cupti/structCUpti__ActivityOpenAcc.html", "doc/Cupti/structCUpti__ActivityOpenAccData.html", "doc/Cupti/structCUpti__ActivityOpenAccLaunch.html", "doc/Cupti/structCUpti__ActivityOpenAccOther.html", "doc/Cupti/structCUpti__ActivityOpenMp.html", "doc/Cupti/structCUpti__ActivityOverhead.html", "doc/Cupti/structCUpti__ActivityPCSampling.html", "doc/Cupti/structCUpti__ActivityPCSampling2.html", "doc/Cupti/structCUpti__ActivityPCSampling3.html", "doc/Cupti/structCUpti__ActivityPCSamplingConfig.html", "doc/Cupti/structCUpti__ActivityPCSamplingRecordInfo.html", "doc/Cupti/structCUpti__ActivityPcie.html", "doc/Cupti/structCUpti__ActivityPreemption.html", "doc/Cupti/structCUpti__ActivitySharedAccess.html", "doc/Cupti/structCUpti__ActivitySourceLocator.html", "doc/Cupti/structCUpti__ActivityStream.html", "doc/Cupti/structCUpti__ActivitySynchronization.html", "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter.html", "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter2.html", "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounterConfig.html", "doc/<PERSON>ti/structCUpti__CallbackData.html", "doc/Cupti/structCUpti__EventGroupSet.html", "doc/Cupti/structCUpti__EventGroupSets.html", "doc/Cupti/structCUpti__GetCubinCrcParams.html", "doc/Cupti/structCUpti__GetSassToSourceCorrelationParams.html", "doc/Cupti/structCUpti__GraphData.html", "doc/Cupti/structCUpti__ModuleResourceData.html", "doc/Cupti/structCUpti__NvtxData.html", "doc/Cupti/structCUpti__PCSamplingConfigurationInfo.html", "doc/Cupti/structCUpti__PCSamplingConfigurationInfoParams.html", "doc/Cupti/structCUpti__PCSamplingData.html", "doc/Cupti/structCUpti__PCSamplingDisableParams.html", "doc/Cupti/structCUpti__PCSamplingEnableParams.html", "doc/Cupti/structCUpti__PCSamplingGetDataParams.html", "doc/Cupti/structCUpti__PCSamplingGetNumStallReasonsParams.html", "doc/Cupti/structCUpti__PCSamplingGetStallReasonsParams.html", "doc/Cupti/structCUpti__PCSamplingPCData.html", "doc/Cupti/structCUpti__PCSamplingStallReason.html", "doc/Cupti/structCUpti__PCSamplingStartParams.html", "doc/Cupti/structCUpti__PCSamplingStopParams.html", "doc/Cupti/structCUpti__Profiler__BeginPass__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__BeginSession__Params.html", "doc/Cupti/structCUpti__Profiler__CounterDataImageOptions.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__Initialize__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__DeInitialize__Params.html", "doc/Cupti/structCUpti__Profiler__DeviceSupported__Params.html", "doc/Cupti/structCUpti__Profiler__DisableProfiling__Params.html", "doc/Cupti/structCUpti__Profiler__EnableProfiling__Params.html", "doc/Cupti/structCUpti__Profiler__EndPass__Params.html", "doc/Cupti/structCUpti__Profiler__EndSession__Params.html", "doc/Cupti/structCUpti__Profiler__FlushCounterData__Params.html", "doc/Cupti/structCUpti__Profiler__GetCounterAvailability__Params.html", "doc/Cupti/structCUpti__Profiler__Initialize__Params.html", "doc/Cupti/structCUpti__Profiler__IsPassCollected__Params.html", "doc/Cupti/structCUpti__Profiler__SetConfig__Params.html", "doc/Cupti/structCUpti__Profiler__UnsetConfig__Params.html", "doc/Cupti/structCUpti__ResourceData.html", "doc/<PERSON>ti/structCUpti__SynchronizeData.html", "doc/Cupti/structHeader.html", "doc/Cupti/structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html", "doc/Cupti/structPcSamplingStallReasons.html", "doc/<PERSON>ti/tab_b.gif", "doc/<PERSON>ti/tab_l.gif", "doc/<PERSON>ti/tab_r.gif", "doc/Cupti/tabs.css", "doc/Cupti/unionCUpti__ActivityObjectKindId.html", "doc/Cupti/unionCUpti__MetricValue.html", "doc/common/formatting/bg-head.png", "doc/common/formatting/bg-horiz.png", "doc/common/formatting/bg-left.png", "doc/common/formatting/bg-right.png", "doc/common/formatting/bg-sidehead-glow.png", "doc/common/formatting/bg-sidehead.png", "doc/common/formatting/bg-vert.png", "doc/common/formatting/common.min.js", "doc/common/formatting/commonltr.css", "doc/common/formatting/cppapiref.css", "doc/common/formatting/cuda-toolkit-documentation.png", "doc/common/formatting/devtools-documentation.png", "doc/common/formatting/devzone.png", "doc/common/formatting/dita.style.css", "doc/common/formatting/html5shiv-printshiv.min.js", "doc/common/formatting/jquery.ba-hashchange.min.js", "doc/common/formatting/jquery.min.js", "doc/common/formatting/jquery.scrollintoview.min.js", "doc/common/formatting/magnify-dropdown.png", "doc/common/formatting/magnify.png", "doc/common/formatting/nvidia.png", "doc/common/formatting/prettify/lang-Splus.js", "doc/common/formatting/prettify/lang-aea.js", "doc/common/formatting/prettify/lang-agc.js", "doc/common/formatting/prettify/lang-apollo.js", "doc/common/formatting/prettify/lang-basic.js", "doc/common/formatting/prettify/lang-cbm.js", "doc/common/formatting/prettify/lang-cl.js", "doc/common/formatting/prettify/lang-clj.js", "doc/common/formatting/prettify/lang-css.js", "doc/common/formatting/prettify/lang-dart.js", "doc/common/formatting/prettify/lang-el.js", "doc/common/formatting/prettify/lang-erl.js", "doc/common/formatting/prettify/lang-erlang.js", "doc/common/formatting/prettify/lang-fs.js", "doc/common/formatting/prettify/lang-go.js", "doc/common/formatting/prettify/lang-hs.js", "doc/common/formatting/prettify/lang-lasso.js", "doc/common/formatting/prettify/lang-lassoscript.js", "doc/common/formatting/prettify/lang-latex.js", "doc/common/formatting/prettify/lang-lgt.js", "doc/common/formatting/prettify/lang-lisp.js", "doc/common/formatting/prettify/lang-ll.js", "doc/common/formatting/prettify/lang-llvm.js", "doc/common/formatting/prettify/lang-logtalk.js", "doc/common/formatting/prettify/lang-ls.js", "doc/common/formatting/prettify/lang-lsp.js", "doc/common/formatting/prettify/lang-lua.js", "doc/common/formatting/prettify/lang-matlab.js", "doc/common/formatting/prettify/lang-ml.js", "doc/common/formatting/prettify/lang-mumps.js", "doc/common/formatting/prettify/lang-n.js", "doc/common/formatting/prettify/lang-nemerle.js", "doc/common/formatting/prettify/lang-pascal.js", "doc/common/formatting/prettify/lang-proto.js", "doc/common/formatting/prettify/lang-r.js", "doc/common/formatting/prettify/lang-rd.js", "doc/common/formatting/prettify/lang-rkt.js", "doc/common/formatting/prettify/lang-rust.js", "doc/common/formatting/prettify/lang-s.js", "doc/common/formatting/prettify/lang-scala.js", "doc/common/formatting/prettify/lang-scm.js", "doc/common/formatting/prettify/lang-sql.js", "doc/common/formatting/prettify/lang-ss.js", "doc/common/formatting/prettify/lang-swift.js", "doc/common/formatting/prettify/lang-tcl.js", "doc/common/formatting/prettify/lang-tex.js", "doc/common/formatting/prettify/lang-vb.js", "doc/common/formatting/prettify/lang-vbs.js", "doc/common/formatting/prettify/lang-vhd.js", "doc/common/formatting/prettify/lang-vhdl.js", "doc/common/formatting/prettify/lang-wiki.js", "doc/common/formatting/prettify/lang-xq.js", "doc/common/formatting/prettify/lang-xquery.js", "doc/common/formatting/prettify/lang-yaml.js", "doc/common/formatting/prettify/lang-yml.js", "doc/common/formatting/prettify/onLoad.png", "doc/common/formatting/prettify/prettify.css", "doc/common/formatting/prettify/prettify.js", "doc/common/formatting/prettify/run_prettify.js", "doc/common/formatting/qwcode.highlight.css", "doc/common/formatting/search-clear.png", "doc/common/formatting/site.css", "doc/common/scripts/google-analytics/google-analytics-tracker.js", "doc/common/scripts/google-analytics/google-analytics-write.js", "doc/common/scripts/tynt/tynt.js", "doc/index.html", "doc/pdf/Cupti.pdf", "doc/search/check.html", "doc/search/files.js", "doc/search/htmlFileInfoList.js", "doc/search/htmlFileList.js", "doc/search/index-1.js", "doc/search/index-2.js", "doc/search/index-3.js", "doc/search/nwSearchFnt.min.js", "doc/search/stemmers/en_stemmer.min.js", "include/cuda_stdint.h", "include/cupti.h", "include/cupti_activity.h", "include/cupti_callbacks.h", "include/cupti_checkpoint.h", "include/cupti_driver_cbid.h", "include/cupti_events.h", "include/cupti_metrics.h", "include/cupti_nvtx_cbid.h", "include/cupti_pcsampling.h", "include/cupti_pcsampling_util.h", "include/cupti_profiler_target.h", "include/cupti_result.h", "include/cupti_runtime_cbid.h", "include/cupti_target.h", "include/cupti_version.h", "include/generated_cudaD3D10_meta.h", "include/generated_cudaD3D11_meta.h", "include/generated_cudaD3D9_meta.h", "include/generated_cudaGL_meta.h", "include/generated_cuda_d3d10_interop_meta.h", "include/generated_cuda_d3d11_interop_meta.h", "include/generated_cuda_d3d9_interop_meta.h", "include/generated_cuda_gl_interop_meta.h", "include/generated_cuda_meta.h", "include/generated_cuda_runtime_api_meta.h", "include/generated_cudart_removed_meta.h", "include/generated_nvtx_meta.h", "include/nvperf_common.h", "include/nvperf_cuda_host.h", "include/nvperf_host.h", "include/nvperf_target.h", "lib/checkpoint.dll", "lib/checkpoint.lib", "lib/cupti.lib", "lib/cupti64_2023.1.0.dll", "lib/nvperf_host.dll", "lib/nvperf_host.lib", "lib/nvperf_target.dll", "lib/nvperf_target.lib", "lib/pcsamplingutil.dll", "lib/pcsamplingutil.lib", "samples/activity_trace_async/Makefile", "samples/activity_trace_async/activity_trace_async.cu", "samples/autorange_profiling/Makefile", "samples/autorange_profiling/auto_range_profiling.cu", "samples/callback_event/Makefile", "samples/callback_event/callback_event.cu", "samples/callback_metric/Makefile", "samples/callback_metric/callback_metric.cu", "samples/callback_profiling/Makefile", "samples/callback_profiling/callback_profiling.cu", "samples/callback_timestamp/Makefile", "samples/callback_timestamp/callback_timestamp.cu", "samples/checkpoint_kernels/Makefile", "samples/checkpoint_kernels/checkpoint_kernels.cu", "samples/common/helper_cupti.h", "samples/common/helper_cupti_activity.h", "samples/concurrent_profiling/Makefile", "samples/concurrent_profiling/concurrent_profiling.cu", "samples/cuda_graphs_trace/Makefile", "samples/cuda_graphs_trace/cuda_graphs_trace.cu", "samples/cuda_memory_trace/Makefile", "samples/cuda_memory_trace/memory_trace.cu", "samples/cupti_correlation/Makefile", "samples/cupti_correlation/cupti_correlation.cu", "samples/cupti_external_correlation/Makefile", "samples/cupti_external_correlation/cupti_external_correlation.cu", "samples/cupti_metric_properties/Makefile", "samples/cupti_metric_properties/cupti_metric_properties.cpp", "samples/cupti_nvtx/Makefile", "samples/cupti_nvtx/cupti_nvtx.cu", "samples/cupti_query/Makefile", "samples/cupti_query/cupti_query.cpp", "samples/cupti_trace_injection/Makefile", "samples/cupti_trace_injection/README.txt", "samples/cupti_trace_injection/cupti_trace_injection.cpp", "samples/event_multi_gpu/Makefile", "samples/event_multi_gpu/event_multi_gpu.cu", "samples/event_sampling/Makefile", "samples/event_sampling/event_sampling.cu", "samples/extensions/include/c_util/FileOp.h", "samples/extensions/include/c_util/ScopeExit.h", "samples/extensions/include/profilerhost_util/Eval.h", "samples/extensions/include/profilerhost_util/List.h", "samples/extensions/include/profilerhost_util/Metric.h", "samples/extensions/include/profilerhost_util/Parser.h", "samples/extensions/include/profilerhost_util/Utils.h", "samples/extensions/src/profilerhost_util/Eval.cpp", "samples/extensions/src/profilerhost_util/List.cpp", "samples/extensions/src/profilerhost_util/Makefile", "samples/extensions/src/profilerhost_util/Metric.cpp", "samples/nested_range_profiling/Makefile", "samples/nested_range_profiling/nested_range_profiling.cu", "samples/nvlink_bandwidth/Makefile", "samples/nvlink_bandwidth/nvlink_bandwidth.cu", "samples/pc_sampling/Makefile", "samples/pc_sampling/pc_sampling.cu", "samples/pc_sampling_continuous/Makefile", "samples/pc_sampling_continuous/README.txt", "samples/pc_sampling_continuous/libpc_sampling_continuous.pl", "samples/pc_sampling_continuous/pc_sampling_continuous.cpp", "samples/pc_sampling_start_stop/Makefile", "samples/pc_sampling_start_stop/pc_sampling_start_stop.cu", "samples/pc_sampling_utility/Makefile", "samples/pc_sampling_utility/README.txt", "samples/pc_sampling_utility/pc_sampling_utility.cpp", "samples/pc_sampling_utility/pc_sampling_utility_helper.h", "samples/sass_source_map/Makefile", "samples/sass_source_map/sass_source_map.cu", "samples/unified_memory/Makefile", "samples/unified_memory/unified_memory.cu", "samples/userrange_profiling/Makefile", "samples/userrange_profiling/user_range_profiling.cu"], "fn": "cuda-cupti-12.1.62-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cupti-12.1.62-0", "type": 1}, "md5": "5a44835f2cfdf3a3010b4e8217071f19", "name": "cuda-cupti", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cupti-12.1.62-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "doc/Cupti/annotated.html", "path_type": "hardlink", "sha256": "74aab76564f905f6106ac603ad0d205aba839ef40f698e3d3ad976e602a0a18c", "sha256_in_prefix": "74aab76564f905f6106ac603ad0d205aba839ef40f698e3d3ad976e602a0a18c", "size_in_bytes": 2277254}, {"_path": "doc/Cupti/classes.html", "path_type": "hardlink", "sha256": "5c3312238080999fbe687ca2c5a5dc21b9e6873bac7e9164ef62dedb0e8ef897", "sha256_in_prefix": "5c3312238080999fbe687ca2c5a5dc21b9e6873bac7e9164ef62dedb0e8ef897", "size_in_bytes": 19355}, {"_path": "doc/Cupti/doxygen.css", "path_type": "hardlink", "sha256": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "sha256_in_prefix": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "size_in_bytes": 5701}, {"_path": "doc/Cupti/doxygen.png", "path_type": "hardlink", "sha256": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "sha256_in_prefix": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "size_in_bytes": 1281}, {"_path": "doc/Cupti/ftv2blank.png", "path_type": "hardlink", "sha256": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "sha256_in_prefix": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "size_in_bytes": 174}, {"_path": "doc/Cupti/ftv2doc.png", "path_type": "hardlink", "sha256": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "sha256_in_prefix": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "size_in_bytes": 255}, {"_path": "doc/Cupti/ftv2folderclosed.png", "path_type": "hardlink", "sha256": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "sha256_in_prefix": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "size_in_bytes": 259}, {"_path": "doc/Cupti/ftv2folderopen.png", "path_type": "hardlink", "sha256": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "sha256_in_prefix": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "size_in_bytes": 261}, {"_path": "doc/Cupti/ftv2lastnode.png", "path_type": "hardlink", "sha256": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "sha256_in_prefix": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "size_in_bytes": 233}, {"_path": "doc/Cupti/ftv2link.png", "path_type": "hardlink", "sha256": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "sha256_in_prefix": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "size_in_bytes": 358}, {"_path": "doc/Cupti/ftv2mlastnode.png", "path_type": "hardlink", "sha256": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "sha256_in_prefix": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "size_in_bytes": 160}, {"_path": "doc/Cupti/ftv2mnode.png", "path_type": "hardlink", "sha256": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "sha256_in_prefix": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "size_in_bytes": 194}, {"_path": "doc/Cupti/ftv2node.png", "path_type": "hardlink", "sha256": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "sha256_in_prefix": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "size_in_bytes": 235}, {"_path": "doc/Cupti/ftv2plastnode.png", "path_type": "hardlink", "sha256": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "sha256_in_prefix": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "size_in_bytes": 165}, {"_path": "doc/Cupti/ftv2pnode.png", "path_type": "hardlink", "sha256": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "sha256_in_prefix": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "size_in_bytes": 200}, {"_path": "doc/Cupti/ftv2vertline.png", "path_type": "hardlink", "sha256": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "sha256_in_prefix": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "size_in_bytes": 229}, {"_path": "doc/Cupti/functions.html", "path_type": "hardlink", "sha256": "d55b6f5740292d49d581ff94f0b9c8eeaad336494190c608128fc497989face2", "sha256_in_prefix": "d55b6f5740292d49d581ff94f0b9c8eeaad336494190c608128fc497989face2", "size_in_bytes": 448212}, {"_path": "doc/Cupti/functions_0x62.html", "path_type": "hardlink", "sha256": "061c91b6d236fb20f92427cb2d9005a37b9644c10c63615fc88f5ef60db78c88", "sha256_in_prefix": "061c91b6d236fb20f92427cb2d9005a37b9644c10c63615fc88f5ef60db78c88", "size_in_bytes": 10512}, {"_path": "doc/Cupti/functions_0x63.html", "path_type": "hardlink", "sha256": "ca1a3e0f2a1512f8a9c444092e5542a1e7134568c5b85f96f5a8d45f440d3b38", "sha256_in_prefix": "ca1a3e0f2a1512f8a9c444092e5542a1e7134568c5b85f96f5a8d45f440d3b38", "size_in_bytes": 32359}, {"_path": "doc/Cupti/functions_0x64.html", "path_type": "hardlink", "sha256": "58c2f1ce72cb4364b06f1bbeb8c567c93f86d9a7105f5b0413883eeaa8f2740f", "sha256_in_prefix": "58c2f1ce72cb4364b06f1bbeb8c567c93f86d9a7105f5b0413883eeaa8f2740f", "size_in_bytes": 14291}, {"_path": "doc/Cupti/functions_0x65.html", "path_type": "hardlink", "sha256": "eefbb3d6fa2dc582abaa6612b35a636dbe03beaa1055649a38e8ca1ee1843b55", "sha256_in_prefix": "eefbb3d6fa2dc582abaa6612b35a636dbe03beaa1055649a38e8ca1ee1843b55", "size_in_bytes": 12307}, {"_path": "doc/Cupti/functions_0x66.html", "path_type": "hardlink", "sha256": "f605f5889980a2d2c07480443ae53963fd9bd4f913b5ad41a52b746d1ab25bf0", "sha256_in_prefix": "f605f5889980a2d2c07480443ae53963fd9bd4f913b5ad41a52b746d1ab25bf0", "size_in_bytes": 12802}, {"_path": "doc/Cupti/functions_0x67.html", "path_type": "hardlink", "sha256": "d4fbe6986ccd6dc423ccf9e2e4674f75d61c50a41d0452705631471e23aedd2b", "sha256_in_prefix": "d4fbe6986ccd6dc423ccf9e2e4674f75d61c50a41d0452705631471e23aedd2b", "size_in_bytes": 12534}, {"_path": "doc/Cupti/functions_0x68.html", "path_type": "hardlink", "sha256": "fb66599adcc6f04481e3c5874c5b0868c052c170c7742ac777875e3af1680f59", "sha256_in_prefix": "fb66599adcc6f04481e3c5874c5b0868c052c170c7742ac777875e3af1680f59", "size_in_bytes": 3904}, {"_path": "doc/Cupti/functions_0x69.html", "path_type": "hardlink", "sha256": "04633a32696c8cca9154ed4149ee98b13c07fa6ffa4a21bc3b8ff9748727d1c2", "sha256_in_prefix": "04633a32696c8cca9154ed4149ee98b13c07fa6ffa4a21bc3b8ff9748727d1c2", "size_in_bytes": 9693}, {"_path": "doc/Cupti/functions_0x6a.html", "path_type": "hardlink", "sha256": "bc678e175613e0d31eab460e13b03298d891a5b7e1a1276d4ed18994a58ffea7", "sha256_in_prefix": "bc678e175613e0d31eab460e13b03298d891a5b7e1a1276d4ed18994a58ffea7", "size_in_bytes": 3659}, {"_path": "doc/Cupti/functions_0x6b.html", "path_type": "hardlink", "sha256": "35d900ca92b0fd159819a2f0a7c114047c610c4a22d0538df8b3e01706217f6a", "sha256_in_prefix": "35d900ca92b0fd159819a2f0a7c114047c610c4a22d0538df8b3e01706217f6a", "size_in_bytes": 13663}, {"_path": "doc/Cupti/functions_0x6c.html", "path_type": "hardlink", "sha256": "dd1aa0399db180ad3944f06a15f00b98d8c06c0a438dfff39fbd7d2e8685c82f", "sha256_in_prefix": "dd1aa0399db180ad3944f06a15f00b98d8c06c0a438dfff39fbd7d2e8685c82f", "size_in_bytes": 8672}, {"_path": "doc/Cupti/functions_0x6d.html", "path_type": "hardlink", "sha256": "d33f369d16a396f24d6904a3fe4e270752479171077a0821e3b710833d8db2a5", "sha256_in_prefix": "d33f369d16a396f24d6904a3fe4e270752479171077a0821e3b710833d8db2a5", "size_in_bytes": 14280}, {"_path": "doc/Cupti/functions_0x6e.html", "path_type": "hardlink", "sha256": "e0470bf85ce6c9cbee1c81ad592900ac86207f28c8f04bdb8bd952d726246e82", "sha256_in_prefix": "e0470bf85ce6c9cbee1c81ad592900ac86207f28c8f04bdb8bd952d726246e82", "size_in_bytes": 11518}, {"_path": "doc/Cupti/functions_0x6f.html", "path_type": "hardlink", "sha256": "e1df971944391b7c616683005211f876505643e552794db637c9d9852357c920", "sha256_in_prefix": "e1df971944391b7c616683005211f876505643e552794db637c9d9852357c920", "size_in_bytes": 5101}, {"_path": "doc/Cupti/functions_0x70.html", "path_type": "hardlink", "sha256": "c8170056ec572d7cfb7b9bc8d4024075396440f80f48d65de88c16cbf6dd6334", "sha256_in_prefix": "c8170056ec572d7cfb7b9bc8d4024075396440f80f48d65de88c16cbf6dd6334", "size_in_bytes": 27231}, {"_path": "doc/Cupti/functions_0x71.html", "path_type": "hardlink", "sha256": "cdead12f6ac0daef709bdc6b1c034a2b734c8606d31166b5e7e92305dbc1fc39", "sha256_in_prefix": "cdead12f6ac0daef709bdc6b1c034a2b734c8606d31166b5e7e92305dbc1fc39", "size_in_bytes": 4094}, {"_path": "doc/Cupti/functions_0x72.html", "path_type": "hardlink", "sha256": "115ff6ec37feb1dc58daf46ae94684789d9b56e761f2625154f78f2e56a143ba", "sha256_in_prefix": "115ff6ec37feb1dc58daf46ae94684789d9b56e761f2625154f78f2e56a143ba", "size_in_bytes": 10856}, {"_path": "doc/Cupti/functions_0x73.html", "path_type": "hardlink", "sha256": "9a3277fcdf72b05037c9e98873b9e0de60a03e4afed93e034585b9619a5e783b", "sha256_in_prefix": "9a3277fcdf72b05037c9e98873b9e0de60a03e4afed93e034585b9619a5e783b", "size_in_bytes": 30796}, {"_path": "doc/Cupti/functions_0x74.html", "path_type": "hardlink", "sha256": "f6e3c9e2706c3e45fde9c1c3366ea3accfdd18e4d464743dab4c2ba6c6ec5a5c", "sha256_in_prefix": "f6e3c9e2706c3e45fde9c1c3366ea3accfdd18e4d464743dab4c2ba6c6ec5a5c", "size_in_bytes": 9047}, {"_path": "doc/Cupti/functions_0x75.html", "path_type": "hardlink", "sha256": "fede368a24fcbea86a48f9ddc9fe4abbffbb2525b15eeec6402071589dbf7084", "sha256_in_prefix": "fede368a24fcbea86a48f9ddc9fe4abbffbb2525b15eeec6402071589dbf7084", "size_in_bytes": 4191}, {"_path": "doc/Cupti/functions_0x76.html", "path_type": "hardlink", "sha256": "73572e0401c502687595471c0f958c66c41817bdef071ac3b108735a32889160", "sha256_in_prefix": "73572e0401c502687595471c0f958c66c41817bdef071ac3b108735a32889160", "size_in_bytes": 6237}, {"_path": "doc/Cupti/functions_0x77.html", "path_type": "hardlink", "sha256": "c129d5cfe533f50d54d124ae56f1d9b12cad4fc5f2fffcb54d3adf22b8e46cff", "sha256_in_prefix": "c129d5cfe533f50d54d124ae56f1d9b12cad4fc5f2fffcb54d3adf22b8e46cff", "size_in_bytes": 3603}, {"_path": "doc/Cupti/functions_vars.html", "path_type": "hardlink", "sha256": "0400389576c9c7a89f15fe300fc36297d574bb3d3ddec023352d7083bdc8e54b", "sha256_in_prefix": "0400389576c9c7a89f15fe300fc36297d574bb3d3ddec023352d7083bdc8e54b", "size_in_bytes": 6195}, {"_path": "doc/Cupti/functions_vars_0x62.html", "path_type": "hardlink", "sha256": "aef2ff6238dbe8ac5b257d367ed1d4b85ab46175836bd056787b275dc3be9802", "sha256_in_prefix": "aef2ff6238dbe8ac5b257d367ed1d4b85ab46175836bd056787b275dc3be9802", "size_in_bytes": 10528}, {"_path": "doc/Cupti/functions_vars_0x63.html", "path_type": "hardlink", "sha256": "575404aa7a7b888ed5195f496075516f9253882835ab8a131bb591a130d0b160", "sha256_in_prefix": "575404aa7a7b888ed5195f496075516f9253882835ab8a131bb591a130d0b160", "size_in_bytes": 32375}, {"_path": "doc/Cupti/functions_vars_0x64.html", "path_type": "hardlink", "sha256": "48ac495b8b27eb01a203f27a00fe32a89997dd517bcac9412fc52085b95a309a", "sha256_in_prefix": "48ac495b8b27eb01a203f27a00fe32a89997dd517bcac9412fc52085b95a309a", "size_in_bytes": 14307}, {"_path": "doc/Cupti/functions_vars_0x65.html", "path_type": "hardlink", "sha256": "5a2bfc0bfe66b8c2b64f7eef14779b3cf6c3b6833f797fe70c196fbb8d54a8e4", "sha256_in_prefix": "5a2bfc0bfe66b8c2b64f7eef14779b3cf6c3b6833f797fe70c196fbb8d54a8e4", "size_in_bytes": 12323}, {"_path": "doc/Cupti/functions_vars_0x66.html", "path_type": "hardlink", "sha256": "219b86b520e1de45cf4fbc6546640cac5f54f7ad94976e83a2e36957561469f6", "sha256_in_prefix": "219b86b520e1de45cf4fbc6546640cac5f54f7ad94976e83a2e36957561469f6", "size_in_bytes": 12818}, {"_path": "doc/Cupti/functions_vars_0x67.html", "path_type": "hardlink", "sha256": "404b1820e807b16c6e8291e52db61e80b15c6517833f22bc2a7b5f2316b26c17", "sha256_in_prefix": "404b1820e807b16c6e8291e52db61e80b15c6517833f22bc2a7b5f2316b26c17", "size_in_bytes": 12550}, {"_path": "doc/Cupti/functions_vars_0x68.html", "path_type": "hardlink", "sha256": "c93649e20fbe8e17dbee2e73234215c2a70895cfbe47946061952bbd5b28d2d8", "sha256_in_prefix": "c93649e20fbe8e17dbee2e73234215c2a70895cfbe47946061952bbd5b28d2d8", "size_in_bytes": 3920}, {"_path": "doc/Cupti/functions_vars_0x69.html", "path_type": "hardlink", "sha256": "d4866e18bb1bb6124139e3d5aff424ca4924976e1123deca8185cfabf198eba9", "sha256_in_prefix": "d4866e18bb1bb6124139e3d5aff424ca4924976e1123deca8185cfabf198eba9", "size_in_bytes": 9709}, {"_path": "doc/Cupti/functions_vars_0x6a.html", "path_type": "hardlink", "sha256": "260fe4d412cc7c74431030b48c412fb90246b4d97a5dcbcde739d901471f134d", "sha256_in_prefix": "260fe4d412cc7c74431030b48c412fb90246b4d97a5dcbcde739d901471f134d", "size_in_bytes": 3675}, {"_path": "doc/Cupti/functions_vars_0x6b.html", "path_type": "hardlink", "sha256": "42cf99aab5d1efdd966a0e621c42e04f0f0a4c020d1e145353cdc3030f42e4c7", "sha256_in_prefix": "42cf99aab5d1efdd966a0e621c42e04f0f0a4c020d1e145353cdc3030f42e4c7", "size_in_bytes": 13679}, {"_path": "doc/Cupti/functions_vars_0x6c.html", "path_type": "hardlink", "sha256": "9a20f49384af1e9bee1e7d045bb8e282f5f9689d30e4d8a4ec07a8abc9d1edc0", "sha256_in_prefix": "9a20f49384af1e9bee1e7d045bb8e282f5f9689d30e4d8a4ec07a8abc9d1edc0", "size_in_bytes": 8688}, {"_path": "doc/Cupti/functions_vars_0x6d.html", "path_type": "hardlink", "sha256": "792e9494686d73a6ba327857912e0617e0397f82ef3275f83bf583b932e02280", "sha256_in_prefix": "792e9494686d73a6ba327857912e0617e0397f82ef3275f83bf583b932e02280", "size_in_bytes": 14296}, {"_path": "doc/Cupti/functions_vars_0x6e.html", "path_type": "hardlink", "sha256": "314338588f7c54a90baf485fd3b14bee8ef3d079e33a9e595ac32b0d38a230f0", "sha256_in_prefix": "314338588f7c54a90baf485fd3b14bee8ef3d079e33a9e595ac32b0d38a230f0", "size_in_bytes": 11534}, {"_path": "doc/Cupti/functions_vars_0x6f.html", "path_type": "hardlink", "sha256": "2ce99fd988fcf1eaeb40f8b62f9e6f4a9b9b14c4309289dbb87981fa069ef034", "sha256_in_prefix": "2ce99fd988fcf1eaeb40f8b62f9e6f4a9b9b14c4309289dbb87981fa069ef034", "size_in_bytes": 5117}, {"_path": "doc/Cupti/functions_vars_0x70.html", "path_type": "hardlink", "sha256": "4b6d9bfe4045c9a243ecdd582ac492bf2d3632b6f51c255b5b1898e577d4eaaf", "sha256_in_prefix": "4b6d9bfe4045c9a243ecdd582ac492bf2d3632b6f51c255b5b1898e577d4eaaf", "size_in_bytes": 27247}, {"_path": "doc/Cupti/functions_vars_0x71.html", "path_type": "hardlink", "sha256": "6d2460c5208dd7bac9941060d9c7b70e14b50eb653276ab086570ffd2e5a841d", "sha256_in_prefix": "6d2460c5208dd7bac9941060d9c7b70e14b50eb653276ab086570ffd2e5a841d", "size_in_bytes": 4110}, {"_path": "doc/Cupti/functions_vars_0x72.html", "path_type": "hardlink", "sha256": "b74b230ccf4f1eeb482c240f8bcf7a440d28a89cc60e7fab3842c07fa1292ee8", "sha256_in_prefix": "b74b230ccf4f1eeb482c240f8bcf7a440d28a89cc60e7fab3842c07fa1292ee8", "size_in_bytes": 10872}, {"_path": "doc/Cupti/functions_vars_0x73.html", "path_type": "hardlink", "sha256": "3aaeaef96bcc591ea0deac1bf4ad4a1277f82d68e3f8cd2e91a603b5e078dbbd", "sha256_in_prefix": "3aaeaef96bcc591ea0deac1bf4ad4a1277f82d68e3f8cd2e91a603b5e078dbbd", "size_in_bytes": 30812}, {"_path": "doc/Cupti/functions_vars_0x74.html", "path_type": "hardlink", "sha256": "0620ad38c346095b4714225b634725bf7ac93984daeb349982e1eefef3c5ab13", "sha256_in_prefix": "0620ad38c346095b4714225b634725bf7ac93984daeb349982e1eefef3c5ab13", "size_in_bytes": 9063}, {"_path": "doc/Cupti/functions_vars_0x75.html", "path_type": "hardlink", "sha256": "4b47db4b1a0cdfe0fff289292016f043cae19c696edb392370def43cde0ee837", "sha256_in_prefix": "4b47db4b1a0cdfe0fff289292016f043cae19c696edb392370def43cde0ee837", "size_in_bytes": 4207}, {"_path": "doc/Cupti/functions_vars_0x76.html", "path_type": "hardlink", "sha256": "d543fe8d406f5e106624d779b0295cc2e2070a4feafe4aa9c2f6ae22ea0ad38e", "sha256_in_prefix": "d543fe8d406f5e106624d779b0295cc2e2070a4feafe4aa9c2f6ae22ea0ad38e", "size_in_bytes": 6253}, {"_path": "doc/Cupti/functions_vars_0x77.html", "path_type": "hardlink", "sha256": "99efcfed265701fc8f7d468cc76f1427da5c6e029592dbced36c28a6e4e1d109", "sha256_in_prefix": "99efcfed265701fc8f7d468cc76f1427da5c6e029592dbced36c28a6e4e1d109", "size_in_bytes": 3619}, {"_path": "doc/Cupti/group__CUPTI__ACTIVITY__API.html", "path_type": "hardlink", "sha256": "b71212aaf0330af8ce4df8219cc7b44099b1079c55131e758d50317e2267a5f6", "sha256_in_prefix": "b71212aaf0330af8ce4df8219cc7b44099b1079c55131e758d50317e2267a5f6", "size_in_bytes": 390653}, {"_path": "doc/Cupti/group__CUPTI__CALLBACK__API.html", "path_type": "hardlink", "sha256": "d30b1033918b19abf90339ed06642a22f5efdf0b676ab4c28b5218edcf229e24", "sha256_in_prefix": "d30b1033918b19abf90339ed06642a22f5efdf0b676ab4c28b5218edcf229e24", "size_in_bytes": 61262}, {"_path": "doc/Cupti/group__CUPTI__CHECKPOINT__API.html", "path_type": "hardlink", "sha256": "5c0ca852bc5a13f559ca674836899288ce32e966b6a15bfe1b642cf33642e955", "sha256_in_prefix": "5c0ca852bc5a13f559ca674836899288ce32e966b6a15bfe1b642cf33642e955", "size_in_bytes": 12806}, {"_path": "doc/Cupti/group__CUPTI__EVENT__API.html", "path_type": "hardlink", "sha256": "dbf1cd9aafbcd8cee5ec7badb024202cd52eb9d628e71acb9b53bbe43c3f104b", "sha256_in_prefix": "dbf1cd9aafbcd8cee5ec7badb024202cd52eb9d628e71acb9b53bbe43c3f104b", "size_in_bytes": 168728}, {"_path": "doc/Cupti/group__CUPTI__METRIC__API.html", "path_type": "hardlink", "sha256": "5c2431061dc16f4c07a47fb2202b6b23e54a0ee25d9afff90f7afa3235b885b2", "sha256_in_prefix": "5c2431061dc16f4c07a47fb2202b6b23e54a0ee25d9afff90f7afa3235b885b2", "size_in_bytes": 81261}, {"_path": "doc/Cupti/group__CUPTI__PCSAMPLING__API.html", "path_type": "hardlink", "sha256": "604d056148bf7c950155ac05882136f1ca1c6f285c5a003fbef402d55f24f782", "sha256_in_prefix": "604d056148bf7c950155ac05882136f1ca1c6f285c5a003fbef402d55f24f782", "size_in_bytes": 56315}, {"_path": "doc/Cupti/group__CUPTI__PCSAMPLING__UTILITY.html", "path_type": "hardlink", "sha256": "9cb325c4021e4c95e513e766f6ec9103730a5cec76946481d9d038d4f0e0406f", "sha256_in_prefix": "9cb325c4021e4c95e513e766f6ec9103730a5cec76946481d9d038d4f0e0406f", "size_in_bytes": 25548}, {"_path": "doc/Cupti/group__CUPTI__PROFILER__API.html", "path_type": "hardlink", "sha256": "91bc5c1303628d633f2fad873578237237b2742a431c6868339d51ee15640e43", "sha256_in_prefix": "91bc5c1303628d633f2fad873578237237b2742a431c6868339d51ee15640e43", "size_in_bytes": 49958}, {"_path": "doc/Cupti/group__CUPTI__RESULT__API.html", "path_type": "hardlink", "sha256": "bd5fa2a5ea4eb38c7da041f8a696d22ad6629636e18e14e33e111a7bd39da2ce", "sha256_in_prefix": "bd5fa2a5ea4eb38c7da041f8a696d22ad6629636e18e14e33e111a7bd39da2ce", "size_in_bytes": 31706}, {"_path": "doc/Cupti/group__CUPTI__VERSION__API.html", "path_type": "hardlink", "sha256": "b090f4da1142c78c4adcd9c5e53f2a3edc1aa08823d89941815fb881d0383073", "sha256_in_prefix": "b090f4da1142c78c4adcd9c5e53f2a3edc1aa08823d89941815fb881d0383073", "size_in_bytes": 5073}, {"_path": "doc/Cupti/index.html", "path_type": "hardlink", "sha256": "a91b5beabf2b13dc063cd2e5b20377ad1e125283c37101d389cbcc30f72e67bd", "sha256_in_prefix": "a91b5beabf2b13dc063cd2e5b20377ad1e125283c37101d389cbcc30f72e67bd", "size_in_bytes": 103530}, {"_path": "doc/Cupti/modules.html", "path_type": "hardlink", "sha256": "88741870b2fe7fb006be570145430e148aff1a921427325da2af0fc990a76b50", "sha256_in_prefix": "88741870b2fe7fb006be570145430e148aff1a921427325da2af0fc990a76b50", "size_in_bytes": 1077682}, {"_path": "doc/Cupti/notices-header.html", "path_type": "hardlink", "sha256": "7099b31112b71780573bf83b0a585672410479de823e939130c88ef3ef0c6541", "sha256_in_prefix": "7099b31112b71780573bf83b0a585672410479de823e939130c88ef3ef0c6541", "size_in_bytes": 62975}, {"_path": "doc/<PERSON>ti/r_library_support.html", "path_type": "hardlink", "sha256": "361b1e00f7b19869220b3e72bf42e8bf09de157d6f6fa5eed7eeea434f7b4521", "sha256_in_prefix": "361b1e00f7b19869220b3e72bf42e8bf09de157d6f6fa5eed7eeea434f7b4521", "size_in_bytes": 63445}, {"_path": "doc/<PERSON>ti/r_main.html", "path_type": "hardlink", "sha256": "560e87e2c7906643d360c44a83fbd7146531ca3fceba25a21dd467cb1fba191c", "sha256_in_prefix": "560e87e2c7906643d360c44a83fbd7146531ca3fceba25a21dd467cb1fba191c", "size_in_bytes": 840235}, {"_path": "doc/<PERSON>ti/r_overview.html", "path_type": "hardlink", "sha256": "fa301d9d89f6cf95e655899cfea238a7208c8069f0fd6d610518548b09d54db5", "sha256_in_prefix": "fa301d9d89f6cf95e655899cfea238a7208c8069f0fd6d610518548b09d54db5", "size_in_bytes": 67783}, {"_path": "doc/<PERSON><PERSON>/r_profiler.html", "path_type": "hardlink", "sha256": "d5e78b8aedd4131794117c746163c81c03c29952770695f232705d6c53ce3c0a", "sha256_in_prefix": "d5e78b8aedd4131794117c746163c81c03c29952770695f232705d6c53ce3c0a", "size_in_bytes": 62425}, {"_path": "doc/<PERSON>ti/r_special_configurations.html", "path_type": "hardlink", "sha256": "a47f8536904dad5e4da6896e9e0fe96434a95c61db6f0d952125a56238213244", "sha256_in_prefix": "a47f8536904dad5e4da6896e9e0fe96434a95c61db6f0d952125a56238213244", "size_in_bytes": 68853}, {"_path": "doc/<PERSON>ti/release_notes.html", "path_type": "hardlink", "sha256": "b71b8e4cbf4e646a8a007323842fbd949778c8c964db9e80cd92096554fad380", "sha256_in_prefix": "b71b8e4cbf4e646a8a007323842fbd949778c8c964db9e80cd92096554fad380", "size_in_bytes": 209177}, {"_path": "doc/Cupti/structBufferInfo.html", "path_type": "hardlink", "sha256": "fa120a4244cefaee09073d47a8ac68247bcd18ef5967aac8706ee187284135a3", "sha256_in_prefix": "fa120a4244cefaee09073d47a8ac68247bcd18ef5967aac8706ee187284135a3", "size_in_bytes": 4849}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html", "path_type": "hardlink", "sha256": "77ce95e5e7c6455d408a55d46b5046b89c22e319c4458a6757e7b36c16797377", "sha256_in_prefix": "77ce95e5e7c6455d408a55d46b5046b89c22e319c4458a6757e7b36c16797377", "size_in_bytes": 4910}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html", "path_type": "hardlink", "sha256": "8b9dad43b7718633cd4012f2d970c63bb9e0cd64ce11790b41aaa7be10efb187", "sha256_in_prefix": "8b9dad43b7718633cd4012f2d970c63bb9e0cd64ce11790b41aaa7be10efb187", "size_in_bytes": 4969}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html", "path_type": "hardlink", "sha256": "9fc7c9df824ba003328e01fe7e6276db476f93b78a4ebf8f808eb66a32866a9e", "sha256_in_prefix": "9fc7c9df824ba003328e01fe7e6276db476f93b78a4ebf8f808eb66a32866a9e", "size_in_bytes": 10567}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html", "path_type": "hardlink", "sha256": "6af847efb520b6a9cac7eb9e0e90a7fe70bf7368abbade703b74d6deb1498545", "sha256_in_prefix": "6af847efb520b6a9cac7eb9e0e90a7fe70bf7368abbade703b74d6deb1498545", "size_in_bytes": 7099}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html", "path_type": "hardlink", "sha256": "108a6edaf7b98d1dd83cb0f75a7b5c55de7d8f09570db6ec4ad52d330cac8312", "sha256_in_prefix": "108a6edaf7b98d1dd83cb0f75a7b5c55de7d8f09570db6ec4ad52d330cac8312", "size_in_bytes": 9414}, {"_path": "doc/Cupti/structCUpti__Activity.html", "path_type": "hardlink", "sha256": "1590aa8a15f3d8c52cacfbd4cd82655dea36f3ac3b9ef4ef370faf6ac3a09321", "sha256_in_prefix": "1590aa8a15f3d8c52cacfbd4cd82655dea36f3ac3b9ef4ef370faf6ac3a09321", "size_in_bytes": 3361}, {"_path": "doc/Cupti/structCUpti__ActivityAPI.html", "path_type": "hardlink", "sha256": "04631a05208e856d10028a43bc3583807aa18a3ab66436299e0ae36739a22970", "sha256_in_prefix": "04631a05208e856d10028a43bc3583807aa18a3ab66436299e0ae36739a22970", "size_in_bytes": 9143}, {"_path": "doc/Cupti/structCUpti__ActivityAutoBoostState.html", "path_type": "hardlink", "sha256": "1b6f49b612603ebe755ba352b8b24855d660c59ee6fb09f5214271821223d5cf", "sha256_in_prefix": "1b6f49b612603ebe755ba352b8b24855d660c59ee6fb09f5214271821223d5cf", "size_in_bytes": 3709}, {"_path": "doc/Cupti/structCUpti__ActivityBranch.html", "path_type": "hardlink", "sha256": "bac2a807db2e5ef84fbf47691d6e06421c179312d8514a512290d875297fbb77", "sha256_in_prefix": "bac2a807db2e5ef84fbf47691d6e06421c179312d8514a512290d875297fbb77", "size_in_bytes": 7917}, {"_path": "doc/Cupti/structCUpti__ActivityBranch2.html", "path_type": "hardlink", "sha256": "23b164ddd3bd22a9ca4b2a82e5dd66d21e0bf269f5305307676bf68c8d1afb36", "sha256_in_prefix": "23b164ddd3bd22a9ca4b2a82e5dd66d21e0bf269f5305307676bf68c8d1afb36", "size_in_bytes": 9304}, {"_path": "doc/Cupti/structCUpti__ActivityCdpKernel.html", "path_type": "hardlink", "sha256": "94f488a69641f2f31df49959a53ff44407e4cfb8bd86a855552eae02193f458e", "sha256_in_prefix": "94f488a69641f2f31df49959a53ff44407e4cfb8bd86a855552eae02193f458e", "size_in_bytes": 27250}, {"_path": "doc/Cupti/structCUpti__ActivityContext.html", "path_type": "hardlink", "sha256": "daa88c24c21d19752a8ad3ab30737c09e6c170ed915ddea58976825ae627c6cf", "sha256_in_prefix": "daa88c24c21d19752a8ad3ab30737c09e6c170ed915ddea58976825ae627c6cf", "size_in_bytes": 6106}, {"_path": "doc/Cupti/structCUpti__ActivityCudaEvent.html", "path_type": "hardlink", "sha256": "978aa1a0cd7da7b195a60f66570d3d89c0bf6bf99dfa26640b95b8aba8e33be9", "sha256_in_prefix": "978aa1a0cd7da7b195a60f66570d3d89c0bf6bf99dfa26640b95b8aba8e33be9", "size_in_bytes": 6807}, {"_path": "doc/Cupti/structCUpti__ActivityDevice.html", "path_type": "hardlink", "sha256": "a80d457365781951b166072e8d64dc83c2f4998de8fe80a12c73e03a990e0b9c", "sha256_in_prefix": "a80d457365781951b166072e8d64dc83c2f4998de8fe80a12c73e03a990e0b9c", "size_in_bytes": 23881}, {"_path": "doc/Cupti/structCUpti__ActivityDevice2.html", "path_type": "hardlink", "sha256": "eb6eec0c79884568697c8758b7ea9c44712dc41bae2cb97477b3f3618d162201", "sha256_in_prefix": "eb6eec0c79884568697c8758b7ea9c44712dc41bae2cb97477b3f3618d162201", "size_in_bytes": 28141}, {"_path": "doc/Cupti/structCUpti__ActivityDevice3.html", "path_type": "hardlink", "sha256": "867bad848ffe0b90f964fe08875ea2a92eb915964bb6926fcd98f027e7277edf", "sha256_in_prefix": "867bad848ffe0b90f964fe08875ea2a92eb915964bb6926fcd98f027e7277edf", "size_in_bytes": 29039}, {"_path": "doc/Cupti/structCUpti__ActivityDevice4.html", "path_type": "hardlink", "sha256": "1331c1f8b222d3e409528b59297ac6c5666edd3f563d11929292d5040e784ea7", "sha256_in_prefix": "1331c1f8b222d3e409528b59297ac6c5666edd3f563d11929292d5040e784ea7", "size_in_bytes": 32187}, {"_path": "doc/Cupti/structCUpti__ActivityDeviceAttribute.html", "path_type": "hardlink", "sha256": "546744e3b1145d1c2dd589fe05b7b4b39fab65e243df8fa431f91f18bf186e6b", "sha256_in_prefix": "546744e3b1145d1c2dd589fe05b7b4b39fab65e243df8fa431f91f18bf186e6b", "size_in_bytes": 7055}, {"_path": "doc/Cupti/structCUpti__ActivityEnvironment.html", "path_type": "hardlink", "sha256": "20a77cce63313d4a27ef6bb6bf7e75956dbeb6c48d8145808b0d8f9f3dc541b0", "sha256_in_prefix": "20a77cce63313d4a27ef6bb6bf7e75956dbeb6c48d8145808b0d8f9f3dc541b0", "size_in_bytes": 17996}, {"_path": "doc/Cupti/structCUpti__ActivityEvent.html", "path_type": "hardlink", "sha256": "45bd99fef6a80ac2ac6e8efc9ab71ea0c33f80b1d1636187d43502ea5ddcd727", "sha256_in_prefix": "45bd99fef6a80ac2ac6e8efc9ab71ea0c33f80b1d1636187d43502ea5ddcd727", "size_in_bytes": 6536}, {"_path": "doc/Cupti/structCUpti__ActivityEventInstance.html", "path_type": "hardlink", "sha256": "35180dcf7101a48e9c308ea03758a2f445d1bee47a07d6384a791c5d95ef1e37", "sha256_in_prefix": "35180dcf7101a48e9c308ea03758a2f445d1bee47a07d6384a791c5d95ef1e37", "size_in_bytes": 8513}, {"_path": "doc/Cupti/structCUpti__ActivityExternalCorrelation.html", "path_type": "hardlink", "sha256": "728ce04eb7581cdaa6868ec3a122a3b54866b59c405be999a896365247735a02", "sha256_in_prefix": "728ce04eb7581cdaa6868ec3a122a3b54866b59c405be999a896365247735a02", "size_in_bytes": 7090}, {"_path": "doc/Cupti/structCUpti__ActivityFunction.html", "path_type": "hardlink", "sha256": "bc35bf7c574dcada93ad393bfb7e608db55aecac4929dbeccf588abce1f8f77c", "sha256_in_prefix": "bc35bf7c574dcada93ad393bfb7e608db55aecac4929dbeccf588abce1f8f77c", "size_in_bytes": 6894}, {"_path": "doc/Cupti/structCUpti__ActivityGlobalAccess.html", "path_type": "hardlink", "sha256": "055cefdfbb8d1932b8a6a34b2449ebae7d5846372fb6ee88fd0fad1cd89f120b", "sha256_in_prefix": "055cefdfbb8d1932b8a6a34b2449ebae7d5846372fb6ee88fd0fad1cd89f120b", "size_in_bytes": 9322}, {"_path": "doc/Cupti/structCUpti__ActivityGlobalAccess2.html", "path_type": "hardlink", "sha256": "c46749c6168b36dcb675ee56efeb4a5daed8b43c435a05122e48826537fb23e3", "sha256_in_prefix": "c46749c6168b36dcb675ee56efeb4a5daed8b43c435a05122e48826537fb23e3", "size_in_bytes": 11889}, {"_path": "doc/Cupti/structCUpti__ActivityGlobalAccess3.html", "path_type": "hardlink", "sha256": "5acfa5894095b409769ce1de6f0ef11384a13f620908839d7d0b1dc53b49a13f", "sha256_in_prefix": "5acfa5894095b409769ce1de6f0ef11384a13f620908839d7d0b1dc53b49a13f", "size_in_bytes": 10846}, {"_path": "doc/Cupti/structCUpti__ActivityGraphTrace.html", "path_type": "hardlink", "sha256": "a427d49f387d23472c827d0ae9c24777bd92800c6d4c6680e3ad5eb84e7f5390", "sha256_in_prefix": "a427d49f387d23472c827d0ae9c24777bd92800c6d4c6680e3ad5eb84e7f5390", "size_in_bytes": 9811}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousEvent.html", "path_type": "hardlink", "sha256": "2dfea7105a89d44e80d5e7d9065c7c950a3652f735773b2ebbed5cb9936da03e", "sha256_in_prefix": "2dfea7105a89d44e80d5e7d9065c7c950a3652f735773b2ebbed5cb9936da03e", "size_in_bytes": 7403}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousEventInstance.html", "path_type": "hardlink", "sha256": "3033cbdf731e2972e4202cf23bdda270d9d8138ae536e70111bc2a20578a221a", "sha256_in_prefix": "3033cbdf731e2972e4202cf23bdda270d9d8138ae536e70111bc2a20578a221a", "size_in_bytes": 8651}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousMetric.html", "path_type": "hardlink", "sha256": "e28b654847a9ab0f996dac1e9be31901ae3b5ad3266107895eaa35df98fcb4c6", "sha256_in_prefix": "e28b654847a9ab0f996dac1e9be31901ae3b5ad3266107895eaa35df98fcb4c6", "size_in_bytes": 8591}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousMetricInstance.html", "path_type": "hardlink", "sha256": "fcc22fdecea5adac882932711dd3d5ebd92cd6241c1008e758cf4e15fc7844c9", "sha256_in_prefix": "fcc22fdecea5adac882932711dd3d5ebd92cd6241c1008e758cf4e15fc7844c9", "size_in_bytes": 9875}, {"_path": "doc/Cupti/structCUpti__ActivityInstructionCorrelation.html", "path_type": "hardlink", "sha256": "eaa20c97c0f0a4e3adf4e1ba69d4ba6cec72895b4274941e6c3d27e3ab79054c", "sha256_in_prefix": "eaa20c97c0f0a4e3adf4e1ba69d4ba6cec72895b4274941e6c3d27e3ab79054c", "size_in_bytes": 7385}, {"_path": "doc/Cupti/structCUpti__ActivityInstructionExecution.html", "path_type": "hardlink", "sha256": "b6c90f7959102b69f66e30b3e4c84eb1970dda1c951497dc3df0bdab9f8e2db2", "sha256_in_prefix": "b6c90f7959102b69f66e30b3e4c84eb1970dda1c951497dc3df0bdab9f8e2db2", "size_in_bytes": 11123}, {"_path": "doc/Cupti/structCUpti__ActivityJit.html", "path_type": "hardlink", "sha256": "6b00022a07d245bf2e5f7105415985d0a46d022915b70cfcbb387902d6f9e163", "sha256_in_prefix": "6b00022a07d245bf2e5f7105415985d0a46d022915b70cfcbb387902d6f9e163", "size_in_bytes": 11668}, {"_path": "doc/Cupti/structCUpti__ActivityKernel.html", "path_type": "hardlink", "sha256": "b6cf5a526f4d80f290d6f916b88e5a7d5ef4737b3f4262640512b48d8803cff4", "sha256_in_prefix": "b6cf5a526f4d80f290d6f916b88e5a7d5ef4737b3f4262640512b48d8803cff4", "size_in_bytes": 22272}, {"_path": "doc/Cupti/structCUpti__ActivityKernel2.html", "path_type": "hardlink", "sha256": "04668b3ca28b42f8e7e6e514ff2d2c0ba41a0fb2705e9ba07cb4c4daf12c1db1", "sha256_in_prefix": "04668b3ca28b42f8e7e6e514ff2d2c0ba41a0fb2705e9ba07cb4c4daf12c1db1", "size_in_bytes": 23244}, {"_path": "doc/Cupti/structCUpti__ActivityKernel3.html", "path_type": "hardlink", "sha256": "5a326cb008853d09a4041e990950e5e31de5913747117749d79036f0a1ba8575", "sha256_in_prefix": "5a326cb008853d09a4041e990950e5e31de5913747117749d79036f0a1ba8575", "size_in_bytes": 25873}, {"_path": "doc/Cupti/structCUpti__ActivityKernel4.html", "path_type": "hardlink", "sha256": "300e095acfbf0cabaa69cefd3d0dc14ff487c27727124c64eac40c9a5a415fef", "sha256_in_prefix": "300e095acfbf0cabaa69cefd3d0dc14ff487c27727124c64eac40c9a5a415fef", "size_in_bytes": 33992}, {"_path": "doc/Cupti/structCUpti__ActivityKernel5.html", "path_type": "hardlink", "sha256": "bc13af7051837afd5f24eef8cd731a527caa9b440cb397845f60bdbe8e553fe6", "sha256_in_prefix": "bc13af7051837afd5f24eef8cd731a527caa9b440cb397845f60bdbe8e553fe6", "size_in_bytes": 36897}, {"_path": "doc/Cupti/structCUpti__ActivityKernel6.html", "path_type": "hardlink", "sha256": "375d0698eea415bb54740332943a03e1665f2ccbc251ff87e6677ca1fa3365f8", "sha256_in_prefix": "375d0698eea415bb54740332943a03e1665f2ccbc251ff87e6677ca1fa3365f8", "size_in_bytes": 37850}, {"_path": "doc/Cupti/structCUpti__ActivityKernel7.html", "path_type": "hardlink", "sha256": "d38086c68fb4db1dc5c3a60364340d8897534ffd45589c367997d7b4478d4ac4", "sha256_in_prefix": "d38086c68fb4db1dc5c3a60364340d8897534ffd45589c367997d7b4478d4ac4", "size_in_bytes": 39458}, {"_path": "doc/Cupti/structCUpti__ActivityKernel8.html", "path_type": "hardlink", "sha256": "6328b8dd378f41d919244cb5980daba921f6e9802cdfa6cd339d9cf2e7c01517", "sha256_in_prefix": "6328b8dd378f41d919244cb5980daba921f6e9802cdfa6cd339d9cf2e7c01517", "size_in_bytes": 43632}, {"_path": "doc/Cupti/structCUpti__ActivityKernel9.html", "path_type": "hardlink", "sha256": "b1efe98d31e8c19f4f74085a2ba22329cba43f7f2a7e46d54c0051295c2c4247", "sha256_in_prefix": "b1efe98d31e8c19f4f74085a2ba22329cba43f7f2a7e46d54c0051295c2c4247", "size_in_bytes": 45317}, {"_path": "doc/Cupti/structCUpti__ActivityMarker.html", "path_type": "hardlink", "sha256": "69de8efa0e1631d595e432f4c9c70e445a276849807ceb920d985f3e383d614a", "sha256_in_prefix": "69de8efa0e1631d595e432f4c9c70e445a276849807ceb920d985f3e383d614a", "size_in_bytes": 8756}, {"_path": "doc/Cupti/structCUpti__ActivityMarker2.html", "path_type": "hardlink", "sha256": "801ce9294e9063b021d541693c40fd938d15e0ba1629f5bb42dbdd5cda043142", "sha256_in_prefix": "801ce9294e9063b021d541693c40fd938d15e0ba1629f5bb42dbdd5cda043142", "size_in_bytes": 10147}, {"_path": "doc/Cupti/structCUpti__ActivityMarkerData.html", "path_type": "hardlink", "sha256": "5f4fee190fe4060b9c3c16621428cfc90cde1b0a17a6dba463cd81bdddbc01af", "sha256_in_prefix": "5f4fee190fe4060b9c3c16621428cfc90cde1b0a17a6dba463cd81bdddbc01af", "size_in_bytes": 8304}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy.html", "path_type": "hardlink", "sha256": "faa3c33684e2b239f22df346355ed67b99bbddfc77383d8fc64b5d40040e32eb", "sha256_in_prefix": "faa3c33684e2b239f22df346355ed67b99bbddfc77383d8fc64b5d40040e32eb", "size_in_bytes": 14744}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy3.html", "path_type": "hardlink", "sha256": "1a047d74b231453b5b2de78356f8863a92ffc2963036dd5d83b57917f8d9d9c9", "sha256_in_prefix": "1a047d74b231453b5b2de78356f8863a92ffc2963036dd5d83b57917f8d9d9c9", "size_in_bytes": 15722}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy4.html", "path_type": "hardlink", "sha256": "9b840b083a716bb4ae9d58cd1bf9db5e81e164f9f6b0c8f50d9555881dd06600", "sha256_in_prefix": "9b840b083a716bb4ae9d58cd1bf9db5e81e164f9f6b0c8f50d9555881dd06600", "size_in_bytes": 17394}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy5.html", "path_type": "hardlink", "sha256": "4d70cb2ffdfb4989621240f7188f84f77949699c28841654a3f6565952aa709e", "sha256_in_prefix": "4d70cb2ffdfb4989621240f7188f84f77949699c28841654a3f6565952aa709e", "size_in_bytes": 18960}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP.html", "path_type": "hardlink", "sha256": "e6cadcbca6d90233cc0ce8a80349f7facc0285134f61d159db11d8cfbea4bb12", "sha256_in_prefix": "e6cadcbca6d90233cc0ce8a80349f7facc0285134f61d159db11d8cfbea4bb12", "size_in_bytes": 18380}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP2.html", "path_type": "hardlink", "sha256": "788bc25c88178ae3ff46a6118299fad1f6ca91217cf11d25689bba7eefc9f39f", "sha256_in_prefix": "788bc25c88178ae3ff46a6118299fad1f6ca91217cf11d25689bba7eefc9f39f", "size_in_bytes": 19104}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP3.html", "path_type": "hardlink", "sha256": "64bd1dd11b94fad747ffcc333c293c163a5dc13d29a8954d04ad857f1101432e", "sha256_in_prefix": "64bd1dd11b94fad747ffcc333c293c163a5dc13d29a8954d04ad857f1101432e", "size_in_bytes": 20808}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP4.html", "path_type": "hardlink", "sha256": "441a2d86918cd44f9ff0dcbe948fdcfa4466625411d4001a250d5312895fb4d7", "sha256_in_prefix": "441a2d86918cd44f9ff0dcbe948fdcfa4466625411d4001a250d5312895fb4d7", "size_in_bytes": 21627}, {"_path": "doc/Cupti/structCUpti__ActivityMemory.html", "path_type": "hardlink", "sha256": "94a917cb4a45f9aec7e9a6c65544920f1b0969c7cff8d5987034d30962c6ae19", "sha256_in_prefix": "94a917cb4a45f9aec7e9a6c65544920f1b0969c7cff8d5987034d30962c6ae19", "size_in_bytes": 12671}, {"_path": "doc/Cupti/structCUpti__ActivityMemory2.html", "path_type": "hardlink", "sha256": "fc9b0501165e0f117cdce5b1a5b220e9e7b897e2bd636750fad2eb1016dd4d3a", "sha256_in_prefix": "fc9b0501165e0f117cdce5b1a5b220e9e7b897e2bd636750fad2eb1016dd4d3a", "size_in_bytes": 20115}, {"_path": "doc/Cupti/structCUpti__ActivityMemory3.html", "path_type": "hardlink", "sha256": "293ccbd22fb7a1fd022322eba96f18337529706a1b3d45c37dbf0deaf0042ef9", "sha256_in_prefix": "293ccbd22fb7a1fd022322eba96f18337529706a1b3d45c37dbf0deaf0042ef9", "size_in_bytes": 16299}, {"_path": "doc/Cupti/structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html", "path_type": "hardlink", "sha256": "fce1e907b727897156d00efc3b00c702baa76d05fe921b69f20fa41599117ff4", "sha256_in_prefix": "fce1e907b727897156d00efc3b00c702baa76d05fe921b69f20fa41599117ff4", "size_in_bytes": 7757}, {"_path": "doc/Cupti/structCUpti__ActivityMemoryPool.html", "path_type": "hardlink", "sha256": "715bb0b2022cfc06bb2dfba1471c19e45e238748b3ac62e447186b295552ad22", "sha256_in_prefix": "715bb0b2022cfc06bb2dfba1471c19e45e238748b3ac62e447186b295552ad22", "size_in_bytes": 13510}, {"_path": "doc/Cupti/structCUpti__ActivityMemoryPool2.html", "path_type": "hardlink", "sha256": "ae61f03742f48a532bc9e35d410402f96643cc25a63e3e74118acae2c2d54d2f", "sha256_in_prefix": "ae61f03742f48a532bc9e35d410402f96643cc25a63e3e74118acae2c2d54d2f", "size_in_bytes": 14575}, {"_path": "doc/Cupti/structCUpti__ActivityMemset.html", "path_type": "hardlink", "sha256": "ecc576919386036d4d8c4cf009a83fb67b14e511b039b26f860b967c19c958f3", "sha256_in_prefix": "ecc576919386036d4d8c4cf009a83fb67b14e511b039b26f860b967c19c958f3", "size_in_bytes": 12316}, {"_path": "doc/Cupti/structCUpti__ActivityMemset2.html", "path_type": "hardlink", "sha256": "ac441a217eec9df66e1105a5febbed055bd4775487d6a416c2855abbab330bfb", "sha256_in_prefix": "ac441a217eec9df66e1105a5febbed055bd4775487d6a416c2855abbab330bfb", "size_in_bytes": 13290}, {"_path": "doc/Cupti/structCUpti__ActivityMemset3.html", "path_type": "hardlink", "sha256": "38ebe30303aa219b734ce94c6ddd40a113f4a185cdf21eb3db984504d56e7d22", "sha256_in_prefix": "38ebe30303aa219b734ce94c6ddd40a113f4a185cdf21eb3db984504d56e7d22", "size_in_bytes": 14966}, {"_path": "doc/Cupti/structCUpti__ActivityMemset4.html", "path_type": "hardlink", "sha256": "3ff08b79362628a44e40a0a35c5b5ca010fe1cc49dee67c83fbbdfe0c640be53", "sha256_in_prefix": "3ff08b79362628a44e40a0a35c5b5ca010fe1cc49dee67c83fbbdfe0c640be53", "size_in_bytes": 16541}, {"_path": "doc/Cupti/structCUpti__ActivityMetric.html", "path_type": "hardlink", "sha256": "6c545af1961bdfe282f1dcd44944a7bad3e1a0b3f9994bfc8b4023885e7b5279", "sha256_in_prefix": "6c545af1961bdfe282f1dcd44944a7bad3e1a0b3f9994bfc8b4023885e7b5279", "size_in_bytes": 7520}, {"_path": "doc/Cupti/structCUpti__ActivityMetricInstance.html", "path_type": "hardlink", "sha256": "09f2326cec3549b46fa1c303dd4c28efeff9ab4df65ea6910ad21b9b0205b613", "sha256_in_prefix": "09f2326cec3549b46fa1c303dd4c28efeff9ab4df65ea6910ad21b9b0205b613", "size_in_bytes": 8720}, {"_path": "doc/Cupti/structCUpti__ActivityModule.html", "path_type": "hardlink", "sha256": "59388832d116fe714fa6db90a4318034c28b53c9507f6fd24c18b328537aac44", "sha256_in_prefix": "59388832d116fe714fa6db90a4318034c28b53c9507f6fd24c18b328537aac44", "size_in_bytes": 6845}, {"_path": "doc/Cupti/structCUpti__ActivityName.html", "path_type": "hardlink", "sha256": "faf113291d8b961e3bfc838fa4ad797800bd596be46dbccb34b7dfaa50c22130", "sha256_in_prefix": "faf113291d8b961e3bfc838fa4ad797800bd596be46dbccb34b7dfaa50c22130", "size_in_bytes": 5535}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink.html", "path_type": "hardlink", "sha256": "b9d981c05d1d5471f107f86ebf8ef91795b59b1a0d8fc8a6bef544aac923d856", "sha256_in_prefix": "b9d981c05d1d5471f107f86ebf8ef91795b59b1a0d8fc8a6bef544aac923d856", "size_in_bytes": 14242}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink2.html", "path_type": "hardlink", "sha256": "d28ce461394036d94210cdb8216b6b7020b6a45f446db275ab3deb40daa790fc", "sha256_in_prefix": "d28ce461394036d94210cdb8216b6b7020b6a45f446db275ab3deb40daa790fc", "size_in_bytes": 14399}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink3.html", "path_type": "hardlink", "sha256": "cb5923dfc519f8feaac8fa230f231bec62790292470527a07d89fba9ed517db9", "sha256_in_prefix": "cb5923dfc519f8feaac8fa230f231bec62790292470527a07d89fba9ed517db9", "size_in_bytes": 15970}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink4.html", "path_type": "hardlink", "sha256": "4085c6eab5d03f838a70e0e04a1aeb3f2f66a9e6e19b74f6f00162c684267be4", "sha256_in_prefix": "4085c6eab5d03f838a70e0e04a1aeb3f2f66a9e6e19b74f6f00162c684267be4", "size_in_bytes": 15798}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAcc.html", "path_type": "hardlink", "sha256": "06624cf56eea0aaedbd42fec078ed7f3925fd60991f73f577da508fc405cea54", "sha256_in_prefix": "06624cf56eea0aaedbd42fec078ed7f3925fd60991f73f577da508fc405cea54", "size_in_bytes": 21899}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAccData.html", "path_type": "hardlink", "sha256": "8c516714746423c617fca17ac4906f68bfc74307091ac581e5c3b1ce760dc3bf", "sha256_in_prefix": "8c516714746423c617fca17ac4906f68bfc74307091ac581e5c3b1ce760dc3bf", "size_in_bytes": 14642}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAccLaunch.html", "path_type": "hardlink", "sha256": "ebc517b741685e7b4f9a6f73a8e6b467b1f8c0bb773a7737f09e5ab6e40aca9c", "sha256_in_prefix": "ebc517b741685e7b4f9a6f73a8e6b467b1f8c0bb773a7737f09e5ab6e40aca9c", "size_in_bytes": 27672}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAccOther.html", "path_type": "hardlink", "sha256": "a66f74aa6a5e4273f24d1d524dbde8f5588512d51d2f0b90be488c37235d8086", "sha256_in_prefix": "a66f74aa6a5e4273f24d1d524dbde8f5588512d51d2f0b90be488c37235d8086", "size_in_bytes": 23368}, {"_path": "doc/Cupti/structCUpti__ActivityOpenMp.html", "path_type": "hardlink", "sha256": "5777b4049c9f39a47ce8a449d16e562c641fce8cb2f80cba052115bb560c1f31", "sha256_in_prefix": "5777b4049c9f39a47ce8a449d16e562c641fce8cb2f80cba052115bb560c1f31", "size_in_bytes": 8381}, {"_path": "doc/Cupti/structCUpti__ActivityOverhead.html", "path_type": "hardlink", "sha256": "41b9f0c519ab2b007f5fea860552bb113531b9a4dfda1352c618eec67860148e", "sha256_in_prefix": "41b9f0c519ab2b007f5fea860552bb113531b9a4dfda1352c618eec67860148e", "size_in_bytes": 7702}, {"_path": "doc/Cupti/structCUpti__ActivityPCSampling.html", "path_type": "hardlink", "sha256": "3876197ca597e2add1246a9a8c642b60245e400fee51a10185438f594b867988", "sha256_in_prefix": "3876197ca597e2add1246a9a8c642b60245e400fee51a10185438f594b867988", "size_in_bytes": 9374}, {"_path": "doc/Cupti/structCUpti__ActivityPCSampling2.html", "path_type": "hardlink", "sha256": "8b28041ba94b50a5f6df56dc414db02687e6449ca80a7bf15853c251dd1b3a64", "sha256_in_prefix": "8b28041ba94b50a5f6df56dc414db02687e6449ca80a7bf15853c251dd1b3a64", "size_in_bytes": 10462}, {"_path": "doc/Cupti/structCUpti__ActivityPCSampling3.html", "path_type": "hardlink", "sha256": "2e357bd12c45ef85ee22839c80b8025b9f200757229742dac73e17799c2f616d", "sha256_in_prefix": "2e357bd12c45ef85ee22839c80b8025b9f200757229742dac73e17799c2f616d", "size_in_bytes": 10235}, {"_path": "doc/Cupti/structCUpti__ActivityPCSamplingConfig.html", "path_type": "hardlink", "sha256": "0ee3bb055cbc5d235f8b16fedaf488c8763d57de6f2a4480f255dee14b7dc8ea", "sha256_in_prefix": "0ee3bb055cbc5d235f8b16fedaf488c8763d57de6f2a4480f255dee14b7dc8ea", "size_in_bytes": 5437}, {"_path": "doc/Cupti/structCUpti__ActivityPCSamplingRecordInfo.html", "path_type": "hardlink", "sha256": "80adcb6ba3056e90890ed737c6a28b7b181db5ed5fcaa09f094f9a3919739bd0", "sha256_in_prefix": "80adcb6ba3056e90890ed737c6a28b7b181db5ed5fcaa09f094f9a3919739bd0", "size_in_bytes": 6478}, {"_path": "doc/Cupti/structCUpti__ActivityPcie.html", "path_type": "hardlink", "sha256": "1f58a95e2f2901384e14a926e901659a5d83fc93dbde1e355e9e956f6d72ed99", "sha256_in_prefix": "1f58a95e2f2901384e14a926e901659a5d83fc93dbde1e355e9e956f6d72ed99", "size_in_bytes": 16029}, {"_path": "doc/Cupti/structCUpti__ActivityPreemption.html", "path_type": "hardlink", "sha256": "1416f93bd0c47bb9ae6357c3eb4928ec72b4e674a100c64193e7b1b93775deda", "sha256_in_prefix": "1416f93bd0c47bb9ae6357c3eb4928ec72b4e674a100c64193e7b1b93775deda", "size_in_bytes": 8680}, {"_path": "doc/Cupti/structCUpti__ActivitySharedAccess.html", "path_type": "hardlink", "sha256": "ee861b9a3ec88838cafe0378b32e1402404eeb7d05e0d47f08eb2a1c31030688", "sha256_in_prefix": "ee861b9a3ec88838cafe0378b32e1402404eeb7d05e0d47f08eb2a1c31030688", "size_in_bytes": 11619}, {"_path": "doc/Cupti/structCUpti__ActivitySourceLocator.html", "path_type": "hardlink", "sha256": "b3ca086a4ba4f813e69b474d2e89a10f7de51458373b85594132faeb946fab01", "sha256_in_prefix": "b3ca086a4ba4f813e69b474d2e89a10f7de51458373b85594132faeb946fab01", "size_in_bytes": 5255}, {"_path": "doc/Cupti/structCUpti__ActivityStream.html", "path_type": "hardlink", "sha256": "c06f712f9987a7e01a03e5f752b87d93fb9e2a3404c8384a8e826922be55ce38", "sha256_in_prefix": "c06f712f9987a7e01a03e5f752b87d93fb9e2a3404c8384a8e826922be55ce38", "size_in_bytes": 6920}, {"_path": "doc/Cupti/structCUpti__ActivitySynchronization.html", "path_type": "hardlink", "sha256": "279a0df704150697791dbe4044d0fa056b26edfa41f5cd0b17f019ce84071614", "sha256_in_prefix": "279a0df704150697791dbe4044d0fa056b26edfa41f5cd0b17f019ce84071614", "size_in_bytes": 9660}, {"_path": "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter.html", "path_type": "hardlink", "sha256": "0645fe27e5679ab643f136196402cd62af4801c065c540ae8d8cb3188108e38a", "sha256_in_prefix": "0645fe27e5679ab643f136196402cd62af4801c065c540ae8d8cb3188108e38a", "size_in_bytes": 9759}, {"_path": "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter2.html", "path_type": "hardlink", "sha256": "78fa32bc3087e768bba2eccf53b9877afc852336d5dbef114dcf527ec3041a23", "sha256_in_prefix": "78fa32bc3087e768bba2eccf53b9877afc852336d5dbef114dcf527ec3041a23", "size_in_bytes": 16320}, {"_path": "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounterConfig.html", "path_type": "hardlink", "sha256": "3f60db2ce7873b9cbda4cd817cbcb5d6af7eea4a884555056dcddab59ba780ec", "sha256_in_prefix": "3f60db2ce7873b9cbda4cd817cbcb5d6af7eea4a884555056dcddab59ba780ec", "size_in_bytes": 6105}, {"_path": "doc/<PERSON>ti/structCUpti__CallbackData.html", "path_type": "hardlink", "sha256": "41f7d0cfbc073faf5edadec75264b414491e63b3766c175847a0b4ea0ccd401a", "sha256_in_prefix": "41f7d0cfbc073faf5edadec75264b414491e63b3766c175847a0b4ea0ccd401a", "size_in_bytes": 12150}, {"_path": "doc/Cupti/structCUpti__EventGroupSet.html", "path_type": "hardlink", "sha256": "6859bb8133ad47aa835a3d9cc02e17ade2ef53707c56d3c97bb4466285dc0566", "sha256_in_prefix": "6859bb8133ad47aa835a3d9cc02e17ade2ef53707c56d3c97bb4466285dc0566", "size_in_bytes": 3862}, {"_path": "doc/Cupti/structCUpti__EventGroupSets.html", "path_type": "hardlink", "sha256": "c234a510401dfea89b1edc6a4bed2163db448821bbea18ffbd3cf7b9a5fb2825", "sha256_in_prefix": "c234a510401dfea89b1edc6a4bed2163db448821bbea18ffbd3cf7b9a5fb2825", "size_in_bytes": 3877}, {"_path": "doc/Cupti/structCUpti__GetCubinCrcParams.html", "path_type": "hardlink", "sha256": "2af1543e97a7734c25f324f5ee417a9ac018a7e056e7769061f3e14c94975572", "sha256_in_prefix": "2af1543e97a7734c25f324f5ee417a9ac018a7e056e7769061f3e14c94975572", "size_in_bytes": 4893}, {"_path": "doc/Cupti/structCUpti__GetSassToSourceCorrelationParams.html", "path_type": "hardlink", "sha256": "8bce4e0391b5335eb3e52f25dfef7af7efb2941bb73bac8c5c90a18cddd54d19", "sha256_in_prefix": "8bce4e0391b5335eb3e52f25dfef7af7efb2941bb73bac8c5c90a18cddd54d19", "size_in_bytes": 8658}, {"_path": "doc/Cupti/structCUpti__GraphData.html", "path_type": "hardlink", "sha256": "c58d898c7a209df3140497d8e0a42044ef67261ae1a9f68f1c55ae0edbe17660", "sha256_in_prefix": "c58d898c7a209df3140497d8e0a42044ef67261ae1a9f68f1c55ae0edbe17660", "size_in_bytes": 8324}, {"_path": "doc/Cupti/structCUpti__ModuleResourceData.html", "path_type": "hardlink", "sha256": "24982de87158bce2b3b4f941031ce7ded251e5f5a541dcd25b97fdc17cf4f0c1", "sha256_in_prefix": "24982de87158bce2b3b4f941031ce7ded251e5f5a541dcd25b97fdc17cf4f0c1", "size_in_bytes": 4593}, {"_path": "doc/Cupti/structCUpti__NvtxData.html", "path_type": "hardlink", "sha256": "3b77cf7569c8539f425e0886c2b75b618102b1a7e6a1f9143d69f3ed924bc7a6", "sha256_in_prefix": "3b77cf7569c8539f425e0886c2b75b618102b1a7e6a1f9143d69f3ed924bc7a6", "size_in_bytes": 4821}, {"_path": "doc/Cupti/structCUpti__PCSamplingConfigurationInfo.html", "path_type": "hardlink", "sha256": "986c0e3981bf4f5203aa23a63c884ecfef571aaaebddbb89848f30ff83da5849", "sha256_in_prefix": "986c0e3981bf4f5203aa23a63c884ecfef571aaaebddbb89848f30ff83da5849", "size_in_bytes": 13729}, {"_path": "doc/Cupti/structCUpti__PCSamplingConfigurationInfoParams.html", "path_type": "hardlink", "sha256": "8fd9bca6491ab5a40d7909d886423463bbc5aab5ea41d79e1d3c0cb155a99bd5", "sha256_in_prefix": "8fd9bca6491ab5a40d7909d886423463bbc5aab5ea41d79e1d3c0cb155a99bd5", "size_in_bytes": 7144}, {"_path": "doc/Cupti/structCUpti__PCSamplingData.html", "path_type": "hardlink", "sha256": "f2d43de19e6e3ff3abf4cdc3c73e643500372374410a556a3dca7c19af6dbca9", "sha256_in_prefix": "f2d43de19e6e3ff3abf4cdc3c73e643500372374410a556a3dca7c19af6dbca9", "size_in_bytes": 11245}, {"_path": "doc/Cupti/structCUpti__PCSamplingDisableParams.html", "path_type": "hardlink", "sha256": "28ecc457c4f4a22f85a5d4dcfc923caac5801b6ba8aaf65ea3863787a4ba4e25", "sha256_in_prefix": "28ecc457c4f4a22f85a5d4dcfc923caac5801b6ba8aaf65ea3863787a4ba4e25", "size_in_bytes": 4182}, {"_path": "doc/Cupti/structCUpti__PCSamplingEnableParams.html", "path_type": "hardlink", "sha256": "801ecd32e17289ebaca4ede6889f47b000e19a33d6303bebed4b82b5fd003cdd", "sha256_in_prefix": "801ecd32e17289ebaca4ede6889f47b000e19a33d6303bebed4b82b5fd003cdd", "size_in_bytes": 4165}, {"_path": "doc/Cupti/structCUpti__PCSamplingGetDataParams.html", "path_type": "hardlink", "sha256": "371a16071255393b29498fb6140fe74c92294725ab1470be8cb79ed99c91642c", "sha256_in_prefix": "371a16071255393b29498fb6140fe74c92294725ab1470be8cb79ed99c91642c", "size_in_bytes": 5415}, {"_path": "doc/Cupti/structCUpti__PCSamplingGetNumStallReasonsParams.html", "path_type": "hardlink", "sha256": "81dde7717efd07a94787b914dee435f55beb09b379755eab41b4187c52722496", "sha256_in_prefix": "81dde7717efd07a94787b914dee435f55beb09b379755eab41b4187c52722496", "size_in_bytes": 5241}, {"_path": "doc/Cupti/structCUpti__PCSamplingGetStallReasonsParams.html", "path_type": "hardlink", "sha256": "853846ff14bb4ae356f8ea2c4d9c6464abd1bc23ca99b79252bfc5f563303a49", "sha256_in_prefix": "853846ff14bb4ae356f8ea2c4d9c6464abd1bc23ca99b79252bfc5f563303a49", "size_in_bytes": 6881}, {"_path": "doc/Cupti/structCUpti__PCSamplingPCData.html", "path_type": "hardlink", "sha256": "dc351bc043cb28d9f23bfad094fff8cc796800ce57b0eaf813a0b6f2fb19f173", "sha256_in_prefix": "dc351bc043cb28d9f23bfad094fff8cc796800ce57b0eaf813a0b6f2fb19f173", "size_in_bytes": 8465}, {"_path": "doc/Cupti/structCUpti__PCSamplingStallReason.html", "path_type": "hardlink", "sha256": "63d47e2da30b747d3baa90a26b6eb815ed8b39912429c514edf9198190b2975d", "sha256_in_prefix": "63d47e2da30b747d3baa90a26b6eb815ed8b39912429c514edf9198190b2975d", "size_in_bytes": 3287}, {"_path": "doc/Cupti/structCUpti__PCSamplingStartParams.html", "path_type": "hardlink", "sha256": "0d5323b4037aa19623f17276b42d8a1e09c424ea409ea9c8be25adfba38bb4a8", "sha256_in_prefix": "0d5323b4037aa19623f17276b42d8a1e09c424ea409ea9c8be25adfba38bb4a8", "size_in_bytes": 4148}, {"_path": "doc/Cupti/structCUpti__PCSamplingStopParams.html", "path_type": "hardlink", "sha256": "c3b92c5a1f45cf9c50e59af1516fc163130766404736916576aae29823b6c638", "sha256_in_prefix": "c3b92c5a1f45cf9c50e59af1516fc163130766404736916576aae29823b6c638", "size_in_bytes": 4131}, {"_path": "doc/Cupti/structCUpti__Profiler__BeginPass__Params.html", "path_type": "hardlink", "sha256": "3e50970fdacb6ce07e24335f6c181f556f922486e8f6d5463295355fc45587b1", "sha256_in_prefix": "3e50970fdacb6ce07e24335f6c181f556f922486e8f6d5463295355fc45587b1", "size_in_bytes": 3176}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__BeginSession__Params.html", "path_type": "hardlink", "sha256": "c886437531e08ef981279a1b656e24ea905b5d00c2d6703727f946ee4d4811ba", "sha256_in_prefix": "c886437531e08ef981279a1b656e24ea905b5d00c2d6703727f946ee4d4811ba", "size_in_bytes": 9164}, {"_path": "doc/Cupti/structCUpti__Profiler__CounterDataImageOptions.html", "path_type": "hardlink", "sha256": "9c5a02f2f8a7d5b5e738b828f86323deb319af2c82f6ad6c23176f5195b2bd9b", "sha256_in_prefix": "9c5a02f2f8a7d5b5e738b828f86323deb319af2c82f6ad6c23176f5195b2bd9b", "size_in_bytes": 6145}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html", "path_type": "hardlink", "sha256": "9f5523ae19d827ab3b729934017eb37e30065f4956ce9ea61384393eceffd111", "sha256_in_prefix": "9f5523ae19d827ab3b729934017eb37e30065f4956ce9ea61384393eceffd111", "size_in_bytes": 4812}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html", "path_type": "hardlink", "sha256": "e21663447e743abee0c3d3322f43f579ba33ba880b1178ffd8f759af583e53b4", "sha256_in_prefix": "e21663447e743abee0c3d3322f43f579ba33ba880b1178ffd8f759af583e53b4", "size_in_bytes": 4763}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html", "path_type": "hardlink", "sha256": "456de750880693b655473880080f529409782720098a124bfeef2a83b87cff7d", "sha256_in_prefix": "456de750880693b655473880080f529409782720098a124bfeef2a83b87cff7d", "size_in_bytes": 5484}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__Initialize__Params.html", "path_type": "hardlink", "sha256": "154f5a8aec75385fb437ac1e31163b3d385392212b4e6c239d313e344292cce9", "sha256_in_prefix": "154f5a8aec75385fb437ac1e31163b3d385392212b4e6c239d313e344292cce9", "size_in_bytes": 5371}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__DeInitialize__Params.html", "path_type": "hardlink", "sha256": "c6da1c9a413ea0aeea62fbb0ffddb8ec9115a641e566fc764a1c52eb0e2cfec1", "sha256_in_prefix": "c6da1c9a413ea0aeea62fbb0ffddb8ec9115a641e566fc764a1c52eb0e2cfec1", "size_in_bytes": 2680}, {"_path": "doc/Cupti/structCUpti__Profiler__DeviceSupported__Params.html", "path_type": "hardlink", "sha256": "975820e2e1a65c4a81519a398c7dfae8fc52908d038accf87a4454afabdcb5af", "sha256_in_prefix": "975820e2e1a65c4a81519a398c7dfae8fc52908d038accf87a4454afabdcb5af", "size_in_bytes": 8374}, {"_path": "doc/Cupti/structCUpti__Profiler__DisableProfiling__Params.html", "path_type": "hardlink", "sha256": "ef27aa9c33b4cb6fe1e4fd3f6ff1a6e91869736b4a9e47a4fdcd6655a8d175f5", "sha256_in_prefix": "ef27aa9c33b4cb6fe1e4fd3f6ff1a6e91869736b4a9e47a4fdcd6655a8d175f5", "size_in_bytes": 3253}, {"_path": "doc/Cupti/structCUpti__Profiler__EnableProfiling__Params.html", "path_type": "hardlink", "sha256": "a9da65d6f07afebf7e98133322fa6d01bc08a8156f4bedf6ab487ad6a3c230c3", "sha256_in_prefix": "a9da65d6f07afebf7e98133322fa6d01bc08a8156f4bedf6ab487ad6a3c230c3", "size_in_bytes": 3242}, {"_path": "doc/Cupti/structCUpti__Profiler__EndPass__Params.html", "path_type": "hardlink", "sha256": "8bbabcdd56df99e61ecd0f7e041ab7659fc03f0d47979a69c30346f04bbb7b7b", "sha256_in_prefix": "8bbabcdd56df99e61ecd0f7e041ab7659fc03f0d47979a69c30346f04bbb7b7b", "size_in_bytes": 4817}, {"_path": "doc/Cupti/structCUpti__Profiler__EndSession__Params.html", "path_type": "hardlink", "sha256": "d94fcacfe31db83da75d020c2064283225ca0239bbbe99dfa238a6687c72d26c", "sha256_in_prefix": "d94fcacfe31db83da75d020c2064283225ca0239bbbe99dfa238a6687c72d26c", "size_in_bytes": 3187}, {"_path": "doc/Cupti/structCUpti__Profiler__FlushCounterData__Params.html", "path_type": "hardlink", "sha256": "388c70f740afa8b3eea3e6a0cd837f49dafeb150b740ce87dcd26f86d60f28cc", "sha256_in_prefix": "388c70f740afa8b3eea3e6a0cd837f49dafeb150b740ce87dcd26f86d60f28cc", "size_in_bytes": 4453}, {"_path": "doc/Cupti/structCUpti__Profiler__GetCounterAvailability__Params.html", "path_type": "hardlink", "sha256": "1ff8382ec220caa44744f1c61de2ab66592baf85e4ee94d1e683b43f1043827b", "sha256_in_prefix": "1ff8382ec220caa44744f1c61de2ab66592baf85e4ee94d1e683b43f1043827b", "size_in_bytes": 5165}, {"_path": "doc/Cupti/structCUpti__Profiler__Initialize__Params.html", "path_type": "hardlink", "sha256": "66c10bf1d6d0244f3ed7fe385bd25e7611497880f762647a6eca2c2cf3b6840e", "sha256_in_prefix": "66c10bf1d6d0244f3ed7fe385bd25e7611497880f762647a6eca2c2cf3b6840e", "size_in_bytes": 2662}, {"_path": "doc/Cupti/structCUpti__Profiler__IsPassCollected__Params.html", "path_type": "hardlink", "sha256": "001b8d2006a28ea096fdb2bc0f8ddbad4bb0ea2b848e7e4872c51c9e4cee968b", "sha256_in_prefix": "001b8d2006a28ea096fdb2bc0f8ddbad4bb0ea2b848e7e4872c51c9e4cee968b", "size_in_bytes": 5593}, {"_path": "doc/Cupti/structCUpti__Profiler__SetConfig__Params.html", "path_type": "hardlink", "sha256": "14c7cb418da34729413a3c75ef6299b7aef70bdac0099df1bab3be5ad0365454", "sha256_in_prefix": "14c7cb418da34729413a3c75ef6299b7aef70bdac0099df1bab3be5ad0365454", "size_in_bytes": 6656}, {"_path": "doc/Cupti/structCUpti__Profiler__UnsetConfig__Params.html", "path_type": "hardlink", "sha256": "4cdf63bbf436e1e7d6923fb100ad6892685bd441707d3d0722fd29161b7a2845", "sha256_in_prefix": "4cdf63bbf436e1e7d6923fb100ad6892685bd441707d3d0722fd29161b7a2845", "size_in_bytes": 3198}, {"_path": "doc/Cupti/structCUpti__ResourceData.html", "path_type": "hardlink", "sha256": "120ace041815044f7061b5e7dbb9eb26c176d99c53a1213acf8b1784a773d028", "sha256_in_prefix": "120ace041815044f7061b5e7dbb9eb26c176d99c53a1213acf8b1784a773d028", "size_in_bytes": 4834}, {"_path": "doc/<PERSON>ti/structCUpti__SynchronizeData.html", "path_type": "hardlink", "sha256": "814d334127e01a97e7b468674eee8ae263301bcd83319237e7ab5017f1e18a11", "sha256_in_prefix": "814d334127e01a97e7b468674eee8ae263301bcd83319237e7ab5017f1e18a11", "size_in_bytes": 3760}, {"_path": "doc/Cupti/structHeader.html", "path_type": "hardlink", "sha256": "69528e874aa40915e88e6696acc632b5a75f114d3cb05c04a20787d20417c99c", "sha256_in_prefix": "69528e874aa40915e88e6696acc632b5a75f114d3cb05c04a20787d20417c99c", "size_in_bytes": 3093}, {"_path": "doc/Cupti/structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html", "path_type": "hardlink", "sha256": "54b523b252c6263c5615a102d5cf8684377916c56954bc1182f6e29696edd245", "sha256_in_prefix": "54b523b252c6263c5615a102d5cf8684377916c56954bc1182f6e29696edd245", "size_in_bytes": 6887}, {"_path": "doc/Cupti/structPcSamplingStallReasons.html", "path_type": "hardlink", "sha256": "0810c41be81e1da5c32c897556fb61d1e74baa4e6a461b2dc9ed0a496a14c4c8", "sha256_in_prefix": "0810c41be81e1da5c32c897556fb61d1e74baa4e6a461b2dc9ed0a496a14c4c8", "size_in_bytes": 4088}, {"_path": "doc/<PERSON>ti/tab_b.gif", "path_type": "hardlink", "sha256": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "sha256_in_prefix": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "size_in_bytes": 35}, {"_path": "doc/<PERSON>ti/tab_l.gif", "path_type": "hardlink", "sha256": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "sha256_in_prefix": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "size_in_bytes": 706}, {"_path": "doc/<PERSON>ti/tab_r.gif", "path_type": "hardlink", "sha256": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "sha256_in_prefix": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "size_in_bytes": 2585}, {"_path": "doc/Cupti/tabs.css", "path_type": "hardlink", "sha256": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "sha256_in_prefix": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "size_in_bytes": 1838}, {"_path": "doc/Cupti/unionCUpti__ActivityObjectKindId.html", "path_type": "hardlink", "sha256": "0764105d049880f62534f75b8dcbf02115ed99b2662a7e237e7fbcf4f277503a", "sha256_in_prefix": "0764105d049880f62534f75b8dcbf02115ed99b2662a7e237e7fbcf4f277503a", "size_in_bytes": 3862}, {"_path": "doc/Cupti/unionCUpti__MetricValue.html", "path_type": "hardlink", "sha256": "31114b38c0c70d1968d41bd70a3cc4374e1d67a80ef487395bc756cd84383dcf", "sha256_in_prefix": "31114b38c0c70d1968d41bd70a3cc4374e1d67a80ef487395bc756cd84383dcf", "size_in_bytes": 1974}, {"_path": "doc/common/formatting/bg-head.png", "path_type": "hardlink", "sha256": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "sha256_in_prefix": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "size_in_bytes": 230}, {"_path": "doc/common/formatting/bg-horiz.png", "path_type": "hardlink", "sha256": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "sha256_in_prefix": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "size_in_bytes": 331}, {"_path": "doc/common/formatting/bg-left.png", "path_type": "hardlink", "sha256": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "sha256_in_prefix": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "size_in_bytes": 132}, {"_path": "doc/common/formatting/bg-right.png", "path_type": "hardlink", "sha256": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "sha256_in_prefix": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "size_in_bytes": 131}, {"_path": "doc/common/formatting/bg-sidehead-glow.png", "path_type": "hardlink", "sha256": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "sha256_in_prefix": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "size_in_bytes": 153}, {"_path": "doc/common/formatting/bg-sidehead.png", "path_type": "hardlink", "sha256": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "sha256_in_prefix": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "size_in_bytes": 2827}, {"_path": "doc/common/formatting/bg-vert.png", "path_type": "hardlink", "sha256": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "sha256_in_prefix": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "size_in_bytes": 152}, {"_path": "doc/common/formatting/common.min.js", "path_type": "hardlink", "sha256": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "sha256_in_prefix": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "size_in_bytes": 10628}, {"_path": "doc/common/formatting/commonltr.css", "path_type": "hardlink", "sha256": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "sha256_in_prefix": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "size_in_bytes": 6097}, {"_path": "doc/common/formatting/cppapiref.css", "path_type": "hardlink", "sha256": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "sha256_in_prefix": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "size_in_bytes": 8927}, {"_path": "doc/common/formatting/cuda-toolkit-documentation.png", "path_type": "hardlink", "sha256": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "sha256_in_prefix": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "size_in_bytes": 9129}, {"_path": "doc/common/formatting/devtools-documentation.png", "path_type": "hardlink", "sha256": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "sha256_in_prefix": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "size_in_bytes": 4359}, {"_path": "doc/common/formatting/devzone.png", "path_type": "hardlink", "sha256": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "sha256_in_prefix": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "size_in_bytes": 10349}, {"_path": "doc/common/formatting/dita.style.css", "path_type": "hardlink", "sha256": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "sha256_in_prefix": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "size_in_bytes": 34852}, {"_path": "doc/common/formatting/html5shiv-printshiv.min.js", "path_type": "hardlink", "sha256": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "sha256_in_prefix": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "size_in_bytes": 3989}, {"_path": "doc/common/formatting/jquery.ba-hashchange.min.js", "path_type": "hardlink", "sha256": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "sha256_in_prefix": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "size_in_bytes": 1604}, {"_path": "doc/common/formatting/jquery.min.js", "path_type": "hardlink", "sha256": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "sha256_in_prefix": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "size_in_bytes": 92633}, {"_path": "doc/common/formatting/jquery.scrollintoview.min.js", "path_type": "hardlink", "sha256": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "sha256_in_prefix": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "size_in_bytes": 3501}, {"_path": "doc/common/formatting/magnify-dropdown.png", "path_type": "hardlink", "sha256": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "sha256_in_prefix": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "size_in_bytes": 1139}, {"_path": "doc/common/formatting/magnify.png", "path_type": "hardlink", "sha256": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "sha256_in_prefix": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "size_in_bytes": 1100}, {"_path": "doc/common/formatting/nvidia.png", "path_type": "hardlink", "sha256": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "sha256_in_prefix": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "size_in_bytes": 4442}, {"_path": "doc/common/formatting/prettify/lang-Splus.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "doc/common/formatting/prettify/lang-aea.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "doc/common/formatting/prettify/lang-agc.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "doc/common/formatting/prettify/lang-apollo.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "doc/common/formatting/prettify/lang-basic.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "doc/common/formatting/prettify/lang-cbm.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "doc/common/formatting/prettify/lang-cl.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-clj.js", "path_type": "hardlink", "sha256": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "sha256_in_prefix": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "size_in_bytes": 1484}, {"_path": "doc/common/formatting/prettify/lang-css.js", "path_type": "hardlink", "sha256": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "sha256_in_prefix": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "size_in_bytes": 1525}, {"_path": "doc/common/formatting/prettify/lang-dart.js", "path_type": "hardlink", "sha256": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "sha256_in_prefix": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "size_in_bytes": 1626}, {"_path": "doc/common/formatting/prettify/lang-el.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-erl.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "doc/common/formatting/prettify/lang-erlang.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "doc/common/formatting/prettify/lang-fs.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "doc/common/formatting/prettify/lang-go.js", "path_type": "hardlink", "sha256": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "sha256_in_prefix": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "size_in_bytes": 884}, {"_path": "doc/common/formatting/prettify/lang-hs.js", "path_type": "hardlink", "sha256": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "sha256_in_prefix": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "size_in_bytes": 1217}, {"_path": "doc/common/formatting/prettify/lang-lasso.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "doc/common/formatting/prettify/lang-lassoscript.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "doc/common/formatting/prettify/lang-latex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "doc/common/formatting/prettify/lang-lgt.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "doc/common/formatting/prettify/lang-lisp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-ll.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "doc/common/formatting/prettify/lang-llvm.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "doc/common/formatting/prettify/lang-logtalk.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "doc/common/formatting/prettify/lang-ls.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "doc/common/formatting/prettify/lang-lsp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-lua.js", "path_type": "hardlink", "sha256": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "sha256_in_prefix": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "size_in_bytes": 1162}, {"_path": "doc/common/formatting/prettify/lang-matlab.js", "path_type": "hardlink", "sha256": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "sha256_in_prefix": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "size_in_bytes": 21092}, {"_path": "doc/common/formatting/prettify/lang-ml.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "doc/common/formatting/prettify/lang-mumps.js", "path_type": "hardlink", "sha256": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "sha256_in_prefix": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "size_in_bytes": 1500}, {"_path": "doc/common/formatting/prettify/lang-n.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "doc/common/formatting/prettify/lang-nemerle.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "doc/common/formatting/prettify/lang-pascal.js", "path_type": "hardlink", "sha256": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "sha256_in_prefix": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "size_in_bytes": 1332}, {"_path": "doc/common/formatting/prettify/lang-proto.js", "path_type": "hardlink", "sha256": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "sha256_in_prefix": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "size_in_bytes": 891}, {"_path": "doc/common/formatting/prettify/lang-r.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "doc/common/formatting/prettify/lang-rd.js", "path_type": "hardlink", "sha256": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "sha256_in_prefix": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "size_in_bytes": 862}, {"_path": "doc/common/formatting/prettify/lang-rkt.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-rust.js", "path_type": "hardlink", "sha256": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "sha256_in_prefix": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "size_in_bytes": 2254}, {"_path": "doc/common/formatting/prettify/lang-s.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "doc/common/formatting/prettify/lang-scala.js", "path_type": "hardlink", "sha256": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "sha256_in_prefix": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "size_in_bytes": 1554}, {"_path": "doc/common/formatting/prettify/lang-scm.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-sql.js", "path_type": "hardlink", "sha256": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "sha256_in_prefix": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "size_in_bytes": 2404}, {"_path": "doc/common/formatting/prettify/lang-ss.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-swift.js", "path_type": "hardlink", "sha256": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "sha256_in_prefix": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "size_in_bytes": 2050}, {"_path": "doc/common/formatting/prettify/lang-tcl.js", "path_type": "hardlink", "sha256": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "sha256_in_prefix": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "size_in_bytes": 1261}, {"_path": "doc/common/formatting/prettify/lang-tex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "doc/common/formatting/prettify/lang-vb.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "doc/common/formatting/prettify/lang-vbs.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "doc/common/formatting/prettify/lang-vhd.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "doc/common/formatting/prettify/lang-vhdl.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "doc/common/formatting/prettify/lang-wiki.js", "path_type": "hardlink", "sha256": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "sha256_in_prefix": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "size_in_bytes": 1157}, {"_path": "doc/common/formatting/prettify/lang-xq.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "doc/common/formatting/prettify/lang-xquery.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "doc/common/formatting/prettify/lang-yaml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "doc/common/formatting/prettify/lang-yml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "doc/common/formatting/prettify/onLoad.png", "path_type": "hardlink", "sha256": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "sha256_in_prefix": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "size_in_bytes": 110}, {"_path": "doc/common/formatting/prettify/prettify.css", "path_type": "hardlink", "sha256": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "sha256_in_prefix": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "size_in_bytes": 675}, {"_path": "doc/common/formatting/prettify/prettify.js", "path_type": "hardlink", "sha256": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "sha256_in_prefix": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "size_in_bytes": 15307}, {"_path": "doc/common/formatting/prettify/run_prettify.js", "path_type": "hardlink", "sha256": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "sha256_in_prefix": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "size_in_bytes": 18100}, {"_path": "doc/common/formatting/qwcode.highlight.css", "path_type": "hardlink", "sha256": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "sha256_in_prefix": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "size_in_bytes": 908}, {"_path": "doc/common/formatting/search-clear.png", "path_type": "hardlink", "sha256": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "sha256_in_prefix": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "size_in_bytes": 3638}, {"_path": "doc/common/formatting/site.css", "path_type": "hardlink", "sha256": "dfa996ee28d5b956e18054960dd079dc7f53b6ee6a458afaee4b8d4b9bb5fb50", "sha256_in_prefix": "dfa996ee28d5b956e18054960dd079dc7f53b6ee6a458afaee4b8d4b9bb5fb50", "size_in_bytes": 12469}, {"_path": "doc/common/scripts/google-analytics/google-analytics-tracker.js", "path_type": "hardlink", "sha256": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "sha256_in_prefix": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "size_in_bytes": 117}, {"_path": "doc/common/scripts/google-analytics/google-analytics-write.js", "path_type": "hardlink", "sha256": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "sha256_in_prefix": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "size_in_bytes": 221}, {"_path": "doc/common/scripts/tynt/tynt.js", "path_type": "hardlink", "sha256": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "sha256_in_prefix": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "size_in_bytes": 316}, {"_path": "doc/index.html", "path_type": "hardlink", "sha256": "b933f52f6e2152f14b827c0aace2480f6eb56283b9cfdec42b18f83775fa9191", "sha256_in_prefix": "b933f52f6e2152f14b827c0aace2480f6eb56283b9cfdec42b18f83775fa9191", "size_in_bytes": 3896}, {"_path": "doc/pdf/Cupti.pdf", "path_type": "hardlink", "sha256": "0e16f640f5710d6f91d44df42c73847b7d38508ccfceb6ffc2e22435a73a636e", "sha256_in_prefix": "0e16f640f5710d6f91d44df42c73847b7d38508ccfceb6ffc2e22435a73a636e", "size_in_bytes": 2026299}, {"_path": "doc/search/check.html", "path_type": "hardlink", "sha256": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "sha256_in_prefix": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "size_in_bytes": 1252}, {"_path": "doc/search/files.js", "path_type": "hardlink", "sha256": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "sha256_in_prefix": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "size_in_bytes": 99}, {"_path": "doc/search/htmlFileInfoList.js", "path_type": "hardlink", "sha256": "402c28cd9e05e474e2667ac97cbd7ca9ba46da05d531aac4beda50bb7704de7b", "sha256_in_prefix": "402c28cd9e05e474e2667ac97cbd7ca9ba46da05d531aac4beda50bb7704de7b", "size_in_bytes": 14495}, {"_path": "doc/search/htmlFileList.js", "path_type": "hardlink", "sha256": "2a2973fc1d4a9af88e18af441e1f66db7f9e1c59c7aa67a03628466583e36b21", "sha256_in_prefix": "2a2973fc1d4a9af88e18af441e1f66db7f9e1c59c7aa67a03628466583e36b21", "size_in_bytes": 11310}, {"_path": "doc/search/index-1.js", "path_type": "hardlink", "sha256": "8ba1c059f747cf30c2a21717d4143c70e2cefd264c280e27d883f5e36874504e", "sha256_in_prefix": "8ba1c059f747cf30c2a21717d4143c70e2cefd264c280e27d883f5e36874504e", "size_in_bytes": 66524}, {"_path": "doc/search/index-2.js", "path_type": "hardlink", "sha256": "5b05d9005c2f2bb6b131cffdce49697e58e3882918adc865ef9a1682c0b99e84", "sha256_in_prefix": "5b05d9005c2f2bb6b131cffdce49697e58e3882918adc865ef9a1682c0b99e84", "size_in_bytes": 67199}, {"_path": "doc/search/index-3.js", "path_type": "hardlink", "sha256": "8d7cd4bc99a41ce8e0301aa26e466aba53193408b258ef3c4b9098d9c3f249e5", "sha256_in_prefix": "8d7cd4bc99a41ce8e0301aa26e466aba53193408b258ef3c4b9098d9c3f249e5", "size_in_bytes": 58133}, {"_path": "doc/search/nwSearchFnt.min.js", "path_type": "hardlink", "sha256": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "sha256_in_prefix": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "size_in_bytes": 12073}, {"_path": "doc/search/stemmers/en_stemmer.min.js", "path_type": "hardlink", "sha256": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "sha256_in_prefix": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "size_in_bytes": 3531}, {"_path": "include/cuda_stdint.h", "path_type": "hardlink", "sha256": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "sha256_in_prefix": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "size_in_bytes": 4205}, {"_path": "include/cupti.h", "path_type": "hardlink", "sha256": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "sha256_in_prefix": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "size_in_bytes": 4820}, {"_path": "include/cupti_activity.h", "path_type": "hardlink", "sha256": "e35da08b237d5a7cc34547c1c88f587b2dfcc243ce5914407012f32d3ce1168a", "sha256_in_prefix": "e35da08b237d5a7cc34547c1c88f587b2dfcc243ce5914407012f32d3ce1168a", "size_in_bytes": 323470}, {"_path": "include/cupti_callbacks.h", "path_type": "hardlink", "sha256": "8facb1dd6867dcdf5a2ce4ed612780ebac1b7e95f60c97b4cab9e71e3c7e5916", "sha256_in_prefix": "8facb1dd6867dcdf5a2ce4ed612780ebac1b7e95f60c97b4cab9e71e3c7e5916", "size_in_bytes": 27349}, {"_path": "include/cupti_checkpoint.h", "path_type": "hardlink", "sha256": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "sha256_in_prefix": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "size_in_bytes": 5391}, {"_path": "include/cupti_driver_cbid.h", "path_type": "hardlink", "sha256": "3cf2687174e04d0d9afe54546d90aad4b9e069afeefd6e37b220c78a773ecf97", "sha256_in_prefix": "3cf2687174e04d0d9afe54546d90aad4b9e069afeefd6e37b220c78a773ecf97", "size_in_bytes": 71071}, {"_path": "include/cupti_events.h", "path_type": "hardlink", "sha256": "6c4a8e40c689bc9065db3b182b57e0ddfb2375c67b49a761ff5f609c49efead5", "sha256_in_prefix": "6c4a8e40c689bc9065db3b182b57e0ddfb2375c67b49a761ff5f609c49efead5", "size_in_bytes": 54010}, {"_path": "include/cupti_metrics.h", "path_type": "hardlink", "sha256": "d6ef3eb2c32728abdd7da530604a611e154d5777279fac7af37b3cdf2ef72ad4", "sha256_in_prefix": "d6ef3eb2c32728abdd7da530604a611e154d5777279fac7af37b3cdf2ef72ad4", "size_in_bytes": 32973}, {"_path": "include/cupti_nvtx_cbid.h", "path_type": "hardlink", "sha256": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "sha256_in_prefix": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "size_in_bytes": 6023}, {"_path": "include/cupti_pcsampling.h", "path_type": "hardlink", "sha256": "20df334a5790becb5b1f95e560890a5be01ebf9ddccf73be049506cf838c4f93", "sha256_in_prefix": "20df334a5790becb5b1f95e560890a5be01ebf9ddccf73be049506cf838c4f93", "size_in_bytes": 33345}, {"_path": "include/cupti_pcsampling_util.h", "path_type": "hardlink", "sha256": "d012ca51196ac11643045bc16a95dd9426ee3f8afae9770141baab478fe63105", "sha256_in_prefix": "d012ca51196ac11643045bc16a95dd9426ee3f8afae9770141baab478fe63105", "size_in_bytes": 13479}, {"_path": "include/cupti_profiler_target.h", "path_type": "hardlink", "sha256": "19b02d3bcad1e37f11e6627e322719aea3af7676d4b251b8175fa811ad6b972c", "sha256_in_prefix": "19b02d3bcad1e37f11e6627e322719aea3af7676d4b251b8175fa811ad6b972c", "size_in_bytes": 32185}, {"_path": "include/cupti_result.h", "path_type": "hardlink", "sha256": "161deb6bc8a6eae2e7f39c85d2dbd08e92ea00d6d2dff6362696e7b6069eaa08", "sha256_in_prefix": "161deb6bc8a6eae2e7f39c85d2dbd08e92ea00d6d2dff6362696e7b6069eaa08", "size_in_bytes": 12354}, {"_path": "include/cupti_runtime_cbid.h", "path_type": "hardlink", "sha256": "53a94d342b18aae4bf653eeacf1e8c1506929cbbad250408b6cac051f92a0f69", "sha256_in_prefix": "53a94d342b18aae4bf653eeacf1e8c1506929cbbad250408b6cac051f92a0f69", "size_in_bytes": 44640}, {"_path": "include/cupti_target.h", "path_type": "hardlink", "sha256": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "sha256_in_prefix": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "size_in_bytes": 1306}, {"_path": "include/cupti_version.h", "path_type": "hardlink", "sha256": "8787bc05fd4eb33767890977bc256054d4b923cda547e18b361d31df49c12927", "sha256_in_prefix": "8787bc05fd4eb33767890977bc256054d4b923cda547e18b361d31df49c12927", "size_in_bytes": 4475}, {"_path": "include/generated_cudaD3D10_meta.h", "path_type": "hardlink", "sha256": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "sha256_in_prefix": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "size_in_bytes": 4439}, {"_path": "include/generated_cudaD3D11_meta.h", "path_type": "hardlink", "sha256": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "sha256_in_prefix": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "size_in_bytes": 1823}, {"_path": "include/generated_cudaD3D9_meta.h", "path_type": "hardlink", "sha256": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "sha256_in_prefix": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "size_in_bytes": 5379}, {"_path": "include/generated_cudaGL_meta.h", "path_type": "hardlink", "sha256": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "sha256_in_prefix": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "size_in_bytes": 3344}, {"_path": "include/generated_cuda_d3d10_interop_meta.h", "path_type": "hardlink", "sha256": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "sha256_in_prefix": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "size_in_bytes": 3364}, {"_path": "include/generated_cuda_d3d11_interop_meta.h", "path_type": "hardlink", "sha256": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "sha256_in_prefix": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "size_in_bytes": 1500}, {"_path": "include/generated_cuda_d3d9_interop_meta.h", "path_type": "hardlink", "sha256": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "sha256_in_prefix": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "size_in_bytes": 4178}, {"_path": "include/generated_cuda_gl_interop_meta.h", "path_type": "hardlink", "sha256": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "sha256_in_prefix": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "size_in_bytes": 2492}, {"_path": "include/generated_cuda_meta.h", "path_type": "hardlink", "sha256": "1d6fb286031aaaa9d6d91237f8bbcecb85dcc965dfe7df701c22804977d15c25", "sha256_in_prefix": "1d6fb286031aaaa9d6d91237f8bbcecb85dcc965dfe7df701c22804977d15c25", "size_in_bytes": 90445}, {"_path": "include/generated_cuda_runtime_api_meta.h", "path_type": "hardlink", "sha256": "b4eb67577dfc2e9f3fab772415182cef2f255afb0a5fac46860904e46e17bf37", "sha256_in_prefix": "b4eb67577dfc2e9f3fab772415182cef2f255afb0a5fac46860904e46e17bf37", "size_in_bytes": 66458}, {"_path": "include/generated_cudart_removed_meta.h", "path_type": "hardlink", "sha256": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "sha256_in_prefix": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "size_in_bytes": 5334}, {"_path": "include/generated_nvtx_meta.h", "path_type": "hardlink", "sha256": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "sha256_in_prefix": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "size_in_bytes": 7760}, {"_path": "include/nvperf_common.h", "path_type": "hardlink", "sha256": "4a420fbd9c699b77db4a6e760f8dddaf350161fef63930094eb81a14df831c65", "sha256_in_prefix": "4a420fbd9c699b77db4a6e760f8dddaf350161fef63930094eb81a14df831c65", "size_in_bytes": 10695}, {"_path": "include/nvperf_cuda_host.h", "path_type": "hardlink", "sha256": "312d7e2ebfa9d061668f8be8094f83f7b8f402c0b3ef143599dfb8fb13c4e153", "sha256_in_prefix": "312d7e2ebfa9d061668f8be8094f83f7b8f402c0b3ef143599dfb8fb13c4e153", "size_in_bytes": 8496}, {"_path": "include/nvperf_host.h", "path_type": "hardlink", "sha256": "7dad2a64f877cf950997ec6f853a514e949b2a3cc7200044c4cd19ad3fa3b3f7", "sha256_in_prefix": "7dad2a64f877cf950997ec6f853a514e949b2a3cc7200044c4cd19ad3fa3b3f7", "size_in_bytes": 67817}, {"_path": "include/nvperf_target.h", "path_type": "hardlink", "sha256": "4d9de238d9796dcdedc200003a22c3245f7a96d0160a70f91597eb371884bfee", "sha256_in_prefix": "4d9de238d9796dcdedc200003a22c3245f7a96d0160a70f91597eb371884bfee", "size_in_bytes": 22048}, {"_path": "lib/checkpoint.dll", "path_type": "hardlink", "sha256": "656029088966ae385171aef41d4e213c67fce0a9f1f9c079cc28c19f2504f53c", "sha256_in_prefix": "656029088966ae385171aef41d4e213c67fce0a9f1f9c079cc28c19f2504f53c", "size_in_bytes": 379992}, {"_path": "lib/checkpoint.lib", "path_type": "hardlink", "sha256": "8307c5d5ce39f17522310bc5fbf7d101efb67c072a9e1d702152e52ca6407b4b", "sha256_in_prefix": "8307c5d5ce39f17522310bc5fbf7d101efb67c072a9e1d702152e52ca6407b4b", "size_in_bytes": 2256}, {"_path": "lib/cupti.lib", "path_type": "hardlink", "sha256": "984c68d490eaafff6ff7a4bcc8dadb0e07e661d6df5dad34932f4d5748a04b30", "sha256_in_prefix": "984c68d490eaafff6ff7a4bcc8dadb0e07e661d6df5dad34932f4d5748a04b30", "size_in_bytes": 37166}, {"_path": "lib/cupti64_2023.1.0.dll", "path_type": "hardlink", "sha256": "62c31fc18bf61da5ec623e2ee8909eaa17265508451d89756935031cfc071626", "sha256_in_prefix": "62c31fc18bf61da5ec623e2ee8909eaa17265508451d89756935031cfc071626", "size_in_bytes": 4327424}, {"_path": "lib/nvperf_host.dll", "path_type": "hardlink", "sha256": "5a5abb432e55d8b79d1cf55b21a41b144af9c90b94612f6d8f2a58fa600c63e6", "sha256_in_prefix": "5a5abb432e55d8b79d1cf55b21a41b144af9c90b94612f6d8f2a58fa600c63e6", "size_in_bytes": 22793304}, {"_path": "lib/nvperf_host.lib", "path_type": "hardlink", "sha256": "1f0b6283e791990802f405c68edba34a27e627bd2b29c7d31009452e6d5433a5", "sha256_in_prefix": "1f0b6283e791990802f405c68edba34a27e627bd2b29c7d31009452e6d5433a5", "size_in_bytes": 131054}, {"_path": "lib/nvperf_target.dll", "path_type": "hardlink", "sha256": "3875c62b47cf17a5eb935c329368c86d0cdaeaba1ed4d3d52adb7c60a531700c", "sha256_in_prefix": "3875c62b47cf17a5eb935c329368c86d0cdaeaba1ed4d3d52adb7c60a531700c", "size_in_bytes": 1996888}, {"_path": "lib/nvperf_target.lib", "path_type": "hardlink", "sha256": "6a2ca15147832d6eef4e27a90e325d8975ce35dd7cbb46046d52ec390fde7cc0", "sha256_in_prefix": "6a2ca15147832d6eef4e27a90e325d8975ce35dd7cbb46046d52ec390fde7cc0", "size_in_bytes": 101514}, {"_path": "lib/pcsamplingutil.dll", "path_type": "hardlink", "sha256": "3254cbb54703169b9eebc5c5d234ba9a69739a08aac24a9a27a33cd7773c236c", "sha256_in_prefix": "3254cbb54703169b9eebc5c5d234ba9a69739a08aac24a9a27a33cd7773c236c", "size_in_bytes": 68184}, {"_path": "lib/pcsamplingutil.lib", "path_type": "hardlink", "sha256": "01d56482cffb1fa296176ea35cf392df74825c00f44262da7a067d266c8fa6ac", "sha256_in_prefix": "01d56482cffb1fa296176ea35cf392df74825c00f44262da7a067d266c8fa6ac", "size_in_bytes": 2914}, {"_path": "samples/activity_trace_async/Makefile", "path_type": "hardlink", "sha256": "12c440088f8ac3d8c1dac8d5366307985f0bc7daf3451e2b60b1efa6a3cfccf2", "sha256_in_prefix": "12c440088f8ac3d8c1dac8d5366307985f0bc7daf3451e2b60b1efa6a3cfccf2", "size_in_bytes": 3029}, {"_path": "samples/activity_trace_async/activity_trace_async.cu", "path_type": "hardlink", "sha256": "7eb91bf42580c39ed23639076d11c53ae8752cafc5ca6aced01a4c02cc92955b", "sha256_in_prefix": "7eb91bf42580c39ed23639076d11c53ae8752cafc5ca6aced01a4c02cc92955b", "size_in_bytes": 5178}, {"_path": "samples/autorange_profiling/Makefile", "path_type": "hardlink", "sha256": "1bfc7b51062af3f74e83c9074616f9d6b845047842d1ab90f7ee8f53c9f6f238", "sha256_in_prefix": "1bfc7b51062af3f74e83c9074616f9d6b845047842d1ab90f7ee8f53c9f6f238", "size_in_bytes": 3776}, {"_path": "samples/autorange_profiling/auto_range_profiling.cu", "path_type": "hardlink", "sha256": "387baef8d8e6d2b0e9dc57ce526029b181d75262d876a79a0f18dfc18dabfebd", "sha256_in_prefix": "387baef8d8e6d2b0e9dc57ce526029b181d75262d876a79a0f18dfc18dabfebd", "size_in_bytes": 17871}, {"_path": "samples/callback_event/Makefile", "path_type": "hardlink", "sha256": "b10f8aa791c12c41d9a3664f1d21abc4e6229d7186313a584c4c22ede528bbbc", "sha256_in_prefix": "b10f8aa791c12c41d9a3664f1d21abc4e6229d7186313a584c4c22ede528bbbc", "size_in_bytes": 2990}, {"_path": "samples/callback_event/callback_event.cu", "path_type": "hardlink", "sha256": "8b685caca887e7010cf08588cd7c816b0e9f79b3cdf4e31fb07146d04e64b960", "sha256_in_prefix": "8b685caca887e7010cf08588cd7c816b0e9f79b3cdf4e31fb07146d04e64b960", "size_in_bytes": 9091}, {"_path": "samples/callback_metric/Makefile", "path_type": "hardlink", "sha256": "ef6d89c245317d980662d60d1ca5113b5aff9e31ddac7f50469eef5ecd1f190f", "sha256_in_prefix": "ef6d89c245317d980662d60d1ca5113b5aff9e31ddac7f50469eef5ecd1f190f", "size_in_bytes": 2998}, {"_path": "samples/callback_metric/callback_metric.cu", "path_type": "hardlink", "sha256": "39ad3498bb0fa4245ba07969092d1021072aa9d34b8a810e53c565af25a0829b", "sha256_in_prefix": "39ad3498bb0fa4245ba07969092d1021072aa9d34b8a810e53c565af25a0829b", "size_in_bytes": 16500}, {"_path": "samples/callback_profiling/Makefile", "path_type": "hardlink", "sha256": "5839cefbdaf5a656c2a217c7b3e49562d57a6d5c8c99e80afa54e9c240b8fdc0", "sha256_in_prefix": "5839cefbdaf5a656c2a217c7b3e49562d57a6d5c8c99e80afa54e9c240b8fdc0", "size_in_bytes": 3752}, {"_path": "samples/callback_profiling/callback_profiling.cu", "path_type": "hardlink", "sha256": "698a79a0bed533cb8641c160a112d94bfc2404ee1e60760354d98f6f193bff74", "sha256_in_prefix": "698a79a0bed533cb8641c160a112d94bfc2404ee1e60760354d98f6f193bff74", "size_in_bytes": 20445}, {"_path": "samples/callback_timestamp/Makefile", "path_type": "hardlink", "sha256": "7b6da305974a60829ff59d2344cfde5944f5849afebd784b8c207d9a2f1a11c6", "sha256_in_prefix": "7b6da305974a60829ff59d2344cfde5944f5849afebd784b8c207d9a2f1a11c6", "size_in_bytes": 3041}, {"_path": "samples/callback_timestamp/callback_timestamp.cu", "path_type": "hardlink", "sha256": "1e14f58e9af760b12786cb1521837a21251ffc33cb1c98b06d5edf1bad9e496c", "sha256_in_prefix": "1e14f58e9af760b12786cb1521837a21251ffc33cb1c98b06d5edf1bad9e496c", "size_in_bytes": 10119}, {"_path": "samples/checkpoint_kernels/Makefile", "path_type": "hardlink", "sha256": "2801743712133f99231c2eba16beb347868c7880c2a4a15df78a51b8ddbe8536", "sha256_in_prefix": "2801743712133f99231c2eba16beb347868c7880c2a4a15df78a51b8ddbe8536", "size_in_bytes": 3206}, {"_path": "samples/checkpoint_kernels/checkpoint_kernels.cu", "path_type": "hardlink", "sha256": "f29c4537031b35b05f43b64888f2f8bfdfa2257ef712d4f191b1c288e8cd3ba3", "sha256_in_prefix": "f29c4537031b35b05f43b64888f2f8bfdfa2257ef712d4f191b1c288e8cd3ba3", "size_in_bytes": 5114}, {"_path": "samples/common/helper_cupti.h", "path_type": "hardlink", "sha256": "27cb44df3934bba0f1ab6af4a6718ac654e55fbdbeb142a63bf7a04823819450", "sha256_in_prefix": "27cb44df3934bba0f1ab6af4a6718ac654e55fbdbeb142a63bf7a04823819450", "size_in_bytes": 7898}, {"_path": "samples/common/helper_cupti_activity.h", "path_type": "hardlink", "sha256": "a0fc50c7721749f5101acd66022e15f0f684634f1f69ce80a330ef25b826f5c8", "sha256_in_prefix": "a0fc50c7721749f5101acd66022e15f0f684634f1f69ce80a330ef25b826f5c8", "size_in_bytes": 97115}, {"_path": "samples/concurrent_profiling/Makefile", "path_type": "hardlink", "sha256": "f5028b8bc6cbd30fffb42dfc07d1420fa7107e9b2e39ee8670e6c336f18e0ed8", "sha256_in_prefix": "f5028b8bc6cbd30fffb42dfc07d1420fa7107e9b2e39ee8670e6c336f18e0ed8", "size_in_bytes": 3769}, {"_path": "samples/concurrent_profiling/concurrent_profiling.cu", "path_type": "hardlink", "sha256": "4d9414c98de80ba5d6eeb5ccef9e0018e2642bce76848163d592c996e3edd4bd", "sha256_in_prefix": "4d9414c98de80ba5d6eeb5ccef9e0018e2642bce76848163d592c996e3edd4bd", "size_in_bytes": 33130}, {"_path": "samples/cuda_graphs_trace/Makefile", "path_type": "hardlink", "sha256": "0d200da9d9b1ee63ab6e13b0c590a5498e5442376a5e923521f7a25d1cf94d39", "sha256_in_prefix": "0d200da9d9b1ee63ab6e13b0c590a5498e5442376a5e923521f7a25d1cf94d39", "size_in_bytes": 3010}, {"_path": "samples/cuda_graphs_trace/cuda_graphs_trace.cu", "path_type": "hardlink", "sha256": "44effc9c72067a945acc1e847a921de312955e82f1476ce906881207513124e8", "sha256_in_prefix": "44effc9c72067a945acc1e847a921de312955e82f1476ce906881207513124e8", "size_in_bytes": 10549}, {"_path": "samples/cuda_memory_trace/Makefile", "path_type": "hardlink", "sha256": "d6aed892b6d03008c7d001e471acbf6fa67017559ce4f8e2edcdbd111668aef7", "sha256_in_prefix": "d6aed892b6d03008c7d001e471acbf6fa67017559ce4f8e2edcdbd111668aef7", "size_in_bytes": 2983}, {"_path": "samples/cuda_memory_trace/memory_trace.cu", "path_type": "hardlink", "sha256": "43a96dd38b49d0fef1a69fb3fc685e0d825bbe3914f16b070032fb1fdac56239", "sha256_in_prefix": "43a96dd38b49d0fef1a69fb3fc685e0d825bbe3914f16b070032fb1fdac56239", "size_in_bytes": 5349}, {"_path": "samples/cupti_correlation/Makefile", "path_type": "hardlink", "sha256": "e2a99c7ae4cc9ddcc5ede5e880c05a596751ac7ce6ef7a31e460d20bc3519db6", "sha256_in_prefix": "e2a99c7ae4cc9ddcc5ede5e880c05a596751ac7ce6ef7a31e460d20bc3519db6", "size_in_bytes": 3009}, {"_path": "samples/cupti_correlation/cupti_correlation.cu", "path_type": "hardlink", "sha256": "92d29a4e09aa2716a6c42d9d710b8c14794674e9719f64b42847c4ff39e895d7", "sha256_in_prefix": "92d29a4e09aa2716a6c42d9d710b8c14794674e9719f64b42847c4ff39e895d7", "size_in_bytes": 10975}, {"_path": "samples/cupti_external_correlation/Makefile", "path_type": "hardlink", "sha256": "20de617504e87c3f95531030964366b3193e85a85ea89d0724e6c3c3b104d92d", "sha256_in_prefix": "20de617504e87c3f95531030964366b3193e85a85ea89d0724e6c3c3b104d92d", "size_in_bytes": 3064}, {"_path": "samples/cupti_external_correlation/cupti_external_correlation.cu", "path_type": "hardlink", "sha256": "c25f3f900f6235f25203c79b86026ba76f412d572402867b57e9d35d588f6d8a", "sha256_in_prefix": "c25f3f900f6235f25203c79b86026ba76f412d572402867b57e9d35d588f6d8a", "size_in_bytes": 8630}, {"_path": "samples/cupti_metric_properties/Makefile", "path_type": "hardlink", "sha256": "ced3f9cc14bc22e933d2ccdef032acf15176fbb912a44ced238f354f4346e692", "sha256_in_prefix": "ced3f9cc14bc22e933d2ccdef032acf15176fbb912a44ced238f354f4346e692", "size_in_bytes": 3813}, {"_path": "samples/cupti_metric_properties/cupti_metric_properties.cpp", "path_type": "hardlink", "sha256": "987c859ece78fb5088297663e03475872d928b2eab2c8c5441d621d8e75d5365", "sha256_in_prefix": "987c859ece78fb5088297663e03475872d928b2eab2c8c5441d621d8e75d5365", "size_in_bytes": 19589}, {"_path": "samples/cupti_nvtx/Makefile", "path_type": "hardlink", "sha256": "3ec2ef14424bfb8a960b03782ed6a305f1d797017197f89f4d77968f1efcb8ad", "sha256_in_prefix": "3ec2ef14424bfb8a960b03782ed6a305f1d797017197f89f4d77968f1efcb8ad", "size_in_bytes": 3146}, {"_path": "samples/cupti_nvtx/cupti_nvtx.cu", "path_type": "hardlink", "sha256": "0030d60f0c61f1df8348c747d4c268891091e95fd095469988d8e6bdc4a53434", "sha256_in_prefix": "0030d60f0c61f1df8348c747d4c268891091e95fd095469988d8e6bdc4a53434", "size_in_bytes": 8798}, {"_path": "samples/cupti_query/Makefile", "path_type": "hardlink", "sha256": "51bb03968b14b48bef496a5f925d5bb38c4599b0caa0c8d628a9a5eb06b0b07b", "sha256_in_prefix": "51bb03968b14b48bef496a5f925d5bb38c4599b0caa0c8d628a9a5eb06b0b07b", "size_in_bytes": 2742}, {"_path": "samples/cupti_query/cupti_query.cpp", "path_type": "hardlink", "sha256": "4e87fbcf950823390de9cf0594a616d407c8e4c1a3cec94d9becbaf012ef9c5c", "sha256_in_prefix": "4e87fbcf950823390de9cf0594a616d407c8e4c1a3cec94d9becbaf012ef9c5c", "size_in_bytes": 16218}, {"_path": "samples/cupti_trace_injection/Makefile", "path_type": "hardlink", "sha256": "3850195b8e1800cbcf1ef3acca2444239b7df458078e8bdea07a8304c17d68e4", "sha256_in_prefix": "3850195b8e1800cbcf1ef3acca2444239b7df458078e8bdea07a8304c17d68e4", "size_in_bytes": 2044}, {"_path": "samples/cupti_trace_injection/README.txt", "path_type": "hardlink", "sha256": "3ce08542b8a24fc2406ef8ddb504c12a1bfa417792b99db39582b42ca39e8ab6", "sha256_in_prefix": "3ce08542b8a24fc2406ef8ddb504c12a1bfa417792b99db39582b42ca39e8ab6", "size_in_bytes": 1416}, {"_path": "samples/cupti_trace_injection/cupti_trace_injection.cpp", "path_type": "hardlink", "sha256": "b5b91c1c628ccc9a15707b6b6f2dfec680e4a53016cdd0efaf58ae39b8b2a635", "sha256_in_prefix": "b5b91c1c628ccc9a15707b6b6f2dfec680e4a53016cdd0efaf58ae39b8b2a635", "size_in_bytes": 12401}, {"_path": "samples/event_multi_gpu/Makefile", "path_type": "hardlink", "sha256": "b98195d417a2c99a8ca5ad73e4ef3996a38efdc3877ff8a9f2539cfd4f67d094", "sha256_in_prefix": "b98195d417a2c99a8ca5ad73e4ef3996a38efdc3877ff8a9f2539cfd4f67d094", "size_in_bytes": 2775}, {"_path": "samples/event_multi_gpu/event_multi_gpu.cu", "path_type": "hardlink", "sha256": "8570dcf01ef5d1aafb22bc9ad1e7fd229d4b2683ecc9ac88531e6db2850c337d", "sha256_in_prefix": "8570dcf01ef5d1aafb22bc9ad1e7fd229d4b2683ecc9ac88531e6db2850c337d", "size_in_bytes": 5299}, {"_path": "samples/event_sampling/Makefile", "path_type": "hardlink", "sha256": "f1926d831b3ce10ce029015d8810072794d39a8cc1cf2508d06c04e40c1ff1aa", "sha256_in_prefix": "f1926d831b3ce10ce029015d8810072794d39a8cc1cf2508d06c04e40c1ff1aa", "size_in_bytes": 2989}, {"_path": "samples/event_sampling/event_sampling.cu", "path_type": "hardlink", "sha256": "4b472a2f775a467f18aba86ec295a06458b98cec08c4d74855e7a29cba0a84f3", "sha256_in_prefix": "4b472a2f775a467f18aba86ec295a06458b98cec08c4d74855e7a29cba0a84f3", "size_in_bytes": 9439}, {"_path": "samples/extensions/include/c_util/FileOp.h", "path_type": "hardlink", "sha256": "d52c91b3d9f7eae23cbe966a63ddcb4635bc762b8fbfc0b66686e3a995be7cd1", "sha256_in_prefix": "d52c91b3d9f7eae23cbe966a63ddcb4635bc762b8fbfc0b66686e3a995be7cd1", "size_in_bytes": 1217}, {"_path": "samples/extensions/include/c_util/ScopeExit.h", "path_type": "hardlink", "sha256": "fa8225ef9c179a5748c2eacdb39a85a149c634aa1fe69dcf9ba08161f2d77975", "sha256_in_prefix": "fa8225ef9c179a5748c2eacdb39a85a149c634aa1fe69dcf9ba08161f2d77975", "size_in_bytes": 504}, {"_path": "samples/extensions/include/profilerhost_util/Eval.h", "path_type": "hardlink", "sha256": "3021012d2d10f82a92eb3e9a2c896b6f25771efc89d944a7ed31cf1fb9dff075", "sha256_in_prefix": "3021012d2d10f82a92eb3e9a2c896b6f25771efc89d944a7ed31cf1fb9dff075", "size_in_bytes": 2142}, {"_path": "samples/extensions/include/profilerhost_util/List.h", "path_type": "hardlink", "sha256": "f60e350724b25fb30c1ab96753b4661186092c2ff2801a3549e9aa1451f56f96", "sha256_in_prefix": "f60e350724b25fb30c1ab96753b4661186092c2ff2801a3549e9aa1451f56f96", "size_in_bytes": 1685}, {"_path": "samples/extensions/include/profilerhost_util/Metric.h", "path_type": "hardlink", "sha256": "1932044dfd049882a1579c90445abda7e8fd391e34e720ceb38505722c56b07c", "sha256_in_prefix": "1932044dfd049882a1579c90445abda7e8fd391e34e720ceb38505722c56b07c", "size_in_bytes": 1862}, {"_path": "samples/extensions/include/profilerhost_util/Parser.h", "path_type": "hardlink", "sha256": "475388560277a08dedb6b9cf70b5a91e69e49cbff3a560cbfe9fc925538b0923", "sha256_in_prefix": "475388560277a08dedb6b9cf70b5a91e69e49cbff3a560cbfe9fc925538b0923", "size_in_bytes": 1957}, {"_path": "samples/extensions/include/profilerhost_util/Utils.h", "path_type": "hardlink", "sha256": "fc134329860310fb70c6eefc84fcca83553e1e7384181b37db45b0954ca8ea8f", "sha256_in_prefix": "fc134329860310fb70c6eefc84fcca83553e1e7384181b37db45b0954ca8ea8f", "size_in_bytes": 4604}, {"_path": "samples/extensions/src/profilerhost_util/Eval.cpp", "path_type": "hardlink", "sha256": "15b469bbc19fb55a8da3db66749bc6d5803df73cc5b2608c953a91e7c6924ae8", "sha256_in_prefix": "15b469bbc19fb55a8da3db66749bc6d5803df73cc5b2608c953a91e7c6924ae8", "size_in_bytes": 15510}, {"_path": "samples/extensions/src/profilerhost_util/List.cpp", "path_type": "hardlink", "sha256": "fd81c7cbc4b5f25089b5678c6dbc884a8abe70c9be80074d349de65f9dd68ab8", "sha256_in_prefix": "fd81c7cbc4b5f25089b5678c6dbc884a8abe70c9be80074d349de65f9dd68ab8", "size_in_bytes": 9847}, {"_path": "samples/extensions/src/profilerhost_util/Makefile", "path_type": "hardlink", "sha256": "b0f2557e8e312d5b8d892223ac1550fc4bbb566d3c3171864e6e49e570e61f98", "sha256_in_prefix": "b0f2557e8e312d5b8d892223ac1550fc4bbb566d3c3171864e6e49e570e61f98", "size_in_bytes": 3232}, {"_path": "samples/extensions/src/profilerhost_util/Metric.cpp", "path_type": "hardlink", "sha256": "ae4ddf8ae11d5f9ceb3de2603721ec9e1d1e5aa7501a27238efce65006ada325", "sha256_in_prefix": "ae4ddf8ae11d5f9ceb3de2603721ec9e1d1e5aa7501a27238efce65006ada325", "size_in_bytes": 12413}, {"_path": "samples/nested_range_profiling/Makefile", "path_type": "hardlink", "sha256": "ca3b4669a6fe02d1a09c5ef39d32861dab3577875ca781c1f53972efecb079ce", "sha256_in_prefix": "ca3b4669a6fe02d1a09c5ef39d32861dab3577875ca781c1f53972efecb079ce", "size_in_bytes": 3793}, {"_path": "samples/nested_range_profiling/nested_range_profiling.cu", "path_type": "hardlink", "sha256": "367c76c1c79cad0defb8cb3c0a56c961946af5f6bea30d5c044cb17806880b02", "sha256_in_prefix": "367c76c1c79cad0defb8cb3c0a56c961946af5f6bea30d5c044cb17806880b02", "size_in_bytes": 19970}, {"_path": "samples/nvlink_bandwidth/Makefile", "path_type": "hardlink", "sha256": "4d81902e297dd7303fa0a22cf6ef948661cb79a59575f1ce586ecd2f541ef31b", "sha256_in_prefix": "4d81902e297dd7303fa0a22cf6ef948661cb79a59575f1ce586ecd2f541ef31b", "size_in_bytes": 3003}, {"_path": "samples/nvlink_bandwidth/nvlink_bandwidth.cu", "path_type": "hardlink", "sha256": "1adec9669c2cbd2cb2923c590fa958889108fcd98bdf4b8345bdfdc39c5c76d3", "sha256_in_prefix": "1adec9669c2cbd2cb2923c590fa958889108fcd98bdf4b8345bdfdc39c5c76d3", "size_in_bytes": 17318}, {"_path": "samples/pc_sampling/Makefile", "path_type": "hardlink", "sha256": "d0b314aa518c1f23ccff4d15fcb30bfd829913159f14eb1397ade652eb8b4738", "sha256_in_prefix": "d0b314aa518c1f23ccff4d15fcb30bfd829913159f14eb1397ade652eb8b4738", "size_in_bytes": 2187}, {"_path": "samples/pc_sampling/pc_sampling.cu", "path_type": "hardlink", "sha256": "b33c5f30d7452667a8e41f59ebe21cd2905e12e731597f5378ef9aaaa0bb6c7a", "sha256_in_prefix": "b33c5f30d7452667a8e41f59ebe21cd2905e12e731597f5378ef9aaaa0bb6c7a", "size_in_bytes": 4349}, {"_path": "samples/pc_sampling_continuous/Makefile", "path_type": "hardlink", "sha256": "154278c3d1e60939ccd806137eca646d824e1f88cff9d1b1a8663aba7fad76e9", "sha256_in_prefix": "154278c3d1e60939ccd806137eca646d824e1f88cff9d1b1a8663aba7fad76e9", "size_in_bytes": 2033}, {"_path": "samples/pc_sampling_continuous/README.txt", "path_type": "hardlink", "sha256": "6a8a3422ab3929ef6eec59b5d99776b3dc11b63875fab4974535823d059e5d43", "sha256_in_prefix": "6a8a3422ab3929ef6eec59b5d99776b3dc11b63875fab4974535823d059e5d43", "size_in_bytes": 1566}, {"_path": "samples/pc_sampling_continuous/libpc_sampling_continuous.pl", "path_type": "hardlink", "sha256": "2440454a9b9128caba745046503c5ddc04d37758aa23dffd1c159ccf78374806", "sha256_in_prefix": "2440454a9b9128caba745046503c5ddc04d37758aa23dffd1c159ccf78374806", "size_in_bytes": 9035}, {"_path": "samples/pc_sampling_continuous/pc_sampling_continuous.cpp", "path_type": "hardlink", "sha256": "8c8f3476a802eba0fb6d7da551e2a3a94be3af331685b74cd1ece7e4ce78f596", "sha256_in_prefix": "8c8f3476a802eba0fb6d7da551e2a3a94be3af331685b74cd1ece7e4ce78f596", "size_in_bytes": 45621}, {"_path": "samples/pc_sampling_start_stop/Makefile", "path_type": "hardlink", "sha256": "15ed56f403ad6b45e84772355f0ba9226946e86d5cc88d52ac00675052e890ec", "sha256_in_prefix": "15ed56f403ad6b45e84772355f0ba9226946e86d5cc88d52ac00675052e890ec", "size_in_bytes": 2249}, {"_path": "samples/pc_sampling_start_stop/pc_sampling_start_stop.cu", "path_type": "hardlink", "sha256": "0fffde6c5c006cc8398f74da97bcc77f97719910fb98eadd4a181572273bb475", "sha256_in_prefix": "0fffde6c5c006cc8398f74da97bcc77f97719910fb98eadd4a181572273bb475", "size_in_bytes": 17702}, {"_path": "samples/pc_sampling_utility/Makefile", "path_type": "hardlink", "sha256": "edfc989e40e52b6fad65d962b0dc7e7c490f9527f727633cbb81685a478eca80", "sha256_in_prefix": "edfc989e40e52b6fad65d962b0dc7e7c490f9527f727633cbb81685a478eca80", "size_in_bytes": 1901}, {"_path": "samples/pc_sampling_utility/README.txt", "path_type": "hardlink", "sha256": "aca22ce313f3abcb84f676ae0afe088ddb12f01a9f88f38f4b2f637c1dbed5ca", "sha256_in_prefix": "aca22ce313f3abcb84f676ae0afe088ddb12f01a9f88f38f4b2f637c1dbed5ca", "size_in_bytes": 992}, {"_path": "samples/pc_sampling_utility/pc_sampling_utility.cpp", "path_type": "hardlink", "sha256": "5c1b34d26cd4f98ca1228a2dfaf9c169a2ec5ca7975b85ae139288c5f38d027f", "sha256_in_prefix": "5c1b34d26cd4f98ca1228a2dfaf9c169a2ec5ca7975b85ae139288c5f38d027f", "size_in_bytes": 1228}, {"_path": "samples/pc_sampling_utility/pc_sampling_utility_helper.h", "path_type": "hardlink", "sha256": "ead5db7a6e744717828a788ff1c9f0437fae2d9f044e2824731d50fef366d417", "sha256_in_prefix": "ead5db7a6e744717828a788ff1c9f0437fae2d9f044e2824731d50fef366d417", "size_in_bytes": 26799}, {"_path": "samples/sass_source_map/Makefile", "path_type": "hardlink", "sha256": "896dea0e6dfb628713293c69fa4118109405d113b2469ebf314cb7c3764c8153", "sha256_in_prefix": "896dea0e6dfb628713293c69fa4118109405d113b2469ebf314cb7c3764c8153", "size_in_bytes": 2985}, {"_path": "samples/sass_source_map/sass_source_map.cu", "path_type": "hardlink", "sha256": "e6ba10142b6851b27e73ef2980afcd3954bbd6c6452ec0f6bebba01c87527c88", "sha256_in_prefix": "e6ba10142b6851b27e73ef2980afcd3954bbd6c6452ec0f6bebba01c87527c88", "size_in_bytes": 5272}, {"_path": "samples/unified_memory/Makefile", "path_type": "hardlink", "sha256": "47aab797850106768a3472042d165e461b6915ab79d4f3482d550e95762c7090", "sha256_in_prefix": "47aab797850106768a3472042d165e461b6915ab79d4f3482d550e95762c7090", "size_in_bytes": 3100}, {"_path": "samples/unified_memory/unified_memory.cu", "path_type": "hardlink", "sha256": "b425245d280bc9a775e115979b67bc7e214c3bd7c9b1f20fa95ee1bde5d6d464", "sha256_in_prefix": "b425245d280bc9a775e115979b67bc7e214c3bd7c9b1f20fa95ee1bde5d6d464", "size_in_bytes": 4329}, {"_path": "samples/userrange_profiling/Makefile", "path_type": "hardlink", "sha256": "f06b8b99f52271789dc876b70be34612dd1e92ebf50a08abf3ebd0ab51d8650e", "sha256_in_prefix": "f06b8b99f52271789dc876b70be34612dd1e92ebf50a08abf3ebd0ab51d8650e", "size_in_bytes": 3774}, {"_path": "samples/userrange_profiling/user_range_profiling.cu", "path_type": "hardlink", "sha256": "080b3ab6022884c5553a72d0c1788e532aee023f4e92da6a133b60e38c413576", "sha256_in_prefix": "080b3ab6022884c5553a72d0c1788e532aee023f4e92da6a133b60e38c413576", "size_in_bytes": 17755}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 12195721, "subdir": "win-64", "timestamp": 1675398947000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-cupti-12.1.62-0.tar.bz2", "version": "12.1.62"}