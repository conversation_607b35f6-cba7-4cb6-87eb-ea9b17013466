<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityAutoBoostState Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityAutoBoostState Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityAutoBoostState" -->Device auto boost state structure.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAutoBoostState.html#03e0666defb373bba5f438637dd85c71">enabled</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityAutoBoostState.html#fe424a40cbe3ec6053530532d088b4d4">pid</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure defines auto boost state for a device. See function <a class="el" href="group__CUPTI__ACTIVITY__API.html#g1ac1cce5ce788b9f2c679d13e982384b">cuptiGetAutoBoostState</a> <hr><h2>Field Documentation</h2>
<a class="anchor" name="03e0666defb373bba5f438637dd85c71"></a><!-- doxytag: member="CUpti_ActivityAutoBoostState::enabled" ref="03e0666defb373bba5f438637dd85c71" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityAutoBoostState.html#03e0666defb373bba5f438637dd85c71">CUpti_ActivityAutoBoostState::enabled</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returned auto boost state. 1 is returned in case auto boost is enabled, 0 otherwise 
</div>
</div><p>
<a class="anchor" name="fe424a40cbe3ec6053530532d088b4d4"></a><!-- doxytag: member="CUpti_ActivityAutoBoostState::pid" ref="fe424a40cbe3ec6053530532d088b4d4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityAutoBoostState.html#fe424a40cbe3ec6053530532d088b4d4">CUpti_ActivityAutoBoostState::pid</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Id of process that has set the current boost state. The value will be CUPTI_AUTO_BOOST_INVALID_CLIENT_PID if the user does not have the permission to query process ids or there is an error in querying the process id. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
