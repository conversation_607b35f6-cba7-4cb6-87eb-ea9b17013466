<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>SanitizerApi: Sanitizer Memory API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Sanitizer Memory API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__MEMORY__API.html#gfa9f57591757dec33a5055948e30a836">sanitizerAlloc</a> (CUcontext ctx, void **devPtr, size_t size)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Allocate memory on the device.  <a href="#gfa9f57591757dec33a5055948e30a836"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__MEMORY__API.html#g097b5e253039ac30a3b7fdb2f7de0481">sanitizerAllocHost</a> (CUcontext ctx, void **devPtr, size_t size)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Allocate host pinned memory.  <a href="#g097b5e253039ac30a3b7fdb2f7de0481"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__MEMORY__API.html#g389679a46d26affa64a0d4bef69a3c33">sanitizerFree</a> (CUcontext ctx, void *devPtr)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Frees memory on the device.  <a href="#g389679a46d26affa64a0d4bef69a3c33"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__MEMORY__API.html#gc2d870dddb001b8cac728fb0ed6727a7">sanitizerFreeHost</a> (CUcontext ctx, void *devPtr)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Frees host memory.  <a href="#gc2d870dddb001b8cac728fb0ed6727a7"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__MEMORY__API.html#g6f7fea96823fa4b2593361f4817848a8">sanitizerMemcpyDeviceToHost</a> (void *dst, void *src, size_t count, Sanitizer_StreamHandle stream)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Copies data from device to host.  <a href="#g6f7fea96823fa4b2593361f4817848a8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__MEMORY__API.html#g98efdd36e482d83b73b23d31a16849ff">sanitizerMemcpyHostToDeviceAsync</a> (void *dst, void *src, size_t count, Sanitizer_StreamHandle stream)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Copies data from host to device.  <a href="#g98efdd36e482d83b73b23d31a16849ff"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__MEMORY__API.html#gd2c72da4658a49c8a0b0c6ba4110e2bc">sanitizerMemset</a> (void *devPtr, int value, size_t count, Sanitizer_StreamHandle stream)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initializes or sets device memory to a value.  <a href="#gd2c72da4658a49c8a0b0c6ba4110e2bc"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the Sanitizer Memory API. <hr><h2>Function Documentation</h2>
<a class="anchor" name="gfa9f57591757dec33a5055948e30a836"></a><!-- doxytag: member="sanitizer_memory.h::sanitizerAlloc" ref="gfa9f57591757dec33a5055948e30a836" args="(CUcontext ctx, void **devPtr, size_t size)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerAlloc           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void **&nbsp;</td>
          <td class="paramname"> <em>devPtr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>size</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaMalloc that can be called within a callback function. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>ctx</em>&nbsp;</td><td>Context for the allocation. If NULL, the current context will be used. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>devPtr</em>&nbsp;</td><td>Pointer to allocated device memory </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>size</em>&nbsp;</td><td>Allocation size in bytes </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g097b5e253039ac30a3b7fdb2f7de0481"></a><!-- doxytag: member="sanitizer_memory.h::sanitizerAllocHost" ref="g097b5e253039ac30a3b7fdb2f7de0481" args="(CUcontext ctx, void **devPtr, size_t size)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerAllocHost           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void **&nbsp;</td>
          <td class="paramname"> <em>devPtr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>size</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaMallocHost that can be called within a callback function. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>ctx</em>&nbsp;</td><td>Context for the allocation. If NULL, the current context will be used. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>devPtr</em>&nbsp;</td><td>Pointer to allocated host memory </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>size</em>&nbsp;</td><td>Allocation size in bytes </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g389679a46d26affa64a0d4bef69a3c33"></a><!-- doxytag: member="sanitizer_memory.h::sanitizerFree" ref="g389679a46d26affa64a0d4bef69a3c33" args="(CUcontext ctx, void *devPtr)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerFree           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>devPtr</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaFree that can be called within a callback function. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>ctx</em>&nbsp;</td><td>Context for the allocation. If NULL, the current context will be used. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>devPtr</em>&nbsp;</td><td>Device pointer to memory to free </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc2d870dddb001b8cac728fb0ed6727a7"></a><!-- doxytag: member="sanitizer_memory.h::sanitizerFreeHost" ref="gc2d870dddb001b8cac728fb0ed6727a7" args="(CUcontext ctx, void *devPtr)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerFreeHost           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>ctx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>devPtr</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaFreeHost that can be called within a callback function. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>ctx</em>&nbsp;</td><td>Context for the allocation. If NULL, the current context will be used. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>devPtr</em>&nbsp;</td><td>Host pointer to memory to free </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g6f7fea96823fa4b2593361f4817848a8"></a><!-- doxytag: member="sanitizer_memory.h::sanitizerMemcpyDeviceToHost" ref="g6f7fea96823fa4b2593361f4817848a8" args="(void *dst, void *src, size_t count, Sanitizer_StreamHandle stream)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerMemcpyDeviceToHost           </td>
          <td>(</td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>dst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>src</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>count</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Sanitizer_StreamHandle&nbsp;</td>
          <td class="paramname"> <em>stream</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaMemcpy that can be called within a callback function. The function will return once the copy has completed. If the function is called from a SANITIZER_CB_DOMAIN_LAUNCH, SANITIZER_CB_DOMAIN_MEMCPY or SANITIZER_CB_DOMAIN_MEMSET callback, only pinned host memory may be used as destination. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>dst</em>&nbsp;</td><td>Destination memory address </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>src</em>&nbsp;</td><td>Source memory address </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>count</em>&nbsp;</td><td>Size in bytes to copy </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>stream</em>&nbsp;</td><td>Stream handle. If NULL, the NULL stream will be used. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g98efdd36e482d83b73b23d31a16849ff"></a><!-- doxytag: member="sanitizer_memory.h::sanitizerMemcpyHostToDeviceAsync" ref="g98efdd36e482d83b73b23d31a16849ff" args="(void *dst, void *src, size_t count, Sanitizer_StreamHandle stream)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerMemcpyHostToDeviceAsync           </td>
          <td>(</td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>dst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>src</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>count</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Sanitizer_StreamHandle&nbsp;</td>
          <td class="paramname"> <em>stream</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaMemcpyAsync that can be called within a callback function. The function will return once the pageable buffer has been copied to the staging memory for DMA transfer to device memory, but the DMA to final destination may not have completed. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>dst</em>&nbsp;</td><td>Destination memory address </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>src</em>&nbsp;</td><td>Source memory address </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>count</em>&nbsp;</td><td>Size in bytes to copy </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>stream</em>&nbsp;</td><td>Stream handle. If NULL, the NULL stream will be used. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd2c72da4658a49c8a0b0c6ba4110e2bc"></a><!-- doxytag: member="sanitizer_memory.h::sanitizerMemset" ref="gd2c72da4658a49c8a0b0c6ba4110e2bc" args="(void *devPtr, int value, size_t count, Sanitizer_StreamHandle stream)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerMemset           </td>
          <td>(</td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>devPtr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>count</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Sanitizer_StreamHandle&nbsp;</td>
          <td class="paramname"> <em>stream</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Equivalent of cudaMemset that can be called within a callback function. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>devPtr</em>&nbsp;</td><td>Pointer to device memory </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>value to set for each byte of specified memory </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>count</em>&nbsp;</td><td>Size in bytes to set </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>stream</em>&nbsp;</td><td>Stream handle. If NULL, the NULL stream will be used. </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Tue Jan 24 19:43:08 2023 for SanitizerApi by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
