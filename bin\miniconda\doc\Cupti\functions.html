<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-us" xml:lang="en-us">
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8"></meta>
      <meta http-equiv="X-UA-Compatible" content="IE=edge"></meta>
      <meta name="copyright" content="(C) Copyright 2005"></meta>
      <meta name="DC.rights.owner" content="(C) Copyright 2005"></meta>
      <meta name="DC.Type" content="reference"></meta>
      <meta name="DC.Title" content="Data Fields"></meta>
      <meta name="DC.Format" content="XHTML"></meta>
      <meta name="DC.Identifier" content="functions"></meta>
      <link rel="stylesheet" type="text/css" href="../common/formatting/commonltr.css"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/site.css"></link>
      <title>CUPTI :: CUPTI Documentation</title>
      <!--[if lt IE 9]>
      <script src="../common/formatting/html5shiv-printshiv.min.js"></script>
      <![endif]-->
      <script type="text/javascript" charset="utf-8" src="../common/scripts/tynt/tynt.js"></script>
      --&gt;
      
      <script src="https://assets.adobedtm.com/5d4962a43b79/c1061d2c5e7b/launch-191c2462b890.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.ba-hashchange.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.scrollintoview.min.js"></script>
      <script type="text/javascript" src="../search/htmlFileList.js"></script>
      <script type="text/javascript" src="../search/htmlFileInfoList.js"></script>
      <script type="text/javascript" src="../search/nwSearchFnt.min.js"></script>
      <script type="text/javascript" src="../search/stemmers/en_stemmer.min.js"></script>
      <script type="text/javascript" src="../search/index-1.js"></script>
      <script type="text/javascript" src="../search/index-2.js"></script>
      <script type="text/javascript" src="../search/index-3.js"></script>
      <link rel="canonical" href="https://docs.nvidia.com/cupti/Cupti/index.html"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/qwcode.highlight.css"></link>
   </head>
   <body>
      
      <header id="header"><span id="company">NVIDIA</span><span id="site-title">CUPTI Documentation</span><form id="search" method="get" action="search">
            <input type="text" name="search-text"></input><fieldset id="search-location">
               <legend>Search In:</legend>
               <label><input type="radio" name="search-type" value="site"></input>Entire Site</label>
               <label><input type="radio" name="search-type" value="document"></input>Just This Document</label></fieldset>
            <button type="reset">clear search</button>
            <button id="submit" type="submit">search</button></form>
      </header>
      <div id="site-content">
         <nav id="site-nav">
            <div class="category closed"><a href="../index.html" title="The root of the site.">CUPTI
                  v12.1</a></div>
            <div class="category"><a href="index.html" title="CUPTI">CUPTI</a></div>
            <ul>
               <li>
                  <div class="section-link"><a href="r_overview.html#r_overview">Overview</a></div>
               </li>
               <li>
                  <div class="section-link"><a href="release_notes.html#release_notes">1.&nbsp;Release Notes</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="release_notes.html#whats-new">1.1.&nbsp;Release Notes</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.1">1.1.1.&nbsp;Updates in CUDA 12.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.0.1">1.1.2.&nbsp;Updates in CUDA 12.0 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.0">1.1.3.&nbsp;Updates in CUDA 12.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.8">1.1.4.&nbsp;Updates in CUDA 11.8</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.7.1">1.1.5.&nbsp;Updates in CUDA 11.7 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.7">1.1.6.&nbsp;Updates in CUDA 11.7</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.6.1">1.1.7.&nbsp;Updates in CUDA 11.6 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.6">1.1.8.&nbsp;Updates in CUDA 11.6</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.5.1">1.1.9.&nbsp;Updates in CUDA 11.5 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.5">1.1.10.&nbsp;Updates in CUDA 11.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.4.1">1.1.11.&nbsp;Updates in CUDA 11.4 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.4">1.1.12.&nbsp;Updates in CUDA 11.4</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.3">1.1.13.&nbsp;Updates in CUDA 11.3</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.2">1.1.14.&nbsp;Updates in CUDA 11.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.1">1.1.15.&nbsp;Updates in CUDA 11.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.0">1.1.16.&nbsp;Updates in CUDA 11.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.2">1.1.17.&nbsp;Updates in CUDA 10.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1.2">1.1.18.&nbsp;Updates in CUDA 10.1 Update 2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1.1">1.1.19.&nbsp;Updates in CUDA 10.1 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1">1.1.20.&nbsp;Updates in CUDA 10.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.0">1.1.21.&nbsp;Updates in CUDA 10.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.2">1.1.22.&nbsp;Updates in CUDA 9.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.1">1.1.23.&nbsp;Updates in CUDA 9.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.0">1.1.24.&nbsp;Updates in CUDA 9.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_8.0">1.1.25.&nbsp;Updates in CUDA 8.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_7.5">1.1.26.&nbsp;Updates in CUDA 7.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_7.0">1.1.27.&nbsp;Updates in CUDA 7.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_6.5">1.1.28.&nbsp;Updates in CUDA 6.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_6.0">1.1.29.&nbsp;Updates in CUDA 6.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_5.5">1.1.30.&nbsp;Updates in CUDA 5.5</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="release_notes.html#known_issues">1.2.&nbsp;Known Issues</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#known_issues_profiling">1.2.1.&nbsp;Profiling</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="release_notes.html#known_issues_event_and_metric">1.2.1.1.&nbsp;Event and Metric API</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="release_notes.html#known_issues_profiling_and_perfworks">1.2.1.2.&nbsp;Profiling and Perfworks Metric API</a></div>
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="release_notes.html#support">1.3.&nbsp;Support</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#platform_support">1.3.1.&nbsp;Platform Support</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#gpu_support">1.3.2.&nbsp;GPU Support</a></div>
                           </li>
                        </ul>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_main.html#r_main">2.&nbsp;Usage</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_compatibility_requirements">2.1.&nbsp;CUPTI Compatibility and Requirements</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_initialization">2.2.&nbsp;CUPTI Initialization</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_activity">2.3.&nbsp;CUPTI Activity API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_source_correlation">2.3.1.&nbsp;SASS Source Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling">2.3.2.&nbsp;PC Sampling</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_nvlink">2.3.3.&nbsp;NVLink</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_openacc">2.3.4.&nbsp;OpenACC</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_graphs">2.3.5.&nbsp;CUDA Graphs</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_ext_correlation">2.3.6.&nbsp;External Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_dynamic_detach">2.3.7.&nbsp;Dynamic Attach and Detach</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_callback_api">2.4.&nbsp;CUPTI Callback API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_driver_runtime_api_callback">2.4.1.&nbsp;Driver and Runtime API Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_resource_callbacks">2.4.2.&nbsp;Resource Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_synchronization_callbacks">2.4.3.&nbsp;Synchronization Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_nvtx_callbacks">2.4.4.&nbsp;NVIDIA Tools Extension Callbacks</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_event_api">2.5.&nbsp;CUPTI Event API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_collecting_kernel_execution_events">2.5.1.&nbsp;Collecting Kernel Execution Events</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_sampling_events">2.5.2.&nbsp;Sampling Events</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_metric_api">2.6.&nbsp;CUPTI Metric API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#metrics-reference">2.6.1.&nbsp;Metrics Reference</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-5x">2.6.1.1.&nbsp;Metrics for Capability 5.x</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-6x">2.6.1.2.&nbsp;Metrics for Capability 6.x</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-7x">2.6.1.3.&nbsp;Metrics for Capability 7.0</a></div>
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_profiler">2.7.&nbsp;CUPTI Profiling API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_multi_pass_collection">2.7.1.&nbsp;Multi Pass Collection</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_range_profiling">2.7.2.&nbsp;Range Profiling</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_profiler_auto_range">2.7.2.1.&nbsp;Auto Range</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_profiler_user_range">2.7.2.2.&nbsp;User Range</a></div>
                                 </li>
                              </ul>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_profiler_definitions">2.7.3.&nbsp;CUPTI Profiler Definitions</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_profiling_missing_features">2.7.4.&nbsp;Differences from event and metric APIs</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_host_metrics_api">2.8.&nbsp;Perfworks Metric API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_host_derived_metrics_api">2.8.1.&nbsp;Derived metrics</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_host_raw_metrics_api">2.8.2.&nbsp;Raw Metrics</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#metrics_map_table_70">2.8.3.&nbsp;Metrics Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#events_map_table_70">2.8.4.&nbsp;Events Mapping Table</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_profiling_migration">2.9.&nbsp;Migration to the Profiling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_pc_sampling_api">2.10.&nbsp;CUPTI PC Sampling API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_config_attrib">2.10.1.&nbsp;Configuration Attributes</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_stall_reasons_mapping">2.10.2.&nbsp;Stall Reasons Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_struct_mapping">2.10.3.&nbsp;Data Structure Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_flush">2.10.4.&nbsp;Data flushing</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_source_correlation">2.10.5.&nbsp;SASS Source Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_usage">2.10.6.&nbsp;API Usage</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_limitations">2.10.7.&nbsp;Limitations</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_checkpoint_api">2.11.&nbsp;CUPTI Checkpoint API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_usage">2.11.1.&nbsp;Usage</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_restrictions">2.11.2.&nbsp;Restrictions</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_samples">2.11.3.&nbsp;Examples</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_overhead">2.12.&nbsp;CUPTI overhead</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_overhead_tracing">2.12.1.&nbsp;Tracing Overhead</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_overhead_tracing_execution">2.12.1.1.&nbsp;Execution overhead</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_overhead_tracing_memory">2.12.1.2.&nbsp;Memory overhead</a></div>
                                 </li>
                              </ul>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_overhead_profiling">2.12.2.&nbsp;Profiling Overhead</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_samples">2.13.&nbsp;Samples</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_library_support.html#r_library_support">3.&nbsp;Library Support</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_library_support.html#r_library_support_optix">3.1.&nbsp;OptiX</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_special_configurations.html#r_special_configurations">4.&nbsp;Special Configurations</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_mig">4.1.&nbsp;Multi-Instance GPU (MIG)</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_vgpu">4.2.&nbsp;NVIDIA Virtual GPU (vGPU)</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_wsl">4.3.&nbsp;Windows Subsystem for Linux (WSL)</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="modules.html#modules">5.&nbsp;Modules</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__RESULT__API">5.1.&nbsp;CUPTI Result Codes</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__VERSION__API">5.2.&nbsp;CUPTI Version</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__ACTIVITY__API">5.3.&nbsp;CUPTI Activity API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__CALLBACK__API">5.4.&nbsp;CUPTI Callback API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__EVENT__API">5.5.&nbsp;CUPTI Event API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__METRIC__API">5.6.&nbsp;CUPTI Metric API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PROFILER__API">5.7.&nbsp;CUPTI Profiling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__CHECKPOINT__API">5.8.&nbsp;CUPTI Checkpoint API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PCSAMPLING__API">5.9.&nbsp;CUPTI PC Sampling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PCSAMPLING__UTILITY">5.10.&nbsp;CUPTI PC Sampling Utility API</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="annotated.html#annotated">6.&nbsp;Data Structures</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="annotated.html#structBufferInfo">6.1.&nbsp;BufferInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams">6.2.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams">6.3.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams">6.4.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams">6.5.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams">6.6.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Activity">6.7.&nbsp;CUpti_Activity</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityAPI">6.8.&nbsp;CUpti_ActivityAPI</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityAutoBoostState">6.9.&nbsp;CUpti_ActivityAutoBoostState</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityBranch">6.10.&nbsp;CUpti_ActivityBranch</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityBranch2">6.11.&nbsp;CUpti_ActivityBranch2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityCdpKernel">6.12.&nbsp;CUpti_ActivityCdpKernel</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityContext">6.13.&nbsp;CUpti_ActivityContext</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityCudaEvent">6.14.&nbsp;CUpti_ActivityCudaEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice">6.15.&nbsp;CUpti_ActivityDevice</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice2">6.16.&nbsp;CUpti_ActivityDevice2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice3">6.17.&nbsp;CUpti_ActivityDevice3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice4">6.18.&nbsp;CUpti_ActivityDevice4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDeviceAttribute">6.19.&nbsp;CUpti_ActivityDeviceAttribute</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEnvironment">6.20.&nbsp;CUpti_ActivityEnvironment</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEvent">6.21.&nbsp;CUpti_ActivityEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEventInstance">6.22.&nbsp;CUpti_ActivityEventInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityExternalCorrelation">6.23.&nbsp;CUpti_ActivityExternalCorrelation</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityFunction">6.24.&nbsp;CUpti_ActivityFunction</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess">6.25.&nbsp;CUpti_ActivityGlobalAccess</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess2">6.26.&nbsp;CUpti_ActivityGlobalAccess2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess3">6.27.&nbsp;CUpti_ActivityGlobalAccess3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGraphTrace">6.28.&nbsp;CUpti_ActivityGraphTrace</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousEvent">6.29.&nbsp;CUpti_ActivityInstantaneousEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousEventInstance">6.30.&nbsp;CUpti_ActivityInstantaneousEventInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousMetric">6.31.&nbsp;CUpti_ActivityInstantaneousMetric</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance">6.32.&nbsp;CUpti_ActivityInstantaneousMetricInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstructionCorrelation">6.33.&nbsp;CUpti_ActivityInstructionCorrelation</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstructionExecution">6.34.&nbsp;CUpti_ActivityInstructionExecution</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityJit">6.35.&nbsp;CUpti_ActivityJit</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel">6.36.&nbsp;CUpti_ActivityKernel</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel2">6.37.&nbsp;CUpti_ActivityKernel2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel3">6.38.&nbsp;CUpti_ActivityKernel3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel4">6.39.&nbsp;CUpti_ActivityKernel4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel5">6.40.&nbsp;CUpti_ActivityKernel5</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel6">6.41.&nbsp;CUpti_ActivityKernel6</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel7">6.42.&nbsp;CUpti_ActivityKernel7</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel8">6.43.&nbsp;CUpti_ActivityKernel8</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel9">6.44.&nbsp;CUpti_ActivityKernel9</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarker">6.45.&nbsp;CUpti_ActivityMarker</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarker2">6.46.&nbsp;CUpti_ActivityMarker2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarkerData">6.47.&nbsp;CUpti_ActivityMarkerData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy">6.48.&nbsp;CUpti_ActivityMemcpy</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy3">6.49.&nbsp;CUpti_ActivityMemcpy3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy4">6.50.&nbsp;CUpti_ActivityMemcpy4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy5">6.51.&nbsp;CUpti_ActivityMemcpy5</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP">6.52.&nbsp;CUpti_ActivityMemcpyPtoP</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP2">6.53.&nbsp;CUpti_ActivityMemcpyPtoP2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP3">6.54.&nbsp;CUpti_ActivityMemcpyPtoP3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP4">6.55.&nbsp;CUpti_ActivityMemcpyPtoP4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory">6.56.&nbsp;CUpti_ActivityMemory</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory2">6.57.&nbsp;CUpti_ActivityMemory2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory3">6.58.&nbsp;CUpti_ActivityMemory3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT">6.59.&nbsp;CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemoryPool">6.60.&nbsp;CUpti_ActivityMemoryPool</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemoryPool2">6.61.&nbsp;CUpti_ActivityMemoryPool2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset">6.62.&nbsp;CUpti_ActivityMemset</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset2">6.63.&nbsp;CUpti_ActivityMemset2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset3">6.64.&nbsp;CUpti_ActivityMemset3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset4">6.65.&nbsp;CUpti_ActivityMemset4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMetric">6.66.&nbsp;CUpti_ActivityMetric</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMetricInstance">6.67.&nbsp;CUpti_ActivityMetricInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityModule">6.68.&nbsp;CUpti_ActivityModule</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityName">6.69.&nbsp;CUpti_ActivityName</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink">6.70.&nbsp;CUpti_ActivityNvLink</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink2">6.71.&nbsp;CUpti_ActivityNvLink2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink3">6.72.&nbsp;CUpti_ActivityNvLink3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink4">6.73.&nbsp;CUpti_ActivityNvLink4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#unionCUpti__ActivityObjectKindId">6.74.&nbsp;CUpti_ActivityObjectKindId</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAcc">6.75.&nbsp;CUpti_ActivityOpenAcc</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccData">6.76.&nbsp;CUpti_ActivityOpenAccData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccLaunch">6.77.&nbsp;CUpti_ActivityOpenAccLaunch</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccOther">6.78.&nbsp;CUpti_ActivityOpenAccOther</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenMp">6.79.&nbsp;CUpti_ActivityOpenMp</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOverhead">6.80.&nbsp;CUpti_ActivityOverhead</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPcie">6.81.&nbsp;CUpti_ActivityPcie</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling">6.82.&nbsp;CUpti_ActivityPCSampling</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling2">6.83.&nbsp;CUpti_ActivityPCSampling2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling3">6.84.&nbsp;CUpti_ActivityPCSampling3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSamplingConfig">6.85.&nbsp;CUpti_ActivityPCSamplingConfig</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo">6.86.&nbsp;CUpti_ActivityPCSamplingRecordInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPreemption">6.87.&nbsp;CUpti_ActivityPreemption</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySharedAccess">6.88.&nbsp;CUpti_ActivitySharedAccess</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySourceLocator">6.89.&nbsp;CUpti_ActivitySourceLocator</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityStream">6.90.&nbsp;CUpti_ActivityStream</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySynchronization">6.91.&nbsp;CUpti_ActivitySynchronization</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter">6.92.&nbsp;CUpti_ActivityUnifiedMemoryCounter</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2">6.93.&nbsp;CUpti_ActivityUnifiedMemoryCounter2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounterConfig">6.94.&nbsp;CUpti_ActivityUnifiedMemoryCounterConfig</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__CallbackData">6.95.&nbsp;CUpti_CallbackData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__EventGroupSet">6.96.&nbsp;CUpti_EventGroupSet</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__EventGroupSets">6.97.&nbsp;CUpti_EventGroupSets</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GetCubinCrcParams">6.98.&nbsp;CUpti_GetCubinCrcParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GetSassToSourceCorrelationParams">6.99.&nbsp;CUpti_GetSassToSourceCorrelationParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GraphData">6.100.&nbsp;CUpti_GraphData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#unionCUpti__MetricValue">6.101.&nbsp;CUpti_MetricValue</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ModuleResourceData">6.102.&nbsp;CUpti_ModuleResourceData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__NvtxData">6.103.&nbsp;CUpti_NvtxData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingConfigurationInfo">6.104.&nbsp;CUpti_PCSamplingConfigurationInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams">6.105.&nbsp;CUpti_PCSamplingConfigurationInfoParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingData">6.106.&nbsp;CUpti_PCSamplingData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingDisableParams">6.107.&nbsp;CUpti_PCSamplingDisableParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingEnableParams">6.108.&nbsp;CUpti_PCSamplingEnableParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetDataParams">6.109.&nbsp;CUpti_PCSamplingGetDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetNumStallReasonsParams">6.110.&nbsp;CUpti_PCSamplingGetNumStallReasonsParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams">6.111.&nbsp;CUpti_PCSamplingGetStallReasonsParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingPCData">6.112.&nbsp;CUpti_PCSamplingPCData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStallReason">6.113.&nbsp;CUpti_PCSamplingStallReason</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStartParams">6.114.&nbsp;CUpti_PCSamplingStartParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStopParams">6.115.&nbsp;CUpti_PCSamplingStopParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__BeginPass__Params">6.116.&nbsp;CUpti_Profiler_BeginPass_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__BeginSession__Params">6.117.&nbsp;CUpti_Profiler_BeginSession_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params">6.118.&nbsp;CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params">6.119.&nbsp;CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params">6.120.&nbsp;CUpti_Profiler_CounterDataImage_Initialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params">6.121.&nbsp;CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImageOptions">6.122.&nbsp;CUpti_Profiler_CounterDataImageOptions</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DeInitialize__Params">6.123.&nbsp;CUpti_Profiler_DeInitialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DeviceSupported__Params">6.124.&nbsp;CUpti_Profiler_DeviceSupported_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DisableProfiling__Params">6.125.&nbsp;CUpti_Profiler_DisableProfiling_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EnableProfiling__Params">6.126.&nbsp;CUpti_Profiler_EnableProfiling_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EndPass__Params">6.127.&nbsp;CUpti_Profiler_EndPass_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EndSession__Params">6.128.&nbsp;CUpti_Profiler_EndSession_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__FlushCounterData__Params">6.129.&nbsp;CUpti_Profiler_FlushCounterData_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params">6.130.&nbsp;CUpti_Profiler_GetCounterAvailability_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__Initialize__Params">6.131.&nbsp;CUpti_Profiler_Initialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__IsPassCollected__Params">6.132.&nbsp;CUpti_Profiler_IsPassCollected_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__SetConfig__Params">6.133.&nbsp;CUpti_Profiler_SetConfig_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__UnsetConfig__Params">6.134.&nbsp;CUpti_Profiler_UnsetConfig_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ResourceData">6.135.&nbsp;CUpti_ResourceData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__SynchronizeData">6.136.&nbsp;CUpti_SynchronizeData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structHeader">6.137.&nbsp;Header</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint">6.138.&nbsp;NV::Cupti::Checkpoint::CUpti_Checkpoint</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structPcSamplingStallReasons">6.139.&nbsp;PcSamplingStallReasons</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="functions.html#functions">7.&nbsp;Data Fields</a></div>
               </li>
               <li>
                  <div class="section-link"><a href="notices-header.html#notices-header">Notices</a></div>
                  <ul></ul>
               </li>
            </ul>
         </nav>
         <div id="resize-nav"></div>
         <nav id="search-results">
            <h2>Search Results</h2>
            <ol></ol>
         </nav>
         
         <div id="contents-container">
            <div id="breadcrumbs-container">
               <div id="breadcrumbs"><a href="annotated.html" shape="rect">&lt; Previous</a> | <a href="notices-header.html" shape="rect">Next &gt;</a></div>
               <div id="release-info">CUPTI
                  (<a href="../pdf/Cupti.pdf">PDF</a>)
                  
                  -
                  
                  v12.1
                  (<a href="https://developer.nvidia.com/cuda-toolkit-archive">older</a>)
                  -
                  Last updated Feb 28, 2023
                  -
                  <a href="mailto:<EMAIL>?subject=CUPTI Documentation Feedback: CUPTI">Send Feedback</a></div>
            </div>
            <article id="contents">
               <div class="topic nested1" id="functions"><a name="functions" shape="rect">
                     <!-- --></a><h2 class="topictitle2">7.&nbsp;Data Fields</h2>
                  <div class="body refbody">
                     <div class="section">
                        <p class="p">Here is a list of all documented struct and union fields with links to the struct/union documentation for each field: </p>
                     </div>
                     <div class="section" id="functions__a"><a name="functions__a" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">A</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">address</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT" shape="rect">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dt class="dt dlterm">allocPC</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dt class="dt dlterm">allowOverwrite</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint" title="Configuration and handle for a CUPTI Checkpoint." shape="rect">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></dd>
                           <dt class="dt dlterm">allPassesCollected</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__IsPassCollected__Params" title="Params for cuptiProfilerIsPassCollected." shape="rect">CUpti_Profiler_IsPassCollected_Params</a></dd>
                           <dt class="dt dlterm">allPassesSubmitted</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndPass__Params" title="Params for cuptiProfilerEndPass." shape="rect">CUpti_Profiler_EndPass_Params</a></dd>
                           <dt class="dt dlterm">architecture</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                           <dt class="dt dlterm">async</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">asyncMap</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">attr</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">attribute</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDeviceAttribute" title="The activity record for a device attribute." shape="rect">CUpti_ActivityDeviceAttribute</a></dd>
                           <dt class="dt dlterm">attributeType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__b"><a name="functions__b" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">B</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">bandwidth</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dt class="dt dlterm">bDumpCounterDataInFile</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dt class="dt dlterm">blockX</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">blockY</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dt class="dt dlterm">blockZ</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dt class="dt dlterm">bridgeId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">bufferByteSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structBufferInfo" title="BufferInfo will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBufferInFile() API." shape="rect">BufferInfo</a></dd>
                           <dt class="dt dlterm">bufferInfoData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams" title="Params for CuptiUtilGetBufferInfo." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a></dd>
                           <dt class="dt dlterm">bufferType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams" title="Params for CuptiUtilPutPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></dd>
                           <dt class="dt dlterm">bytes</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__c"><a name="functions__c" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">C</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">cacheConfig</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">cacheConfigExecuted</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dt class="dt dlterm">cacheConfigRequested</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dt class="dt dlterm">cachePath</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dt class="dt dlterm">cacheSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dt class="dt dlterm">callbackSite</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                           <dt class="dt dlterm">category</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMarkerData" title="The activity record providing detailed information for a marker." shape="rect">CUpti_ActivityMarkerData</a></dd>
                           <dt class="dt dlterm">cbid</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dt class="dt dlterm">channelID</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dt class="dt dlterm">channelType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">clocksThrottleReasons</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">clusterSchedulingPolicy</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">clusterX</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">clusterY</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dt class="dt dlterm">clusterZ</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">cmp</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                           <dt class="dt dlterm">collectionModeData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">collectNumPcs</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">color</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMarkerData" title="The activity record providing detailed information for a marker." shape="rect">CUpti_ActivityMarkerData</a></dd>
                           <dt class="dt dlterm">completed</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">computeApiKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityContext" title="The activity record for a context." shape="rect">CUpti_ActivityContext</a></dd>
                           <dt class="dt dlterm">computeCapabilityMajor</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">computeCapabilityMinor</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">computeInstanceId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">confidentialCompute</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                           <dt class="dt dlterm">configSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dt class="dt dlterm">constantMemorySize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">context</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ResourceData" title="Data passed into a resource callback function." shape="rect">CUpti_ResourceData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__SynchronizeData" title="Data passed into a synchronize callback function." shape="rect">CUpti_SynchronizeData</a></dd>
                           <dt class="dt dlterm">contextId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityContext" title="The activity record for a context." shape="rect">CUpti_ActivityContext</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityFunction" title="The activity record for global/device functions." shape="rect">CUpti_ActivityFunction</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityModule" title="The activity record for a CUDA module." shape="rect">CUpti_ActivityModule</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCudaEvent" title="The activity record for CUDA event." shape="rect">CUpti_ActivityCudaEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityStream" title="The activity record for CUDA stream." shape="rect">CUpti_ActivityStream</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dt class="dt dlterm">contextUid</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                           <dt class="dt dlterm">cooling</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">copyKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dt class="dt dlterm">coreClockRate</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">correlationData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                           <dt class="dt dlterm">correlationId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEvent" title="The activity record for a CUPTI event." shape="rect">CUpti_ActivityEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEventInstance" title="The activity record for a CUPTI event with instance information." shape="rect">CUpti_ActivityEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityStream" title="The activity record for CUDA stream." shape="rect">CUpti_ActivityStream</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetricInstance" title="The activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch" title="The activity record for source level result branch. (deprecated)." shape="rect">CUpti_ActivityBranch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityExternalCorrelation" title="The activity record for correlation with external records." shape="rect">CUpti_ActivityExternalCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo" title="The activity record for record status for PC sampling." shape="rect">CUpti_ActivityPCSamplingRecordInfo</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCudaEvent" title="The activity record for CUDA event." shape="rect">CUpti_ActivityCudaEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetric" title="The activity record for a CUPTI metric." shape="rect">CUpti_ActivityMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dt class="dt dlterm">counterAvailabilityImageSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params" title="Params for cuptiProfilerGetCounterAvailability." shape="rect">CUpti_Profiler_GetCounterAvailability_Params</a></dd>
                           <dt class="dt dlterm">counterDataImageSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params" title="Params for cuptiProfilerCounterDataImageInitialize." shape="rect">CUpti_Profiler_CounterDataImage_Initialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateScratchBufferSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params" title="Params for cuptiProfilerCounterDataImageInitializeScratchBuffer." shape="rect">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></dd>
                           <dt class="dt dlterm">counterDataPrefixSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImageOptions" title="Input parameter to define the counterDataImage." shape="rect">CUpti_Profiler_CounterDataImageOptions</a></dd>
                           <dt class="dt dlterm">counterDataScratchBufferSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateScratchBufferSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params" title="Params for cuptiProfilerCounterDataImageInitializeScratchBuffer." shape="rect">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></dd>
                           <dt class="dt dlterm">counterKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dt class="dt dlterm">ctx</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStartParams" title="Params for cuptiPCSamplingStart." shape="rect">CUpti_PCSamplingStartParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetDataParams" title="Params for cuptiPCSamplingEnable." shape="rect">CUpti_PCSamplingGetDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params" title="Params for cuptiProfilerGetCounterAvailability." shape="rect">CUpti_Profiler_GetCounterAvailability_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetNumStallReasonsParams" title="Params for cuptiPCSamplingGetNumStallReasons." shape="rect">CUpti_PCSamplingGetNumStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams" title="PC sampling configuration structure." shape="rect">CUpti_PCSamplingConfigurationInfoParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndSession__Params" title="Params for cuptiProfilerEndSession." shape="rect">CUpti_Profiler_EndSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndPass__Params" title="Params for cuptiProfilerEndPass." shape="rect">CUpti_Profiler_EndPass_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__FlushCounterData__Params" title="Params for cuptiProfilerFlushCounterData." shape="rect">CUpti_Profiler_FlushCounterData_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint" title="Configuration and handle for a CUPTI Checkpoint." shape="rect">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__DisableProfiling__Params" title="Params for cuptiProfilerDisableProfiling." shape="rect">CUpti_Profiler_DisableProfiling_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStopParams" title="Params for cuptiPCSamplingStop." shape="rect">CUpti_PCSamplingStopParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams" title="Params for cuptiPCSamplingGetStallReasons." shape="rect">CUpti_PCSamplingGetStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__IsPassCollected__Params" title="Params for cuptiProfilerIsPassCollected." shape="rect">CUpti_Profiler_IsPassCollected_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EnableProfiling__Params" title="Params for cuptiProfilerEnableProfiling." shape="rect">CUpti_Profiler_EnableProfiling_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginPass__Params" title="Params for cuptiProfilerBeginPass." shape="rect">CUpti_Profiler_BeginPass_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingEnableParams" title="Params for cuptiPCSamplingEnable." shape="rect">CUpti_PCSamplingEnableParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__UnsetConfig__Params" title="Params for cuptiProfilerUnsetConfig." shape="rect">CUpti_Profiler_UnsetConfig_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingDisableParams" title="Params for cuptiPCSamplingDisable." shape="rect">CUpti_PCSamplingDisableParams</a></dd>
                           <dt class="dt dlterm">cubin</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GetCubinCrcParams" title="Params for cuptiGetCubinCrc." shape="rect">CUpti_GetCubinCrcParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityModule" title="The activity record for a CUDA module." shape="rect">CUpti_ActivityModule</a></dd>
                           <dt class="dt dlterm">cubinCrc</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GetCubinCrcParams" title="Params for cuptiGetCubinCrc." shape="rect">CUpti_GetCubinCrcParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dt class="dt dlterm">cubinSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetCubinCrcParams" title="Params for cuptiGetCubinCrc." shape="rect">CUpti_GetCubinCrcParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityModule" title="The activity record for a CUDA module." shape="rect">CUpti_ActivityModule</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ModuleResourceData" title="Module data passed into a resource callback function." shape="rect">CUpti_ModuleResourceData</a></dd>
                           <dt class="dt dlterm">cuContextId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dt class="dt dlterm">cudaEventId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dt class="dt dlterm">cuDevice</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                           <dt class="dt dlterm">cuDeviceId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">cuProcessId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">cuStreamId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dt class="dt dlterm">cuThreadId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__d"><a name="functions__d" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">D</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">dcs</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#unionCUpti__ActivityObjectKindId" title="Identifiers for object kinds as specified by CUpti_ActivityObjectKind." shape="rect">CUpti_ActivityObjectKindId</a></dd>
                           <dt class="dt dlterm">dependency</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GraphData" title="CUDA graphs data passed into a resource callback function." shape="rect">CUpti_GraphData</a></dd>
                           <dt class="dt dlterm">deviceId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityContext" title="The activity record for a context." shape="rect">CUpti_ActivityContext</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEvent" title="The activity record for an instantaneous CUPTI event." shape="rect">CUpti_ActivityInstantaneousEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEventInstance" title="The activity record for an instantaneous CUPTI event with event domain instance information." shape="rect">CUpti_ActivityInstantaneousEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetric" title="The activity record for an instantaneous CUPTI metric." shape="rect">CUpti_ActivityInstantaneousMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounterConfig" title="Unified Memory counters configuration structure." shape="rect">CUpti_ActivityUnifiedMemoryCounterConfig</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDeviceAttribute" title="The activity record for a device attribute." shape="rect">CUpti_ActivityDeviceAttribute</a></dd>
                           <dt class="dt dlterm">deviceNumber</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">devicePtr</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dt class="dt dlterm">deviceType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">devId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">dirName</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dt class="dt dlterm">diverged</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch" title="The activity record for source level result branch. (deprecated)." shape="rect">CUpti_ActivityBranch</a></dd>
                           <dt class="dt dlterm">domain</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEvent" title="The activity record for a CUPTI event." shape="rect">CUpti_ActivityEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEventInstance" title="The activity record for a CUPTI event with instance information." shape="rect">CUpti_ActivityEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">domainId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dt class="dt dlterm">droppedSamples</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo" title="The activity record for record status for PC sampling." shape="rect">CUpti_ActivityPCSamplingRecordInfo</a></dd>
                           <dt class="dt dlterm">dstContextId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dt class="dt dlterm">dstDeviceId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dt class="dt dlterm">dstId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dt class="dt dlterm">dstKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dt class="dt dlterm">dynamicSharedMemory</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__e"><a name="functions__e" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">E</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">eccEnabled</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">enable</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounterConfig" title="Unified Memory counters configuration structure." shape="rect">CUpti_ActivityUnifiedMemoryCounterConfig</a></dd>
                           <dt class="dt dlterm">enabled</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityAutoBoostState" title="Device auto boost state structure." shape="rect">CUpti_ActivityAutoBoostState</a></dd>
                           <dt class="dt dlterm">enableStartStopControlData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">end</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOverhead" title="The activity record for CUPTI and driver overheads." shape="rect">CUpti_ActivityOverhead</a></dd>
                           <dt class="dt dlterm">endLineNo</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">environmentKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">eventGroups</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__EventGroupSet" title="A set of event groups." shape="rect">CUpti_EventGroupSet</a></dd>
                           <dt class="dt dlterm">eventId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityCudaEvent" title="The activity record for CUDA event." shape="rect">CUpti_ActivityCudaEvent</a></dd>
                           <dt class="dt dlterm">eventKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dt class="dt dlterm">executed</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch" title="The activity record for source level result branch. (deprecated)." shape="rect">CUpti_ActivityBranch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dt class="dt dlterm">externalId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityExternalCorrelation" title="The activity record for correlation with external records." shape="rect">CUpti_ActivityExternalCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dt class="dt dlterm">externalKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityExternalCorrelation" title="The activity record for correlation with external records." shape="rect">CUpti_ActivityExternalCorrelation</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__f"><a name="functions__f" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">F</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">fanSpeed</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">fileHandler</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams" title="Params for CuptiUtilGetHeaderData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams" title="Params for CuptiUtilGetBufferInfo." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a></dd>
                           <dt class="dt dlterm">fileName</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivitySourceLocator" title="The activity record for source locator." shape="rect">CUpti_ActivitySourceLocator</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams" title="Params for CuptiUtilPutPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></dd>
                           <dt class="dt dlterm">flag</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityStream" title="The activity record for CUDA stream." shape="rect">CUpti_ActivityStream</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dt class="dt dlterm">flags</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDeviceAttribute" title="The activity record for a device attribute." shape="rect">CUpti_ActivityDeviceAttribute</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker" title="The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityMarker</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarkerData" title="The activity record providing detailed information for a marker." shape="rect">CUpti_ActivityMarkerData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionCorrelation" title="The activity record for source-level sass/source line-by-line correlation." shape="rect">CUpti_ActivityInstructionCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetric" title="The activity record for an instantaneous CUPTI metric." shape="rect">CUpti_ActivityInstantaneousMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetric" title="The activity record for a CUPTI metric." shape="rect">CUpti_ActivityMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetricInstance" title="The activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dt class="dt dlterm">freePC</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dt class="dt dlterm">funcEndLineNo</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">funcLineNo</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dt class="dt dlterm">funcName</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">functionId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionCorrelation" title="The activity record for source-level sass/source line-by-line correlation." shape="rect">CUpti_ActivityInstructionCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dt class="dt dlterm">functionIndex</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityFunction" title="The activity record for global/device functions." shape="rect">CUpti_ActivityFunction</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dt class="dt dlterm">functionName</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__NvtxData" title="Data passed into a NVTX callback function." shape="rect">CUpti_NvtxData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dt class="dt dlterm">functionParams</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__NvtxData" title="Data passed into a NVTX callback function." shape="rect">CUpti_NvtxData</a></dd>
                           <dt class="dt dlterm">functionReturnValue</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__NvtxData" title="Data passed into a NVTX callback function." shape="rect">CUpti_NvtxData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__g"><a name="functions__g" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">G</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">globalMemoryBandwidth</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dt class="dt dlterm">globalMemorySize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dt class="dt dlterm">gpuInstanceId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">gpuTemperature</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">graph</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GraphData" title="CUDA graphs data passed into a resource callback function." shape="rect">CUpti_GraphData</a></dd>
                           <dt class="dt dlterm">graphExec</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GraphData" title="CUDA graphs data passed into a resource callback function." shape="rect">CUpti_GraphData</a></dd>
                           <dt class="dt dlterm">graphId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dt class="dt dlterm">graphNodeId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dt class="dt dlterm">gridId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dt class="dt dlterm">gridX</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">gridY</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dt class="dt dlterm">gridZ</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__h"><a name="functions__h" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">H</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">hardwareBufferFull</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">hardwareBufferSizeData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">headerInfo</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams" title="Params for CuptiUtilGetHeaderData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a></dd>
                           <dt class="dt dlterm">hostPtr</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__i"><a name="functions__i" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">I</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">id</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEvent" title="The activity record for a CUPTI event." shape="rect">CUpti_ActivityEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEventInstance" title="The activity record for a CUPTI event with instance information." shape="rect">CUpti_ActivityEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetricInstance" title="The activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEvent" title="The activity record for an instantaneous CUPTI event." shape="rect">CUpti_ActivityInstantaneousEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEventInstance" title="The activity record for an instantaneous CUPTI event with event domain instance information." shape="rect">CUpti_ActivityInstantaneousEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetric" title="The activity record for an instantaneous CUPTI metric." shape="rect">CUpti_ActivityInstantaneousMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySourceLocator" title="The activity record for source locator." shape="rect">CUpti_ActivitySourceLocator</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker" title="The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityMarker</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetric" title="The activity record for a CUPTI metric." shape="rect">CUpti_ActivityMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarkerData" title="The activity record providing detailed information for a marker." shape="rect">CUpti_ActivityMarkerData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityFunction" title="The activity record for global/device functions." shape="rect">CUpti_ActivityFunction</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityModule" title="The activity record for a CUDA module." shape="rect">CUpti_ActivityModule</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">idDev0</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dt class="dt dlterm">idDev1</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dt class="dt dlterm">implicit</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">index</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dt class="dt dlterm">instance</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetricInstance" title="The activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEventInstance" title="The activity record for an instantaneous CUPTI event with event domain instance information." shape="rect">CUpti_ActivityInstantaneousEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEventInstance" title="The activity record for a CUPTI event with instance information." shape="rect">CUpti_ActivityEventInstance</a></dd>
                           <dt class="dt dlterm">invalidData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">isAsync</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dt class="dt dlterm">isCudaVisible</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">isMigEnabled</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">isSharedMemoryCarveoutRequested</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dt class="dt dlterm">isSupported</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__j"><a name="functions__j" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">J</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">jitEntryType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dt class="dt dlterm">jitOperationCorrelationId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dt class="dt dlterm">jitOperationType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__k"><a name="functions__k" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">K</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">kernelName</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">kind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetric" title="The activity record for an instantaneous CUPTI metric." shape="rect">CUpti_ActivityInstantaneousMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEventInstance" title="The activity record for an instantaneous CUPTI event with event domain instance information." shape="rect">CUpti_ActivityInstantaneousEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEvent" title="The activity record for an instantaneous CUPTI event." shape="rect">CUpti_ActivityInstantaneousEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityExternalCorrelation" title="The activity record for correlation with external records." shape="rect">CUpti_ActivityExternalCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionCorrelation" title="The activity record for source-level sass/source line-by-line correlation." shape="rect">CUpti_ActivityInstructionCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityStream" title="The activity record for CUDA stream." shape="rect">CUpti_ActivityStream</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCudaEvent" title="The activity record for CUDA event." shape="rect">CUpti_ActivityCudaEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityModule" title="The activity record for a CUDA module." shape="rect">CUpti_ActivityModule</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityFunction" title="The activity record for global/device functions." shape="rect">CUpti_ActivityFunction</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo" title="The activity record for record status for PC sampling." shape="rect">CUpti_ActivityPCSamplingRecordInfo</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOverhead" title="The activity record for CUPTI and driver overheads." shape="rect">CUpti_ActivityOverhead</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarkerData" title="The activity record providing detailed information for a marker." shape="rect">CUpti_ActivityMarkerData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker" title="The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityMarker</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityName" title="The activity record providing a name." shape="rect">CUpti_ActivityName</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityContext" title="The activity record for a context." shape="rect">CUpti_ActivityContext</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDeviceAttribute" title="The activity record for a device attribute." shape="rect">CUpti_ActivityDeviceAttribute</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch" title="The activity record for source level result branch. (deprecated)." shape="rect">CUpti_ActivityBranch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySourceLocator" title="The activity record for source locator." shape="rect">CUpti_ActivitySourceLocator</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetricInstance" title="The activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetric" title="The activity record for a CUPTI metric." shape="rect">CUpti_ActivityMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEventInstance" title="The activity record for a CUPTI event with instance information." shape="rect">CUpti_ActivityEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEvent" title="The activity record for a CUPTI event." shape="rect">CUpti_ActivityEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Activity" title="The base activity record." shape="rect">CUpti_Activity</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounterConfig" title="Unified Memory counters configuration structure." shape="rect">CUpti_ActivityUnifiedMemoryCounterConfig</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__l"><a name="functions__l" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">L</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">l2_transactions</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dt class="dt dlterm">l2CacheSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">latencySamples</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dt class="dt dlterm">launchType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">lineNo</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">lineNumber</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivitySourceLocator" title="The activity record for source locator." shape="rect">CUpti_ActivitySourceLocator</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dt class="dt dlterm">linkRate</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">linkWidth</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">localMemoryPerThread</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dt class="dt dlterm">localMemoryTotal</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">localMemoryTotal_v2</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__m"><a name="functions__m" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">M</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">maxActiveClusters</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">maxBlockDimX</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dt class="dt dlterm">maxBlockDimY</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dt class="dt dlterm">maxBlockDimZ</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxBlocksPerMultiprocessor</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dt class="dt dlterm">maxGridDimX</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxGridDimY</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxGridDimZ</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxIPC</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dt class="dt dlterm">maxLaunchesPerPass</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dt class="dt dlterm">maxNumRanges</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImageOptions" title="Input parameter to define the counterDataImage." shape="rect">CUpti_Profiler_CounterDataImageOptions</a></dd>
                           <dt class="dt dlterm">maxNumRangeTreeNodes</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImageOptions" title="Input parameter to define the counterDataImage." shape="rect">CUpti_Profiler_CounterDataImageOptions</a></dd>
                           <dt class="dt dlterm">maxPotentialClusterSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">maxRangeNameLength</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImageOptions" title="Input parameter to define the counterDataImage." shape="rect">CUpti_Profiler_CounterDataImageOptions</a></dd>
                           <dt class="dt dlterm">maxRangesPerPass</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dt class="dt dlterm">maxRegistersPerBlock</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxRegistersPerMultiprocessor</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxSharedMemoryPerBlock</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxSharedMemoryPerMultiprocessor</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxThreadsPerBlock</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">maxWarpsPerMultiprocessor</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dt class="dt dlterm">memoryClock</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">memoryKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dt class="dt dlterm">memoryOperationType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dt class="dt dlterm">memoryPoolConfig</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dt class="dt dlterm">memoryPoolOperationType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dt class="dt dlterm">memoryPoolType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT" shape="rect">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dt class="dt dlterm">MergedPcSampDataBuffers</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams" title="Params for CuptiUtilMergePcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></dd>
                           <dt class="dt dlterm">migUuid</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">minBytesToKeep</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dt class="dt dlterm">minNestingLevel</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dt class="dt dlterm">moduleId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityFunction" title="The activity record for global/device functions." shape="rect">CUpti_ActivityFunction</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ModuleResourceData" title="Module data passed into a resource callback function." shape="rect">CUpti_ModuleResourceData</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__n"><a name="functions__n" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">N</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">name</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityName" title="The activity record providing a name." shape="rect">CUpti_ActivityName</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker" title="The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityMarker</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityFunction" title="The activity record for global/device functions." shape="rect">CUpti_ActivityFunction</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dt class="dt dlterm">node</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GraphData" title="CUDA graphs data passed into a resource callback function." shape="rect">CUpti_GraphData</a></dd>
                           <dt class="dt dlterm">nodeType</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GraphData" title="CUDA graphs data passed into a resource callback function." shape="rect">CUpti_GraphData</a></dd>
                           <dt class="dt dlterm">nonUsrKernelsTotalSamples</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">notPredOffThreadsExecuted</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dt class="dt dlterm">nullStreamId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityContext" title="The activity record for a context." shape="rect">CUpti_ActivityContext</a></dd>
                           <dt class="dt dlterm">numAttributes</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams" title="PC sampling configuration structure." shape="rect">CUpti_PCSamplingConfigurationInfoParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams" title="Params for CuptiUtilPutPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dt class="dt dlterm">numberOfBuffers</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams" title="Params for CuptiUtilMergePcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></dd>
                           <dt class="dt dlterm">numEventGroups</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__EventGroupSet" title="A set of event groups." shape="rect">CUpti_EventGroupSet</a></dd>
                           <dt class="dt dlterm">numGangs</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">numMemcpyEngines</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">numMergedBuffer</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams" title="Params for CuptiUtilMergePcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></dd>
                           <dt class="dt dlterm">numMultiprocessors</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dt class="dt dlterm">numNestingLevels</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dt class="dt dlterm">numRangesDropped</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__IsPassCollected__Params" title="Params for cuptiProfilerIsPassCollected." shape="rect">CUpti_Profiler_IsPassCollected_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__FlushCounterData__Params" title="Params for cuptiProfilerFlushCounterData." shape="rect">CUpti_Profiler_FlushCounterData_Params</a></dd>
                           <dt class="dt dlterm">numSelectedStallReasons</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structBufferInfo" title="BufferInfo will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBufferInFile() API." shape="rect">BufferInfo</a></dd>
                           <dt class="dt dlterm">numSets</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__EventGroupSets" title="A set of event group sets." shape="rect">CUpti_EventGroupSets</a></dd>
                           <dt class="dt dlterm">numStallReasons</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structPcSamplingStallReasons" title="All available stall reasons name and respective indexes will be stored in it." shape="rect">PcSamplingStallReasons</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams" title="Params for cuptiPCSamplingGetStallReasons." shape="rect">CUpti_PCSamplingGetStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structBufferInfo" title="BufferInfo will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBufferInFile() API." shape="rect">BufferInfo</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetNumStallReasonsParams" title="Params for cuptiPCSamplingGetNumStallReasons." shape="rect">CUpti_PCSamplingGetNumStallReasonsParams</a></dd>
                           <dt class="dt dlterm">numThreadsPerWarp</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice</a></dd>
                           <dt class="dt dlterm">numTraceBytesDropped</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__IsPassCollected__Params" title="Params for cuptiProfilerIsPassCollected." shape="rect">CUpti_Profiler_IsPassCollected_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__FlushCounterData__Params" title="Params for cuptiProfilerFlushCounterData." shape="rect">CUpti_Profiler_FlushCounterData_Params</a></dd>
                           <dt class="dt dlterm">numWorkers</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">nvlinkVersion</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dt class="dt dlterm">nvswitchConnected</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__o"><a name="functions__o" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">O</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">objectId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityName" title="The activity record providing a name." shape="rect">CUpti_ActivityName</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker" title="The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityMarker</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOverhead" title="The activity record for CUPTI and driver overheads." shape="rect">CUpti_ActivityOverhead</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dt class="dt dlterm">objectKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOverhead" title="The activity record for CUPTI and driver overheads." shape="rect">CUpti_ActivityOverhead</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityName" title="The activity record providing a name." shape="rect">CUpti_ActivityName</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker" title="The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityMarker</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dt class="dt dlterm">onePassCollected</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__IsPassCollected__Params" title="Params for cuptiProfilerIsPassCollected." shape="rect">CUpti_Profiler_IsPassCollected_Params</a></dd>
                           <dt class="dt dlterm">optimizations</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint" title="Configuration and handle for a CUPTI Checkpoint." shape="rect">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></dd>
                           <dt class="dt dlterm">originalGraph</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GraphData" title="CUDA graphs data passed into a resource callback function." shape="rect">CUpti_GraphData</a></dd>
                           <dt class="dt dlterm">originalNode</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__GraphData" title="CUDA graphs data passed into a resource callback function." shape="rect">CUpti_GraphData</a></dd>
                           <dt class="dt dlterm">outputDataFormatData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">overheadKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOverhead" title="The activity record for CUPTI and driver overheads." shape="rect">CUpti_ActivityOverhead</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__p"><a name="functions__p" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">P</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">pAccessPolicyWindow</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dt class="dt dlterm">pad</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetric" title="The activity record for an instantaneous CUPTI metric." shape="rect">CUpti_ActivityInstantaneousMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityModule" title="The activity record for a CUDA module." shape="rect">CUpti_ActivityModule</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEventInstance" title="The activity record for a CUPTI event with instance information." shape="rect">CUpti_ActivityEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCudaEvent" title="The activity record for CUDA event." shape="rect">CUpti_ActivityCudaEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetric" title="The activity record for a CUPTI metric." shape="rect">CUpti_ActivityMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionCorrelation" title="The activity record for source-level sass/source line-by-line correlation." shape="rect">CUpti_ActivityInstructionCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetricInstance" title="The activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEventInstance" title="The activity record for an instantaneous CUPTI event with event domain instance information." shape="rect">CUpti_ActivityInstantaneousEventInstance</a></dd>
                           <dt class="dt dlterm">pad0</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">pad1</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">pad2</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dt class="dt dlterm">padding</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dt class="dt dlterm">parentBlockX</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">parentBlockY</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">parentBlockZ</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">parentConstruct</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">parentGridId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">partitionedGlobalCacheExecuted</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">partitionedGlobalCacheRequested</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">passIndex</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndPass__Params" title="Params for cuptiProfilerEndPass." shape="rect">CUpti_Profiler_EndPass_Params</a></dd>
                           <dt class="dt dlterm">payload</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMarkerData" title="The activity record providing detailed information for a marker." shape="rect">CUpti_ActivityMarkerData</a></dd>
                           <dt class="dt dlterm">payloadKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMarkerData" title="The activity record providing detailed information for a marker." shape="rect">CUpti_ActivityMarkerData</a></dd>
                           <dt class="dt dlterm">pBufferInfoData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dt class="dt dlterm">PC</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dt class="dt dlterm">pcieGeneration</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">pcieLinkGen</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">pcieLinkWidth</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">pcOffset</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch" title="The activity record for source level result branch. (deprecated)." shape="rect">CUpti_ActivityBranch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionCorrelation" title="The activity record for source-level sass/source line-by-line correlation." shape="rect">CUpti_ActivityInstructionCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dt class="dt dlterm">pConfig</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dt class="dt dlterm">pCounterAvailabilityImage</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params" title="Params for cuptiProfilerGetCounterAvailability." shape="rect">CUpti_Profiler_GetCounterAvailability_Params</a></dd>
                           <dt class="dt dlterm">pCounterDataFilePath</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dt class="dt dlterm">pCounterDataImage</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params" title="Params for cuptiProfilerCounterDataImageInitialize." shape="rect">CUpti_Profiler_CounterDataImage_Initialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateScratchBufferSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params" title="Params for cuptiProfilerCounterDataImageInitializeScratchBuffer." shape="rect">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dt class="dt dlterm">pCounterDataPrefix</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImageOptions" title="Input parameter to define the counterDataImage." shape="rect">CUpti_Profiler_CounterDataImageOptions</a></dd>
                           <dt class="dt dlterm">pCounterDataScratchBuffer</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params" title="Params for cuptiProfilerCounterDataImageInitializeScratchBuffer." shape="rect">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></dd>
                           <dt class="dt dlterm">PcSampDataBuffer</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams" title="Params for CuptiUtilMergePcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></dd>
                           <dt class="dt dlterm">pcSamplingData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetDataParams" title="Params for cuptiPCSamplingEnable." shape="rect">CUpti_PCSamplingGetDataParams</a></dd>
                           <dt class="dt dlterm">pcSamplingStallReasonIndex</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStallReason" title="PC Sampling stall reasons." shape="rect">CUpti_PCSamplingStallReason</a></dd>
                           <dt class="dt dlterm">pCubin</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ModuleResourceData" title="Module data passed into a resource callback function." shape="rect">CUpti_ModuleResourceData</a></dd>
                           <dt class="dt dlterm">peerDev</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">physicalNvLinkCount</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dt class="dt dlterm">pid</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityAutoBoostState" title="Device auto boost state structure." shape="rect">CUpti_ActivityAutoBoostState</a></dd>
                           <dt class="dt dlterm">pOptions</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params" title="Params for cuptiProfilerCounterDataImageInitialize." shape="rect">CUpti_Profiler_CounterDataImage_Initialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></dd>
                           <dt class="dt dlterm">portDev0</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dt class="dt dlterm">portDev1</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dt class="dt dlterm">power</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">powerLimit</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">pPcData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">pPCSamplingConfigurationInfo</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams" title="PC sampling configuration structure." shape="rect">CUpti_PCSamplingConfigurationInfoParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams" title="Params for CuptiUtilPutPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dt class="dt dlterm">pPcSamplingStallReasons</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams" title="Params for CuptiUtilPutPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></dd>
                           <dt class="dt dlterm">pPriv</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint" title="Configuration and handle for a CUPTI Checkpoint." shape="rect">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__Initialize__Params" title="Default parameter for cuptiProfilerInitialize." shape="rect">CUpti_Profiler_Initialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params" title="Params for cuptiProfilerGetCounterAvailability." shape="rect">CUpti_Profiler_GetCounterAvailability_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetDataParams" title="Params for cuptiPCSamplingEnable." shape="rect">CUpti_PCSamplingGetDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginPass__Params" title="Params for cuptiProfilerBeginPass." shape="rect">CUpti_Profiler_BeginPass_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStartParams" title="Params for cuptiPCSamplingStart." shape="rect">CUpti_PCSamplingStartParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EnableProfiling__Params" title="Params for cuptiProfilerEnableProfiling." shape="rect">CUpti_Profiler_EnableProfiling_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingDisableParams" title="Params for cuptiPCSamplingDisable." shape="rect">CUpti_PCSamplingDisableParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams" title="Params for cuptiPCSamplingGetStallReasons." shape="rect">CUpti_PCSamplingGetStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStopParams" title="Params for cuptiPCSamplingStop." shape="rect">CUpti_PCSamplingStopParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingEnableParams" title="Params for cuptiPCSamplingEnable." shape="rect">CUpti_PCSamplingEnableParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params" title="Params for cuptiProfilerCounterDataImageInitialize." shape="rect">CUpti_Profiler_CounterDataImage_Initialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImageOptions" title="Input parameter to define the counterDataImage." shape="rect">CUpti_Profiler_CounterDataImageOptions</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetNumStallReasonsParams" title="Params for cuptiPCSamplingGetNumStallReasons." shape="rect">CUpti_PCSamplingGetNumStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams" title="PC sampling configuration structure." shape="rect">CUpti_PCSamplingConfigurationInfoParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__IsPassCollected__Params" title="Params for cuptiProfilerIsPassCollected." shape="rect">CUpti_Profiler_IsPassCollected_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__UnsetConfig__Params" title="Params for cuptiProfilerUnsetConfig." shape="rect">CUpti_Profiler_UnsetConfig_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeInitialize__Params" title="Default parameter for cuptiProfilerDeInitialize." shape="rect">CUpti_Profiler_DeInitialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params" title="Params for cuptiProfilerCounterDataImageInitializeScratchBuffer." shape="rect">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndSession__Params" title="Params for cuptiProfilerEndSession." shape="rect">CUpti_Profiler_EndSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateScratchBufferSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__DisableProfiling__Params" title="Params for cuptiProfilerDisableProfiling." shape="rect">CUpti_Profiler_DisableProfiling_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndPass__Params" title="Params for cuptiProfilerEndPass." shape="rect">CUpti_Profiler_EndPass_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__FlushCounterData__Params" title="Params for cuptiProfilerFlushCounterData." shape="rect">CUpti_Profiler_FlushCounterData_Params</a></dd>
                           <dt class="dt dlterm">preemptionKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dt class="dt dlterm">priority</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityStream" title="The activity record for CUDA stream." shape="rect">CUpti_ActivityStream</a></dd>
                           <dt class="dt dlterm">processId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT" shape="rect">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dt class="dt dlterm">pSamplingData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams" title="Params for CuptiUtilPutPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dt class="dt dlterm">pt</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#unionCUpti__ActivityObjectKindId" title="Identifiers for object kinds as specified by CUpti_ActivityObjectKind." shape="rect">CUpti_ActivityObjectKindId</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__q"><a name="functions__q" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">Q</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">queued</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__r"><a name="functions__r" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">R</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">range</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dt class="dt dlterm">rangeId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">recordCount</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structBufferInfo" title="BufferInfo will be stored in the file for every buffer i.e for every call of UtilDumpPcSamplingBufferInFile() API." shape="rect">BufferInfo</a></dd>
                           <dt class="dt dlterm">registersPerThread</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dt class="dt dlterm">releaseThreshold</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT" shape="rect">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></dd>
                           <dt class="dt dlterm">remainingNumPcs</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">replayMode</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dt class="dt dlterm">requested</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">reserved</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityExternalCorrelation" title="The activity record for correlation with external records." shape="rect">CUpti_ActivityExternalCorrelation</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEvent" title="The activity record for an instantaneous CUPTI event." shape="rect">CUpti_ActivityInstantaneousEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dt class="dt dlterm">reserved0</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dt class="dt dlterm">reserveDeviceMB</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint" title="Configuration and handle for a CUPTI Checkpoint." shape="rect">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></dd>
                           <dt class="dt dlterm">reserveHostMB</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint" title="Configuration and handle for a CUPTI Checkpoint." shape="rect">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></dd>
                           <dt class="dt dlterm">resourceDescriptor</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ResourceData" title="Data passed into a resource callback function." shape="rect">CUpti_ResourceData</a></dd>
                           <dt class="dt dlterm">returnValue</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dt class="dt dlterm">runtimeCorrelationId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__s"><a name="functions__s" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">S</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">samples</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStallReason" title="PC Sampling stall reasons." shape="rect">CUpti_PCSamplingStallReason</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dt class="dt dlterm">samplingDataBufferData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">samplingPeriod</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingConfig" title="PC sampling configuration structure." shape="rect">CUpti_ActivityPCSamplingConfig</a></dd>
                           <dt class="dt dlterm">samplingPeriod2</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingConfig" title="PC sampling configuration structure." shape="rect">CUpti_ActivityPCSamplingConfig</a></dd>
                           <dt class="dt dlterm">samplingPeriodData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">samplingPeriodInCycles</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo" title="The activity record for record status for PC sampling." shape="rect">CUpti_ActivityPCSamplingRecordInfo</a></dd>
                           <dt class="dt dlterm">scope</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounterConfig" title="Unified Memory counters configuration structure." shape="rect">CUpti_ActivityUnifiedMemoryCounterConfig</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dt class="dt dlterm">scratchBufferSizeData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">secondaryBus</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">sets</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__EventGroupSets" title="A set of event group sets." shape="rect">CUpti_EventGroupSets</a></dd>
                           <dt class="dt dlterm">sharedMemoryCarveoutRequested</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dt class="dt dlterm">sharedMemoryConfig</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dt class="dt dlterm">sharedMemoryExecuted</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dt class="dt dlterm">sharedTransactions</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dt class="dt dlterm">shmemLimitConfig</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dt class="dt dlterm">size</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingConfig" title="PC sampling configuration structure." shape="rect">CUpti_ActivityPCSamplingConfig</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT" shape="rect">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams" title="PC sampling configuration structure." shape="rect">CUpti_PCSamplingConfigurationInfoParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetDataParams" title="Params for cuptiPCSamplingEnable." shape="rect">CUpti_PCSamplingGetDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingEnableParams" title="Params for cuptiPCSamplingEnable." shape="rect">CUpti_PCSamplingEnableParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingDisableParams" title="Params for cuptiPCSamplingDisable." shape="rect">CUpti_PCSamplingDisableParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStartParams" title="Params for cuptiPCSamplingStart." shape="rect">CUpti_PCSamplingStartParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingStopParams" title="Params for cuptiPCSamplingStop." shape="rect">CUpti_PCSamplingStopParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetNumStallReasonsParams" title="Params for cuptiPCSamplingGetNumStallReasons." shape="rect">CUpti_PCSamplingGetNumStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams" title="Params for cuptiPCSamplingGetStallReasons." shape="rect">CUpti_PCSamplingGetStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetSassToSourceCorrelationParams" title="Params for cuptiGetSassToSourceCorrelation." shape="rect">CUpti_GetSassToSourceCorrelationParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__GetCubinCrcParams" title="Params for cuptiGetCubinCrc." shape="rect">CUpti_GetCubinCrcParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams" title="Params for CuptiUtilPutPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams" title="Params for CuptiUtilGetHeaderData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams" title="Params for CuptiUtilGetBufferInfo." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams" title="Params for CuptiUtilGetPcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams" title="Params for CuptiUtilMergePcSampData." shape="rect">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></dd>
                           <dt class="dt dlterm">sizeofCounterDataImageOptions</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params" title="Params for cuptiProfilerCounterDataImageInitialize." shape="rect">CUpti_Profiler_CounterDataImage_Initialize_Params</a></dd>
                           <dt class="dt dlterm">sli</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                           <dt class="dt dlterm">smClock</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">sourceLocatorId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch" title="The activity record for source level result branch. (deprecated)." shape="rect">CUpti_ActivityBranch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionCorrelation" title="The activity record for source-level sass/source line-by-line correlation." shape="rect">CUpti_ActivityInstructionCorrelation</a></dd>
                           <dt class="dt dlterm">speed</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">srcContextId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dt class="dt dlterm">srcDeviceId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dt class="dt dlterm">srcFile</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">srcId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dt class="dt dlterm">srcKind</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dt class="dt dlterm">stallReason</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling" title="The activity record for PC sampling. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityPCSampling</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling2" title="The activity record for PC sampling. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityPCSampling2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSampling3" title="The activity record for PC sampling." shape="rect">CUpti_ActivityPCSampling3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dt class="dt dlterm">stallReasonCount</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingPCData" title="PC Sampling data." shape="rect">CUpti_PCSamplingPCData</a></dd>
                           <dt class="dt dlterm">stallReasonData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">stallReasonIndex</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams" title="Params for cuptiPCSamplingGetStallReasons." shape="rect">CUpti_PCSamplingGetStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structPcSamplingStallReasons" title="All available stall reasons name and respective indexes will be stored in it." shape="rect">PcSamplingStallReasons</a></dd>
                           <dt class="dt dlterm">stallReasons</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams" title="Params for cuptiPCSamplingGetStallReasons." shape="rect">CUpti_PCSamplingGetStallReasonsParams</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structPcSamplingStallReasons" title="All available stall reasons name and respective indexes will be stored in it." shape="rect">PcSamplingStallReasons</a></dd>
                           <dt class="dt dlterm">start</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOverhead" title="The activity record for CUPTI and driver overheads." shape="rect">CUpti_ActivityOverhead</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityJit" title="The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation." shape="rect">CUpti_ActivityJit</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory" title="The activity record for memory." shape="rect">CUpti_ActivityMemory</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dt class="dt dlterm">staticSharedMemory</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dt class="dt dlterm">stream</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__SynchronizeData" title="Data passed into a synchronize callback function." shape="rect">CUpti_SynchronizeData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ResourceData" title="Data passed into a resource callback function." shape="rect">CUpti_ResourceData</a></dd>
                           <dt class="dt dlterm">streamId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP4" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy5" title="The activity record for memory copies." shape="rect">CUpti_ActivityMemcpy5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityStream" title="The activity record for CUDA stream." shape="rect">CUpti_ActivityStream</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy3" title="The activity record for memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpy3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP2" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemcpyPtoP2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel2" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel3" title="The activity record for a kernel (CUDA 6.5(with sm_52 support) onwards). (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityKernel3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCudaEvent" title="The activity record for CUDA event." shape="rect">CUpti_ActivityCudaEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel" title="The activity record for kernel. (deprecated)." shape="rect">CUpti_ActivityKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy4" title="The activity record for memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpy4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP" title="The activity record for peer-to-peer memory copies." shape="rect">CUpti_ActivityMemcpyPtoP</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpyPtoP3" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemcpyPtoP3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGraphTrace" title="The activity record for trace of graph execution." shape="rect">CUpti_ActivityGraphTrace</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemcpy" title="The activity record for memory copies. (deprecated)." shape="rect">CUpti_ActivityMemcpy</a></dd>
                           <dt class="dt dlterm">structSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params" title="Params for cuptiProfilerCounterDataImageInitialize." shape="rect">CUpti_Profiler_CounterDataImage_Initialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params" title="Params for cuptiProfilerGetCounterAvailability." shape="rect">CUpti_Profiler_GetCounterAvailability_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeInitialize__Params" title="Default parameter for cuptiProfilerDeInitialize." shape="rect">CUpti_Profiler_DeInitialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__IsPassCollected__Params" title="Params for cuptiProfilerIsPassCollected." shape="rect">CUpti_Profiler_IsPassCollected_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__DisableProfiling__Params" title="Params for cuptiProfilerDisableProfiling." shape="rect">CUpti_Profiler_DisableProfiling_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint" title="Configuration and handle for a CUPTI Checkpoint." shape="rect">NV::Cupti::Checkpoint::CUpti_Checkpoint</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImageOptions" title="Input parameter to define the counterDataImage." shape="rect">CUpti_Profiler_CounterDataImageOptions</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndSession__Params" title="Params for cuptiProfilerEndSession." shape="rect">CUpti_Profiler_EndSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginSession__Params" title="Params for cuptiProfilerBeginSession." shape="rect">CUpti_Profiler_BeginSession_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EndPass__Params" title="Params for cuptiProfilerEndPass." shape="rect">CUpti_Profiler_EndPass_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__FlushCounterData__Params" title="Params for cuptiProfilerFlushCounterData." shape="rect">CUpti_Profiler_FlushCounterData_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__UnsetConfig__Params" title="Params for cuptiProfilerUnsetConfig." shape="rect">CUpti_Profiler_UnsetConfig_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__Initialize__Params" title="Default parameter for cuptiProfilerInitialize." shape="rect">CUpti_Profiler_Initialize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params" title="Params for cuptiProfilerCounterDataImageCalculateScratchBufferSize." shape="rect">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__BeginPass__Params" title="Params for cuptiProfilerBeginPass." shape="rect">CUpti_Profiler_BeginPass_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__EnableProfiling__Params" title="Params for cuptiProfilerEnableProfiling." shape="rect">CUpti_Profiler_EnableProfiling_Params</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params" title="Params for cuptiProfilerCounterDataImageInitializeScratchBuffer." shape="rect">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></dd>
                           <dt class="dt dlterm">submitted</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel4" title="The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0)." shape="rect">CUpti_ActivityKernel4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel8" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel8</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel5" title="The activity record for a kernel (CUDA 11.0(with sm_80 support) onwards). (deprecated in CUDA 11.2) This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the CUpti_ActivityKernel9 activity record." shape="rect">CUpti_ActivityKernel5</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityCdpKernel" title="The activity record for CDP (CUDA Dynamic Parallelism) kernel." shape="rect">CUpti_ActivityCdpKernel</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel9" title="The activity record for kernel." shape="rect">CUpti_ActivityKernel9</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel6" title="The activity record for kernel. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityKernel6</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityKernel7" title="The activity record for kernel. (deprecated in CUDA 11.8)." shape="rect">CUpti_ActivityKernel7</a></dd>
                           <dt class="dt dlterm">symbolName</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__CallbackData" title="Data passed into a runtime or driver API callback function." shape="rect">CUpti_CallbackData</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__t"><a name="functions__t" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">T</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">targetNestingLevel</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__SetConfig__Params" title="Params for cuptiProfilerSetConfig." shape="rect">CUpti_Profiler_SetConfig_Params</a></dd>
                           <dt class="dt dlterm">temperature</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dt class="dt dlterm">theoreticalL2Transactions</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dt class="dt dlterm">theoreticalSharedTransactions</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dt class="dt dlterm">threadId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccData" title="The activity record for OpenACC data." shape="rect">CUpti_ActivityOpenAccData</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityAPI" title="The activity record for a driver or runtime API invocation." shape="rect">CUpti_ActivityAPI</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dt class="dt dlterm">threadsExecuted</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch2" title="The activity record for source level result branch." shape="rect">CUpti_ActivityBranch2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstructionExecution" title="The activity record for source-level instruction execution." shape="rect">CUpti_ActivityInstructionExecution</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySharedAccess" title="The activity record for source-level shared access." shape="rect">CUpti_ActivitySharedAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess" title="The activity record for source-level global access. (deprecated)." shape="rect">CUpti_ActivityGlobalAccess</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess2" title="The activity record for source-level global access. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityGlobalAccess2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityGlobalAccess3" title="The activity record for source-level global access." shape="rect">CUpti_ActivityGlobalAccess3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityBranch" title="The activity record for source level result branch. (deprecated)." shape="rect">CUpti_ActivityBranch</a></dd>
                           <dt class="dt dlterm">timestamp</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory2" title="The activity record for memory." shape="rect">CUpti_ActivityMemory2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3" title="The activity record for memory." shape="rect">CUpti_ActivityMemory3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityPreemption" title="The activity record for a preemption of a CDP kernel." shape="rect">CUpti_ActivityPreemption</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEventInstance" title="The activity record for an instantaneous CUPTI event with event domain instance information." shape="rect">CUpti_ActivityInstantaneousEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker" title="The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0)." shape="rect">CUpti_ActivityMarker</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMarker2" title="The activity record providing a marker which is an instantaneous point in time." shape="rect">CUpti_ActivityMarker2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEnvironment" title="The activity record for CUPTI environmental data." shape="rect">CUpti_ActivityEnvironment</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetric" title="The activity record for an instantaneous CUPTI metric." shape="rect">CUpti_ActivityInstantaneousMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEvent" title="The activity record for an instantaneous CUPTI event." shape="rect">CUpti_ActivityInstantaneousEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dt class="dt dlterm">totalBuffers</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structHeader" title="Header info will be stored in file." shape="rect">Header</a></dd>
                           <dt class="dt dlterm">totalNumPcs</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">totalSamples</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo" title="The activity record for record status for PC sampling." shape="rect">CUpti_ActivityPCSamplingRecordInfo</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__PCSamplingData" title="Collected PC Sampling data." shape="rect">CUpti_PCSamplingData</a></dd>
                           <dt class="dt dlterm">type</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivitySynchronization" title="The activity record for synchronization management." shape="rect">CUpti_ActivitySynchronization</a></dd>
                           <dt class="dt dlterm">typeDev0</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dt class="dt dlterm">typeDev1</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink2" title="NVLink information. (deprecated in CUDA 10.0)." shape="rect">CUpti_ActivityNvLink2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink4" title="NVLink information." shape="rect">CUpti_ActivityNvLink4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink3" title="NVLink information." shape="rect">CUpti_ActivityNvLink3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityNvLink" title="NVLink information. (deprecated in CUDA 9.0)." shape="rect">CUpti_ActivityNvLink</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__u"><a name="functions__u" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">U</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">upstreamBus</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">utilizedSize</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT" shape="rect">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemoryPool2" title="The activity record for memory pool." shape="rect">CUpti_ActivityMemoryPool2</a></dd>
                           <dt class="dt dlterm">uuid</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice3" title="The activity record for a device. (CUDA 7.0 onwards)." shape="rect">CUpti_ActivityDevice3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice2" title="The activity record for a device. (deprecated)." shape="rect">CUpti_ActivityDevice2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDevice4" title="The activity record for a device. (CUDA 11.6 onwards)." shape="rect">CUpti_ActivityDevice4</a></dd>
                           <dt class="dt dlterm">uuidDev</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__v"><a name="functions__v" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">V</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">value</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset" title="The activity record for memset. (deprecated)." shape="rect">CUpti_ActivityMemset</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset2" title="The activity record for memset. (deprecated in CUDA 11.1)." shape="rect">CUpti_ActivityMemset2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset4" title="The activity record for memset." shape="rect">CUpti_ActivityMemset4</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetricInstance" title="The activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityDeviceAttribute" title="The activity record for a device attribute." shape="rect">CUpti_ActivityDeviceAttribute</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEvent" title="The activity record for a CUPTI event." shape="rect">CUpti_ActivityEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter" title="The activity record for Unified Memory counters (deprecated in CUDA 7.0)." shape="rect">CUpti_ActivityUnifiedMemoryCounter</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2" title="The activity record for Unified Memory counters (CUDA 7.0 and beyond)." shape="rect">CUpti_ActivityUnifiedMemoryCounter2</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMemset3" title="The activity record for memset. (deprecated in CUDA 11.6)." shape="rect">CUpti_ActivityMemset3</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance" title="The instantaneous activity record for a CUPTI metric with instance information." shape="rect">CUpti_ActivityInstantaneousMetricInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousMetric" title="The activity record for an instantaneous CUPTI metric." shape="rect">CUpti_ActivityInstantaneousMetric</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEventInstance" title="The activity record for an instantaneous CUPTI event with event domain instance information." shape="rect">CUpti_ActivityInstantaneousEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityEventInstance" title="The activity record for a CUPTI event with instance information." shape="rect">CUpti_ActivityEventInstance</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityInstantaneousEvent" title="The activity record for an instantaneous CUPTI event." shape="rect">CUpti_ActivityInstantaneousEvent</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityMetric" title="The activity record for a CUPTI metric." shape="rect">CUpti_ActivityMetric</a></dd>
                           <dt class="dt dlterm">vectorLength</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dt class="dt dlterm">vendorId</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityPcie" title="PCI devices information required to construct topology." shape="rect">CUpti_ActivityPcie</a></dd>
                           <dt class="dt dlterm">version</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccLaunch" title="The activity record for OpenACC launch." shape="rect">CUpti_ActivityOpenAccLaunch</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenMp" title="The base activity record for OpenMp records." shape="rect">CUpti_ActivityOpenMp</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAcc" title="The base activity record for OpenAcc records." shape="rect">CUpti_ActivityOpenAcc</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structHeader" title="Header info will be stored in file." shape="rect">Header</a></dd>
                           <dd class="dd not_first"><a class="xref" href="annotated.html#structCUpti__ActivityOpenAccOther" title="The activity record for OpenACC other." shape="rect">CUpti_ActivityOpenAccOther</a></dd>
                           <dt class="dt dlterm">vGpu</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                        </dl>
                     </div>
                     <div class="section" id="functions__w"><a name="functions__w" shape="rect">
                           <!-- --></a><h2 class="title sectiontitle">W</h2>
                        <dl class="dl table-display-members">
                           <dt class="dt dlterm">workerThreadPeriodicSleepSpanData</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__PCSamplingConfigurationInfo" title="PC sampling configuration information structure." shape="rect">CUpti_PCSamplingConfigurationInfo</a></dd>
                           <dt class="dt dlterm">wsl</dt>
                           <dd class="dd first"><a class="xref" href="annotated.html#structCUpti__Profiler__DeviceSupported__Params" title="Params for cuptiProfilerDeviceSupported." shape="rect">CUpti_Profiler_DeviceSupported_Params</a></dd>
                        </dl>
                     </div>
                  </div>
               </div>
               
               <hr id="contents-end"></hr>
               
            </article>
         </div>
      </div>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/formatting/common.min.js"></script>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-write.js"></script>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-tracker.js"></script>
      <script type="text/javascript">_satellite.pageBottom();</script>
      <script type="text/javascript">var switchTo5x=true;</script><script type="text/javascript">stLight.options({publisher: "998dc202-a267-4d8e-bce9-14debadb8d92", doNotHash: false, doNotCopy: false, hashAddressBar: false});</script></body>
</html>