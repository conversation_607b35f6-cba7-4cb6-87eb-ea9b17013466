{"arch": "x86_64", "build": "py310hfc23b7f_100", "build_number": 100, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": ["conda >=23.7"], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda-anon-usage-0.5.0-py310hfc23b7f_100", "files": ["Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/INSTALLER", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/LICENSE", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/METADATA", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/RECORD", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/REQUESTED", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/WHEEL", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/direct_url.json", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/entry_points.txt", "Lib/site-packages/anaconda_anon_usage-0.5.0.dist-info/top_level.txt", "Lib/site-packages/anaconda_anon_usage/__init__.py", "Lib/site-packages/anaconda_anon_usage/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/anaconda_anon_usage/__pycache__/_version.cpython-310.pyc", "Lib/site-packages/anaconda_anon_usage/__pycache__/install.cpython-310.pyc", "Lib/site-packages/anaconda_anon_usage/__pycache__/patch.cpython-310.pyc", "Lib/site-packages/anaconda_anon_usage/__pycache__/plugin.cpython-310.pyc", "Lib/site-packages/anaconda_anon_usage/__pycache__/tokens.cpython-310.pyc", "Lib/site-packages/anaconda_anon_usage/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/anaconda_anon_usage/_version.py", "Lib/site-packages/anaconda_anon_usage/patch.py", "Lib/site-packages/anaconda_anon_usage/plugin.py", "Lib/site-packages/anaconda_anon_usage/tokens.py", "Lib/site-packages/anaconda_anon_usage/utils.py"], "fn": "anaconda-anon-usage-0.5.0-py310hfc23b7f_100.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda-anon-usage-0.5.0-py310hfc23b7f_100", "type": 1}, "md5": "893c3cd6b27d0bcb92d857e662f78738", "name": "anaconda-anon-usage", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda-anon-usage-0.5.0-py310hfc23b7f_100.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::anaconda-anon-usage==0.5.0=py310hfc23b7f_100[md5=893c3cd6b27d0bcb92d857e662f78738]", "sha256": "ddbbb7aa4e6c4532e25680b7ca9e34318241b30e925ecd9b57afdeecedc75586", "size": 28515, "subdir": "win-64", "timestamp": 1732732631000, "url": "https://repo.anaconda.com/pkgs/main/win-64/anaconda-anon-usage-0.5.0-py310hfc23b7f_100.conda", "version": "0.5.0"}