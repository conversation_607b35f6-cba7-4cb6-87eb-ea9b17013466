<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityJit Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityJit Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityJit" -->The activity record for JIT operations. This activity represents the JIT operations (compile, load, store) of a CUmodule from the Compute Cache. Gives the exact hashed path of where the cached module is loaded from, or where the module will be stored after Just-In-Time (JIT) compilation.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#5a79bcb1b9718b714be007e9f5b15a9e">cachePath</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#0b97b0e162c2e018235a8f48fcd47b6d">cacheSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#e2f63d8035076a80c9fe51c822bf78bc">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#ddac75813ca52f12a60552bb7cee47fc">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#f950b7889a01a46b90cf3fd92e8035f6">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0367503f3fc0af7b3c5051a9408efa80">CUpti_ActivityJitEntryType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#322622f4cff44d90b81e4864a240ac11">jitEntryType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#bd1fcf25f9e07c93e151c8a386cd8f8e">jitOperationCorrelationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g2dc4ccb146a37875b3d8564b8100e59a">CUpti_ActivityJitOperationType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#0da44799ae68437b2915057ed9556deb">jitOperationType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#a31c2a7a58036e6b267c3852ce08974b">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#6f4a9f5127664c72c113a1365666f156">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityJit.html#cc435cb5eedd5179968dc315b0ba49db">start</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="5a79bcb1b9718b714be007e9f5b15a9e"></a><!-- doxytag: member="CUpti_ActivityJit::cachePath" ref="5a79bcb1b9718b714be007e9f5b15a9e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityJit.html#5a79bcb1b9718b714be007e9f5b15a9e">CUpti_ActivityJit::cachePath</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The path where the fat binary is cached. 
</div>
</div><p>
<a class="anchor" name="0b97b0e162c2e018235a8f48fcd47b6d"></a><!-- doxytag: member="CUpti_ActivityJit::cacheSize" ref="0b97b0e162c2e018235a8f48fcd47b6d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityJit.html#0b97b0e162c2e018235a8f48fcd47b6d">CUpti_ActivityJit::cacheSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of compute cache. 
</div>
</div><p>
<a class="anchor" name="e2f63d8035076a80c9fe51c822bf78bc"></a><!-- doxytag: member="CUpti_ActivityJit::correlationId" ref="e2f63d8035076a80c9fe51c822bf78bc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityJit.html#e2f63d8035076a80c9fe51c822bf78bc">CUpti_ActivityJit::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the JIT operation to which records belong to. Each JIT operation is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the JIT operation. 
</div>
</div><p>
<a class="anchor" name="ddac75813ca52f12a60552bb7cee47fc"></a><!-- doxytag: member="CUpti_ActivityJit::deviceId" ref="ddac75813ca52f12a60552bb7cee47fc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityJit.html#ddac75813ca52f12a60552bb7cee47fc">CUpti_ActivityJit::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device ID. 
</div>
</div><p>
<a class="anchor" name="f950b7889a01a46b90cf3fd92e8035f6"></a><!-- doxytag: member="CUpti_ActivityJit::end" ref="f950b7889a01a46b90cf3fd92e8035f6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityJit.html#f950b7889a01a46b90cf3fd92e8035f6">CUpti_ActivityJit::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the JIT operation, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the JIT operation. 
</div>
</div><p>
<a class="anchor" name="322622f4cff44d90b81e4864a240ac11"></a><!-- doxytag: member="CUpti_ActivityJit::jitEntryType" ref="322622f4cff44d90b81e4864a240ac11" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0367503f3fc0af7b3c5051a9408efa80">CUpti_ActivityJitEntryType</a> <a class="el" href="structCUpti__ActivityJit.html#322622f4cff44d90b81e4864a240ac11">CUpti_ActivityJit::jitEntryType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The JIT entry type. 
</div>
</div><p>
<a class="anchor" name="bd1fcf25f9e07c93e151c8a386cd8f8e"></a><!-- doxytag: member="CUpti_ActivityJit::jitOperationCorrelationId" ref="bd1fcf25f9e07c93e151c8a386cd8f8e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityJit.html#bd1fcf25f9e07c93e151c8a386cd8f8e">CUpti_ActivityJit::jitOperationCorrelationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID to correlate JIT compilation, load and store operations. Each JIT compilation unit is assigned a unique correlation ID at the time of the JIT compilation. This correlation id can be used to find the matching JIT cache load/store records. 
</div>
</div><p>
<a class="anchor" name="0da44799ae68437b2915057ed9556deb"></a><!-- doxytag: member="CUpti_ActivityJit::jitOperationType" ref="0da44799ae68437b2915057ed9556deb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g2dc4ccb146a37875b3d8564b8100e59a">CUpti_ActivityJitOperationType</a> <a class="el" href="structCUpti__ActivityJit.html#0da44799ae68437b2915057ed9556deb">CUpti_ActivityJit::jitOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The JIT operation type. 
</div>
</div><p>
<a class="anchor" name="a31c2a7a58036e6b267c3852ce08974b"></a><!-- doxytag: member="CUpti_ActivityJit::kind" ref="a31c2a7a58036e6b267c3852ce08974b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityJit.html#a31c2a7a58036e6b267c3852ce08974b">CUpti_ActivityJit::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind must be CUPTI_ACTIVITY_KIND_JIT. 
</div>
</div><p>
<a class="anchor" name="6f4a9f5127664c72c113a1365666f156"></a><!-- doxytag: member="CUpti_ActivityJit::padding" ref="6f4a9f5127664c72c113a1365666f156" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityJit.html#6f4a9f5127664c72c113a1365666f156">CUpti_ActivityJit::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Internal use. 
</div>
</div><p>
<a class="anchor" name="cc435cb5eedd5179968dc315b0ba49db"></a><!-- doxytag: member="CUpti_ActivityJit::start" ref="cc435cb5eedd5179968dc315b0ba49db" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityJit.html#cc435cb5eedd5179968dc315b0ba49db">CUpti_ActivityJit::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the JIT operation, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the JIT operation. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
