{"arch": "x86_64", "build": "py310h636fa0f_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0", "typing-extensions >=4.6.0,!=4.7.0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\pydantic-core-2.27.1-py310h636fa0f_0", "files": ["Lib/site-packages/pydantic_core-2.27.1.dist-info/INSTALLER", "Lib/site-packages/pydantic_core-2.27.1.dist-info/METADATA", "Lib/site-packages/pydantic_core-2.27.1.dist-info/RECORD", "Lib/site-packages/pydantic_core-2.27.1.dist-info/REQUESTED", "Lib/site-packages/pydantic_core-2.27.1.dist-info/WHEEL", "Lib/site-packages/pydantic_core-2.27.1.dist-info/direct_url.json", "Lib/site-packages/pydantic_core-2.27.1.dist-info/license_files/LICENSE", "Lib/site-packages/pydantic_core/__init__.py", "Lib/site-packages/pydantic_core/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pydantic_core/__pycache__/core_schema.cpython-310.pyc", "Lib/site-packages/pydantic_core/_pydantic_core.cp310-win_amd64.pyd", "Lib/site-packages/pydantic_core/_pydantic_core.pyi", "Lib/site-packages/pydantic_core/core_schema.py", "Lib/site-packages/pydantic_core/py.typed"], "fn": "pydantic-core-2.27.1-py310h636fa0f_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\pydantic-core-2.27.1-py310h636fa0f_0", "type": 1}, "md5": "adc59b02aa6884816a5c981dfa31f709", "name": "pydantic-core", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\pydantic-core-2.27.1-py310h636fa0f_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::pydantic-core==2.27.1=py310h636fa0f_0[md5=adc59b02aa6884816a5c981dfa31f709]", "sha256": "fd716853164ee3a59d392d121acc291fe1839f1dc4650c48001dab1f3f9cf1a2", "size": 1841277, "subdir": "win-64", "timestamp": 1734727367000, "url": "https://repo.anaconda.com/pkgs/main/win-64/pydantic-core-2.27.1-py310h636fa0f_0.conda", "version": "2.27.1"}