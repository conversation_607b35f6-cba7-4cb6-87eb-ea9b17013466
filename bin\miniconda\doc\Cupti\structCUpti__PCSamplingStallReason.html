<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingStallReason Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingStallReason Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingStallReason" -->PC Sampling stall reasons.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingStallReason.html#c11d563facfd8d0bb4d05f08d5450bd3">pcSamplingStallReasonIndex</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingStallReason.html#c406f7573333f7d501cf0acd1894bb31">samples</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="c11d563facfd8d0bb4d05f08d5450bd3"></a><!-- doxytag: member="CUpti_PCSamplingStallReason::pcSamplingStallReasonIndex" ref="c11d563facfd8d0bb4d05f08d5450bd3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__PCSamplingStallReason.html#c11d563facfd8d0bb4d05f08d5450bd3">CUpti_PCSamplingStallReason::pcSamplingStallReasonIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Collected stall reason index 
</div>
</div><p>
<a class="anchor" name="c406f7573333f7d501cf0acd1894bb31"></a><!-- doxytag: member="CUpti_PCSamplingStallReason::samples" ref="c406f7573333f7d501cf0acd1894bb31" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__PCSamplingStallReason.html#c406f7573333f7d501cf0acd1894bb31">CUpti_PCSamplingStallReason::samples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Number of times the PC was sampled with the stallReason. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
