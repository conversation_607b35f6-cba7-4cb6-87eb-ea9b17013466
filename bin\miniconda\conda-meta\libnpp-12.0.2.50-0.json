{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnpp-12.0.2.50-0", "features": "", "files": ["lib/x64/nppc.lib", "lib/x64/nppial.lib", "lib/x64/nppicc.lib", "lib/x64/nppidei.lib", "lib/x64/nppif.lib", "lib/x64/nppig.lib", "lib/x64/nppim.lib", "lib/x64/nppist.lib", "lib/x64/nppisu.lib", "lib/x64/nppitc.lib", "lib/x64/npps.lib"], "fn": "libnpp-12.0.2.50-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnpp-12.0.2.50-0", "type": 1}, "md5": "67d3d8ea8018683f39d88befc10984bb", "name": "libnpp", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnpp-12.0.2.50-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/nppc.lib", "path_type": "hardlink", "sha256": "8fba44427a0f964acb1f80aec3e65bc90200706430e2c2eed067c1069daa2e7a", "sha256_in_prefix": "8fba44427a0f964acb1f80aec3e65bc90200706430e2c2eed067c1069daa2e7a", "size_in_bytes": 4710}, {"_path": "lib/x64/nppial.lib", "path_type": "hardlink", "sha256": "35178059ee05f57a19a70ea20617bcde7a61309d1165cf7328b5e4a937140d84", "sha256_in_prefix": "35178059ee05f57a19a70ea20617bcde7a61309d1165cf7328b5e4a937140d84", "size_in_bytes": 503164}, {"_path": "lib/x64/nppicc.lib", "path_type": "hardlink", "sha256": "9ffea823f2bbce5bb063c8b1e27c323676fe71638d54e0633d9cb5acb566da18", "sha256_in_prefix": "9ffea823f2bbce5bb063c8b1e27c323676fe71638d54e0633d9cb5acb566da18", "size_in_bytes": 284888}, {"_path": "lib/x64/nppidei.lib", "path_type": "hardlink", "sha256": "dc13d734b749ecc2d181e5f066648c4ed858601822082673ccae8c557bc51f9e", "sha256_in_prefix": "dc13d734b749ecc2d181e5f066648c4ed858601822082673ccae8c557bc51f9e", "size_in_bytes": 392410}, {"_path": "lib/x64/nppif.lib", "path_type": "hardlink", "sha256": "6df4a40b96307db2dcb9c442b74efe490b98d39fc7081cd960f1112abeafbc91", "sha256_in_prefix": "6df4a40b96307db2dcb9c442b74efe490b98d39fc7081cd960f1112abeafbc91", "size_in_bytes": 649354}, {"_path": "lib/x64/nppig.lib", "path_type": "hardlink", "sha256": "2b44a2bc9301f38ff6694bd07f9674df406a767ed7bcca978d661b69e10333c7", "sha256_in_prefix": "2b44a2bc9301f38ff6694bd07f9674df406a767ed7bcca978d661b69e10333c7", "size_in_bytes": 184146}, {"_path": "lib/x64/nppim.lib", "path_type": "hardlink", "sha256": "10eca33705017d8f15aaa069d8bffb47b7c3c5653fd1f3adb9a27a7dc294cc30", "sha256_in_prefix": "10eca33705017d8f15aaa069d8bffb47b7c3c5653fd1f3adb9a27a7dc294cc30", "size_in_bytes": 88484}, {"_path": "lib/x64/nppist.lib", "path_type": "hardlink", "sha256": "eebfe3963ad807ae121fd38c567d2b8b8ace78b8bf2a7490a50fa2b91209aa3e", "sha256_in_prefix": "eebfe3963ad807ae121fd38c567d2b8b8ace78b8bf2a7490a50fa2b91209aa3e", "size_in_bytes": 1044382}, {"_path": "lib/x64/nppisu.lib", "path_type": "hardlink", "sha256": "9fb2b7a8f8ce9801d156bd0e0e57a301a0dd634e0c85b650e8171936bbae2784", "sha256_in_prefix": "9fb2b7a8f8ce9801d156bd0e0e57a301a0dd634e0c85b650e8171936bbae2784", "size_in_bytes": 8416}, {"_path": "lib/x64/nppitc.lib", "path_type": "hardlink", "sha256": "616cf33f8a1e35144fa0b5f7d08d0efd8e3104adb9d6674dae18d2cc8cf15a7e", "sha256_in_prefix": "616cf33f8a1e35144fa0b5f7d08d0efd8e3104adb9d6674dae18d2cc8cf15a7e", "size_in_bytes": 126246}, {"_path": "lib/x64/npps.lib", "path_type": "hardlink", "sha256": "fac4d4d3a297f3a84e721e07be5d4791b24c7578a093bae692d620c9eb98ad6a", "sha256_in_prefix": "fac4d4d3a297f3a84e721e07be5d4791b24c7578a093bae692d620c9eb98ad6a", "size_in_bytes": 460904}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 312620, "subdir": "win-64", "timestamp": 1675319837000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libnpp-12.0.2.50-0.tar.bz2", "version": "12.0.2.50"}