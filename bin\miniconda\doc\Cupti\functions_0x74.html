<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li class="current"><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all documented struct and union fields with links to the struct/union documentation for each field:
<p>
<h3><a class="anchor" name="index_t">- t -</a></h3><ul>
<li>targetNestingLevel
: <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#91c1e47aa6642ff97555a5619c6a6ba3">CUpti_Profiler_SetConfig_Params</a>
<li>temperature
: <a class="el" href="structCUpti__ActivityEnvironment.html#330d18924ce3e785806244fdb8f7fca9">CUpti_ActivityEnvironment</a>
<li>theoreticalL2Transactions
: <a class="el" href="structCUpti__ActivityGlobalAccess3.html#097d3828fdde7d24df95ca676a16f221">CUpti_ActivityGlobalAccess3</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#2d675310a86f099a284b26f8a8dbc0a9">CUpti_ActivityGlobalAccess2</a>
<li>theoreticalSharedTransactions
: <a class="el" href="structCUpti__ActivitySharedAccess.html#89882826e508fc592e58876ccc196b22">CUpti_ActivitySharedAccess</a>
<li>threadId
: <a class="el" href="structCUpti__ActivityOpenAccData.html#10c9b3f640c146f293a54bd8747aaa60">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#d1690212b96257e354aadf6da889f666">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#ea584646498d30acfb9a3722d422b394">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenMp.html#d2953d5453b295c412038ec775c26eea">CUpti_ActivityOpenMp</a>
, <a class="el" href="structCUpti__ActivityAPI.html#d78f64c5ac657f33face821f25b5d9c1">CUpti_ActivityAPI</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#1b2cf3c8327c2598a812b00d9d3fb246">CUpti_ActivityOpenAcc</a>
<li>threadsExecuted
: <a class="el" href="structCUpti__ActivityBranch2.html#89ca5b9bfde56fb3437062bec4e241d1">CUpti_ActivityBranch2</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#f6ec4bbd487d71c1769f46fc8f1f8c02">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#8b227d1cbbb2c9eabcd037724f69a6a7">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess.html#59c2b836e5a884774d81193053f9b7b7">CUpti_ActivityGlobalAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#0bfbfbb07ef13a1df8b89fda4fef8f2e">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#7d06f4a8c882ef0591501cf7488dff0d">CUpti_ActivityGlobalAccess3</a>
, <a class="el" href="structCUpti__ActivityBranch.html#af46d0ef2afa4ec8903314ec095ce2b2">CUpti_ActivityBranch</a>
<li>timestamp
: <a class="el" href="structCUpti__ActivityMemory2.html#94fa41c60dd647509fa69ff76b191552">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#a2d7ab77c0793a3f46e1790d0a5ab78e">CUpti_ActivityUnifiedMemoryCounter</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#5c48ceb7af7d409584f1ec86fbc2c6da">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#8dd5075b882ee9f4fa3adb0c2d71151c">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#dba2a2a9099ee6957de50f28b6a642cc">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityPreemption.html#73c679e8a3f9b559b648705c95bd8b51">CUpti_ActivityPreemption</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEventInstance.html#06ae58d8edd0e3a3fa779b7c5405b9e9">CUpti_ActivityInstantaneousEventInstance</a>
, <a class="el" href="structCUpti__ActivityMarker.html#af62263cba8b33e679ecc5e3afae3ef1">CUpti_ActivityMarker</a>
, <a class="el" href="structCUpti__ActivityMarker2.html#12512957066b6e129046099d7e9735a0">CUpti_ActivityMarker2</a>
, <a class="el" href="structCUpti__ActivityEnvironment.html#e015f7d1a880d8948df34c90e28e679a">CUpti_ActivityEnvironment</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#03e0fe94bfb2add6b6aa68ac9163d4d3">CUpti_ActivityInstantaneousMetric</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#b0f959ed8195a1437c959a0d932f5bfa">CUpti_ActivityInstantaneousEvent</a>
, <a class="el" href="structCUpti__ActivityInstantaneousMetricInstance.html#01dfb11ab9544de0e53d1f7d2ea29255">CUpti_ActivityInstantaneousMetricInstance</a>
<li>totalBuffers
: <a class="el" href="structHeader.html#7ce481d8fe28bab8893fc93b6f7d86a5">Header</a>
<li>totalNumPcs
: <a class="el" href="structCUpti__PCSamplingData.html#3e8220b654f65b35d3a44c1c6191fa5c">CUpti_PCSamplingData</a>
<li>totalSamples
: <a class="el" href="structCUpti__ActivityPCSamplingRecordInfo.html#51e3e3bb43d36d240a7b9e22bdac6791">CUpti_ActivityPCSamplingRecordInfo</a>
, <a class="el" href="structCUpti__PCSamplingData.html#37f913259cadfb0f61c2e37204328a16">CUpti_PCSamplingData</a>
<li>type
: <a class="el" href="structCUpti__ActivityPcie.html#d08f77ef897e067279f406d2a9420427">CUpti_ActivityPcie</a>
, <a class="el" href="structCUpti__ActivitySynchronization.html#394d7c722338b1fb8c425efe79e8f1b2">CUpti_ActivitySynchronization</a>
<li>typeDev0
: <a class="el" href="structCUpti__ActivityNvLink4.html#045b345ced0bc553c82a7d48e994538c">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#0078203dad557507bbb83034e4aaf6bd">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink.html#1e27bdb7050da1582f23c42857a9ded1">CUpti_ActivityNvLink</a>
, <a class="el" href="structCUpti__ActivityNvLink2.html#eb937266117b2e71c7438fc63f9a6cbb">CUpti_ActivityNvLink2</a>
<li>typeDev1
: <a class="el" href="structCUpti__ActivityNvLink2.html#505ebfd0a0324e4bd11bf90d4773826d">CUpti_ActivityNvLink2</a>
, <a class="el" href="structCUpti__ActivityNvLink4.html#a0f6bf083a7f271984b7d1b9580144d8">CUpti_ActivityNvLink4</a>
, <a class="el" href="structCUpti__ActivityNvLink3.html#7a16913df232fb649af06eec8b71c3ea">CUpti_ActivityNvLink3</a>
, <a class="el" href="structCUpti__ActivityNvLink.html#f5b62aa4efc633a0350a45305617deb7">CUpti_ActivityNvLink</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
