{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\annotated-types-0.6.0-py310haa95532_0", "files": ["Lib/site-packages/annotated_types-0.6.0.dist-info/INSTALLER", "Lib/site-packages/annotated_types-0.6.0.dist-info/METADATA", "Lib/site-packages/annotated_types-0.6.0.dist-info/RECORD", "Lib/site-packages/annotated_types-0.6.0.dist-info/REQUESTED", "Lib/site-packages/annotated_types-0.6.0.dist-info/WHEEL", "Lib/site-packages/annotated_types-0.6.0.dist-info/direct_url.json", "Lib/site-packages/annotated_types-0.6.0.dist-info/licenses/LICENSE", "Lib/site-packages/annotated_types/__init__.py", "Lib/site-packages/annotated_types/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/annotated_types/__pycache__/test_cases.cpython-310.pyc", "Lib/site-packages/annotated_types/py.typed", "Lib/site-packages/annotated_types/test_cases.py"], "fn": "annotated-types-0.6.0-py310haa95532_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\annotated-types-0.6.0-py310haa95532_0", "type": 1}, "md5": "4be9ffb179b88aed65db8a5696d443cd", "name": "annotated-types", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\annotated-types-0.6.0-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::annotated-types==0.6.0=py310haa95532_0[md5=4be9ffb179b88aed65db8a5696d443cd]", "sha256": "3c03315ab58ed4d409ad43f10c3b221f8555886d091ed071776b97a2ecca8f15", "size": 23522, "subdir": "win-64", "timestamp": 1709543029000, "url": "https://repo.anaconda.com/pkgs/main/win-64/annotated-types-0.6.0-py310haa95532_0.conda", "version": "0.6.0"}