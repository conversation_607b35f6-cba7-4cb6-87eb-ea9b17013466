{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-cupti >=12.1.62", "cuda-nvdisasm >=12.1.55", "cuda-nvprof >=12.1.55", "cuda-nvtx >=12.1.66", "cuda-sanitizer-api >=12.1.55"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-command-line-tools-12.1.0-0", "features": "", "files": [], "fn": "cuda-command-line-tools-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-command-line-tools-12.1.0-0", "type": 1}, "md5": "3eb589e009fd6796321f772e973d20bb", "name": "cuda-command-line-tools", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-command-line-tools-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1442, "subdir": "win-64", "timestamp": 1677129941000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-command-line-tools-12.1.0-0.tar.bz2", "version": "12.1.0"}