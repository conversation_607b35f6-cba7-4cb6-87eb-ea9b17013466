<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI Event API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUPTI Event API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__EventGroupSet.html">CUpti_EventGroupSet</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A set of event groups.  <a href="structCUpti__EventGroupSet.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A set of event group sets.  <a href="structCUpti__EventGroupSets.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gafb9f9296e7d3df0e9ee891830cafa3a"></a><!-- doxytag: member="CUPTI_EVENT_API::CUPTI_EVENT_INVALID" ref="gafb9f9296e7d3df0e9ee891830cafa3a" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gafb9f9296e7d3df0e9ee891830cafa3a">CUPTI_EVENT_INVALID</a>&nbsp;&nbsp;&nbsp;((uint64_t)0xFFFFFFFFFFFFFFFEULL)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The value that indicates the event value is invalid. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g4f280929637a726da2ba4c907e5b9ba1">CUPTI_EVENT_OVERFLOW</a>&nbsp;&nbsp;&nbsp;((uint64_t)0xFFFFFFFFFFFFFFFFULL)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The overflow value for a CUPTI event.  <a href="#g4f280929637a726da2ba4c907e5b9ba1"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">ID for an event domain.  <a href="#g8a898bd33ce64d353b79d7d39e74d198"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">A group of events.  <a href="#g649750f363752bccbf9e98582d5f6925"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">ID for an event.  <a href="#g6ce7370be9ed31ce6f2d475de39045df"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef void(*&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g4c9cefe906f4af450ae0ad5dc1d39b1c">CUpti_KernelReplayUpdateFunc</a> )(const char *kernelName, int numReplaysDone, void *customData)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Function type for getting updates on kernel replay.  <a href="#g4c9cefe906f4af450ae0ad5dc1d39b1c"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g07af1fece58c6f17c48067740034e31b">CUpti_DeviceAttribute</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b6006619ba70b90a1a787673cf64a8e5c">CUPTI_DEVICE_ATTR_MAX_EVENT_ID</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b514c67f9d512fa9970b456ed4062ebb8">CUPTI_DEVICE_ATTR_MAX_EVENT_DOMAIN_ID</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b7e95692e0edfb518b1170b3707b630ad">CUPTI_DEVICE_ATTR_GLOBAL_MEMORY_BANDWIDTH</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b5a96aad93301bb10e92cacc6856432fc">CUPTI_DEVICE_ATTR_INSTRUCTION_PER_CYCLE</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b0891b8b997de3c94ab70ddd240efb594">CUPTI_DEVICE_ATTR_INSTRUCTION_THROUGHPUT_SINGLE_PRECISION</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31be6e195d7e69d82b47a6fb811bbb1b44f">CUPTI_DEVICE_ATTR_MAX_FRAME_BUFFERS</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31ba7e128bc358880df56ec911bec400097">CUPTI_DEVICE_ATTR_PCIE_LINK_RATE</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31bc71dfb398189e6084aa5ddaa1f33fe3b">CUPTI_DEVICE_ATTR_PCIE_LINK_WIDTH</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31bf9794e9b1a5a41b586ffb249dfd7db7c">CUPTI_DEVICE_ATTR_PCIE_GEN</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31bcc1c7544343bfe738fdc499a4e4293c9">CUPTI_DEVICE_ATTR_DEVICE_CLASS</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b7256e4ede078800e29511aa7136884c7">CUPTI_DEVICE_ATTR_FLOP_SP_PER_CYCLE</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b1ab37ff50a14b66a446d7260aefa2df5">CUPTI_DEVICE_ATTR_FLOP_DP_PER_CYCLE</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31be4280195644be12af2abd413e9a41c31">CUPTI_DEVICE_ATTR_MAX_L2_UNITS</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b7fac6cc5865ce3126a4d102a276231b2">CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_SHARED</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b208d80386d7f5c7298419543921183da">CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_L1</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31bd78875a015662732a0af8fdd932b990c">CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_EQUAL</a> =  16, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b9d19daa5ea3d8cc57c6f5653dcff9a9c">CUPTI_DEVICE_ATTR_FLOP_HP_PER_CYCLE</a> =  17, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31b5d5f0617cb3cddae7a31b6b5cae69dad">CUPTI_DEVICE_ATTR_NVLINK_PRESENT</a> =  18, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31bafee592a6302a3df0e6626520f149264">CUPTI_DEVICE_ATTR_GPU_CPU_NVLINK_BW</a> =  19, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg07af1fece58c6f17c48067740034e31ba3c3c3077abdb5c809c1ed1390ca5373">CUPTI_DEVICE_ATTR_NVSWITCH_PRESENT</a> =  20
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Device attributes.  <a href="group__CUPTI__EVENT__API.html#g07af1fece58c6f17c48067740034e31b">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g77fc6151563b2f16b4eeea3d9bdbf3f3">CUpti_DeviceAttributeDeviceClass</a> </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Device class.  <a href="group__CUPTI__EVENT__API.html#g77fc6151563b2f16b4eeea3d9bdbf3f3">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g232a6c540682383abd4cee93ee18e6aa">CUpti_EventAttribute</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg232a6c540682383abd4cee93ee18e6aa4a6394ddd536a73cb3446831a6efc9c4">CUPTI_EVENT_ATTR_NAME</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg232a6c540682383abd4cee93ee18e6aae336de744661553a7bde8e8f704967b2">CUPTI_EVENT_ATTR_SHORT_DESCRIPTION</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg232a6c540682383abd4cee93ee18e6aaac191fb2f48e00651ac86c18d1abbd9c">CUPTI_EVENT_ATTR_LONG_DESCRIPTION</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg232a6c540682383abd4cee93ee18e6aac8173e23f5d3806e7ccd397e3d6638d9">CUPTI_EVENT_ATTR_CATEGORY</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg232a6c540682383abd4cee93ee18e6aa2803d24d461af7124ccb017c736a31c8">CUPTI_EVENT_ATTR_PROFILING_SCOPE</a> =  5
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Event attributes.  <a href="group__CUPTI__EVENT__API.html#g232a6c540682383abd4cee93ee18e6aa">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gd9604f1d97d7c84664aa37f325e35f39">CUpti_EventCategory</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggd9604f1d97d7c84664aa37f325e35f39b3f3e075259468f241de0e6cd7ccc2f9">CUPTI_EVENT_CATEGORY_INSTRUCTION</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggd9604f1d97d7c84664aa37f325e35f39d5390275d63008f422b994ed2cc7ecc5">CUPTI_EVENT_CATEGORY_MEMORY</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggd9604f1d97d7c84664aa37f325e35f39c820faabb8a0dd73281cae7cbb1201c2">CUPTI_EVENT_CATEGORY_CACHE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggd9604f1d97d7c84664aa37f325e35f39040f655492ae007620479cd9a9853cac">CUPTI_EVENT_CATEGORY_PROFILE_TRIGGER</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggd9604f1d97d7c84664aa37f325e35f39d395ffa93358255e47324b0290901fad">CUPTI_EVENT_CATEGORY_SYSTEM</a> =  4
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">An event category.  <a href="group__CUPTI__EVENT__API.html#gd9604f1d97d7c84664aa37f325e35f39">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gdafc575cd4614872eba5d99e2f2bc831">CUpti_EventCollectionMethod</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggdafc575cd4614872eba5d99e2f2bc8312c8858ae323136db8b0fa694dfeace26">CUPTI_EVENT_COLLECTION_METHOD_PM</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggdafc575cd4614872eba5d99e2f2bc83118c829ff673304c6ead4854c17addf04">CUPTI_EVENT_COLLECTION_METHOD_SM</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggdafc575cd4614872eba5d99e2f2bc831d24aad7cd6c912d0d6419642f984f44b">CUPTI_EVENT_COLLECTION_METHOD_INSTRUMENTED</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggdafc575cd4614872eba5d99e2f2bc831f7eed1727d5b44144bc12edcb13640e7">CUPTI_EVENT_COLLECTION_METHOD_NVLINK_TC</a> =  3
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The collection method used for an event.  <a href="group__CUPTI__EVENT__API.html#gdafc575cd4614872eba5d99e2f2bc831">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g54361f7bed89987eabb91300cc218712">CUpti_EventCollectionMode</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg54361f7bed89987eabb91300cc21871200547df82922544723de3f7ed4f8aea8">CUPTI_EVENT_COLLECTION_MODE_CONTINUOUS</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg54361f7bed89987eabb91300cc218712ea24eb0dbf64d034bedac90f326e0e1d">CUPTI_EVENT_COLLECTION_MODE_KERNEL</a> =  1
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Event collection modes.  <a href="group__CUPTI__EVENT__API.html#g54361f7bed89987eabb91300cc218712">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#ga201105be2a6a988b72e70a643c82ad5">CUpti_EventDomainAttribute</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gga201105be2a6a988b72e70a643c82ad5d4814f4b5e298b0aff246585fdf6dc16">CUPTI_EVENT_DOMAIN_ATTR_NAME</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gga201105be2a6a988b72e70a643c82ad5af06e2bff439ac6ef15b348873e21f2d">CUPTI_EVENT_DOMAIN_ATTR_INSTANCE_COUNT</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gga201105be2a6a988b72e70a643c82ad513400cf57ccf213e2f62acaad7bb4c3f">CUPTI_EVENT_DOMAIN_ATTR_TOTAL_INSTANCE_COUNT</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gga201105be2a6a988b72e70a643c82ad5ba774bdb36c899b9359d437318813d91">CUPTI_EVENT_DOMAIN_ATTR_COLLECTION_METHOD</a> =  4
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Event domain attributes.  <a href="group__CUPTI__EVENT__API.html#ga201105be2a6a988b72e70a643c82ad5">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gc8aaac37b2aefeea5177c0b813806b9f">CUpti_EventGroupAttribute</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9fddae3327832db311c655f258e94b47b1">CUPTI_EVENT_GROUP_ATTR_EVENT_DOMAIN_ID</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f776be3128e2ab834a96ec9ff16dda611">CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9ffc374bac14388fc27bfc6bfaeefd3970">CUPTI_EVENT_GROUP_ATTR_USER_DATA</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f86ac9ea1f6da12140f30c3c6f42522d1">CUPTI_EVENT_GROUP_ATTR_NUM_EVENTS</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f5163e8a0d301e48732bfbd71065e725a">CUPTI_EVENT_GROUP_ATTR_EVENTS</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f46001f8b8e2928977ae48b2637cdc939">CUPTI_EVENT_GROUP_ATTR_INSTANCE_COUNT</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f2e15e285c661b8767ffa5e3d42fb26a8">CUPTI_EVENT_GROUP_ATTR_PROFILING_SCOPE</a> =  6
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Event group attributes.  <a href="group__CUPTI__EVENT__API.html#gc8aaac37b2aefeea5177c0b813806b9f">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g5fbde5d55673e4d937491e6ea9631940">CUpti_EventProfilingScope</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg5fbde5d55673e4d937491e6ea963194002acd9ac5f62339379285daa9aa1a172">CUPTI_EVENT_PROFILING_SCOPE_CONTEXT</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg5fbde5d55673e4d937491e6ea96319404e57028e6fe2e3a3d6bd8d1076089901">CUPTI_EVENT_PROFILING_SCOPE_DEVICE</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__CUPTI__EVENT__API.html#gg5fbde5d55673e4d937491e6ea96319409fbac86ec79d99e00f16c79e7f46ccb2">CUPTI_EVENT_PROFILING_SCOPE_BOTH</a> =  2
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Profiling scope for event.  <a href="group__CUPTI__EVENT__API.html#g5fbde5d55673e4d937491e6ea9631940">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#ge8ea7de2653cc087a9c5399386604528">CUpti_ReadEventFlags</a> { <a class="el" href="group__CUPTI__EVENT__API.html#gge8ea7de2653cc087a9c5399386604528726bee26555027440a8b4acdf6ff13a3">CUPTI_EVENT_READ_FLAG_NONE</a> =  0
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags for cuptiEventGroupReadEvent an cuptiEventGroupReadAllEvents.  <a href="group__CUPTI__EVENT__API.html#ge8ea7de2653cc087a9c5399386604528">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g430869bc1bfcc11589241bac94774c06">cuptiDeviceEnumEventDomains</a> (CUdevice device, size_t *arraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> *domainArray)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the event domains for a device.  <a href="#g430869bc1bfcc11589241bac94774c06"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g631a8f494a7f0f3d254ad49f2040eff5">cuptiDeviceGetAttribute</a> (CUdevice device, <a class="el" href="group__CUPTI__EVENT__API.html#g07af1fece58c6f17c48067740034e31b">CUpti_DeviceAttribute</a> attrib, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read a device attribute.  <a href="#g631a8f494a7f0f3d254ad49f2040eff5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gbb745099ffef139c4ee98a1de82fcac2">cuptiDeviceGetEventDomainAttribute</a> (CUdevice device, <a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> eventDomain, <a class="el" href="group__CUPTI__EVENT__API.html#ga201105be2a6a988b72e70a643c82ad5">CUpti_EventDomainAttribute</a> attrib, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read an event domain attribute.  <a href="#gbb745099ffef139c4ee98a1de82fcac2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gfebb6c61c0c1f8427e187223d799cda5">cuptiDeviceGetNumEventDomains</a> (CUdevice device, uint32_t *numDomains)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the number of domains for a device.  <a href="#gfebb6c61c0c1f8427e187223d799cda5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g581c6a513e81f9431854561f43c06f36">cuptiDeviceGetTimestamp</a> (CUcontext context, uint64_t *timestamp)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read a device timestamp.  <a href="#g581c6a513e81f9431854561f43c06f36"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g1746f087372090b5289e2512864b8e25">cuptiDisableKernelReplayMode</a> (CUcontext context)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Disable kernel replay mode.  <a href="#g1746f087372090b5289e2512864b8e25"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g2a99f01445e5ba2f7e4ba89796e9b20f">cuptiEnableKernelReplayMode</a> (CUcontext context)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable kernel replay mode.  <a href="#g2a99f01445e5ba2f7e4ba89796e9b20f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g3f6f5236a79cafda724796dc2d599ad1">cuptiEnumEventDomains</a> (size_t *arraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> *domainArray)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the event domains available on any device.  <a href="#g3f6f5236a79cafda724796dc2d599ad1"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gd23627587d3f21c3d637edd562e362d3">cuptiEventDomainEnumEvents</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> eventDomain, size_t *arraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *eventArray)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the events in a domain.  <a href="#gd23627587d3f21c3d637edd562e362d3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gf8084beb1c692a17616fb99523a044a5">cuptiEventDomainGetAttribute</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> eventDomain, <a class="el" href="group__CUPTI__EVENT__API.html#ga201105be2a6a988b72e70a643c82ad5">CUpti_EventDomainAttribute</a> attrib, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read an event domain attribute.  <a href="#gf8084beb1c692a17616fb99523a044a5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g7341de781de833b65e0748b37acc802f">cuptiEventDomainGetNumEvents</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> eventDomain, uint32_t *numEvents)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get number of events in a domain.  <a href="#g7341de781de833b65e0748b37acc802f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gec3d52db48a14aef9e8b35785bb4d60e">cuptiEventGetAttribute</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> event, <a class="el" href="group__CUPTI__EVENT__API.html#g232a6c540682383abd4cee93ee18e6aa">CUpti_EventAttribute</a> attrib, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get an event attribute.  <a href="#gec3d52db48a14aef9e8b35785bb4d60e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gd27429cf327f99db6b49e88548f801d4">cuptiEventGetIdFromName</a> (CUdevice device, const char *eventName, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *event)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Find an event by name.  <a href="#gd27429cf327f99db6b49e88548f801d4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g08b80dcd21975492c7d756eac59d8b6a">cuptiEventGroupAddEvent</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> event)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Add an event to an event group.  <a href="#g08b80dcd21975492c7d756eac59d8b6a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g9d5cad301581eb32e94ffc7c1b9afc9d">cuptiEventGroupCreate</a> (CUcontext context, <a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> *eventGroup, uint32_t flags)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Create a new event group for a context.  <a href="#g9d5cad301581eb32e94ffc7c1b9afc9d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g99f31aed86f5c117ef07409982f25bd0">cuptiEventGroupDestroy</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Destroy an event group.  <a href="#g99f31aed86f5c117ef07409982f25bd0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g63365c4bb038575bcd6b1a2ba13649d4">cuptiEventGroupDisable</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Disable an event group.  <a href="#g63365c4bb038575bcd6b1a2ba13649d4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g1b72c6cea4de6cc0ebee2c0b41453223">cuptiEventGroupEnable</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable an event group.  <a href="#g1b72c6cea4de6cc0ebee2c0b41453223"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g573c7e8a0295d36cf0051e27554971a8">cuptiEventGroupGetAttribute</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup, <a class="el" href="group__CUPTI__EVENT__API.html#gc8aaac37b2aefeea5177c0b813806b9f">CUpti_EventGroupAttribute</a> attrib, size_t *valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read an event group attribute.  <a href="#g573c7e8a0295d36cf0051e27554971a8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gbb522fec20a70e3b5d8f819693c4f2dd">cuptiEventGroupReadAllEvents</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup, <a class="el" href="group__CUPTI__EVENT__API.html#ge8ea7de2653cc087a9c5399386604528">CUpti_ReadEventFlags</a> flags, size_t *eventValueBufferSizeBytes, uint64_t *eventValueBuffer, size_t *eventIdArraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *eventIdArray, size_t *numEventIdsRead)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read the values for all the events in an event group.  <a href="#gbb522fec20a70e3b5d8f819693c4f2dd"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g31c30e2c86218a3501ae8a21127dec15">cuptiEventGroupReadEvent</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup, <a class="el" href="group__CUPTI__EVENT__API.html#ge8ea7de2653cc087a9c5399386604528">CUpti_ReadEventFlags</a> flags, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> event, size_t *eventValueBufferSizeBytes, uint64_t *eventValueBuffer)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Read the value for an event in an event group.  <a href="#g31c30e2c86218a3501ae8a21127dec15"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g4d7555b6c900a27dee2ee3465701bb96">cuptiEventGroupRemoveAllEvents</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Remove all events from an event group.  <a href="#g4d7555b6c900a27dee2ee3465701bb96"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g0da4bfbdde94a0fcb5bba2f1c69aa1f0">cuptiEventGroupRemoveEvent</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> event)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Remove an event from an event group.  <a href="#g0da4bfbdde94a0fcb5bba2f1c69aa1f0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gbda509844936046694b6081cffa4886d">cuptiEventGroupResetAllEvents</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Zero all the event counts in an event group.  <a href="#gbda509844936046694b6081cffa4886d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gdeea7eb4a79d3c7e85db597f2c98d5ac">cuptiEventGroupSetAttribute</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> eventGroup, <a class="el" href="group__CUPTI__EVENT__API.html#gc8aaac37b2aefeea5177c0b813806b9f">CUpti_EventGroupAttribute</a> attrib, size_t valueSize, void *value)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Write an event group attribute.  <a href="#gdeea7eb4a79d3c7e85db597f2c98d5ac"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g0dc70d03c11800d734801a3d4fdac073">cuptiEventGroupSetDisable</a> (<a class="el" href="structCUpti__EventGroupSet.html">CUpti_EventGroupSet</a> *eventGroupSet)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Disable an event group set.  <a href="#g0dc70d03c11800d734801a3d4fdac073"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gff1d5b7675cc2748eadae7348e30cbfc">cuptiEventGroupSetEnable</a> (<a class="el" href="structCUpti__EventGroupSet.html">CUpti_EventGroupSet</a> *eventGroupSet)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable an event group set.  <a href="#gff1d5b7675cc2748eadae7348e30cbfc"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g0fd307d429d4e37f61f45472de069910">cuptiEventGroupSetsCreate</a> (CUcontext context, size_t eventIdArraySizeBytes, <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *eventIdArray, <a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> **eventGroupPasses)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">For a set of events, get the grouping that indicates the number of passes and the event groups necessary to collect the events.  <a href="#g0fd307d429d4e37f61f45472de069910"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gcf75cc84517241e4c40c21c2f312e2ad">cuptiEventGroupSetsDestroy</a> (<a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> *eventGroupSets)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Destroy a event group sets object.  <a href="#gcf75cc84517241e4c40c21c2f312e2ad"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#g93814f61b681f440b43c8130f72a99f9">cuptiGetNumEventDomains</a> (uint32_t *numDomains)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get the number of event domains available on any device.  <a href="#g93814f61b681f440b43c8130f72a99f9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gfce5f989d4daee01499c96884ca5653e">cuptiKernelReplaySubscribeUpdate</a> (<a class="el" href="group__CUPTI__EVENT__API.html#g4c9cefe906f4af450ae0ad5dc1d39b1c">CUpti_KernelReplayUpdateFunc</a> updateFunc, void *customData)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Subscribe to kernel replay updates.  <a href="#gfce5f989d4daee01499c96884ca5653e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__CUPTI__EVENT__API.html#gdfa8bf3fa3d6c6445f2cb874d0b54cf3">cuptiSetEventCollectionMode</a> (CUcontext context, <a class="el" href="group__CUPTI__EVENT__API.html#g54361f7bed89987eabb91300cc218712">CUpti_EventCollectionMode</a> mode)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set the event collection mode.  <a href="#gdfa8bf3fa3d6c6445f2cb874d0b54cf3"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the CUPTI Event API.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd>CUPTI event API from the header cupti_events.h are not supported on devices with compute capability 7.5 and higher (i.e. Turing and later GPU architectures). These API will be deprecated in a future CUDA release. These are replaced by Profiling API in the header cupti_profiler_target.h and Perfworks metrics API in the headers nvperf_host.h and nvperf_target.h which are supported on devices with compute capability 7.0 and higher (i.e. Volta and later GPU architectures). </dd></dl>
<hr><h2>Define Documentation</h2>
<a class="anchor" name="g4f280929637a726da2ba4c907e5b9ba1"></a><!-- doxytag: member="cupti_events.h::CUPTI_EVENT_OVERFLOW" ref="g4f280929637a726da2ba4c907e5b9ba1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUPTI_EVENT_OVERFLOW&nbsp;&nbsp;&nbsp;((uint64_t)0xFFFFFFFFFFFFFFFFULL)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The CUPTI event value that indicates an overflow. 
</div>
</div><p>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g8a898bd33ce64d353b79d7d39e74d198"></a><!-- doxytag: member="cupti_events.h::CUpti_EventDomainID" ref="g8a898bd33ce64d353b79d7d39e74d198" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint32_t <a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ID for an event domain. An event domain represents a group of related events. A device may have multiple instances of a domain, indicating that the device can simultaneously record multiple instances of each event within that domain. 
</div>
</div><p>
<a class="anchor" name="g649750f363752bccbf9e98582d5f6925"></a><!-- doxytag: member="cupti_events.h::CUpti_EventGroup" ref="g649750f363752bccbf9e98582d5f6925" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void* <a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An event group is a collection of events that are managed together. All events in an event group must belong to the same domain. 
</div>
</div><p>
<a class="anchor" name="g6ce7370be9ed31ce6f2d475de39045df"></a><!-- doxytag: member="cupti_events.h::CUpti_EventID" ref="g6ce7370be9ed31ce6f2d475de39045df" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint32_t <a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An event represents a countable activity, action, or occurrence on the device. 
</div>
</div><p>
<a class="anchor" name="g4c9cefe906f4af450ae0ad5dc1d39b1c"></a><!-- doxytag: member="cupti_events.h::CUpti_KernelReplayUpdateFunc" ref="g4c9cefe906f4af450ae0ad5dc1d39b1c" args=")(const char *kernelName, int numReplaysDone, void *customData)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void( * <a class="el" href="group__CUPTI__EVENT__API.html#g4c9cefe906f4af450ae0ad5dc1d39b1c">CUpti_KernelReplayUpdateFunc</a>)(const char *kernelName, int numReplaysDone, void *customData)          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>kernelName</em>&nbsp;</td><td>The mangled kernel name </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>numReplaysDone</em>&nbsp;</td><td>Number of replays done so far </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>customData</em>&nbsp;</td><td>Pointer of any custom data passed in when subscribing </td></tr>
  </table>
</dl>

</div>
</div><p>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g07af1fece58c6f17c48067740034e31b"></a><!-- doxytag: member="cupti_events.h::CUpti_DeviceAttribute" ref="g07af1fece58c6f17c48067740034e31b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#g07af1fece58c6f17c48067740034e31b">CUpti_DeviceAttribute</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI device attributes. These attributes can be read using <a class="el" href="group__CUPTI__EVENT__API.html#g631a8f494a7f0f3d254ad49f2040eff5">cuptiDeviceGetAttribute</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b6006619ba70b90a1a787673cf64a8e5c"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_MAX_EVENT_ID" ref="gg07af1fece58c6f17c48067740034e31b6006619ba70b90a1a787673cf64a8e5c" args="" -->CUPTI_DEVICE_ATTR_MAX_EVENT_ID</em>&nbsp;</td><td>
Number of event IDs for a device. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b514c67f9d512fa9970b456ed4062ebb8"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_MAX_EVENT_DOMAIN_ID" ref="gg07af1fece58c6f17c48067740034e31b514c67f9d512fa9970b456ed4062ebb8" args="" -->CUPTI_DEVICE_ATTR_MAX_EVENT_DOMAIN_ID</em>&nbsp;</td><td>
Number of event domain IDs for a device. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b7e95692e0edfb518b1170b3707b630ad"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_GLOBAL_MEMORY_BANDWIDTH" ref="gg07af1fece58c6f17c48067740034e31b7e95692e0edfb518b1170b3707b630ad" args="" -->CUPTI_DEVICE_ATTR_GLOBAL_MEMORY_BANDWIDTH</em>&nbsp;</td><td>
Get global memory bandwidth in Kbytes/sec. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b5a96aad93301bb10e92cacc6856432fc"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_INSTRUCTION_PER_CYCLE" ref="gg07af1fece58c6f17c48067740034e31b5a96aad93301bb10e92cacc6856432fc" args="" -->CUPTI_DEVICE_ATTR_INSTRUCTION_PER_CYCLE</em>&nbsp;</td><td>
Get theoretical maximum number of instructions per cycle. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b0891b8b997de3c94ab70ddd240efb594"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_INSTRUCTION_THROUGHPUT_SINGLE_PRECISION" ref="gg07af1fece58c6f17c48067740034e31b0891b8b997de3c94ab70ddd240efb594" args="" -->CUPTI_DEVICE_ATTR_INSTRUCTION_THROUGHPUT_SINGLE_PRECISION</em>&nbsp;</td><td>
Get theoretical maximum number of single precision instructions that can be executed per second. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31be6e195d7e69d82b47a6fb811bbb1b44f"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_MAX_FRAME_BUFFERS" ref="gg07af1fece58c6f17c48067740034e31be6e195d7e69d82b47a6fb811bbb1b44f" args="" -->CUPTI_DEVICE_ATTR_MAX_FRAME_BUFFERS</em>&nbsp;</td><td>
Get number of frame buffers for device. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31ba7e128bc358880df56ec911bec400097"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_PCIE_LINK_RATE" ref="gg07af1fece58c6f17c48067740034e31ba7e128bc358880df56ec911bec400097" args="" -->CUPTI_DEVICE_ATTR_PCIE_LINK_RATE</em>&nbsp;</td><td>
Get PCIE link rate in Mega bits/sec for device. Return 0 if bus-type is non-PCIE. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31bc71dfb398189e6084aa5ddaa1f33fe3b"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_PCIE_LINK_WIDTH" ref="gg07af1fece58c6f17c48067740034e31bc71dfb398189e6084aa5ddaa1f33fe3b" args="" -->CUPTI_DEVICE_ATTR_PCIE_LINK_WIDTH</em>&nbsp;</td><td>
Get PCIE link width for device. Return 0 if bus-type is non-PCIE. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31bf9794e9b1a5a41b586ffb249dfd7db7c"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_PCIE_GEN" ref="gg07af1fece58c6f17c48067740034e31bf9794e9b1a5a41b586ffb249dfd7db7c" args="" -->CUPTI_DEVICE_ATTR_PCIE_GEN</em>&nbsp;</td><td>
Get PCIE generation for device. Return 0 if bus-type is non-PCIE. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31bcc1c7544343bfe738fdc499a4e4293c9"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_DEVICE_CLASS" ref="gg07af1fece58c6f17c48067740034e31bcc1c7544343bfe738fdc499a4e4293c9" args="" -->CUPTI_DEVICE_ATTR_DEVICE_CLASS</em>&nbsp;</td><td>
Get the class for the device. Value is a CUpti_DeviceAttributeDeviceClass. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b7256e4ede078800e29511aa7136884c7"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_FLOP_SP_PER_CYCLE" ref="gg07af1fece58c6f17c48067740034e31b7256e4ede078800e29511aa7136884c7" args="" -->CUPTI_DEVICE_ATTR_FLOP_SP_PER_CYCLE</em>&nbsp;</td><td>
Get the peak single precision flop per cycle. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b1ab37ff50a14b66a446d7260aefa2df5"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_FLOP_DP_PER_CYCLE" ref="gg07af1fece58c6f17c48067740034e31b1ab37ff50a14b66a446d7260aefa2df5" args="" -->CUPTI_DEVICE_ATTR_FLOP_DP_PER_CYCLE</em>&nbsp;</td><td>
Get the peak double precision flop per cycle. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31be4280195644be12af2abd413e9a41c31"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_MAX_L2_UNITS" ref="gg07af1fece58c6f17c48067740034e31be4280195644be12af2abd413e9a41c31" args="" -->CUPTI_DEVICE_ATTR_MAX_L2_UNITS</em>&nbsp;</td><td>
Get number of L2 units. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b7fac6cc5865ce3126a4d102a276231b2"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_SHARED" ref="gg07af1fece58c6f17c48067740034e31b7fac6cc5865ce3126a4d102a276231b2" args="" -->CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_SHARED</em>&nbsp;</td><td>
Get the maximum shared memory for the CU_FUNC_CACHE_PREFER_SHARED preference. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b208d80386d7f5c7298419543921183da"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_L1" ref="gg07af1fece58c6f17c48067740034e31b208d80386d7f5c7298419543921183da" args="" -->CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_L1</em>&nbsp;</td><td>
Get the maximum shared memory for the CU_FUNC_CACHE_PREFER_L1 preference. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31bd78875a015662732a0af8fdd932b990c"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_EQUAL" ref="gg07af1fece58c6f17c48067740034e31bd78875a015662732a0af8fdd932b990c" args="" -->CUPTI_DEVICE_ATTR_MAX_SHARED_MEMORY_CACHE_CONFIG_PREFER_EQUAL</em>&nbsp;</td><td>
Get the maximum shared memory for the CU_FUNC_CACHE_PREFER_EQUAL preference. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b9d19daa5ea3d8cc57c6f5653dcff9a9c"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_FLOP_HP_PER_CYCLE" ref="gg07af1fece58c6f17c48067740034e31b9d19daa5ea3d8cc57c6f5653dcff9a9c" args="" -->CUPTI_DEVICE_ATTR_FLOP_HP_PER_CYCLE</em>&nbsp;</td><td>
Get the peak half precision flop per cycle. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31b5d5f0617cb3cddae7a31b6b5cae69dad"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_NVLINK_PRESENT" ref="gg07af1fece58c6f17c48067740034e31b5d5f0617cb3cddae7a31b6b5cae69dad" args="" -->CUPTI_DEVICE_ATTR_NVLINK_PRESENT</em>&nbsp;</td><td>
Check if Nvlink is connected to device. Returns 1, if at least one Nvlink is connected to the device, returns 0 otherwise. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31bafee592a6302a3df0e6626520f149264"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_GPU_CPU_NVLINK_BW" ref="gg07af1fece58c6f17c48067740034e31bafee592a6302a3df0e6626520f149264" args="" -->CUPTI_DEVICE_ATTR_GPU_CPU_NVLINK_BW</em>&nbsp;</td><td>
Check if Nvlink is present between GPU and CPU. Returns Bandwidth, in Bytes/sec, if Nvlink is present, returns 0 otherwise. Value is a uint64_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg07af1fece58c6f17c48067740034e31ba3c3c3077abdb5c809c1ed1390ca5373"></a><!-- doxytag: member="CUPTI_DEVICE_ATTR_NVSWITCH_PRESENT" ref="gg07af1fece58c6f17c48067740034e31ba3c3c3077abdb5c809c1ed1390ca5373" args="" -->CUPTI_DEVICE_ATTR_NVSWITCH_PRESENT</em>&nbsp;</td><td>
Check if NVSwitch is present in the underlying topology. Returns 1, if present, returns 0 otherwise. Value is a uint32_t. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g77fc6151563b2f16b4eeea3d9bdbf3f3"></a><!-- doxytag: member="cupti_events.h::CUpti_DeviceAttributeDeviceClass" ref="g77fc6151563b2f16b4eeea3d9bdbf3f3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#g77fc6151563b2f16b4eeea3d9bdbf3f3">CUpti_DeviceAttributeDeviceClass</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enumeration of device classes for device attribute CUPTI_DEVICE_ATTR_DEVICE_CLASS. 
</div>
</div><p>
<a class="anchor" name="g232a6c540682383abd4cee93ee18e6aa"></a><!-- doxytag: member="cupti_events.h::CUpti_EventAttribute" ref="g232a6c540682383abd4cee93ee18e6aa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#g232a6c540682383abd4cee93ee18e6aa">CUpti_EventAttribute</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Event attributes. These attributes can be read using <a class="el" href="group__CUPTI__EVENT__API.html#gec3d52db48a14aef9e8b35785bb4d60e">cuptiEventGetAttribute</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg232a6c540682383abd4cee93ee18e6aa4a6394ddd536a73cb3446831a6efc9c4"></a><!-- doxytag: member="CUPTI_EVENT_ATTR_NAME" ref="gg232a6c540682383abd4cee93ee18e6aa4a6394ddd536a73cb3446831a6efc9c4" args="" -->CUPTI_EVENT_ATTR_NAME</em>&nbsp;</td><td>
Event name. Value is a null terminated const c-string. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg232a6c540682383abd4cee93ee18e6aae336de744661553a7bde8e8f704967b2"></a><!-- doxytag: member="CUPTI_EVENT_ATTR_SHORT_DESCRIPTION" ref="gg232a6c540682383abd4cee93ee18e6aae336de744661553a7bde8e8f704967b2" args="" -->CUPTI_EVENT_ATTR_SHORT_DESCRIPTION</em>&nbsp;</td><td>
Short description of event. Value is a null terminated const c-string. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg232a6c540682383abd4cee93ee18e6aaac191fb2f48e00651ac86c18d1abbd9c"></a><!-- doxytag: member="CUPTI_EVENT_ATTR_LONG_DESCRIPTION" ref="gg232a6c540682383abd4cee93ee18e6aaac191fb2f48e00651ac86c18d1abbd9c" args="" -->CUPTI_EVENT_ATTR_LONG_DESCRIPTION</em>&nbsp;</td><td>
Long description of event. Value is a null terminated const c-string. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg232a6c540682383abd4cee93ee18e6aac8173e23f5d3806e7ccd397e3d6638d9"></a><!-- doxytag: member="CUPTI_EVENT_ATTR_CATEGORY" ref="gg232a6c540682383abd4cee93ee18e6aac8173e23f5d3806e7ccd397e3d6638d9" args="" -->CUPTI_EVENT_ATTR_CATEGORY</em>&nbsp;</td><td>
Category of event. Value is CUpti_EventCategory. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg232a6c540682383abd4cee93ee18e6aa2803d24d461af7124ccb017c736a31c8"></a><!-- doxytag: member="CUPTI_EVENT_ATTR_PROFILING_SCOPE" ref="gg232a6c540682383abd4cee93ee18e6aa2803d24d461af7124ccb017c736a31c8" args="" -->CUPTI_EVENT_ATTR_PROFILING_SCOPE</em>&nbsp;</td><td>
Profiling scope of the events. It can be either device or context or both. Value is a <a class="el" href="group__CUPTI__EVENT__API.html#g5fbde5d55673e4d937491e6ea9631940">CUpti_EventProfilingScope</a>. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd9604f1d97d7c84664aa37f325e35f39"></a><!-- doxytag: member="cupti_events.h::CUpti_EventCategory" ref="gd9604f1d97d7c84664aa37f325e35f39" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#gd9604f1d97d7c84664aa37f325e35f39">CUpti_EventCategory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Each event is assigned to a category that represents the general type of the event. A event's category is accessed using <a class="el" href="group__CUPTI__EVENT__API.html#gec3d52db48a14aef9e8b35785bb4d60e">cuptiEventGetAttribute</a> and the CUPTI_EVENT_ATTR_CATEGORY attribute. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggd9604f1d97d7c84664aa37f325e35f39b3f3e075259468f241de0e6cd7ccc2f9"></a><!-- doxytag: member="CUPTI_EVENT_CATEGORY_INSTRUCTION" ref="ggd9604f1d97d7c84664aa37f325e35f39b3f3e075259468f241de0e6cd7ccc2f9" args="" -->CUPTI_EVENT_CATEGORY_INSTRUCTION</em>&nbsp;</td><td>
An instruction related event. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd9604f1d97d7c84664aa37f325e35f39d5390275d63008f422b994ed2cc7ecc5"></a><!-- doxytag: member="CUPTI_EVENT_CATEGORY_MEMORY" ref="ggd9604f1d97d7c84664aa37f325e35f39d5390275d63008f422b994ed2cc7ecc5" args="" -->CUPTI_EVENT_CATEGORY_MEMORY</em>&nbsp;</td><td>
A memory related event. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd9604f1d97d7c84664aa37f325e35f39c820faabb8a0dd73281cae7cbb1201c2"></a><!-- doxytag: member="CUPTI_EVENT_CATEGORY_CACHE" ref="ggd9604f1d97d7c84664aa37f325e35f39c820faabb8a0dd73281cae7cbb1201c2" args="" -->CUPTI_EVENT_CATEGORY_CACHE</em>&nbsp;</td><td>
A cache related event. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd9604f1d97d7c84664aa37f325e35f39040f655492ae007620479cd9a9853cac"></a><!-- doxytag: member="CUPTI_EVENT_CATEGORY_PROFILE_TRIGGER" ref="ggd9604f1d97d7c84664aa37f325e35f39040f655492ae007620479cd9a9853cac" args="" -->CUPTI_EVENT_CATEGORY_PROFILE_TRIGGER</em>&nbsp;</td><td>
A profile-trigger event. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggd9604f1d97d7c84664aa37f325e35f39d395ffa93358255e47324b0290901fad"></a><!-- doxytag: member="CUPTI_EVENT_CATEGORY_SYSTEM" ref="ggd9604f1d97d7c84664aa37f325e35f39d395ffa93358255e47324b0290901fad" args="" -->CUPTI_EVENT_CATEGORY_SYSTEM</em>&nbsp;</td><td>
A system event. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gdafc575cd4614872eba5d99e2f2bc831"></a><!-- doxytag: member="cupti_events.h::CUpti_EventCollectionMethod" ref="gdafc575cd4614872eba5d99e2f2bc831" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#gdafc575cd4614872eba5d99e2f2bc831">CUpti_EventCollectionMethod</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The collection method indicates how an event is collected. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggdafc575cd4614872eba5d99e2f2bc8312c8858ae323136db8b0fa694dfeace26"></a><!-- doxytag: member="CUPTI_EVENT_COLLECTION_METHOD_PM" ref="ggdafc575cd4614872eba5d99e2f2bc8312c8858ae323136db8b0fa694dfeace26" args="" -->CUPTI_EVENT_COLLECTION_METHOD_PM</em>&nbsp;</td><td>
Event is collected using a hardware global performance monitor. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdafc575cd4614872eba5d99e2f2bc83118c829ff673304c6ead4854c17addf04"></a><!-- doxytag: member="CUPTI_EVENT_COLLECTION_METHOD_SM" ref="ggdafc575cd4614872eba5d99e2f2bc83118c829ff673304c6ead4854c17addf04" args="" -->CUPTI_EVENT_COLLECTION_METHOD_SM</em>&nbsp;</td><td>
Event is collected using a hardware SM performance monitor. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdafc575cd4614872eba5d99e2f2bc831d24aad7cd6c912d0d6419642f984f44b"></a><!-- doxytag: member="CUPTI_EVENT_COLLECTION_METHOD_INSTRUMENTED" ref="ggdafc575cd4614872eba5d99e2f2bc831d24aad7cd6c912d0d6419642f984f44b" args="" -->CUPTI_EVENT_COLLECTION_METHOD_INSTRUMENTED</em>&nbsp;</td><td>
Event is collected using software instrumentation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdafc575cd4614872eba5d99e2f2bc831f7eed1727d5b44144bc12edcb13640e7"></a><!-- doxytag: member="CUPTI_EVENT_COLLECTION_METHOD_NVLINK_TC" ref="ggdafc575cd4614872eba5d99e2f2bc831f7eed1727d5b44144bc12edcb13640e7" args="" -->CUPTI_EVENT_COLLECTION_METHOD_NVLINK_TC</em>&nbsp;</td><td>
Event is collected using NvLink throughput counter method. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g54361f7bed89987eabb91300cc218712"></a><!-- doxytag: member="cupti_events.h::CUpti_EventCollectionMode" ref="g54361f7bed89987eabb91300cc218712" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#g54361f7bed89987eabb91300cc218712">CUpti_EventCollectionMode</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The event collection mode determines the period over which the events within the enabled event groups will be collected. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg54361f7bed89987eabb91300cc21871200547df82922544723de3f7ed4f8aea8"></a><!-- doxytag: member="CUPTI_EVENT_COLLECTION_MODE_CONTINUOUS" ref="gg54361f7bed89987eabb91300cc21871200547df82922544723de3f7ed4f8aea8" args="" -->CUPTI_EVENT_COLLECTION_MODE_CONTINUOUS</em>&nbsp;</td><td>
Events are collected for the entire duration between the cuptiEventGroupEnable and cuptiEventGroupDisable calls. Event values are reset when the events are read. For CUDA toolkit v6.0 and older this was the default mode. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg54361f7bed89987eabb91300cc218712ea24eb0dbf64d034bedac90f326e0e1d"></a><!-- doxytag: member="CUPTI_EVENT_COLLECTION_MODE_KERNEL" ref="gg54361f7bed89987eabb91300cc218712ea24eb0dbf64d034bedac90f326e0e1d" args="" -->CUPTI_EVENT_COLLECTION_MODE_KERNEL</em>&nbsp;</td><td>
Events are collected only for the durations of kernel executions that occur between the cuptiEventGroupEnable and cuptiEventGroupDisable calls. Event collection begins when a kernel execution begins, and stops when kernel execution completes. Event values are reset to zero when each kernel execution begins. If multiple kernel executions occur between the cuptiEventGroupEnable and cuptiEventGroupDisable calls then the event values must be read after each kernel launch if those events need to be associated with the specific kernel launch. Note that collection in this mode may significantly change the overall performance characteristics of the application because kernel executions that occur between the cuptiEventGroupEnable and cuptiEventGroupDisable calls are serialized on the GPU. This is the default mode from CUDA toolkit v6.5 </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="ga201105be2a6a988b72e70a643c82ad5"></a><!-- doxytag: member="cupti_events.h::CUpti_EventDomainAttribute" ref="ga201105be2a6a988b72e70a643c82ad5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#ga201105be2a6a988b72e70a643c82ad5">CUpti_EventDomainAttribute</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Event domain attributes. Except where noted, all the attributes can be read using either <a class="el" href="group__CUPTI__EVENT__API.html#gbb745099ffef139c4ee98a1de82fcac2">cuptiDeviceGetEventDomainAttribute</a> or <a class="el" href="group__CUPTI__EVENT__API.html#gf8084beb1c692a17616fb99523a044a5">cuptiEventDomainGetAttribute</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gga201105be2a6a988b72e70a643c82ad5d4814f4b5e298b0aff246585fdf6dc16"></a><!-- doxytag: member="CUPTI_EVENT_DOMAIN_ATTR_NAME" ref="gga201105be2a6a988b72e70a643c82ad5d4814f4b5e298b0aff246585fdf6dc16" args="" -->CUPTI_EVENT_DOMAIN_ATTR_NAME</em>&nbsp;</td><td>
Event domain name. Value is a null terminated const c-string. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga201105be2a6a988b72e70a643c82ad5af06e2bff439ac6ef15b348873e21f2d"></a><!-- doxytag: member="CUPTI_EVENT_DOMAIN_ATTR_INSTANCE_COUNT" ref="gga201105be2a6a988b72e70a643c82ad5af06e2bff439ac6ef15b348873e21f2d" args="" -->CUPTI_EVENT_DOMAIN_ATTR_INSTANCE_COUNT</em>&nbsp;</td><td>
Number of instances of the domain for which event counts will be collected. The domain may have additional instances that cannot be profiled (see CUPTI_EVENT_DOMAIN_ATTR_TOTAL_INSTANCE_COUNT). Can be read only with <a class="el" href="group__CUPTI__EVENT__API.html#gbb745099ffef139c4ee98a1de82fcac2">cuptiDeviceGetEventDomainAttribute</a>. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga201105be2a6a988b72e70a643c82ad513400cf57ccf213e2f62acaad7bb4c3f"></a><!-- doxytag: member="CUPTI_EVENT_DOMAIN_ATTR_TOTAL_INSTANCE_COUNT" ref="gga201105be2a6a988b72e70a643c82ad513400cf57ccf213e2f62acaad7bb4c3f" args="" -->CUPTI_EVENT_DOMAIN_ATTR_TOTAL_INSTANCE_COUNT</em>&nbsp;</td><td>
Total number of instances of the domain, including instances that cannot be profiled. Use CUPTI_EVENT_DOMAIN_ATTR_INSTANCE_COUNT to get the number of instances that can be profiled. Can be read only with <a class="el" href="group__CUPTI__EVENT__API.html#gbb745099ffef139c4ee98a1de82fcac2">cuptiDeviceGetEventDomainAttribute</a>. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gga201105be2a6a988b72e70a643c82ad5ba774bdb36c899b9359d437318813d91"></a><!-- doxytag: member="CUPTI_EVENT_DOMAIN_ATTR_COLLECTION_METHOD" ref="gga201105be2a6a988b72e70a643c82ad5ba774bdb36c899b9359d437318813d91" args="" -->CUPTI_EVENT_DOMAIN_ATTR_COLLECTION_METHOD</em>&nbsp;</td><td>
Collection method used for events contained in the event domain. Value is a <a class="el" href="group__CUPTI__EVENT__API.html#gdafc575cd4614872eba5d99e2f2bc831">CUpti_EventCollectionMethod</a>. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="gc8aaac37b2aefeea5177c0b813806b9f"></a><!-- doxytag: member="cupti_events.h::CUpti_EventGroupAttribute" ref="gc8aaac37b2aefeea5177c0b813806b9f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#gc8aaac37b2aefeea5177c0b813806b9f">CUpti_EventGroupAttribute</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Event group attributes. These attributes can be read using <a class="el" href="group__CUPTI__EVENT__API.html#g573c7e8a0295d36cf0051e27554971a8">cuptiEventGroupGetAttribute</a>. Attributes marked [rw] can also be written using <a class="el" href="group__CUPTI__EVENT__API.html#gdeea7eb4a79d3c7e85db597f2c98d5ac">cuptiEventGroupSetAttribute</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggc8aaac37b2aefeea5177c0b813806b9fddae3327832db311c655f258e94b47b1"></a><!-- doxytag: member="CUPTI_EVENT_GROUP_ATTR_EVENT_DOMAIN_ID" ref="ggc8aaac37b2aefeea5177c0b813806b9fddae3327832db311c655f258e94b47b1" args="" -->CUPTI_EVENT_GROUP_ATTR_EVENT_DOMAIN_ID</em>&nbsp;</td><td>
The domain to which the event group is bound. This attribute is set when the first event is added to the group. Value is a CUpti_EventDomainID. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc8aaac37b2aefeea5177c0b813806b9f776be3128e2ab834a96ec9ff16dda611"></a><!-- doxytag: member="CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES" ref="ggc8aaac37b2aefeea5177c0b813806b9f776be3128e2ab834a96ec9ff16dda611" args="" -->CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES</em>&nbsp;</td><td>
[rw] Profile all the instances of the domain for this eventgroup. This feature can be used to get load balancing across all instances of a domain. Value is an integer. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc8aaac37b2aefeea5177c0b813806b9ffc374bac14388fc27bfc6bfaeefd3970"></a><!-- doxytag: member="CUPTI_EVENT_GROUP_ATTR_USER_DATA" ref="ggc8aaac37b2aefeea5177c0b813806b9ffc374bac14388fc27bfc6bfaeefd3970" args="" -->CUPTI_EVENT_GROUP_ATTR_USER_DATA</em>&nbsp;</td><td>
[rw] Reserved for user data. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc8aaac37b2aefeea5177c0b813806b9f86ac9ea1f6da12140f30c3c6f42522d1"></a><!-- doxytag: member="CUPTI_EVENT_GROUP_ATTR_NUM_EVENTS" ref="ggc8aaac37b2aefeea5177c0b813806b9f86ac9ea1f6da12140f30c3c6f42522d1" args="" -->CUPTI_EVENT_GROUP_ATTR_NUM_EVENTS</em>&nbsp;</td><td>
Number of events in the group. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc8aaac37b2aefeea5177c0b813806b9f5163e8a0d301e48732bfbd71065e725a"></a><!-- doxytag: member="CUPTI_EVENT_GROUP_ATTR_EVENTS" ref="ggc8aaac37b2aefeea5177c0b813806b9f5163e8a0d301e48732bfbd71065e725a" args="" -->CUPTI_EVENT_GROUP_ATTR_EVENTS</em>&nbsp;</td><td>
Enumerates events in the group. Value is a pointer to buffer of size sizeof(CUpti_EventID) * num_of_events in the eventgroup. num_of_events can be queried using CUPTI_EVENT_GROUP_ATTR_NUM_EVENTS. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc8aaac37b2aefeea5177c0b813806b9f46001f8b8e2928977ae48b2637cdc939"></a><!-- doxytag: member="CUPTI_EVENT_GROUP_ATTR_INSTANCE_COUNT" ref="ggc8aaac37b2aefeea5177c0b813806b9f46001f8b8e2928977ae48b2637cdc939" args="" -->CUPTI_EVENT_GROUP_ATTR_INSTANCE_COUNT</em>&nbsp;</td><td>
Number of instances of the domain bound to this event group that will be counted. Value is a uint32_t. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggc8aaac37b2aefeea5177c0b813806b9f2e15e285c661b8767ffa5e3d42fb26a8"></a><!-- doxytag: member="CUPTI_EVENT_GROUP_ATTR_PROFILING_SCOPE" ref="ggc8aaac37b2aefeea5177c0b813806b9f2e15e285c661b8767ffa5e3d42fb26a8" args="" -->CUPTI_EVENT_GROUP_ATTR_PROFILING_SCOPE</em>&nbsp;</td><td>
Event group scope can be set to CUPTI_EVENT_PROFILING_SCOPE_DEVICE or CUPTI_EVENT_PROFILING_SCOPE_CONTEXT for an eventGroup, before adding any event. Sets the scope of eventgroup as CUPTI_EVENT_PROFILING_SCOPE_DEVICE or CUPTI_EVENT_PROFILING_SCOPE_CONTEXT when the scope of the events that will be added is CUPTI_EVENT_PROFILING_SCOPE_BOTH. If profiling scope of event is either CUPTI_EVENT_PROFILING_SCOPE_DEVICE or CUPTI_EVENT_PROFILING_SCOPE_CONTEXT then setting this attribute will not affect the default scope. It is not allowed to add events of different scope to same eventgroup. Value is a uint32_t. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="g5fbde5d55673e4d937491e6ea9631940"></a><!-- doxytag: member="cupti_events.h::CUpti_EventProfilingScope" ref="g5fbde5d55673e4d937491e6ea9631940" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#g5fbde5d55673e4d937491e6ea9631940">CUpti_EventProfilingScope</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Profiling scope of event indicates if the event can be collected at context scope or device scope or both i.e. it can be collected at any of context or device scope. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg5fbde5d55673e4d937491e6ea963194002acd9ac5f62339379285daa9aa1a172"></a><!-- doxytag: member="CUPTI_EVENT_PROFILING_SCOPE_CONTEXT" ref="gg5fbde5d55673e4d937491e6ea963194002acd9ac5f62339379285daa9aa1a172" args="" -->CUPTI_EVENT_PROFILING_SCOPE_CONTEXT</em>&nbsp;</td><td>
Event is collected at context scope. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg5fbde5d55673e4d937491e6ea96319404e57028e6fe2e3a3d6bd8d1076089901"></a><!-- doxytag: member="CUPTI_EVENT_PROFILING_SCOPE_DEVICE" ref="gg5fbde5d55673e4d937491e6ea96319404e57028e6fe2e3a3d6bd8d1076089901" args="" -->CUPTI_EVENT_PROFILING_SCOPE_DEVICE</em>&nbsp;</td><td>
Event is collected at device scope. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg5fbde5d55673e4d937491e6ea96319409fbac86ec79d99e00f16c79e7f46ccb2"></a><!-- doxytag: member="CUPTI_EVENT_PROFILING_SCOPE_BOTH" ref="gg5fbde5d55673e4d937491e6ea96319409fbac86ec79d99e00f16c79e7f46ccb2" args="" -->CUPTI_EVENT_PROFILING_SCOPE_BOTH</em>&nbsp;</td><td>
Event can be collected at device or context scope. The scope can be set using <a class="el" href="group__CUPTI__EVENT__API.html#gdeea7eb4a79d3c7e85db597f2c98d5ac">cuptiEventGroupSetAttribute</a> API. </td></tr>
</table>
</dl>

</div>
</div><p>
<a class="anchor" name="ge8ea7de2653cc087a9c5399386604528"></a><!-- doxytag: member="cupti_events.h::CUpti_ReadEventFlags" ref="ge8ea7de2653cc087a9c5399386604528" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__CUPTI__EVENT__API.html#ge8ea7de2653cc087a9c5399386604528">CUpti_ReadEventFlags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flags for <a class="el" href="group__CUPTI__EVENT__API.html#g31c30e2c86218a3501ae8a21127dec15">cuptiEventGroupReadEvent</a> an <a class="el" href="group__CUPTI__EVENT__API.html#gbb522fec20a70e3b5d8f819693c4f2dd">cuptiEventGroupReadAllEvents</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gge8ea7de2653cc087a9c5399386604528726bee26555027440a8b4acdf6ff13a3"></a><!-- doxytag: member="CUPTI_EVENT_READ_FLAG_NONE" ref="gge8ea7de2653cc087a9c5399386604528726bee26555027440a8b4acdf6ff13a3" args="" -->CUPTI_EVENT_READ_FLAG_NONE</em>&nbsp;</td><td>
No flags. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g430869bc1bfcc11589241bac94774c06"></a><!-- doxytag: member="cupti_events.h::cuptiDeviceEnumEventDomains" ref="g430869bc1bfcc11589241bac94774c06" args="(CUdevice device, size_t *arraySizeBytes, CUpti_EventDomainID *domainArray)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceEnumEventDomains           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>arraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> *&nbsp;</td>
          <td class="paramname"> <em>domainArray</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the event domains IDs in <code>domainArray</code> for a device. The size of the <code>domainArray</code> buffer is given by <code>*arraySizeBytes</code>. The size of the <code>domainArray</code> buffer must be at least <code>numdomains</code> * sizeof(CUpti_EventDomainID) or else all domains will not be returned. The value returned in <code>*arraySizeBytes</code> contains the number of bytes returned in <code>domainArray</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>arraySizeBytes</em>&nbsp;</td><td>The size of <code>domainArray</code> in bytes, and returns the number of bytes written to <code>domainArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domainArray</em>&nbsp;</td><td>Returns the IDs of the event domains for the device</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>arraySizeBytes</code> or <code>domainArray</code> are NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g631a8f494a7f0f3d254ad49f2040eff5"></a><!-- doxytag: member="cupti_events.h::cuptiDeviceGetAttribute" ref="g631a8f494a7f0f3d254ad49f2040eff5" args="(CUdevice device, CUpti_DeviceAttribute attrib, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceGetAttribute           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g07af1fece58c6f17c48067740034e31b">CUpti_DeviceAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Read a device attribute and return it in <code>*value</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>attrib</em>&nbsp;</td><td>The attribute to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>Size of buffer pointed by the value, and returns the number of bytes written to <code>value</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>Returns the value of the attribute</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attrib</code> is not a device attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>For non-c-string attribute values, indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gbb745099ffef139c4ee98a1de82fcac2"></a><!-- doxytag: member="cupti_events.h::cuptiDeviceGetEventDomainAttribute" ref="gbb745099ffef139c4ee98a1de82fcac2" args="(CUdevice device, CUpti_EventDomainID eventDomain, CUpti_EventDomainAttribute attrib, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceGetEventDomainAttribute           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a>&nbsp;</td>
          <td class="paramname"> <em>eventDomain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#ga201105be2a6a988b72e70a643c82ad5">CUpti_EventDomainAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns an event domain attribute in <code>*value</code>. The size of the <code>value</code> buffer is given by <code>*valueSize</code>. The value returned in <code>*valueSize</code> contains the number of bytes returned in <code>value</code>.<p>
If the attribute value is a c-string that is longer than <code>*valueSize</code>, then only the first <code>*valueSize</code> characters will be returned and there will be no terminating null byte. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventDomain</em>&nbsp;</td><td>ID of the event domain </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>attrib</em>&nbsp;</td><td>The event domain attribute to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>The size of the <code>value</code> buffer in bytes, and returns the number of bytes written to <code>value</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>Returns the attribute's value</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_DOMAIN_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attrib</code> is not an event domain attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>For non-c-string attribute values, indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gfebb6c61c0c1f8427e187223d799cda5"></a><!-- doxytag: member="cupti_events.h::cuptiDeviceGetNumEventDomains" ref="gfebb6c61c0c1f8427e187223d799cda5" args="(CUdevice device, uint32_t *numDomains)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceGetNumEventDomains           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numDomains</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of domains in <code>numDomains</code> for a device. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>numDomains</em>&nbsp;</td><td>Returns the number of domains</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>numDomains</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g581c6a513e81f9431854561f43c06f36"></a><!-- doxytag: member="cupti_events.h::cuptiDeviceGetTimestamp" ref="g581c6a513e81f9431854561f43c06f36" args="(CUcontext context, uint64_t *timestamp)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDeviceGetTimestamp           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>timestamp</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the device timestamp in <code>*timestamp</code>. The timestamp is reported in nanoseconds and indicates the time since the device was last reset. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>A context on the device from which to get the timestamp </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>timestamp</em>&nbsp;</td><td>Returns the device timestamp</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>is <code>timestamp</code> is NULL</td></tr>
  </table>
</dl>
**DEPRECATED** This API is deprecated as of CUDA 11.3 
</div>
</div><p>
<a class="anchor" name="g1746f087372090b5289e2512864b8e25"></a><!-- doxytag: member="cupti_events.h::cuptiDisableKernelReplayMode" ref="g1746f087372090b5289e2512864b8e25" args="(CUcontext context)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiDisableKernelReplayMode           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set profiling mode for the context to non-replay (default) mode. Event collection mode will be set to CUPTI_EVENT_COLLECTION_MODE_KERNEL. All previously enabled event groups and event group sets will be disabled. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g2a99f01445e5ba2f7e4ba89796e9b20f"></a><!-- doxytag: member="cupti_events.h::cuptiEnableKernelReplayMode" ref="g2a99f01445e5ba2f7e4ba89796e9b20f" args="(CUcontext context)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEnableKernelReplayMode           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set profiling mode for the context to replay mode. In this mode, any number of events can be collected in one run of the kernel. The event collection mode will automatically switch to CUPTI_EVENT_COLLECTION_MODE_KERNEL. In this mode, <a class="el" href="group__CUPTI__EVENT__API.html#gdfa8bf3fa3d6c6445f2cb874d0b54cf3">cuptiSetEventCollectionMode</a> will return CUPTI_ERROR_INVALID_OPERATION. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Kernels</b> might take longer to run if many events are enabled. <p>
<b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g3f6f5236a79cafda724796dc2d599ad1"></a><!-- doxytag: member="cupti_events.h::cuptiEnumEventDomains" ref="g3f6f5236a79cafda724796dc2d599ad1" args="(size_t *arraySizeBytes, CUpti_EventDomainID *domainArray)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEnumEventDomains           </td>
          <td>(</td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>arraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a> *&nbsp;</td>
          <td class="paramname"> <em>domainArray</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns all the event domains available on any CUDA-capable device. Event domain IDs are returned in <code>domainArray</code>. The size of the <code>domainArray</code> buffer is given by <code>*arraySizeBytes</code>. The size of the <code>domainArray</code> buffer must be at least <code>numDomains</code> * sizeof(CUpti_EventDomainID) or all domains will not be returned. The value returned in <code>*arraySizeBytes</code> contains the number of bytes returned in <code>domainArray</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>arraySizeBytes</em>&nbsp;</td><td>The size of <code>domainArray</code> in bytes, and returns the number of bytes written to <code>domainArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>domainArray</em>&nbsp;</td><td>Returns all the event domains</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>arraySizeBytes</code> or <code>domainArray</code> are NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd23627587d3f21c3d637edd562e362d3"></a><!-- doxytag: member="cupti_events.h::cuptiEventDomainEnumEvents" ref="gd23627587d3f21c3d637edd562e362d3" args="(CUpti_EventDomainID eventDomain, size_t *arraySizeBytes, CUpti_EventID *eventArray)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventDomainEnumEvents           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a>&nbsp;</td>
          <td class="paramname"> <em>eventDomain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>arraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *&nbsp;</td>
          <td class="paramname"> <em>eventArray</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the event IDs in <code>eventArray</code> for a domain. The size of the <code>eventArray</code> buffer is given by <code>*arraySizeBytes</code>. The size of the <code>eventArray</code> buffer must be at least <code>numdomainevents</code> * sizeof(CUpti_EventID) or else all events will not be returned. The value returned in <code>*arraySizeBytes</code> contains the number of bytes returned in <code>eventArray</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventDomain</em>&nbsp;</td><td>ID of the event domain </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>arraySizeBytes</em>&nbsp;</td><td>The size of <code>eventArray</code> in bytes, and returns the number of bytes written to <code>eventArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventArray</em>&nbsp;</td><td>Returns the IDs of the events in the domain</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_DOMAIN_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>arraySizeBytes</code> or <code>eventArray</code> are NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gf8084beb1c692a17616fb99523a044a5"></a><!-- doxytag: member="cupti_events.h::cuptiEventDomainGetAttribute" ref="gf8084beb1c692a17616fb99523a044a5" args="(CUpti_EventDomainID eventDomain, CUpti_EventDomainAttribute attrib, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventDomainGetAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a>&nbsp;</td>
          <td class="paramname"> <em>eventDomain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#ga201105be2a6a988b72e70a643c82ad5">CUpti_EventDomainAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns an event domain attribute in <code>*value</code>. The size of the <code>value</code> buffer is given by <code>*valueSize</code>. The value returned in <code>*valueSize</code> contains the number of bytes returned in <code>value</code>.<p>
If the attribute value is a c-string that is longer than <code>*valueSize</code>, then only the first <code>*valueSize</code> characters will be returned and there will be no terminating null byte. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventDomain</em>&nbsp;</td><td>ID of the event domain </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>attrib</em>&nbsp;</td><td>The event domain attribute to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>The size of the <code>value</code> buffer in bytes, and returns the number of bytes written to <code>value</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>Returns the attribute's value</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_DOMAIN_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attrib</code> is not an event domain attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>For non-c-string attribute values, indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g7341de781de833b65e0748b37acc802f"></a><!-- doxytag: member="cupti_events.h::cuptiEventDomainGetNumEvents" ref="g7341de781de833b65e0748b37acc802f" args="(CUpti_EventDomainID eventDomain, uint32_t *numEvents)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventDomainGetNumEvents           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g8a898bd33ce64d353b79d7d39e74d198">CUpti_EventDomainID</a>&nbsp;</td>
          <td class="paramname"> <em>eventDomain</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numEvents</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of events in <code>numEvents</code> for a domain. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventDomain</em>&nbsp;</td><td>ID of the event domain </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>numEvents</em>&nbsp;</td><td>Returns the number of events in the domain</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_DOMAIN_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>numEvents</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gec3d52db48a14aef9e8b35785bb4d60e"></a><!-- doxytag: member="cupti_events.h::cuptiEventGetAttribute" ref="gec3d52db48a14aef9e8b35785bb4d60e" args="(CUpti_EventID event, CUpti_EventAttribute attrib, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGetAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a>&nbsp;</td>
          <td class="paramname"> <em>event</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g232a6c540682383abd4cee93ee18e6aa">CUpti_EventAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns an event attribute in <code>*value</code>. The size of the <code>value</code> buffer is given by <code>*valueSize</code>. The value returned in <code>*valueSize</code> contains the number of bytes returned in <code>value</code>.<p>
If the attribute value is a c-string that is longer than <code>*valueSize</code>, then only the first <code>*valueSize</code> characters will be returned and there will be no terminating null byte. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>event</em>&nbsp;</td><td>ID of the event </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>attrib</em>&nbsp;</td><td>The event attribute to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>The size of the <code>value</code> buffer in bytes, and returns the number of bytes written to <code>value</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>Returns the attribute's value</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attrib</code> is not an event attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>For non-c-string attribute values, indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gd27429cf327f99db6b49e88548f801d4"></a><!-- doxytag: member="cupti_events.h::cuptiEventGetIdFromName" ref="gd27429cf327f99db6b49e88548f801d4" args="(CUdevice device, const char *eventName, CUpti_EventID *event)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGetIdFromName           </td>
          <td>(</td>
          <td class="paramtype">CUdevice&nbsp;</td>
          <td class="paramname"> <em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>eventName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *&nbsp;</td>
          <td class="paramname"> <em>event</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Find an event by name and return the event ID in <code>*event</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>device</em>&nbsp;</td><td>The CUDA device </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventName</em>&nbsp;</td><td>The name of the event to find </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>event</em>&nbsp;</td><td>Returns the ID of the found event or undefined if unable to find the event</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_DEVICE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_NAME</em>&nbsp;</td><td>if unable to find an event with name <code>eventName</code>. In this case <code>*event</code> is undefined </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventName</code> or <code>event</code> are NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g08b80dcd21975492c7d756eac59d8b6a"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupAddEvent" ref="g08b80dcd21975492c7d756eac59d8b6a" args="(CUpti_EventGroup eventGroup, CUpti_EventID event)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupAddEvent           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a>&nbsp;</td>
          <td class="paramname"> <em>event</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Add an event to an event group. The event add can fail for a number of reasons: <ul>
<li>The event group is enabled </li>
<li>The event does not belong to the same event domain as the events that are already in the event group </li>
<li>Device limitations on the events that can belong to the same group </li>
<li>The event group is full</li>
</ul>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>event</em>&nbsp;</td><td>The event to add to the group</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if <code>eventGroup</code> is enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>if <code>event</code> belongs to a different event domain than the events already in <code>eventGroup</code>, or if a device limitation prevents <code>event</code> from being collected at the same time as the events already in <code>eventGroup</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_MAX_LIMIT_REACHED</em>&nbsp;</td><td>if <code>eventGroup</code> is full </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g9d5cad301581eb32e94ffc7c1b9afc9d"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupCreate" ref="g9d5cad301581eb32e94ffc7c1b9afc9d" args="(CUcontext context, CUpti_EventGroup *eventGroup, uint32_t flags)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupCreate           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> *&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>flags</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Creates a new event group for <code>context</code> and returns the new group in <code>*eventGroup</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><code>flags</code> are reserved for future use and should be set to zero. <p>
<b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context for the event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>Returns the new event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>flags</em>&nbsp;</td><td>Reserved - must be zero</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g99f31aed86f5c117ef07409982f25bd0"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupDestroy" ref="g99f31aed86f5c117ef07409982f25bd0" args="(CUpti_EventGroup eventGroup)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupDestroy           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Destroy an <code>eventGroup</code> and free its resources. An event group cannot be destroyed if it is enabled. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group to destroy</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if the event group is enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if eventGroup is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g63365c4bb038575bcd6b1a2ba13649d4"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupDisable" ref="g63365c4bb038575bcd6b1a2ba13649d4" args="(CUpti_EventGroup eventGroup)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupDisable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Disable an event group. Disabling an event group stops collection of events contained in the group. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g1b72c6cea4de6cc0ebee2c0b41453223"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupEnable" ref="g1b72c6cea4de6cc0ebee2c0b41453223" args="(CUpti_EventGroup eventGroup)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupEnable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable an event group. Enabling an event group zeros the value of all the events in the group and then starts collection of those events. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_READY</em>&nbsp;</td><td>if <code>eventGroup</code> does not contain any events </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>if <code>eventGroup</code> cannot be enabled due to other already enabled event groups </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code> is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE_BUSY</em>&nbsp;</td><td>if another client is profiling and hardware is busy </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g573c7e8a0295d36cf0051e27554971a8"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupGetAttribute" ref="g573c7e8a0295d36cf0051e27554971a8" args="(CUpti_EventGroup eventGroup, CUpti_EventGroupAttribute attrib, size_t *valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupGetAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#gc8aaac37b2aefeea5177c0b813806b9f">CUpti_EventGroupAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Read an event group attribute and return it in <code>*value</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe but client must guard against simultaneous destruction or modification of <code>eventGroup</code> (for example, client must guard against simultaneous calls to <a class="el" href="group__CUPTI__EVENT__API.html#g99f31aed86f5c117ef07409982f25bd0">cuptiEventGroupDestroy</a>, <a class="el" href="group__CUPTI__EVENT__API.html#g08b80dcd21975492c7d756eac59d8b6a">cuptiEventGroupAddEvent</a>, etc.), and must guard against simultaneous destruction of the context in which <code>eventGroup</code> was created (for example, client must guard against simultaneous calls to cudaDeviceReset, cuCtxDestroy, etc.).</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>attrib</em>&nbsp;</td><td>The attribute to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>Size of buffer pointed by the value, and returns the number of bytes written to <code>value</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>Returns the value of the attribute</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attrib</code> is not an eventgroup attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>For non-c-string attribute values, indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gbb522fec20a70e3b5d8f819693c4f2dd"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupReadAllEvents" ref="gbb522fec20a70e3b5d8f819693c4f2dd" args="(CUpti_EventGroup eventGroup, CUpti_ReadEventFlags flags, size_t *eventValueBufferSizeBytes, uint64_t *eventValueBuffer, size_t *eventIdArraySizeBytes, CUpti_EventID *eventIdArray, size_t *numEventIdsRead)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupReadAllEvents           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#ge8ea7de2653cc087a9c5399386604528">CUpti_ReadEventFlags</a>&nbsp;</td>
          <td class="paramname"> <em>flags</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>eventValueBufferSizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>eventValueBuffer</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>eventIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *&nbsp;</td>
          <td class="paramname"> <em>eventIdArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>numEventIdsRead</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Read the values for all the events in an event group. The event values are returned in the <code>eventValueBuffer</code> buffer. <code>eventValueBufferSizeBytes</code> indicates the size of <code>eventValueBuffer</code>. The buffer must be at least (sizeof(uint64) * number of events in group) if <a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f776be3128e2ab834a96ec9ff16dda611">CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES</a> is not set on the group containing the events. The buffer must be at least (sizeof(uint64) * number of domain instances * number of events in group) if <a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f776be3128e2ab834a96ec9ff16dda611">CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES</a> is set on the group.<p>
The data format returned in <code>eventValueBuffer</code> is:<ul>
<li>domain instance 0: event0 event1 ... eventN</li><li>domain instance 1: event0 event1 ... eventN</li><li>...</li><li>domain instance M: event0 event1 ... eventN</li></ul>
<p>
The event order in <code>eventValueBuffer</code> is returned in <code>eventIdArray</code>. The size of <code>eventIdArray</code> is specified in <code>eventIdArraySizeBytes</code>. The size should be at least (sizeof(CUpti_EventID) * number of events in group).<p>
If any instance of any event counter overflows, the value returned for that event instance will be <a class="el" href="group__CUPTI__EVENT__API.html#g4f280929637a726da2ba4c907e5b9ba1" title="The overflow value for a CUPTI event.">CUPTI_EVENT_OVERFLOW</a>.<p>
The only allowed value for <code>flags</code> is <a class="el" href="group__CUPTI__EVENT__API.html#gge8ea7de2653cc087a9c5399386604528726bee26555027440a8b4acdf6ff13a3">CUPTI_EVENT_READ_FLAG_NONE</a>.<p>
Reading events from a disabled event group is not allowed. After being read, an event's value is reset to zero. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe but client must guard against simultaneous destruction or modification of <code>eventGroup</code> (for example, client must guard against simultaneous calls to <a class="el" href="group__CUPTI__EVENT__API.html#g99f31aed86f5c117ef07409982f25bd0">cuptiEventGroupDestroy</a>, <a class="el" href="group__CUPTI__EVENT__API.html#g08b80dcd21975492c7d756eac59d8b6a">cuptiEventGroupAddEvent</a>, etc.), and must guard against simultaneous destruction of the context in which <code>eventGroup</code> was created (for example, client must guard against simultaneous calls to cudaDeviceReset, cuCtxDestroy, etc.). If <a class="el" href="group__CUPTI__EVENT__API.html#gbda509844936046694b6081cffa4886d">cuptiEventGroupResetAllEvents</a> is called simultaneously with this function, then returned event values are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>flags</em>&nbsp;</td><td>Flags controlling the reading mode </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueBufferSizeBytes</em>&nbsp;</td><td>The size of <code>eventValueBuffer</code> in bytes, and returns the number of bytes written to <code>eventValueBuffer</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueBuffer</em>&nbsp;</td><td>Returns the event values </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArraySizeBytes</em>&nbsp;</td><td>The size of <code>eventIdArray</code> in bytes, and returns the number of bytes written to <code>eventIdArray</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArray</em>&nbsp;</td><td>Returns the IDs of the events in the same order as the values return in eventValueBuffer. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>numEventIdsRead</em>&nbsp;</td><td>Returns the number of event IDs returned in <code>eventIdArray</code> </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if <code>eventGroup</code> is disabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code>, <code>eventValueBufferSizeBytes</code>, <code>eventValueBuffer</code>, <code>eventIdArraySizeBytes</code>, <code>eventIdArray</code> or <code>numEventIdsRead</code> is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>if size of <code>eventValueBuffer</code> or <code>eventIdArray</code> is not sufficient </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g31c30e2c86218a3501ae8a21127dec15"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupReadEvent" ref="g31c30e2c86218a3501ae8a21127dec15" args="(CUpti_EventGroup eventGroup, CUpti_ReadEventFlags flags, CUpti_EventID event, size_t *eventValueBufferSizeBytes, uint64_t *eventValueBuffer)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupReadEvent           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#ge8ea7de2653cc087a9c5399386604528">CUpti_ReadEventFlags</a>&nbsp;</td>
          <td class="paramname"> <em>flags</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a>&nbsp;</td>
          <td class="paramname"> <em>event</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&nbsp;</td>
          <td class="paramname"> <em>eventValueBufferSizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&nbsp;</td>
          <td class="paramname"> <em>eventValueBuffer</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Read the value for an event in an event group. The event value is returned in the <code>eventValueBuffer</code> buffer. <code>eventValueBufferSizeBytes</code> indicates the size of the <code>eventValueBuffer</code> buffer. The buffer must be at least sizeof(uint64) if <a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f776be3128e2ab834a96ec9ff16dda611">CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES</a> is not set on the group containing the event. The buffer must be at least (sizeof(uint64) * number of domain instances) if <a class="el" href="group__CUPTI__EVENT__API.html#ggc8aaac37b2aefeea5177c0b813806b9f776be3128e2ab834a96ec9ff16dda611">CUPTI_EVENT_GROUP_ATTR_PROFILE_ALL_DOMAIN_INSTANCES</a> is set on the group.<p>
If any instance of an event counter overflows, the value returned for that event instance will be <a class="el" href="group__CUPTI__EVENT__API.html#g4f280929637a726da2ba4c907e5b9ba1" title="The overflow value for a CUPTI event.">CUPTI_EVENT_OVERFLOW</a>.<p>
The only allowed value for <code>flags</code> is <a class="el" href="group__CUPTI__EVENT__API.html#gge8ea7de2653cc087a9c5399386604528726bee26555027440a8b4acdf6ff13a3">CUPTI_EVENT_READ_FLAG_NONE</a>.<p>
Reading an event from a disabled event group is not allowed. After being read, an event's value is reset to zero. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe but client must guard against simultaneous destruction or modification of <code>eventGroup</code> (for example, client must guard against simultaneous calls to <a class="el" href="group__CUPTI__EVENT__API.html#g99f31aed86f5c117ef07409982f25bd0">cuptiEventGroupDestroy</a>, <a class="el" href="group__CUPTI__EVENT__API.html#g08b80dcd21975492c7d756eac59d8b6a">cuptiEventGroupAddEvent</a>, etc.), and must guard against simultaneous destruction of the context in which <code>eventGroup</code> was created (for example, client must guard against simultaneous calls to cudaDeviceReset, cuCtxDestroy, etc.). If <a class="el" href="group__CUPTI__EVENT__API.html#gbda509844936046694b6081cffa4886d">cuptiEventGroupResetAllEvents</a> is called simultaneously with this function, then returned event values are undefined.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>flags</em>&nbsp;</td><td>Flags controlling the reading mode </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>event</em>&nbsp;</td><td>The event to read </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueBufferSizeBytes</em>&nbsp;</td><td>The size of <code>eventValueBuffer</code> in bytes, and returns the number of bytes written to <code>eventValueBuffer</code> </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventValueBuffer</em>&nbsp;</td><td>Returns the event value(s)</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if <code>eventGroup</code> is disabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code>, <code>eventValueBufferSizeBytes</code> or <code>eventValueBuffer</code> is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>if size of <code>eventValueBuffer</code> is not sufficient </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g4d7555b6c900a27dee2ee3465701bb96"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupRemoveAllEvents" ref="g4d7555b6c900a27dee2ee3465701bb96" args="(CUpti_EventGroup eventGroup)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupRemoveAllEvents           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Remove all events from an event group. Events cannot be removed if the event group is enabled. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if <code>eventGroup</code> is enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0da4bfbdde94a0fcb5bba2f1c69aa1f0"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupRemoveEvent" ref="g0da4bfbdde94a0fcb5bba2f1c69aa1f0" args="(CUpti_EventGroup eventGroup, CUpti_EventID event)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupRemoveEvent           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a>&nbsp;</td>
          <td class="paramname"> <em>event</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Remove <code>event</code> from the an event group. The event cannot be removed if the event group is enabled. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>event</em>&nbsp;</td><td>The event to remove from the group</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if <code>eventGroup</code> is enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gbda509844936046694b6081cffa4886d"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupResetAllEvents" ref="gbda509844936046694b6081cffa4886d" args="(CUpti_EventGroup eventGroup)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupResetAllEvents           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Zero all the event counts in an event group. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe but client must guard against simultaneous destruction or modification of <code>eventGroup</code> (for example, client must guard against simultaneous calls to <a class="el" href="group__CUPTI__EVENT__API.html#g99f31aed86f5c117ef07409982f25bd0">cuptiEventGroupDestroy</a>, <a class="el" href="group__CUPTI__EVENT__API.html#g08b80dcd21975492c7d756eac59d8b6a">cuptiEventGroupAddEvent</a>, etc.), and must guard against simultaneous destruction of the context in which <code>eventGroup</code> was created (for example, client must guard against simultaneous calls to cudaDeviceReset, cuCtxDestroy, etc.).</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroup</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gdeea7eb4a79d3c7e85db597f2c98d5ac"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupSetAttribute" ref="gdeea7eb4a79d3c7e85db597f2c98d5ac" args="(CUpti_EventGroup eventGroup, CUpti_EventGroupAttribute attrib, size_t valueSize, void *value)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupSetAttribute           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>&nbsp;</td>
          <td class="paramname"> <em>eventGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#gc8aaac37b2aefeea5177c0b813806b9f">CUpti_EventGroupAttribute</a>&nbsp;</td>
          <td class="paramname"> <em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>valueSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>value</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Write an event group attribute. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroup</em>&nbsp;</td><td>The event group </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>attrib</em>&nbsp;</td><td>The attribute to write </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>valueSize</em>&nbsp;</td><td>The size, in bytes, of the value </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>value</em>&nbsp;</td><td>The attribute value to write</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>valueSize</code> or <code>value</code> is NULL, or if <code>attrib</code> is not an event group attribute, or if <code>attrib</code> is not a writable attribute </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>Indicates that the <code>value</code> buffer is too small to hold the attribute value. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0dc70d03c11800d734801a3d4fdac073"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupSetDisable" ref="g0dc70d03c11800d734801a3d4fdac073" args="(CUpti_EventGroupSet *eventGroupSet)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupSetDisable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__EventGroupSet.html">CUpti_EventGroupSet</a> *&nbsp;</td>
          <td class="paramname"> <em>eventGroupSet</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Disable a set of event groups. Disabling a set of event groups stops collection of events contained in the groups. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe. <p>
<b>If</b> this call fails, some of the event groups in the set may be disabled and other event groups may remain enabled.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroupSet</em>&nbsp;</td><td>The pointer to the event group set </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroupSet</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gff1d5b7675cc2748eadae7348e30cbfc"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupSetEnable" ref="gff1d5b7675cc2748eadae7348e30cbfc" args="(CUpti_EventGroupSet *eventGroupSet)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupSetEnable           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__EventGroupSet.html">CUpti_EventGroupSet</a> *&nbsp;</td>
          <td class="paramname"> <em>eventGroupSet</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable a set of event groups. Enabling a set of event groups zeros the value of all the events in all the groups and then starts collection of those events. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroupSet</em>&nbsp;</td><td>The pointer to the event group set</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_READY</em>&nbsp;</td><td>if <code>eventGroup</code> does not contain any events </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>if <code>eventGroup</code> cannot be enabled due to other already enabled event groups </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroupSet</code> is NULL </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_HARDWARE_BUSY</em>&nbsp;</td><td>if other client is profiling and hardware is busy </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g0fd307d429d4e37f61f45472de069910"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupSetsCreate" ref="g0fd307d429d4e37f61f45472de069910" args="(CUcontext context, size_t eventIdArraySizeBytes, CUpti_EventID *eventIdArray, CUpti_EventGroupSets **eventGroupPasses)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupSetsCreate           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>eventIdArraySizeBytes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> *&nbsp;</td>
          <td class="paramname"> <em>eventIdArray</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> **&nbsp;</td>
          <td class="paramname"> <em>eventGroupPasses</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of events that can be collected simultaneously varies by device and by the type of the events. When events can be collected simultaneously, they may need to be grouped into multiple event groups because they are from different event domains. This function takes a set of events and determines how many passes are required to collect all those events, and which events can be collected simultaneously in each pass.<p>
The <a class="el" href="structCUpti__EventGroupSets.html" title="A set of event group sets.">CUpti_EventGroupSets</a> returned in <code>eventGroupPasses</code> indicates how many passes are required to collect the events with the <code>numSets</code> field. Within each event group set, the <code>sets</code> array indicates the event groups that should be collected on each pass. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe, but client must guard against another thread simultaneously destroying <code>context</code>.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context for event collection </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArraySizeBytes</em>&nbsp;</td><td>Size of <code>eventIdArray</code> in bytes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventIdArray</em>&nbsp;</td><td>Array of event IDs that need to be grouped </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>eventGroupPasses</em>&nbsp;</td><td>Returns a <a class="el" href="structCUpti__EventGroupSets.html" title="A set of event group sets.">CUpti_EventGroupSets</a> object that indicates the number of passes required to collect the events and the events to collect on each pass</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_EVENT_ID</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventIdArray</code> or <code>eventGroupPasses</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gcf75cc84517241e4c40c21c2f312e2ad"></a><!-- doxytag: member="cupti_events.h::cuptiEventGroupSetsDestroy" ref="gcf75cc84517241e4c40c21c2f312e2ad" args="(CUpti_EventGroupSets *eventGroupSets)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiEventGroupSetsDestroy           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structCUpti__EventGroupSets.html">CUpti_EventGroupSets</a> *&nbsp;</td>
          <td class="paramname"> <em>eventGroupSets</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Destroy a <a class="el" href="structCUpti__EventGroupSets.html" title="A set of event group sets.">CUpti_EventGroupSets</a> object. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>eventGroupSets</em>&nbsp;</td><td>The object to destroy</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if any of the event groups contained in the sets is enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>eventGroupSets</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="g93814f61b681f440b43c8130f72a99f9"></a><!-- doxytag: member="cupti_events.h::cuptiGetNumEventDomains" ref="g93814f61b681f440b43c8130f72a99f9" args="(uint32_t *numDomains)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiGetNumEventDomains           </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numDomains</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the total number of event domains available on any CUDA-capable device. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>numDomains</em>&nbsp;</td><td>Returns the number of domains</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>numDomains</code> is NULL </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gfce5f989d4daee01499c96884ca5653e"></a><!-- doxytag: member="cupti_events.h::cuptiKernelReplaySubscribeUpdate" ref="gfce5f989d4daee01499c96884ca5653e" args="(CUpti_KernelReplayUpdateFunc updateFunc, void *customData)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiKernelReplaySubscribeUpdate           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g4c9cefe906f4af450ae0ad5dc1d39b1c">CUpti_KernelReplayUpdateFunc</a>&nbsp;</td>
          <td class="paramname"> <em>updateFunc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname"> <em>customData</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
When subscribed, the function pointer passed in will be called each time a kernel run is finished during kernel replay. Previously subscribed function pointer will be replaced. Pass in NULL as the function pointer unsubscribes the update.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>updateFunc</em>&nbsp;</td><td>The update function pointer </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>customData</em>&nbsp;</td><td>Pointer to any custom data </td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="gdfa8bf3fa3d6c6445f2cb874d0b54cf3"></a><!-- doxytag: member="cupti_events.h::cuptiSetEventCollectionMode" ref="gdfa8bf3fa3d6c6445f2cb874d0b54cf3" args="(CUcontext context, CUpti_EventCollectionMode mode)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__RESULT__API.html#g8c54bf95108e67d858f37fcf76c88714">CUptiResult</a> cuptiSetEventCollectionMode           </td>
          <td>(</td>
          <td class="paramtype">CUcontext&nbsp;</td>
          <td class="paramname"> <em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__CUPTI__EVENT__API.html#g54361f7bed89987eabb91300cc218712">CUpti_EventCollectionMode</a>&nbsp;</td>
          <td class="paramname"> <em>mode</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set the event collection mode for a <code>context</code>. The <code>mode</code> controls the event collection behavior of all events in event groups created in the <code>context</code>. This API is invalid in kernel replay mode. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>context</em>&nbsp;</td><td>The context </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>mode</em>&nbsp;</td><td>The event collection mode</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_SUCCESS</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td></td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>if called when replay mode is enabled </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>CUPTI_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>if mode is not supported on the device </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:47 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
