<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>CUPTI</b>::<b>PcSamplingUtil</b>::<a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html">CUptiUtil_GetPcSampDataParams</a>
  </div>
</div>
<div class="contents">
<h1>CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html">CUPTI PC Sampling Utility API</a>]</small>
</h1><!-- doxytag: class="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams" -->Params for CuptiUtilGetPcSampData.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g1dc90a8b2a7ee09a0c230aa6256bba5e">PcSamplingBufferType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#c699349113597fc553aef28c79984368">bufferType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">std::ifstream *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#3da28dd6faa77904a1dee9608a3296e8">fileHandler</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#a5938dd3236f8326d2aaec8ea92b80ee">numAttributes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structBufferInfo.html">BufferInfo</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#5b7c261e08f1600a008eef46023cb658">pBufferInfoData</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#780bcd388e5f1da82df2bb60a001724f">pPCSamplingConfigurationInfo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#885d3688c9b0d0ae5d168d866d8db2ce">pPcSamplingStallReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#6c0e44aaa78a597f5aca381ff8fb7535">pSamplingData</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#ea2918b846192545ec3855dd05fb3b1f">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="c699349113597fc553aef28c79984368"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::bufferType" ref="c699349113597fc553aef28c79984368" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html#g1dc90a8b2a7ee09a0c230aa6256bba5e">PcSamplingBufferType</a> <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#c699349113597fc553aef28c79984368">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::bufferType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of buffer to store in file 
</div>
</div><p>
<a class="anchor" name="3da28dd6faa77904a1dee9608a3296e8"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::fileHandler" ref="3da28dd6faa77904a1dee9608a3296e8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::ifstream* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#3da28dd6faa77904a1dee9608a3296e8">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::fileHandler</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
File handle. 
</div>
</div><p>
<a class="anchor" name="a5938dd3236f8326d2aaec8ea92b80ee"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::numAttributes" ref="a5938dd3236f8326d2aaec8ea92b80ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#a5938dd3236f8326d2aaec8ea92b80ee">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::numAttributes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of configuration attributes 
</div>
</div><p>
<a class="anchor" name="5b7c261e08f1600a008eef46023cb658"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pBufferInfoData" ref="5b7c261e08f1600a008eef46023cb658" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structBufferInfo.html">BufferInfo</a>* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#5b7c261e08f1600a008eef46023cb658">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pBufferInfoData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to collected buffer info using CuptiUtilGetBufferInfo 
</div>
</div><p>
<a class="anchor" name="780bcd388e5f1da82df2bb60a001724f"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pPCSamplingConfigurationInfo" ref="780bcd388e5f1da82df2bb60a001724f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a>* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#780bcd388e5f1da82df2bb60a001724f">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pPCSamplingConfigurationInfo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html">CUpti_PCSamplingConfigurationInfo</a> 
</div>
</div><p>
<a class="anchor" name="885d3688c9b0d0ae5d168d866d8db2ce"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pPcSamplingStallReasons" ref="885d3688c9b0d0ae5d168d866d8db2ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a>* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#885d3688c9b0d0ae5d168d866d8db2ce">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pPcSamplingStallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a>. For stallReasons field of <a class="el" href="structPcSamplingStallReasons.html">PcSamplingStallReasons</a> it is expected to allocate memory for each string element of array. 
</div>
</div><p>
<a class="anchor" name="6c0e44aaa78a597f5aca381ff8fb7535"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pSamplingData" ref="6c0e44aaa78a597f5aca381ff8fb7535" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#6c0e44aaa78a597f5aca381ff8fb7535">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::pSamplingData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to allocated memory to store retrieved data from file. 
</div>
</div><p>
<a class="anchor" name="ea2918b846192545ec3855dd05fb3b1f"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::size" ref="ea2918b846192545ec3855dd05fb3b1f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#ea2918b846192545ec3855dd05fb3b1f">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of the data structure i.e. CUpti_PCSamplingDisableParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:52 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
