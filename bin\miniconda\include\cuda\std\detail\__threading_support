//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA___THREADING_SUPPORT
#define _CUDA___THREADING_SUPPORT

#ifndef __CUDACC_RTC__
    #include <thread>
    #include <errno.h>
#endif


#include "../chrono"
#include "../climits"

#include "__config"

#include "libcxx/include/__threading_support"

#endif //_CUDA___THREADING_SUPPORT
