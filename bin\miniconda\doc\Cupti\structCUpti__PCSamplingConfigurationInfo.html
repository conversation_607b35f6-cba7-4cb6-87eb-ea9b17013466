<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingConfigurationInfo Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingConfigurationInfo Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingConfigurationInfo" -->PC sampling configuration information structure.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g4fe866bd47d8825f4d46cc52c8e29bb5">CUpti_PCSamplingConfigurationAttributeType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#61f71926fc2dd6186d2d6759b80c9446">attributeType</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#55b8045c38d989095c50d2ac7c5f3fd2">collectionModeData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#878da908c058ab9b44f43d0d360985b8">enableStartStopControlData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#581bec63b687e610fd4655c566debb00">hardwareBufferSizeData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#9f72022e5494979f35c59a9d16633392">invalidData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#b083c6e1deab377f674a5aa9a34d1d6e">outputDataFormatData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#b0ebcf6890eb69cf0ab41e84e981f894">samplingDataBufferData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8833ff4efd02bdf82b21ba834b68694b">samplingPeriodData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#295cf66f184fb5e9fd464006185789b7">scratchBufferSizeData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8094dc8e2d1a7d895b2fb311d60f02aa">stallReasonData</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8d38c366c66897ee3b00f0581cdc7b11">workerThreadPeriodicSleepSpanData</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure provides <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g4fe866bd47d8825f4d46cc52c8e29bb5">CUpti_PCSamplingConfigurationAttributeType</a> which can be configured or queried for PC sampling configuration <hr><h2>Field Documentation</h2>
<a class="anchor" name="61f71926fc2dd6186d2d6759b80c9446"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::attributeType" ref="61f71926fc2dd6186d2d6759b80c9446" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__PCSAMPLING__API.html#g4fe866bd47d8825f4d46cc52c8e29bb5">CUpti_PCSamplingConfigurationAttributeType</a> <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#61f71926fc2dd6186d2d6759b80c9446">CUpti_PCSamplingConfigurationInfo::attributeType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#g4fe866bd47d8825f4d46cc52c8e29bb5">CUpti_PCSamplingConfigurationAttributeType</a> for all supported attribute types 
</div>
</div><p>
<a class="anchor" name="55b8045c38d989095c50d2ac7c5f3fd2"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::collectionModeData" ref="55b8045c38d989095c50d2ac7c5f3fd2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#55b8045c38d989095c50d2ac7c5f3fd2">CUpti_PCSamplingConfigurationInfo::collectionModeData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5a561bd34aebe51eb26e4695ea0d865bb">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_COLLECTION_MODE</a> 
</div>
</div><p>
<a class="anchor" name="878da908c058ab9b44f43d0d360985b8"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::enableStartStopControlData" ref="878da908c058ab9b44f43d0d360985b8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#878da908c058ab9b44f43d0d360985b8">CUpti_PCSamplingConfigurationInfo::enableStartStopControlData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5c75dea72b5908405f9096dd1ae27366b">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_ENABLE_START_STOP_CONTROL</a> 
</div>
</div><p>
<a class="anchor" name="581bec63b687e610fd4655c566debb00"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::hardwareBufferSizeData" ref="581bec63b687e610fd4655c566debb00" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#581bec63b687e610fd4655c566debb00">CUpti_PCSamplingConfigurationInfo::hardwareBufferSizeData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5979e3dbbec977c8141ba3e01b8dbf014">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_HARDWARE_BUFFER_SIZE</a> 
</div>
</div><p>
<a class="anchor" name="9f72022e5494979f35c59a9d16633392"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::invalidData" ref="9f72022e5494979f35c59a9d16633392" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#9f72022e5494979f35c59a9d16633392">CUpti_PCSamplingConfigurationInfo::invalidData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Invalid Value 
</div>
</div><p>
<a class="anchor" name="b083c6e1deab377f674a5aa9a34d1d6e"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::outputDataFormatData" ref="b083c6e1deab377f674a5aa9a34d1d6e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#b083c6e1deab377f674a5aa9a34d1d6e">CUpti_PCSamplingConfigurationInfo::outputDataFormatData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5a19b4a5c74150e029c95a6a320018f6a">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_OUTPUT_DATA_FORMAT</a> 
</div>
</div><p>
<a class="anchor" name="b0ebcf6890eb69cf0ab41e84e981f894"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::samplingDataBufferData" ref="b0ebcf6890eb69cf0ab41e84e981f894" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#b0ebcf6890eb69cf0ab41e84e981f894">CUpti_PCSamplingConfigurationInfo::samplingDataBufferData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb5cefa869098e32174b5ed1e8dfd96af06">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_DATA_BUFFER</a> 
</div>
</div><p>
<a class="anchor" name="8833ff4efd02bdf82b21ba834b68694b"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::samplingPeriodData" ref="8833ff4efd02bdf82b21ba834b68694b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8833ff4efd02bdf82b21ba834b68694b">CUpti_PCSamplingConfigurationInfo::samplingPeriodData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb55a3304f055fc43b3f1885f2b5fe3d35c">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SAMPLING_PERIOD</a> 
</div>
</div><p>
<a class="anchor" name="295cf66f184fb5e9fd464006185789b7"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::scratchBufferSizeData" ref="295cf66f184fb5e9fd464006185789b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#295cf66f184fb5e9fd464006185789b7">CUpti_PCSamplingConfigurationInfo::scratchBufferSizeData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb58f4358cee727885dde3311a81bb996cc">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_SCRATCH_BUFFER_SIZE</a> 
</div>
</div><p>
<a class="anchor" name="8094dc8e2d1a7d895b2fb311d60f02aa"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::stallReasonData" ref="8094dc8e2d1a7d895b2fb311d60f02aa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8094dc8e2d1a7d895b2fb311d60f02aa">CUpti_PCSamplingConfigurationInfo::stallReasonData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb56b99e85c0ca755ba6f1e1b68af6de405">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_STALL_REASON</a> 
</div>
</div><p>
<a class="anchor" name="8d38c366c66897ee3b00f0581cdc7b11"></a><!-- doxytag: member="CUpti_PCSamplingConfigurationInfo::workerThreadPeriodicSleepSpanData" ref="8d38c366c66897ee3b00f0581cdc7b11" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8d38c366c66897ee3b00f0581cdc7b11">CUpti_PCSamplingConfigurationInfo::workerThreadPeriodicSleepSpanData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Refer <a class="el" href="group__CUPTI__PCSAMPLING__API.html#gg4fe866bd47d8825f4d46cc52c8e29bb567b78d0e6180a9cb3220e49e6b842748">CUPTI_PC_SAMPLING_CONFIGURATION_ATTR_TYPE_WORKER_THREAD_PERIODIC_SLEEP_SPAN</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
