<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-us" xml:lang="en-us">
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8"></meta>
      <meta http-equiv="X-UA-Compatible" content="IE=edge"></meta>
      <meta name="copyright" content="(C) Copyright 2005"></meta>
      <meta name="DC.rights.owner" content="(C) Copyright 2005"></meta>
      <meta name="DC.Type" content="cuda_reference"></meta>
      <meta name="DC.Title" content="Library Support"></meta>
      <meta name="DC.Format" content="XHTML"></meta>
      <meta name="DC.Identifier" content="r_library_support"></meta>
      <link rel="stylesheet" type="text/css" href="../common/formatting/commonltr.css"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/site.css"></link>
      <title>CUPTI :: CUPTI Documentation</title>
      <!--[if lt IE 9]>
      <script src="../common/formatting/html5shiv-printshiv.min.js"></script>
      <![endif]-->
      <script type="text/javascript" charset="utf-8" src="../common/scripts/tynt/tynt.js"></script>
      --&gt;
      
      <script src="https://assets.adobedtm.com/5d4962a43b79/c1061d2c5e7b/launch-191c2462b890.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.ba-hashchange.min.js"></script>
      <script type="text/javascript" charset="utf-8" src="../common/formatting/jquery.scrollintoview.min.js"></script>
      <script type="text/javascript" src="../search/htmlFileList.js"></script>
      <script type="text/javascript" src="../search/htmlFileInfoList.js"></script>
      <script type="text/javascript" src="../search/nwSearchFnt.min.js"></script>
      <script type="text/javascript" src="../search/stemmers/en_stemmer.min.js"></script>
      <script type="text/javascript" src="../search/index-1.js"></script>
      <script type="text/javascript" src="../search/index-2.js"></script>
      <script type="text/javascript" src="../search/index-3.js"></script>
      <link rel="canonical" href="https://docs.nvidia.com/cupti/Cupti/index.html"></link>
      <link rel="stylesheet" type="text/css" href="../common/formatting/qwcode.highlight.css"></link>
   </head>
   <body>
      
      <header id="header"><span id="company">NVIDIA</span><span id="site-title">CUPTI Documentation</span><form id="search" method="get" action="search">
            <input type="text" name="search-text"></input><fieldset id="search-location">
               <legend>Search In:</legend>
               <label><input type="radio" name="search-type" value="site"></input>Entire Site</label>
               <label><input type="radio" name="search-type" value="document"></input>Just This Document</label></fieldset>
            <button type="reset">clear search</button>
            <button id="submit" type="submit">search</button></form>
      </header>
      <div id="site-content">
         <nav id="site-nav">
            <div class="category closed"><a href="../index.html" title="The root of the site.">CUPTI
                  v12.1</a></div>
            <div class="category"><a href="index.html" title="CUPTI">CUPTI</a></div>
            <ul>
               <li>
                  <div class="section-link"><a href="r_overview.html#r_overview">Overview</a></div>
               </li>
               <li>
                  <div class="section-link"><a href="release_notes.html#release_notes">1.&nbsp;Release Notes</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="release_notes.html#whats-new">1.1.&nbsp;Release Notes</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.1">1.1.1.&nbsp;Updates in CUDA 12.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.0.1">1.1.2.&nbsp;Updates in CUDA 12.0 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_12.0">1.1.3.&nbsp;Updates in CUDA 12.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.8">1.1.4.&nbsp;Updates in CUDA 11.8</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.7.1">1.1.5.&nbsp;Updates in CUDA 11.7 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.7">1.1.6.&nbsp;Updates in CUDA 11.7</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.6.1">1.1.7.&nbsp;Updates in CUDA 11.6 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.6">1.1.8.&nbsp;Updates in CUDA 11.6</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.5.1">1.1.9.&nbsp;Updates in CUDA 11.5 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.5">1.1.10.&nbsp;Updates in CUDA 11.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.4.1">1.1.11.&nbsp;Updates in CUDA 11.4 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.4">1.1.12.&nbsp;Updates in CUDA 11.4</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.3">1.1.13.&nbsp;Updates in CUDA 11.3</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.2">1.1.14.&nbsp;Updates in CUDA 11.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.1">1.1.15.&nbsp;Updates in CUDA 11.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_11.0">1.1.16.&nbsp;Updates in CUDA 11.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.2">1.1.17.&nbsp;Updates in CUDA 10.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1.2">1.1.18.&nbsp;Updates in CUDA 10.1 Update 2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1.1">1.1.19.&nbsp;Updates in CUDA 10.1 Update 1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.1">1.1.20.&nbsp;Updates in CUDA 10.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_10.0">1.1.21.&nbsp;Updates in CUDA 10.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.2">1.1.22.&nbsp;Updates in CUDA 9.2</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.1">1.1.23.&nbsp;Updates in CUDA 9.1</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_9.0">1.1.24.&nbsp;Updates in CUDA 9.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_8.0">1.1.25.&nbsp;Updates in CUDA 8.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_7.5">1.1.26.&nbsp;Updates in CUDA 7.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_7.0">1.1.27.&nbsp;Updates in CUDA 7.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_6.5">1.1.28.&nbsp;Updates in CUDA 6.5</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_6.0">1.1.29.&nbsp;Updates in CUDA 6.0</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#release_notes_5.5">1.1.30.&nbsp;Updates in CUDA 5.5</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="release_notes.html#known_issues">1.2.&nbsp;Known Issues</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#known_issues_profiling">1.2.1.&nbsp;Profiling</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="release_notes.html#known_issues_event_and_metric">1.2.1.1.&nbsp;Event and Metric API</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="release_notes.html#known_issues_profiling_and_perfworks">1.2.1.2.&nbsp;Profiling and Perfworks Metric API</a></div>
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="release_notes.html#support">1.3.&nbsp;Support</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="release_notes.html#platform_support">1.3.1.&nbsp;Platform Support</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="release_notes.html#gpu_support">1.3.2.&nbsp;GPU Support</a></div>
                           </li>
                        </ul>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_main.html#r_main">2.&nbsp;Usage</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_compatibility_requirements">2.1.&nbsp;CUPTI Compatibility and Requirements</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_initialization">2.2.&nbsp;CUPTI Initialization</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_activity">2.3.&nbsp;CUPTI Activity API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_source_correlation">2.3.1.&nbsp;SASS Source Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling">2.3.2.&nbsp;PC Sampling</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_nvlink">2.3.3.&nbsp;NVLink</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_openacc">2.3.4.&nbsp;OpenACC</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_graphs">2.3.5.&nbsp;CUDA Graphs</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_ext_correlation">2.3.6.&nbsp;External Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_dynamic_detach">2.3.7.&nbsp;Dynamic Attach and Detach</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_callback_api">2.4.&nbsp;CUPTI Callback API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_driver_runtime_api_callback">2.4.1.&nbsp;Driver and Runtime API Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_resource_callbacks">2.4.2.&nbsp;Resource Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_synchronization_callbacks">2.4.3.&nbsp;Synchronization Callbacks</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_nvtx_callbacks">2.4.4.&nbsp;NVIDIA Tools Extension Callbacks</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_event_api">2.5.&nbsp;CUPTI Event API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_collecting_kernel_execution_events">2.5.1.&nbsp;Collecting Kernel Execution Events</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_sampling_events">2.5.2.&nbsp;Sampling Events</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_metric_api">2.6.&nbsp;CUPTI Metric API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#metrics-reference">2.6.1.&nbsp;Metrics Reference</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-5x">2.6.1.1.&nbsp;Metrics for Capability 5.x</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-6x">2.6.1.2.&nbsp;Metrics for Capability 6.x</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#metrics-reference-7x">2.6.1.3.&nbsp;Metrics for Capability 7.0</a></div>
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_profiler">2.7.&nbsp;CUPTI Profiling API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_multi_pass_collection">2.7.1.&nbsp;Multi Pass Collection</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_range_profiling">2.7.2.&nbsp;Range Profiling</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_profiler_auto_range">2.7.2.1.&nbsp;Auto Range</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_profiler_user_range">2.7.2.2.&nbsp;User Range</a></div>
                                 </li>
                              </ul>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_profiler_definitions">2.7.3.&nbsp;CUPTI Profiler Definitions</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_profiling_missing_features">2.7.4.&nbsp;Differences from event and metric APIs</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_host_metrics_api">2.8.&nbsp;Perfworks Metric API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_host_derived_metrics_api">2.8.1.&nbsp;Derived metrics</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_host_raw_metrics_api">2.8.2.&nbsp;Raw Metrics</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#metrics_map_table_70">2.8.3.&nbsp;Metrics Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#events_map_table_70">2.8.4.&nbsp;Events Mapping Table</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_profiling_migration">2.9.&nbsp;Migration to the Profiling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_pc_sampling_api">2.10.&nbsp;CUPTI PC Sampling API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_config_attrib">2.10.1.&nbsp;Configuration Attributes</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_stall_reasons_mapping">2.10.2.&nbsp;Stall Reasons Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_struct_mapping">2.10.3.&nbsp;Data Structure Mapping Table</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_flush">2.10.4.&nbsp;Data flushing</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_source_correlation">2.10.5.&nbsp;SASS Source Correlation</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_usage">2.10.6.&nbsp;API Usage</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_pc_sampling_api_limitations">2.10.7.&nbsp;Limitations</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_checkpoint_api">2.11.&nbsp;CUPTI Checkpoint API</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_usage">2.11.1.&nbsp;Usage</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_restrictions">2.11.2.&nbsp;Restrictions</a></div>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_checkpoint_samples">2.11.3.&nbsp;Examples</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_overhead">2.12.&nbsp;CUPTI overhead</a></div>
                        <ul>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_overhead_tracing">2.12.1.&nbsp;Tracing Overhead</a></div>
                              <ul>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_overhead_tracing_execution">2.12.1.1.&nbsp;Execution overhead</a></div>
                                 </li>
                                 <li>
                                    <div class="section-link"><a href="r_main.html#r_overhead_tracing_memory">2.12.1.2.&nbsp;Memory overhead</a></div>
                                 </li>
                              </ul>
                           </li>
                           <li>
                              <div class="section-link"><a href="r_main.html#r_overhead_profiling">2.12.2.&nbsp;Profiling Overhead</a></div>
                           </li>
                        </ul>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_main.html#r_samples">2.13.&nbsp;Samples</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_library_support.html#r_library_support">3.&nbsp;Library Support</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_library_support.html#r_library_support_optix">3.1.&nbsp;OptiX</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="r_special_configurations.html#r_special_configurations">4.&nbsp;Special Configurations</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_mig">4.1.&nbsp;Multi-Instance GPU (MIG)</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_vgpu">4.2.&nbsp;NVIDIA Virtual GPU (vGPU)</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="r_special_configurations.html#r_wsl">4.3.&nbsp;Windows Subsystem for Linux (WSL)</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="modules.html#modules">5.&nbsp;Modules</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__RESULT__API">5.1.&nbsp;CUPTI Result Codes</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__VERSION__API">5.2.&nbsp;CUPTI Version</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__ACTIVITY__API">5.3.&nbsp;CUPTI Activity API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__CALLBACK__API">5.4.&nbsp;CUPTI Callback API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__EVENT__API">5.5.&nbsp;CUPTI Event API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__METRIC__API">5.6.&nbsp;CUPTI Metric API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PROFILER__API">5.7.&nbsp;CUPTI Profiling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__CHECKPOINT__API">5.8.&nbsp;CUPTI Checkpoint API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PCSAMPLING__API">5.9.&nbsp;CUPTI PC Sampling API</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="modules.html#group__CUPTI__PCSAMPLING__UTILITY">5.10.&nbsp;CUPTI PC Sampling Utility API</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="annotated.html#annotated">6.&nbsp;Data Structures</a></div>
                  <ul>
                     <li>
                        <div class="section-link"><a href="annotated.html#structBufferInfo">6.1.&nbsp;BufferInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams">6.2.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams">6.3.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams">6.4.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams">6.5.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams">6.6.&nbsp;CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Activity">6.7.&nbsp;CUpti_Activity</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityAPI">6.8.&nbsp;CUpti_ActivityAPI</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityAutoBoostState">6.9.&nbsp;CUpti_ActivityAutoBoostState</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityBranch">6.10.&nbsp;CUpti_ActivityBranch</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityBranch2">6.11.&nbsp;CUpti_ActivityBranch2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityCdpKernel">6.12.&nbsp;CUpti_ActivityCdpKernel</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityContext">6.13.&nbsp;CUpti_ActivityContext</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityCudaEvent">6.14.&nbsp;CUpti_ActivityCudaEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice">6.15.&nbsp;CUpti_ActivityDevice</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice2">6.16.&nbsp;CUpti_ActivityDevice2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice3">6.17.&nbsp;CUpti_ActivityDevice3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDevice4">6.18.&nbsp;CUpti_ActivityDevice4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityDeviceAttribute">6.19.&nbsp;CUpti_ActivityDeviceAttribute</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEnvironment">6.20.&nbsp;CUpti_ActivityEnvironment</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEvent">6.21.&nbsp;CUpti_ActivityEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityEventInstance">6.22.&nbsp;CUpti_ActivityEventInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityExternalCorrelation">6.23.&nbsp;CUpti_ActivityExternalCorrelation</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityFunction">6.24.&nbsp;CUpti_ActivityFunction</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess">6.25.&nbsp;CUpti_ActivityGlobalAccess</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess2">6.26.&nbsp;CUpti_ActivityGlobalAccess2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGlobalAccess3">6.27.&nbsp;CUpti_ActivityGlobalAccess3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityGraphTrace">6.28.&nbsp;CUpti_ActivityGraphTrace</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousEvent">6.29.&nbsp;CUpti_ActivityInstantaneousEvent</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousEventInstance">6.30.&nbsp;CUpti_ActivityInstantaneousEventInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousMetric">6.31.&nbsp;CUpti_ActivityInstantaneousMetric</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstantaneousMetricInstance">6.32.&nbsp;CUpti_ActivityInstantaneousMetricInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstructionCorrelation">6.33.&nbsp;CUpti_ActivityInstructionCorrelation</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityInstructionExecution">6.34.&nbsp;CUpti_ActivityInstructionExecution</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityJit">6.35.&nbsp;CUpti_ActivityJit</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel">6.36.&nbsp;CUpti_ActivityKernel</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel2">6.37.&nbsp;CUpti_ActivityKernel2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel3">6.38.&nbsp;CUpti_ActivityKernel3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel4">6.39.&nbsp;CUpti_ActivityKernel4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel5">6.40.&nbsp;CUpti_ActivityKernel5</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel6">6.41.&nbsp;CUpti_ActivityKernel6</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel7">6.42.&nbsp;CUpti_ActivityKernel7</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel8">6.43.&nbsp;CUpti_ActivityKernel8</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityKernel9">6.44.&nbsp;CUpti_ActivityKernel9</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarker">6.45.&nbsp;CUpti_ActivityMarker</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarker2">6.46.&nbsp;CUpti_ActivityMarker2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMarkerData">6.47.&nbsp;CUpti_ActivityMarkerData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy">6.48.&nbsp;CUpti_ActivityMemcpy</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy3">6.49.&nbsp;CUpti_ActivityMemcpy3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy4">6.50.&nbsp;CUpti_ActivityMemcpy4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpy5">6.51.&nbsp;CUpti_ActivityMemcpy5</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP">6.52.&nbsp;CUpti_ActivityMemcpyPtoP</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP2">6.53.&nbsp;CUpti_ActivityMemcpyPtoP2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP3">6.54.&nbsp;CUpti_ActivityMemcpyPtoP3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemcpyPtoP4">6.55.&nbsp;CUpti_ActivityMemcpyPtoP4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory">6.56.&nbsp;CUpti_ActivityMemory</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory2">6.57.&nbsp;CUpti_ActivityMemory2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory3">6.58.&nbsp;CUpti_ActivityMemory3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT">6.59.&nbsp;CUpti_ActivityMemory3::PACKED_ALIGNMENT</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemoryPool">6.60.&nbsp;CUpti_ActivityMemoryPool</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemoryPool2">6.61.&nbsp;CUpti_ActivityMemoryPool2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset">6.62.&nbsp;CUpti_ActivityMemset</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset2">6.63.&nbsp;CUpti_ActivityMemset2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset3">6.64.&nbsp;CUpti_ActivityMemset3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMemset4">6.65.&nbsp;CUpti_ActivityMemset4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMetric">6.66.&nbsp;CUpti_ActivityMetric</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityMetricInstance">6.67.&nbsp;CUpti_ActivityMetricInstance</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityModule">6.68.&nbsp;CUpti_ActivityModule</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityName">6.69.&nbsp;CUpti_ActivityName</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink">6.70.&nbsp;CUpti_ActivityNvLink</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink2">6.71.&nbsp;CUpti_ActivityNvLink2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink3">6.72.&nbsp;CUpti_ActivityNvLink3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityNvLink4">6.73.&nbsp;CUpti_ActivityNvLink4</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#unionCUpti__ActivityObjectKindId">6.74.&nbsp;CUpti_ActivityObjectKindId</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAcc">6.75.&nbsp;CUpti_ActivityOpenAcc</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccData">6.76.&nbsp;CUpti_ActivityOpenAccData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccLaunch">6.77.&nbsp;CUpti_ActivityOpenAccLaunch</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenAccOther">6.78.&nbsp;CUpti_ActivityOpenAccOther</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOpenMp">6.79.&nbsp;CUpti_ActivityOpenMp</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityOverhead">6.80.&nbsp;CUpti_ActivityOverhead</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPcie">6.81.&nbsp;CUpti_ActivityPcie</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling">6.82.&nbsp;CUpti_ActivityPCSampling</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling2">6.83.&nbsp;CUpti_ActivityPCSampling2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSampling3">6.84.&nbsp;CUpti_ActivityPCSampling3</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSamplingConfig">6.85.&nbsp;CUpti_ActivityPCSamplingConfig</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPCSamplingRecordInfo">6.86.&nbsp;CUpti_ActivityPCSamplingRecordInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityPreemption">6.87.&nbsp;CUpti_ActivityPreemption</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySharedAccess">6.88.&nbsp;CUpti_ActivitySharedAccess</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySourceLocator">6.89.&nbsp;CUpti_ActivitySourceLocator</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityStream">6.90.&nbsp;CUpti_ActivityStream</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivitySynchronization">6.91.&nbsp;CUpti_ActivitySynchronization</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter">6.92.&nbsp;CUpti_ActivityUnifiedMemoryCounter</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounter2">6.93.&nbsp;CUpti_ActivityUnifiedMemoryCounter2</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ActivityUnifiedMemoryCounterConfig">6.94.&nbsp;CUpti_ActivityUnifiedMemoryCounterConfig</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__CallbackData">6.95.&nbsp;CUpti_CallbackData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__EventGroupSet">6.96.&nbsp;CUpti_EventGroupSet</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__EventGroupSets">6.97.&nbsp;CUpti_EventGroupSets</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GetCubinCrcParams">6.98.&nbsp;CUpti_GetCubinCrcParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GetSassToSourceCorrelationParams">6.99.&nbsp;CUpti_GetSassToSourceCorrelationParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__GraphData">6.100.&nbsp;CUpti_GraphData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#unionCUpti__MetricValue">6.101.&nbsp;CUpti_MetricValue</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ModuleResourceData">6.102.&nbsp;CUpti_ModuleResourceData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__NvtxData">6.103.&nbsp;CUpti_NvtxData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingConfigurationInfo">6.104.&nbsp;CUpti_PCSamplingConfigurationInfo</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingConfigurationInfoParams">6.105.&nbsp;CUpti_PCSamplingConfigurationInfoParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingData">6.106.&nbsp;CUpti_PCSamplingData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingDisableParams">6.107.&nbsp;CUpti_PCSamplingDisableParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingEnableParams">6.108.&nbsp;CUpti_PCSamplingEnableParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetDataParams">6.109.&nbsp;CUpti_PCSamplingGetDataParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetNumStallReasonsParams">6.110.&nbsp;CUpti_PCSamplingGetNumStallReasonsParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingGetStallReasonsParams">6.111.&nbsp;CUpti_PCSamplingGetStallReasonsParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingPCData">6.112.&nbsp;CUpti_PCSamplingPCData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStallReason">6.113.&nbsp;CUpti_PCSamplingStallReason</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStartParams">6.114.&nbsp;CUpti_PCSamplingStartParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__PCSamplingStopParams">6.115.&nbsp;CUpti_PCSamplingStopParams</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__BeginPass__Params">6.116.&nbsp;CUpti_Profiler_BeginPass_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__BeginSession__Params">6.117.&nbsp;CUpti_Profiler_BeginSession_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params">6.118.&nbsp;CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__CalculateSize__Params">6.119.&nbsp;CUpti_Profiler_CounterDataImage_CalculateSize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__Initialize__Params">6.120.&nbsp;CUpti_Profiler_CounterDataImage_Initialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params">6.121.&nbsp;CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__CounterDataImageOptions">6.122.&nbsp;CUpti_Profiler_CounterDataImageOptions</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DeInitialize__Params">6.123.&nbsp;CUpti_Profiler_DeInitialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DeviceSupported__Params">6.124.&nbsp;CUpti_Profiler_DeviceSupported_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__DisableProfiling__Params">6.125.&nbsp;CUpti_Profiler_DisableProfiling_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EnableProfiling__Params">6.126.&nbsp;CUpti_Profiler_EnableProfiling_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EndPass__Params">6.127.&nbsp;CUpti_Profiler_EndPass_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__EndSession__Params">6.128.&nbsp;CUpti_Profiler_EndSession_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__FlushCounterData__Params">6.129.&nbsp;CUpti_Profiler_FlushCounterData_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__GetCounterAvailability__Params">6.130.&nbsp;CUpti_Profiler_GetCounterAvailability_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__Initialize__Params">6.131.&nbsp;CUpti_Profiler_Initialize_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__IsPassCollected__Params">6.132.&nbsp;CUpti_Profiler_IsPassCollected_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__SetConfig__Params">6.133.&nbsp;CUpti_Profiler_SetConfig_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__Profiler__UnsetConfig__Params">6.134.&nbsp;CUpti_Profiler_UnsetConfig_Params</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__ResourceData">6.135.&nbsp;CUpti_ResourceData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structCUpti__SynchronizeData">6.136.&nbsp;CUpti_SynchronizeData</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structHeader">6.137.&nbsp;Header</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint">6.138.&nbsp;NV::Cupti::Checkpoint::CUpti_Checkpoint</a></div>
                     </li>
                     <li>
                        <div class="section-link"><a href="annotated.html#structPcSamplingStallReasons">6.139.&nbsp;PcSamplingStallReasons</a></div>
                     </li>
                  </ul>
               </li>
               <li>
                  <div class="section-link"><a href="functions.html#functions">7.&nbsp;Data Fields</a></div>
               </li>
               <li>
                  <div class="section-link"><a href="notices-header.html#notices-header">Notices</a></div>
                  <ul></ul>
               </li>
            </ul>
         </nav>
         <div id="resize-nav"></div>
         <nav id="search-results">
            <h2>Search Results</h2>
            <ol></ol>
         </nav>
         
         <div id="contents-container">
            <div id="breadcrumbs-container">
               <div id="breadcrumbs"><a href="r_main.html" shape="rect">&lt; Previous</a> | <a href="r_special_configurations.html" shape="rect">Next &gt;</a></div>
               <div id="release-info">CUPTI
                  (<a href="../pdf/Cupti.pdf">PDF</a>)
                  
                  -
                  
                  v12.1
                  (<a href="https://developer.nvidia.com/cuda-toolkit-archive">older</a>)
                  -
                  Last updated Feb 28, 2023
                  -
                  <a href="mailto:<EMAIL>?subject=CUPTI Documentation Feedback: CUPTI">Send Feedback</a></div>
            </div>
            <article id="contents">
               <div class="topic nested1" id="r_library_support"><a name="r_library_support" shape="rect">
                     <!-- --></a><h2 class="topictitle2">3.&nbsp;Library Support</h2>
                  <div class="body refbody">
                     <div class="section">
                        <p class="p">
                           CUPTI can be used to profile CUDA applications, as well as
                           applications that use CUDA via NVIDIA or third-party libraries. For most such libraries,
                           the behavior is expected to be identical to applications using CUDA directly. However,
                           for certain libraries, CUPTI has certain restrictions, or alternate behavior.
                        </p>
                     </div>
                  </div>
                  <div class="topic reference cuda_reference nested1" id="r_library_support_optix"><a name="r_library_support_optix" shape="rect">
                        <!-- --></a><h3 class="topictitle3">3.1.&nbsp;OptiX</h3>
                     <div class="body refbody">
                        <div class="section">
                           <p class="p">
                              CUPTI supports profiling of OptiX applications, but with certain restrictions.
                              
                           </p>
                           <p class="p"><strong class="ph b"> Tracing</strong></p>
                           <ul class="ul">
                              <li class="li"><strong class="ph b">Internal Kernels</strong><p class="p"> Kernels launched by OptiX that contain no user-defined code are given the generic
                                    name <dfn class="term">NVIDIA internal</dfn>. CUPTI provides the tracing information for these kernels.
                                 </p>
                              </li>
                              <li class="li"><strong class="ph b">User Kernels</strong><p class="p"> Kernels launched by OptiX can contain user-defined code. OptiX identifies these
                                    kernels with a custom name. This name starts with
                                    <dfn class="term">raygen__</dfn> (for "ray generation"). These kernels can be traced.
                                 </p>
                              </li>
                           </ul>
                           <p class="p"><strong class="ph b"> Profiling</strong></p>
                           <p class="p"> CUPTI can profile both internal and user kernels using the Profiling APIs.
                              In the auto range mode, range names will be numeric values starting from 0 
                              to total number of kernels including internal and user defined kernels or 
                              maximum number of range set while calling set config API, whichever is minimum.
                              
                           </p>
                           <p class="p"> It is suggested to create the profiling session and enable the profiling at 
                              resource allocation time (e.g. context creation) and disable the profiling at 
                              the context destruction time.
                              
                           </p>
                           <p class="p"><strong class="ph b"> Limitations</strong></p>
                           <ul class="ul">
                              <li class="li">
                                 <p class="p"> CUPTI doesn't issue any driver or runtime API callback for user kernels.</p>
                              </li>
                              <li class="li">
                                 <p class="p">Event, Metric and PC sampling APIs are not supported for OptiX applications.</p>
                              </li>
                           </ul>
                        </div>
                     </div>
                  </div>
               </div>
               
               <hr id="contents-end"></hr>
               
            </article>
         </div>
      </div>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/formatting/common.min.js"></script>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-write.js"></script>
      <script language="JavaScript" type="text/javascript" charset="utf-8" src="../common/scripts/google-analytics/google-analytics-tracker.js"></script>
      <script type="text/javascript">_satellite.pageBottom();</script>
      <script type="text/javascript">var switchTo5x=true;</script><script type="text/javascript">stLight.options({publisher: "998dc202-a267-4d8e-bce9-14debadb8d92", doNotHash: false, doNotCopy: false, hashAddressBar: false});</script></body>
</html>