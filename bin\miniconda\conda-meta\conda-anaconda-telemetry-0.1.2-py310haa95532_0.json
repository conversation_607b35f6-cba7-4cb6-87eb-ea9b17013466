{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["conda >=24.11", "python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-anaconda-telemetry-0.1.2-py310haa95532_0", "files": ["Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/INSTALLER", "Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/METADATA", "Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/RECORD", "Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/REQUESTED", "Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/WHEEL", "Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/direct_url.json", "Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/entry_points.txt", "Lib/site-packages/conda_anaconda_telemetry-0.1.2.dist-info/licenses/LICENSE", "Lib/site-packages/conda_anaconda_telemetry/__init__.py", "Lib/site-packages/conda_anaconda_telemetry/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/conda_anaconda_telemetry/__pycache__/_version.cpython-310.pyc", "Lib/site-packages/conda_anaconda_telemetry/__pycache__/hooks.cpython-310.pyc", "Lib/site-packages/conda_anaconda_telemetry/_version.py", "Lib/site-packages/conda_anaconda_telemetry/hooks.py", "Lib/site-packages/conda_anaconda_telemetry/py.typed"], "fn": "conda-anaconda-telemetry-0.1.2-py310haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-anaconda-telemetry-0.1.2-py310haa95532_0", "type": 1}, "md5": "6ac41cacd34f77ec401291330c9c19b8", "name": "conda-anaconda-telemetry", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-anaconda-telemetry-0.1.2-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::conda-anaconda-telemetry==0.1.2=py310haa95532_0[md5=6ac41cacd34f77ec401291330c9c19b8]", "sha256": "447db7275b5ff49a96335dd624e5eb128c0df81121d9b2ec882a580fa7e24495", "size": 14109, "subdir": "win-64", "timestamp": 1736524910000, "url": "https://repo.anaconda.com/pkgs/main/win-64/conda-anaconda-telemetry-0.1.2-py310haa95532_0.conda", "version": "0.1.2"}