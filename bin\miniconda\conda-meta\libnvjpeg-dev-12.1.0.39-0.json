{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["libnvjpeg >=12.1.0.39"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjpeg-dev-12.1.0.39-0", "features": "", "files": ["bin/nvjpeg64_12.dll", "include/nvjpeg.h"], "fn": "libnvjpeg-dev-12.1.0.39-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjpeg-dev-12.1.0.39-0", "type": 1}, "md5": "eb075675050cbd9e46e88bb8e439cbb9", "name": "libnvjpeg-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjpeg-dev-12.1.0.39-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/nvjpeg64_12.dll", "path_type": "hardlink", "sha256": "b0b758b5ded05327647fa117957295aaf59fcf6421ab231f7b83ed8834d92702", "sha256_in_prefix": "b0b758b5ded05327647fa117957295aaf59fcf6421ab231f7b83ed8834d92702", "size_in_bytes": 4131328}, {"_path": "include/nvjpeg.h", "path_type": "hardlink", "sha256": "62241dee636b08832e032e94c0de8d8c7006109b6fc503c2ca8fcc0d84a223e5", "sha256_in_prefix": "62241dee636b08832e032e94c0de8d8c7006109b6fc503c2ca8fcc0d84a223e5", "size_in_bytes": 34421}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 2125490, "subdir": "win-64", "timestamp": 1674623637000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libnvjpeg-dev-12.1.0.39-0.tar.bz2", "version": "12.1.0.39"}