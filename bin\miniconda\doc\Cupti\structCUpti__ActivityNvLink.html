<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityNvLink Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityNvLink Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityNvLink" -->NVLink information. (deprecated in CUDA 9.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#76f5ab2981dde781aee62ab3eceaa95e">bandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#a51fecf57e08c0ea0f6894dcdf921a3f">flag</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#2da9e328573fde3d4f0940f92ff64f9f">idDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#25ba8cb612e81c0dfa22d230f2cd496e">idDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#510dffdaa89f771d7923ab55e732eb1b">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#f5dca02236bf7fbe1369d820899a5d75">nvlinkVersion</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#6f5de984955775d5bb188e09e698784c">physicalNvLinkCount</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#896ce20863bca39ce8856cab1d6404f0">portDev0</a> [4]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#e90506328344c02dfa1ff6eb982285f4">portDev1</a> [4]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#1e27bdb7050da1582f23c42857a9ded1">typeDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#f5b62aa4efc633a0350a45305617deb7">typeDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#991ea08473f9aaa44b0358b43182858b">domainId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink.html#6c2499dad4531ca2b0daaa914b509afa">index</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure gives capabilities of each logical NVLink connection between two devices, gpu&lt;-&gt;gpu or gpu&lt;-&gt;CPU which can be used to understand the topology. NVLink information are now reported using the <a class="el" href="structCUpti__ActivityNvLink2.html" title="NVLink information. (deprecated in CUDA 10.0).">CUpti_ActivityNvLink2</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="76f5ab2981dde781aee62ab3eceaa95e"></a><!-- doxytag: member="CUpti_ActivityNvLink::bandwidth" ref="76f5ab2981dde781aee62ab3eceaa95e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityNvLink.html#76f5ab2981dde781aee62ab3eceaa95e">CUpti_ActivityNvLink::bandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Banwidth of NVLink in kbytes/sec 
</div>
</div><p>
<a class="anchor" name="991ea08473f9aaa44b0358b43182858b"></a><!-- doxytag: member="CUpti_ActivityNvLink::domainId" ref="991ea08473f9aaa44b0358b43182858b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink.html#991ea08473f9aaa44b0358b43182858b">CUpti_ActivityNvLink::domainId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Domain ID of NPU. On Linux, this can be queried using lspci. 
</div>
</div><p>
<a class="anchor" name="a51fecf57e08c0ea0f6894dcdf921a3f"></a><!-- doxytag: member="CUpti_ActivityNvLink::flag" ref="a51fecf57e08c0ea0f6894dcdf921a3f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink.html#a51fecf57e08c0ea0f6894dcdf921a3f">CUpti_ActivityNvLink::flag</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flag gives capabilities of the link <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g819d686d23d2a3dfed9cc1d3c4ecb162" title="Link flags.">CUpti_LinkFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="2da9e328573fde3d4f0940f92ff64f9f"></a><!-- doxytag: member="CUpti_ActivityNvLink::idDev0" ref="2da9e328573fde3d4f0940f92ff64f9f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink.html#2da9e328573fde3d4f0940f92ff64f9f">CUpti_ActivityNvLink::idDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev0 is CUPTI_DEV_TYPE_GPU, UUID for device 0. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev0 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="25ba8cb612e81c0dfa22d230f2cd496e"></a><!-- doxytag: member="CUpti_ActivityNvLink::idDev1" ref="25ba8cb612e81c0dfa22d230f2cd496e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink.html#25ba8cb612e81c0dfa22d230f2cd496e">CUpti_ActivityNvLink::idDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev1 is CUPTI_DEV_TYPE_GPU, UUID for device 1. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev1 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="6c2499dad4531ca2b0daaa914b509afa"></a><!-- doxytag: member="CUpti_ActivityNvLink::index" ref="6c2499dad4531ca2b0daaa914b509afa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink.html#6c2499dad4531ca2b0daaa914b509afa">CUpti_ActivityNvLink::index</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Index of the NPU. First index will always be zero. 
</div>
</div><p>
<a class="anchor" name="510dffdaa89f771d7923ab55e732eb1b"></a><!-- doxytag: member="CUpti_ActivityNvLink::kind" ref="510dffdaa89f771d7923ab55e732eb1b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityNvLink.html#510dffdaa89f771d7923ab55e732eb1b">CUpti_ActivityNvLink::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_NVLINK. 
</div>
</div><p>
<a class="anchor" name="f5dca02236bf7fbe1369d820899a5d75"></a><!-- doxytag: member="CUpti_ActivityNvLink::nvlinkVersion" ref="f5dca02236bf7fbe1369d820899a5d75" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink.html#f5dca02236bf7fbe1369d820899a5d75">CUpti_ActivityNvLink::nvlinkVersion</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
NVLink version. 
</div>
</div><p>
<a class="anchor" name="6f5de984955775d5bb188e09e698784c"></a><!-- doxytag: member="CUpti_ActivityNvLink::physicalNvLinkCount" ref="6f5de984955775d5bb188e09e698784c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink.html#6f5de984955775d5bb188e09e698784c">CUpti_ActivityNvLink::physicalNvLinkCount</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of physical NVLinks present between two devices. 
</div>
</div><p>
<a class="anchor" name="896ce20863bca39ce8856cab1d6404f0"></a><!-- doxytag: member="CUpti_ActivityNvLink::portDev0" ref="896ce20863bca39ce8856cab1d6404f0" args="[4]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink.html#896ce20863bca39ce8856cab1d6404f0">CUpti_ActivityNvLink::portDev0</a>[4]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 4 NVLinks connected to device 0. If typeDev0 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="e90506328344c02dfa1ff6eb982285f4"></a><!-- doxytag: member="CUpti_ActivityNvLink::portDev1" ref="e90506328344c02dfa1ff6eb982285f4" args="[4]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink.html#e90506328344c02dfa1ff6eb982285f4">CUpti_ActivityNvLink::portDev1</a>[4]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 4 NVLinks connected to device 1. If typeDev1 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="1e27bdb7050da1582f23c42857a9ded1"></a><!-- doxytag: member="CUpti_ActivityNvLink::typeDev0" ref="1e27bdb7050da1582f23c42857a9ded1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink.html#1e27bdb7050da1582f23c42857a9ded1">CUpti_ActivityNvLink::typeDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 0 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
<a class="anchor" name="f5b62aa4efc633a0350a45305617deb7"></a><!-- doxytag: member="CUpti_ActivityNvLink::typeDev1" ref="f5b62aa4efc633a0350a45305617deb7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink.html#f5b62aa4efc633a0350a45305617deb7">CUpti_ActivityNvLink::typeDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 1 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
