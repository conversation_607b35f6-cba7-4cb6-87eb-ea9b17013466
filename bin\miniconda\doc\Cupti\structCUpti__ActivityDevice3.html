<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityDevice3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityDevice3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityDevice3" -->The activity record for a device. (CUDA 7.0 onwards).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#0aa6c5b25d6e6550e36b171c477cea4a">computeCapabilityMajor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#3a1163d2ebf2baec2f3eb4b9dad22c77">computeCapabilityMinor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#18145ed01cdf41a71476fc5acd582964">constantMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#1c32001a11787b74e8e5e05e785de766">coreClockRate</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#74e01758edc213936a49b950a7def423">eccEnabled</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#54677f82410f5e3cb57abbf21397e7c9">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#47dd2547285f34fa3e9239e4e64b1597">globalMemoryBandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#9495de17626f8e0192c0cff98ab9f1c2">globalMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#c204a14b572254d87d5af3331e9c1ea6">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#6f1f34728af000ea36a1e12724bde3e6">isCudaVisible</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#********************************">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#b42315071cf0a14b630ac360597cc5d2">l2CacheSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#44a151299c085d919966add3a6f47f87">maxBlockDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#35e57e444c9f87c43d5d6413d80dd2c7">maxBlockDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#3a76bb7e2ee4db21487dbba23755f789">maxBlockDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#0960c954286cab808c0b32c7c0761185">maxBlocksPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#a06f5afaad0337b0037564698895d79c">maxGridDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#302b76a3ab7ef5a1e68517a44eb41329">maxGridDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#770c47aa48a2e4b6e0fc387fbfcdec05">maxGridDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#3f1eed403962c75d88c4bcdbe53910bf">maxIPC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#12e3beb24e73e88318ef6d962c8a708b">maxRegistersPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#a9c52b68bf83203dcbe1ceca58748e40">maxRegistersPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#bbc309912776c74c5783d97a39ca9ec2">maxSharedMemoryPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#f39a9f5ed5cd17ecd60df8d57f2e3e89">maxSharedMemoryPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#a48515dc3e6d9eabb912c489aa4bd101">maxThreadsPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#6124d277c15d9ee2a297f0d97c1b4ca8">maxWarpsPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#bbd6a67338f1a93a3977b0133ba03d7f">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#e3b6102eba7bedad732e2ae9466d69de">numMemcpyEngines</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#1b8d2d2350ef003289b157051f5055a5">numMultiprocessors</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#3b1098a032c9df1607810bfc62d5d90b">numThreadsPerWarp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#3de2e15af1811460186e73e321096ec7">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUuuid&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice3.html#f6885b2af0004caee2e7ccfd2cc1a14d">uuid</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents information about a GPU device (CUPTI_ACTIVITY_KIND_DEVICE). Device activity is now reported using the <a class="el" href="structCUpti__ActivityDevice4.html" title="The activity record for a device. (CUDA 11.6 onwards).">CUpti_ActivityDevice4</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="0aa6c5b25d6e6550e36b171c477cea4a"></a><!-- doxytag: member="CUpti_ActivityDevice3::computeCapabilityMajor" ref="0aa6c5b25d6e6550e36b171c477cea4a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#0aa6c5b25d6e6550e36b171c477cea4a">CUpti_ActivityDevice3::computeCapabilityMajor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, major number. 
</div>
</div><p>
<a class="anchor" name="3a1163d2ebf2baec2f3eb4b9dad22c77"></a><!-- doxytag: member="CUpti_ActivityDevice3::computeCapabilityMinor" ref="3a1163d2ebf2baec2f3eb4b9dad22c77" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#3a1163d2ebf2baec2f3eb4b9dad22c77">CUpti_ActivityDevice3::computeCapabilityMinor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, minor number. 
</div>
</div><p>
<a class="anchor" name="18145ed01cdf41a71476fc5acd582964"></a><!-- doxytag: member="CUpti_ActivityDevice3::constantMemorySize" ref="18145ed01cdf41a71476fc5acd582964" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#18145ed01cdf41a71476fc5acd582964">CUpti_ActivityDevice3::constantMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of constant memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="1c32001a11787b74e8e5e05e785de766"></a><!-- doxytag: member="CUpti_ActivityDevice3::coreClockRate" ref="1c32001a11787b74e8e5e05e785de766" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#1c32001a11787b74e8e5e05e785de766">CUpti_ActivityDevice3::coreClockRate</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The core clock rate of the device, in kHz. 
</div>
</div><p>
<a class="anchor" name="74e01758edc213936a49b950a7def423"></a><!-- doxytag: member="CUpti_ActivityDevice3::eccEnabled" ref="74e01758edc213936a49b950a7def423" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#74e01758edc213936a49b950a7def423">CUpti_ActivityDevice3::eccEnabled</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ECC enabled flag for device 
</div>
</div><p>
<a class="anchor" name="54677f82410f5e3cb57abbf21397e7c9"></a><!-- doxytag: member="CUpti_ActivityDevice3::flags" ref="54677f82410f5e3cb57abbf21397e7c9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityDevice3.html#54677f82410f5e3cb57abbf21397e7c9">CUpti_ActivityDevice3::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the device. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="47dd2547285f34fa3e9239e4e64b1597"></a><!-- doxytag: member="CUpti_ActivityDevice3::globalMemoryBandwidth" ref="47dd2547285f34fa3e9239e4e64b1597" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice3.html#47dd2547285f34fa3e9239e4e64b1597">CUpti_ActivityDevice3::globalMemoryBandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The global memory bandwidth available on the device, in kBytes/sec. 
</div>
</div><p>
<a class="anchor" name="9495de17626f8e0192c0cff98ab9f1c2"></a><!-- doxytag: member="CUpti_ActivityDevice3::globalMemorySize" ref="9495de17626f8e0192c0cff98ab9f1c2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice3.html#9495de17626f8e0192c0cff98ab9f1c2">CUpti_ActivityDevice3::globalMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of global memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="c204a14b572254d87d5af3331e9c1ea6"></a><!-- doxytag: member="CUpti_ActivityDevice3::id" ref="c204a14b572254d87d5af3331e9c1ea6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#c204a14b572254d87d5af3331e9c1ea6">CUpti_ActivityDevice3::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device ID. 
</div>
</div><p>
<a class="anchor" name="6f1f34728af000ea36a1e12724bde3e6"></a><!-- doxytag: member="CUpti_ActivityDevice3::isCudaVisible" ref="6f1f34728af000ea36a1e12724bde3e6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityDevice3.html#6f1f34728af000ea36a1e12724bde3e6">CUpti_ActivityDevice3::isCudaVisible</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flag to indicate whether the device is visible to CUDA. Users can set the device visibility using CUDA_VISIBLE_DEVICES environment 
</div>
</div><p>
<a class="anchor" name="********************************"></a><!-- doxytag: member="CUpti_ActivityDevice3::kind" ref="********************************" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityDevice3.html#********************************">CUpti_ActivityDevice3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_DEVICE. 
</div>
</div><p>
<a class="anchor" name="b42315071cf0a14b630ac360597cc5d2"></a><!-- doxytag: member="CUpti_ActivityDevice3::l2CacheSize" ref="b42315071cf0a14b630ac360597cc5d2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#b42315071cf0a14b630ac360597cc5d2">CUpti_ActivityDevice3::l2CacheSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the L2 cache on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="44a151299c085d919966add3a6f47f87"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxBlockDimX" ref="44a151299c085d919966add3a6f47f87" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#44a151299c085d919966add3a6f47f87">CUpti_ActivityDevice3::maxBlockDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a block. 
</div>
</div><p>
<a class="anchor" name="35e57e444c9f87c43d5d6413d80dd2c7"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxBlockDimY" ref="35e57e444c9f87c43d5d6413d80dd2c7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#35e57e444c9f87c43d5d6413d80dd2c7">CUpti_ActivityDevice3::maxBlockDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a block. 
</div>
</div><p>
<a class="anchor" name="3a76bb7e2ee4db21487dbba23755f789"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxBlockDimZ" ref="3a76bb7e2ee4db21487dbba23755f789" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#3a76bb7e2ee4db21487dbba23755f789">CUpti_ActivityDevice3::maxBlockDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a block. 
</div>
</div><p>
<a class="anchor" name="0960c954286cab808c0b32c7c0761185"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxBlocksPerMultiprocessor" ref="0960c954286cab808c0b32c7c0761185" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#0960c954286cab808c0b32c7c0761185">CUpti_ActivityDevice3::maxBlocksPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of blocks that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="a06f5afaad0337b0037564698895d79c"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxGridDimX" ref="a06f5afaad0337b0037564698895d79c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#a06f5afaad0337b0037564698895d79c">CUpti_ActivityDevice3::maxGridDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="302b76a3ab7ef5a1e68517a44eb41329"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxGridDimY" ref="302b76a3ab7ef5a1e68517a44eb41329" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#302b76a3ab7ef5a1e68517a44eb41329">CUpti_ActivityDevice3::maxGridDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="770c47aa48a2e4b6e0fc387fbfcdec05"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxGridDimZ" ref="770c47aa48a2e4b6e0fc387fbfcdec05" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#770c47aa48a2e4b6e0fc387fbfcdec05">CUpti_ActivityDevice3::maxGridDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="3f1eed403962c75d88c4bcdbe53910bf"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxIPC" ref="3f1eed403962c75d88c4bcdbe53910bf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#3f1eed403962c75d88c4bcdbe53910bf">CUpti_ActivityDevice3::maxIPC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The maximum "instructions per cycle" possible on each device multiprocessor. 
</div>
</div><p>
<a class="anchor" name="12e3beb24e73e88318ef6d962c8a708b"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxRegistersPerBlock" ref="12e3beb24e73e88318ef6d962c8a708b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#12e3beb24e73e88318ef6d962c8a708b">CUpti_ActivityDevice3::maxRegistersPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of registers that can be allocated to a block. 
</div>
</div><p>
<a class="anchor" name="a9c52b68bf83203dcbe1ceca58748e40"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxRegistersPerMultiprocessor" ref="a9c52b68bf83203dcbe1ceca58748e40" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#a9c52b68bf83203dcbe1ceca58748e40">CUpti_ActivityDevice3::maxRegistersPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of 32-bit registers available per multiprocessor. 
</div>
</div><p>
<a class="anchor" name="bbc309912776c74c5783d97a39ca9ec2"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxSharedMemoryPerBlock" ref="bbc309912776c74c5783d97a39ca9ec2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#bbc309912776c74c5783d97a39ca9ec2">CUpti_ActivityDevice3::maxSharedMemoryPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum amount of shared memory that can be assigned to a block, in bytes. 
</div>
</div><p>
<a class="anchor" name="f39a9f5ed5cd17ecd60df8d57f2e3e89"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxSharedMemoryPerMultiprocessor" ref="f39a9f5ed5cd17ecd60df8d57f2e3e89" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#f39a9f5ed5cd17ecd60df8d57f2e3e89">CUpti_ActivityDevice3::maxSharedMemoryPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum amount of shared memory available per multiprocessor, in bytes. 
</div>
</div><p>
<a class="anchor" name="a48515dc3e6d9eabb912c489aa4bd101"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxThreadsPerBlock" ref="a48515dc3e6d9eabb912c489aa4bd101" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#a48515dc3e6d9eabb912c489aa4bd101">CUpti_ActivityDevice3::maxThreadsPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of threads allowed in a block. 
</div>
</div><p>
<a class="anchor" name="6124d277c15d9ee2a297f0d97c1b4ca8"></a><!-- doxytag: member="CUpti_ActivityDevice3::maxWarpsPerMultiprocessor" ref="6124d277c15d9ee2a297f0d97c1b4ca8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#6124d277c15d9ee2a297f0d97c1b4ca8">CUpti_ActivityDevice3::maxWarpsPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of warps that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="bbd6a67338f1a93a3977b0133ba03d7f"></a><!-- doxytag: member="CUpti_ActivityDevice3::name" ref="bbd6a67338f1a93a3977b0133ba03d7f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityDevice3.html#bbd6a67338f1a93a3977b0133ba03d7f">CUpti_ActivityDevice3::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device name. This name is shared across all activity records representing instances of the device, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="e3b6102eba7bedad732e2ae9466d69de"></a><!-- doxytag: member="CUpti_ActivityDevice3::numMemcpyEngines" ref="e3b6102eba7bedad732e2ae9466d69de" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#e3b6102eba7bedad732e2ae9466d69de">CUpti_ActivityDevice3::numMemcpyEngines</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of memory copy engines on the device. 
</div>
</div><p>
<a class="anchor" name="1b8d2d2350ef003289b157051f5055a5"></a><!-- doxytag: member="CUpti_ActivityDevice3::numMultiprocessors" ref="1b8d2d2350ef003289b157051f5055a5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#1b8d2d2350ef003289b157051f5055a5">CUpti_ActivityDevice3::numMultiprocessors</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of multiprocessors on the device. 
</div>
</div><p>
<a class="anchor" name="3b1098a032c9df1607810bfc62d5d90b"></a><!-- doxytag: member="CUpti_ActivityDevice3::numThreadsPerWarp" ref="3b1098a032c9df1607810bfc62d5d90b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#3b1098a032c9df1607810bfc62d5d90b">CUpti_ActivityDevice3::numThreadsPerWarp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of threads per warp on the device. 
</div>
</div><p>
<a class="anchor" name="3de2e15af1811460186e73e321096ec7"></a><!-- doxytag: member="CUpti_ActivityDevice3::pad" ref="3de2e15af1811460186e73e321096ec7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice3.html#3de2e15af1811460186e73e321096ec7">CUpti_ActivityDevice3::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="f6885b2af0004caee2e7ccfd2cc1a14d"></a><!-- doxytag: member="CUpti_ActivityDevice3::uuid" ref="f6885b2af0004caee2e7ccfd2cc1a14d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUuuid <a class="el" href="structCUpti__ActivityDevice3.html#f6885b2af0004caee2e7ccfd2cc1a14d">CUpti_ActivityDevice3::uuid</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device UUID. This value is the globally unique immutable alphanumeric identifier of the device. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
