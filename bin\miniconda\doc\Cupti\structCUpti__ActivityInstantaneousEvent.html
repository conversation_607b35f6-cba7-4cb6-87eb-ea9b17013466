<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityInstantaneousEvent Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityInstantaneousEvent Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityInstantaneousEvent" -->The activity record for an instantaneous CUPTI event.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEvent.html#b1f58744be5677c488bd287aa4bc2214">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEvent.html#21f481535903a23ad916969a10639384">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEvent.html#353ba475de7addf1dec265276dac40fa">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEvent.html#4965924e17cec028c3b240b76c0770b1">reserved</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEvent.html#b0f959ed8195a1437c959a0d932f5bfa">timestamp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousEvent.html#34cbc3149c59cb71fd9d6687efc87e34">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a CUPTI event value (CUPTI_ACTIVITY_KIND_EVENT) sampled at a particular instant. This activity record kind is not produced by the activity API but is included for completeness and ease-of-use. Profiler frameworks built on top of CUPTI that collect event data at a particular time may choose to use this type to store the collected event data. <hr><h2>Field Documentation</h2>
<a class="anchor" name="b1f58744be5677c488bd287aa4bc2214"></a><!-- doxytag: member="CUpti_ActivityInstantaneousEvent::deviceId" ref="b1f58744be5677c488bd287aa4bc2214" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#b1f58744be5677c488bd287aa4bc2214">CUpti_ActivityInstantaneousEvent::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device id 
</div>
</div><p>
<a class="anchor" name="21f481535903a23ad916969a10639384"></a><!-- doxytag: member="CUpti_ActivityInstantaneousEvent::id" ref="21f481535903a23ad916969a10639384" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__EVENT__API.html#g6ce7370be9ed31ce6f2d475de39045df">CUpti_EventID</a> <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#21f481535903a23ad916969a10639384">CUpti_ActivityInstantaneousEvent::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The event ID. 
</div>
</div><p>
<a class="anchor" name="353ba475de7addf1dec265276dac40fa"></a><!-- doxytag: member="CUpti_ActivityInstantaneousEvent::kind" ref="353ba475de7addf1dec265276dac40fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#353ba475de7addf1dec265276dac40fa">CUpti_ActivityInstantaneousEvent::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_INSTANTANEOUS_EVENT. 
</div>
</div><p>
<a class="anchor" name="4965924e17cec028c3b240b76c0770b1"></a><!-- doxytag: member="CUpti_ActivityInstantaneousEvent::reserved" ref="4965924e17cec028c3b240b76c0770b1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#4965924e17cec028c3b240b76c0770b1">CUpti_ActivityInstantaneousEvent::reserved</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. reserved for internal use 
</div>
</div><p>
<a class="anchor" name="b0f959ed8195a1437c959a0d932f5bfa"></a><!-- doxytag: member="CUpti_ActivityInstantaneousEvent::timestamp" ref="b0f959ed8195a1437c959a0d932f5bfa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#b0f959ed8195a1437c959a0d932f5bfa">CUpti_ActivityInstantaneousEvent::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp at which event is sampled 
</div>
</div><p>
<a class="anchor" name="34cbc3149c59cb71fd9d6687efc87e34"></a><!-- doxytag: member="CUpti_ActivityInstantaneousEvent::value" ref="34cbc3149c59cb71fd9d6687efc87e34" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#34cbc3149c59cb71fd9d6687efc87e34">CUpti_ActivityInstantaneousEvent::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The event value. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
