// -*- C++ -*-
//===------------------------ string_view ---------------------------------===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _LIBCUDACXX_STRING_VIEW
#define _LIBCUDACXX_STRING_VIEW

/*
string_view synopsis

namespace std {

    // 7.2, Class template basic_string_view
    template<class charT, class traits = char_traits<charT>>
        class basic_string_view;

    // 7.9, basic_string_view non-member comparison functions
    template<class charT, class traits>
    constexpr bool operator==(basic_string_view<charT, traits> x,
                              basic_string_view<charT, traits> y) noexcept;
    template<class charT, class traits>
    constexpr bool operator!=(basic_string_view<charT, traits> x,
                              basic_string_view<charT, traits> y) noexcept;
    template<class charT, class traits>
    constexpr bool operator< (basic_string_view<charT, traits> x,
                                 basic_string_view<charT, traits> y) noexcept;
    template<class charT, class traits>
    constexpr bool operator> (basic_string_view<charT, traits> x,
                              basic_string_view<charT, traits> y) noexcept;
    template<class charT, class traits>
    constexpr bool operator<=(basic_string_view<charT, traits> x,
                                 basic_string_view<charT, traits> y) noexcept;
    template<class charT, class traits>
    constexpr bool operator>=(basic_string_view<charT, traits> x,
                              basic_string_view<charT, traits> y) noexcept;
    // see below, sufficient additional overloads of comparison functions

    // 7.10, Inserters and extractors
    template<class charT, class traits>
      basic_ostream<charT, traits>&
        operator<<(basic_ostream<charT, traits>& os,
                   basic_string_view<charT, traits> str);

    // basic_string_view typedef names
    typedef basic_string_view<char> string_view;
    typedef basic_string_view<char16_t> u16string_view;
    typedef basic_string_view<char32_t> u32string_view;
    typedef basic_string_view<wchar_t> wstring_view;

    template<class charT, class traits = char_traits<charT>>
    class basic_string_view {
      public:
      // types
      typedef traits traits_type;
      typedef charT value_type;
      typedef charT* pointer;
      typedef const charT* const_pointer;
      typedef charT& reference;
      typedef const charT& const_reference;
      typedef implementation-defined const_iterator;
      typedef const_iterator iterator;
      typedef reverse_iterator<const_iterator> const_reverse_iterator;
      typedef const_reverse_iterator reverse_iterator;
      typedef size_t size_type;
      typedef ptrdiff_t difference_type;
      static constexpr size_type npos = size_type(-1);

      // 7.3, basic_string_view constructors and assignment operators
      constexpr basic_string_view() noexcept;
      constexpr basic_string_view(const basic_string_view&) noexcept = default;
      basic_string_view& operator=(const basic_string_view&) noexcept = default;
      template<class Allocator>
      constexpr basic_string_view(const charT* str);
      constexpr basic_string_view(const charT* str, size_type len);

      // 7.4, basic_string_view iterator support
      constexpr const_iterator begin() const noexcept;
      constexpr const_iterator end() const noexcept;
      constexpr const_iterator cbegin() const noexcept;
      constexpr const_iterator cend() const noexcept;
      const_reverse_iterator rbegin() const noexcept;
      const_reverse_iterator rend() const noexcept;
      const_reverse_iterator crbegin() const noexcept;
      const_reverse_iterator crend() const noexcept;

      // 7.5, basic_string_view capacity
      constexpr size_type size() const noexcept;
      constexpr size_type length() const noexcept;
      constexpr size_type max_size() const noexcept;
      constexpr bool empty() const noexcept;

      // 7.6, basic_string_view element access
      constexpr const_reference operator[](size_type pos) const;
      constexpr const_reference at(size_type pos) const;
      constexpr const_reference front() const;
      constexpr const_reference back() const;
      constexpr const_pointer data() const noexcept;

      // 7.7, basic_string_view modifiers
      constexpr void remove_prefix(size_type n);
      constexpr void remove_suffix(size_type n);
      constexpr void swap(basic_string_view& s) noexcept;

      size_type copy(charT* s, size_type n, size_type pos = 0) const;

      constexpr basic_string_view substr(size_type pos = 0, size_type n = npos) const;
      constexpr int compare(basic_string_view s) const noexcept;
      constexpr int compare(size_type pos1, size_type n1, basic_string_view s) const;
      constexpr int compare(size_type pos1, size_type n1,
                            basic_string_view s, size_type pos2, size_type n2) const;
      constexpr int compare(const charT* s) const;
      constexpr int compare(size_type pos1, size_type n1, const charT* s) const;
      constexpr int compare(size_type pos1, size_type n1,
                            const charT* s, size_type n2) const;
      constexpr size_type find(basic_string_view s, size_type pos = 0) const noexcept;
      constexpr size_type find(charT c, size_type pos = 0) const noexcept;
      constexpr size_type find(const charT* s, size_type pos, size_type n) const;
      constexpr size_type find(const charT* s, size_type pos = 0) const;
      constexpr size_type rfind(basic_string_view s, size_type pos = npos) const noexcept;
      constexpr size_type rfind(charT c, size_type pos = npos) const noexcept;
      constexpr size_type rfind(const charT* s, size_type pos, size_type n) const;
      constexpr size_type rfind(const charT* s, size_type pos = npos) const;
      constexpr size_type find_first_of(basic_string_view s, size_type pos = 0) const noexcept;
      constexpr size_type find_first_of(charT c, size_type pos = 0) const noexcept;
      constexpr size_type find_first_of(const charT* s, size_type pos, size_type n) const;
      constexpr size_type find_first_of(const charT* s, size_type pos = 0) const;
      constexpr size_type find_last_of(basic_string_view s, size_type pos = npos) const noexcept;
      constexpr size_type find_last_of(charT c, size_type pos = npos) const noexcept;
      constexpr size_type find_last_of(const charT* s, size_type pos, size_type n) const;
      constexpr size_type find_last_of(const charT* s, size_type pos = npos) const;
      constexpr size_type find_first_not_of(basic_string_view s, size_type pos = 0) const noexcept;
      constexpr size_type find_first_not_of(charT c, size_type pos = 0) const noexcept;
      constexpr size_type find_first_not_of(const charT* s, size_type pos, size_type n) const;
      constexpr size_type find_first_not_of(const charT* s, size_type pos = 0) const;
      constexpr size_type find_last_not_of(basic_string_view s, size_type pos = npos) const noexcept;
      constexpr size_type find_last_not_of(charT c, size_type pos = npos) const noexcept;
      constexpr size_type find_last_not_of(const charT* s, size_type pos, size_type n) const;
      constexpr size_type find_last_not_of(const charT* s, size_type pos = npos) const;

      constexpr bool starts_with(basic_string_view s) const noexcept; // C++2a
      constexpr bool starts_with(charT c) const noexcept;             // C++2a
      constexpr bool starts_with(const charT* s) const;               // C++2a
      constexpr bool ends_with(basic_string_view s) const noexcept;   // C++2a
      constexpr bool ends_with(charT c) const noexcept;               // C++2a
      constexpr bool ends_with(const charT* s) const;                 // C++2a

     private:
      const_pointer data_;  // exposition only
      size_type     size_;  // exposition only
    };

  // 7.11, Hash support
  template <class T> struct hash;
  template <> struct hash<string_view>;
  template <> struct hash<u16string_view>;
  template <> struct hash<u32string_view>;
  template <> struct hash<wstring_view>;

  constexpr basic_string_view<char>     operator "" sv( const char *str,     size_t len ) noexcept;
  constexpr basic_string_view<wchar_t>  operator "" sv( const wchar_t *str,  size_t len ) noexcept;
  constexpr basic_string_view<char16_t> operator "" sv( const char16_t *str, size_t len ) noexcept;
  constexpr basic_string_view<char32_t> operator "" sv( const char32_t *str, size_t len ) noexcept;

}  // namespace std


*/

#include <__config>
#include <__string>
#include <iosfwd>
#include <algorithm>
#include <iterator>
#include <limits>
#include <stdexcept>
#include <version>
#include <__debug>

#if defined(_LIBCUDACXX_USE_PRAGMA_GCC_SYSTEM_HEADER)
#pragma GCC system_header
#endif

_LIBCUDACXX_PUSH_MACROS
#include <__undef_macros>


_LIBCUDACXX_BEGIN_NAMESPACE_STD

template<class _CharT, class _Traits = char_traits<_CharT> >
class _LIBCUDACXX_TEMPLATE_VIS basic_string_view {
public:
    // types
    typedef _Traits                                    traits_type;
    typedef _CharT                                     value_type;
    typedef _CharT*                                    pointer;
    typedef const _CharT*                              const_pointer;
    typedef _CharT&                                    reference;
    typedef const _CharT&                              const_reference;
    typedef const_pointer                              const_iterator; // See [string.view.iterators]
    typedef const_iterator                             iterator;
    typedef _CUDA_VSTD::reverse_iterator<const_iterator>    const_reverse_iterator;
    typedef const_reverse_iterator                     reverse_iterator;
    typedef size_t                                     size_type;
    typedef ptrdiff_t                                  difference_type;
    static _LIBCUDACXX_CONSTEXPR const size_type npos = -1; // size_type(-1);

    static_assert((!is_array<value_type>::value), "Character type of basic_string_view must not be an array");
    static_assert(( is_standard_layout<value_type>::value), "Character type of basic_string_view must be standard-layout");
    static_assert(( is_trivial<value_type>::value), "Character type of basic_string_view must be trivial");
    static_assert((is_same<_CharT, typename traits_type::char_type>::value),
                  "traits_type::char_type must be the same type as CharT");

    // [string.view.cons], construct/copy
    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    basic_string_view() _NOEXCEPT : __data (nullptr), __size(0) {}

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    basic_string_view(const basic_string_view&) _NOEXCEPT = default;

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    basic_string_view& operator=(const basic_string_view&) _NOEXCEPT = default;

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    basic_string_view(const _CharT* __s, size_type __len) _NOEXCEPT
        : __data(__s), __size(__len)
    {
#if _LIBCUDACXX_STD_VER > 11
    _LIBCUDACXX_ASSERT(__len == 0 || __s != nullptr, "string_view::string_view(_CharT *, size_t): received nullptr");
#endif
    }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    basic_string_view(const _CharT* __s)
        : __data(__s), __size(std::__char_traits_length_checked<_Traits>(__s)) {}

    // [string.view.iterators], iterators
    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator begin()  const _NOEXCEPT { return cbegin(); }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator end()    const _NOEXCEPT { return cend(); }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator cbegin() const _NOEXCEPT { return __data; }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator cend()   const _NOEXCEPT { return __data + __size; }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX14 _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator rbegin()   const _NOEXCEPT { return const_reverse_iterator(cend()); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX14 _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator rend()     const _NOEXCEPT { return const_reverse_iterator(cbegin()); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX14 _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator crbegin()  const _NOEXCEPT { return const_reverse_iterator(cend()); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX14 _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator crend()    const _NOEXCEPT { return const_reverse_iterator(cbegin()); }

    // [string.view.capacity], capacity
    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    size_type size()     const _NOEXCEPT { return __size; }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    size_type length()   const _NOEXCEPT { return __size; }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    size_type max_size() const _NOEXCEPT { return numeric_limits<size_type>::max(); }

    _LIBCUDACXX_NODISCARD_AFTER_CXX17 _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR
    bool empty()         const _NOEXCEPT { return __size == 0; }

    // [string.view.access], element access
    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_reference operator[](size_type __pos) const _NOEXCEPT { return __data[__pos]; }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_reference at(size_type __pos) const
    {
        return __pos >= size()
            ? (__throw_out_of_range("string_view::at"), __data[0])
            : __data[__pos];
    }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_reference front() const _NOEXCEPT
    {
        return _LIBCUDACXX_ASSERT(!empty(), "string_view::front(): string is empty"), __data[0];
    }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_reference back() const _NOEXCEPT
    {
        return _LIBCUDACXX_ASSERT(!empty(), "string_view::back(): string is empty"), __data[__size-1];
    }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    const_pointer data() const _NOEXCEPT { return __data; }

    // [string.view.modifiers], modifiers:
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    void remove_prefix(size_type __n) _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__n <= size(), "remove_prefix() can't remove more than size()");
        __data += __n;
        __size -= __n;
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    void remove_suffix(size_type __n) _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__n <= size(), "remove_suffix() can't remove more than size()");
        __size -= __n;
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    void swap(basic_string_view& __other) _NOEXCEPT
    {
        const value_type *__p = __data;
        __data = __other.__data;
        __other.__data = __p;

        size_type __sz = __size;
        __size = __other.__size;
        __other.__size = __sz;
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    size_type copy(_CharT* __s, size_type __n, size_type __pos = 0) const
    {
        if (__pos > size())
            __throw_out_of_range("string_view::copy");
        size_type __rlen = _CUDA_VSTD::min(__n, size() - __pos);
        _Traits::copy(__s, data() + __pos, __rlen);
        return __rlen;
    }

    _LIBCUDACXX_CONSTEXPR _LIBCUDACXX_INLINE_VISIBILITY
    basic_string_view substr(size_type __pos = 0, size_type __n = npos) const
    {
        return __pos > size()
            ? (__throw_out_of_range("string_view::substr"), basic_string_view())
            : basic_string_view(data() + __pos, _CUDA_VSTD::min(__n, size() - __pos));
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 int compare(basic_string_view __sv) const _NOEXCEPT
    {
        size_type __rlen = _CUDA_VSTD::min( size(), __sv.size());
        int __retval = _Traits::compare(data(), __sv.data(), __rlen);
        if ( __retval == 0 ) // first __rlen chars matched
            __retval = size() == __sv.size() ? 0 : ( size() < __sv.size() ? -1 : 1 );
        return __retval;
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    int compare(size_type __pos1, size_type __n1, basic_string_view __sv) const
    {
        return substr(__pos1, __n1).compare(__sv);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    int compare(                       size_type __pos1, size_type __n1,
                basic_string_view __sv, size_type __pos2, size_type __n2) const
    {
        return substr(__pos1, __n1).compare(__sv.substr(__pos2, __n2));
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    int compare(const _CharT* __s) const _NOEXCEPT
    {
        return compare(basic_string_view(__s));
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    int compare(size_type __pos1, size_type __n1, const _CharT* __s) const
    {
        return substr(__pos1, __n1).compare(basic_string_view(__s));
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    int compare(size_type __pos1, size_type __n1, const _CharT* __s, size_type __n2) const
    {
        return substr(__pos1, __n1).compare(basic_string_view(__s, __n2));
    }

    // find
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find(basic_string_view __s, size_type __pos = 0) const _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__s.size() == 0 || __s.data() != nullptr, "string_view::find(): received nullptr");
        return __str_find<value_type, size_type, traits_type, npos>
            (data(), size(), __s.data(), __pos, __s.size());
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find(_CharT __c, size_type __pos = 0) const _NOEXCEPT
    {
        return __str_find<value_type, size_type, traits_type, npos>
            (data(), size(), __c, __pos);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find(const _CharT* __s, size_type __pos, size_type __n) const
    {
        _LIBCUDACXX_ASSERT(__n == 0 || __s != nullptr, "string_view::find(): received nullptr");
        return __str_find<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, __n);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find(const _CharT* __s, size_type __pos = 0) const
    {
        _LIBCUDACXX_ASSERT(__s != nullptr, "string_view::find(): received nullptr");
        return __str_find<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, traits_type::length(__s));
    }

    // rfind
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type rfind(basic_string_view __s, size_type __pos = npos) const _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__s.size() == 0 || __s.data() != nullptr, "string_view::find(): received nullptr");
        return __str_rfind<value_type, size_type, traits_type, npos>
            (data(), size(), __s.data(), __pos, __s.size());
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type rfind(_CharT __c, size_type __pos = npos) const _NOEXCEPT
    {
        return __str_rfind<value_type, size_type, traits_type, npos>
            (data(), size(), __c, __pos);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type rfind(const _CharT* __s, size_type __pos, size_type __n) const
    {
        _LIBCUDACXX_ASSERT(__n == 0 || __s != nullptr, "string_view::rfind(): received nullptr");
        return __str_rfind<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, __n);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type rfind(const _CharT* __s, size_type __pos=npos) const
    {
        _LIBCUDACXX_ASSERT(__s != nullptr, "string_view::rfind(): received nullptr");
        return __str_rfind<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, traits_type::length(__s));
    }

    // find_first_of
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_of(basic_string_view __s, size_type __pos = 0) const _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__s.size() == 0 || __s.data() != nullptr, "string_view::find_first_of(): received nullptr");
        return __str_find_first_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s.data(), __pos, __s.size());
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_of(_CharT __c, size_type __pos = 0) const _NOEXCEPT
    { return find(__c, __pos); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_of(const _CharT* __s, size_type __pos, size_type __n) const
    {
        _LIBCUDACXX_ASSERT(__n == 0 || __s != nullptr, "string_view::find_first_of(): received nullptr");
        return __str_find_first_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, __n);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_of(const _CharT* __s, size_type __pos=0) const
    {
        _LIBCUDACXX_ASSERT(__s != nullptr, "string_view::find_first_of(): received nullptr");
        return __str_find_first_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, traits_type::length(__s));
    }

    // find_last_of
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_of(basic_string_view __s, size_type __pos=npos) const _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__s.size() == 0 || __s.data() != nullptr, "string_view::find_last_of(): received nullptr");
        return __str_find_last_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s.data(), __pos, __s.size());
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_of(_CharT __c, size_type __pos = npos) const _NOEXCEPT
    { return rfind(__c, __pos); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_of(const _CharT* __s, size_type __pos, size_type __n) const
    {
        _LIBCUDACXX_ASSERT(__n == 0 || __s != nullptr, "string_view::find_last_of(): received nullptr");
        return __str_find_last_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, __n);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_of(const _CharT* __s, size_type __pos=npos) const
    {
        _LIBCUDACXX_ASSERT(__s != nullptr, "string_view::find_last_of(): received nullptr");
        return __str_find_last_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, traits_type::length(__s));
    }

    // find_first_not_of
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_not_of(basic_string_view __s, size_type __pos=0) const _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__s.size() == 0 || __s.data() != nullptr, "string_view::find_first_not_of(): received nullptr");
        return __str_find_first_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s.data(), __pos, __s.size());
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_not_of(_CharT __c, size_type __pos=0) const _NOEXCEPT
    {
        return __str_find_first_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __c, __pos);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_not_of(const _CharT* __s, size_type __pos, size_type __n) const
    {
        _LIBCUDACXX_ASSERT(__n == 0 || __s != nullptr, "string_view::find_first_not_of(): received nullptr");
        return __str_find_first_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, __n);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_first_not_of(const _CharT* __s, size_type __pos=0) const
    {
        _LIBCUDACXX_ASSERT(__s != nullptr, "string_view::find_first_not_of(): received nullptr");
        return __str_find_first_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, traits_type::length(__s));
    }

    // find_last_not_of
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_not_of(basic_string_view __s, size_type __pos=npos) const _NOEXCEPT
    {
        _LIBCUDACXX_ASSERT(__s.size() == 0 || __s.data() != nullptr, "string_view::find_last_not_of(): received nullptr");
        return __str_find_last_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s.data(), __pos, __s.size());
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_not_of(_CharT __c, size_type __pos=npos) const _NOEXCEPT
    {
        return __str_find_last_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __c, __pos);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_not_of(const _CharT* __s, size_type __pos, size_type __n) const
    {
        _LIBCUDACXX_ASSERT(__n == 0 || __s != nullptr, "string_view::find_last_not_of(): received nullptr");
        return __str_find_last_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, __n);
    }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    size_type find_last_not_of(const _CharT* __s, size_type __pos=npos) const
    {
        _LIBCUDACXX_ASSERT(__s != nullptr, "string_view::find_last_not_of(): received nullptr");
        return __str_find_last_not_of<value_type, size_type, traits_type, npos>
            (data(), size(), __s, __pos, traits_type::length(__s));
    }

#if _LIBCUDACXX_STD_VER > 17
    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    bool starts_with(basic_string_view __s) const _NOEXCEPT
    { return size() >= __s.size() && compare(0, __s.size(), __s) == 0; }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    bool starts_with(value_type __c) const _NOEXCEPT
    { return !empty() && _Traits::eq(front(), __c); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    bool starts_with(const value_type* __s) const _NOEXCEPT
    { return starts_with(basic_string_view(__s)); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    bool ends_with(basic_string_view __s) const _NOEXCEPT
    { return size() >= __s.size() && compare(size() - __s.size(), npos, __s) == 0; }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    bool ends_with(value_type __c) const _NOEXCEPT
    { return !empty() && _Traits::eq(back(), __c); }

    _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
    bool ends_with(const value_type* __s) const _NOEXCEPT
    { return ends_with(basic_string_view(__s)); }
#endif

private:
    const   value_type* __data;
    size_type           __size;
};


// [string.view.comparison]
// operator ==
template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator==(basic_string_view<_CharT, _Traits> __lhs,
                basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    if ( __lhs.size() != __rhs.size()) return false;
    return __lhs.compare(__rhs) == 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator==(basic_string_view<_CharT, _Traits> __lhs,
                typename common_type<basic_string_view<_CharT, _Traits> >::type __rhs) _NOEXCEPT
{
    if ( __lhs.size() != __rhs.size()) return false;
    return __lhs.compare(__rhs) == 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator==(typename common_type<basic_string_view<_CharT, _Traits> >::type __lhs,
                basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    if ( __lhs.size() != __rhs.size()) return false;
    return __lhs.compare(__rhs) == 0;
}


// operator !=
template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator!=(basic_string_view<_CharT, _Traits> __lhs, basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    if ( __lhs.size() != __rhs.size())
        return true;
    return __lhs.compare(__rhs) != 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator!=(basic_string_view<_CharT, _Traits> __lhs,
                typename common_type<basic_string_view<_CharT, _Traits> >::type __rhs) _NOEXCEPT
{
    if ( __lhs.size() != __rhs.size())
        return true;
    return __lhs.compare(__rhs) != 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator!=(typename common_type<basic_string_view<_CharT, _Traits> >::type __lhs,
                basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    if ( __lhs.size() != __rhs.size())
        return true;
    return __lhs.compare(__rhs) != 0;
}


// operator <
template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator<(basic_string_view<_CharT, _Traits> __lhs, basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) < 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator<(basic_string_view<_CharT, _Traits> __lhs,
                typename common_type<basic_string_view<_CharT, _Traits> >::type __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) < 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator<(typename common_type<basic_string_view<_CharT, _Traits> >::type __lhs,
                basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) < 0;
}


// operator >
template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator> (basic_string_view<_CharT, _Traits> __lhs, basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) > 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator>(basic_string_view<_CharT, _Traits> __lhs,
                typename common_type<basic_string_view<_CharT, _Traits> >::type __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) > 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator>(typename common_type<basic_string_view<_CharT, _Traits> >::type __lhs,
                basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) > 0;
}


// operator <=
template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator<=(basic_string_view<_CharT, _Traits> __lhs, basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) <= 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator<=(basic_string_view<_CharT, _Traits> __lhs,
                typename common_type<basic_string_view<_CharT, _Traits> >::type __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) <= 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator<=(typename common_type<basic_string_view<_CharT, _Traits> >::type __lhs,
                basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) <= 0;
}


// operator >=
template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator>=(basic_string_view<_CharT, _Traits> __lhs, basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) >= 0;
}


template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator>=(basic_string_view<_CharT, _Traits> __lhs,
                typename common_type<basic_string_view<_CharT, _Traits> >::type __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) >= 0;
}

template<class _CharT, class _Traits>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11 _LIBCUDACXX_INLINE_VISIBILITY
bool operator>=(typename common_type<basic_string_view<_CharT, _Traits> >::type __lhs,
                basic_string_view<_CharT, _Traits> __rhs) _NOEXCEPT
{
    return __lhs.compare(__rhs) >= 0;
}


template<class _CharT, class _Traits>
basic_ostream<_CharT, _Traits>&
operator<<(basic_ostream<_CharT, _Traits>& __os,
           basic_string_view<_CharT, _Traits> __str);

typedef basic_string_view<char>     string_view;
#ifndef _LIBCUDACXX_NO_HAS_CHAR8_T
typedef basic_string_view<char8_t>  u8string_view;
#endif
typedef basic_string_view<char16_t> u16string_view;
typedef basic_string_view<char32_t> u32string_view;
typedef basic_string_view<wchar_t>  wstring_view;

// [string.view.hash]
template<class _CharT>
struct _LIBCUDACXX_TEMPLATE_VIS hash<basic_string_view<_CharT, char_traits<_CharT> > >
    : public unary_function<basic_string_view<_CharT, char_traits<_CharT> >, size_t>
{
    _LIBCUDACXX_INLINE_VISIBILITY
    size_t operator()(const basic_string_view<_CharT, char_traits<_CharT> > __val) const _NOEXCEPT {
        return __do_string_hash(__val.data(), __val.data() + __val.size());
    }
};


#if _LIBCUDACXX_STD_VER > 11
inline namespace literals
{
  inline namespace string_view_literals
  {
    inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR
    basic_string_view<char> operator "" sv(const char *__str, size_t __len) _NOEXCEPT
    {
        return basic_string_view<char> (__str, __len);
    }

    inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR
    basic_string_view<wchar_t> operator "" sv(const wchar_t *__str, size_t __len) _NOEXCEPT
    {
        return basic_string_view<wchar_t> (__str, __len);
    }

#ifndef _LIBCUDACXX_NO_HAS_CHAR8_T
    inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR
    basic_string_view<char8_t> operator "" sv(const char8_t *__str, size_t __len) _NOEXCEPT
    {
        return basic_string_view<char8_t> (__str, __len);
    }
#endif

    inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR
    basic_string_view<char16_t> operator "" sv(const char16_t *__str, size_t __len) _NOEXCEPT
    {
        return basic_string_view<char16_t> (__str, __len);
    }

    inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR
    basic_string_view<char32_t> operator "" sv(const char32_t *__str, size_t __len) _NOEXCEPT
    {
        return basic_string_view<char32_t> (__str, __len);
    }
  }
}
#endif
_LIBCUDACXX_END_NAMESPACE_STD

_LIBCUDACXX_POP_MACROS

#endif // _LIBCUDACXX_STRING_VIEW
