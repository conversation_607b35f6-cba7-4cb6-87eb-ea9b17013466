{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["filelock", "fsspec >=2023.5.0", "hf-xet >=1.1.2,<2.0.0", "packaging >=20.9", "python >=3.9", "pyyaml >=5.1", "requests", "tqdm >=4.42.1", "typing-extensions >=*******", "typing_extensions >=*******"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\huggingface_hub-0.33.4-pyhd8ed1ab_0", "features": "", "files": ["Scripts/tiny-agents", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/INSTALLER", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/METADATA", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/RECORD", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/REQUESTED", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/WHEEL", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/direct_url.json", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/entry_points.txt", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/licenses/LICENSE", "Lib/site-packages/huggingface_hub-0.33.4.dist-info/top_level.txt", "Lib/site-packages/huggingface_hub/__init__.py", "Lib/site-packages/huggingface_hub/_commit_api.py", "Lib/site-packages/huggingface_hub/_commit_scheduler.py", "Lib/site-packages/huggingface_hub/_inference_endpoints.py", "Lib/site-packages/huggingface_hub/_local_folder.py", "Lib/site-packages/huggingface_hub/_login.py", "Lib/site-packages/huggingface_hub/_oauth.py", "Lib/site-packages/huggingface_hub/_snapshot_download.py", "Lib/site-packages/huggingface_hub/_space_api.py", "Lib/site-packages/huggingface_hub/_tensorboard_logger.py", "Lib/site-packages/huggingface_hub/_upload_large_folder.py", "Lib/site-packages/huggingface_hub/_webhooks_payload.py", "Lib/site-packages/huggingface_hub/_webhooks_server.py", "Lib/site-packages/huggingface_hub/commands/__init__.py", "Lib/site-packages/huggingface_hub/commands/_cli_utils.py", "Lib/site-packages/huggingface_hub/commands/delete_cache.py", "Lib/site-packages/huggingface_hub/commands/download.py", "Lib/site-packages/huggingface_hub/commands/env.py", "Lib/site-packages/huggingface_hub/commands/huggingface_cli.py", "Lib/site-packages/huggingface_hub/commands/lfs.py", "Lib/site-packages/huggingface_hub/commands/repo.py", "Lib/site-packages/huggingface_hub/commands/repo_files.py", "Lib/site-packages/huggingface_hub/commands/scan_cache.py", "Lib/site-packages/huggingface_hub/commands/tag.py", "Lib/site-packages/huggingface_hub/commands/upload.py", "Lib/site-packages/huggingface_hub/commands/upload_large_folder.py", "Lib/site-packages/huggingface_hub/commands/user.py", "Lib/site-packages/huggingface_hub/commands/version.py", "Lib/site-packages/huggingface_hub/community.py", "Lib/site-packages/huggingface_hub/constants.py", "Lib/site-packages/huggingface_hub/dataclasses.py", "Lib/site-packages/huggingface_hub/errors.py", "Lib/site-packages/huggingface_hub/fastai_utils.py", "Lib/site-packages/huggingface_hub/file_download.py", "Lib/site-packages/huggingface_hub/hf_api.py", "Lib/site-packages/huggingface_hub/hf_file_system.py", "Lib/site-packages/huggingface_hub/hub_mixin.py", "Lib/site-packages/huggingface_hub/inference/__init__.py", "Lib/site-packages/huggingface_hub/inference/_client.py", "Lib/site-packages/huggingface_hub/inference/_common.py", "Lib/site-packages/huggingface_hub/inference/_generated/__init__.py", "Lib/site-packages/huggingface_hub/inference/_generated/_async_client.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/__init__.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/audio_classification.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/audio_to_audio.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/automatic_speech_recognition.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/base.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/chat_completion.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/depth_estimation.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/document_question_answering.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/feature_extraction.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/fill_mask.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/image_classification.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/image_segmentation.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/image_to_image.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/image_to_text.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/object_detection.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/question_answering.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/sentence_similarity.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/summarization.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/table_question_answering.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/text2text_generation.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/text_classification.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/text_generation.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/text_to_audio.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/text_to_image.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/text_to_speech.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/text_to_video.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/token_classification.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/translation.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/video_classification.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/visual_question_answering.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/zero_shot_classification.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/zero_shot_image_classification.py", "Lib/site-packages/huggingface_hub/inference/_generated/types/zero_shot_object_detection.py", "Lib/site-packages/huggingface_hub/inference/_mcp/__init__.py", "Lib/site-packages/huggingface_hub/inference/_mcp/_cli_hacks.py", "Lib/site-packages/huggingface_hub/inference/_mcp/agent.py", "Lib/site-packages/huggingface_hub/inference/_mcp/cli.py", "Lib/site-packages/huggingface_hub/inference/_mcp/constants.py", "Lib/site-packages/huggingface_hub/inference/_mcp/mcp_client.py", "Lib/site-packages/huggingface_hub/inference/_mcp/types.py", "Lib/site-packages/huggingface_hub/inference/_mcp/utils.py", "Lib/site-packages/huggingface_hub/inference/_providers/__init__.py", "Lib/site-packages/huggingface_hub/inference/_providers/_common.py", "Lib/site-packages/huggingface_hub/inference/_providers/black_forest_labs.py", "Lib/site-packages/huggingface_hub/inference/_providers/cerebras.py", "Lib/site-packages/huggingface_hub/inference/_providers/cohere.py", "Lib/site-packages/huggingface_hub/inference/_providers/fal_ai.py", "Lib/site-packages/huggingface_hub/inference/_providers/featherless_ai.py", "Lib/site-packages/huggingface_hub/inference/_providers/fireworks_ai.py", "Lib/site-packages/huggingface_hub/inference/_providers/groq.py", "Lib/site-packages/huggingface_hub/inference/_providers/hf_inference.py", "Lib/site-packages/huggingface_hub/inference/_providers/hyperbolic.py", "Lib/site-packages/huggingface_hub/inference/_providers/nebius.py", "Lib/site-packages/huggingface_hub/inference/_providers/novita.py", "Lib/site-packages/huggingface_hub/inference/_providers/nscale.py", "Lib/site-packages/huggingface_hub/inference/_providers/openai.py", "Lib/site-packages/huggingface_hub/inference/_providers/replicate.py", "Lib/site-packages/huggingface_hub/inference/_providers/sambanova.py", "Lib/site-packages/huggingface_hub/inference/_providers/together.py", "Lib/site-packages/huggingface_hub/inference_api.py", "Lib/site-packages/huggingface_hub/keras_mixin.py", "Lib/site-packages/huggingface_hub/lfs.py", "Lib/site-packages/huggingface_hub/py.typed", "Lib/site-packages/huggingface_hub/repocard.py", "Lib/site-packages/huggingface_hub/repocard_data.py", "Lib/site-packages/huggingface_hub/repository.py", "Lib/site-packages/huggingface_hub/serialization/__init__.py", "Lib/site-packages/huggingface_hub/serialization/_base.py", "Lib/site-packages/huggingface_hub/serialization/_dduf.py", "Lib/site-packages/huggingface_hub/serialization/_tensorflow.py", "Lib/site-packages/huggingface_hub/serialization/_torch.py", "Lib/site-packages/huggingface_hub/templates/datasetcard_template.md", "Lib/site-packages/huggingface_hub/templates/modelcard_template.md", "Lib/site-packages/huggingface_hub/utils/__init__.py", "Lib/site-packages/huggingface_hub/utils/_auth.py", "Lib/site-packages/huggingface_hub/utils/_cache_assets.py", "Lib/site-packages/huggingface_hub/utils/_cache_manager.py", "Lib/site-packages/huggingface_hub/utils/_chunk_utils.py", "Lib/site-packages/huggingface_hub/utils/_datetime.py", "Lib/site-packages/huggingface_hub/utils/_deprecation.py", "Lib/site-packages/huggingface_hub/utils/_experimental.py", "Lib/site-packages/huggingface_hub/utils/_fixes.py", "Lib/site-packages/huggingface_hub/utils/_git_credential.py", "Lib/site-packages/huggingface_hub/utils/_headers.py", "Lib/site-packages/huggingface_hub/utils/_hf_folder.py", "Lib/site-packages/huggingface_hub/utils/_http.py", "Lib/site-packages/huggingface_hub/utils/_lfs.py", "Lib/site-packages/huggingface_hub/utils/_pagination.py", "Lib/site-packages/huggingface_hub/utils/_paths.py", "Lib/site-packages/huggingface_hub/utils/_runtime.py", "Lib/site-packages/huggingface_hub/utils/_safetensors.py", "Lib/site-packages/huggingface_hub/utils/_subprocess.py", "Lib/site-packages/huggingface_hub/utils/_telemetry.py", "Lib/site-packages/huggingface_hub/utils/_typing.py", "Lib/site-packages/huggingface_hub/utils/_validators.py", "Lib/site-packages/huggingface_hub/utils/_xet.py", "Lib/site-packages/huggingface_hub/utils/endpoint_helpers.py", "Lib/site-packages/huggingface_hub/utils/insecure_hashlib.py", "Lib/site-packages/huggingface_hub/utils/logging.py", "Lib/site-packages/huggingface_hub/utils/sha.py", "Lib/site-packages/huggingface_hub/utils/tqdm.py", "Lib/site-packages/huggingface_hub/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_commit_api.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_commit_scheduler.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_inference_endpoints.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_local_folder.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_login.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_oauth.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_snapshot_download.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_space_api.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_tensorboard_logger.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_upload_large_folder.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_webhooks_payload.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/_webhooks_server.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/_cli_utils.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/delete_cache.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/download.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/env.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/huggingface_cli.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/lfs.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/repo.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/repo_files.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/scan_cache.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/tag.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/upload.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/upload_large_folder.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/user.cpython-310.pyc", "Lib/site-packages/huggingface_hub/commands/__pycache__/version.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/community.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/constants.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/dataclasses.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/fastai_utils.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/file_download.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/hf_api.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/hf_file_system.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/hub_mixin.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/__pycache__/_client.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/__pycache__/_common.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/__pycache__/_async_client.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_classification.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_to_audio.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/automatic_speech_recognition.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/base.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/chat_completion.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/depth_estimation.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/document_question_answering.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/feature_extraction.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/fill_mask.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_classification.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_segmentation.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_image.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_text.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/object_detection.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/question_answering.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/sentence_similarity.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/summarization.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/table_question_answering.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text2text_generation.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_classification.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_generation.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_audio.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_image.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_speech.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_video.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/token_classification.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/translation.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/video_classification.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/visual_question_answering.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_classification.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_image_classification.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_object_detection.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/_cli_hacks.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/agent.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/cli.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/constants.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/mcp_client.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/types.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/_common.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/black_forest_labs.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/cerebras.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/cohere.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/fal_ai.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/featherless_ai.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/fireworks_ai.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/groq.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/hf_inference.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/hyperbolic.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/nebius.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/novita.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/nscale.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/openai.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/replicate.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/sambanova.cpython-310.pyc", "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/together.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/inference_api.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/keras_mixin.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/lfs.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/repocard.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/repocard_data.cpython-310.pyc", "Lib/site-packages/huggingface_hub/__pycache__/repository.cpython-310.pyc", "Lib/site-packages/huggingface_hub/serialization/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/serialization/__pycache__/_base.cpython-310.pyc", "Lib/site-packages/huggingface_hub/serialization/__pycache__/_dduf.cpython-310.pyc", "Lib/site-packages/huggingface_hub/serialization/__pycache__/_tensorflow.cpython-310.pyc", "Lib/site-packages/huggingface_hub/serialization/__pycache__/_torch.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_auth.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_cache_assets.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_cache_manager.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_chunk_utils.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_datetime.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_deprecation.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_experimental.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_fixes.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_git_credential.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_headers.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_hf_folder.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_http.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_lfs.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_pagination.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_paths.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_runtime.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_safetensors.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_subprocess.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_telemetry.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_typing.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_validators.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/_xet.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/endpoint_helpers.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/insecure_hashlib.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/logging.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/sha.cpython-310.pyc", "Lib/site-packages/huggingface_hub/utils/__pycache__/tqdm.cpython-310.pyc", "Scripts/huggingface-cli-script.py", "Scripts/huggingface-cli.exe"], "fn": "huggingface_hub-0.33.4-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\huggingface_hub-0.33.4-pyhd8ed1ab_0", "type": 1}, "md5": "6c1dddeb4310cf07c0c9bb5f94aa9c9a", "name": "huggingface_hub", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\huggingface_hub-0.33.4-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "python-scripts/tiny-agents", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/home/<USER>/feedstock_root/build_artifacts/huggingface_hub_1752514897442/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "a7f3273abbd8ee3f82b6dfef88bede004c0039ae1446049937472ed8d442f4c4", "sha256_in_prefix": "2ad6ce5ef23f1b57261925289e72e5b3397c7f066fb7a0f6f51cba605868c1a8", "size_in_bytes": 514}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/METADATA", "path_type": "hardlink", "sha256": "2a00b065ed70ef323e2c79786d73feb2c9a79835ef1c801408a6cd5753ea487a", "sha256_in_prefix": "2a00b065ed70ef323e2c79786d73feb2c9a79835ef1c801408a6cd5753ea487a", "size_in_bytes": 15156}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/RECORD", "path_type": "hardlink", "sha256": "43bc076587d9d9160679783465354daf969cb9fc4f881fc98d5569fa0dd005f5", "sha256_in_prefix": "43bc076587d9d9160679783465354daf969cb9fc4f881fc98d5569fa0dd005f5", "size_in_bytes": 25511}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "sha256_in_prefix": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "size_in_bytes": 91}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "ee261492608e4d48fab3c80de9b782b472aa7c0ac4d3102235cc301dc37cf46e", "sha256_in_prefix": "ee261492608e4d48fab3c80de9b782b472aa7c0ac4d3102235cc301dc37cf46e", "size_in_bytes": 111}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "97e2000688b3ac4fa695e0c4cfff35b8a28a3c99a72e94b872bab51d669066f7", "sha256_in_prefix": "97e2000688b3ac4fa695e0c4cfff35b8a28a3c99a72e94b872bab51d669066f7", "size_in_bytes": 185}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "site-packages/huggingface_hub-0.33.4.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "f0ace5409018e26894be302cb0e009a1da8a3b0de16ab373ba2c0643da8b4929", "sha256_in_prefix": "f0ace5409018e26894be302cb0e009a1da8a3b0de16ab373ba2c0643da8b4929", "size_in_bytes": 16}, {"_path": "site-packages/huggingface_hub/__init__.py", "path_type": "hardlink", "sha256": "caf53cb8a80ce5328fd2eea6710140a76d28fd0589dcd246d4ae643c4d08b289", "sha256_in_prefix": "caf53cb8a80ce5328fd2eea6710140a76d28fd0589dcd246d4ae643c4d08b289", "size_in_bytes": 50644}, {"_path": "site-packages/huggingface_hub/_commit_api.py", "path_type": "hardlink", "sha256": "65b9ae22115d17c07717f72f1adc68ae46bb3262103a4f280640ad625b670af2", "sha256_in_prefix": "65b9ae22115d17c07717f72f1adc68ae46bb3262103a4f280640ad625b670af2", "size_in_bytes": 39456}, {"_path": "site-packages/huggingface_hub/_commit_scheduler.py", "path_type": "hardlink", "sha256": "b5f2283b5c561e34c9eaacba552e87228ca60f27053e0d1dea9052669aeb5365", "sha256_in_prefix": "b5f2283b5c561e34c9eaacba552e87228ca60f27053e0d1dea9052669aeb5365", "size_in_bytes": 14679}, {"_path": "site-packages/huggingface_hub/_inference_endpoints.py", "path_type": "hardlink", "sha256": "6a199b3dc117b09fc970c6fd4c381d903f19db3f6ecad9051b7ff5a3a7539bc8", "sha256_in_prefix": "6a199b3dc117b09fc970c6fd4c381d903f19db3f6ecad9051b7ff5a3a7539bc8", "size_in_bytes": 17598}, {"_path": "site-packages/huggingface_hub/_local_folder.py", "path_type": "hardlink", "sha256": "f4d90d1acc847d3b68a5f8576e2712d93148726f650332c5a88b73632da1d03e", "sha256_in_prefix": "f4d90d1acc847d3b68a5f8576e2712d93148726f650332c5a88b73632da1d03e", "size_in_bytes": 16915}, {"_path": "site-packages/huggingface_hub/_login.py", "path_type": "hardlink", "sha256": "b2c7f8be24f90611c8d9989d9d2b8065caf04b1cda2ceadff3181154abafbbf0", "sha256_in_prefix": "b2c7f8be24f90611c8d9989d9d2b8065caf04b1cda2ceadff3181154abafbbf0", "size_in_bytes": 20298}, {"_path": "site-packages/huggingface_hub/_oauth.py", "path_type": "hardlink", "sha256": "60d6d249908d64b882ab032861ba1201f2375e312c6f20000c973081101d8417", "sha256_in_prefix": "60d6d249908d64b882ab032861ba1201f2375e312c6f20000c973081101d8417", "size_in_bytes": 18802}, {"_path": "site-packages/huggingface_hub/_snapshot_download.py", "path_type": "hardlink", "sha256": "e9747acff05654fe3ce29517ea1cd7f0982ca88aca30518306a4fdeea02b3d2e", "sha256_in_prefix": "e9747acff05654fe3ce29517ea1cd7f0982ca88aca30518306a4fdeea02b3d2e", "size_in_bytes": 16090}, {"_path": "site-packages/huggingface_hub/_space_api.py", "path_type": "hardlink", "sha256": "8dbeab17ca8bb6368d535d83fbcca000f336eb10e21c2bbc0875c7a308464e68", "sha256_in_prefix": "8dbeab17ca8bb6368d535d83fbcca000f336eb10e21c2bbc0875c7a308464e68", "size_in_bytes": 5470}, {"_path": "site-packages/huggingface_hub/_tensorboard_logger.py", "path_type": "hardlink", "sha256": "66461c0148910bc4462f6d7841462da79f0ef06e6d9fe1c5e8309685af6299c0", "sha256_in_prefix": "66461c0148910bc4462f6d7841462da79f0ef06e6d9fe1c5e8309685af6299c0", "size_in_bytes": 8358}, {"_path": "site-packages/huggingface_hub/_upload_large_folder.py", "path_type": "hardlink", "sha256": "7a563946fd98549102569759f4f335cdd3bc906fa39a2f0389f2ce6bb682dc45", "sha256_in_prefix": "7a563946fd98549102569759f4f335cdd3bc906fa39a2f0389f2ce6bb682dc45", "size_in_bytes": 24178}, {"_path": "site-packages/huggingface_hub/_webhooks_payload.py", "path_type": "hardlink", "sha256": "5e6dca68aeed08e18195792e66f6f29bace31d7ad3d570ab6d4156b9788198d6", "sha256_in_prefix": "5e6dca68aeed08e18195792e66f6f29bace31d7ad3d570ab6d4156b9788198d6", "size_in_bytes": 3617}, {"_path": "site-packages/huggingface_hub/_webhooks_server.py", "path_type": "hardlink", "sha256": "e49eb7c24f4c50628134956c383f62eb4989f95329d1862695ff3bbd0b25f8bf", "sha256_in_prefix": "e49eb7c24f4c50628134956c383f62eb4989f95329d1862695ff3bbd0b25f8bf", "size_in_bytes": 15767}, {"_path": "site-packages/huggingface_hub/commands/__init__.py", "path_type": "hardlink", "sha256": "0246ccd9afa21a1d15abfc405a12b79aedee678e249bcf97e6e5a329cbdcad44", "sha256_in_prefix": "0246ccd9afa21a1d15abfc405a12b79aedee678e249bcf97e6e5a329cbdcad44", "size_in_bytes": 928}, {"_path": "site-packages/huggingface_hub/commands/_cli_utils.py", "path_type": "hardlink", "sha256": "36de828db918a90411ba1ef46d45d503aad9a5b66dfd26b55aa054c6340bbba8", "sha256_in_prefix": "36de828db918a90411ba1ef46d45d503aad9a5b66dfd26b55aa054c6340bbba8", "size_in_bytes": 2095}, {"_path": "site-packages/huggingface_hub/commands/delete_cache.py", "path_type": "hardlink", "sha256": "e946a1a827b1ff7ab2c4396d9f819c6a2bb0cf17c89cf95708ad74a37dde6555", "sha256_in_prefix": "e946a1a827b1ff7ab2c4396d9f819c6a2bb0cf17c89cf95708ad74a37dde6555", "size_in_bytes": 17623}, {"_path": "site-packages/huggingface_hub/commands/download.py", "path_type": "hardlink", "sha256": "d585cab6d07c6015fb489d09c60d2dd67f32a76054657b58d1c93a0e10a0f971", "sha256_in_prefix": "d585cab6d07c6015fb489d09c60d2dd67f32a76054657b58d1c93a0e10a0f971", "size_in_bytes": 8183}, {"_path": "site-packages/huggingface_hub/commands/env.py", "path_type": "hardlink", "sha256": "c989780d24b5e15f2ddb8e27022d2defb233c792c87604bf772eb1895e5540c1", "sha256_in_prefix": "c989780d24b5e15f2ddb8e27022d2defb233c792c87604bf772eb1895e5540c1", "size_in_bytes": 1226}, {"_path": "site-packages/huggingface_hub/commands/huggingface_cli.py", "path_type": "hardlink", "sha256": "64026a6a506d8e132b2e6fd893dfdc3ffb635648a6acb6ecc7ff82f04d63d00f", "sha256_in_prefix": "64026a6a506d8e132b2e6fd893dfdc3ffb635648a6acb6ecc7ff82f04d63d00f", "size_in_bytes": 2523}, {"_path": "site-packages/huggingface_hub/commands/lfs.py", "path_type": "hardlink", "sha256": "c5d6e73513b4e14b907a61215064fcd3d8c58109fd463f929f24ffd0f87e5588", "sha256_in_prefix": "c5d6e73513b4e14b907a61215064fcd3d8c58109fd463f929f24ffd0f87e5588", "size_in_bytes": 7342}, {"_path": "site-packages/huggingface_hub/commands/repo.py", "path_type": "hardlink", "sha256": "13436c87a6ef4b096548e1d60fecd4a164cd385c982ae313968718b30aa64805", "sha256_in_prefix": "13436c87a6ef4b096548e1d60fecd4a164cd385c982ae313968718b30aa64805", "size_in_bytes": 5923}, {"_path": "site-packages/huggingface_hub/commands/repo_files.py", "path_type": "hardlink", "sha256": "35fbfc4e3b9a6553ab8fb4d98eba23b63743f167f9e1a66f60f0c439ebe1eed0", "sha256_in_prefix": "35fbfc4e3b9a6553ab8fb4d98eba23b63743f167f9e1a66f60f0c439ebe1eed0", "size_in_bytes": 4923}, {"_path": "site-packages/huggingface_hub/commands/scan_cache.py", "path_type": "hardlink", "sha256": "c5d0ffcd129de3d851b804f2a6d1becda634f21d5ff42023079cd904a7b4604a", "sha256_in_prefix": "c5d0ffcd129de3d851b804f2a6d1becda634f21d5ff42023079cd904a7b4604a", "size_in_bytes": 8563}, {"_path": "site-packages/huggingface_hub/commands/tag.py", "path_type": "hardlink", "sha256": "d0b3506722be58a8b45482fd8b5c56ccac49d482f0d63c4c17f13ab76c1e26cb", "sha256_in_prefix": "d0b3506722be58a8b45482fd8b5c56ccac49d482f0d63c4c17f13ab76c1e26cb", "size_in_bytes": 6288}, {"_path": "site-packages/huggingface_hub/commands/upload.py", "path_type": "hardlink", "sha256": "de6701068da934ef7d347ccdbbaa3e55c1078c3a7b9adc9061e284f5e55aa34c", "sha256_in_prefix": "de6701068da934ef7d347ccdbbaa3e55c1078c3a7b9adc9061e284f5e55aa34c", "size_in_bytes": 14453}, {"_path": "site-packages/huggingface_hub/commands/upload_large_folder.py", "path_type": "hardlink", "sha256": "3fe10ee38256565dfd031e1bd04d19f3bdddd1ae92dfc41ab3c3fa0da2f51302", "sha256_in_prefix": "3fe10ee38256565dfd031e1bd04d19f3bdddd1ae92dfc41ab3c3fa0da2f51302", "size_in_bytes": 6129}, {"_path": "site-packages/huggingface_hub/commands/user.py", "path_type": "hardlink", "sha256": "ff8ae30ab3fce0aaada82327fabdd858bb862eb9e494e5937491554cd14c2ee5", "sha256_in_prefix": "ff8ae30ab3fce0aaada82327fabdd858bb862eb9e494e5937491554cd14c2ee5", "size_in_bytes": 7096}, {"_path": "site-packages/huggingface_hub/commands/version.py", "path_type": "hardlink", "sha256": "bdf0899fb18ed66f83b4399b76cb72f3f45356d9d9ee55fa309b31d017f87beb", "sha256_in_prefix": "bdf0899fb18ed66f83b4399b76cb72f3f45356d9d9ee55fa309b31d017f87beb", "size_in_bytes": 1266}, {"_path": "site-packages/huggingface_hub/community.py", "path_type": "hardlink", "sha256": "e0cb5ca31108f7fd259a68a50449ef5048bcfced48bdf6bca7a78ac58539fadb", "sha256_in_prefix": "e0cb5ca31108f7fd259a68a50449ef5048bcfced48bdf6bca7a78ac58539fadb", "size_in_bytes": 12198}, {"_path": "site-packages/huggingface_hub/constants.py", "path_type": "hardlink", "sha256": "d517576de39147edb56aec8a2ec2db3890c80bd09def4b58100556ccfeb80497", "sha256_in_prefix": "d517576de39147edb56aec8a2ec2db3890c80bd09def4b58100556ccfeb80497", "size_in_bytes": 10239}, {"_path": "site-packages/huggingface_hub/dataclasses.py", "path_type": "hardlink", "sha256": "b203dd122d940e9ae134f3f63cf9224a5cec1dd0b559ca705532f094700472bd", "sha256_in_prefix": "b203dd122d940e9ae134f3f63cf9224a5cec1dd0b559ca705532f094700472bd", "size_in_bytes": 17224}, {"_path": "site-packages/huggingface_hub/errors.py", "path_type": "hardlink", "sha256": "0fb2f0d098eb7fcbdf983d01dba2c4be0f895a453c66ad0a0cf24ecc563840bc", "sha256_in_prefix": "0fb2f0d098eb7fcbdf983d01dba2c4be0f895a453c66ad0a0cf24ecc563840bc", "size_in_bytes": 11201}, {"_path": "site-packages/huggingface_hub/fastai_utils.py", "path_type": "hardlink", "sha256": "0e9787f5dfbabadda4fe7080030825339d579919a07db45ed923e27e954be589", "sha256_in_prefix": "0e9787f5dfbabadda4fe7080030825339d579919a07db45ed923e27e954be589", "size_in_bytes": 16745}, {"_path": "site-packages/huggingface_hub/file_download.py", "path_type": "hardlink", "sha256": "a973d1986453bf5a8003f43053b08762eb05182304df6a31d7243a5fadbfe4ef", "sha256_in_prefix": "a973d1986453bf5a8003f43053b08762eb05182304df6a31d7243a5fadbfe4ef", "size_in_bytes": 78542}, {"_path": "site-packages/huggingface_hub/hf_api.py", "path_type": "hardlink", "sha256": "2d2b5ea11ea776f6e44b51a8aed80095f2198761cd4d904e862294278a7081d6", "sha256_in_prefix": "2d2b5ea11ea776f6e44b51a8aed80095f2198761cd4d904e862294278a7081d6", "size_in_bytes": 444347}, {"_path": "site-packages/huggingface_hub/hf_file_system.py", "path_type": "hardlink", "sha256": "53a218fd02cdcd97efa6c6ef28489a90e052d94e9c76e670e6dd31f3004f527e", "sha256_in_prefix": "53a218fd02cdcd97efa6c6ef28489a90e052d94e9c76e670e6dd31f3004f527e", "size_in_bytes": 47531}, {"_path": "site-packages/huggingface_hub/hub_mixin.py", "path_type": "hardlink", "sha256": "2e96e080e3c896becbd90562dc339f5ac198b1d7bd38c970c53e4b65f7127524", "sha256_in_prefix": "2e96e080e3c896becbd90562dc339f5ac198b1d7bd38c970c53e4b65f7127524", "size_in_bytes": 38115}, {"_path": "site-packages/huggingface_hub/inference/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/inference/_client.py", "path_type": "hardlink", "sha256": "dddd4ff514f60c0e20d60008c67aba836be3cde0b25f7d3417969f4441298e17", "sha256_in_prefix": "dddd4ff514f60c0e20d60008c67aba836be3cde0b25f7d3417969f4441298e17", "size_in_bytes": 161775}, {"_path": "site-packages/huggingface_hub/inference/_common.py", "path_type": "hardlink", "sha256": "55ef8923f03d2ee0c83e62fb4adb2bf64721eb286cf64bb03b56f52f15422ec1", "sha256_in_prefix": "55ef8923f03d2ee0c83e62fb4adb2bf64721eb286cf64bb03b56f52f15422ec1", "size_in_bytes": 14781}, {"_path": "site-packages/huggingface_hub/inference/_generated/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/inference/_generated/_async_client.py", "path_type": "hardlink", "sha256": "0f72affc421deed32853a103e35ff64872ebc368e52485b6ab4bc7c744b14e99", "sha256_in_prefix": "0f72affc421deed32853a103e35ff64872ebc368e52485b6ab4bc7c744b14e99", "size_in_bytes": 167696}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/__init__.py", "path_type": "hardlink", "sha256": "a88f04bbd59c05c2a15642e58ba6278861e97e227d30baadce6c175f7e44edb0", "sha256_in_prefix": "a88f04bbd59c05c2a15642e58ba6278861e97e227d30baadce6c175f7e44edb0", "size_in_bytes": 6443}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/audio_classification.py", "path_type": "hardlink", "sha256": "260de6cdf1a10921fa09fbd5be0252885a64cfabf89cd0341b82c95da70480d7", "sha256_in_prefix": "260de6cdf1a10921fa09fbd5be0252885a64cfabf89cd0341b82c95da70480d7", "size_in_bytes": 1573}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/audio_to_audio.py", "path_type": "hardlink", "sha256": "d84a785a478f2fba09c1ca799d126a029c2f8ae9861db7edf4785713d5cb1e3e", "sha256_in_prefix": "d84a785a478f2fba09c1ca799d126a029c2f8ae9861db7edf4785713d5cb1e3e", "size_in_bytes": 891}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/automatic_speech_recognition.py", "path_type": "hardlink", "sha256": "f0212986beabbd11e0ab52f931ddedab5e15d2d1009b32647a6875ffb812b30a", "sha256_in_prefix": "f0212986beabbd11e0ab52f931ddedab5e15d2d1009b32647a6875ffb812b30a", "size_in_bytes": 5515}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/base.py", "path_type": "hardlink", "sha256": "e171b8f6ad3ed9239fb5843c1d742758bc62264b68bbe6bb2281b791d3affa48", "sha256_in_prefix": "e171b8f6ad3ed9239fb5843c1d742758bc62264b68bbe6bb2281b791d3affa48", "size_in_bytes": 6751}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/chat_completion.py", "path_type": "hardlink", "sha256": "e84a969e96de4f4cec88b3598e8243ceb999df83368f4d52f7da0c31ac9983d6", "sha256_in_prefix": "e84a969e96de4f4cec88b3598e8243ceb999df83368f4d52f7da0c31ac9983d6", "size_in_bytes": 11182}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/depth_estimation.py", "path_type": "hardlink", "sha256": "adca5ef4c85831e2e37e53b006cdca3193ebe968ce1f7158113852b46f852773", "sha256_in_prefix": "adca5ef4c85831e2e37e53b006cdca3193ebe968ce1f7158113852b46f852773", "size_in_bytes": 929}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/document_question_answering.py", "path_type": "hardlink", "sha256": "e811181b025ca8695a87844124302f5854c45e43b49a8b0188ccbcdb8df69c03", "sha256_in_prefix": "e811181b025ca8695a87844124302f5854c45e43b49a8b0188ccbcdb8df69c03", "size_in_bytes": 3202}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/feature_extraction.py", "path_type": "hardlink", "sha256": "34c5952ff4cb486e524b96ddb75f9f7e5919ef950c94c29e4ccb667675130037", "sha256_in_prefix": "34c5952ff4cb486e524b96ddb75f9f7e5919ef950c94c29e4ccb667675130037", "size_in_bytes": 1537}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/fill_mask.py", "path_type": "hardlink", "sha256": "3ab4e043b35d9f4fdd58ae6d850859c133876d09e2f23d2225cc7d965ca145db", "sha256_in_prefix": "3ab4e043b35d9f4fdd58ae6d850859c133876d09e2f23d2225cc7d965ca145db", "size_in_bytes": 1708}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_classification.py", "path_type": "hardlink", "sha256": "03e634db8a3cef6dff9fc986568b384f07409152fad8c70678bd62228e15ccdb", "sha256_in_prefix": "03e634db8a3cef6dff9fc986568b384f07409152fad8c70678bd62228e15ccdb", "size_in_bytes": 1585}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_segmentation.py", "path_type": "hardlink", "sha256": "beb908e12b8fd48abf88b5dcfb6a50858637487378833bc506866a6d41f153ba", "sha256_in_prefix": "beb908e12b8fd48abf88b5dcfb6a50858637487378833bc506866a6d41f153ba", "size_in_bytes": 1950}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_to_image.py", "path_type": "hardlink", "sha256": "1cfcf5b8a5e4ffdc6f80d522dc657a9f8970f89dc6e9c7464dc5b73aefcdd25f", "sha256_in_prefix": "1cfcf5b8a5e4ffdc6f80d522dc657a9f8970f89dc6e9c7464dc5b73aefcdd25f", "size_in_bytes": 2044}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_to_text.py", "path_type": "hardlink", "sha256": "39a1440407e04fe7ce57327bc557ab9867fb54e0e1adcf7e260dfc5ab33bfb6a", "sha256_in_prefix": "39a1440407e04fe7ce57327bc557ab9867fb54e0e1adcf7e260dfc5ab33bfb6a", "size_in_bytes": 4810}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/object_detection.py", "path_type": "hardlink", "sha256": "56e1656f5dbcd6a4d7a128090e6aae1b3f9535f1192e8d87d1187f17a305eadb", "sha256_in_prefix": "56e1656f5dbcd6a4d7a128090e6aae1b3f9535f1192e8d87d1187f17a305eadb", "size_in_bytes": 2000}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/question_answering.py", "path_type": "hardlink", "sha256": "cf0dfc6bdffd97693589f61979f8e48a8a99e1ab1f49133d3389d4de04829804", "sha256_in_prefix": "cf0dfc6bdffd97693589f61979f8e48a8a99e1ab1f49133d3389d4de04829804", "size_in_bytes": 2898}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/sentence_similarity.py", "path_type": "hardlink", "sha256": "c39363d60d7c781ce8a59c31b832c8f9f12cc9a08ad8aac7039c9ffd77d28e0a", "sha256_in_prefix": "c39363d60d7c781ce8a59c31b832c8f9f12cc9a08ad8aac7039c9ffd77d28e0a", "size_in_bytes": 1052}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/summarization.py", "path_type": "hardlink", "sha256": "5861abf2e0cbad983c250805f593143fd7aec3ab99a3acf091567e21fbc2148d", "sha256_in_prefix": "5861abf2e0cbad983c250805f593143fd7aec3ab99a3acf091567e21fbc2148d", "size_in_bytes": 1487}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/table_question_answering.py", "path_type": "hardlink", "sha256": "7099c83c0d9f21b40fd848e7ed7fdeb18e3ca865a85e0df47e734ea825e23954", "sha256_in_prefix": "7099c83c0d9f21b40fd848e7ed7fdeb18e3ca865a85e0df47e734ea825e23954", "size_in_bytes": 2293}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text2text_generation.py", "path_type": "hardlink", "sha256": "bfee35f30d4934d499dadb96f43525e9adfa4d04020036b8dfc037b9fbdc6cec", "sha256_in_prefix": "bfee35f30d4934d499dadb96f43525e9adfa4d04020036b8dfc037b9fbdc6cec", "size_in_bytes": 1609}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_classification.py", "path_type": "hardlink", "sha256": "15aac08f280b11f3e87cb7ca79a6f327b3ca10122d9471a850d50ecf22d1a4be", "sha256_in_prefix": "15aac08f280b11f3e87cb7ca79a6f327b3ca10122d9471a850d50ecf22d1a4be", "size_in_bytes": 1445}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_generation.py", "path_type": "hardlink", "sha256": "dbcbbed7353b7a5936b5e3f7cb8bb5540b430c7cd8d096762841097b9779baf8", "sha256_in_prefix": "dbcbbed7353b7a5936b5e3f7cb8bb5540b430c7cd8d096762841097b9779baf8", "size_in_bytes": 5922}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_audio.py", "path_type": "hardlink", "sha256": "d4747d43ab3d317aad2864ef1cf2c654cba6e7e7a0ecef8f82fe8d774bfcfc75", "sha256_in_prefix": "d4747d43ab3d317aad2864ef1cf2c654cba6e7e7a0ecef8f82fe8d774bfcfc75", "size_in_bytes": 4741}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_image.py", "path_type": "hardlink", "sha256": "b061a2d456b49f93e677a1b723e17648125c27533b1a6aa79e0eac7e2d00573b", "sha256_in_prefix": "b061a2d456b49f93e677a1b723e17648125c27533b1a6aa79e0eac7e2d00573b", "size_in_bytes": 1903}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_speech.py", "path_type": "hardlink", "sha256": "44e16e477da28d13827aa6eff35268b34966680f12456c8852c5ab743e325a8c", "sha256_in_prefix": "44e16e477da28d13827aa6eff35268b34966680f12456c8852c5ab743e325a8c", "size_in_bytes": 4760}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_video.py", "path_type": "hardlink", "sha256": "c875d536cdede9a60eeef8acac1941e5c1fb923a32b3117de75d1aa1f71fff5f", "sha256_in_prefix": "c875d536cdede9a60eeef8acac1941e5c1fb923a32b3117de75d1aa1f71fff5f", "size_in_bytes": 1790}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/token_classification.py", "path_type": "hardlink", "sha256": "89b9407207f15de68b609d7835d8a208c210b8195aad49272e45d492586f70b2", "sha256_in_prefix": "89b9407207f15de68b609d7835d8a208c210b8195aad49272e45d492586f70b2", "size_in_bytes": 1915}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/translation.py", "path_type": "hardlink", "sha256": "c70c385f971f098bff174a083562f0a8944f093e92577541009b8f8d3b3f8fba", "sha256_in_prefix": "c70c385f971f098bff174a083562f0a8944f093e92577541009b8f8d3b3f8fba", "size_in_bytes": 1763}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/video_classification.py", "path_type": "hardlink", "sha256": "4f2c9d8d0c363512caf6c0c6cc95159e40dea3ce3c79b982c79f3c52bf08f6ad", "sha256_in_prefix": "4f2c9d8d0c363512caf6c0c6cc95159e40dea3ce3c79b982c79f3c52bf08f6ad", "size_in_bytes": 1680}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/visual_question_answering.py", "path_type": "hardlink", "sha256": "016ad0eaaa388196b73c679d68da730c5ab1e72398ca3867501ea2b99123feea", "sha256_in_prefix": "016ad0eaaa388196b73c679d68da730c5ab1e72398ca3867501ea2b99123feea", "size_in_bytes": 1673}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/zero_shot_classification.py", "path_type": "hardlink", "sha256": "04089e6cf8ecaa835af04537e43c74a5f22ff16d9ce064a5f9325c915d4c6b14", "sha256_in_prefix": "04089e6cf8ecaa835af04537e43c74a5f22ff16d9ce064a5f9325c915d4c6b14", "size_in_bytes": 1738}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/zero_shot_image_classification.py", "path_type": "hardlink", "sha256": "f09f67e95a85011916bcf7c064d5841bbd0096b30695d53de441904309f4eb32", "sha256_in_prefix": "f09f67e95a85011916bcf7c064d5841bbd0096b30695d53de441904309f4eb32", "size_in_bytes": 1487}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/zero_shot_object_detection.py", "path_type": "hardlink", "sha256": "19477cd4b215ee811b4566b20e50158322e6639f7aaf5337016d235c3a75c930", "sha256_in_prefix": "19477cd4b215ee811b4566b20e50158322e6639f7aaf5337016d235c3a75c930", "size_in_bytes": 1630}, {"_path": "site-packages/huggingface_hub/inference/_mcp/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/inference/_mcp/_cli_hacks.py", "path_type": "hardlink", "sha256": "70c662ad515ee0dd0433d373cecf5a126cd4054101611e28619a41c935a56423", "sha256_in_prefix": "70c662ad515ee0dd0433d373cecf5a126cd4054101611e28619a41c935a56423", "size_in_bytes": 3182}, {"_path": "site-packages/huggingface_hub/inference/_mcp/agent.py", "path_type": "hardlink", "sha256": "55a86f4aa95d882d51ef60851f84f4e65f34b845e5e4e8cbc1ba164055096ecc", "sha256_in_prefix": "55a86f4aa95d882d51ef60851f84f4e65f34b845e5e4e8cbc1ba164055096ecc", "size_in_bytes": 4281}, {"_path": "site-packages/huggingface_hub/inference/_mcp/cli.py", "path_type": "hardlink", "sha256": "6ef712f0d0abf2c12a0781866aa2fce64cadae2f09c868dbc2d23903b0e80d60", "sha256_in_prefix": "6ef712f0d0abf2c12a0781866aa2fce64cadae2f09c868dbc2d23903b0e80d60", "size_in_bytes": 9031}, {"_path": "site-packages/huggingface_hub/inference/_mcp/constants.py", "path_type": "hardlink", "sha256": "58934e9ec51453b476c7d1d31a082184891a931a282ce46b89e4b515e2757ab2", "sha256_in_prefix": "58934e9ec51453b476c7d1d31a082184891a931a282ce46b89e4b515e2757ab2", "size_in_bytes": 2273}, {"_path": "site-packages/huggingface_hub/inference/_mcp/mcp_client.py", "path_type": "hardlink", "sha256": "9dd69371964f6d4d594cd51e07df9675a3b1edb1c3de5b2b5e72b109e8b0a548", "sha256_in_prefix": "9dd69371964f6d4d594cd51e07df9675a3b1edb1c3de5b2b5e72b109e8b0a548", "size_in_bytes": 15788}, {"_path": "site-packages/huggingface_hub/inference/_mcp/types.py", "path_type": "hardlink", "sha256": "7ba6dcbc5b769eeda131a5f7ee163bb6c466d856360e81d1372bf3f7f01b907d", "sha256_in_prefix": "7ba6dcbc5b769eeda131a5f7ee163bb6c466d856360e81d1372bf3f7f01b907d", "size_in_bytes": 743}, {"_path": "site-packages/huggingface_hub/inference/_mcp/utils.py", "path_type": "hardlink", "sha256": "56c4569747ee4990d2d33353f67ec538c4a5cc0d1405b3fca7cc562960edd8f7", "sha256_in_prefix": "56c4569747ee4990d2d33353f67ec538c4a5cc0d1405b3fca7cc562960edd8f7", "size_in_bytes": 4093}, {"_path": "site-packages/huggingface_hub/inference/_providers/__init__.py", "path_type": "hardlink", "sha256": "ace6942fccd729acd83209cf303c4437b6379d9c1a2ac03482420b2d63751cb8", "sha256_in_prefix": "ace6942fccd729acd83209cf303c4437b6379d9c1a2ac03482420b2d63751cb8", "size_in_bytes": 8116}, {"_path": "site-packages/huggingface_hub/inference/_providers/_common.py", "path_type": "hardlink", "sha256": "76564c8f6d23ffcd64966f9ad557446c7e693c68dc948bf594b536bab87f6739", "sha256_in_prefix": "76564c8f6d23ffcd64966f9ad557446c7e693c68dc948bf594b536bab87f6739", "size_in_bytes": 11448}, {"_path": "site-packages/huggingface_hub/inference/_providers/black_forest_labs.py", "path_type": "hardlink", "sha256": "c0eeea811c8dcab20a959b6f2f7bc911b4b8f83d7d91fa17664e8f0e1d5d4e2b", "sha256_in_prefix": "c0eeea811c8dcab20a959b6f2f7bc911b4b8f83d7d91fa17664e8f0e1d5d4e2b", "size_in_bytes": 2842}, {"_path": "site-packages/huggingface_hub/inference/_providers/cerebras.py", "path_type": "hardlink", "sha256": "40e27ed54fa8b3bb84ee9e9e5279ff3ff00fabec90871dbc6deedcdd3ab612e0", "sha256_in_prefix": "40e27ed54fa8b3bb84ee9e9e5279ff3ff00fabec90871dbc6deedcdd3ab612e0", "size_in_bytes": 210}, {"_path": "site-packages/huggingface_hub/inference/_providers/cohere.py", "path_type": "hardlink", "sha256": "3b7b42faa2142fdd66c7f984f1b3870ad0d6710b8a3946ae854a17494054099f", "sha256_in_prefix": "3b7b42faa2142fdd66c7f984f1b3870ad0d6710b8a3946ae854a17494054099f", "size_in_bytes": 1253}, {"_path": "site-packages/huggingface_hub/inference/_providers/fal_ai.py", "path_type": "hardlink", "sha256": "80658fb2f408b2e9379132171f0a4ea80d11d59b0f128e4c61cecec14a058f16", "sha256_in_prefix": "80658fb2f408b2e9379132171f0a4ea80d11d59b0f128e4c61cecec14a058f16", "size_in_bytes": 7162}, {"_path": "site-packages/huggingface_hub/inference/_providers/featherless_ai.py", "path_type": "hardlink", "sha256": "431073fb7d8ee0fcedc62c6b223adf2ae4cecef7eac948be715b30d077ffce56", "sha256_in_prefix": "431073fb7d8ee0fcedc62c6b223adf2ae4cecef7eac948be715b30d077ffce56", "size_in_bytes": 1382}, {"_path": "site-packages/huggingface_hub/inference/_providers/fireworks_ai.py", "path_type": "hardlink", "sha256": "21ddb6e884df3e439c14c173972dcc5bd97e75997d978aa2ccbe091075a40459", "sha256_in_prefix": "21ddb6e884df3e439c14c173972dcc5bd97e75997d978aa2ccbe091075a40459", "size_in_bytes": 1215}, {"_path": "site-packages/huggingface_hub/inference/_providers/groq.py", "path_type": "hardlink", "sha256": "253936255e193a56a8861a3bccb00542d93dda4195b0f98b275866cdcc2caaf4", "sha256_in_prefix": "253936255e193a56a8861a3bccb00542d93dda4195b0f98b275866cdcc2caaf4", "size_in_bytes": 315}, {"_path": "site-packages/huggingface_hub/inference/_providers/hf_inference.py", "path_type": "hardlink", "sha256": "3e81f18eb43d86ce4a67a88aa76492ba6eeeba817f268caead2e32985fea8602", "sha256_in_prefix": "3e81f18eb43d86ce4a67a88aa76492ba6eeeba817f268caead2e32985fea8602", "size_in_bytes": 9133}, {"_path": "site-packages/huggingface_hub/inference/_providers/hyperbolic.py", "path_type": "hardlink", "sha256": "3902018b68f768dbee69243c0542b52b53d57915ddaf173cd06fba62605afa7b", "sha256_in_prefix": "3902018b68f768dbee69243c0542b52b53d57915ddaf173cd06fba62605afa7b", "size_in_bytes": 1985}, {"_path": "site-packages/huggingface_hub/inference/_providers/nebius.py", "path_type": "hardlink", "sha256": "549a53176259e7caf39dcf70c5d93ee7bbf017cb15daf112c3f5a45e35ea0a1a", "sha256_in_prefix": "549a53176259e7caf39dcf70c5d93ee7bbf017cb15daf112c3f5a45e35ea0a1a", "size_in_bytes": 3580}, {"_path": "site-packages/huggingface_hub/inference/_providers/novita.py", "path_type": "hardlink", "sha256": "1c6542f303eb691414b88e6e068c9ed58e16a9ee17d75e81ef51a185b5b2e723", "sha256_in_prefix": "1c6542f303eb691414b88e6e068c9ed58e16a9ee17d75e81ef51a185b5b2e723", "size_in_bytes": 2514}, {"_path": "site-packages/huggingface_hub/inference/_providers/nscale.py", "path_type": "hardlink", "sha256": "a9652c5a29d09946cd52a7a1c8a9f7e2d56859e86ef2077e399d85e2e8f64963", "sha256_in_prefix": "a9652c5a29d09946cd52a7a1c8a9f7e2d56859e86ef2077e399d85e2e8f64963", "size_in_bytes": 1802}, {"_path": "site-packages/huggingface_hub/inference/_providers/openai.py", "path_type": "hardlink", "sha256": "18255878d763588829410ec4fd7bfc21e6e67614e2d12e967c5a2ccf79cbb69b", "sha256_in_prefix": "18255878d763588829410ec4fd7bfc21e6e67614e2d12e967c5a2ccf79cbb69b", "size_in_bytes": 1089}, {"_path": "site-packages/huggingface_hub/inference/_providers/replicate.py", "path_type": "hardlink", "sha256": "cc54279c068d991aeea93bd9506ffc22df3190a78f1cb18a4689929308eb52e9", "sha256_in_prefix": "cc54279c068d991aeea93bd9506ffc22df3190a78f1cb18a4689929308eb52e9", "size_in_bytes": 3157}, {"_path": "site-packages/huggingface_hub/inference/_providers/sambanova.py", "path_type": "hardlink", "sha256": "527b771f78ebfe4808f6fcd18e6996d43172a04b8f90a09c808225a223a3de3f", "sha256_in_prefix": "527b771f78ebfe4808f6fcd18e6996d43172a04b8f90a09c808225a223a3de3f", "size_in_bytes": 2037}, {"_path": "site-packages/huggingface_hub/inference/_providers/together.py", "path_type": "hardlink", "sha256": "287175f424b7a974bb1b5f82c1c3220fc679c333ca10a8b81760f3a80b616c11", "sha256_in_prefix": "287175f424b7a974bb1b5f82c1c3220fc679c333ca10a8b81760f3a80b616c11", "size_in_bytes": 3439}, {"_path": "site-packages/huggingface_hub/inference_api.py", "path_type": "hardlink", "sha256": "6f8f8d84f4a7f5be389d8295f2d0ca5e8766138255744ca63162f80951a4ce51", "sha256_in_prefix": "6f8f8d84f4a7f5be389d8295f2d0ca5e8766138255744ca63162f80951a4ce51", "size_in_bytes": 8323}, {"_path": "site-packages/huggingface_hub/keras_mixin.py", "path_type": "hardlink", "sha256": "dddda85b7e5200b5c7abe587a0b0ffb5bab452b71a6c62a3dc789db4f471e755", "sha256_in_prefix": "dddda85b7e5200b5c7abe587a0b0ffb5bab452b71a6c62a3dc789db4f471e755", "size_in_bytes": 19574}, {"_path": "site-packages/huggingface_hub/lfs.py", "path_type": "hardlink", "sha256": "9fe4c88caec9eda5c6df38bfff49e477a68d904e1d8ce7fd083e9d610390e4ff", "sha256_in_prefix": "9fe4c88caec9eda5c6df38bfff49e477a68d904e1d8ce7fd083e9d610390e4ff", "size_in_bytes": 16649}, {"_path": "site-packages/huggingface_hub/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/repocard.py", "path_type": "hardlink", "sha256": "8a1141298a8f35a5b0f6b58c52f71a44ac6ba282f7d8d0387c096bc335e4f4b6", "sha256_in_prefix": "8a1141298a8f35a5b0f6b58c52f71a44ac6ba282f7d8d0387c096bc335e4f4b6", "size_in_bytes": 34733}, {"_path": "site-packages/huggingface_hub/repocard_data.py", "path_type": "hardlink", "sha256": "86be11785a4440c35d87ff43c7e2fe209a08d44947ca4fa1fbc651ab055860e1", "sha256_in_prefix": "86be11785a4440c35d87ff43c7e2fe209a08d44947ca4fa1fbc651ab055860e1", "size_in_bytes": 34082}, {"_path": "site-packages/huggingface_hub/repository.py", "path_type": "hardlink", "sha256": "c55411f8c44a3437c9fd9ffdf43c2d5d9077c4d3b4e9e606fc6bd13387cb8935", "sha256_in_prefix": "c55411f8c44a3437c9fd9ffdf43c2d5d9077c4d3b4e9e606fc6bd13387cb8935", "size_in_bytes": 54557}, {"_path": "site-packages/huggingface_hub/serialization/__init__.py", "path_type": "hardlink", "sha256": "927f856be9b817332737c94db05f92c0571fcee8380ae71ec726c62b2bd9f12d", "sha256_in_prefix": "927f856be9b817332737c94db05f92c0571fcee8380ae71ec726c62b2bd9f12d", "size_in_bytes": 1041}, {"_path": "site-packages/huggingface_hub/serialization/_base.py", "path_type": "hardlink", "sha256": "0dfdc6c0647d37378afd20fbe69ad72ee7090333e234f8076e05d2c3efcb4e4f", "sha256_in_prefix": "0dfdc6c0647d37378afd20fbe69ad72ee7090333e234f8076e05d2c3efcb4e4f", "size_in_bytes": 8126}, {"_path": "site-packages/huggingface_hub/serialization/_dduf.py", "path_type": "hardlink", "sha256": "b38db6dfdacb887c1a244dfa4031264b9187ec34a643ffc17df88724ee518c88", "sha256_in_prefix": "b38db6dfdacb887c1a244dfa4031264b9187ec34a643ffc17df88724ee518c88", "size_in_bytes": 15424}, {"_path": "site-packages/huggingface_hub/serialization/_tensorflow.py", "path_type": "hardlink", "sha256": "cc73af10c83e2470b9e459b8ae80d3dcb5020cee7307daad5d97df1b4eb94403", "sha256_in_prefix": "cc73af10c83e2470b9e459b8ae80d3dcb5020cee7307daad5d97df1b4eb94403", "size_in_bytes": 3625}, {"_path": "site-packages/huggingface_hub/serialization/_torch.py", "path_type": "hardlink", "sha256": "8e9066b92649ca6329bcb70370c68dc43bbf7c4e55918fe90151fc7bcb2d608a", "sha256_in_prefix": "8e9066b92649ca6329bcb70370c68dc43bbf7c4e55918fe90151fc7bcb2d608a", "size_in_bytes": 45201}, {"_path": "site-packages/huggingface_hub/templates/datasetcard_template.md", "path_type": "hardlink", "sha256": "5be10ca91eb09dd6eb9d9a2b915bf9e944563c6e3d97b3004c6788d35e644efb", "sha256_in_prefix": "5be10ca91eb09dd6eb9d9a2b915bf9e944563c6e3d97b3004c6788d35e644efb", "size_in_bytes": 5503}, {"_path": "site-packages/huggingface_hub/templates/modelcard_template.md", "path_type": "hardlink", "sha256": "e00a80ad2ddca9db5b8ade41a3e0e18dc9c3151fa9cdae6112b2cb4cf33862e7", "sha256_in_prefix": "e00a80ad2ddca9db5b8ade41a3e0e18dc9c3151fa9cdae6112b2cb4cf33862e7", "size_in_bytes": 6870}, {"_path": "site-packages/huggingface_hub/utils/__init__.py", "path_type": "hardlink", "sha256": "3917d5927e43d30b8b22ad768e3853ce7e7f73817c7d13f107b4c6fa279d9ee4", "sha256_in_prefix": "3917d5927e43d30b8b22ad768e3853ce7e7f73817c7d13f107b4c6fa279d9ee4", "size_in_bytes": 3722}, {"_path": "site-packages/huggingface_hub/utils/_auth.py", "path_type": "hardlink", "sha256": "fbda774923ad58a30c0832a5b0cffe79bb08197d2c4a02934a70befcee244f18", "sha256_in_prefix": "fbda774923ad58a30c0832a5b0cffe79bb08197d2c4a02934a70befcee244f18", "size_in_bytes": 8294}, {"_path": "site-packages/huggingface_hub/utils/_cache_assets.py", "path_type": "hardlink", "sha256": "91a8bbec73d031f62944ea2e301402aff81d04269e4e6f7de92aa3d1d1316cd8", "sha256_in_prefix": "91a8bbec73d031f62944ea2e301402aff81d04269e4e6f7de92aa3d1d1316cd8", "size_in_bytes": 5728}, {"_path": "site-packages/huggingface_hub/utils/_cache_manager.py", "path_type": "hardlink", "sha256": "1a18ae550b04916539e6e624920886255d7f9da79c8b293cbb8a9be164c8572c", "sha256_in_prefix": "1a18ae550b04916539e6e624920886255d7f9da79c8b293cbb8a9be164c8572c", "size_in_bytes": 34531}, {"_path": "site-packages/huggingface_hub/utils/_chunk_utils.py", "path_type": "hardlink", "sha256": "91109a8f9db6f3fbca7322d6b2977c5ead357f5ec9cfa76ce52afd79de5dfd15", "sha256_in_prefix": "91109a8f9db6f3fbca7322d6b2977c5ead357f5ec9cfa76ce52afd79de5dfd15", "size_in_bytes": 2130}, {"_path": "site-packages/huggingface_hub/utils/_datetime.py", "path_type": "hardlink", "sha256": "9024b98da295db990e9dc5f5c6e8db5eccf98832dc8cb70bc3ce6c7a618dcf14", "sha256_in_prefix": "9024b98da295db990e9dc5f5c6e8db5eccf98832dc8cb70bc3ce6c7a618dcf14", "size_in_bytes": 2770}, {"_path": "site-packages/huggingface_hub/utils/_deprecation.py", "path_type": "hardlink", "sha256": "1d9851186517fd030a0410701c795f7cbb660922b4d533a97971de7d96cf7f02", "sha256_in_prefix": "1d9851186517fd030a0410701c795f7cbb660922b4d533a97971de7d96cf7f02", "size_in_bytes": 4872}, {"_path": "site-packages/huggingface_hub/utils/_experimental.py", "path_type": "hardlink", "sha256": "dfe73c8ab6e7f6c26bd82c166f3846908add5ca83cff1ec189f847172df67a2f", "sha256_in_prefix": "dfe73c8ab6e7f6c26bd82c166f3846908add5ca83cff1ec189f847172df67a2f", "size_in_bytes": 2470}, {"_path": "site-packages/huggingface_hub/utils/_fixes.py", "path_type": "hardlink", "sha256": "c50575424527d96a4ba8b8ed5cd8b29fd821fb8e782ba005f90de4c24600403f", "sha256_in_prefix": "c50575424527d96a4ba8b8ed5cd8b29fd821fb8e782ba005f90de4c24600403f", "size_in_bytes": 4437}, {"_path": "site-packages/huggingface_hub/utils/_git_credential.py", "path_type": "hardlink", "sha256": "48376c89112bd5370047665ed93074139718cd5260bc366bb3ad2877d940b0c7", "sha256_in_prefix": "48376c89112bd5370047665ed93074139718cd5260bc366bb3ad2877d940b0c7", "size_in_bytes": 4596}, {"_path": "site-packages/huggingface_hub/utils/_headers.py", "path_type": "hardlink", "sha256": "ded29037972202dd7af379d95c4a4fc90392ee85a77d8234b7f37f68953e6e6b", "sha256_in_prefix": "ded29037972202dd7af379d95c4a4fc90392ee85a77d8234b7f37f68953e6e6b", "size_in_bytes": 8876}, {"_path": "site-packages/huggingface_hub/utils/_hf_folder.py", "path_type": "hardlink", "sha256": "58d8d39eed10eeda9c492f44b0fe2cb0226bac931c0af02df0fffe2c4b66394f", "sha256_in_prefix": "58d8d39eed10eeda9c492f44b0fe2cb0226bac931c0af02df0fffe2c4b66394f", "size_in_bytes": 2487}, {"_path": "site-packages/huggingface_hub/utils/_http.py", "path_type": "hardlink", "sha256": "85eafb519d0a468f5660302ba6a545c845d3bac3865040a3e47352f046a1aa6f", "sha256_in_prefix": "85eafb519d0a468f5660302ba6a545c845d3bac3865040a3e47352f046a1aa6f", "size_in_bytes": 25531}, {"_path": "site-packages/huggingface_hub/utils/_lfs.py", "path_type": "hardlink", "sha256": "102d0ecfa5a2c25f1fa1136450eceb1135f301695b8299e9c68e5ae35d28bc56", "sha256_in_prefix": "102d0ecfa5a2c25f1fa1136450eceb1135f301695b8299e9c68e5ae35d28bc56", "size_in_bytes": 3957}, {"_path": "site-packages/huggingface_hub/utils/_pagination.py", "path_type": "hardlink", "sha256": "117e6d45ab12b900da29b5ee1986c89c12b6a1d9d258d1e0cf0dad4a0a9e0512", "sha256_in_prefix": "117e6d45ab12b900da29b5ee1986c89c12b6a1d9d258d1e0cf0dad4a0a9e0512", "size_in_bytes": 1906}, {"_path": "site-packages/huggingface_hub/utils/_paths.py", "path_type": "hardlink", "sha256": "c35661166983e729168e9fe102f863b4ea1ad9951c39726b1786ba3b7429016a", "sha256_in_prefix": "c35661166983e729168e9fe102f863b4ea1ad9951c39726b1786ba3b7429016a", "size_in_bytes": 5042}, {"_path": "site-packages/huggingface_hub/utils/_runtime.py", "path_type": "hardlink", "sha256": "bb304db2ec8d77642d5b33201304a824d52d5b6e22a8d8c0f993431b57dcf62e", "sha256_in_prefix": "bb304db2ec8d77642d5b33201304a824d52d5b6e22a8d8c0f993431b57dcf62e", "size_in_bytes": 11616}, {"_path": "site-packages/huggingface_hub/utils/_safetensors.py", "path_type": "hardlink", "sha256": "196de7cafef141cbb039b298798a13f55854455cc6d436536ca04a868f016e8b", "sha256_in_prefix": "196de7cafef141cbb039b298798a13f55854455cc6d436536ca04a868f016e8b", "size_in_bytes": 4458}, {"_path": "site-packages/huggingface_hub/utils/_subprocess.py", "path_type": "hardlink", "sha256": "bbd14550313b4ebcd04e2b84ce55271f1ed2d8fe7b19b61157cbb5e86270ac5c", "sha256_in_prefix": "bbd14550313b4ebcd04e2b84ce55271f1ed2d8fe7b19b61157cbb5e86270ac5c", "size_in_bytes": 4625}, {"_path": "site-packages/huggingface_hub/utils/_telemetry.py", "path_type": "hardlink", "sha256": "e782d7788254e691068213c0874ea0a8d011f94a313a354bbcaa8042c730a99b", "sha256_in_prefix": "e782d7788254e691068213c0874ea0a8d011f94a313a354bbcaa8042c730a99b", "size_in_bytes": 4890}, {"_path": "site-packages/huggingface_hub/utils/_typing.py", "path_type": "hardlink", "sha256": "0e0a7a4d0525a734ad7d52e84af5c70813f86f73731d9f04d0683d9980280d2e", "sha256_in_prefix": "0e0a7a4d0525a734ad7d52e84af5c70813f86f73731d9f04d0683d9980280d2e", "size_in_bytes": 2903}, {"_path": "site-packages/huggingface_hub/utils/_validators.py", "path_type": "hardlink", "sha256": "743b151b7d62a284d8ac8ca2e55c2bd43ba42f47c4989c2eddc79535dba1b2e1", "sha256_in_prefix": "743b151b7d62a284d8ac8ca2e55c2bd43ba42f47c4989c2eddc79535dba1b2e1", "size_in_bytes": 9204}, {"_path": "site-packages/huggingface_hub/utils/_xet.py", "path_type": "hardlink", "sha256": "2578150a58bc943eced4cb2f920aa7598e92f608aad5732b1e6b4e3cf78b9834", "sha256_in_prefix": "2578150a58bc943eced4cb2f920aa7598e92f608aad5732b1e6b4e3cf78b9834", "size_in_bytes": 7020}, {"_path": "site-packages/huggingface_hub/utils/endpoint_helpers.py", "path_type": "hardlink", "sha256": "f55b48025c50e47ff8cb7d2c8c20206eeed70aa02d34b0bb69163168d9f484b2", "sha256_in_prefix": "f55b48025c50e47ff8cb7d2c8c20206eeed70aa02d34b0bb69163168d9f484b2", "size_in_bytes": 2366}, {"_path": "site-packages/huggingface_hub/utils/insecure_hashlib.py", "path_type": "hardlink", "sha256": "88069ea5abc56790e17dae67f0aa3345f429aca9af7234a7b775f9f0e525f5f4", "sha256_in_prefix": "88069ea5abc56790e17dae67f0aa3345f429aca9af7234a7b775f9f0e525f5f4", "size_in_bytes": 1142}, {"_path": "site-packages/huggingface_hub/utils/logging.py", "path_type": "hardlink", "sha256": "d00f1f175ca1dcbf4a6bf6c20d7da69785391edd2d63c0ebdc971b46f585bb0a", "sha256_in_prefix": "d00f1f175ca1dcbf4a6bf6c20d7da69785391edd2d63c0ebdc971b46f585bb0a", "size_in_bytes": 4909}, {"_path": "site-packages/huggingface_hub/utils/sha.py", "path_type": "hardlink", "sha256": "3859cd1826dad2c35c4f6814c1a54226795dc6596dac71ded034bf3c2a55dc2e", "sha256_in_prefix": "3859cd1826dad2c35c4f6814c1a54226795dc6596dac71ded034bf3c2a55dc2e", "size_in_bytes": 2134}, {"_path": "site-packages/huggingface_hub/utils/tqdm.py", "path_type": "hardlink", "sha256": "c4029cc9f9cd1ec67b2f4f56b84339130e7e3038626a12c009b6cddb332670bb", "sha256_in_prefix": "c4029cc9f9cd1ec67b2f4f56b84339130e7e3038626a12c009b6cddb332670bb", "size_in_bytes": 10671}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_commit_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_commit_scheduler.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_inference_endpoints.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_local_folder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_login.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_oauth.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_snapshot_download.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_space_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_tensorboard_logger.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_upload_large_folder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_webhooks_payload.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/_webhooks_server.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/_cli_utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/delete_cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/download.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/env.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/huggingface_cli.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/lfs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/repo.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/repo_files.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/scan_cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/tag.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/upload.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/upload_large_folder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/user.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/commands/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/community.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/constants.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/dataclasses.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/errors.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/fastai_utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/file_download.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/hf_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/hf_file_system.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/hub_mixin.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/__pycache__/_client.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/__pycache__/_common.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/__pycache__/_async_client.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_classification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_to_audio.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/automatic_speech_recognition.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/chat_completion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/depth_estimation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/document_question_answering.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/feature_extraction.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/fill_mask.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_classification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_segmentation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_image.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_text.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/object_detection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/question_answering.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/sentence_similarity.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/summarization.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/table_question_answering.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text2text_generation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_classification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_generation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_audio.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_image.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_speech.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_video.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/token_classification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/translation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/video_classification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/visual_question_answering.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_classification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_image_classification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_object_detection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/_cli_hacks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/agent.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/cli.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/constants.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/mcp_client.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/types.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_mcp/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/_common.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/black_forest_labs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/cerebras.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/cohere.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/fal_ai.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/featherless_ai.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/fireworks_ai.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/groq.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/hf_inference.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/hyperbolic.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/nebius.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/novita.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/nscale.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/openai.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/replicate.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/sambanova.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/inference/_providers/__pycache__/together.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/inference_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/keras_mixin.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/lfs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/repocard.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/repocard_data.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/__pycache__/repository.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/serialization/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/serialization/__pycache__/_base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/serialization/__pycache__/_dduf.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/serialization/__pycache__/_tensorflow.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/serialization/__pycache__/_torch.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_auth.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_cache_assets.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_cache_manager.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_chunk_utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_datetime.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_deprecation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_experimental.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_fixes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_git_credential.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_headers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_hf_folder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_http.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_lfs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_pagination.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_paths.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_runtime.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_safetensors.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_subprocess.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_telemetry.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_typing.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_validators.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/_xet.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/endpoint_helpers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/insecure_hashlib.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/logging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/sha.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/huggingface_hub/utils/__pycache__/tqdm.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Scripts/huggingface-cli-script.py", "path_type": "windows_python_entry_point_script"}, {"_path": "Scripts/huggingface-cli.exe", "path_type": "windows_python_entry_point_exe"}], "paths_version": 1}, "requested_spec": "huggingface_hub", "sha256": "3aa04a293974f6e0a3685c085d405ba95e439e1d5a51956c614243bd209da147", "size": 318652, "subdir": "noarch", "timestamp": 1752515004000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.4-pyhd8ed1ab_0.conda", "version": "0.33.4"}