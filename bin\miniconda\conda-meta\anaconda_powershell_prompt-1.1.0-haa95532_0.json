{"arch": "x86_64", "build": "haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": ["anaconda-navigator >=1.9.8", "menuinst >=2.1.1"], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda_powershell_prompt-1.1.0-haa95532_0", "files": ["Menu/anaconda_powershell_prompt.ico", "Menu/anaconda_powershell_prompt_menu.json", "Scripts/.anaconda_powershell_prompt-post-link.bat", "Menu/anaconda_powershell_prompt_menu.json"], "fn": "anaconda_powershell_prompt-1.1.0-haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON> AND CC-BY-NC-ND-4.0", "license_family": "Other", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda_powershell_prompt-1.1.0-haa95532_0", "type": 1}, "md5": "19ef6ba2f17caca6afeae240c8193979", "name": "anaconda_powershell_prompt", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\anaconda_powershell_prompt-1.1.0-haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::anaconda_powershell_prompt==1.1.0=haa95532_0[md5=19ef6ba2f17caca6afeae240c8193979]", "sha256": "fbb102cc0d439ab6ae9e3475f5924650375dd66cc7f30b5c622eb875d9d3f1f7", "size": 170702, "subdir": "win-64", "timestamp": 1727197149000, "url": "https://repo.anaconda.com/pkgs/main/win-64/anaconda_powershell_prompt-1.1.0-haa95532_0.conda", "version": "1.1.0"}