<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMarker2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMarker2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMarker2" -->The activity record providing a marker which is an instantaneous point in time.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#0da97a221757e01e31887f71ebde32ff">domain</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#d2f907bc6015b0c0053dab9b743f7ce7">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#6762b4a5be5ecd3a8644763ee9821ca0">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#cf88cc07dc2bd7a42aae358ddb78055b">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#b7a66939efa7a3418b82f73d570d6547">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="unionCUpti__ActivityObjectKindId.html">CUpti_ActivityObjectKindId</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#97f04715d8e2704cc3ce1541ea63967b">objectId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#8020c1ecff9e4c54d4123e1a5fdd3b12">objectKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#5e09a5bb7d2c0c62557ce7d56deddd09">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker2.html#12512957066b6e129046099d7e9735a0">timestamp</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
The marker is specified with a descriptive name and unique id (CUPTI_ACTIVITY_KIND_MARKER). <hr><h2>Field Documentation</h2>
<a class="anchor" name="0da97a221757e01e31887f71ebde32ff"></a><!-- doxytag: member="CUpti_ActivityMarker2::domain" ref="0da97a221757e01e31887f71ebde32ff" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityMarker2.html#0da97a221757e01e31887f71ebde32ff">CUpti_ActivityMarker2::domain</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the domain to which this marker belongs to. This will be NULL for default domain. 
</div>
</div><p>
<a class="anchor" name="d2f907bc6015b0c0053dab9b743f7ce7"></a><!-- doxytag: member="CUpti_ActivityMarker2::flags" ref="d2f907bc6015b0c0053dab9b743f7ce7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityMarker2.html#d2f907bc6015b0c0053dab9b743f7ce7">CUpti_ActivityMarker2::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the marker. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="6762b4a5be5ecd3a8644763ee9821ca0"></a><!-- doxytag: member="CUpti_ActivityMarker2::id" ref="6762b4a5be5ecd3a8644763ee9821ca0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMarker2.html#6762b4a5be5ecd3a8644763ee9821ca0">CUpti_ActivityMarker2::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The marker ID. 
</div>
</div><p>
<a class="anchor" name="cf88cc07dc2bd7a42aae358ddb78055b"></a><!-- doxytag: member="CUpti_ActivityMarker2::kind" ref="cf88cc07dc2bd7a42aae358ddb78055b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMarker2.html#cf88cc07dc2bd7a42aae358ddb78055b">CUpti_ActivityMarker2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MARKER. 
</div>
</div><p>
<a class="anchor" name="b7a66939efa7a3418b82f73d570d6547"></a><!-- doxytag: member="CUpti_ActivityMarker2::name" ref="b7a66939efa7a3418b82f73d570d6547" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityMarker2.html#b7a66939efa7a3418b82f73d570d6547">CUpti_ActivityMarker2::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The marker name for an instantaneous or start marker. This will be NULL for an end marker. 
</div>
</div><p>
<a class="anchor" name="97f04715d8e2704cc3ce1541ea63967b"></a><!-- doxytag: member="CUpti_ActivityMarker2::objectId" ref="97f04715d8e2704cc3ce1541ea63967b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionCUpti__ActivityObjectKindId.html">CUpti_ActivityObjectKindId</a> <a class="el" href="structCUpti__ActivityMarker2.html#97f04715d8e2704cc3ce1541ea63967b">CUpti_ActivityMarker2::objectId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The identifier for the activity object associated with this marker. 'objectKind' indicates which ID is valid for this record. 
</div>
</div><p>
<a class="anchor" name="8020c1ecff9e4c54d4123e1a5fdd3b12"></a><!-- doxytag: member="CUpti_ActivityMarker2::objectKind" ref="8020c1ecff9e4c54d4123e1a5fdd3b12" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a> <a class="el" href="structCUpti__ActivityMarker2.html#8020c1ecff9e4c54d4123e1a5fdd3b12">CUpti_ActivityMarker2::objectKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of activity object associated with this marker. 
</div>
</div><p>
<a class="anchor" name="5e09a5bb7d2c0c62557ce7d56deddd09"></a><!-- doxytag: member="CUpti_ActivityMarker2::pad" ref="5e09a5bb7d2c0c62557ce7d56deddd09" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMarker2.html#5e09a5bb7d2c0c62557ce7d56deddd09">CUpti_ActivityMarker2::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="12512957066b6e129046099d7e9735a0"></a><!-- doxytag: member="CUpti_ActivityMarker2::timestamp" ref="12512957066b6e129046099d7e9735a0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMarker2.html#12512957066b6e129046099d7e9735a0">CUpti_ActivityMarker2::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp for the marker, in ns. A value of 0 indicates that timestamp information could not be collected for the marker. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
