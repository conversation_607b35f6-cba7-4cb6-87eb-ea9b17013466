{"arch": "x86_64", "build": "py310haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["mdurl >=0.1,<1", "python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\markdown-it-py-2.2.0-py310haa95532_1", "files": ["Lib/site-packages/markdown_it/__init__.py", "Lib/site-packages/markdown_it/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/_compat.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/_punycode.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/main.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/parser_block.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/parser_core.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/parser_inline.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/renderer.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/ruler.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/token.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/tree.cpython-310.pyc", "Lib/site-packages/markdown_it/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/markdown_it/_compat.py", "Lib/site-packages/markdown_it/_punycode.py", "Lib/site-packages/markdown_it/cli/__init__.py", "Lib/site-packages/markdown_it/cli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/cli/__pycache__/parse.cpython-310.pyc", "Lib/site-packages/markdown_it/cli/parse.py", "Lib/site-packages/markdown_it/common/__init__.py", "Lib/site-packages/markdown_it/common/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/common/__pycache__/entities.cpython-310.pyc", "Lib/site-packages/markdown_it/common/__pycache__/html_blocks.cpython-310.pyc", "Lib/site-packages/markdown_it/common/__pycache__/html_re.cpython-310.pyc", "Lib/site-packages/markdown_it/common/__pycache__/normalize_url.cpython-310.pyc", "Lib/site-packages/markdown_it/common/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/markdown_it/common/entities.py", "Lib/site-packages/markdown_it/common/html_blocks.py", "Lib/site-packages/markdown_it/common/html_re.py", "Lib/site-packages/markdown_it/common/normalize_url.py", "Lib/site-packages/markdown_it/common/utils.py", "Lib/site-packages/markdown_it/helpers/__init__.py", "Lib/site-packages/markdown_it/helpers/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/helpers/__pycache__/parse_link_destination.cpython-310.pyc", "Lib/site-packages/markdown_it/helpers/__pycache__/parse_link_label.cpython-310.pyc", "Lib/site-packages/markdown_it/helpers/__pycache__/parse_link_title.cpython-310.pyc", "Lib/site-packages/markdown_it/helpers/parse_link_destination.py", "Lib/site-packages/markdown_it/helpers/parse_link_label.py", "Lib/site-packages/markdown_it/helpers/parse_link_title.py", "Lib/site-packages/markdown_it/main.py", "Lib/site-packages/markdown_it/parser_block.py", "Lib/site-packages/markdown_it/parser_core.py", "Lib/site-packages/markdown_it/parser_inline.py", "Lib/site-packages/markdown_it/port.yaml", "Lib/site-packages/markdown_it/presets/__init__.py", "Lib/site-packages/markdown_it/presets/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/presets/__pycache__/commonmark.cpython-310.pyc", "Lib/site-packages/markdown_it/presets/__pycache__/default.cpython-310.pyc", "Lib/site-packages/markdown_it/presets/__pycache__/zero.cpython-310.pyc", "Lib/site-packages/markdown_it/presets/commonmark.py", "Lib/site-packages/markdown_it/presets/default.py", "Lib/site-packages/markdown_it/presets/zero.py", "Lib/site-packages/markdown_it/py.typed", "Lib/site-packages/markdown_it/renderer.py", "Lib/site-packages/markdown_it/ruler.py", "Lib/site-packages/markdown_it/rules_block/__init__.py", "Lib/site-packages/markdown_it/rules_block/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/blockquote.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/code.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/fence.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/heading.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/hr.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/html_block.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/lheading.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/list.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/paragraph.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/reference.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/state_block.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/__pycache__/table.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_block/blockquote.py", "Lib/site-packages/markdown_it/rules_block/code.py", "Lib/site-packages/markdown_it/rules_block/fence.py", "Lib/site-packages/markdown_it/rules_block/heading.py", "Lib/site-packages/markdown_it/rules_block/hr.py", "Lib/site-packages/markdown_it/rules_block/html_block.py", "Lib/site-packages/markdown_it/rules_block/lheading.py", "Lib/site-packages/markdown_it/rules_block/list.py", "Lib/site-packages/markdown_it/rules_block/paragraph.py", "Lib/site-packages/markdown_it/rules_block/reference.py", "Lib/site-packages/markdown_it/rules_block/state_block.py", "Lib/site-packages/markdown_it/rules_block/table.py", "Lib/site-packages/markdown_it/rules_core/__init__.py", "Lib/site-packages/markdown_it/rules_core/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/__pycache__/block.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/__pycache__/inline.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/__pycache__/linkify.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/__pycache__/normalize.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/__pycache__/replacements.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/__pycache__/smartquotes.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/__pycache__/state_core.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_core/block.py", "Lib/site-packages/markdown_it/rules_core/inline.py", "Lib/site-packages/markdown_it/rules_core/linkify.py", "Lib/site-packages/markdown_it/rules_core/normalize.py", "Lib/site-packages/markdown_it/rules_core/replacements.py", "Lib/site-packages/markdown_it/rules_core/smartquotes.py", "Lib/site-packages/markdown_it/rules_core/state_core.py", "Lib/site-packages/markdown_it/rules_inline/__init__.py", "Lib/site-packages/markdown_it/rules_inline/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/autolink.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/backticks.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/balance_pairs.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/emphasis.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/entity.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/escape.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/html_inline.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/image.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/link.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/newline.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/state_inline.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/strikethrough.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/text.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/__pycache__/text_collapse.cpython-310.pyc", "Lib/site-packages/markdown_it/rules_inline/autolink.py", "Lib/site-packages/markdown_it/rules_inline/backticks.py", "Lib/site-packages/markdown_it/rules_inline/balance_pairs.py", "Lib/site-packages/markdown_it/rules_inline/emphasis.py", "Lib/site-packages/markdown_it/rules_inline/entity.py", "Lib/site-packages/markdown_it/rules_inline/escape.py", "Lib/site-packages/markdown_it/rules_inline/html_inline.py", "Lib/site-packages/markdown_it/rules_inline/image.py", "Lib/site-packages/markdown_it/rules_inline/link.py", "Lib/site-packages/markdown_it/rules_inline/newline.py", "Lib/site-packages/markdown_it/rules_inline/state_inline.py", "Lib/site-packages/markdown_it/rules_inline/strikethrough.py", "Lib/site-packages/markdown_it/rules_inline/text.py", "Lib/site-packages/markdown_it/rules_inline/text_collapse.py", "Lib/site-packages/markdown_it/token.py", "Lib/site-packages/markdown_it/tree.py", "Lib/site-packages/markdown_it/utils.py", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/INSTALLER", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/LICENSE", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/LICENSE.markdown-it", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/METADATA", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/RECORD", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/REQUESTED", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/WHEEL", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/direct_url.json", "Lib/site-packages/markdown_it_py-2.2.0.dist-info/entry_points.txt", "Scripts/markdown-it-script.py", "Scripts/markdown-it.exe"], "fn": "markdown-it-py-2.2.0-py310haa95532_1.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\markdown-it-py-2.2.0-py310haa95532_1", "type": 1}, "md5": "87dee742a559728f41ef85055ebf9ba1", "name": "markdown-it-py", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\markdown-it-py-2.2.0-py310haa95532_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::markdown-it-py==2.2.0=py310haa95532_1[md5=87dee742a559728f41ef85055ebf9ba1]", "sha256": "b898419b5c1d1386d38a32d2ec6f9afd343888e4a65991b4279e7f01e54e2c1b", "size": 133516, "subdir": "win-64", "timestamp": 1684280061000, "url": "https://repo.anaconda.com/pkgs/main/win-64/markdown-it-py-2.2.0-py310haa95532_1.conda", "version": "2.2.0"}