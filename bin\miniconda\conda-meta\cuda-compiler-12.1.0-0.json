{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-cuobjdump >=12.1.55", "cuda-cuxxfilt >=12.1.55", "cuda-nvcc >=12.1.66", "cuda-nvprune >=12.1.55"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-compiler-12.1.0-0", "features": "", "files": [], "fn": "cuda-compiler-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-compiler-12.1.0-0", "type": 1}, "md5": "df01d64062e610bacea10c5f6f72eb91", "name": "cuda-compiler", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-compiler-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1430, "subdir": "win-64", "timestamp": 1677129954000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-compiler-12.1.0-0.tar.bz2", "version": "12.1.0"}