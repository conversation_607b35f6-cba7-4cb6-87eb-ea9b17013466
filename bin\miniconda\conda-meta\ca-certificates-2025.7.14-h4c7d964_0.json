{"build": "h4c7d964_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["__win"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\ca-certificates-2025.7.14-h4c7d964_0", "features": "", "files": ["Library/ssl/cacert.pem", "Library/ssl/cert.pem"], "fn": "ca-certificates-2025.7.14-h4c7d964_0.conda", "license": "ISC", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\ca-certificates-2025.7.14-h4c7d964_0", "type": 1}, "md5": "40334594f5916bc4c0a0313d64bfe046", "name": "ca-certificates", "noarch": "generic", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\ca-certificates-2025.7.14-h4c7d964_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/ssl/cacert.pem", "path_type": "hardlink", "sha256": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "sha256_in_prefix": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "size_in_bytes": 290057}, {"_path": "Library/ssl/cert.pem", "path_type": "hardlink", "sha256": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "sha256_in_prefix": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "size_in_bytes": 290057}], "paths_version": 1}, "requested_spec": "None", "sha256": "a7fe9bce8a0f9f985d44940ec13a297df571ee70fb2264b339c62fa190b2c437", "size": 155882, "subdir": "noarch", "timestamp": 1752482396000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-h4c7d964_0.conda", "version": "2025.7.14"}