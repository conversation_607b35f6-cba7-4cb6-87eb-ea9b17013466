<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityOpenAcc Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityOpenAcc Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityOpenAcc" -->The base activity record for OpenAcc records.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#7f2f1ba8677db32397be3d1bcbf69d8d">async</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#fe0f9caadc974e006e594986cab625ba">asyncMap</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#81d02af0a1a0c452cf06941885070b34">cuContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#05595dd4ed260e4cde2cb3457e569377">cuDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#2de64d3c200f75a7db15677956ecc966">cuProcessId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#176ab2a2c03001fc1ac89376cc7e6eaf">cuStreamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#34dd59dff4b6fb79749c6210163b40a9">cuThreadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#0e4fcccc203e63f979d0f5c8ef65cd43">deviceNumber</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#e5c173af41e879131c3317cba733279d">deviceType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#3fe92274275627fb28986e5ffc046d57">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#509bf31ec7eb759fa779b447e1ed3f75">endLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#5f7b0689f352034596684eaf0d64f504">eventKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#e42271bc670e3c511ee286b4f574c863">externalId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#237d817dec1fbda3ade3e62dc26d6437">funcEndLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#d6cbaf987363d22784d919a8a639b1f7">funcLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#056897be910aa4b7f4399fcedf5f7848">implicit</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#a2b0df639616ce0b527408b450306f79">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#e35c6ba06465ae242fa2a40909eb2835">lineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e">CUpti_OpenAccConstructKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#86449c5baf4c4eda27860511e3b909c8">parentConstruct</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#02dd59b8058dae9addf082ee863155fb">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#1b2cf3c8327c2598a812b00d9d3fb246">threadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAcc.html#d43b316186c9c0eb67277d23410cdb25">version</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
The OpenACC activity API part uses a <a class="el" href="structCUpti__ActivityOpenAcc.html" title="The base activity record for OpenAcc records.">CUpti_ActivityOpenAcc</a> as a generic representation for any OpenACC activity. The 'kind' field is used to determine the specific activity kind, and from that the <a class="el" href="structCUpti__ActivityOpenAcc.html" title="The base activity record for OpenAcc records.">CUpti_ActivityOpenAcc</a> object can be cast to the specific OpenACC activity record type appropriate for that kind.<p>
Note that all OpenACC activity record types are padded and aligned to ensure that each member of the record is naturally aligned.<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3" title="The kinds of activity records.">CUpti_ActivityKind</a> </dd></dl>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="7f2f1ba8677db32397be3d1bcbf69d8d"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::async" ref="7f2f1ba8677db32397be3d1bcbf69d8d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAcc.html#7f2f1ba8677db32397be3d1bcbf69d8d">CUpti_ActivityOpenAcc::async</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Value of <a class="el" href="structCUpti__ActivityOpenAcc.html#7f2f1ba8677db32397be3d1bcbf69d8d">async()</a> clause of the corresponding directive 
</div>
</div><p>
<a class="anchor" name="fe0f9caadc974e006e594986cab625ba"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::asyncMap" ref="fe0f9caadc974e006e594986cab625ba" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAcc.html#fe0f9caadc974e006e594986cab625ba">CUpti_ActivityOpenAcc::asyncMap</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Internal asynchronous queue number used 
</div>
</div><p>
<a class="anchor" name="81d02af0a1a0c452cf06941885070b34"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::cuContextId" ref="81d02af0a1a0c452cf06941885070b34" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#81d02af0a1a0c452cf06941885070b34">CUpti_ActivityOpenAcc::cuContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA context id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="05595dd4ed260e4cde2cb3457e569377"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::cuDeviceId" ref="05595dd4ed260e4cde2cb3457e569377" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#05595dd4ed260e4cde2cb3457e569377">CUpti_ActivityOpenAcc::cuDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA device id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="2de64d3c200f75a7db15677956ecc966"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::cuProcessId" ref="2de64d3c200f75a7db15677956ecc966" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#2de64d3c200f75a7db15677956ecc966">CUpti_ActivityOpenAcc::cuProcessId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="176ab2a2c03001fc1ac89376cc7e6eaf"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::cuStreamId" ref="176ab2a2c03001fc1ac89376cc7e6eaf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#176ab2a2c03001fc1ac89376cc7e6eaf">CUpti_ActivityOpenAcc::cuStreamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA stream id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="34dd59dff4b6fb79749c6210163b40a9"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::cuThreadId" ref="34dd59dff4b6fb79749c6210163b40a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#34dd59dff4b6fb79749c6210163b40a9">CUpti_ActivityOpenAcc::cuThreadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the thread where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="0e4fcccc203e63f979d0f5c8ef65cd43"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::deviceNumber" ref="0e4fcccc203e63f979d0f5c8ef65cd43" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#0e4fcccc203e63f979d0f5c8ef65cd43">CUpti_ActivityOpenAcc::deviceNumber</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device number 
</div>
</div><p>
<a class="anchor" name="e5c173af41e879131c3317cba733279d"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::deviceType" ref="e5c173af41e879131c3317cba733279d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#e5c173af41e879131c3317cba733279d">CUpti_ActivityOpenAcc::deviceType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device type 
</div>
</div><p>
<a class="anchor" name="3fe92274275627fb28986e5ffc046d57"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::end" ref="3fe92274275627fb28986e5ffc046d57" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAcc.html#3fe92274275627fb28986e5ffc046d57">CUpti_ActivityOpenAcc::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI end timestamp 
</div>
</div><p>
<a class="anchor" name="509bf31ec7eb759fa779b447e1ed3f75"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::endLineNo" ref="509bf31ec7eb759fa779b447e1ed3f75" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#509bf31ec7eb759fa779b447e1ed3f75">CUpti_ActivityOpenAcc::endLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For an OpenACC construct, this contains the line number of the end of the construct. A zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="5f7b0689f352034596684eaf0d64f504"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::eventKind" ref="5f7b0689f352034596684eaf0d64f504" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a> <a class="el" href="structCUpti__ActivityOpenAcc.html#5f7b0689f352034596684eaf0d64f504">CUpti_ActivityOpenAcc::eventKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenACC event kind (<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717" title="The OpenAcc event kind for OpenAcc activity records.">CUpti_OpenAccEventKind</a>) </dd></dl>

</div>
</div><p>
<a class="anchor" name="e42271bc670e3c511ee286b4f574c863"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::externalId" ref="e42271bc670e3c511ee286b4f574c863" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#e42271bc670e3c511ee286b4f574c863">CUpti_ActivityOpenAcc::externalId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The OpenACC correlation ID. Valid only if deviceType is acc_device_nvidia. If not 0, it uniquely identifies this record. It is identical to the externalId in the preceeding external correlation record of type CUPTI_EXTERNAL_CORRELATION_KIND_OPENACC. 
</div>
</div><p>
<a class="anchor" name="237d817dec1fbda3ade3e62dc26d6437"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::funcEndLineNo" ref="237d817dec1fbda3ade3e62dc26d6437" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#237d817dec1fbda3ade3e62dc26d6437">CUpti_ActivityOpenAcc::funcEndLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The last line number of the function named in funcName. A zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="d6cbaf987363d22784d919a8a639b1f7"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::funcLineNo" ref="d6cbaf987363d22784d919a8a639b1f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#d6cbaf987363d22784d919a8a639b1f7">CUpti_ActivityOpenAcc::funcLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The line number of the first line of the function named in funcName. A zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="056897be910aa4b7f4399fcedf5f7848"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::implicit" ref="056897be910aa4b7f4399fcedf5f7848" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#056897be910aa4b7f4399fcedf5f7848">CUpti_ActivityOpenAcc::implicit</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
1 for any implicit event, such as an implicit wait at a synchronous data construct 0 otherwise 
</div>
</div><p>
<a class="anchor" name="a2b0df639616ce0b527408b450306f79"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::kind" ref="a2b0df639616ce0b527408b450306f79" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityOpenAcc.html#a2b0df639616ce0b527408b450306f79">CUpti_ActivityOpenAcc::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of this activity. 
</div>
</div><p>
<a class="anchor" name="e35c6ba06465ae242fa2a40909eb2835"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::lineNo" ref="e35c6ba06465ae242fa2a40909eb2835" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#e35c6ba06465ae242fa2a40909eb2835">CUpti_ActivityOpenAcc::lineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The line number of the directive or program construct or the starting line number of the OpenACC construct corresponding to the event. A zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="86449c5baf4c4eda27860511e3b909c8"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::parentConstruct" ref="86449c5baf4c4eda27860511e3b909c8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e">CUpti_OpenAccConstructKind</a> <a class="el" href="structCUpti__ActivityOpenAcc.html#86449c5baf4c4eda27860511e3b909c8">CUpti_ActivityOpenAcc::parentConstruct</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenACC parent construct kind (<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e" title="The OpenAcc parent construct kind for OpenAcc activity records.">CUpti_OpenAccConstructKind</a>)</dd></dl>
Note that for applications using PGI OpenACC runtime &lt; 16.1, this will always be CUPTI_OPENACC_CONSTRUCT_KIND_UNKNOWN. 
</div>
</div><p>
<a class="anchor" name="02dd59b8058dae9addf082ee863155fb"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::start" ref="02dd59b8058dae9addf082ee863155fb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAcc.html#02dd59b8058dae9addf082ee863155fb">CUpti_ActivityOpenAcc::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI start timestamp 
</div>
</div><p>
<a class="anchor" name="1b2cf3c8327c2598a812b00d9d3fb246"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::threadId" ref="1b2cf3c8327c2598a812b00d9d3fb246" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#1b2cf3c8327c2598a812b00d9d3fb246">CUpti_ActivityOpenAcc::threadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ThreadId 
</div>
</div><p>
<a class="anchor" name="d43b316186c9c0eb67277d23410cdb25"></a><!-- doxytag: member="CUpti_ActivityOpenAcc::version" ref="d43b316186c9c0eb67277d23410cdb25" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAcc.html#d43b316186c9c0eb67277d23410cdb25">CUpti_ActivityOpenAcc::version</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Version number 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
