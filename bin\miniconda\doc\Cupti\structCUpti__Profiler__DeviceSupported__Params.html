<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_DeviceSupported_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_DeviceSupported_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_DeviceSupported_Params" -->Params for cuptiProfilerDeviceSupported.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="d68ca321150e7bc3c84db9c907f7355a"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::architecture" ref="d68ca321150e7bc3c84db9c907f7355a" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#d68ca321150e7bc3c84db9c907f7355a">architecture</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] SUPPORTED if the device architecture level supports the Profiling API (Compute Capability &gt;= 7.0), UNSUPPORTED otherwise <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="9f89cbf5bfd929a933356d13a13de501"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::cmp" ref="9f89cbf5bfd929a933356d13a13de501" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#9f89cbf5bfd929a933356d13a13de501">cmp</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] SUPPORTED if not NVIDIA Crypto Mining Processors (CMP), UNSUPPORTED otherwise <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="37583e75ba89a506594a9ce4f2932221"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::confidentialCompute" ref="37583e75ba89a506594a9ce4f2932221" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#37583e75ba89a506594a9ce4f2932221">confidentialCompute</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] SUPPORTED if confidential compute is not enabled, UNSUPPORTED otherwise <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="3edec41549077cfbc87da9f5d2333e4b"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::cuDevice" ref="3edec41549077cfbc87da9f5d2333e4b" args="" -->
CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#3edec41549077cfbc87da9f5d2333e4b">cuDevice</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] if NULL, the current CUcontext is used <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="f5f609c65bfa8abdc0d0dbb3a3061ac4"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::isSupported" ref="f5f609c65bfa8abdc0d0dbb3a3061ac4" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#f5f609c65bfa8abdc0d0dbb3a3061ac4">isSupported</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] overall SUPPORTED / UNSUPPORTED flag representing whether Profiling and PC Sampling APIs work on the given device and configuration. SUPPORTED if all following flags are SUPPORTED, UNSUPPORTED otherwise. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="43b73ef08bbba02af58fd287d017d4b9"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::pPriv" ref="43b73ef08bbba02af58fd287d017d4b9" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#43b73ef08bbba02af58fd287d017d4b9">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="4fbf19767dc11f5056dad81998c96625"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::sli" ref="4fbf19767dc11f5056dad81998c96625" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#4fbf19767dc11f5056dad81998c96625">sli</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] SUPPORTED if SLI is not enabled, UNSUPPORTED otherwise <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="40450339c5074a440d352b68be6d961d"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::structSize" ref="40450339c5074a440d352b68be6d961d" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#40450339c5074a440d352b68be6d961d">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Must be CUpti_Profiler_DeviceSupported_Params_STRUCT_SIZE <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="40cd0bfb37cf7d9d00dba741dec4e58a"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::vGpu" ref="40cd0bfb37cf7d9d00dba741dec4e58a" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#40cd0bfb37cf7d9d00dba741dec4e58a">vGpu</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] SUPPORTED if vGPU is supported and profiling is enabled, DISABLED if profiling is supported but not enabled, UNSUPPORTED otherwise <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="680707a82015896bc7d3b517721e6034"></a><!-- doxytag: member="CUpti_Profiler_DeviceSupported_Params::wsl" ref="680707a82015896bc7d3b517721e6034" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ge9c7f68d6d33973878b56e39fb9cfa52">CUpti_Profiler_Support_Level</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#680707a82015896bc7d3b517721e6034">wsl</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] SUPPORTED if WSL supported, UNSUPPORTED otherwise <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
