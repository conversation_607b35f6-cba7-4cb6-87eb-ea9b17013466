<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_BeginSession_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_BeginSession_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_BeginSession_Params" -->Params for cuptiProfilerBeginSession.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="3ed440e4991c509e871bd6c38c11a69b"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::bDumpCounterDataInFile" ref="3ed440e4991c509e871bd6c38c11a69b" args="" -->
uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#3ed440e4991c509e871bd6c38c11a69b">bDumpCounterDataInFile</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] [optional] <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="6cbe6ce5fea2b09bdaf0a22a1c562d89"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::counterDataImageSize" ref="6cbe6ce5fea2b09bdaf0a22a1c562d89" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#6cbe6ce5fea2b09bdaf0a22a1c562d89">counterDataImageSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] size calculated from cuptiProfilerCounterDataImageCalculateSize <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="1712c6a4834d647ca20d204da810fc78"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::counterDataScratchBufferSize" ref="1712c6a4834d647ca20d204da810fc78" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#1712c6a4834d647ca20d204da810fc78">counterDataScratchBufferSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] size calculated from cuptiProfilerCounterDataImageInitializeScratchBuffer <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ace2f2f52ed397e873e18716a7bafe28"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::ctx" ref="ace2f2f52ed397e873e18716a7bafe28" args="" -->
CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#ace2f2f52ed397e873e18716a7bafe28">ctx</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] if NULL, the current CUcontext is used <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="aba56724442c1959f7a075a0e1b5a72b"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::maxLaunchesPerPass" ref="aba56724442c1959f7a075a0e1b5a72b" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#aba56724442c1959f7a075a0e1b5a72b">maxLaunchesPerPass</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Maximum number of kernel launches that can be recorded in a single pass; must be &gt;= maxRangesPerPass. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="1d2a72387e1edbcd8ed6eafb67975086"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::maxRangesPerPass" ref="1d2a72387e1edbcd8ed6eafb67975086" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#1d2a72387e1edbcd8ed6eafb67975086">maxRangesPerPass</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Maximum number of ranges that can be recorded in a single pass. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="7061112a59b3ec59c3f60261f41fb0f0"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::pCounterDataFilePath" ref="7061112a59b3ec59c3f60261f41fb0f0" args="" -->
const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#7061112a59b3ec59c3f60261f41fb0f0">pCounterDataFilePath</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] [optional] <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="b5502318f4afd407f15a800cece9f030"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::pCounterDataImage" ref="b5502318f4afd407f15a800cece9f030" args="" -->
uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#b5502318f4afd407f15a800cece9f030">pCounterDataImage</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] address of CounterDataImage <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="330585a7b6cef3912946bdc21cfaf922"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::pCounterDataScratchBuffer" ref="330585a7b6cef3912946bdc21cfaf922" args="" -->
uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#330585a7b6cef3912946bdc21cfaf922">pCounterDataScratchBuffer</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] address of CounterDataImage scratch buffer <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="a26c48169e271b780d7628cc31eadbc9"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::pPriv" ref="a26c48169e271b780d7628cc31eadbc9" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#a26c48169e271b780d7628cc31eadbc9">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="2762f636101d110ab5e40b0e78048372"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::range" ref="2762f636101d110ab5e40b0e78048372" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#ga6faef578b53da403b15878e05d1b395">CUpti_ProfilerRange</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#2762f636101d110ab5e40b0e78048372">range</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_ProfilerRange <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="7c368e06ef80b05b511897f437a8ea83"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::replayMode" ref="7c368e06ef80b05b511897f437a8ea83" args="" -->
<a class="el" href="group__CUPTI__PROFILER__API.html#gd6960f26de20f317a784ee565743e457">CUpti_ProfilerReplayMode</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#7c368e06ef80b05b511897f437a8ea83">replayMode</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_ProfilerReplayMode <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="2719af9f3c30462d924b8705f3c2ecf5"></a><!-- doxytag: member="CUpti_Profiler_BeginSession_Params::structSize" ref="2719af9f3c30462d924b8705f3c2ecf5" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__BeginSession__Params.html#2719af9f3c30462d924b8705f3c2ecf5">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_BeginSession_Params_STRUCT_SIZE <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
