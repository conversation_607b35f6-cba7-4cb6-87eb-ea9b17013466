{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["conda-package-streaming >=0.9.0", "python >=3.10,<3.11.0a0", "zstandard >=0.15"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-package-handling-2.4.0-py310haa95532_0", "files": ["Lib/site-packages/conda_package_handling-2.4.0.dist-info/AUTHORS.md", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/INSTALLER", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/LICENSE", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/METADATA", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/RECORD", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/REQUESTED", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/WHEEL", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/direct_url.json", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/entry_points.txt", "Lib/site-packages/conda_package_handling-2.4.0.dist-info/top_level.txt", "Lib/site-packages/conda_package_handling/__init__.py", "Lib/site-packages/conda_package_handling/__main__.py", "Lib/site-packages/conda_package_handling/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/api.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/cli.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/conda_fmt.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/interface.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/streaming.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/tarball.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/conda_package_handling/__pycache__/validate.cpython-310.pyc", "Lib/site-packages/conda_package_handling/api.py", "Lib/site-packages/conda_package_handling/cli.py", "Lib/site-packages/conda_package_handling/conda_fmt.py", "Lib/site-packages/conda_package_handling/exceptions.py", "Lib/site-packages/conda_package_handling/interface.py", "Lib/site-packages/conda_package_handling/streaming.py", "Lib/site-packages/conda_package_handling/tarball.py", "Lib/site-packages/conda_package_handling/utils.py", "Lib/site-packages/conda_package_handling/validate.py", "Scripts/cph-script.py", "Scripts/cph.exe"], "fn": "conda-package-handling-2.4.0-py310haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-package-handling-2.4.0-py310haa95532_0", "type": 1}, "md5": "2aef2377b0f84a34b6bc99944b234f58", "name": "conda-package-handling", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-package-handling-2.4.0-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::conda-package-handling==2.4.0=py310haa95532_0[md5=2aef2377b0f84a34b6bc99944b234f58]", "sha256": "cbf003d0650db3b1897ce5b25626943260000b64ca8786f1c9b4ed4304869cd2", "size": 304500, "subdir": "win-64", "timestamp": 1731369340000, "url": "https://repo.anaconda.com/pkgs/main/win-64/conda-package-handling-2.4.0-py310haa95532_0.conda", "version": "2.4.0"}