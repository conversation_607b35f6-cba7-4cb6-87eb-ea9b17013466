<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_CounterDataImage_CalculateSize_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_CounterDataImage_CalculateSize_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_CounterDataImage_CalculateSize_Params" -->Params for cuptiProfilerCounterDataImageCalculateSize.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="c4338dd6cbda228f6383465e156ac764"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateSize_Params::counterDataImageSize" ref="c4338dd6cbda228f6383465e156ac764" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#c4338dd6cbda228f6383465e156ac764">counterDataImageSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="4c61d978d675f51e8094818a2cf026e0"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateSize_Params::pOptions" ref="4c61d978d675f51e8094818a2cf026e0" args="" -->
const <br class="typebreak">
<a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html">CUpti_Profiler_CounterDataImageOptions</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#4c61d978d675f51e8094818a2cf026e0">pOptions</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Pointer to Counter Data Image Options <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="59aa3633a09400518d6f9336bab2b8a8"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateSize_Params::pPriv" ref="59aa3633a09400518d6f9336bab2b8a8" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#59aa3633a09400518d6f9336bab2b8a8">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="b194bf39ff44e2a695de3fddeff77e77"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateSize_Params::sizeofCounterDataImageOptions" ref="b194bf39ff44e2a695de3fddeff77e77" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#b194bf39ff44e2a695de3fddeff77e77">sizeofCounterDataImageOptions</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_CounterDataImageOptions_STRUCT_SIZE <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="5e9da316383bb055cc47b400d03b7c58"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_CalculateSize_Params::structSize" ref="5e9da316383bb055cc47b400d03b7c58" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#5e9da316383bb055cc47b400d03b7c58">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_CounterDataImage_CalculateSize_Params_STRUCT_SIZE <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
