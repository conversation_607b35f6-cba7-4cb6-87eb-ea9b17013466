<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel4 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel4 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel4" -->The activity record for a kernel (CUDA 9.0(with sm_70 support) onwards). (deprecated in CUDA 11.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#3167070ca5ac3c82dfcd0e37c06da438">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#4af602a0d1aad7d12840775b7c8c4650">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#d604a04491cfe5771fbc3978337a22d3">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#cfda7534ae14275cdfc5d0d22e15ac55">cacheConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#a38a2f171e8ada8373e5f785ecb62e87">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#61b625b5ad46c8e84dfe741187191263">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#027755b451c467fd9a4ebf6b139e0e50">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#71e90f37f3d0400236e2b9f86d2a6287">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#370a28e894f1aa22d44cfae6850ed3b6">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#105333252eb769a65bf72999bcdf1adf">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#b692f4068b76b91330233a14ce243d6c">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#f4e40b02b47ea216738318524d413048">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#f6fd2f107a07a799526b421fcd97a3b3">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#787b5f0e93c1143a3a18172005dd246f">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#70e44b06c5c6d5e03e43587bf6bced1e">isSharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#e838faf1f960f463a4a98531ad74a9aa">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#247ccacf787fb2a55c77d96687fb3ee7">launchType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#0019e20da584a302b95d7310075e2ce0">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#25b4acf0caeed888f082105f93a82736">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#3985681a4104f3d7b5e9c91330643612">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#d006e56fc6a027ab93154703d2f5ef6a">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#28fd56a6fec868f81723c3d07ca502e7">partitionedGlobalCacheExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#7e8f55639860b40dcd53249500dabd96">partitionedGlobalCacheRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#29126f718cd16493f1c9bddb6b7ed741">queued</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#e9573155f11831f3fcea6d960b5060f0">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#810aa4ec3ea45b137a75956b8ad26ffc">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#c871fea410ea952aa0c023dd3d17a3c6">sharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#8f29d931899104ce17327ca5bdc88cc8">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#d8e0d7c39287b6485f4e37932437529e">sharedMemoryExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#dfedc37bb31232afba143564acc07739">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#1e4c190643fb497f7af68cf943a137d0">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#49eee33e574f6a52424bd0d33d1e65d1">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#ddeaca33de73bc8721f6fcf10947c9e5">submitted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#dd927e9f135243f3033bb65cb36723bb">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel4.html#127c7773b8a51866e08502def3be32e5">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL). Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="3167070ca5ac3c82dfcd0e37c06da438"></a><!-- doxytag: member="CUpti_ActivityKernel4::blockX" ref="3167070ca5ac3c82dfcd0e37c06da438" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#3167070ca5ac3c82dfcd0e37c06da438">CUpti_ActivityKernel4::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="4af602a0d1aad7d12840775b7c8c4650"></a><!-- doxytag: member="CUpti_ActivityKernel4::blockY" ref="4af602a0d1aad7d12840775b7c8c4650" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#4af602a0d1aad7d12840775b7c8c4650">CUpti_ActivityKernel4::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="d604a04491cfe5771fbc3978337a22d3"></a><!-- doxytag: member="CUpti_ActivityKernel4::blockZ" ref="d604a04491cfe5771fbc3978337a22d3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#d604a04491cfe5771fbc3978337a22d3">CUpti_ActivityKernel4::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="cfda7534ae14275cdfc5d0d22e15ac55"></a><!-- doxytag: member="CUpti_ActivityKernel4::cacheConfig" ref="cfda7534ae14275cdfc5d0d22e15ac55" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityKernel4.html#cfda7534ae14275cdfc5d0d22e15ac55">CUpti_ActivityKernel4::cacheConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For devices with compute capability 7.0+ cacheConfig values are not updated in case field isSharedMemoryCarveoutRequested is set 
</div>
</div><p>
<a class="anchor" name="a38a2f171e8ada8373e5f785ecb62e87"></a><!-- doxytag: member="CUpti_ActivityKernel4::completed" ref="a38a2f171e8ada8373e5f785ecb62e87" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel4.html#a38a2f171e8ada8373e5f785ecb62e87">CUpti_ActivityKernel4::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="61b625b5ad46c8e84dfe741187191263"></a><!-- doxytag: member="CUpti_ActivityKernel4::contextId" ref="61b625b5ad46c8e84dfe741187191263" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel4.html#61b625b5ad46c8e84dfe741187191263">CUpti_ActivityKernel4::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="027755b451c467fd9a4ebf6b139e0e50"></a><!-- doxytag: member="CUpti_ActivityKernel4::correlationId" ref="027755b451c467fd9a4ebf6b139e0e50" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel4.html#027755b451c467fd9a4ebf6b139e0e50">CUpti_ActivityKernel4::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="71e90f37f3d0400236e2b9f86d2a6287"></a><!-- doxytag: member="CUpti_ActivityKernel4::deviceId" ref="71e90f37f3d0400236e2b9f86d2a6287" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel4.html#71e90f37f3d0400236e2b9f86d2a6287">CUpti_ActivityKernel4::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="370a28e894f1aa22d44cfae6850ed3b6"></a><!-- doxytag: member="CUpti_ActivityKernel4::dynamicSharedMemory" ref="370a28e894f1aa22d44cfae6850ed3b6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#370a28e894f1aa22d44cfae6850ed3b6">CUpti_ActivityKernel4::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="105333252eb769a65bf72999bcdf1adf"></a><!-- doxytag: member="CUpti_ActivityKernel4::end" ref="105333252eb769a65bf72999bcdf1adf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel4.html#105333252eb769a65bf72999bcdf1adf">CUpti_ActivityKernel4::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="dd927e9f135243f3033bb65cb36723bb"></a><!-- doxytag: member="CUpti_ActivityKernel4::executed" ref="dd927e9f135243f3033bb65cb36723bb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel4.html#dd927e9f135243f3033bb65cb36723bb">CUpti_ActivityKernel4::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="b692f4068b76b91330233a14ce243d6c"></a><!-- doxytag: member="CUpti_ActivityKernel4::gridId" ref="b692f4068b76b91330233a14ce243d6c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel4.html#b692f4068b76b91330233a14ce243d6c">CUpti_ActivityKernel4::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="f4e40b02b47ea216738318524d413048"></a><!-- doxytag: member="CUpti_ActivityKernel4::gridX" ref="f4e40b02b47ea216738318524d413048" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#f4e40b02b47ea216738318524d413048">CUpti_ActivityKernel4::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="f6fd2f107a07a799526b421fcd97a3b3"></a><!-- doxytag: member="CUpti_ActivityKernel4::gridY" ref="f6fd2f107a07a799526b421fcd97a3b3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#f6fd2f107a07a799526b421fcd97a3b3">CUpti_ActivityKernel4::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="787b5f0e93c1143a3a18172005dd246f"></a><!-- doxytag: member="CUpti_ActivityKernel4::gridZ" ref="787b5f0e93c1143a3a18172005dd246f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#787b5f0e93c1143a3a18172005dd246f">CUpti_ActivityKernel4::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="70e44b06c5c6d5e03e43587bf6bced1e"></a><!-- doxytag: member="CUpti_ActivityKernel4::isSharedMemoryCarveoutRequested" ref="70e44b06c5c6d5e03e43587bf6bced1e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel4.html#70e44b06c5c6d5e03e43587bf6bced1e">CUpti_ActivityKernel4::isSharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This indicates if CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT was updated for the kernel launch 
</div>
</div><p>
<a class="anchor" name="e838faf1f960f463a4a98531ad74a9aa"></a><!-- doxytag: member="CUpti_ActivityKernel4::kind" ref="e838faf1f960f463a4a98531ad74a9aa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel4.html#e838faf1f960f463a4a98531ad74a9aa">CUpti_ActivityKernel4::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="247ccacf787fb2a55c77d96687fb3ee7"></a><!-- doxytag: member="CUpti_ActivityKernel4::launchType" ref="247ccacf787fb2a55c77d96687fb3ee7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel4.html#247ccacf787fb2a55c77d96687fb3ee7">CUpti_ActivityKernel4::launchType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The indicates if the kernel was executed via a regular launch or via a single/multi device cooperative launch. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c" title="The type of the CUDA kernel launch.">CUpti_ActivityLaunchType</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="0019e20da584a302b95d7310075e2ce0"></a><!-- doxytag: member="CUpti_ActivityKernel4::localMemoryPerThread" ref="0019e20da584a302b95d7310075e2ce0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel4.html#0019e20da584a302b95d7310075e2ce0">CUpti_ActivityKernel4::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="25b4acf0caeed888f082105f93a82736"></a><!-- doxytag: member="CUpti_ActivityKernel4::localMemoryTotal" ref="25b4acf0caeed888f082105f93a82736" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel4.html#25b4acf0caeed888f082105f93a82736">CUpti_ActivityKernel4::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="3985681a4104f3d7b5e9c91330643612"></a><!-- doxytag: member="CUpti_ActivityKernel4::name" ref="3985681a4104f3d7b5e9c91330643612" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel4.html#3985681a4104f3d7b5e9c91330643612">CUpti_ActivityKernel4::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="d006e56fc6a027ab93154703d2f5ef6a"></a><!-- doxytag: member="CUpti_ActivityKernel4::padding" ref="d006e56fc6a027ab93154703d2f5ef6a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel4.html#d006e56fc6a027ab93154703d2f5ef6a">CUpti_ActivityKernel4::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="28fd56a6fec868f81723c3d07ca502e7"></a><!-- doxytag: member="CUpti_ActivityKernel4::partitionedGlobalCacheExecuted" ref="28fd56a6fec868f81723c3d07ca502e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel4.html#28fd56a6fec868f81723c3d07ca502e7">CUpti_ActivityKernel4::partitionedGlobalCacheExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching executed for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. Partitioned global caching can be automatically disabled if the occupancy requirement of the launch cannot support caching. 
</div>
</div><p>
<a class="anchor" name="7e8f55639860b40dcd53249500dabd96"></a><!-- doxytag: member="CUpti_ActivityKernel4::partitionedGlobalCacheRequested" ref="7e8f55639860b40dcd53249500dabd96" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel4.html#7e8f55639860b40dcd53249500dabd96">CUpti_ActivityKernel4::partitionedGlobalCacheRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching requested for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. 
</div>
</div><p>
<a class="anchor" name="29126f718cd16493f1c9bddb6b7ed741"></a><!-- doxytag: member="CUpti_ActivityKernel4::queued" ref="29126f718cd16493f1c9bddb6b7ed741" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel4.html#29126f718cd16493f1c9bddb6b7ed741">CUpti_ActivityKernel4::queued</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the kernel is queued up in the command buffer, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the queued time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection.<p>
Command buffer is a buffer written by CUDA driver to send commands like kernel launch, memory copy etc to the GPU. All launches of CUDA kernels are asynchrnous with respect to the host, the host requests the launch by writing commands into the command buffer, then returns without checking the GPU's progress. 
</div>
</div><p>
<a class="anchor" name="e9573155f11831f3fcea6d960b5060f0"></a><!-- doxytag: member="CUpti_ActivityKernel4::registersPerThread" ref="e9573155f11831f3fcea6d960b5060f0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel4.html#e9573155f11831f3fcea6d960b5060f0">CUpti_ActivityKernel4::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="127c7773b8a51866e08502def3be32e5"></a><!-- doxytag: member="CUpti_ActivityKernel4::requested" ref="127c7773b8a51866e08502def3be32e5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel4.html#127c7773b8a51866e08502def3be32e5">CUpti_ActivityKernel4::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="810aa4ec3ea45b137a75956b8ad26ffc"></a><!-- doxytag: member="CUpti_ActivityKernel4::reserved0" ref="810aa4ec3ea45b137a75956b8ad26ffc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel4.html#810aa4ec3ea45b137a75956b8ad26ffc">CUpti_ActivityKernel4::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="c871fea410ea952aa0c023dd3d17a3c6"></a><!-- doxytag: member="CUpti_ActivityKernel4::sharedMemoryCarveoutRequested" ref="c871fea410ea952aa0c023dd3d17a3c6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel4.html#c871fea410ea952aa0c023dd3d17a3c6">CUpti_ActivityKernel4::sharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory carveout value requested for the function in percentage of the total resource. The value will be updated only if field isSharedMemoryCarveoutRequested is set. 
</div>
</div><p>
<a class="anchor" name="8f29d931899104ce17327ca5bdc88cc8"></a><!-- doxytag: member="CUpti_ActivityKernel4::sharedMemoryConfig" ref="8f29d931899104ce17327ca5bdc88cc8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel4.html#8f29d931899104ce17327ca5bdc88cc8">CUpti_ActivityKernel4::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="d8e0d7c39287b6485f4e37932437529e"></a><!-- doxytag: member="CUpti_ActivityKernel4::sharedMemoryExecuted" ref="d8e0d7c39287b6485f4e37932437529e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel4.html#d8e0d7c39287b6485f4e37932437529e">CUpti_ActivityKernel4::sharedMemoryExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory size set by the driver. 
</div>
</div><p>
<a class="anchor" name="dfedc37bb31232afba143564acc07739"></a><!-- doxytag: member="CUpti_ActivityKernel4::start" ref="dfedc37bb31232afba143564acc07739" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel4.html#dfedc37bb31232afba143564acc07739">CUpti_ActivityKernel4::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="1e4c190643fb497f7af68cf943a137d0"></a><!-- doxytag: member="CUpti_ActivityKernel4::staticSharedMemory" ref="1e4c190643fb497f7af68cf943a137d0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel4.html#1e4c190643fb497f7af68cf943a137d0">CUpti_ActivityKernel4::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="49eee33e574f6a52424bd0d33d1e65d1"></a><!-- doxytag: member="CUpti_ActivityKernel4::streamId" ref="49eee33e574f6a52424bd0d33d1e65d1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel4.html#49eee33e574f6a52424bd0d33d1e65d1">CUpti_ActivityKernel4::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="ddeaca33de73bc8721f6fcf10947c9e5"></a><!-- doxytag: member="CUpti_ActivityKernel4::submitted" ref="ddeaca33de73bc8721f6fcf10947c9e5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel4.html#ddeaca33de73bc8721f6fcf10947c9e5">CUpti_ActivityKernel4::submitted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the command buffer containing the kernel launch is submitted to the GPU, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the submitted time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
