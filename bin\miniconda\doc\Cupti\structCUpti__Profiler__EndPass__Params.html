<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_EndPass_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_EndPass_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_EndPass_Params" -->Params for cuptiProfilerEndPass.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="690480d5291940ff7d4def7e7f3b50b3"></a><!-- doxytag: member="CUpti_Profiler_EndPass_Params::allPassesSubmitted" ref="690480d5291940ff7d4def7e7f3b50b3" args="" -->
uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EndPass__Params.html#690480d5291940ff7d4def7e7f3b50b3">allPassesSubmitted</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] becomes true when the last pass has been queued to the GPU <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="2dd6da8a0094da5ab474c10265d1ce3a"></a><!-- doxytag: member="CUpti_Profiler_EndPass_Params::ctx" ref="2dd6da8a0094da5ab474c10265d1ce3a" args="" -->
CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EndPass__Params.html#2dd6da8a0094da5ab474c10265d1ce3a">ctx</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] if NULL, the current CUcontext is used <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EndPass__Params.html#bd1d93bc5460ad132990adefda6cfcf2">passIndex</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] The targetNestingLevel that will be collected by the *next* BeginPass.  <a href="#bd1d93bc5460ad132990adefda6cfcf2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="fbda0269ab05f8a5698495f842c24d0d"></a><!-- doxytag: member="CUpti_Profiler_EndPass_Params::pPriv" ref="fbda0269ab05f8a5698495f842c24d0d" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EndPass__Params.html#fbda0269ab05f8a5698495f842c24d0d">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="f1ba39050dd671887899a7bf4405be20"></a><!-- doxytag: member="CUpti_Profiler_EndPass_Params::structSize" ref="f1ba39050dd671887899a7bf4405be20" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__EndPass__Params.html#f1ba39050dd671887899a7bf4405be20">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_EndPass_Params_STRUCT_SIZE <br></td></tr>
</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="bd1d93bc5460ad132990adefda6cfcf2"></a><!-- doxytag: member="CUpti_Profiler_EndPass_Params::passIndex" ref="bd1d93bc5460ad132990adefda6cfcf2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__Profiler__EndPass__Params.html#bd1d93bc5460ad132990adefda6cfcf2">CUpti_Profiler_EndPass_Params::passIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[out] The passIndex that will be collected by the *next* BeginPass 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
