{"arch": "x86_64", "build": "h0ff8eda_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["bzip2 >=1.0.8,<2.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "zlib >=1.2.13,<1.3.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\pcre2-10.42-h0ff8eda_1", "files": ["Library/bin/pcre2-8.dll", "Library/bin/pcre2-config", "Library/bin/pcre2-posix.dll", "Library/bin/pcre2_jit_test.exe", "Library/bin/pcre2grep.exe", "Library/bin/pcre2posix_test.exe", "Library/bin/pcre2test.exe", "Library/cmake/pcre2-config-version.cmake", "Library/cmake/pcre2-config.cmake", "Library/include/pcre2.h", "Library/include/pcre2posix.h", "Library/lib/pcre2-8-static.lib", "Library/lib/pcre2-8.lib", "Library/lib/pcre2-posix-static.lib", "Library/lib/pcre2-posix.lib", "Library/lib/pkgconfig/libpcre2-8.pc", "Library/lib/pkgconfig/libpcre2-posix.pc", "Library/man/man1/pcre2-config.1", "Library/man/man1/pcre2grep.1", "Library/man/man1/pcre2test.1", "Library/man/man3/pcre2.3", "Library/man/man3/pcre2_callout_enumerate.3", "Library/man/man3/pcre2_code_copy.3", "Library/man/man3/pcre2_code_copy_with_tables.3", "Library/man/man3/pcre2_code_free.3", "Library/man/man3/pcre2_compile.3", "Library/man/man3/pcre2_compile_context_copy.3", "Library/man/man3/pcre2_compile_context_create.3", "Library/man/man3/pcre2_compile_context_free.3", "Library/man/man3/pcre2_config.3", "Library/man/man3/pcre2_convert_context_copy.3", "Library/man/man3/pcre2_convert_context_create.3", "Library/man/man3/pcre2_convert_context_free.3", "Library/man/man3/pcre2_converted_pattern_free.3", "Library/man/man3/pcre2_dfa_match.3", "Library/man/man3/pcre2_general_context_copy.3", "Library/man/man3/pcre2_general_context_create.3", "Library/man/man3/pcre2_general_context_free.3", "Library/man/man3/pcre2_get_error_message.3", "Library/man/man3/pcre2_get_mark.3", "Library/man/man3/pcre2_get_match_data_size.3", "Library/man/man3/pcre2_get_ovector_count.3", "Library/man/man3/pcre2_get_ovector_pointer.3", "Library/man/man3/pcre2_get_startchar.3", "Library/man/man3/pcre2_jit_compile.3", "Library/man/man3/pcre2_jit_free_unused_memory.3", "Library/man/man3/pcre2_jit_match.3", "Library/man/man3/pcre2_jit_stack_assign.3", "Library/man/man3/pcre2_jit_stack_create.3", "Library/man/man3/pcre2_jit_stack_free.3", "Library/man/man3/pcre2_maketables.3", "Library/man/man3/pcre2_maketables_free.3", "Library/man/man3/pcre2_match.3", "Library/man/man3/pcre2_match_context_copy.3", "Library/man/man3/pcre2_match_context_create.3", "Library/man/man3/pcre2_match_context_free.3", "Library/man/man3/pcre2_match_data_create.3", "Library/man/man3/pcre2_match_data_create_from_pattern.3", "Library/man/man3/pcre2_match_data_free.3", "Library/man/man3/pcre2_pattern_convert.3", "Library/man/man3/pcre2_pattern_info.3", "Library/man/man3/pcre2_serialize_decode.3", "Library/man/man3/pcre2_serialize_encode.3", "Library/man/man3/pcre2_serialize_free.3", "Library/man/man3/pcre2_serialize_get_number_of_codes.3", "Library/man/man3/pcre2_set_bsr.3", "Library/man/man3/pcre2_set_callout.3", "Library/man/man3/pcre2_set_character_tables.3", "Library/man/man3/pcre2_set_compile_extra_options.3", "Library/man/man3/pcre2_set_compile_recursion_guard.3", "Library/man/man3/pcre2_set_depth_limit.3", "Library/man/man3/pcre2_set_glob_escape.3", "Library/man/man3/pcre2_set_glob_separator.3", "Library/man/man3/pcre2_set_heap_limit.3", "Library/man/man3/pcre2_set_match_limit.3", "Library/man/man3/pcre2_set_max_pattern_length.3", "Library/man/man3/pcre2_set_newline.3", "Library/man/man3/pcre2_set_offset_limit.3", "Library/man/man3/pcre2_set_parens_nest_limit.3", "Library/man/man3/pcre2_set_recursion_limit.3", "Library/man/man3/pcre2_set_recursion_memory_management.3", "Library/man/man3/pcre2_set_substitute_callout.3", "Library/man/man3/pcre2_substitute.3", "Library/man/man3/pcre2_substring_copy_byname.3", "Library/man/man3/pcre2_substring_copy_bynumber.3", "Library/man/man3/pcre2_substring_free.3", "Library/man/man3/pcre2_substring_get_byname.3", "Library/man/man3/pcre2_substring_get_bynumber.3", "Library/man/man3/pcre2_substring_length_byname.3", "Library/man/man3/pcre2_substring_length_bynumber.3", "Library/man/man3/pcre2_substring_list_free.3", "Library/man/man3/pcre2_substring_list_get.3", "Library/man/man3/pcre2_substring_nametable_scan.3", "Library/man/man3/pcre2_substring_number_from_name.3", "Library/man/man3/pcre2api.3", "Library/man/man3/pcre2build.3", "Library/man/man3/pcre2callout.3", "Library/man/man3/pcre2compat.3", "Library/man/man3/pcre2convert.3", "Library/man/man3/pcre2demo.3", "Library/man/man3/pcre2jit.3", "Library/man/man3/pcre2limits.3", "Library/man/man3/pcre2matching.3", "Library/man/man3/pcre2partial.3", "Library/man/man3/pcre2pattern.3", "Library/man/man3/pcre2perform.3", "Library/man/man3/pcre2posix.3", "Library/man/man3/pcre2sample.3", "Library/man/man3/pcre2serialize.3", "Library/man/man3/pcre2syntax.3", "Library/man/man3/pcre2unicode.3", "Library/share/doc/pcre2/html/index.html", "Library/share/doc/pcre2/html/pcre2-config.html", "Library/share/doc/pcre2/html/pcre2.html", "Library/share/doc/pcre2/html/pcre2_callout_enumerate.html", "Library/share/doc/pcre2/html/pcre2_code_copy.html", "Library/share/doc/pcre2/html/pcre2_code_copy_with_tables.html", "Library/share/doc/pcre2/html/pcre2_code_free.html", "Library/share/doc/pcre2/html/pcre2_compile.html", "Library/share/doc/pcre2/html/pcre2_compile_context_copy.html", "Library/share/doc/pcre2/html/pcre2_compile_context_create.html", "Library/share/doc/pcre2/html/pcre2_compile_context_free.html", "Library/share/doc/pcre2/html/pcre2_config.html", "Library/share/doc/pcre2/html/pcre2_convert_context_copy.html", "Library/share/doc/pcre2/html/pcre2_convert_context_create.html", "Library/share/doc/pcre2/html/pcre2_convert_context_free.html", "Library/share/doc/pcre2/html/pcre2_converted_pattern_free.html", "Library/share/doc/pcre2/html/pcre2_dfa_match.html", "Library/share/doc/pcre2/html/pcre2_general_context_copy.html", "Library/share/doc/pcre2/html/pcre2_general_context_create.html", "Library/share/doc/pcre2/html/pcre2_general_context_free.html", "Library/share/doc/pcre2/html/pcre2_get_error_message.html", "Library/share/doc/pcre2/html/pcre2_get_mark.html", "Library/share/doc/pcre2/html/pcre2_get_match_data_size.html", "Library/share/doc/pcre2/html/pcre2_get_ovector_count.html", "Library/share/doc/pcre2/html/pcre2_get_ovector_pointer.html", "Library/share/doc/pcre2/html/pcre2_get_startchar.html", "Library/share/doc/pcre2/html/pcre2_jit_compile.html", "Library/share/doc/pcre2/html/pcre2_jit_free_unused_memory.html", "Library/share/doc/pcre2/html/pcre2_jit_match.html", "Library/share/doc/pcre2/html/pcre2_jit_stack_assign.html", "Library/share/doc/pcre2/html/pcre2_jit_stack_create.html", "Library/share/doc/pcre2/html/pcre2_jit_stack_free.html", "Library/share/doc/pcre2/html/pcre2_maketables.html", "Library/share/doc/pcre2/html/pcre2_maketables_free.html", "Library/share/doc/pcre2/html/pcre2_match.html", "Library/share/doc/pcre2/html/pcre2_match_context_copy.html", "Library/share/doc/pcre2/html/pcre2_match_context_create.html", "Library/share/doc/pcre2/html/pcre2_match_context_free.html", "Library/share/doc/pcre2/html/pcre2_match_data_create.html", "Library/share/doc/pcre2/html/pcre2_match_data_create_from_pattern.html", "Library/share/doc/pcre2/html/pcre2_match_data_free.html", "Library/share/doc/pcre2/html/pcre2_pattern_convert.html", "Library/share/doc/pcre2/html/pcre2_pattern_info.html", "Library/share/doc/pcre2/html/pcre2_serialize_decode.html", "Library/share/doc/pcre2/html/pcre2_serialize_encode.html", "Library/share/doc/pcre2/html/pcre2_serialize_free.html", "Library/share/doc/pcre2/html/pcre2_serialize_get_number_of_codes.html", "Library/share/doc/pcre2/html/pcre2_set_bsr.html", "Library/share/doc/pcre2/html/pcre2_set_callout.html", "Library/share/doc/pcre2/html/pcre2_set_character_tables.html", "Library/share/doc/pcre2/html/pcre2_set_compile_extra_options.html", "Library/share/doc/pcre2/html/pcre2_set_compile_recursion_guard.html", "Library/share/doc/pcre2/html/pcre2_set_depth_limit.html", "Library/share/doc/pcre2/html/pcre2_set_glob_escape.html", "Library/share/doc/pcre2/html/pcre2_set_glob_separator.html", "Library/share/doc/pcre2/html/pcre2_set_heap_limit.html", "Library/share/doc/pcre2/html/pcre2_set_match_limit.html", "Library/share/doc/pcre2/html/pcre2_set_max_pattern_length.html", "Library/share/doc/pcre2/html/pcre2_set_newline.html", "Library/share/doc/pcre2/html/pcre2_set_offset_limit.html", "Library/share/doc/pcre2/html/pcre2_set_parens_nest_limit.html", "Library/share/doc/pcre2/html/pcre2_set_recursion_limit.html", "Library/share/doc/pcre2/html/pcre2_set_recursion_memory_management.html", "Library/share/doc/pcre2/html/pcre2_set_substitute_callout.html", "Library/share/doc/pcre2/html/pcre2_substitute.html", "Library/share/doc/pcre2/html/pcre2_substring_copy_byname.html", "Library/share/doc/pcre2/html/pcre2_substring_copy_bynumber.html", "Library/share/doc/pcre2/html/pcre2_substring_free.html", "Library/share/doc/pcre2/html/pcre2_substring_get_byname.html", "Library/share/doc/pcre2/html/pcre2_substring_get_bynumber.html", "Library/share/doc/pcre2/html/pcre2_substring_length_byname.html", "Library/share/doc/pcre2/html/pcre2_substring_length_bynumber.html", "Library/share/doc/pcre2/html/pcre2_substring_list_free.html", "Library/share/doc/pcre2/html/pcre2_substring_list_get.html", "Library/share/doc/pcre2/html/pcre2_substring_nametable_scan.html", "Library/share/doc/pcre2/html/pcre2_substring_number_from_name.html", "Library/share/doc/pcre2/html/pcre2api.html", "Library/share/doc/pcre2/html/pcre2build.html", "Library/share/doc/pcre2/html/pcre2callout.html", "Library/share/doc/pcre2/html/pcre2compat.html", "Library/share/doc/pcre2/html/pcre2convert.html", "Library/share/doc/pcre2/html/pcre2demo.html", "Library/share/doc/pcre2/html/pcre2grep.html", "Library/share/doc/pcre2/html/pcre2jit.html", "Library/share/doc/pcre2/html/pcre2limits.html", "Library/share/doc/pcre2/html/pcre2matching.html", "Library/share/doc/pcre2/html/pcre2partial.html", "Library/share/doc/pcre2/html/pcre2pattern.html", "Library/share/doc/pcre2/html/pcre2perform.html", "Library/share/doc/pcre2/html/pcre2posix.html", "Library/share/doc/pcre2/html/pcre2sample.html", "Library/share/doc/pcre2/html/pcre2serialize.html", "Library/share/doc/pcre2/html/pcre2syntax.html", "Library/share/doc/pcre2/html/pcre2test.html", "Library/share/doc/pcre2/html/pcre2unicode.html"], "fn": "pcre2-10.42-h0ff8eda_1.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\pcre2-10.42-h0ff8eda_1", "type": 1}, "md5": "c09acf9bd80c5a76f5315108b9a34ad5", "name": "pcre2", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\pcre2-10.42-h0ff8eda_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/pcre2-config", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_e8pfur1r4_/croot/pcre2_1714657639286/_h_env", "sha256": "14e3db8bb64f0ea2b6bd045bff9fe7099e74401063678103f007e508cab069f8", "sha256_in_prefix": "ecf932f3a1ae277125221b72cc9fac30f8e0c522fab8276290ae89513ebf0c6c", "size_in_bytes": 2346}, {"_path": "Library/lib/pkgconfig/libpcre2-8.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_e8pfur1r4_/croot/pcre2_1714657639286/_h_env", "sha256": "9b4a004a7f02e6446cbdf0e03d4ac356c0d698f4c4034b2e4cf5f1af76811ca5", "sha256_in_prefix": "5b46b5174bff0f4918e0fe3c49c8b152021f8da9ec6f972edc0e6fb2e3f96b5b", "size_in_bytes": 404}, {"_path": "Library/lib/pkgconfig/libpcre2-posix.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_e8pfur1r4_/croot/pcre2_1714657639286/_h_env", "sha256": "06e569243cca4bcec0c58cd8782d5d66280ad02ece5931e197c38c016f2efb67", "sha256_in_prefix": "9ef80204b7937aed03f507974e8ea5d9809f225b3833e6164ae2cf0d7e0838a4", "size_in_bytes": 373}], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::pcre2==10.42=h0ff8eda_1[md5=c09acf9bd80c5a76f5315108b9a34ad5]", "sha256": "dee5636e3d5b74cd6616330602a919188b2031b725b156f9619cf6ff6ae70582", "size": 745279, "subdir": "win-64", "timestamp": 1714657817000, "url": "https://repo.anaconda.com/pkgs/main/win-64/pcre2-10.42-h0ff8eda_1.conda", "version": "10.42"}