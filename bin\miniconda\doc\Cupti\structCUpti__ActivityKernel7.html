<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel7 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel7 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel7" -->The activity record for kernel. (deprecated in CUDA 11.8).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#a18b40e9973894fc043da5803a21d19b">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#7f49212be7ec36092fac6652f7aec432">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#70d8420f11e25e92c19a0fabcd8de717">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#3a7f2c68e1e2e60f2b4d048779a96768">cacheConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#6ecb807c8e48852cec46c08f845409f9">channelID</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUpti_ChannelType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#1b0dab60670b8fc8fd06c17a857bd383">channelType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#67233708cd369fcf514868ccd8d2446a">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#39e86b623aed7aa660b6fe1f2d67e61d">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#d951fba194d863142500e4fae13f0a04">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#5d85fa82fa7b75629fd28a1b262b16ce">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#9ca52f620088f6b8b897a405517addd4">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#37b75fa3484590e7dee6638cef67574e">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#20ac925a7feca9355e6202e61499b486">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#87186dc31f07d335978371bc11fe6dca">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#fffa63cea86d290cedd37a04f52e1fee">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#737db85290d9906f99ac0e9b12aec652">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#f9ebdf3be7dda5f53805915611cf0954">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#91fa04a2278c1088e55f18fd71a5d0fd">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#4658d1a5cc6be67d9657c22fa58f5531">isSharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#2f72b863a4efd82991df90064ac6e006">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#b9a8a4ed9d4bb056483a5a1b41977b2e">launchType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#756b5088d3b1384de0252c9419a82db0">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#9be00dece5e343f16a1e876c0044d426">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#e64321d051a4d89c07c7c9235463e18b">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUaccessPolicyWindow *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#baeaa887ff076807205823fe4e87c6b4">pAccessPolicyWindow</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#71fcb028839bad4c9981a3cc5e350b3a">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#0098287b94ed1b7b430a21302ecd3d6d">partitionedGlobalCacheExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#056376e67dfebdea78793f1a35b89fa2">partitionedGlobalCacheRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#c6c6aa3362ad8ef726769ba125663212">queued</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#d90ea5041a5d20cba239929aa4fe9873">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#e905e9efd17ee5b55500427caef7b95c">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#3148147d369042966914d736ae2e8194">sharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#aa84f40c516afd4af1f9007dc9a13113">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#3e5917bc600004fad95cf981e263e914">sharedMemoryExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#6d1c66d6fa75de8061b0a60124562921">shmemLimitConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#75b55803d48aba6f847b4d2bbfb39e87">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#0282ba3b1063bc3cc74c9c28d63e833d">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#85180e01af817733bc98fb8763dd377d">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#f9ccef6805b79f1d4ecda318577245d4">submitted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#c99848bbc5ac89558f18ffdc00bf06a3">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel7.html#141f3146852da54f164003fa8c8195ab">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="a18b40e9973894fc043da5803a21d19b"></a><!-- doxytag: member="CUpti_ActivityKernel7::blockX" ref="a18b40e9973894fc043da5803a21d19b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#a18b40e9973894fc043da5803a21d19b">CUpti_ActivityKernel7::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="7f49212be7ec36092fac6652f7aec432"></a><!-- doxytag: member="CUpti_ActivityKernel7::blockY" ref="7f49212be7ec36092fac6652f7aec432" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#7f49212be7ec36092fac6652f7aec432">CUpti_ActivityKernel7::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="70d8420f11e25e92c19a0fabcd8de717"></a><!-- doxytag: member="CUpti_ActivityKernel7::blockZ" ref="70d8420f11e25e92c19a0fabcd8de717" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#70d8420f11e25e92c19a0fabcd8de717">CUpti_ActivityKernel7::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="3a7f2c68e1e2e60f2b4d048779a96768"></a><!-- doxytag: member="CUpti_ActivityKernel7::cacheConfig" ref="3a7f2c68e1e2e60f2b4d048779a96768" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityKernel7.html#3a7f2c68e1e2e60f2b4d048779a96768">CUpti_ActivityKernel7::cacheConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For devices with compute capability 7.0+ cacheConfig values are not updated in case field isSharedMemoryCarveoutRequested is set 
</div>
</div><p>
<a class="anchor" name="6ecb807c8e48852cec46c08f845409f9"></a><!-- doxytag: member="CUpti_ActivityKernel7::channelID" ref="6ecb807c8e48852cec46c08f845409f9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#6ecb807c8e48852cec46c08f845409f9">CUpti_ActivityKernel7::channelID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the HW channel on which the kernel is launched. 
</div>
</div><p>
<a class="anchor" name="1b0dab60670b8fc8fd06c17a857bd383"></a><!-- doxytag: member="CUpti_ActivityKernel7::channelType" ref="1b0dab60670b8fc8fd06c17a857bd383" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUpti_ChannelType <a class="el" href="structCUpti__ActivityKernel7.html#1b0dab60670b8fc8fd06c17a857bd383">CUpti_ActivityKernel7::channelType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the channel 
</div>
</div><p>
<a class="anchor" name="67233708cd369fcf514868ccd8d2446a"></a><!-- doxytag: member="CUpti_ActivityKernel7::completed" ref="67233708cd369fcf514868ccd8d2446a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel7.html#67233708cd369fcf514868ccd8d2446a">CUpti_ActivityKernel7::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="39e86b623aed7aa660b6fe1f2d67e61d"></a><!-- doxytag: member="CUpti_ActivityKernel7::contextId" ref="39e86b623aed7aa660b6fe1f2d67e61d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#39e86b623aed7aa660b6fe1f2d67e61d">CUpti_ActivityKernel7::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="d951fba194d863142500e4fae13f0a04"></a><!-- doxytag: member="CUpti_ActivityKernel7::correlationId" ref="d951fba194d863142500e4fae13f0a04" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#d951fba194d863142500e4fae13f0a04">CUpti_ActivityKernel7::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="5d85fa82fa7b75629fd28a1b262b16ce"></a><!-- doxytag: member="CUpti_ActivityKernel7::deviceId" ref="5d85fa82fa7b75629fd28a1b262b16ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#5d85fa82fa7b75629fd28a1b262b16ce">CUpti_ActivityKernel7::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="9ca52f620088f6b8b897a405517addd4"></a><!-- doxytag: member="CUpti_ActivityKernel7::dynamicSharedMemory" ref="9ca52f620088f6b8b897a405517addd4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#9ca52f620088f6b8b897a405517addd4">CUpti_ActivityKernel7::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="37b75fa3484590e7dee6638cef67574e"></a><!-- doxytag: member="CUpti_ActivityKernel7::end" ref="37b75fa3484590e7dee6638cef67574e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel7.html#37b75fa3484590e7dee6638cef67574e">CUpti_ActivityKernel7::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="c99848bbc5ac89558f18ffdc00bf06a3"></a><!-- doxytag: member="CUpti_ActivityKernel7::executed" ref="c99848bbc5ac89558f18ffdc00bf06a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel7.html#c99848bbc5ac89558f18ffdc00bf06a3">CUpti_ActivityKernel7::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="20ac925a7feca9355e6202e61499b486"></a><!-- doxytag: member="CUpti_ActivityKernel7::graphId" ref="20ac925a7feca9355e6202e61499b486" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#20ac925a7feca9355e6202e61499b486">CUpti_ActivityKernel7::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="87186dc31f07d335978371bc11fe6dca"></a><!-- doxytag: member="CUpti_ActivityKernel7::graphNodeId" ref="87186dc31f07d335978371bc11fe6dca" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel7.html#87186dc31f07d335978371bc11fe6dca">CUpti_ActivityKernel7::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="fffa63cea86d290cedd37a04f52e1fee"></a><!-- doxytag: member="CUpti_ActivityKernel7::gridId" ref="fffa63cea86d290cedd37a04f52e1fee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel7.html#fffa63cea86d290cedd37a04f52e1fee">CUpti_ActivityKernel7::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="737db85290d9906f99ac0e9b12aec652"></a><!-- doxytag: member="CUpti_ActivityKernel7::gridX" ref="737db85290d9906f99ac0e9b12aec652" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#737db85290d9906f99ac0e9b12aec652">CUpti_ActivityKernel7::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="f9ebdf3be7dda5f53805915611cf0954"></a><!-- doxytag: member="CUpti_ActivityKernel7::gridY" ref="f9ebdf3be7dda5f53805915611cf0954" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#f9ebdf3be7dda5f53805915611cf0954">CUpti_ActivityKernel7::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="91fa04a2278c1088e55f18fd71a5d0fd"></a><!-- doxytag: member="CUpti_ActivityKernel7::gridZ" ref="91fa04a2278c1088e55f18fd71a5d0fd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#91fa04a2278c1088e55f18fd71a5d0fd">CUpti_ActivityKernel7::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="4658d1a5cc6be67d9657c22fa58f5531"></a><!-- doxytag: member="CUpti_ActivityKernel7::isSharedMemoryCarveoutRequested" ref="4658d1a5cc6be67d9657c22fa58f5531" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel7.html#4658d1a5cc6be67d9657c22fa58f5531">CUpti_ActivityKernel7::isSharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This indicates if CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT was updated for the kernel launch 
</div>
</div><p>
<a class="anchor" name="2f72b863a4efd82991df90064ac6e006"></a><!-- doxytag: member="CUpti_ActivityKernel7::kind" ref="2f72b863a4efd82991df90064ac6e006" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel7.html#2f72b863a4efd82991df90064ac6e006">CUpti_ActivityKernel7::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="b9a8a4ed9d4bb056483a5a1b41977b2e"></a><!-- doxytag: member="CUpti_ActivityKernel7::launchType" ref="b9a8a4ed9d4bb056483a5a1b41977b2e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel7.html#b9a8a4ed9d4bb056483a5a1b41977b2e">CUpti_ActivityKernel7::launchType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The indicates if the kernel was executed via a regular launch or via a single/multi device cooperative launch. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c" title="The type of the CUDA kernel launch.">CUpti_ActivityLaunchType</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="756b5088d3b1384de0252c9419a82db0"></a><!-- doxytag: member="CUpti_ActivityKernel7::localMemoryPerThread" ref="756b5088d3b1384de0252c9419a82db0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#756b5088d3b1384de0252c9419a82db0">CUpti_ActivityKernel7::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="9be00dece5e343f16a1e876c0044d426"></a><!-- doxytag: member="CUpti_ActivityKernel7::localMemoryTotal" ref="9be00dece5e343f16a1e876c0044d426" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#9be00dece5e343f16a1e876c0044d426">CUpti_ActivityKernel7::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="e64321d051a4d89c07c7c9235463e18b"></a><!-- doxytag: member="CUpti_ActivityKernel7::name" ref="e64321d051a4d89c07c7c9235463e18b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel7.html#e64321d051a4d89c07c7c9235463e18b">CUpti_ActivityKernel7::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="baeaa887ff076807205823fe4e87c6b4"></a><!-- doxytag: member="CUpti_ActivityKernel7::pAccessPolicyWindow" ref="baeaa887ff076807205823fe4e87c6b4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUaccessPolicyWindow* <a class="el" href="structCUpti__ActivityKernel7.html#baeaa887ff076807205823fe4e87c6b4">CUpti_ActivityKernel7::pAccessPolicyWindow</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pointer to the access policy window. The structure CUaccessPolicyWindow is defined in cuda.h. 
</div>
</div><p>
<a class="anchor" name="71fcb028839bad4c9981a3cc5e350b3a"></a><!-- doxytag: member="CUpti_ActivityKernel7::padding" ref="71fcb028839bad4c9981a3cc5e350b3a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel7.html#71fcb028839bad4c9981a3cc5e350b3a">CUpti_ActivityKernel7::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="0098287b94ed1b7b430a21302ecd3d6d"></a><!-- doxytag: member="CUpti_ActivityKernel7::partitionedGlobalCacheExecuted" ref="0098287b94ed1b7b430a21302ecd3d6d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel7.html#0098287b94ed1b7b430a21302ecd3d6d">CUpti_ActivityKernel7::partitionedGlobalCacheExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching executed for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. Partitioned global caching can be automatically disabled if the occupancy requirement of the launch cannot support caching. 
</div>
</div><p>
<a class="anchor" name="056376e67dfebdea78793f1a35b89fa2"></a><!-- doxytag: member="CUpti_ActivityKernel7::partitionedGlobalCacheRequested" ref="056376e67dfebdea78793f1a35b89fa2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel7.html#056376e67dfebdea78793f1a35b89fa2">CUpti_ActivityKernel7::partitionedGlobalCacheRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching requested for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. 
</div>
</div><p>
<a class="anchor" name="c6c6aa3362ad8ef726769ba125663212"></a><!-- doxytag: member="CUpti_ActivityKernel7::queued" ref="c6c6aa3362ad8ef726769ba125663212" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel7.html#c6c6aa3362ad8ef726769ba125663212">CUpti_ActivityKernel7::queued</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the kernel is queued up in the command buffer, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the queued time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection.<p>
Command buffer is a buffer written by CUDA driver to send commands like kernel launch, memory copy etc to the GPU. All launches of CUDA kernels are asynchrnous with respect to the host, the host requests the launch by writing commands into the command buffer, then returns without checking the GPU's progress. 
</div>
</div><p>
<a class="anchor" name="d90ea5041a5d20cba239929aa4fe9873"></a><!-- doxytag: member="CUpti_ActivityKernel7::registersPerThread" ref="d90ea5041a5d20cba239929aa4fe9873" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel7.html#d90ea5041a5d20cba239929aa4fe9873">CUpti_ActivityKernel7::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="141f3146852da54f164003fa8c8195ab"></a><!-- doxytag: member="CUpti_ActivityKernel7::requested" ref="141f3146852da54f164003fa8c8195ab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel7.html#141f3146852da54f164003fa8c8195ab">CUpti_ActivityKernel7::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="e905e9efd17ee5b55500427caef7b95c"></a><!-- doxytag: member="CUpti_ActivityKernel7::reserved0" ref="e905e9efd17ee5b55500427caef7b95c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel7.html#e905e9efd17ee5b55500427caef7b95c">CUpti_ActivityKernel7::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="3148147d369042966914d736ae2e8194"></a><!-- doxytag: member="CUpti_ActivityKernel7::sharedMemoryCarveoutRequested" ref="3148147d369042966914d736ae2e8194" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel7.html#3148147d369042966914d736ae2e8194">CUpti_ActivityKernel7::sharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory carveout value requested for the function in percentage of the total resource. The value will be updated only if field isSharedMemoryCarveoutRequested is set. 
</div>
</div><p>
<a class="anchor" name="aa84f40c516afd4af1f9007dc9a13113"></a><!-- doxytag: member="CUpti_ActivityKernel7::sharedMemoryConfig" ref="aa84f40c516afd4af1f9007dc9a13113" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel7.html#aa84f40c516afd4af1f9007dc9a13113">CUpti_ActivityKernel7::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="3e5917bc600004fad95cf981e263e914"></a><!-- doxytag: member="CUpti_ActivityKernel7::sharedMemoryExecuted" ref="3e5917bc600004fad95cf981e263e914" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#3e5917bc600004fad95cf981e263e914">CUpti_ActivityKernel7::sharedMemoryExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory size set by the driver. 
</div>
</div><p>
<a class="anchor" name="6d1c66d6fa75de8061b0a60124562921"></a><!-- doxytag: member="CUpti_ActivityKernel7::shmemLimitConfig" ref="6d1c66d6fa75de8061b0a60124562921" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a> <a class="el" href="structCUpti__ActivityKernel7.html#6d1c66d6fa75de8061b0a60124562921">CUpti_ActivityKernel7::shmemLimitConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory limit config for the kernel. This field shows whether user has opted for a higher per block limit of dynamic shared memory. 
</div>
</div><p>
<a class="anchor" name="75b55803d48aba6f847b4d2bbfb39e87"></a><!-- doxytag: member="CUpti_ActivityKernel7::start" ref="75b55803d48aba6f847b4d2bbfb39e87" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel7.html#75b55803d48aba6f847b4d2bbfb39e87">CUpti_ActivityKernel7::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="0282ba3b1063bc3cc74c9c28d63e833d"></a><!-- doxytag: member="CUpti_ActivityKernel7::staticSharedMemory" ref="0282ba3b1063bc3cc74c9c28d63e833d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel7.html#0282ba3b1063bc3cc74c9c28d63e833d">CUpti_ActivityKernel7::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="85180e01af817733bc98fb8763dd377d"></a><!-- doxytag: member="CUpti_ActivityKernel7::streamId" ref="85180e01af817733bc98fb8763dd377d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel7.html#85180e01af817733bc98fb8763dd377d">CUpti_ActivityKernel7::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="f9ccef6805b79f1d4ecda318577245d4"></a><!-- doxytag: member="CUpti_ActivityKernel7::submitted" ref="f9ccef6805b79f1d4ecda318577245d4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel7.html#f9ccef6805b79f1d4ecda318577245d4">CUpti_ActivityKernel7::submitted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the command buffer containing the kernel launch is submitted to the GPU, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the submitted time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
