{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-compiler >=12.1.0", "cuda-documentation >=12.1.55", "cuda-libraries >=12.1.0", "cuda-libraries-dev >=12.1.0", "cuda-nvml-dev >=12.1.55", "cuda-tools >=12.1.0", "libnvvm-samples >=12.1.55"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-toolkit-12.1.0-0", "features": "", "files": [], "fn": "cuda-toolkit-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-toolkit-12.1.0-0", "type": 1}, "md5": "de370f0609e3f3db0c498a67e91529d0", "name": "cuda-toolkit", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-toolkit-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1450, "subdir": "win-64", "timestamp": 1677130077000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-toolkit-12.1.0-0.tar.bz2", "version": "12.1.0"}