<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_EventGroupSet Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_EventGroupSet Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__EVENT__API.html">CUPTI Event API</a>]</small>
</h1><!-- doxytag: class="CUpti_EventGroupSet" -->A set of event groups.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__EventGroupSet.html#f60597b272e9f6dc4df31900244fe1ea">eventGroups</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__EventGroupSet.html#18b3e9aa81b673c5b6c2f74a6203e3bb">numEventGroups</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
A set of event groups. When returned by <a class="el" href="group__CUPTI__EVENT__API.html#g0fd307d429d4e37f61f45472de069910">cuptiEventGroupSetsCreate</a> and <a class="el" href="group__CUPTI__METRIC__API.html#gffab8f5a74b98e7680710203e3932450">cuptiMetricCreateEventGroupSets</a> a set indicates that event groups that can be enabled at the same time (i.e. all the events in the set can be collected simultaneously). <hr><h2>Field Documentation</h2>
<a class="anchor" name="f60597b272e9f6dc4df31900244fe1ea"></a><!-- doxytag: member="CUpti_EventGroupSet::eventGroups" ref="f60597b272e9f6dc4df31900244fe1ea" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__EVENT__API.html#g649750f363752bccbf9e98582d5f6925">CUpti_EventGroup</a>* <a class="el" href="structCUpti__EventGroupSet.html#f60597b272e9f6dc4df31900244fe1ea">CUpti_EventGroupSet::eventGroups</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
An array of <code>numEventGroups</code> event groups. 
</div>
</div><p>
<a class="anchor" name="18b3e9aa81b673c5b6c2f74a6203e3bb"></a><!-- doxytag: member="CUpti_EventGroupSet::numEventGroups" ref="18b3e9aa81b673c5b6c2f74a6203e3bb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__EventGroupSet.html#18b3e9aa81b673c5b6c2f74a6203e3bb">CUpti_EventGroupSet::numEventGroups</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of event groups in the set. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
