<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_vars_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li class="current"><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_s">- s -</a></h3><ul>
<li>samples
: <a class="el" href="structCUpti__ActivityPCSampling.html#8d9e18a7983a7208c5f6d86f71559269">CUpti_ActivityPCSampling</a>
, <a class="el" href="structCUpti__ActivityPCSampling2.html#9f9220a522b755527950accaf0bd8aac">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__PCSamplingStallReason.html#c406f7573333f7d501cf0acd1894bb31">CUpti_PCSamplingStallReason</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#4cd90b008f537cce8237969e7ea60b2c">CUpti_ActivityPCSampling3</a>
<li>samplingDataBufferData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#b0ebcf6890eb69cf0ab41e84e981f894">CUpti_PCSamplingConfigurationInfo</a>
<li>samplingPeriod
: <a class="el" href="structCUpti__ActivityPCSamplingConfig.html#2fc850d7bb051b65831fc0c2dafdc74a">CUpti_ActivityPCSamplingConfig</a>
<li>samplingPeriod2
: <a class="el" href="structCUpti__ActivityPCSamplingConfig.html#79a30d2b8a0a784cc404e79bab973cbd">CUpti_ActivityPCSamplingConfig</a>
<li>samplingPeriodData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8833ff4efd02bdf82b21ba834b68694b">CUpti_PCSamplingConfigurationInfo</a>
<li>samplingPeriodInCycles
: <a class="el" href="structCUpti__ActivityPCSamplingRecordInfo.html#395e4f0b081e552b844a4fb1b7f6f9ae">CUpti_ActivityPCSamplingRecordInfo</a>
<li>scope
: <a class="el" href="structCUpti__ActivityUnifiedMemoryCounterConfig.html#98ab2d234d466672b548a08abe57827e">CUpti_ActivityUnifiedMemoryCounterConfig</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter.html#a007aa3f6a9becfca4fe5d4b02319cd1">CUpti_ActivityUnifiedMemoryCounter</a>
<li>scratchBufferSizeData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#295cf66f184fb5e9fd464006185789b7">CUpti_PCSamplingConfigurationInfo</a>
<li>secondaryBus
: <a class="el" href="structCUpti__ActivityPcie.html#8976ff694e28acaadc04dadb09e75590">CUpti_ActivityPcie</a>
<li>sets
: <a class="el" href="structCUpti__EventGroupSets.html#f19684ea814acad1386e8facc6d06fd9">CUpti_EventGroupSets</a>
<li>sharedMemoryCarveoutRequested
: <a class="el" href="structCUpti__ActivityKernel5.html#1e930f5a20e6ef3337c8e81a69432f03">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#3b5ab3e19ad21b0ed686bad8ebdcd9d0">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#3148147d369042966914d736ae2e8194">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#a79c3959b204eff4b2ecb9fa2d820571">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#4063f9ca2d050f4aea14dc61663d64af">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#c871fea410ea952aa0c023dd3d17a3c6">CUpti_ActivityKernel4</a>
<li>sharedMemoryConfig
: <a class="el" href="structCUpti__ActivityKernel2.html#3bf3e0af4293a057012726a11fdc487d">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#f32f4efe66730c22eadc4f55b8691e79">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#8f29d931899104ce17327ca5bdc88cc8">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#f92bb2ca9a9f489a26bd8a6ab11a0370">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#8999a401981ec6f71d55ae3b1d3fa14c">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#aa84f40c516afd4af1f9007dc9a13113">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#98cfa5371e39324a62ea6451e5dd5367">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#f58e06fed66f3626b8328f9caa5cc675">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#a8b82d1cb39fee8cbd8b447a2ff8291b">CUpti_ActivityCdpKernel</a>
<li>sharedMemoryExecuted
: <a class="el" href="structCUpti__ActivityKernel6.html#2a978ce99b3f1068cc41fabe2d20fb57">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#3e5917bc600004fad95cf981e263e914">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#3dd049ef7a44c5a1ac03e0f8d92eb51f">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#7a83af8f0143cdd49298eceda91ddf60">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#d8e0d7c39287b6485f4e37932437529e">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#3cd8ba3fbe71eae8670142b19afe560a">CUpti_ActivityKernel5</a>
<li>sharedTransactions
: <a class="el" href="structCUpti__ActivitySharedAccess.html#58ea172f2c23f9e2a5484d182f0f8439">CUpti_ActivitySharedAccess</a>
<li>shmemLimitConfig
: <a class="el" href="structCUpti__ActivityKernel5.html#4472a37d059787668ab98f29e012bd94">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#a8323e003d914ae0f26679bc40bec056">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#6d1c66d6fa75de8061b0a60124562921">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#cca157ea7cb025d5607d3407cfc53fec">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#e894e90f1d092c40becb0f8e3b32ae10">CUpti_ActivityKernel9</a>
<li>size
: <a class="el" href="structCUpti__ActivityPCSamplingConfig.html#be5b7680a63371a3de4a0eaec47cd5e8">CUpti_ActivityPCSamplingConfig</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#98bcbf28ab5b8c5715772991be60f2d0">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#fcd6da7fa06dd3837b4f56f5c895aa5b">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#8eebc490f35e113385549c55e4900fcf">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#f10a1e6649ab0e7466eb699999971974">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__PCSamplingPCData.html#572a91a8cb93e68da4f21ffae9c3efb5">CUpti_PCSamplingPCData</a>
, <a class="el" href="structCUpti__PCSamplingData.html#7843c641dd14fb54b8910f1f8290ec26">CUpti_PCSamplingData</a>
, <a class="el" href="structCUpti__PCSamplingConfigurationInfoParams.html#6a5ae5acab25aa0b994dcfb2c2804a60">CUpti_PCSamplingConfigurationInfoParams</a>
, <a class="el" href="structCUpti__PCSamplingGetDataParams.html#8bd57dfc9db2544bebc3716f355f3705">CUpti_PCSamplingGetDataParams</a>
, <a class="el" href="structCUpti__PCSamplingEnableParams.html#ee915ff1cb4a32414e17cfd0cdb700ca">CUpti_PCSamplingEnableParams</a>
, <a class="el" href="structCUpti__PCSamplingDisableParams.html#22721af8a11ed7ae4931038e3577b921">CUpti_PCSamplingDisableParams</a>
, <a class="el" href="structCUpti__PCSamplingStartParams.html#85c927b417bcd83dda1138ed53ba8fc0">CUpti_PCSamplingStartParams</a>
, <a class="el" href="structCUpti__PCSamplingStopParams.html#2205bed210d58bd3cff5eda389cf857f">CUpti_PCSamplingStopParams</a>
, <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#b6b0ae8b6d0450e402b2d6e365e63a85">CUpti_PCSamplingGetNumStallReasonsParams</a>
, <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#32a88cb0bfb4f5580f60769b3d25eb05">CUpti_PCSamplingGetStallReasonsParams</a>
, <a class="el" href="structCUpti__GetSassToSourceCorrelationParams.html#2820390c5cb5cbf9b303d32b5c2518ee">CUpti_GetSassToSourceCorrelationParams</a>
, <a class="el" href="structCUpti__GetCubinCrcParams.html#0b8a3127ca1a3e60013963f3205f6b83">CUpti_GetCubinCrcParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html#2e013441478d453676527e02034c704c">CUPTI::PcSamplingUtil::CUptiUtil_PutPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#faff3ea6e934fc528105635d8626b891">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#d8878b11400c5f365488657206365866">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html#ea2918b846192545ec3855dd05fb3b1f">CUPTI::PcSamplingUtil::CUptiUtil_GetPcSampDataParams</a>
, <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#808596a0967b302067db7339c8edc097">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams</a>
<li>sizeofCounterDataImageOptions
: <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#b194bf39ff44e2a695de3fddeff77e77">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#f48cebdffa5473f56bd6ffaaf88e45e1">CUpti_Profiler_CounterDataImage_Initialize_Params</a>
<li>sli
: <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#4fbf19767dc11f5056dad81998c96625">CUpti_Profiler_DeviceSupported_Params</a>
<li>smClock
: <a class="el" href="structCUpti__ActivityEnvironment.html#8d27b0f9fc286bdf0907c24105630e9b">CUpti_ActivityEnvironment</a>
<li>sourceLocatorId
: <a class="el" href="structCUpti__ActivityGlobalAccess.html#bf3e6e00df66291d88018d6d8c54e0e1">CUpti_ActivityGlobalAccess</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess2.html#1779046cdacce8a88ba657a66939db88">CUpti_ActivityGlobalAccess2</a>
, <a class="el" href="structCUpti__ActivityGlobalAccess3.html#12392c85bce65a1b3d93b93282909e1e">CUpti_ActivityGlobalAccess3</a>
, <a class="el" href="structCUpti__ActivityBranch.html#6c15eb8df540cb08dee0a3b044b58a97">CUpti_ActivityBranch</a>
, <a class="el" href="structCUpti__ActivityBranch2.html#a41ae841700f3fcdaca095d96019291d">CUpti_ActivityBranch2</a>
, <a class="el" href="structCUpti__ActivityInstructionExecution.html#2dbd792bc93cbd93ef8cd552bbe067a4">CUpti_ActivityInstructionExecution</a>
, <a class="el" href="structCUpti__ActivityPCSampling.html#8f5fc5f957e5ae84ea82789b23e1dce0">CUpti_ActivityPCSampling</a>
, <a class="el" href="structCUpti__ActivityPCSampling2.html#3d2b206f639e283376916059e3d913a3">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#e17dde3d0807f5835a0c515b45951c02">CUpti_ActivityPCSampling3</a>
, <a class="el" href="structCUpti__ActivitySharedAccess.html#b4a566843c7034846d3dafcbebef51de">CUpti_ActivitySharedAccess</a>
, <a class="el" href="structCUpti__ActivityInstructionCorrelation.html#71fcd169d499803ca57609cebf2bc38e">CUpti_ActivityInstructionCorrelation</a>
<li>speed
: <a class="el" href="structCUpti__ActivityEnvironment.html#db88826b444ad2ac61555ee510662f28">CUpti_ActivityEnvironment</a>
<li>srcContextId
: <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#5fed0c4ba667570ac9513bc59b9e00c9">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#629f91a401bdb94b3120da538a753e98">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#27e1c54c04f8159ad9869914eef33eca">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#59a28c17da1c9bd6dbd6dd28bad907b7">CUpti_ActivityMemcpyPtoP4</a>
<li>srcDeviceId
: <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#796c868b26586812da12ced78e9d3b0b">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#52a2d35f9457468f5dc98ead20834916">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#dd8ab5abf544830fa2cc883637d13d33">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#448e89607344c732f82127db366551ca">CUpti_ActivityMemcpyPtoP4</a>
<li>srcFile
: <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#ffe221b15fd1827b1d8a9b9c2c09b9dd">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#010c5873a04729c4c0b605f616d401e5">CUpti_ActivityOpenAccOther</a>
<li>srcId
: <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#d52654626d33fe90d1a86f3294dfd200">CUpti_ActivityUnifiedMemoryCounter2</a>
<li>srcKind
: <a class="el" href="structCUpti__ActivityMemcpy.html#ecffc65a3493dd46c9b7d2e7b394e595">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#84747b2d933c9ff25a2e954351e168b3">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#3caeffd2f7e67b198888edfe37af176b">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#939744fdb08ae4ecb349324b919ed712">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#ffd22a34324a0f852af3e5f6bf9154f3">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#e011ef9aafabfd607faff943f22892f0">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#df9d55bd09afe7491a05d8dec94618d0">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#05ebfd8cbc450e7bccb1d209dbbbcf53">CUpti_ActivityMemcpyPtoP4</a>
<li>stallReason
: <a class="el" href="structCUpti__ActivityPCSampling.html#5ff5ad24b1af1dbe867809c0bd861dd8">CUpti_ActivityPCSampling</a>
, <a class="el" href="structCUpti__ActivityPCSampling2.html#c2b5f65abc42c53abe8572850e6692a9">CUpti_ActivityPCSampling2</a>
, <a class="el" href="structCUpti__ActivityPCSampling3.html#09c4bdf6f46401652f682c29cf0a5c56">CUpti_ActivityPCSampling3</a>
, <a class="el" href="structCUpti__PCSamplingPCData.html#f7816940e6cec8f434251de19517a32b">CUpti_PCSamplingPCData</a>
<li>stallReasonCount
: <a class="el" href="structCUpti__PCSamplingPCData.html#0b823240e19037c27164e37216c5587d">CUpti_PCSamplingPCData</a>
<li>stallReasonData
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#8094dc8e2d1a7d895b2fb311d60f02aa">CUpti_PCSamplingConfigurationInfo</a>
<li>stallReasonIndex
: <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#c568ee9da54a690f70fda5e5fdf88bd1">CUpti_PCSamplingGetStallReasonsParams</a>
, <a class="el" href="structPcSamplingStallReasons.html#adbd7f82361702725bf54225fcdf9605">PcSamplingStallReasons</a>
<li>stallReasons
: <a class="el" href="structCUpti__PCSamplingGetStallReasonsParams.html#eaced8e7311449f2ce4831bc44efa3a9">CUpti_PCSamplingGetStallReasonsParams</a>
, <a class="el" href="structPcSamplingStallReasons.html#0db3ddf0898e98c97c0691f3e47cff34">PcSamplingStallReasons</a>
<li>start
: <a class="el" href="structCUpti__ActivitySynchronization.html#ab12527761f1e688558d6eca39722ff9">CUpti_ActivitySynchronization</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#75b55803d48aba6f847b4d2bbfb39e87">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#3ef92fa20916173624e5c5133571dc35">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#de576f758b208a441bf34736d2d5f848">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#dd2f6b817f8ad954e0c755fcb7a986d0">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityAPI.html#390fc4344da529663bfc37745eca0d54">CUpti_ActivityAPI</a>
, <a class="el" href="structCUpti__ActivityOverhead.html#efa4d16d033eddc0e586981fcf13198b">CUpti_ActivityOverhead</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#9396ec87eb0e27c29664ea5e9ca3b099">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#02dd59b8058dae9addf082ee863155fb">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccData.html#9bb4b306c68535c753e0d740fdc4f46f">CUpti_ActivityOpenAccData</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#1214d33f2848f006de98e7e9dff15795">CUpti_ActivityOpenAccLaunch</a>
, <a class="el" href="structCUpti__ActivityOpenMp.html#0b5dd126b68af77c482b205f58b14889">CUpti_ActivityOpenMp</a>
, <a class="el" href="structCUpti__ActivityJit.html#cc435cb5eedd5179968dc315b0ba49db">CUpti_ActivityJit</a>
, <a class="el" href="structCUpti__ActivityGraphTrace.html#63d4edb284a3f1986251988d26ece452">CUpti_ActivityGraphTrace</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#216adf7e2087154e20c490da402c5460">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#ff1a6519fe35c1c2a8a8a7201b18b1c7">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#809fd89a377f0af29ea9781e7984fdb5">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#26fa72bce71b25d187492a43e188d772">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#e078e7e93991fd57ae7fa5fd8d449fe7">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#f2ee23b605610659df0484026a68f670">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#505faabd77fe94e6b82c193e9650a792">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#8da8117c4e6d0d4488be1507e7f66a11">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#bb7de7ad6fbdce94d442fd96337d97eb">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#b0627b707c6df921c414e3c55c4995a9">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#405e29ca7cd6ece7e30f29d8d10d3d13">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#4e3482983ecec51ba22c3e538df5afc0">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityMemset.html#e08e1528d00826b903726a7e69c5642e">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#05fe7da921c7b348be2fc856a9480e85">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#8e8d739a94205fa8a063d6d667dae25f">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityMemory.html#dfa91d9df7f8defade47a033974a1f14">CUpti_ActivityMemory</a>
, <a class="el" href="structCUpti__ActivityKernel.html#9d9ad4235c7ef9b1cff6cb8241cf78b5">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#1bd2d2d4fb7069a04164f0151a758475">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#dfedc37bb31232afba143564acc07739">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#1e06bc296d674e602ff1684976309d4a">CUpti_ActivityKernel5</a>
<li>staticSharedMemory
: <a class="el" href="structCUpti__ActivityKernel7.html#0282ba3b1063bc3cc74c9c28d63e833d">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#e3b43ccafa257038e17520642c294150">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#98eb66964d6014b1e22de1abc8ad8038">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#1e4c190643fb497f7af68cf943a137d0">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#0f74a2c66412a58e9f93a32c8e3641b8">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#8656b76c619f8029f311ebdd87d56bcd">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#b788fe8577f4c6b07b709e0d5ee6c767">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#79eca25f9d20c68c4195379378936cc1">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#8ab62b9c269425d61454e83c5042008c">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel.html#390d6d8ed2caec3f408ed3eeac4cefd8">CUpti_ActivityKernel</a>
<li>stream
: <a class="el" href="structCUpti__SynchronizeData.html#5b186eb1afc301f2eeb4fa0ca8b915ab">CUpti_SynchronizeData</a>
, <a class="el" href="structCUpti__ResourceData.html#c25bd7f69d64d6f1b5b710d9da4130ed">CUpti_ResourceData</a>
<li>streamId
: <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#a1565e451475facc31b5bf1f7709f9bd">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#c1a797c8b73f28e0a029bb56a463e149">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#23cb65383137c8be145ce99f6255f6ad">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityStream.html#9f35567b6b0dcee8ab5b15fcbbb02abf">CUpti_ActivityStream</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#c8f4e29169e741c7ad885f8214bdf06c">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#c8f0a24899363870b6cfb1635f432a79">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#49eee33e574f6a52424bd0d33d1e65d1">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#577d5a810459619ceaeedca87181b57a">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#a1db957966dabf0d20ec0bfc07fbd25f">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#85180e01af817733bc98fb8763dd377d">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivitySynchronization.html#f8c4a5d97a842dbedf012993923e21e3">CUpti_ActivitySynchronization</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#c8de749b5fa90977bb2b7e493d60dfed">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#642cb198ed20485d9ddf3fe150d94c30">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#ae5ccf7a09ea26295fd7fe5737be5258">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#561127cefbf69d446a9b958f099b5edf">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#6018be7cf08d0daa5a92a5b31f895d7f">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityCudaEvent.html#3d92c66e6a1ce745af7d641f4bcd4324">CUpti_ActivityCudaEvent</a>
, <a class="el" href="structCUpti__ActivityKernel.html#cf1a4bc3d0e7791aadefbe8749ce2c82">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#60ab70288f5d57d036c590659d49df99">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#d35d0f668befb8c6a3dbeca382b2365f">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#b3cdd2044aaab415fe927bcef43f8408">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#85d5dda38e346c14ae0e4f3fd9eacde9">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityMemset.html#0ebad6c077e37fac5b96709cfa5631d6">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ffa5a1fe02293174d6e04b165c7b0d60">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityGraphTrace.html#bc75567b79a8f3658387a157d68f3782">CUpti_ActivityGraphTrace</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#f4ce72b6db3f815e3589ee67aef768e7">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#bb8b65dc9a8de4edd50366ecdaa10ccc">CUpti_ActivityUnifiedMemoryCounter2</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#4cfbe1f2b00a5afc528c846aaef2ee45">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#49baa8894d0fe9cfaf5adf86a07c2dd4">CUpti_ActivityMemcpy</a>
<li>structSize
: <a class="el" href="structCUpti__Profiler__SetConfig__Params.html#44687aab18b99379dab6e57fec3e13f2">CUpti_Profiler_SetConfig_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#1de1b7b170fb76a18674b900290a2e25">CUpti_Profiler_CounterDataImage_Initialize_Params</a>
, <a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#a23ec684bdbe398cc2d98fa082ba6fd9">CUpti_Profiler_GetCounterAvailability_Params</a>
, <a class="el" href="structCUpti__Profiler__DeInitialize__Params.html#2b0bb5913ac0485fa6a57413f47ba33a">CUpti_Profiler_DeInitialize_Params</a>
, <a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#a2b6ab8636ac6afb4f0d9e56e9c23e18">CUpti_Profiler_IsPassCollected_Params</a>
, <a class="el" href="structCUpti__Profiler__DisableProfiling__Params.html#dcec5c9d76fde27b00462b17cc34efaf">CUpti_Profiler_DisableProfiling_Params</a>
, <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#87bc7073520790b7d81bc990c8385f46">NV::Cupti::Checkpoint::CUpti_Checkpoint</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html#005ccabc523d2e4af4104ab0894aff02">CUpti_Profiler_CounterDataImageOptions</a>
, <a class="el" href="structCUpti__Profiler__EndSession__Params.html#a601cf5cfbb688ff3e48866d441e4af8">CUpti_Profiler_EndSession_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html#5e9da316383bb055cc47b400d03b7c58">CUpti_Profiler_CounterDataImage_CalculateSize_Params</a>
, <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#40450339c5074a440d352b68be6d961d">CUpti_Profiler_DeviceSupported_Params</a>
, <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#2719af9f3c30462d924b8705f3c2ecf5">CUpti_Profiler_BeginSession_Params</a>
, <a class="el" href="structCUpti__Profiler__EndPass__Params.html#f1ba39050dd671887899a7bf4405be20">CUpti_Profiler_EndPass_Params</a>
, <a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#3e792ad6cddaad1013adbba15221a775">CUpti_Profiler_FlushCounterData_Params</a>
, <a class="el" href="structCUpti__Profiler__UnsetConfig__Params.html#dd9abd80cd080fb7ead9cee88d0c4f6c">CUpti_Profiler_UnsetConfig_Params</a>
, <a class="el" href="structCUpti__Profiler__Initialize__Params.html#97d06c5a19d29b5d02d9290fb35fd34a">CUpti_Profiler_Initialize_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html#e102811eea536a9ba9cf4944e071afb0">CUpti_Profiler_CounterDataImage_CalculateScratchBufferSize_Params</a>
, <a class="el" href="structCUpti__Profiler__BeginPass__Params.html#8fb20d461501122695366b8fcb784f4a">CUpti_Profiler_BeginPass_Params</a>
, <a class="el" href="structCUpti__Profiler__EnableProfiling__Params.html#79a32a58917776a07af4e3791917d85c">CUpti_Profiler_EnableProfiling_Params</a>
, <a class="el" href="structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html#4605887493f255b93dd5ef346169d53d">CUpti_Profiler_CounterDataImage_InitializeScratchBuffer_Params</a>
<li>submitted
: <a class="el" href="structCUpti__ActivityKernel4.html#ddeaca33de73bc8721f6fcf10947c9e5">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#13656d1e9a0b4e83754f78a26f0156d8">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#c4f3e84370dbfa4eb6ef447e83b4d156">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#b5176c22fd072f2a951c6c0278db169f">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#2789727f1a83cf45a3230172887be4a4">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#a90bb0179c75aeb63a92d3c97c1e210a">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#f9ccef6805b79f1d4ecda318577245d4">CUpti_ActivityKernel7</a>
<li>symbolName
: <a class="el" href="structCUpti__CallbackData.html#6e5af5a34bb534f64ad8f968d780b63f">CUpti_CallbackData</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
