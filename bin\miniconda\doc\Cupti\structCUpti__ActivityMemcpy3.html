<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpy3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpy3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpy3" -->The activity record for memory copies. (deprecated in CUDA 11.1).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#97b161ae27a7b00f5c776d9fc53ba5a2">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#40eba2a48618f18174116f317bb51ead">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#de788e44dac2230c5b9bc296f22f1a8d">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#eb80c0867c8b9f38ac0b0bcbacf83c20">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#ec484d4b3d78592037f3e5e0d49338ca">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#1a5d2023ab94de724c40458921d91430">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#5bd27ea4d0bb88b5007d7d3c80269968">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#91e830d5ea735c4a0905f69e37d764d3">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#f6c35e68b6444dec004842a3e3c856ae">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#d3c6c47e7027b3561cb14698c6960349">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#366d41146597b9ecdcf5fd85316fee55">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#3fcf8b9d4e65845da9f36a2528699a41">runtimeCorrelationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#84747b2d933c9ff25a2e954351e168b3">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#505faabd77fe94e6b82c193e9650a792">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy3.html#c8f4e29169e741c7ad885f8214bdf06c">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory copy (CUPTI_ACTIVITY_KIND_MEMCPY). <hr><h2>Field Documentation</h2>
<a class="anchor" name="97b161ae27a7b00f5c776d9fc53ba5a2"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::bytes" ref="97b161ae27a7b00f5c776d9fc53ba5a2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy3.html#97b161ae27a7b00f5c776d9fc53ba5a2">CUpti_ActivityMemcpy3::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="40eba2a48618f18174116f317bb51ead"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::contextId" ref="40eba2a48618f18174116f317bb51ead" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy3.html#40eba2a48618f18174116f317bb51ead">CUpti_ActivityMemcpy3::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="de788e44dac2230c5b9bc296f22f1a8d"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::copyKind" ref="de788e44dac2230c5b9bc296f22f1a8d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy3.html#de788e44dac2230c5b9bc296f22f1a8d">CUpti_ActivityMemcpy3::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="eb80c0867c8b9f38ac0b0bcbacf83c20"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::correlationId" ref="eb80c0867c8b9f38ac0b0bcbacf83c20" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy3.html#eb80c0867c8b9f38ac0b0bcbacf83c20">CUpti_ActivityMemcpy3::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="ec484d4b3d78592037f3e5e0d49338ca"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::deviceId" ref="ec484d4b3d78592037f3e5e0d49338ca" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy3.html#ec484d4b3d78592037f3e5e0d49338ca">CUpti_ActivityMemcpy3::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="1a5d2023ab94de724c40458921d91430"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::dstKind" ref="1a5d2023ab94de724c40458921d91430" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy3.html#1a5d2023ab94de724c40458921d91430">CUpti_ActivityMemcpy3::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="5bd27ea4d0bb88b5007d7d3c80269968"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::end" ref="5bd27ea4d0bb88b5007d7d3c80269968" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy3.html#5bd27ea4d0bb88b5007d7d3c80269968">CUpti_ActivityMemcpy3::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="91e830d5ea735c4a0905f69e37d764d3"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::flags" ref="91e830d5ea735c4a0905f69e37d764d3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy3.html#91e830d5ea735c4a0905f69e37d764d3">CUpti_ActivityMemcpy3::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="f6c35e68b6444dec004842a3e3c856ae"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::graphNodeId" ref="f6c35e68b6444dec004842a3e3c856ae" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy3.html#f6c35e68b6444dec004842a3e3c856ae">CUpti_ActivityMemcpy3::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed this memcpy through graph launch. This field will be 0 if the memcpy is not done through graph launch. 
</div>
</div><p>
<a class="anchor" name="d3c6c47e7027b3561cb14698c6960349"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::kind" ref="d3c6c47e7027b3561cb14698c6960349" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpy3.html#d3c6c47e7027b3561cb14698c6960349">CUpti_ActivityMemcpy3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY. 
</div>
</div><p>
<a class="anchor" name="366d41146597b9ecdcf5fd85316fee55"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::reserved0" ref="366d41146597b9ecdcf5fd85316fee55" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpy3.html#366d41146597b9ecdcf5fd85316fee55">CUpti_ActivityMemcpy3::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="3fcf8b9d4e65845da9f36a2528699a41"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::runtimeCorrelationId" ref="3fcf8b9d4e65845da9f36a2528699a41" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy3.html#3fcf8b9d4e65845da9f36a2528699a41">CUpti_ActivityMemcpy3::runtimeCorrelationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The runtime correlation ID of the memory copy. Each memory copy is assigned a unique runtime correlation ID that is identical to the correlation ID in the runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="84747b2d933c9ff25a2e954351e168b3"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::srcKind" ref="84747b2d933c9ff25a2e954351e168b3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy3.html#84747b2d933c9ff25a2e954351e168b3">CUpti_ActivityMemcpy3::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="505faabd77fe94e6b82c193e9650a792"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::start" ref="505faabd77fe94e6b82c193e9650a792" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy3.html#505faabd77fe94e6b82c193e9650a792">CUpti_ActivityMemcpy3::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="c8f4e29169e741c7ad885f8214bdf06c"></a><!-- doxytag: member="CUpti_ActivityMemcpy3::streamId" ref="c8f4e29169e741c7ad885f8214bdf06c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy3.html#c8f4e29169e741c7ad885f8214bdf06c">CUpti_ActivityMemcpy3::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
