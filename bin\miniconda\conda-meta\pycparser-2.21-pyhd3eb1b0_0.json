{"build": "pyhd3eb1b0_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.6"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\pycparser-2.21-pyhd3eb1b0_0", "files": ["Lib/site-packages/pycparser-2.21.dist-info/INSTALLER", "Lib/site-packages/pycparser-2.21.dist-info/LICENSE", "Lib/site-packages/pycparser-2.21.dist-info/METADATA", "Lib/site-packages/pycparser-2.21.dist-info/RECORD", "Lib/site-packages/pycparser-2.21.dist-info/REQUESTED", "Lib/site-packages/pycparser-2.21.dist-info/WHEEL", "Lib/site-packages/pycparser-2.21.dist-info/direct_url.json", "Lib/site-packages/pycparser-2.21.dist-info/top_level.txt", "Lib/site-packages/pycparser/__init__.py", "Lib/site-packages/pycparser/_ast_gen.py", "Lib/site-packages/pycparser/_build_tables.py", "Lib/site-packages/pycparser/_c_ast.cfg", "Lib/site-packages/pycparser/ast_transforms.py", "Lib/site-packages/pycparser/c_ast.py", "Lib/site-packages/pycparser/c_generator.py", "Lib/site-packages/pycparser/c_lexer.py", "Lib/site-packages/pycparser/c_parser.py", "Lib/site-packages/pycparser/lextab.py", "Lib/site-packages/pycparser/ply/__init__.py", "Lib/site-packages/pycparser/ply/cpp.py", "Lib/site-packages/pycparser/ply/ctokens.py", "Lib/site-packages/pycparser/ply/lex.py", "Lib/site-packages/pycparser/ply/yacc.py", "Lib/site-packages/pycparser/ply/ygen.py", "Lib/site-packages/pycparser/plyparser.py", "Lib/site-packages/pycparser/yacctab.py", "Lib/site-packages/pycparser/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/_ast_gen.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/_build_tables.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/ast_transforms.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/c_ast.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/c_generator.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/c_lexer.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/c_parser.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/lextab.cpython-310.pyc", "Lib/site-packages/pycparser/ply/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pycparser/ply/__pycache__/cpp.cpython-310.pyc", "Lib/site-packages/pycparser/ply/__pycache__/ctokens.cpython-310.pyc", "Lib/site-packages/pycparser/ply/__pycache__/lex.cpython-310.pyc", "Lib/site-packages/pycparser/ply/__pycache__/yacc.cpython-310.pyc", "Lib/site-packages/pycparser/ply/__pycache__/ygen.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/plyparser.cpython-310.pyc", "Lib/site-packages/pycparser/__pycache__/yacctab.cpython-310.pyc"], "fn": "pycparser-2.21-pyhd3eb1b0_0.conda", "license": "BSD-3-clause", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\pycparser-2.21-pyhd3eb1b0_0", "type": 1}, "md5": "135a72ff2a31150a3a3ff0b1edd41ca9", "name": "pyc<PERSON><PERSON>", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\pycparser-2.21-pyhd3eb1b0_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "Lib/site-packages/pycparser/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/_ast_gen.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/_build_tables.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/ast_transforms.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/c_ast.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/c_generator.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/c_lexer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/c_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/lextab.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/ply/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/ply/__pycache__/cpp.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/ply/__pycache__/ctokens.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/ply/__pycache__/lex.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/ply/__pycache__/yacc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/ply/__pycache__/ygen.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/plyparser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pycparser/__pycache__/yacctab.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "defaults/noarch::pycparser==2.21=pyhd3eb1b0_0[md5=135a72ff2a31150a3a3ff0b1edd41ca9]", "sha256": "4405b5aeff26863972c82e8b54d09f88cd084f70e01e4343107b2676ffbeab57", "size": 96617, "subdir": "noarch", "timestamp": 1636541382000, "url": "https://repo.anaconda.com/pkgs/main/noarch/pycparser-2.21-pyhd3eb1b0_0.conda", "version": "2.21"}