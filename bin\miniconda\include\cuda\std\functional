//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_FUNCTIONAL
#define _CUDA_FUNCTIONAL

#include "type_traits"
#include "version"
#include "iterator"
// #include "memory"
// #include "tuple"
#include "utility"

#include "detail/__config"

#include "detail/__pragma_push"

#include "detail/__functional_base"

#include "detail/libcxx/include/functional"

#include "detail/__pragma_pop"

#endif //_CUDA_FUNCTIONAL


