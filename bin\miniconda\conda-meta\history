==> 2025-02-11 20:17:33 <==
# cmd: constructor C:/Users/<USER>/AppData/Local/Temp/tmp.7J08MQv1Xi/miniconda3/ --output-dir C:/Users/<USER>/AppData/Local/Temp/tmp.7J08MQv1Xi
+defaults::anaconda_powershell_prompt-1.1.0-haa95532_0
+defaults::anaconda_prompt-1.1.0-haa95532_0
+defaults::ca-certificates-2024.12.31-haa95532_0
+defaults::nlohmann_json-3.11.2-h6c2663c_0
+defaults::pybind11-abi-5-hd3eb1b0_0
+defaults::tzdata-2025a-h04d1e81_0
+defaults::vs2015_runtime-14.42.34433-he0abc0d_4
+defaults::vc-14.42-haa95532_4
+defaults::bzip2-1.0.8-h2bbff1b_6
+defaults::cpp-expected-1.1.0-h214f63a_0
+defaults::fmt-9.1.0-h6d14046_1
+defaults::libffi-3.4.4-hd77b12b_1
+defaults::libiconv-1.16-h2bbff1b_3
+defaults::lz4-c-1.9.4-h2bbff1b_1
+defaults::openssl-3.0.15-h827c3e9_0
+defaults::reproc-14.2.4-hd77b12b_2
+defaults::simdjson-3.10.1-h214f63a_0
+defaults::sqlite-3.45.3-h2bbff1b_0
+defaults::xz-5.4.6-h8cc25b3_1
+defaults::yaml-cpp-0.8.0-hd77b12b_1
+defaults::zlib-1.2.13-h8cc25b3_1
+defaults::libssh2-1.11.1-h2addb87_0
+defaults::libxml2-2.13.5-h24da03e_0
+defaults::pcre2-10.42-h0ff8eda_1
+defaults::reproc-cpp-14.2.4-hd77b12b_2
+defaults::spdlog-1.11.0-h59b6b97_0
+defaults::tk-8.6.14-h0416ee5_0
+defaults::zstd-1.5.6-h8880b57_0
+defaults::libarchive-3.7.7-h9243413_0
+defaults::libcurl-8.11.1-haff574d_0
+defaults::libsolv-0.7.30-hf2fb9eb_1
+defaults::python-3.10.16-h4607a30_1
+defaults::libmamba-2.0.5-hcd6fe79_1
+defaults::menuinst-2.2.0-py310h5da7b33_1
+defaults::anaconda-anon-usage-0.5.0-py310hfc23b7f_100
+defaults::annotated-types-0.6.0-py310haa95532_0
+defaults::archspec-0.2.3-pyhd3eb1b0_0
+defaults::boltons-24.1.0-py310haa95532_0
+defaults::brotli-python-1.0.9-py310h5da7b33_9
+defaults::certifi-2025.1.31-py310haa95532_0
+defaults::charset-normalizer-3.3.2-pyhd3eb1b0_0
+defaults::colorama-0.4.6-py310haa95532_0
+defaults::distro-1.9.0-py310haa95532_0
+defaults::frozendict-2.4.2-py310h2bbff1b_0
+defaults::idna-3.7-py310haa95532_0
+defaults::libmambapy-2.0.5-py310h214f63a_1
+defaults::mdurl-0.1.0-py310haa95532_0
+defaults::packaging-24.2-py310haa95532_0
+defaults::platformdirs-3.10.0-py310haa95532_0
+defaults::pluggy-1.5.0-py310haa95532_0
+defaults::pycosat-0.6.6-py310h827c3e9_2
+defaults::pygments-2.15.1-py310haa95532_1
+defaults::ruamel.yaml.clib-0.2.8-py310h827c3e9_0
+defaults::setuptools-75.8.0-py310haa95532_0
+defaults::truststore-0.10.0-py310haa95532_0
+defaults::typing_extensions-4.12.2-py310haa95532_0
+defaults::wheel-0.45.1-py310haa95532_0
+defaults::win_inet_pton-1.1.0-py310haa95532_0
+defaults::markdown-it-py-2.2.0-py310haa95532_1
+defaults::pip-25.0-py310haa95532_0
+defaults::pysocks-1.7.1-py310haa95532_0
+defaults::ruamel.yaml-0.18.6-py310h827c3e9_0
+defaults::tqdm-4.67.1-py310h9909e9c_0
+defaults::typing-extensions-4.12.2-py310haa95532_0
+defaults::pydantic-core-2.27.1-py310h636fa0f_0
+defaults::rich-13.9.4-py310haa95532_0
+defaults::urllib3-2.3.0-py310haa95532_0
+defaults::pydantic-2.10.3-py310haa95532_0
+defaults::requests-2.32.3-py310haa95532_1
+defaults::conda-libmamba-solver-25.1.1-pyhd3eb1b0_0
+defaults::jsonpointer-2.1-pyhd3eb1b0_0
+defaults::jsonpatch-1.33-py310haa95532_1
+defaults::pycparser-2.21-pyhd3eb1b0_0
+defaults::cffi-1.17.1-py310h827c3e9_1
+defaults::cryptography-43.0.3-py310hbd6ee87_1
+defaults::zstandard-0.23.0-py310h4fc1ca9_1
+defaults::conda-content-trust-0.2.0-py310haa95532_1
+defaults::conda-package-streaming-0.11.0-py310haa95532_0
+defaults::conda-package-handling-2.4.0-py310haa95532_0
+defaults::conda-25.1.1-py310haa95532_0
+defaults::conda-anaconda-telemetry-0.1.2-py310haa95532_0
+defaults::conda-anaconda-tos-0.1.2-py310haa95532_0
# update specs: ['python=3.10', 'conda==25.1.1', 'setuptools', 'pip', 'wheel', 'conda-anaconda-telemetry', "conda-anaconda-tos[version='>=0.1.2']", 'conda-content-trust', 'conda-libmamba-solver', 'menuinst', 'anaconda_prompt', 'anaconda_powershell_prompt', 'anaconda-anon-usage']

==> 2025-07-23 00:19:19 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y -c conda-forge sqlite=3.47.2
# conda version: 25.1.1
-defaults::anaconda-anon-usage-0.5.0-py310hfc23b7f_100
-defaults::anaconda_powershell_prompt-1.1.0-haa95532_0
-defaults::anaconda_prompt-1.1.0-haa95532_0
-defaults::annotated-types-0.6.0-py310haa95532_0
-defaults::archspec-0.2.3-pyhd3eb1b0_0
-defaults::boltons-24.1.0-py310haa95532_0
-defaults::brotli-python-1.0.9-py310h5da7b33_9
-defaults::bzip2-1.0.8-h2bbff1b_6
-defaults::ca-certificates-2024.12.31-haa95532_0
-defaults::certifi-2025.1.31-py310haa95532_0
-defaults::cffi-1.17.1-py310h827c3e9_1
-defaults::charset-normalizer-3.3.2-pyhd3eb1b0_0
-defaults::colorama-0.4.6-py310haa95532_0
-defaults::conda-25.1.1-py310haa95532_0
-defaults::conda-anaconda-telemetry-0.1.2-py310haa95532_0
-defaults::conda-anaconda-tos-0.1.2-py310haa95532_0
-defaults::conda-content-trust-0.2.0-py310haa95532_1
-defaults::conda-libmamba-solver-25.1.1-pyhd3eb1b0_0
-defaults::conda-package-handling-2.4.0-py310haa95532_0
-defaults::conda-package-streaming-0.11.0-py310haa95532_0
-defaults::cpp-expected-1.1.0-h214f63a_0
-defaults::cryptography-43.0.3-py310hbd6ee87_1
-defaults::distro-1.9.0-py310haa95532_0
-defaults::fmt-9.1.0-h6d14046_1
-defaults::frozendict-2.4.2-py310h2bbff1b_0
-defaults::idna-3.7-py310haa95532_0
-defaults::jsonpatch-1.33-py310haa95532_1
-defaults::jsonpointer-2.1-pyhd3eb1b0_0
-defaults::libarchive-3.7.7-h9243413_0
-defaults::libcurl-8.11.1-haff574d_0
-defaults::libffi-3.4.4-hd77b12b_1
-defaults::libiconv-1.16-h2bbff1b_3
-defaults::libmamba-2.0.5-hcd6fe79_1
-defaults::libmambapy-2.0.5-py310h214f63a_1
-defaults::libsolv-0.7.30-hf2fb9eb_1
-defaults::libssh2-1.11.1-h2addb87_0
-defaults::libxml2-2.13.5-h24da03e_0
-defaults::lz4-c-1.9.4-h2bbff1b_1
-defaults::markdown-it-py-2.2.0-py310haa95532_1
-defaults::mdurl-0.1.0-py310haa95532_0
-defaults::menuinst-2.2.0-py310h5da7b33_1
-defaults::nlohmann_json-3.11.2-h6c2663c_0
-defaults::openssl-3.0.15-h827c3e9_0
-defaults::packaging-24.2-py310haa95532_0
-defaults::pcre2-10.42-h0ff8eda_1
-defaults::pip-25.0-py310haa95532_0
-defaults::platformdirs-3.10.0-py310haa95532_0
-defaults::pluggy-1.5.0-py310haa95532_0
-defaults::pybind11-abi-5-hd3eb1b0_0
-defaults::pycosat-0.6.6-py310h827c3e9_2
-defaults::pycparser-2.21-pyhd3eb1b0_0
-defaults::pydantic-2.10.3-py310haa95532_0
-defaults::pydantic-core-2.27.1-py310h636fa0f_0
-defaults::pygments-2.15.1-py310haa95532_1
-defaults::pysocks-1.7.1-py310haa95532_0
-defaults::python-3.10.16-h4607a30_1
-defaults::reproc-14.2.4-hd77b12b_2
-defaults::reproc-cpp-14.2.4-hd77b12b_2
-defaults::requests-2.32.3-py310haa95532_1
-defaults::rich-13.9.4-py310haa95532_0
-defaults::ruamel.yaml-0.18.6-py310h827c3e9_0
-defaults::ruamel.yaml.clib-0.2.8-py310h827c3e9_0
-defaults::setuptools-75.8.0-py310haa95532_0
-defaults::simdjson-3.10.1-h214f63a_0
-defaults::spdlog-1.11.0-h59b6b97_0
-defaults::sqlite-3.45.3-h2bbff1b_0
-defaults::tk-8.6.14-h0416ee5_0
-defaults::tqdm-4.67.1-py310h9909e9c_0
-defaults::truststore-0.10.0-py310haa95532_0
-defaults::typing-extensions-4.12.2-py310haa95532_0
-defaults::typing_extensions-4.12.2-py310haa95532_0
-defaults::tzdata-2025a-h04d1e81_0
-defaults::urllib3-2.3.0-py310haa95532_0
-defaults::vc-14.42-haa95532_4
-defaults::vs2015_runtime-14.42.34433-he0abc0d_4
-defaults::wheel-0.45.1-py310haa95532_0
-defaults::win_inet_pton-1.1.0-py310haa95532_0
-defaults::xz-5.4.6-h8cc25b3_1
-defaults::yaml-cpp-0.8.0-hd77b12b_1
-defaults::zlib-1.2.13-h8cc25b3_1
-defaults::zstandard-0.23.0-py310h4fc1ca9_1
-defaults::zstd-1.5.6-h8880b57_0
+conda-forge/noarch::ca-certificates-2025.7.14-h4c7d964_0
+conda-forge/noarch::certifi-2025.7.14-pyhd8ed1ab_0
+conda-forge/win-64::conda-25.5.1-py310h5588dad_0
+conda-forge/win-64::libsqlite-3.47.2-h67fdade_0
+conda-forge/win-64::openssl-3.5.1-h725018a_0
+conda-forge/win-64::python_abi-3.10-2_cp310
+conda-forge/win-64::sqlite-3.47.2-h2466b09_0
+conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1
+conda-forge/win-64::vc14_runtime-14.44.35208-h818238b_30
+conda-forge/win-64::vs2015_runtime-14.44.35208-h38c0c73_30
+defaults/noarch::archspec-0.2.3-pyhd3eb1b0_0
+defaults/noarch::charset-normalizer-3.3.2-pyhd3eb1b0_0
+defaults/noarch::conda-libmamba-solver-25.1.1-pyhd3eb1b0_0
+defaults/noarch::jsonpointer-2.1-pyhd3eb1b0_0
+defaults/noarch::pybind11-abi-5-hd3eb1b0_0
+defaults/noarch::pycparser-2.21-pyhd3eb1b0_0
+defaults/noarch::tzdata-2025a-h04d1e81_0
+defaults/win-64::anaconda-anon-usage-0.5.0-py310hfc23b7f_100
+defaults/win-64::anaconda_powershell_prompt-1.1.0-haa95532_0
+defaults/win-64::anaconda_prompt-1.1.0-haa95532_0
+defaults/win-64::annotated-types-0.6.0-py310haa95532_0
+defaults/win-64::boltons-24.1.0-py310haa95532_0
+defaults/win-64::brotli-python-1.0.9-py310h5da7b33_9
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::cffi-1.17.1-py310h827c3e9_1
+defaults/win-64::colorama-0.4.6-py310haa95532_0
+defaults/win-64::conda-anaconda-telemetry-0.1.2-py310haa95532_0
+defaults/win-64::conda-anaconda-tos-0.1.2-py310haa95532_0
+defaults/win-64::conda-content-trust-0.2.0-py310haa95532_1
+defaults/win-64::conda-package-handling-2.4.0-py310haa95532_0
+defaults/win-64::conda-package-streaming-0.11.0-py310haa95532_0
+defaults/win-64::cpp-expected-1.1.0-h214f63a_0
+defaults/win-64::cryptography-43.0.3-py310hbd6ee87_1
+defaults/win-64::distro-1.9.0-py310haa95532_0
+defaults/win-64::fmt-9.1.0-h6d14046_1
+defaults/win-64::frozendict-2.4.2-py310h2bbff1b_0
+defaults/win-64::idna-3.7-py310haa95532_0
+defaults/win-64::jsonpatch-1.33-py310haa95532_1
+defaults/win-64::libarchive-3.7.7-h9243413_0
+defaults/win-64::libcurl-8.11.1-haff574d_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::libiconv-1.16-h2bbff1b_3
+defaults/win-64::libmamba-2.0.5-hcd6fe79_1
+defaults/win-64::libmambapy-2.0.5-py310h214f63a_1
+defaults/win-64::libsolv-0.7.30-hf2fb9eb_1
+defaults/win-64::libssh2-1.11.1-h2addb87_0
+defaults/win-64::libxml2-2.13.5-h24da03e_0
+defaults/win-64::lz4-c-1.9.4-h2bbff1b_1
+defaults/win-64::markdown-it-py-2.2.0-py310haa95532_1
+defaults/win-64::mdurl-0.1.0-py310haa95532_0
+defaults/win-64::menuinst-2.2.0-py310h5da7b33_1
+defaults/win-64::nlohmann_json-3.11.2-h6c2663c_0
+defaults/win-64::packaging-24.2-py310haa95532_0
+defaults/win-64::pcre2-10.42-h0ff8eda_1
+defaults/win-64::pip-25.0-py310haa95532_0
+defaults/win-64::platformdirs-3.10.0-py310haa95532_0
+defaults/win-64::pluggy-1.5.0-py310haa95532_0
+defaults/win-64::pycosat-0.6.6-py310h827c3e9_2
+defaults/win-64::pydantic-2.10.3-py310haa95532_0
+defaults/win-64::pydantic-core-2.27.1-py310h636fa0f_0
+defaults/win-64::pygments-2.15.1-py310haa95532_1
+defaults/win-64::pysocks-1.7.1-py310haa95532_0
+defaults/win-64::python-3.10.16-h4607a30_1
+defaults/win-64::reproc-14.2.4-hd77b12b_2
+defaults/win-64::reproc-cpp-14.2.4-hd77b12b_2
+defaults/win-64::requests-2.32.3-py310haa95532_1
+defaults/win-64::rich-13.9.4-py310haa95532_0
+defaults/win-64::ruamel.yaml-0.18.6-py310h827c3e9_0
+defaults/win-64::ruamel.yaml.clib-0.2.8-py310h827c3e9_0
+defaults/win-64::setuptools-75.8.0-py310haa95532_0
+defaults/win-64::simdjson-3.10.1-h214f63a_0
+defaults/win-64::spdlog-1.11.0-h59b6b97_0
+defaults/win-64::tk-8.6.14-h0416ee5_0
+defaults/win-64::tqdm-4.67.1-py310h9909e9c_0
+defaults/win-64::truststore-0.10.0-py310haa95532_0
+defaults/win-64::typing-extensions-4.12.2-py310haa95532_0
+defaults/win-64::typing_extensions-4.12.2-py310haa95532_0
+defaults/win-64::urllib3-2.3.0-py310haa95532_0
+defaults/win-64::vc-14.42-haa95532_4
+defaults/win-64::wheel-0.45.1-py310haa95532_0
+defaults/win-64::win_inet_pton-1.1.0-py310haa95532_0
+defaults/win-64::xz-5.4.6-h8cc25b3_1
+defaults/win-64::yaml-cpp-0.8.0-hd77b12b_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
+defaults/win-64::zstandard-0.23.0-py310h4fc1ca9_1
+defaults/win-64::zstd-1.5.6-h8880b57_0
# update specs: ['sqlite=3.47.2']
# neutered specs: ['conda==25.5.1']
==> 2025-07-23 00:22:50 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install --no-shortcuts -y -c conda-forge git git-lfs
# conda version: 25.5.1
+conda-forge/win-64::git-2.49.0-h57928b3_2
+conda-forge/win-64::git-lfs-3.7.0-h86e1c39_0
# update specs: ['git', 'git-lfs']
==> 2025-07-23 00:23:49 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y -c conda-forge 7zip
# conda version: 25.5.1
+conda-forge/win-64::7zip-24.08-h49e36cd_1
# update specs: ['7zip']
==> 2025-07-23 00:24:41 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y nodejs pnpm -c conda-forge
# conda version: 25.5.1
+conda-forge/win-64::nodejs-24.4.1-he453025_0
+conda-forge/win-64::pnpm-10.13.1-h785286a_1
# update specs: ['pnpm', 'nodejs']
==> 2025-07-23 00:25:42 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y -c conda-forge ffmpeg
# conda version: 25.5.1
+conda-forge/win-64::ffmpeg-4.3.1-ha925a31_0
# update specs: ['ffmpeg']
==> 2025-07-23 00:31:41 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y cudnn libzlib-wapi -c conda-forge
# conda version: 25.5.1
-defaults/win-64::zlib-1.2.13-h8cc25b3_1
+conda-forge/noarch::cuda-version-12.9-h4f385c5_3
+conda-forge/win-64::cuda-nvrtc-12.9.86-he0c23c2_0
+conda-forge/win-64::cudnn-********-h1361d0a_0
+conda-forge/win-64::libcublas-********-he0c23c2_0
+conda-forge/win-64::libcudnn-********-hffc9a7f_0
+conda-forge/win-64::libcudnn-dev-********-hffc9a7f_0
+conda-forge/win-64::libzlib-1.2.13-h2466b09_6
+conda-forge/win-64::libzlib-wapi-1.2.13-h2466b09_6
+conda-forge/win-64::zlib-1.2.13-h2466b09_6
# update specs: ['cudnn', 'libzlib-wapi']
==> 2025-07-23 00:51:25 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y cuda -c nvidia/label/cuda-12.1.0
# conda version: 25.5.1
+nvidia/label/cuda-12.1.0/win-64::cuda-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-cccl-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-command-line-tools-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-compiler-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-cudart-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-cudart-dev-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-cuobjdump-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-cupti-12.1.62-0
+nvidia/label/cuda-12.1.0/win-64::cuda-cuxxfilt-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-demo-suite-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-documentation-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-libraries-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-libraries-dev-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nsight-compute-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvcc-12.1.66-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvdisasm-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvml-dev-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvprof-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvprune-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvrtc-dev-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvtx-12.1.66-0
+nvidia/label/cuda-12.1.0/win-64::cuda-nvvp-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-opencl-12.1.56-0
+nvidia/label/cuda-12.1.0/win-64::cuda-opencl-dev-12.1.56-0
+nvidia/label/cuda-12.1.0/win-64::cuda-profiler-api-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-runtime-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-sanitizer-api-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::cuda-toolkit-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-tools-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::cuda-visual-tools-12.1.0-0
+nvidia/label/cuda-12.1.0/win-64::libcublas-dev-12.1.0.26-0
+nvidia/label/cuda-12.1.0/win-64::libcufft-11.0.2.4-0
+nvidia/label/cuda-12.1.0/win-64::libcufft-dev-11.0.2.4-0
+nvidia/label/cuda-12.1.0/win-64::libcurand-10.3.2.56-0
+nvidia/label/cuda-12.1.0/win-64::libcurand-dev-10.3.2.56-0
+nvidia/label/cuda-12.1.0/win-64::libcusolver-11.4.4.55-0
+nvidia/label/cuda-12.1.0/win-64::libcusolver-dev-11.4.4.55-0
+nvidia/label/cuda-12.1.0/win-64::libcusparse-12.0.2.55-0
+nvidia/label/cuda-12.1.0/win-64::libcusparse-dev-12.0.2.55-0
+nvidia/label/cuda-12.1.0/win-64::libnpp-*********-0
+nvidia/label/cuda-12.1.0/win-64::libnpp-dev-*********-0
+nvidia/label/cuda-12.1.0/win-64::libnvjitlink-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::libnvjitlink-dev-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::libnvjpeg-*********-0
+nvidia/label/cuda-12.1.0/win-64::libnvjpeg-dev-*********-0
+nvidia/label/cuda-12.1.0/win-64::libnvvm-samples-12.1.55-0
+nvidia/label/cuda-12.1.0/win-64::nsight-compute-2023.1.0.15-0
# update specs: ['cuda']
==> 2025-07-23 00:52:50 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y -c conda-forge huggingface_hub
# conda version: 25.5.1
+conda-forge/noarch::_python_abi3_support-1.0-hd8ed1ab_2
+conda-forge/noarch::cpython-3.10.18-py310hd8ed1ab_0
+conda-forge/noarch::filelock-3.18.0-pyhd8ed1ab_0
+conda-forge/noarch::fsspec-2025.7.0-pyhd8ed1ab_0
+conda-forge/noarch::huggingface_hub-0.33.4-pyhd8ed1ab_0
+conda-forge/noarch::python-gil-3.10.18-hd8ed1ab_0
+conda-forge/win-64::hf-xet-1.1.5-py39h17685eb_3
+conda-forge/win-64::pyyaml-6.0.2-py310h38315fa_2
+conda-forge/win-64::yaml-0.2.5-h8ffe710_2
# update specs: ['huggingface_hub']
==> 2025-07-23 00:53:45 <==
# cmd: C:\pinokio\bin\miniconda\Scripts\conda-script.py install -y -c conda-forge uv
# conda version: 25.5.1
+conda-forge/win-64::uv-0.8.2-h579f82e_0
# update specs: ['uv']
