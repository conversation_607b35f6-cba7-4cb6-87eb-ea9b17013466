<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityPCSampling3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityPCSampling3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityPCSampling3" -->The activity record for PC sampling.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#9a21cfac5591387bce8a5a3b87dd09b6">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#db195817dc4a6192c48a74202baeb44f">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#ec2ac604ceefb2fa93022e578c786396">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#23d9b52b430d30985b4933e91377ca51">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#41dbe00ec06438094bde3a1402bc819a">latencySamples</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#f09b061063d1c9e7b4a989f7ef9c04ad">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#4cd90b008f537cce8237969e7ea60b2c">samples</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#e17dde3d0807f5835a0c515b45951c02">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling3.html#09c4bdf6f46401652f682c29cf0a5c56">stallReason</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records information obtained by sampling PC (CUPTI_ACTIVITY_KIND_PC_SAMPLING). <hr><h2>Field Documentation</h2>
<a class="anchor" name="9a21cfac5591387bce8a5a3b87dd09b6"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::correlationId" ref="9a21cfac5591387bce8a5a3b87dd09b6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling3.html#9a21cfac5591387bce8a5a3b87dd09b6">CUpti_ActivityPCSampling3::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="db195817dc4a6192c48a74202baeb44f"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::flags" ref="db195817dc4a6192c48a74202baeb44f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityPCSampling3.html#db195817dc4a6192c48a74202baeb44f">CUpti_ActivityPCSampling3::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this instruction. 
</div>
</div><p>
<a class="anchor" name="ec2ac604ceefb2fa93022e578c786396"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::functionId" ref="ec2ac604ceefb2fa93022e578c786396" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling3.html#ec2ac604ceefb2fa93022e578c786396">CUpti_ActivityPCSampling3::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="23d9b52b430d30985b4933e91377ca51"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::kind" ref="23d9b52b430d30985b4933e91377ca51" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityPCSampling3.html#23d9b52b430d30985b4933e91377ca51">CUpti_ActivityPCSampling3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_PC_SAMPLING. 
</div>
</div><p>
<a class="anchor" name="41dbe00ec06438094bde3a1402bc819a"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::latencySamples" ref="41dbe00ec06438094bde3a1402bc819a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling3.html#41dbe00ec06438094bde3a1402bc819a">CUpti_ActivityPCSampling3::latencySamples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of times the PC was sampled with the stallReason in the record. These samples indicate that no instruction was issued in that cycle from the warp scheduler from where the warp was sampled. Field is valid for devices with compute capability 6.0 and higher 
</div>
</div><p>
<a class="anchor" name="f09b061063d1c9e7b4a989f7ef9c04ad"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::pcOffset" ref="f09b061063d1c9e7b4a989f7ef9c04ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityPCSampling3.html#f09b061063d1c9e7b4a989f7ef9c04ad">CUpti_ActivityPCSampling3::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the instruction. 
</div>
</div><p>
<a class="anchor" name="4cd90b008f537cce8237969e7ea60b2c"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::samples" ref="4cd90b008f537cce8237969e7ea60b2c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling3.html#4cd90b008f537cce8237969e7ea60b2c">CUpti_ActivityPCSampling3::samples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of times the PC was sampled with the stallReason in the record. The same PC can be sampled with different stall reasons. The count includes latencySamples. 
</div>
</div><p>
<a class="anchor" name="e17dde3d0807f5835a0c515b45951c02"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::sourceLocatorId" ref="e17dde3d0807f5835a0c515b45951c02" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling3.html#e17dde3d0807f5835a0c515b45951c02">CUpti_ActivityPCSampling3::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="09c4bdf6f46401652f682c29cf0a5c56"></a><!-- doxytag: member="CUpti_ActivityPCSampling3::stallReason" ref="09c4bdf6f46401652f682c29cf0a5c56" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a> <a class="el" href="structCUpti__ActivityPCSampling3.html#09c4bdf6f46401652f682c29cf0a5c56">CUpti_ActivityPCSampling3::stallReason</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Current stall reason. Includes one of the reasons from <a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
