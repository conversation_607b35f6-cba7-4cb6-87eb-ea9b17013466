<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ResourceData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ResourceData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__CALLBACK__API.html">CUPTI Callback API</a>]</small>
</h1><!-- doxytag: class="CUpti_ResourceData" -->Data passed into a resource callback function.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ResourceData.html#a3d5a7b566d4bf6dc4ce57872d9d72a8">context</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ResourceData.html#fb6ad0494c0d5b882236ab9eda3a1e9b">resourceDescriptor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUstream&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ResourceData.html#c25bd7f69d64d6f1b5b710d9da4130ed">stream</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Data passed into a resource callback function as the <code>cbdata</code> argument to <a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a>. The <code>cbdata</code> will be this type for <code>domain</code> equal to CUPTI_CB_DOMAIN_RESOURCE. The callback data is valid only within the invocation of the callback function that is passed the data. If you need to retain some data for use outside of the callback, you must make a copy of that data. <hr><h2>Field Documentation</h2>
<a class="anchor" name="a3d5a7b566d4bf6dc4ce57872d9d72a8"></a><!-- doxytag: member="CUpti_ResourceData::context" ref="a3d5a7b566d4bf6dc4ce57872d9d72a8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="structCUpti__ResourceData.html#a3d5a7b566d4bf6dc4ce57872d9d72a8">CUpti_ResourceData::context</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For CUPTI_CBID_RESOURCE_CONTEXT_CREATED and CUPTI_CBID_RESOURCE_CONTEXT_DESTROY_STARTING, the context being created or destroyed. For CUPTI_CBID_RESOURCE_STREAM_CREATED and CUPTI_CBID_RESOURCE_STREAM_DESTROY_STARTING, the context containing the stream being created or destroyed. 
</div>
</div><p>
<a class="anchor" name="fb6ad0494c0d5b882236ab9eda3a1e9b"></a><!-- doxytag: member="CUpti_ResourceData::resourceDescriptor" ref="fb6ad0494c0d5b882236ab9eda3a1e9b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ResourceData.html#fb6ad0494c0d5b882236ab9eda3a1e9b">CUpti_ResourceData::resourceDescriptor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Reserved for future use. 
</div>
</div><p>
<a class="anchor" name="c25bd7f69d64d6f1b5b710d9da4130ed"></a><!-- doxytag: member="CUpti_ResourceData::stream" ref="c25bd7f69d64d6f1b5b710d9da4130ed" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUstream <a class="el" href="structCUpti__ResourceData.html#c25bd7f69d64d6f1b5b710d9da4130ed">CUpti_ResourceData::stream</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For CUPTI_CBID_RESOURCE_STREAM_CREATED and CUPTI_CBID_RESOURCE_STREAM_DESTROY_STARTING, the stream being created or destroyed. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
