{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurand-10.3.2.56-0", "features": "", "files": ["lib/x64/curand.lib"], "fn": "libcurand-10.3.2.56-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurand-10.3.2.56-0", "type": 1}, "md5": "19faeb9f624e8106f2c804d5d08ebfae", "name": "lib<PERSON><PERSON>", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcurand-10.3.2.56-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/curand.lib", "path_type": "hardlink", "sha256": "947527aaeec7364727077681c29abe06bddc043c868d6ac890bbcbb16b913b9d", "sha256_in_prefix": "947527aaeec7364727077681c29abe06bddc043c868d6ac890bbcbb16b913b9d", "size_in_bytes": 8534}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 3361, "subdir": "win-64", "timestamp": 1674624610000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libcurand-10.3.2.56-0.tar.bz2", "version": "10.3.2.56"}