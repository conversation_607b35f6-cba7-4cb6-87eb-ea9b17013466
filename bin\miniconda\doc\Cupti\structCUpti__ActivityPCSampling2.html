<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityPCSampling2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityPCSampling2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityPCSampling2" -->The activity record for PC sampling. (deprecated in CUDA 9.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#644805aa5b91ab6dcedfef2fe78ac155">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#48b54f9ebe412db579a739569fb51f1c">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#a79550dec752213b2af90dba0d3e72a8">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#28207c40ebe7399f550e62ddb4d2a02d">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#961407bd5c98c35ea90f4b73ec9f6bf4">latencySamples</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#16fffe727d36705fc60c9766ea32bbe2">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#9f9220a522b755527950accaf0bd8aac">samples</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#3d2b206f639e283376916059e3d913a3">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPCSampling2.html#c2b5f65abc42c53abe8572850e6692a9">stallReason</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records information obtained by sampling PC (CUPTI_ACTIVITY_KIND_PC_SAMPLING). PC sampling activities are now reported using the <a class="el" href="structCUpti__ActivityPCSampling3.html" title="The activity record for PC sampling.">CUpti_ActivityPCSampling3</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="644805aa5b91ab6dcedfef2fe78ac155"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::correlationId" ref="644805aa5b91ab6dcedfef2fe78ac155" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling2.html#644805aa5b91ab6dcedfef2fe78ac155">CUpti_ActivityPCSampling2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="48b54f9ebe412db579a739569fb51f1c"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::flags" ref="48b54f9ebe412db579a739569fb51f1c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityPCSampling2.html#48b54f9ebe412db579a739569fb51f1c">CUpti_ActivityPCSampling2::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this instruction. 
</div>
</div><p>
<a class="anchor" name="a79550dec752213b2af90dba0d3e72a8"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::functionId" ref="a79550dec752213b2af90dba0d3e72a8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling2.html#a79550dec752213b2af90dba0d3e72a8">CUpti_ActivityPCSampling2::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="28207c40ebe7399f550e62ddb4d2a02d"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::kind" ref="28207c40ebe7399f550e62ddb4d2a02d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityPCSampling2.html#28207c40ebe7399f550e62ddb4d2a02d">CUpti_ActivityPCSampling2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_PC_SAMPLING. 
</div>
</div><p>
<a class="anchor" name="961407bd5c98c35ea90f4b73ec9f6bf4"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::latencySamples" ref="961407bd5c98c35ea90f4b73ec9f6bf4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling2.html#961407bd5c98c35ea90f4b73ec9f6bf4">CUpti_ActivityPCSampling2::latencySamples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of times the PC was sampled with the stallReason in the record. These samples indicate that no instruction was issued in that cycle from the warp scheduler from where the warp was sampled. Field is valid for devices with compute capability 6.0 and higher 
</div>
</div><p>
<a class="anchor" name="16fffe727d36705fc60c9766ea32bbe2"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::pcOffset" ref="16fffe727d36705fc60c9766ea32bbe2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling2.html#16fffe727d36705fc60c9766ea32bbe2">CUpti_ActivityPCSampling2::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the instruction. 
</div>
</div><p>
<a class="anchor" name="9f9220a522b755527950accaf0bd8aac"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::samples" ref="9f9220a522b755527950accaf0bd8aac" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling2.html#9f9220a522b755527950accaf0bd8aac">CUpti_ActivityPCSampling2::samples</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of times the PC was sampled with the stallReason in the record. The same PC can be sampled with different stall reasons. The count includes latencySamples. 
</div>
</div><p>
<a class="anchor" name="3d2b206f639e283376916059e3d913a3"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::sourceLocatorId" ref="3d2b206f639e283376916059e3d913a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPCSampling2.html#3d2b206f639e283376916059e3d913a3">CUpti_ActivityPCSampling2::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="c2b5f65abc42c53abe8572850e6692a9"></a><!-- doxytag: member="CUpti_ActivityPCSampling2::stallReason" ref="c2b5f65abc42c53abe8572850e6692a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a> <a class="el" href="structCUpti__ActivityPCSampling2.html#c2b5f65abc42c53abe8572850e6692a9">CUpti_ActivityPCSampling2::stallReason</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Current stall reason. Includes one of the reasons from <a class="el" href="group__CUPTI__ACTIVITY__API.html#g57bafb9baafeae0880dae6eaa1a8e12d">CUpti_ActivityPCSamplingStallReason</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
