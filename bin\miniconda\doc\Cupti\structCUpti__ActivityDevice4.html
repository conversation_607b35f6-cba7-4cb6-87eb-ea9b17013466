<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityDevice4 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityDevice4 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityDevice4" -->The activity record for a device. (CUDA 11.6 onwards).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#b486f6b884f1064179fc892eebe69bea">computeCapabilityMajor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#3b7e328f0bf453a96a8c3961d43bbaa9">computeCapabilityMinor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#3684f9752a3b1cd8956ad6e38e66b1a0">computeInstanceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#06692a5be5bdeb9c304822ea329dbb13">constantMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#13f173a627df6380783988d625197a20">coreClockRate</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#c35f658d021675ac8166ce7890d4b7ef">eccEnabled</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#9bb3a90c90223464d90e1c3e53ea8f0b">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#acd75ceee5a8e1881a291e23366aed0d">globalMemoryBandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#758f86c7e49bfb54bd3efe6a0f2114e7">globalMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#aecf8527085fdc2244508778110f73ab">gpuInstanceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#be72f2460387e4d90ce4e2290521aec8">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#17cee191aea6852f11b10a6516bd5a80">isCudaVisible</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#4771935964fade3b950c2e21519c9dd6">isMigEnabled</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#b5e8e0f9d74b21c5b00523a9f79a2750">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#e6649c324d5b3b654c43cf939fb3014b">l2CacheSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#1604f10e3e23230e64425cb4c3fd8dff">maxBlockDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#3042415a52810209895bedbbb24604cb">maxBlockDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#c27990ba772d6427cd20310b27f990b9">maxBlockDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#6135d1bf4955359585eeefa04a81993c">maxBlocksPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#a4a54e3d8bf51d13c4767659fa6a4ce8">maxGridDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#278c643ad3c3bf68176043eba552ab3c">maxGridDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#a9895adfbe1d7a77066cc5f3d41c994d">maxGridDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#78f7448e51a6a48d12948e8a3a966520">maxIPC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#89b91cff6a48bc9e8b40fe485cafd335">maxRegistersPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#72c97a93e745ff3d5850350850090dbb">maxRegistersPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#c8129e482993ab5ae51ee9d60161fd7b">maxSharedMemoryPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#7e2687c87eec55bf34f2d8753b3a9ae9">maxSharedMemoryPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#f2f64d2367314bbd8fd746c952eaa62a">maxThreadsPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#bf236a35f937e0526532c3d9a9d5033d">maxWarpsPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUuuid&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#16b3123d076b965451fcbcd0e94d091c">migUuid</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#4e1a2f98b3ba230045e977be43e3c95a">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#d681fcc27d1b233e67ff24f75b30495d">numMemcpyEngines</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#3a41a14294f152f369456a5c29a386e6">numMultiprocessors</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#35d812373cb37ff3f1c2b28a1bfc202f">numThreadsPerWarp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#01abbe9d8286128d05007fee7868bc33">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUuuid&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice4.html#fc9573ae4bb2b1575f789d8ea4cf7782">uuid</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents information about a GPU device (CUPTI_ACTIVITY_KIND_DEVICE). <hr><h2>Field Documentation</h2>
<a class="anchor" name="b486f6b884f1064179fc892eebe69bea"></a><!-- doxytag: member="CUpti_ActivityDevice4::computeCapabilityMajor" ref="b486f6b884f1064179fc892eebe69bea" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#b486f6b884f1064179fc892eebe69bea">CUpti_ActivityDevice4::computeCapabilityMajor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, major number. 
</div>
</div><p>
<a class="anchor" name="3b7e328f0bf453a96a8c3961d43bbaa9"></a><!-- doxytag: member="CUpti_ActivityDevice4::computeCapabilityMinor" ref="3b7e328f0bf453a96a8c3961d43bbaa9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#3b7e328f0bf453a96a8c3961d43bbaa9">CUpti_ActivityDevice4::computeCapabilityMinor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, minor number. 
</div>
</div><p>
<a class="anchor" name="3684f9752a3b1cd8956ad6e38e66b1a0"></a><!-- doxytag: member="CUpti_ActivityDevice4::computeInstanceId" ref="3684f9752a3b1cd8956ad6e38e66b1a0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#3684f9752a3b1cd8956ad6e38e66b1a0">CUpti_ActivityDevice4::computeInstanceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute Instance id for MIG enabled devices. If mig mode is disabled value is set to UINT32_MAX 
</div>
</div><p>
<a class="anchor" name="06692a5be5bdeb9c304822ea329dbb13"></a><!-- doxytag: member="CUpti_ActivityDevice4::constantMemorySize" ref="06692a5be5bdeb9c304822ea329dbb13" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#06692a5be5bdeb9c304822ea329dbb13">CUpti_ActivityDevice4::constantMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of constant memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="13f173a627df6380783988d625197a20"></a><!-- doxytag: member="CUpti_ActivityDevice4::coreClockRate" ref="13f173a627df6380783988d625197a20" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#13f173a627df6380783988d625197a20">CUpti_ActivityDevice4::coreClockRate</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The core clock rate of the device, in kHz. 
</div>
</div><p>
<a class="anchor" name="c35f658d021675ac8166ce7890d4b7ef"></a><!-- doxytag: member="CUpti_ActivityDevice4::eccEnabled" ref="c35f658d021675ac8166ce7890d4b7ef" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#c35f658d021675ac8166ce7890d4b7ef">CUpti_ActivityDevice4::eccEnabled</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ECC enabled flag for device 
</div>
</div><p>
<a class="anchor" name="9bb3a90c90223464d90e1c3e53ea8f0b"></a><!-- doxytag: member="CUpti_ActivityDevice4::flags" ref="9bb3a90c90223464d90e1c3e53ea8f0b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityDevice4.html#9bb3a90c90223464d90e1c3e53ea8f0b">CUpti_ActivityDevice4::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the device. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="acd75ceee5a8e1881a291e23366aed0d"></a><!-- doxytag: member="CUpti_ActivityDevice4::globalMemoryBandwidth" ref="acd75ceee5a8e1881a291e23366aed0d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice4.html#acd75ceee5a8e1881a291e23366aed0d">CUpti_ActivityDevice4::globalMemoryBandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The global memory bandwidth available on the device, in kBytes/sec. 
</div>
</div><p>
<a class="anchor" name="758f86c7e49bfb54bd3efe6a0f2114e7"></a><!-- doxytag: member="CUpti_ActivityDevice4::globalMemorySize" ref="758f86c7e49bfb54bd3efe6a0f2114e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice4.html#758f86c7e49bfb54bd3efe6a0f2114e7">CUpti_ActivityDevice4::globalMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of global memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="aecf8527085fdc2244508778110f73ab"></a><!-- doxytag: member="CUpti_ActivityDevice4::gpuInstanceId" ref="aecf8527085fdc2244508778110f73ab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#aecf8527085fdc2244508778110f73ab">CUpti_ActivityDevice4::gpuInstanceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
GPU Instance id for MIG enabled devices. If mig mode is disabled value is set to UINT32_MAX 
</div>
</div><p>
<a class="anchor" name="be72f2460387e4d90ce4e2290521aec8"></a><!-- doxytag: member="CUpti_ActivityDevice4::id" ref="be72f2460387e4d90ce4e2290521aec8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#be72f2460387e4d90ce4e2290521aec8">CUpti_ActivityDevice4::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device ID. 
</div>
</div><p>
<a class="anchor" name="17cee191aea6852f11b10a6516bd5a80"></a><!-- doxytag: member="CUpti_ActivityDevice4::isCudaVisible" ref="17cee191aea6852f11b10a6516bd5a80" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityDevice4.html#17cee191aea6852f11b10a6516bd5a80">CUpti_ActivityDevice4::isCudaVisible</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flag to indicate whether the device is visible to CUDA. Users can set the device visibility using CUDA_VISIBLE_DEVICES environment 
</div>
</div><p>
<a class="anchor" name="4771935964fade3b950c2e21519c9dd6"></a><!-- doxytag: member="CUpti_ActivityDevice4::isMigEnabled" ref="4771935964fade3b950c2e21519c9dd6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityDevice4.html#4771935964fade3b950c2e21519c9dd6">CUpti_ActivityDevice4::isMigEnabled</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
MIG enabled flag for device 
</div>
</div><p>
<a class="anchor" name="b5e8e0f9d74b21c5b00523a9f79a2750"></a><!-- doxytag: member="CUpti_ActivityDevice4::kind" ref="b5e8e0f9d74b21c5b00523a9f79a2750" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityDevice4.html#b5e8e0f9d74b21c5b00523a9f79a2750">CUpti_ActivityDevice4::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_DEVICE. 
</div>
</div><p>
<a class="anchor" name="e6649c324d5b3b654c43cf939fb3014b"></a><!-- doxytag: member="CUpti_ActivityDevice4::l2CacheSize" ref="e6649c324d5b3b654c43cf939fb3014b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#e6649c324d5b3b654c43cf939fb3014b">CUpti_ActivityDevice4::l2CacheSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the L2 cache on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="1604f10e3e23230e64425cb4c3fd8dff"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxBlockDimX" ref="1604f10e3e23230e64425cb4c3fd8dff" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#1604f10e3e23230e64425cb4c3fd8dff">CUpti_ActivityDevice4::maxBlockDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a block. 
</div>
</div><p>
<a class="anchor" name="3042415a52810209895bedbbb24604cb"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxBlockDimY" ref="3042415a52810209895bedbbb24604cb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#3042415a52810209895bedbbb24604cb">CUpti_ActivityDevice4::maxBlockDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a block. 
</div>
</div><p>
<a class="anchor" name="c27990ba772d6427cd20310b27f990b9"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxBlockDimZ" ref="c27990ba772d6427cd20310b27f990b9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#c27990ba772d6427cd20310b27f990b9">CUpti_ActivityDevice4::maxBlockDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a block. 
</div>
</div><p>
<a class="anchor" name="6135d1bf4955359585eeefa04a81993c"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxBlocksPerMultiprocessor" ref="6135d1bf4955359585eeefa04a81993c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#6135d1bf4955359585eeefa04a81993c">CUpti_ActivityDevice4::maxBlocksPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of blocks that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="a4a54e3d8bf51d13c4767659fa6a4ce8"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxGridDimX" ref="a4a54e3d8bf51d13c4767659fa6a4ce8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#a4a54e3d8bf51d13c4767659fa6a4ce8">CUpti_ActivityDevice4::maxGridDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="278c643ad3c3bf68176043eba552ab3c"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxGridDimY" ref="278c643ad3c3bf68176043eba552ab3c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#278c643ad3c3bf68176043eba552ab3c">CUpti_ActivityDevice4::maxGridDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="a9895adfbe1d7a77066cc5f3d41c994d"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxGridDimZ" ref="a9895adfbe1d7a77066cc5f3d41c994d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#a9895adfbe1d7a77066cc5f3d41c994d">CUpti_ActivityDevice4::maxGridDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="78f7448e51a6a48d12948e8a3a966520"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxIPC" ref="78f7448e51a6a48d12948e8a3a966520" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#78f7448e51a6a48d12948e8a3a966520">CUpti_ActivityDevice4::maxIPC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The maximum "instructions per cycle" possible on each device multiprocessor. 
</div>
</div><p>
<a class="anchor" name="89b91cff6a48bc9e8b40fe485cafd335"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxRegistersPerBlock" ref="89b91cff6a48bc9e8b40fe485cafd335" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#89b91cff6a48bc9e8b40fe485cafd335">CUpti_ActivityDevice4::maxRegistersPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of registers that can be allocated to a block. 
</div>
</div><p>
<a class="anchor" name="72c97a93e745ff3d5850350850090dbb"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxRegistersPerMultiprocessor" ref="72c97a93e745ff3d5850350850090dbb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#72c97a93e745ff3d5850350850090dbb">CUpti_ActivityDevice4::maxRegistersPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of 32-bit registers available per multiprocessor. 
</div>
</div><p>
<a class="anchor" name="c8129e482993ab5ae51ee9d60161fd7b"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxSharedMemoryPerBlock" ref="c8129e482993ab5ae51ee9d60161fd7b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#c8129e482993ab5ae51ee9d60161fd7b">CUpti_ActivityDevice4::maxSharedMemoryPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum amount of shared memory that can be assigned to a block, in bytes. 
</div>
</div><p>
<a class="anchor" name="7e2687c87eec55bf34f2d8753b3a9ae9"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxSharedMemoryPerMultiprocessor" ref="7e2687c87eec55bf34f2d8753b3a9ae9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#7e2687c87eec55bf34f2d8753b3a9ae9">CUpti_ActivityDevice4::maxSharedMemoryPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum amount of shared memory available per multiprocessor, in bytes. 
</div>
</div><p>
<a class="anchor" name="f2f64d2367314bbd8fd746c952eaa62a"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxThreadsPerBlock" ref="f2f64d2367314bbd8fd746c952eaa62a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#f2f64d2367314bbd8fd746c952eaa62a">CUpti_ActivityDevice4::maxThreadsPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of threads allowed in a block. 
</div>
</div><p>
<a class="anchor" name="bf236a35f937e0526532c3d9a9d5033d"></a><!-- doxytag: member="CUpti_ActivityDevice4::maxWarpsPerMultiprocessor" ref="bf236a35f937e0526532c3d9a9d5033d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#bf236a35f937e0526532c3d9a9d5033d">CUpti_ActivityDevice4::maxWarpsPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of warps that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="16b3123d076b965451fcbcd0e94d091c"></a><!-- doxytag: member="CUpti_ActivityDevice4::migUuid" ref="16b3123d076b965451fcbcd0e94d091c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUuuid <a class="el" href="structCUpti__ActivityDevice4.html#16b3123d076b965451fcbcd0e94d091c">CUpti_ActivityDevice4::migUuid</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The MIG UUID. This value is the globally unique immutable alphanumeric identifier of the device. 
</div>
</div><p>
<a class="anchor" name="4e1a2f98b3ba230045e977be43e3c95a"></a><!-- doxytag: member="CUpti_ActivityDevice4::name" ref="4e1a2f98b3ba230045e977be43e3c95a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityDevice4.html#4e1a2f98b3ba230045e977be43e3c95a">CUpti_ActivityDevice4::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device name. This name is shared across all activity records representing instances of the device, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="d681fcc27d1b233e67ff24f75b30495d"></a><!-- doxytag: member="CUpti_ActivityDevice4::numMemcpyEngines" ref="d681fcc27d1b233e67ff24f75b30495d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#d681fcc27d1b233e67ff24f75b30495d">CUpti_ActivityDevice4::numMemcpyEngines</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of memory copy engines on the device. 
</div>
</div><p>
<a class="anchor" name="3a41a14294f152f369456a5c29a386e6"></a><!-- doxytag: member="CUpti_ActivityDevice4::numMultiprocessors" ref="3a41a14294f152f369456a5c29a386e6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#3a41a14294f152f369456a5c29a386e6">CUpti_ActivityDevice4::numMultiprocessors</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of multiprocessors on the device. 
</div>
</div><p>
<a class="anchor" name="35d812373cb37ff3f1c2b28a1bfc202f"></a><!-- doxytag: member="CUpti_ActivityDevice4::numThreadsPerWarp" ref="35d812373cb37ff3f1c2b28a1bfc202f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#35d812373cb37ff3f1c2b28a1bfc202f">CUpti_ActivityDevice4::numThreadsPerWarp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of threads per warp on the device. 
</div>
</div><p>
<a class="anchor" name="01abbe9d8286128d05007fee7868bc33"></a><!-- doxytag: member="CUpti_ActivityDevice4::pad" ref="01abbe9d8286128d05007fee7868bc33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice4.html#01abbe9d8286128d05007fee7868bc33">CUpti_ActivityDevice4::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="fc9573ae4bb2b1575f789d8ea4cf7782"></a><!-- doxytag: member="CUpti_ActivityDevice4::uuid" ref="fc9573ae4bb2b1575f789d8ea4cf7782" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUuuid <a class="el" href="structCUpti__ActivityDevice4.html#fc9573ae4bb2b1575f789d8ea4cf7782">CUpti_ActivityDevice4::uuid</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device UUID. This value is the globally unique immutable alphanumeric identifier of the device. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
