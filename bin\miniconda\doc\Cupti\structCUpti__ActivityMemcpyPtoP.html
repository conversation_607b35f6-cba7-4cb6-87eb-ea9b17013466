<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpyPtoP Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpyPtoP Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpyPtoP" -->The activity record for peer-to-peer memory copies.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#3a6b0871c5161cb8e4cd8e836e2588f3">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#c2c9a5cf0e23b37eae4ba963956d2a2c">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#af181be654f6a3701fc543378b1126e8">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#e72a3d638e2ed03b81f308cd69f53662">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#e1f074c068a8f80a13dc2b0dd426b4ba">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#8df3d02d9e6ec1682f045df55d30312f">dstContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#960e9b3b65ca69895ffc7e98a12154eb">dstDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#5c0a008780c40f7545a6b09d2f279b9f">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#07f09f693bb1e992290561f97719600b">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#99a3ffbf3db91324072d87d855c3cf2a">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#d8899d711c4534b7a2ce0f4384f49976">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#847820efa98a41794b7c8e14eea7f47a">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#3b7b5615cbc5940dd7f918ace8931e12">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#5fed0c4ba667570ac9513bc59b9e00c9">srcContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#796c868b26586812da12ced78e9d3b0b">srcDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#ffd22a34324a0f852af3e5f6bf9154f3">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#bb7de7ad6fbdce94d442fd96337d97eb">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP.html#d35d0f668befb8c6a3dbeca382b2365f">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a peer-to-peer memory copy (CUPTI_ACTIVITY_KIND_MEMCPY2) but is no longer generated by CUPTI. Peer-to-peer memory copy activities are now reported using the <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html" title="The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1).">CUpti_ActivityMemcpyPtoP2</a> activity record.. <hr><h2>Field Documentation</h2>
<a class="anchor" name="3a6b0871c5161cb8e4cd8e836e2588f3"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::bytes" ref="3a6b0871c5161cb8e4cd8e836e2588f3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#3a6b0871c5161cb8e4cd8e836e2588f3">CUpti_ActivityMemcpyPtoP::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="c2c9a5cf0e23b37eae4ba963956d2a2c"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::contextId" ref="c2c9a5cf0e23b37eae4ba963956d2a2c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#c2c9a5cf0e23b37eae4ba963956d2a2c">CUpti_ActivityMemcpyPtoP::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="af181be654f6a3701fc543378b1126e8"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::copyKind" ref="af181be654f6a3701fc543378b1126e8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#af181be654f6a3701fc543378b1126e8">CUpti_ActivityMemcpyPtoP::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="e72a3d638e2ed03b81f308cd69f53662"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::correlationId" ref="e72a3d638e2ed03b81f308cd69f53662" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#e72a3d638e2ed03b81f308cd69f53662">CUpti_ActivityMemcpyPtoP::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="e1f074c068a8f80a13dc2b0dd426b4ba"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::deviceId" ref="e1f074c068a8f80a13dc2b0dd426b4ba" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#e1f074c068a8f80a13dc2b0dd426b4ba">CUpti_ActivityMemcpyPtoP::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="8df3d02d9e6ec1682f045df55d30312f"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::dstContextId" ref="8df3d02d9e6ec1682f045df55d30312f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#8df3d02d9e6ec1682f045df55d30312f">CUpti_ActivityMemcpyPtoP::dstContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied to. 
</div>
</div><p>
<a class="anchor" name="960e9b3b65ca69895ffc7e98a12154eb"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::dstDeviceId" ref="960e9b3b65ca69895ffc7e98a12154eb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#960e9b3b65ca69895ffc7e98a12154eb">CUpti_ActivityMemcpyPtoP::dstDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied to. 
</div>
</div><p>
<a class="anchor" name="5c0a008780c40f7545a6b09d2f279b9f"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::dstKind" ref="5c0a008780c40f7545a6b09d2f279b9f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#5c0a008780c40f7545a6b09d2f279b9f">CUpti_ActivityMemcpyPtoP::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="07f09f693bb1e992290561f97719600b"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::end" ref="07f09f693bb1e992290561f97719600b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#07f09f693bb1e992290561f97719600b">CUpti_ActivityMemcpyPtoP::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="99a3ffbf3db91324072d87d855c3cf2a"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::flags" ref="99a3ffbf3db91324072d87d855c3cf2a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#99a3ffbf3db91324072d87d855c3cf2a">CUpti_ActivityMemcpyPtoP::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="d8899d711c4534b7a2ce0f4384f49976"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::kind" ref="d8899d711c4534b7a2ce0f4384f49976" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#d8899d711c4534b7a2ce0f4384f49976">CUpti_ActivityMemcpyPtoP::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY2. 
</div>
</div><p>
<a class="anchor" name="847820efa98a41794b7c8e14eea7f47a"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::pad" ref="847820efa98a41794b7c8e14eea7f47a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#847820efa98a41794b7c8e14eea7f47a">CUpti_ActivityMemcpyPtoP::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="3b7b5615cbc5940dd7f918ace8931e12"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::reserved0" ref="3b7b5615cbc5940dd7f918ace8931e12" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#3b7b5615cbc5940dd7f918ace8931e12">CUpti_ActivityMemcpyPtoP::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="5fed0c4ba667570ac9513bc59b9e00c9"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::srcContextId" ref="5fed0c4ba667570ac9513bc59b9e00c9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#5fed0c4ba667570ac9513bc59b9e00c9">CUpti_ActivityMemcpyPtoP::srcContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied from. 
</div>
</div><p>
<a class="anchor" name="796c868b26586812da12ced78e9d3b0b"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::srcDeviceId" ref="796c868b26586812da12ced78e9d3b0b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#796c868b26586812da12ced78e9d3b0b">CUpti_ActivityMemcpyPtoP::srcDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied from. 
</div>
</div><p>
<a class="anchor" name="ffd22a34324a0f852af3e5f6bf9154f3"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::srcKind" ref="ffd22a34324a0f852af3e5f6bf9154f3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#ffd22a34324a0f852af3e5f6bf9154f3">CUpti_ActivityMemcpyPtoP::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="bb7de7ad6fbdce94d442fd96337d97eb"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::start" ref="bb7de7ad6fbdce94d442fd96337d97eb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#bb7de7ad6fbdce94d442fd96337d97eb">CUpti_ActivityMemcpyPtoP::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="d35d0f668befb8c6a3dbeca382b2365f"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP::streamId" ref="d35d0f668befb8c6a3dbeca382b2365f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#d35d0f668befb8c6a3dbeca382b2365f">CUpti_ActivityMemcpyPtoP::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
