<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityOpenAccData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityOpenAccData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityOpenAccData" -->The activity record for OpenACC data.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#f4085ad746614e100684b3df2075482d">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#b0010f5478fe49a056db03acde332778">cuContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#f4a231cab24d941f13ba728f0a8b3109">cuDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#8191f559463d3c9010ccc9f8e7152617">cuProcessId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#eb71e6f91975fb8818856ed196c046b6">cuStreamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#cb345df2cc10791c0caefb14b25c44fe">cuThreadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#8190cce66c42a0d560c413fada4b5224">devicePtr</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#a1c632d5e5a81b1cbd1ebd5a24cb2156">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#6df560544faee7bb2e7814bd385da964">eventKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#38d62d6c2b3634c119a1451638edd2ce">externalId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#11c19586c925014e63a59c21739bdb57">hostPtr</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#23c56c1e661c254195e25740fc23ece0">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#70b525b93b3830f1ba1fdbb23f2bd99e">pad1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#9bb4b306c68535c753e0d740fdc4f46f">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccData.html#10c9b3f640c146f293a54bd8747aaa60">threadId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
(CUPTI_ACTIVITY_KIND_OPENACC_DATA). <hr><h2>Field Documentation</h2>
<a class="anchor" name="f4085ad746614e100684b3df2075482d"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::bytes" ref="f4085ad746614e100684b3df2075482d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccData.html#f4085ad746614e100684b3df2075482d">CUpti_ActivityOpenAccData::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of bytes 
</div>
</div><p>
<a class="anchor" name="b0010f5478fe49a056db03acde332778"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::cuContextId" ref="b0010f5478fe49a056db03acde332778" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#b0010f5478fe49a056db03acde332778">CUpti_ActivityOpenAccData::cuContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA context id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="f4a231cab24d941f13ba728f0a8b3109"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::cuDeviceId" ref="f4a231cab24d941f13ba728f0a8b3109" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#f4a231cab24d941f13ba728f0a8b3109">CUpti_ActivityOpenAccData::cuDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA device id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="8191f559463d3c9010ccc9f8e7152617"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::cuProcessId" ref="8191f559463d3c9010ccc9f8e7152617" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#8191f559463d3c9010ccc9f8e7152617">CUpti_ActivityOpenAccData::cuProcessId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="eb71e6f91975fb8818856ed196c046b6"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::cuStreamId" ref="eb71e6f91975fb8818856ed196c046b6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#eb71e6f91975fb8818856ed196c046b6">CUpti_ActivityOpenAccData::cuStreamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA stream id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="cb345df2cc10791c0caefb14b25c44fe"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::cuThreadId" ref="cb345df2cc10791c0caefb14b25c44fe" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#cb345df2cc10791c0caefb14b25c44fe">CUpti_ActivityOpenAccData::cuThreadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the thread where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="8190cce66c42a0d560c413fada4b5224"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::devicePtr" ref="8190cce66c42a0d560c413fada4b5224" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccData.html#8190cce66c42a0d560c413fada4b5224">CUpti_ActivityOpenAccData::devicePtr</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device pointer if available 
</div>
</div><p>
<a class="anchor" name="a1c632d5e5a81b1cbd1ebd5a24cb2156"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::end" ref="a1c632d5e5a81b1cbd1ebd5a24cb2156" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccData.html#a1c632d5e5a81b1cbd1ebd5a24cb2156">CUpti_ActivityOpenAccData::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI end timestamp 
</div>
</div><p>
<a class="anchor" name="6df560544faee7bb2e7814bd385da964"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::eventKind" ref="6df560544faee7bb2e7814bd385da964" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a> <a class="el" href="structCUpti__ActivityOpenAccData.html#6df560544faee7bb2e7814bd385da964">CUpti_ActivityOpenAccData::eventKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenACC event kind (<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717" title="The OpenAcc event kind for OpenAcc activity records.">CUpti_OpenAccEventKind</a>) </dd></dl>

</div>
</div><p>
<a class="anchor" name="38d62d6c2b3634c119a1451638edd2ce"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::externalId" ref="38d62d6c2b3634c119a1451638edd2ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#38d62d6c2b3634c119a1451638edd2ce">CUpti_ActivityOpenAccData::externalId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The OpenACC correlation ID. Valid only if deviceType is acc_device_nvidia. If not 0, it uniquely identifies this record. It is identical to the externalId in the preceeding external correlation record of type CUPTI_EXTERNAL_CORRELATION_KIND_OPENACC. 
</div>
</div><p>
<a class="anchor" name="11c19586c925014e63a59c21739bdb57"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::hostPtr" ref="11c19586c925014e63a59c21739bdb57" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccData.html#11c19586c925014e63a59c21739bdb57">CUpti_ActivityOpenAccData::hostPtr</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Host pointer if available 
</div>
</div><p>
<a class="anchor" name="23c56c1e661c254195e25740fc23ece0"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::kind" ref="23c56c1e661c254195e25740fc23ece0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityOpenAccData.html#23c56c1e661c254195e25740fc23ece0">CUpti_ActivityOpenAccData::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_OPENACC_DATA. 
</div>
</div><p>
<a class="anchor" name="70b525b93b3830f1ba1fdbb23f2bd99e"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::pad1" ref="70b525b93b3830f1ba1fdbb23f2bd99e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#70b525b93b3830f1ba1fdbb23f2bd99e">CUpti_ActivityOpenAccData::pad1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="9bb4b306c68535c753e0d740fdc4f46f"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::start" ref="9bb4b306c68535c753e0d740fdc4f46f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccData.html#9bb4b306c68535c753e0d740fdc4f46f">CUpti_ActivityOpenAccData::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI start timestamp 
</div>
</div><p>
<a class="anchor" name="10c9b3f640c146f293a54bd8747aaa60"></a><!-- doxytag: member="CUpti_ActivityOpenAccData::threadId" ref="10c9b3f640c146f293a54bd8747aaa60" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccData.html#10c9b3f640c146f293a54bd8747aaa60">CUpti_ActivityOpenAccData::threadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ThreadId 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
