<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityOpenAccOther Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityOpenAccOther Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityOpenAccOther" -->The activity record for OpenACC other.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#703828877b868e7068d7b902b4b5a5a7">async</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#3e6779087d5cf058d4d7b771de3c8550">asyncMap</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#a3bbac0d04d417352041f1d0e8110ab6">cuContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#ffe555c3d60dcb8770e3052b74b30f0b">cuDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#294ffb92ef4cf0e943867ac55fd73a82">cuProcessId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#ad7fcaeec49a422e87bef2071cda098e">cuStreamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#b6786ac497f391b3090d164376a53151">cuThreadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#78ee47377101651ccbe5be7d826a0126">deviceNumber</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#8cf7fb10252ceca1a2c089920f735507">deviceType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#e6fce4b07317091d45c4f88b781827d6">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#99cd7548f7706d58caf4835693ef14a6">endLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#a65025a8e30dd956d9c194153500dae5">eventKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#e4bddc0b6ccca9b232fcbc5bed99906a">externalId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#49e27bd7d04829bebab835603cf18d60">funcEndLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#2dae9dea02831e90bd3d29e8b90f37ad">funcLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#2707237f98dd557b8c97a4cd15a5b8c8">funcName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#2cd615f39bac31a2f6bcc2680343ea4f">implicit</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#a81755d8f5c22808a23c595677c2f1d3">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#958003e64ec16ea0b54d354b5cdeb7fa">lineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e">CUpti_OpenAccConstructKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#3d6a65793fd64b90bf620bfb8d66ebf5">parentConstruct</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#010c5873a04729c4c0b605f616d401e5">srcFile</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#216adf7e2087154e20c490da402c5460">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#ea584646498d30acfb9a3722d422b394">threadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccOther.html#34dac6acd09554be6a38a4ba23e64e14">version</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
(CUPTI_ACTIVITY_KIND_OPENACC_OTHER). <hr><h2>Field Documentation</h2>
<a class="anchor" name="703828877b868e7068d7b902b4b5a5a7"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::async" ref="703828877b868e7068d7b902b4b5a5a7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#703828877b868e7068d7b902b4b5a5a7">CUpti_ActivityOpenAccOther::async</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Value of <a class="el" href="structCUpti__ActivityOpenAccOther.html#703828877b868e7068d7b902b4b5a5a7">async()</a> clause of the corresponding directive 
</div>
</div><p>
<a class="anchor" name="3e6779087d5cf058d4d7b771de3c8550"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::asyncMap" ref="3e6779087d5cf058d4d7b771de3c8550" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#3e6779087d5cf058d4d7b771de3c8550">CUpti_ActivityOpenAccOther::asyncMap</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Internal asynchronous queue number used 
</div>
</div><p>
<a class="anchor" name="a3bbac0d04d417352041f1d0e8110ab6"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::cuContextId" ref="a3bbac0d04d417352041f1d0e8110ab6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#a3bbac0d04d417352041f1d0e8110ab6">CUpti_ActivityOpenAccOther::cuContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA context id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="ffe555c3d60dcb8770e3052b74b30f0b"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::cuDeviceId" ref="ffe555c3d60dcb8770e3052b74b30f0b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#ffe555c3d60dcb8770e3052b74b30f0b">CUpti_ActivityOpenAccOther::cuDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA device id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="294ffb92ef4cf0e943867ac55fd73a82"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::cuProcessId" ref="294ffb92ef4cf0e943867ac55fd73a82" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#294ffb92ef4cf0e943867ac55fd73a82">CUpti_ActivityOpenAccOther::cuProcessId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="ad7fcaeec49a422e87bef2071cda098e"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::cuStreamId" ref="ad7fcaeec49a422e87bef2071cda098e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#ad7fcaeec49a422e87bef2071cda098e">CUpti_ActivityOpenAccOther::cuStreamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA stream id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="b6786ac497f391b3090d164376a53151"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::cuThreadId" ref="b6786ac497f391b3090d164376a53151" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#b6786ac497f391b3090d164376a53151">CUpti_ActivityOpenAccOther::cuThreadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the thread where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="78ee47377101651ccbe5be7d826a0126"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::deviceNumber" ref="78ee47377101651ccbe5be7d826a0126" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#78ee47377101651ccbe5be7d826a0126">CUpti_ActivityOpenAccOther::deviceNumber</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device number 
</div>
</div><p>
<a class="anchor" name="8cf7fb10252ceca1a2c089920f735507"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::deviceType" ref="8cf7fb10252ceca1a2c089920f735507" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#8cf7fb10252ceca1a2c089920f735507">CUpti_ActivityOpenAccOther::deviceType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device type 
</div>
</div><p>
<a class="anchor" name="e6fce4b07317091d45c4f88b781827d6"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::end" ref="e6fce4b07317091d45c4f88b781827d6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#e6fce4b07317091d45c4f88b781827d6">CUpti_ActivityOpenAccOther::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI end timestamp 
</div>
</div><p>
<a class="anchor" name="99cd7548f7706d58caf4835693ef14a6"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::endLineNo" ref="99cd7548f7706d58caf4835693ef14a6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#99cd7548f7706d58caf4835693ef14a6">CUpti_ActivityOpenAccOther::endLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For an OpenACC construct, this contains the line number of the end of the construct. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="a65025a8e30dd956d9c194153500dae5"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::eventKind" ref="a65025a8e30dd956d9c194153500dae5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a> <a class="el" href="structCUpti__ActivityOpenAccOther.html#a65025a8e30dd956d9c194153500dae5">CUpti_ActivityOpenAccOther::eventKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenACC event kind (<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717" title="The OpenAcc event kind for OpenAcc activity records.">CUpti_OpenAccEventKind</a>) </dd></dl>

</div>
</div><p>
<a class="anchor" name="e4bddc0b6ccca9b232fcbc5bed99906a"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::externalId" ref="e4bddc0b6ccca9b232fcbc5bed99906a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#e4bddc0b6ccca9b232fcbc5bed99906a">CUpti_ActivityOpenAccOther::externalId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The OpenACC correlation ID. Valid only if deviceType is acc_device_nvidia. If not 0, it uniquely identifies this record. It is identical to the externalId in the preceeding external correlation record of type CUPTI_EXTERNAL_CORRELATION_KIND_OPENACC. 
</div>
</div><p>
<a class="anchor" name="49e27bd7d04829bebab835603cf18d60"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::funcEndLineNo" ref="49e27bd7d04829bebab835603cf18d60" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#49e27bd7d04829bebab835603cf18d60">CUpti_ActivityOpenAccOther::funcEndLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The last line number of the function named in func_name. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="2dae9dea02831e90bd3d29e8b90f37ad"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::funcLineNo" ref="2dae9dea02831e90bd3d29e8b90f37ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#2dae9dea02831e90bd3d29e8b90f37ad">CUpti_ActivityOpenAccOther::funcLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The line number of the first line of the function named in func_name. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="2707237f98dd557b8c97a4cd15a5b8c8"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::funcName" ref="2707237f98dd557b8c97a4cd15a5b8c8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityOpenAccOther.html#2707237f98dd557b8c97a4cd15a5b8c8">CUpti_ActivityOpenAccOther::funcName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A pointer to a null-terminated string containing the name of the function in which the event occurred. 
</div>
</div><p>
<a class="anchor" name="2cd615f39bac31a2f6bcc2680343ea4f"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::implicit" ref="2cd615f39bac31a2f6bcc2680343ea4f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#2cd615f39bac31a2f6bcc2680343ea4f">CUpti_ActivityOpenAccOther::implicit</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
1 for any implicit event, such as an implicit wait at a synchronous data construct 0 otherwise 
</div>
</div><p>
<a class="anchor" name="a81755d8f5c22808a23c595677c2f1d3"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::kind" ref="a81755d8f5c22808a23c595677c2f1d3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityOpenAccOther.html#a81755d8f5c22808a23c595677c2f1d3">CUpti_ActivityOpenAccOther::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_OPENACC_OTHER. 
</div>
</div><p>
<a class="anchor" name="958003e64ec16ea0b54d354b5cdeb7fa"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::lineNo" ref="958003e64ec16ea0b54d354b5cdeb7fa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#958003e64ec16ea0b54d354b5cdeb7fa">CUpti_ActivityOpenAccOther::lineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The line number of the directive or program construct or the starting line number of the OpenACC construct corresponding to the event. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="3d6a65793fd64b90bf620bfb8d66ebf5"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::parentConstruct" ref="3d6a65793fd64b90bf620bfb8d66ebf5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e">CUpti_OpenAccConstructKind</a> <a class="el" href="structCUpti__ActivityOpenAccOther.html#3d6a65793fd64b90bf620bfb8d66ebf5">CUpti_ActivityOpenAccOther::parentConstruct</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenACC parent construct kind (<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e" title="The OpenAcc parent construct kind for OpenAcc activity records.">CUpti_OpenAccConstructKind</a>)</dd></dl>
Note that for applications using PGI OpenACC runtime &lt; 16.1, this will always be CUPTI_OPENACC_CONSTRUCT_KIND_UNKNOWN. 
</div>
</div><p>
<a class="anchor" name="010c5873a04729c4c0b605f616d401e5"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::srcFile" ref="010c5873a04729c4c0b605f616d401e5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityOpenAccOther.html#010c5873a04729c4c0b605f616d401e5">CUpti_ActivityOpenAccOther::srcFile</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A pointer to null-terminated string containing the name of or path to the source file, if known, or a null pointer if not. 
</div>
</div><p>
<a class="anchor" name="216adf7e2087154e20c490da402c5460"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::start" ref="216adf7e2087154e20c490da402c5460" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#216adf7e2087154e20c490da402c5460">CUpti_ActivityOpenAccOther::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI start timestamp 
</div>
</div><p>
<a class="anchor" name="ea584646498d30acfb9a3722d422b394"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::threadId" ref="ea584646498d30acfb9a3722d422b394" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#ea584646498d30acfb9a3722d422b394">CUpti_ActivityOpenAccOther::threadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ThreadId 
</div>
</div><p>
<a class="anchor" name="34dac6acd09554be6a38a4ba23e64e14"></a><!-- doxytag: member="CUpti_ActivityOpenAccOther::version" ref="34dac6acd09554be6a38a4ba23e64e14" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccOther.html#34dac6acd09554be6a38a4ba23e64e14">CUpti_ActivityOpenAccOther::version</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Version number 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
