<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>CUPTI</b>::<b>PcSamplingUtil</b>::<a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html">CUptiUtil_GetHeaderDataParams</a>
  </div>
</div>
<div class="contents">
<h1>CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html">CUPTI PC Sampling Utility API</a>]</small>
</h1><!-- doxytag: class="CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams" -->Params for CuptiUtilGetHeaderData.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">std::ifstream *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#ee70dc881868b513d73c1bc6daa376a5">fileHandler</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structHeader.html">Header</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#b06dbd8503bc420dccacd03c3a63aa53">headerInfo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#faff3ea6e934fc528105635d8626b891">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="ee70dc881868b513d73c1bc6daa376a5"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams::fileHandler" ref="ee70dc881868b513d73c1bc6daa376a5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::ifstream* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#ee70dc881868b513d73c1bc6daa376a5">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams::fileHandler</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
File handle. 
</div>
</div><p>
<a class="anchor" name="b06dbd8503bc420dccacd03c3a63aa53"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams::headerInfo" ref="b06dbd8503bc420dccacd03c3a63aa53" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structHeader.html">Header</a> <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#b06dbd8503bc420dccacd03c3a63aa53">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams::headerInfo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<a class="el" href="structHeader.html" title="Header info will be stored in file.">Header</a> Info. 
</div>
</div><p>
<a class="anchor" name="faff3ea6e934fc528105635d8626b891"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams::size" ref="faff3ea6e934fc528105635d8626b891" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html#faff3ea6e934fc528105635d8626b891">CUPTI::PcSamplingUtil::CUptiUtil_GetHeaderDataParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of the data structure i.e. CUpti_PCSamplingDisableParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:52 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
