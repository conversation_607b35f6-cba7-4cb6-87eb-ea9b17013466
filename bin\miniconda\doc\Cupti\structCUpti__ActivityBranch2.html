<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityBranch2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityBranch2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityBranch2" -->The activity record for source level result branch.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#6e9a8f7001f8c0a8ff83ee1611b450dc">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#2cd8e6ceaa2a6afdc6b3e8bb78781c17">diverged</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#44f12bb52eb2c19ae96a276d2a1af8a1">executed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#3ec1e59a986523300bc8577140a9e9c5">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#eadf6b1b6b1c15e04cf821b348795bbe">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#e55573635b20e8dd1b601b8be09fd045">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#57817347cd8d51dad49ce547077976b1">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#a41ae841700f3fcdaca095d96019291d">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityBranch2.html#89ca5b9bfde56fb3437062bec4e241d1">threadsExecuted</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record the locations of the branches in the source (CUPTI_ACTIVITY_KIND_BRANCH). <hr><h2>Field Documentation</h2>
<a class="anchor" name="6e9a8f7001f8c0a8ff83ee1611b450dc"></a><!-- doxytag: member="CUpti_ActivityBranch2::correlationId" ref="6e9a8f7001f8c0a8ff83ee1611b450dc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch2.html#6e9a8f7001f8c0a8ff83ee1611b450dc">CUpti_ActivityBranch2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="2cd8e6ceaa2a6afdc6b3e8bb78781c17"></a><!-- doxytag: member="CUpti_ActivityBranch2::diverged" ref="2cd8e6ceaa2a6afdc6b3e8bb78781c17" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch2.html#2cd8e6ceaa2a6afdc6b3e8bb78781c17">CUpti_ActivityBranch2::diverged</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of times this branch diverged 
</div>
</div><p>
<a class="anchor" name="44f12bb52eb2c19ae96a276d2a1af8a1"></a><!-- doxytag: member="CUpti_ActivityBranch2::executed" ref="44f12bb52eb2c19ae96a276d2a1af8a1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch2.html#44f12bb52eb2c19ae96a276d2a1af8a1">CUpti_ActivityBranch2::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of times this instruction was executed per warp. It will be incremented regardless of predicate or condition code. 
</div>
</div><p>
<a class="anchor" name="3ec1e59a986523300bc8577140a9e9c5"></a><!-- doxytag: member="CUpti_ActivityBranch2::functionId" ref="3ec1e59a986523300bc8577140a9e9c5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch2.html#3ec1e59a986523300bc8577140a9e9c5">CUpti_ActivityBranch2::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="eadf6b1b6b1c15e04cf821b348795bbe"></a><!-- doxytag: member="CUpti_ActivityBranch2::kind" ref="eadf6b1b6b1c15e04cf821b348795bbe" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityBranch2.html#eadf6b1b6b1c15e04cf821b348795bbe">CUpti_ActivityBranch2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_BRANCH. 
</div>
</div><p>
<a class="anchor" name="e55573635b20e8dd1b601b8be09fd045"></a><!-- doxytag: member="CUpti_ActivityBranch2::pad" ref="e55573635b20e8dd1b601b8be09fd045" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch2.html#e55573635b20e8dd1b601b8be09fd045">CUpti_ActivityBranch2::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="57817347cd8d51dad49ce547077976b1"></a><!-- doxytag: member="CUpti_ActivityBranch2::pcOffset" ref="57817347cd8d51dad49ce547077976b1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch2.html#57817347cd8d51dad49ce547077976b1">CUpti_ActivityBranch2::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the branch. 
</div>
</div><p>
<a class="anchor" name="a41ae841700f3fcdaca095d96019291d"></a><!-- doxytag: member="CUpti_ActivityBranch2::sourceLocatorId" ref="a41ae841700f3fcdaca095d96019291d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityBranch2.html#a41ae841700f3fcdaca095d96019291d">CUpti_ActivityBranch2::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="89ca5b9bfde56fb3437062bec4e241d1"></a><!-- doxytag: member="CUpti_ActivityBranch2::threadsExecuted" ref="89ca5b9bfde56fb3437062bec4e241d1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityBranch2.html#89ca5b9bfde56fb3437062bec4e241d1">CUpti_ActivityBranch2::threadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
