<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_MetricValue Union Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_MetricValue Union Reference<br>
<small>
[<a class="el" href="group__CUPTI__METRIC__API.html">CUPTI Metric API</a>]</small>
</h1><!-- doxytag: class="CUpti_MetricValue" -->A metric value.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Metric values can be one of several different kinds. Corresponding to each kind is a member of the <a class="el" href="unionCUpti__MetricValue.html" title="A metric value.">CUpti_MetricValue</a> union. The metric value returned by <a class="el" href="group__CUPTI__METRIC__API.html#gf42dcb1d349f91265e7809bbba2fc01e">cuptiMetricGetValue</a> should be accessed using the appropriate member of that union based on its value kind. </div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
