<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemset Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemset Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemset" -->The activity record for memset. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#c064a23adf1ed1f33846c206320808fd">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#75c712107f6583925de1709e2de757dd">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#af27c20e42193ae3fa29aca352c49d10">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#4d0b3a89837a18c8ad9e16e3d913fdf5">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#3f2cbd0bcdc8f359644d6dc48d48e107">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#367e3d07412add37fa2a20e74a05e8e7">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#a8eb38a62e28e785f6bb1f9e58f95f34">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#0d34e34bd810931bd970a4c13a876f48">memoryKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#4fdea6a2a1881b6c0ba640455546761a">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#e08e1528d00826b903726a7e69c5642e">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#0ebad6c077e37fac5b96709cfa5631d6">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset.html#711c6ac2fcbad69775408ba63689167d">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory set operation (CUPTI_ACTIVITY_KIND_MEMSET). <hr><h2>Field Documentation</h2>
<a class="anchor" name="c064a23adf1ed1f33846c206320808fd"></a><!-- doxytag: member="CUpti_ActivityMemset::bytes" ref="c064a23adf1ed1f33846c206320808fd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset.html#c064a23adf1ed1f33846c206320808fd">CUpti_ActivityMemset::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes being set by the memory set. 
</div>
</div><p>
<a class="anchor" name="75c712107f6583925de1709e2de757dd"></a><!-- doxytag: member="CUpti_ActivityMemset::contextId" ref="75c712107f6583925de1709e2de757dd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset.html#75c712107f6583925de1709e2de757dd">CUpti_ActivityMemset::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="af27c20e42193ae3fa29aca352c49d10"></a><!-- doxytag: member="CUpti_ActivityMemset::correlationId" ref="af27c20e42193ae3fa29aca352c49d10" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset.html#af27c20e42193ae3fa29aca352c49d10">CUpti_ActivityMemset::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory set. Each memory set is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory set. 
</div>
</div><p>
<a class="anchor" name="4d0b3a89837a18c8ad9e16e3d913fdf5"></a><!-- doxytag: member="CUpti_ActivityMemset::deviceId" ref="4d0b3a89837a18c8ad9e16e3d913fdf5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset.html#4d0b3a89837a18c8ad9e16e3d913fdf5">CUpti_ActivityMemset::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="3f2cbd0bcdc8f359644d6dc48d48e107"></a><!-- doxytag: member="CUpti_ActivityMemset::end" ref="3f2cbd0bcdc8f359644d6dc48d48e107" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset.html#3f2cbd0bcdc8f359644d6dc48d48e107">CUpti_ActivityMemset::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="367e3d07412add37fa2a20e74a05e8e7"></a><!-- doxytag: member="CUpti_ActivityMemset::flags" ref="367e3d07412add37fa2a20e74a05e8e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset.html#367e3d07412add37fa2a20e74a05e8e7">CUpti_ActivityMemset::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memset. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="a8eb38a62e28e785f6bb1f9e58f95f34"></a><!-- doxytag: member="CUpti_ActivityMemset::kind" ref="a8eb38a62e28e785f6bb1f9e58f95f34" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemset.html#a8eb38a62e28e785f6bb1f9e58f95f34">CUpti_ActivityMemset::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMSET. 
</div>
</div><p>
<a class="anchor" name="0d34e34bd810931bd970a4c13a876f48"></a><!-- doxytag: member="CUpti_ActivityMemset::memoryKind" ref="0d34e34bd810931bd970a4c13a876f48" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset.html#0d34e34bd810931bd970a4c13a876f48">CUpti_ActivityMemset::memoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory kind of the memory set <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="4fdea6a2a1881b6c0ba640455546761a"></a><!-- doxytag: member="CUpti_ActivityMemset::reserved0" ref="4fdea6a2a1881b6c0ba640455546761a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemset.html#4fdea6a2a1881b6c0ba640455546761a">CUpti_ActivityMemset::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="e08e1528d00826b903726a7e69c5642e"></a><!-- doxytag: member="CUpti_ActivityMemset::start" ref="e08e1528d00826b903726a7e69c5642e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset.html#e08e1528d00826b903726a7e69c5642e">CUpti_ActivityMemset::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="0ebad6c077e37fac5b96709cfa5631d6"></a><!-- doxytag: member="CUpti_ActivityMemset::streamId" ref="0ebad6c077e37fac5b96709cfa5631d6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset.html#0ebad6c077e37fac5b96709cfa5631d6">CUpti_ActivityMemset::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="711c6ac2fcbad69775408ba63689167d"></a><!-- doxytag: member="CUpti_ActivityMemset::value" ref="711c6ac2fcbad69775408ba63689167d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset.html#711c6ac2fcbad69775408ba63689167d">CUpti_ActivityMemset::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The value being assigned to memory by the memory set. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
