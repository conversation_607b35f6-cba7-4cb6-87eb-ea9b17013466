{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-nvrtc >=12.1.55"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvrtc-dev-12.1.55-0", "features": "", "files": ["bin/nvrtc-builtins64_121.dll", "bin/nvrtc64_120_0.dll", "include/nvrtc.h"], "fn": "cuda-nvrtc-dev-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvrtc-dev-12.1.55-0", "type": 1}, "md5": "861b74f8661946485904264dd39b4495", "name": "cuda-nvrtc-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvrtc-dev-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/nvrtc-builtins64_121.dll", "path_type": "hardlink", "sha256": "1fad824e8635b1bcee6c4641a112145b52e3f9caf4404b4324cdacf055c6bf18", "sha256_in_prefix": "1fad824e8635b1bcee6c4641a112145b52e3f9caf4404b4324cdacf055c6bf18", "size_in_bytes": 7001600}, {"_path": "bin/nvrtc64_120_0.dll", "path_type": "hardlink", "sha256": "b6728e893db1c797af2cf55c9104e25e793a1b709d8809b9991af81035a7a8a2", "sha256_in_prefix": "b6728e893db1c797af2cf55c9104e25e793a1b709d8809b9991af81035a7a8a2", "size_in_bytes": 42159104}, {"_path": "include/nvrtc.h", "path_type": "hardlink", "sha256": "d3f66c7a24688381d9b8887c171dfbe7e2ec9348b0915adcccf925f3cf570912", "sha256_in_prefix": "d3f66c7a24688381d9b8887c171dfbe7e2ec9348b0915adcccf925f3cf570912", "size_in_bytes": 35045}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 17263533, "subdir": "win-64", "timestamp": 1674623109000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-nvrtc-dev-12.1.55-0.tar.bz2", "version": "12.1.55"}