<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingPCData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingPCData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingPCData" -->PC Sampling data.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#c70f1869eddeb333ed864d4836afc645">cubinCrc</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#f15888e0f326e1281d22f9c2ce83a4db">functionIndex</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#cf1a26f89202959daa2566316f6bad6b">functionName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#41663811d199caf6f52cc9b91092651b">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#a40b1a291c8d4dc774cac92657ffdd2b">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#572a91a8cb93e68da4f21ffae9c3efb5">size</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structCUpti__PCSamplingStallReason.html">CUpti_PCSamplingStallReason</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#f7816940e6cec8f434251de19517a32b">stallReason</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingPCData.html#0b823240e19037c27164e37216c5587d">stallReasonCount</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="c70f1869eddeb333ed864d4836afc645"></a><!-- doxytag: member="CUpti_PCSamplingPCData::cubinCrc" ref="c70f1869eddeb333ed864d4836afc645" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__PCSamplingPCData.html#c70f1869eddeb333ed864d4836afc645">CUpti_PCSamplingPCData::cubinCrc</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Unique cubin id 
</div>
</div><p>
<a class="anchor" name="f15888e0f326e1281d22f9c2ce83a4db"></a><!-- doxytag: member="CUpti_PCSamplingPCData::functionIndex" ref="f15888e0f326e1281d22f9c2ce83a4db" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__PCSamplingPCData.html#f15888e0f326e1281d22f9c2ce83a4db">CUpti_PCSamplingPCData::functionIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The function's unique symbol index in the module. 
</div>
</div><p>
<a class="anchor" name="cf1a26f89202959daa2566316f6bad6b"></a><!-- doxytag: member="CUpti_PCSamplingPCData::functionName" ref="cf1a26f89202959daa2566316f6bad6b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* <a class="el" href="structCUpti__PCSamplingPCData.html#cf1a26f89202959daa2566316f6bad6b">CUpti_PCSamplingPCData::functionName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] The function name. This name string might be shared across all the records including records from activity APIs representing the same function, and so it should not be modified or freed until post processing of all the records is done. Once done, it is user’s responsibility to free the memory using free() function. 
</div>
</div><p>
<a class="anchor" name="41663811d199caf6f52cc9b91092651b"></a><!-- doxytag: member="CUpti_PCSamplingPCData::pad" ref="41663811d199caf6f52cc9b91092651b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__PCSamplingPCData.html#41663811d199caf6f52cc9b91092651b">CUpti_PCSamplingPCData::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Padding 
</div>
</div><p>
<a class="anchor" name="a40b1a291c8d4dc774cac92657ffdd2b"></a><!-- doxytag: member="CUpti_PCSamplingPCData::pcOffset" ref="a40b1a291c8d4dc774cac92657ffdd2b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__PCSamplingPCData.html#a40b1a291c8d4dc774cac92657ffdd2b">CUpti_PCSamplingPCData::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] PC offset 
</div>
</div><p>
<a class="anchor" name="572a91a8cb93e68da4f21ffae9c3efb5"></a><!-- doxytag: member="CUpti_PCSamplingPCData::size" ref="572a91a8cb93e68da4f21ffae9c3efb5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingPCData.html#572a91a8cb93e68da4f21ffae9c3efb5">CUpti_PCSamplingPCData::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure. CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
<a class="anchor" name="f7816940e6cec8f434251de19517a32b"></a><!-- doxytag: member="CUpti_PCSamplingPCData::stallReason" ref="f7816940e6cec8f434251de19517a32b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structCUpti__PCSamplingStallReason.html">CUpti_PCSamplingStallReason</a>* <a class="el" href="structCUpti__PCSamplingPCData.html#f7816940e6cec8f434251de19517a32b">CUpti_PCSamplingPCData::stallReason</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Stall reason id Total samples 
</div>
</div><p>
<a class="anchor" name="0b823240e19037c27164e37216c5587d"></a><!-- doxytag: member="CUpti_PCSamplingPCData::stallReasonCount" ref="0b823240e19037c27164e37216c5587d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingPCData.html#0b823240e19037c27164e37216c5587d">CUpti_PCSamplingPCData::stallReasonCount</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Collected stall reason count 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
