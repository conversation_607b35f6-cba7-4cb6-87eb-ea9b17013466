<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityGlobalAccess2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityGlobalAccess2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityGlobalAccess2" -->The activity record for source-level global access. (deprecated in CUDA 9.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#df3d2db4274bf1b565cd356774c0cf5f">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#96a37b99d338e3db9d8e0fdf43ad4f93">executed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#a937cff74ff532080cb0d3c585641836">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#26050e961db8caf048c51e1c34f5b972">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#bf9047b21f04068513fdda1d63784374">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#43f104be05f9edcf5c754f5c47e14db7">l2_transactions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#c410295b3b8cf87493369e321c9fd72a">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#b2275cc0d73ea69d2409fe0d7747c3ec">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#1779046cdacce8a88ba657a66939db88">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#2d675310a86f099a284b26f8a8dbc0a9">theoreticalL2Transactions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityGlobalAccess2.html#0bfbfbb07ef13a1df8b89fda4fef8f2e">threadsExecuted</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records the locations of the global accesses in the source (CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS). Global access activities are now reported using the <a class="el" href="structCUpti__ActivityGlobalAccess3.html" title="The activity record for source-level global access.">CUpti_ActivityGlobalAccess3</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="df3d2db4274bf1b565cd356774c0cf5f"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::correlationId" ref="df3d2db4274bf1b565cd356774c0cf5f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#df3d2db4274bf1b565cd356774c0cf5f">CUpti_ActivityGlobalAccess2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="96a37b99d338e3db9d8e0fdf43ad4f93"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::executed" ref="96a37b99d338e3db9d8e0fdf43ad4f93" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#96a37b99d338e3db9d8e0fdf43ad4f93">CUpti_ActivityGlobalAccess2::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of times this instruction was executed per warp. It will be incremented when at least one of thread among warp is active with predicate and condition code evaluating to true. 
</div>
</div><p>
<a class="anchor" name="a937cff74ff532080cb0d3c585641836"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::flags" ref="a937cff74ff532080cb0d3c585641836" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityGlobalAccess2.html#a937cff74ff532080cb0d3c585641836">CUpti_ActivityGlobalAccess2::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this global access. 
</div>
</div><p>
<a class="anchor" name="26050e961db8caf048c51e1c34f5b972"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::functionId" ref="26050e961db8caf048c51e1c34f5b972" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#26050e961db8caf048c51e1c34f5b972">CUpti_ActivityGlobalAccess2::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="bf9047b21f04068513fdda1d63784374"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::kind" ref="bf9047b21f04068513fdda1d63784374" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityGlobalAccess2.html#bf9047b21f04068513fdda1d63784374">CUpti_ActivityGlobalAccess2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_GLOBAL_ACCESS. 
</div>
</div><p>
<a class="anchor" name="43f104be05f9edcf5c754f5c47e14db7"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::l2_transactions" ref="43f104be05f9edcf5c754f5c47e14db7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#43f104be05f9edcf5c754f5c47e14db7">CUpti_ActivityGlobalAccess2::l2_transactions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total number of 32 bytes transactions to L2 cache generated by this access 
</div>
</div><p>
<a class="anchor" name="c410295b3b8cf87493369e321c9fd72a"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::pad" ref="c410295b3b8cf87493369e321c9fd72a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#c410295b3b8cf87493369e321c9fd72a">CUpti_ActivityGlobalAccess2::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="b2275cc0d73ea69d2409fe0d7747c3ec"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::pcOffset" ref="b2275cc0d73ea69d2409fe0d7747c3ec" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#b2275cc0d73ea69d2409fe0d7747c3ec">CUpti_ActivityGlobalAccess2::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the access. 
</div>
</div><p>
<a class="anchor" name="1779046cdacce8a88ba657a66939db88"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::sourceLocatorId" ref="1779046cdacce8a88ba657a66939db88" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#1779046cdacce8a88ba657a66939db88">CUpti_ActivityGlobalAccess2::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="2d675310a86f099a284b26f8a8dbc0a9"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::theoreticalL2Transactions" ref="2d675310a86f099a284b26f8a8dbc0a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#2d675310a86f099a284b26f8a8dbc0a9">CUpti_ActivityGlobalAccess2::theoreticalL2Transactions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The minimum number of L2 transactions possible based on the access pattern. 
</div>
</div><p>
<a class="anchor" name="0bfbfbb07ef13a1df8b89fda4fef8f2e"></a><!-- doxytag: member="CUpti_ActivityGlobalAccess2::threadsExecuted" ref="0bfbfbb07ef13a1df8b89fda4fef8f2e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityGlobalAccess2.html#0bfbfbb07ef13a1df8b89fda4fef8f2e">CUpti_ActivityGlobalAccess2::threadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction with predicate and condition code evaluating to true. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
