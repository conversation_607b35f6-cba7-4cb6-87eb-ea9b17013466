{"build": "he0c23c2_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["cuda-version >=12.9,<12.10.0a0", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvrtc-12.9.86-he0c23c2_0", "features": "", "files": ["Library/bin/nvrtc-builtins64_129.dll", "Library/bin/nvrtc64_120_0.alt.dll", "Library/bin/nvrtc64_120_0.dll"], "fn": "cuda-nvrtc-12.9.86-he0c23c2_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvrtc-12.9.86-he0c23c2_0", "type": 1}, "md5": "eaf294eebe18e813a14fc04b486addab", "name": "cuda-nvrtc", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvrtc-12.9.86-he0c23c2_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvrtc-builtins64_129.dll", "path_type": "hardlink", "sha256": "c405226b0b394ed8bdc593bb1419b94a989f93248ed2873e8f2e0e27c1688ca1", "sha256_in_prefix": "c405226b0b394ed8bdc593bb1419b94a989f93248ed2873e8f2e0e27c1688ca1", "size_in_bytes": 7217664}, {"_path": "Library/bin/nvrtc64_120_0.alt.dll", "path_type": "hardlink", "sha256": "ac56a4d3b5a99f294523abec28b8f648b96324a9277622a450ec20ddb9e2c47d", "sha256_in_prefix": "ac56a4d3b5a99f294523abec28b8f648b96324a9277622a450ec20ddb9e2c47d", "size_in_bytes": 89899520}, {"_path": "Library/bin/nvrtc64_120_0.dll", "path_type": "hardlink", "sha256": "0cc63349fedffeec1660b30955e8028b6e4e4c26cf1e41c56dc4569fd5eca82c", "sha256_in_prefix": "0cc63349fedffeec1660b30955e8028b6e4e4c26cf1e41c56dc4569fd5eca82c", "size_in_bytes": 89832960}], "paths_version": 1}, "requested_spec": "None", "sha256": "dadb5980acc9622e65ebf20559c6e5fff781c3ae5d69fccb2fc19de89644fc7c", "size": 58555916, "subdir": "win-64", "timestamp": 1749221935000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/cuda-nvrtc-12.9.86-he0c23c2_0.conda", "version": "12.9.86"}