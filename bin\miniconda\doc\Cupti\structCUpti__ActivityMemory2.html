<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemory2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemory2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemory2" -->The activity record for memory.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#091f2f757e1efd329dc1673c721a894e">address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#c84308d7764183c8ed7a73206b50c553">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#fe095f4500de8bb38feb52a26559468c">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#ba753043faca43e903ab8dfcd068759f">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#e24573e7257ea6067575d2632b0470fd">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#982110a1d4a8b07e73b98ee8c4128f27">isAsync</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#bc175c3ea0054abce5518556b39348f7">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#7857c71ffba0eaa9e02fe92c82a357da">memoryKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#db7078d199d29cca3f62c58759fc0689">memoryOperationType</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint64_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityMemory2.html#091f2f757e1efd329dc1673c721a894e">address</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityMemory2.html#5f18419b83569564924b55177dc1fefb">memoryPoolType</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint64_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityMemory2.html#dc0e926abd13ce22546621c6ff957c7e">releaseThreshold</a></td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#dbe413e53c66e0e05526f25248084e19">memoryPoolConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#1ce4277d58693c0bbe8b63f9a8d34e25">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#b78ee1d974b686cd6e0316cd0470117c">PC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#0a0b49b0018469f8e304a58f14423b7d">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#f4ce72b6db3f815e3589ee67aef768e7">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#94fa41c60dd647509fa69ff76b191552">timestamp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#48458c3eddcfaaeea2c3ab45bbbb1092">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory2.html#98bcbf28ab5b8c5715772991be60f2d0">size</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory allocation and free operation (CUPTI_ACTIVITY_KIND_MEMORY2). This activity record provides separate records for memory allocation and memory release operations. This allows to correlate the corresponding driver and runtime API activity record with the memory operation.<p>
Note: This activity record is an upgrade over <a class="el" href="structCUpti__ActivityMemory.html">CUpti_ActivityMemory</a> enabled using the kind <a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3fee93ef030088d7a992a1abd6d6e53b0">CUPTI_ACTIVITY_KIND_MEMORY</a>. <a class="el" href="structCUpti__ActivityMemory.html">CUpti_ActivityMemory</a> provides a single record for the memory allocation and memory release operations. <hr><h2>Field Documentation</h2>
<a class="anchor" name="091f2f757e1efd329dc1673c721a894e"></a><!-- doxytag: member="CUpti_ActivityMemory2::address" ref="091f2f757e1efd329dc1673c721a894e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory2.html#091f2f757e1efd329dc1673c721a894e">CUpti_ActivityMemory2::address</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The virtual address of the allocation.<p>
The base address of the memory pool. 
</div>
</div><p>
<a class="anchor" name="c84308d7764183c8ed7a73206b50c553"></a><!-- doxytag: member="CUpti_ActivityMemory2::bytes" ref="c84308d7764183c8ed7a73206b50c553" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory2.html#c84308d7764183c8ed7a73206b50c553">CUpti_ActivityMemory2::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes of memory allocated. 
</div>
</div><p>
<a class="anchor" name="fe095f4500de8bb38feb52a26559468c"></a><!-- doxytag: member="CUpti_ActivityMemory2::contextId" ref="fe095f4500de8bb38feb52a26559468c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory2.html#fe095f4500de8bb38feb52a26559468c">CUpti_ActivityMemory2::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context. If context is NULL, <code>contextId</code> is set to CUPTI_INVALID_CONTEXT_ID. 
</div>
</div><p>
<a class="anchor" name="ba753043faca43e903ab8dfcd068759f"></a><!-- doxytag: member="CUpti_ActivityMemory2::correlationId" ref="ba753043faca43e903ab8dfcd068759f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory2.html#ba753043faca43e903ab8dfcd068759f">CUpti_ActivityMemory2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory operation. Each memory operation is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory operation. 
</div>
</div><p>
<a class="anchor" name="e24573e7257ea6067575d2632b0470fd"></a><!-- doxytag: member="CUpti_ActivityMemory2::deviceId" ref="e24573e7257ea6067575d2632b0470fd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory2.html#e24573e7257ea6067575d2632b0470fd">CUpti_ActivityMemory2::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory operation is taking place. 
</div>
</div><p>
<a class="anchor" name="982110a1d4a8b07e73b98ee8c4128f27"></a><!-- doxytag: member="CUpti_ActivityMemory2::isAsync" ref="982110a1d4a8b07e73b98ee8c4128f27" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory2.html#982110a1d4a8b07e73b98ee8c4128f27">CUpti_ActivityMemory2::isAsync</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>isAsync</code> is set if memory operation happens through async memory APIs. 
</div>
</div><p>
<a class="anchor" name="bc175c3ea0054abce5518556b39348f7"></a><!-- doxytag: member="CUpti_ActivityMemory2::kind" ref="bc175c3ea0054abce5518556b39348f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemory2.html#bc175c3ea0054abce5518556b39348f7">CUpti_ActivityMemory2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMORY2 
</div>
</div><p>
<a class="anchor" name="7857c71ffba0eaa9e02fe92c82a357da"></a><!-- doxytag: member="CUpti_ActivityMemory2::memoryKind" ref="7857c71ffba0eaa9e02fe92c82a357da" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a> <a class="el" href="structCUpti__ActivityMemory2.html#7857c71ffba0eaa9e02fe92c82a357da">CUpti_ActivityMemory2::memoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory kind requested by the user, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a>. 
</div>
</div><p>
<a class="anchor" name="db7078d199d29cca3f62c58759fc0689"></a><!-- doxytag: member="CUpti_ActivityMemory2::memoryOperationType" ref="db7078d199d29cca3f62c58759fc0689" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a> <a class="el" href="structCUpti__ActivityMemory2.html#db7078d199d29cca3f62c58759fc0689">CUpti_ActivityMemory2::memoryOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory operation requested by the user, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a>. 
</div>
</div><p>
<a class="anchor" name="dbe413e53c66e0e05526f25248084e19"></a><!-- doxytag: member="CUpti_ActivityMemory2::memoryPoolConfig" ref="dbe413e53c66e0e05526f25248084e19" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__ActivityMemory2.html#dbe413e53c66e0e05526f25248084e19">CUpti_ActivityMemory2::memoryPoolConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory pool configuration used for the memory operations. 
</div>
</div><p>
<a class="anchor" name="5f18419b83569564924b55177dc1fefb"></a><!-- doxytag: member="CUpti_ActivityMemory2::memoryPoolType" ref="5f18419b83569564924b55177dc1fefb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> <a class="el" href="structCUpti__ActivityMemory2.html#5f18419b83569564924b55177dc1fefb">CUpti_ActivityMemory2::memoryPoolType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the memory pool, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> 
</div>
</div><p>
<a class="anchor" name="1ce4277d58693c0bbe8b63f9a8d34e25"></a><!-- doxytag: member="CUpti_ActivityMemory2::name" ref="1ce4277d58693c0bbe8b63f9a8d34e25" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityMemory2.html#1ce4277d58693c0bbe8b63f9a8d34e25">CUpti_ActivityMemory2::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Variable name. This name is shared across all activity records representing the same symbol, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="b78ee1d974b686cd6e0316cd0470117c"></a><!-- doxytag: member="CUpti_ActivityMemory2::PC" ref="b78ee1d974b686cd6e0316cd0470117c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory2.html#b78ee1d974b686cd6e0316cd0470117c">CUpti_ActivityMemory2::PC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The program counter of the memory operation. 
</div>
</div><p>
<a class="anchor" name="48458c3eddcfaaeea2c3ab45bbbb1092"></a><!-- doxytag: member="CUpti_ActivityMemory2::processId" ref="48458c3eddcfaaeea2c3ab45bbbb1092" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory2.html#0a0b49b0018469f8e304a58f14423b7d">CUpti_ActivityMemory2::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The processId of the memory pool. <code>processId</code> is valid if <code>memoryPoolType</code> is CUPTI_ACTIVITY_MEMORY_POOL_TYPE_IMPORTED, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="0a0b49b0018469f8e304a58f14423b7d"></a><!-- doxytag: member="CUpti_ActivityMemory2::processId" ref="0a0b49b0018469f8e304a58f14423b7d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory2.html#0a0b49b0018469f8e304a58f14423b7d">CUpti_ActivityMemory2::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process to which this record belongs to. 
</div>
</div><p>
<a class="anchor" name="dc0e926abd13ce22546621c6ff957c7e"></a><!-- doxytag: member="CUpti_ActivityMemory2::releaseThreshold" ref="dc0e926abd13ce22546621c6ff957c7e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory2.html#dc0e926abd13ce22546621c6ff957c7e">CUpti_ActivityMemory2::releaseThreshold</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The release threshold of the memory pool in bytes. <code>releaseThreshold</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="98bcbf28ab5b8c5715772991be60f2d0"></a><!-- doxytag: member="CUpti_ActivityMemory2::size" ref="98bcbf28ab5b8c5715772991be60f2d0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory2.html#98bcbf28ab5b8c5715772991be60f2d0">CUpti_ActivityMemory2::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the memory pool in bytes. <code>size</code> is valid if <code>memoryPoolType</code> is CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="f4ce72b6db3f815e3589ee67aef768e7"></a><!-- doxytag: member="CUpti_ActivityMemory2::streamId" ref="f4ce72b6db3f815e3589ee67aef768e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory2.html#f4ce72b6db3f815e3589ee67aef768e7">CUpti_ActivityMemory2::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream. If memory operation is not async, <code>streamId</code> is set to CUPTI_INVALID_STREAM_ID. 
</div>
</div><p>
<a class="anchor" name="94fa41c60dd647509fa69ff76b191552"></a><!-- doxytag: member="CUpti_ActivityMemory2::timestamp" ref="94fa41c60dd647509fa69ff76b191552" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory2.html#94fa41c60dd647509fa69ff76b191552">CUpti_ActivityMemory2::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory operation, in ns. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
