<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityOpenAccLaunch Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityOpenAccLaunch Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityOpenAccLaunch" -->The activity record for OpenACC launch.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#9a88f776fbccfc4f9660636a9d8cc46e">async</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#5d9139164b4acee503d8f8d9837251f1">asyncMap</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#b0a74b8822815cbd13e98264dfb505f6">cuContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#47d2055d7659060858a3639e66552d88">cuDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#83c7c475f684690388443bc3dd488271">cuProcessId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#8491d24973366a3d333360ba0651b2f9">cuStreamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#bff7482d2fa33f1b81f90e93e6c96ca8">cuThreadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#af14d5669a4d8f9aa8b005be1a3b6d5a">deviceNumber</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#130d308f37f4bdf2674620614547f468">deviceType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#17ff240af01360230ce7ac60a5edec17">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#6b91c979cf60028b61486a1e81ca83e4">endLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#3122d644c244764c169d0558b034af55">eventKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#ff35f0dd72a9d6231f10d31085e6cd21">externalId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#005016da79873557a652c47c19eb0fba">funcEndLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#c5d4a85a1757ed1598cf5a0a72740289">funcLineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#3ca77809b50823e4aab22f7d29e68093">funcName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#b80223d24069659ec57dc537db2608b2">implicit</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#16038a668c324ab17862f41c54fde537">kernelName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#873e8833466438d77851576da14ada65">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#94009306a3216640ccdaee51d3c63278">lineNo</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#9111d35a7b7a041d57d6261ecb97db86">numGangs</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#245c5b442944988d46429160953a699f">numWorkers</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#1eeb9352d984ac2415ce27f8b05b46cf">pad1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e">CUpti_OpenAccConstructKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#68a68ffc7cfe0760a9c2f68a8982ee8f">parentConstruct</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#ffe221b15fd1827b1d8a9b9c2c09b9dd">srcFile</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#1214d33f2848f006de98e7e9dff15795">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#d1690212b96257e354aadf6da889f666">threadId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#5819c194e93027f30528cd665d7eccc0">vectorLength</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityOpenAccLaunch.html#7fe4cf59bba0a55c1247bb4f3d2cd653">version</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
(CUPTI_ACTIVITY_KIND_OPENACC_LAUNCH). <hr><h2>Field Documentation</h2>
<a class="anchor" name="9a88f776fbccfc4f9660636a9d8cc46e"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::async" ref="9a88f776fbccfc4f9660636a9d8cc46e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#9a88f776fbccfc4f9660636a9d8cc46e">CUpti_ActivityOpenAccLaunch::async</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Value of <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#9a88f776fbccfc4f9660636a9d8cc46e">async()</a> clause of the corresponding directive 
</div>
</div><p>
<a class="anchor" name="5d9139164b4acee503d8f8d9837251f1"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::asyncMap" ref="5d9139164b4acee503d8f8d9837251f1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#5d9139164b4acee503d8f8d9837251f1">CUpti_ActivityOpenAccLaunch::asyncMap</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Internal asynchronous queue number used 
</div>
</div><p>
<a class="anchor" name="b0a74b8822815cbd13e98264dfb505f6"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::cuContextId" ref="b0a74b8822815cbd13e98264dfb505f6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#b0a74b8822815cbd13e98264dfb505f6">CUpti_ActivityOpenAccLaunch::cuContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA context id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="47d2055d7659060858a3639e66552d88"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::cuDeviceId" ref="47d2055d7659060858a3639e66552d88" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#47d2055d7659060858a3639e66552d88">CUpti_ActivityOpenAccLaunch::cuDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA device id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="83c7c475f684690388443bc3dd488271"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::cuProcessId" ref="83c7c475f684690388443bc3dd488271" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#83c7c475f684690388443bc3dd488271">CUpti_ActivityOpenAccLaunch::cuProcessId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="8491d24973366a3d333360ba0651b2f9"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::cuStreamId" ref="8491d24973366a3d333360ba0651b2f9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#8491d24973366a3d333360ba0651b2f9">CUpti_ActivityOpenAccLaunch::cuStreamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUDA stream id Valid only if deviceType is acc_device_nvidia. 
</div>
</div><p>
<a class="anchor" name="bff7482d2fa33f1b81f90e93e6c96ca8"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::cuThreadId" ref="bff7482d2fa33f1b81f90e93e6c96ca8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#bff7482d2fa33f1b81f90e93e6c96ca8">CUpti_ActivityOpenAccLaunch::cuThreadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the thread where the OpenACC activity is executing. 
</div>
</div><p>
<a class="anchor" name="af14d5669a4d8f9aa8b005be1a3b6d5a"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::deviceNumber" ref="af14d5669a4d8f9aa8b005be1a3b6d5a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#af14d5669a4d8f9aa8b005be1a3b6d5a">CUpti_ActivityOpenAccLaunch::deviceNumber</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device number 
</div>
</div><p>
<a class="anchor" name="130d308f37f4bdf2674620614547f468"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::deviceType" ref="130d308f37f4bdf2674620614547f468" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#130d308f37f4bdf2674620614547f468">CUpti_ActivityOpenAccLaunch::deviceType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device type 
</div>
</div><p>
<a class="anchor" name="17ff240af01360230ce7ac60a5edec17"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::end" ref="17ff240af01360230ce7ac60a5edec17" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#17ff240af01360230ce7ac60a5edec17">CUpti_ActivityOpenAccLaunch::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI end timestamp 
</div>
</div><p>
<a class="anchor" name="6b91c979cf60028b61486a1e81ca83e4"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::endLineNo" ref="6b91c979cf60028b61486a1e81ca83e4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#6b91c979cf60028b61486a1e81ca83e4">CUpti_ActivityOpenAccLaunch::endLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For an OpenACC construct, this contains the line number of the end of the construct. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="3122d644c244764c169d0558b034af55"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::eventKind" ref="3122d644c244764c169d0558b034af55" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717">CUpti_OpenAccEventKind</a> <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#3122d644c244764c169d0558b034af55">CUpti_ActivityOpenAccLaunch::eventKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenACC event kind (<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g0e638b0b6a210164345ab159bcba6717" title="The OpenAcc event kind for OpenAcc activity records.">CUpti_OpenAccEventKind</a>) </dd></dl>

</div>
</div><p>
<a class="anchor" name="ff35f0dd72a9d6231f10d31085e6cd21"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::externalId" ref="ff35f0dd72a9d6231f10d31085e6cd21" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#ff35f0dd72a9d6231f10d31085e6cd21">CUpti_ActivityOpenAccLaunch::externalId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The OpenACC correlation ID. Valid only if deviceType is acc_device_nvidia. If not 0, it uniquely identifies this record. It is identical to the externalId in the preceeding external correlation record of type CUPTI_EXTERNAL_CORRELATION_KIND_OPENACC. 
</div>
</div><p>
<a class="anchor" name="005016da79873557a652c47c19eb0fba"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::funcEndLineNo" ref="005016da79873557a652c47c19eb0fba" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#005016da79873557a652c47c19eb0fba">CUpti_ActivityOpenAccLaunch::funcEndLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The last line number of the function named in func_name. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="c5d4a85a1757ed1598cf5a0a72740289"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::funcLineNo" ref="c5d4a85a1757ed1598cf5a0a72740289" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#c5d4a85a1757ed1598cf5a0a72740289">CUpti_ActivityOpenAccLaunch::funcLineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The line number of the first line of the function named in func_name. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="3ca77809b50823e4aab22f7d29e68093"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::funcName" ref="3ca77809b50823e4aab22f7d29e68093" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#3ca77809b50823e4aab22f7d29e68093">CUpti_ActivityOpenAccLaunch::funcName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A pointer to a null-terminated string containing the name of the function in which the event occurred. 
</div>
</div><p>
<a class="anchor" name="b80223d24069659ec57dc537db2608b2"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::implicit" ref="b80223d24069659ec57dc537db2608b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#b80223d24069659ec57dc537db2608b2">CUpti_ActivityOpenAccLaunch::implicit</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
1 for any implicit event, such as an implicit wait at a synchronous data construct 0 otherwise 
</div>
</div><p>
<a class="anchor" name="16038a668c324ab17862f41c54fde537"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::kernelName" ref="16038a668c324ab17862f41c54fde537" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#16038a668c324ab17862f41c54fde537">CUpti_ActivityOpenAccLaunch::kernelName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A pointer to null-terminated string containing the name of the kernel being launched, if known, or a null pointer if not. 
</div>
</div><p>
<a class="anchor" name="873e8833466438d77851576da14ada65"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::kind" ref="873e8833466438d77851576da14ada65" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#873e8833466438d77851576da14ada65">CUpti_ActivityOpenAccLaunch::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_OPENACC_LAUNCH. 
</div>
</div><p>
<a class="anchor" name="94009306a3216640ccdaee51d3c63278"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::lineNo" ref="94009306a3216640ccdaee51d3c63278" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#94009306a3216640ccdaee51d3c63278">CUpti_ActivityOpenAccLaunch::lineNo</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The line number of the directive or program construct or the starting line number of the OpenACC construct corresponding to the event. A negative or zero value means the line number is not known. 
</div>
</div><p>
<a class="anchor" name="9111d35a7b7a041d57d6261ecb97db86"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::numGangs" ref="9111d35a7b7a041d57d6261ecb97db86" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#9111d35a7b7a041d57d6261ecb97db86">CUpti_ActivityOpenAccLaunch::numGangs</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of gangs created for this kernel launch 
</div>
</div><p>
<a class="anchor" name="245c5b442944988d46429160953a699f"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::numWorkers" ref="245c5b442944988d46429160953a699f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#245c5b442944988d46429160953a699f">CUpti_ActivityOpenAccLaunch::numWorkers</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of workers created for this kernel launch 
</div>
</div><p>
<a class="anchor" name="1eeb9352d984ac2415ce27f8b05b46cf"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::pad1" ref="1eeb9352d984ac2415ce27f8b05b46cf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#1eeb9352d984ac2415ce27f8b05b46cf">CUpti_ActivityOpenAccLaunch::pad1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="68a68ffc7cfe0760a9c2f68a8982ee8f"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::parentConstruct" ref="68a68ffc7cfe0760a9c2f68a8982ee8f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e">CUpti_OpenAccConstructKind</a> <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#68a68ffc7cfe0760a9c2f68a8982ee8f">CUpti_ActivityOpenAccLaunch::parentConstruct</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI OpenACC parent construct kind (<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gad1f871f3bfe3f626520561cc767881e" title="The OpenAcc parent construct kind for OpenAcc activity records.">CUpti_OpenAccConstructKind</a>)</dd></dl>
Note that for applications using PGI OpenACC runtime &lt; 16.1, this will always be CUPTI_OPENACC_CONSTRUCT_KIND_UNKNOWN. 
</div>
</div><p>
<a class="anchor" name="ffe221b15fd1827b1d8a9b9c2c09b9dd"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::srcFile" ref="ffe221b15fd1827b1d8a9b9c2c09b9dd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#ffe221b15fd1827b1d8a9b9c2c09b9dd">CUpti_ActivityOpenAccLaunch::srcFile</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A pointer to null-terminated string containing the name of or path to the source file, if known, or a null pointer if not. 
</div>
</div><p>
<a class="anchor" name="1214d33f2848f006de98e7e9dff15795"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::start" ref="1214d33f2848f006de98e7e9dff15795" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#1214d33f2848f006de98e7e9dff15795">CUpti_ActivityOpenAccLaunch::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUPTI start timestamp 
</div>
</div><p>
<a class="anchor" name="d1690212b96257e354aadf6da889f666"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::threadId" ref="d1690212b96257e354aadf6da889f666" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#d1690212b96257e354aadf6da889f666">CUpti_ActivityOpenAccLaunch::threadId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
ThreadId 
</div>
</div><p>
<a class="anchor" name="5819c194e93027f30528cd665d7eccc0"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::vectorLength" ref="5819c194e93027f30528cd665d7eccc0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#5819c194e93027f30528cd665d7eccc0">CUpti_ActivityOpenAccLaunch::vectorLength</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of vector lanes created for this kernel launch 
</div>
</div><p>
<a class="anchor" name="7fe4cf59bba0a55c1247bb4f3d2cd653"></a><!-- doxytag: member="CUpti_ActivityOpenAccLaunch::version" ref="7fe4cf59bba0a55c1247bb4f3d2cd653" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#7fe4cf59bba0a55c1247bb4f3d2cd653">CUpti_ActivityOpenAccLaunch::version</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Version number 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
