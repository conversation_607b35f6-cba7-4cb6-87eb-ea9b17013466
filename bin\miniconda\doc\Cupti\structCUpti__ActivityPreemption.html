<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityPreemption Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityPreemption Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityPreemption" -->The activity record for a preemption of a CDP kernel.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#853f8d094bb5d0e0cec157183cd1da96">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#076fdb658f4c3d2cc76abc397120d2e5">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#71bbbf76cdc8bb6363fcc2af1cfab561">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#bef3e12a3d3941cb05173ee1b49000d6">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#46fc0712b94f3d12a2e2f4fdc4f999a4">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#10ad616ac9340294182ea6d6f72ed1a7">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g7609527c3c6c122a8bb5b1e5d1ae1c4a">CUpti_ActivityPreemptionKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#cae1214e9afa9408296b61d1057053ed">preemptionKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPreemption.html#73c679e8a3f9b559b648705c95bd8b51">timestamp</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a preemption of a CDP kernel. <hr><h2>Field Documentation</h2>
<a class="anchor" name="853f8d094bb5d0e0cec157183cd1da96"></a><!-- doxytag: member="CUpti_ActivityPreemption::blockX" ref="853f8d094bb5d0e0cec157183cd1da96" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPreemption.html#853f8d094bb5d0e0cec157183cd1da96">CUpti_ActivityPreemption::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension of the block that is preempted 
</div>
</div><p>
<a class="anchor" name="076fdb658f4c3d2cc76abc397120d2e5"></a><!-- doxytag: member="CUpti_ActivityPreemption::blockY" ref="076fdb658f4c3d2cc76abc397120d2e5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPreemption.html#076fdb658f4c3d2cc76abc397120d2e5">CUpti_ActivityPreemption::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension of the block that is preempted 
</div>
</div><p>
<a class="anchor" name="71bbbf76cdc8bb6363fcc2af1cfab561"></a><!-- doxytag: member="CUpti_ActivityPreemption::blockZ" ref="71bbbf76cdc8bb6363fcc2af1cfab561" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPreemption.html#71bbbf76cdc8bb6363fcc2af1cfab561">CUpti_ActivityPreemption::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension of the block that is preempted 
</div>
</div><p>
<a class="anchor" name="bef3e12a3d3941cb05173ee1b49000d6"></a><!-- doxytag: member="CUpti_ActivityPreemption::gridId" ref="bef3e12a3d3941cb05173ee1b49000d6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityPreemption.html#bef3e12a3d3941cb05173ee1b49000d6">CUpti_ActivityPreemption::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid-id of the block that is preempted 
</div>
</div><p>
<a class="anchor" name="46fc0712b94f3d12a2e2f4fdc4f999a4"></a><!-- doxytag: member="CUpti_ActivityPreemption::kind" ref="46fc0712b94f3d12a2e2f4fdc4f999a4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityPreemption.html#46fc0712b94f3d12a2e2f4fdc4f999a4">CUpti_ActivityPreemption::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_PREEMPTION 
</div>
</div><p>
<a class="anchor" name="10ad616ac9340294182ea6d6f72ed1a7"></a><!-- doxytag: member="CUpti_ActivityPreemption::pad" ref="10ad616ac9340294182ea6d6f72ed1a7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPreemption.html#10ad616ac9340294182ea6d6f72ed1a7">CUpti_ActivityPreemption::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="cae1214e9afa9408296b61d1057053ed"></a><!-- doxytag: member="CUpti_ActivityPreemption::preemptionKind" ref="cae1214e9afa9408296b61d1057053ed" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g7609527c3c6c122a8bb5b1e5d1ae1c4a">CUpti_ActivityPreemptionKind</a> <a class="el" href="structCUpti__ActivityPreemption.html#cae1214e9afa9408296b61d1057053ed">CUpti_ActivityPreemption::preemptionKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
kind of the preemption 
</div>
</div><p>
<a class="anchor" name="73c679e8a3f9b559b648705c95bd8b51"></a><!-- doxytag: member="CUpti_ActivityPreemption::timestamp" ref="73c679e8a3f9b559b648705c95bd8b51" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityPreemption.html#73c679e8a3f9b559b648705c95bd8b51">CUpti_ActivityPreemption::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp of the preemption, in ns. A value of 0 indicates that timestamp information could not be collected for the preemption. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
