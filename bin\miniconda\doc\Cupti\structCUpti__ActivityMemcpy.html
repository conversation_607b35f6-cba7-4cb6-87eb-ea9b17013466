<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpy Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpy Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpy" -->The activity record for memory copies. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#6050f9550f0d7db52036cffe81745f47">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#d3697623a1fac6c2cd54805a201a70b1">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#54db77c0e4c71dc7f4b30213ff9f6f3b">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#8715619a0555e7e75e4f96890aad4af4">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#4d34daa9068348322d931dcc3d8cc5da">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#1f21718f123f1dce41f80d16e3f89c94">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#bb7798000bd79dab352b22ef586fbb9b">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#02f04d50706dc986ff487572e28396b2">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#6b196d14f88cd0f956b77127d4919222">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#49cbe015c779a6cbdc2220bce339866a">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#d9366d4d84c6a37aeda7632a91180644">runtimeCorrelationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#ecffc65a3493dd46c9b7d2e7b394e595">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#f2ee23b605610659df0484026a68f670">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy.html#49baa8894d0fe9cfaf5adf86a07c2dd4">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory copy (CUPTI_ACTIVITY_KIND_MEMCPY). <hr><h2>Field Documentation</h2>
<a class="anchor" name="6050f9550f0d7db52036cffe81745f47"></a><!-- doxytag: member="CUpti_ActivityMemcpy::bytes" ref="6050f9550f0d7db52036cffe81745f47" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy.html#6050f9550f0d7db52036cffe81745f47">CUpti_ActivityMemcpy::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="d3697623a1fac6c2cd54805a201a70b1"></a><!-- doxytag: member="CUpti_ActivityMemcpy::contextId" ref="d3697623a1fac6c2cd54805a201a70b1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy.html#d3697623a1fac6c2cd54805a201a70b1">CUpti_ActivityMemcpy::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="54db77c0e4c71dc7f4b30213ff9f6f3b"></a><!-- doxytag: member="CUpti_ActivityMemcpy::copyKind" ref="54db77c0e4c71dc7f4b30213ff9f6f3b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy.html#54db77c0e4c71dc7f4b30213ff9f6f3b">CUpti_ActivityMemcpy::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="8715619a0555e7e75e4f96890aad4af4"></a><!-- doxytag: member="CUpti_ActivityMemcpy::correlationId" ref="8715619a0555e7e75e4f96890aad4af4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy.html#8715619a0555e7e75e4f96890aad4af4">CUpti_ActivityMemcpy::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="4d34daa9068348322d931dcc3d8cc5da"></a><!-- doxytag: member="CUpti_ActivityMemcpy::deviceId" ref="4d34daa9068348322d931dcc3d8cc5da" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy.html#4d34daa9068348322d931dcc3d8cc5da">CUpti_ActivityMemcpy::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="1f21718f123f1dce41f80d16e3f89c94"></a><!-- doxytag: member="CUpti_ActivityMemcpy::dstKind" ref="1f21718f123f1dce41f80d16e3f89c94" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy.html#1f21718f123f1dce41f80d16e3f89c94">CUpti_ActivityMemcpy::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="bb7798000bd79dab352b22ef586fbb9b"></a><!-- doxytag: member="CUpti_ActivityMemcpy::end" ref="bb7798000bd79dab352b22ef586fbb9b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy.html#bb7798000bd79dab352b22ef586fbb9b">CUpti_ActivityMemcpy::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="02f04d50706dc986ff487572e28396b2"></a><!-- doxytag: member="CUpti_ActivityMemcpy::flags" ref="02f04d50706dc986ff487572e28396b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy.html#02f04d50706dc986ff487572e28396b2">CUpti_ActivityMemcpy::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="6b196d14f88cd0f956b77127d4919222"></a><!-- doxytag: member="CUpti_ActivityMemcpy::kind" ref="6b196d14f88cd0f956b77127d4919222" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpy.html#6b196d14f88cd0f956b77127d4919222">CUpti_ActivityMemcpy::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY. 
</div>
</div><p>
<a class="anchor" name="49cbe015c779a6cbdc2220bce339866a"></a><!-- doxytag: member="CUpti_ActivityMemcpy::reserved0" ref="49cbe015c779a6cbdc2220bce339866a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpy.html#49cbe015c779a6cbdc2220bce339866a">CUpti_ActivityMemcpy::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="d9366d4d84c6a37aeda7632a91180644"></a><!-- doxytag: member="CUpti_ActivityMemcpy::runtimeCorrelationId" ref="d9366d4d84c6a37aeda7632a91180644" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy.html#d9366d4d84c6a37aeda7632a91180644">CUpti_ActivityMemcpy::runtimeCorrelationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The runtime correlation ID of the memory copy. Each memory copy is assigned a unique runtime correlation ID that is identical to the correlation ID in the runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="ecffc65a3493dd46c9b7d2e7b394e595"></a><!-- doxytag: member="CUpti_ActivityMemcpy::srcKind" ref="ecffc65a3493dd46c9b7d2e7b394e595" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy.html#ecffc65a3493dd46c9b7d2e7b394e595">CUpti_ActivityMemcpy::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="f2ee23b605610659df0484026a68f670"></a><!-- doxytag: member="CUpti_ActivityMemcpy::start" ref="f2ee23b605610659df0484026a68f670" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy.html#f2ee23b605610659df0484026a68f670">CUpti_ActivityMemcpy::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="49baa8894d0fe9cfaf5adf86a07c2dd4"></a><!-- doxytag: member="CUpti_ActivityMemcpy::streamId" ref="49baa8894d0fe9cfaf5adf86a07c2dd4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy.html#49baa8894d0fe9cfaf5adf86a07c2dd4">CUpti_ActivityMemcpy::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
