{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": ["email-validator >=2.0.0"], "depends": ["annotated-types >=0.6.0", "pydantic-core 2.27.1", "python >=3.10,<3.11.0a0", "typing-extensions >=4.12.2"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\pydantic-2.10.3-py310haa95532_0", "files": ["Lib/site-packages/pydantic-2.10.3.dist-info/INSTALLER", "Lib/site-packages/pydantic-2.10.3.dist-info/METADATA", "Lib/site-packages/pydantic-2.10.3.dist-info/RECORD", "Lib/site-packages/pydantic-2.10.3.dist-info/REQUESTED", "Lib/site-packages/pydantic-2.10.3.dist-info/WHEEL", "Lib/site-packages/pydantic-2.10.3.dist-info/direct_url.json", "Lib/site-packages/pydantic-2.10.3.dist-info/licenses/LICENSE", "Lib/site-packages/pydantic/__init__.py", "Lib/site-packages/pydantic/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/_migration.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/alias_generators.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/aliases.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/annotated_handlers.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/class_validators.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/color.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/config.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/dataclasses.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/datetime_parse.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/decorator.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/env_settings.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/error_wrappers.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/fields.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/functional_serializers.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/functional_validators.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/generics.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/json.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/json_schema.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/main.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/mypy.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/networks.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/parse.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/root_model.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/schema.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/tools.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/type_adapter.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/types.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/typing.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/validate_call_decorator.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/validators.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/version.cpython-310.pyc", "Lib/site-packages/pydantic/__pycache__/warnings.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__init__.py", "Lib/site-packages/pydantic/_internal/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_config.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_core_metadata.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_core_utils.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_dataclasses.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_decorators.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_decorators_v1.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_discriminated_union.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_docs_extraction.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_fields.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_forward_ref.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_generate_schema.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_generics.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_git.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_import_utils.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_internal_dataclass.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_known_annotated_metadata.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_mock_val_ser.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_model_construction.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_namespace_utils.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_repr.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_schema_generation_shared.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_serializers.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_signature.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_std_types_schema.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_typing_extra.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_utils.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_validate_call.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/__pycache__/_validators.cpython-310.pyc", "Lib/site-packages/pydantic/_internal/_config.py", "Lib/site-packages/pydantic/_internal/_core_metadata.py", "Lib/site-packages/pydantic/_internal/_core_utils.py", "Lib/site-packages/pydantic/_internal/_dataclasses.py", "Lib/site-packages/pydantic/_internal/_decorators.py", "Lib/site-packages/pydantic/_internal/_decorators_v1.py", "Lib/site-packages/pydantic/_internal/_discriminated_union.py", "Lib/site-packages/pydantic/_internal/_docs_extraction.py", "Lib/site-packages/pydantic/_internal/_fields.py", "Lib/site-packages/pydantic/_internal/_forward_ref.py", "Lib/site-packages/pydantic/_internal/_generate_schema.py", "Lib/site-packages/pydantic/_internal/_generics.py", "Lib/site-packages/pydantic/_internal/_git.py", "Lib/site-packages/pydantic/_internal/_import_utils.py", "Lib/site-packages/pydantic/_internal/_internal_dataclass.py", "Lib/site-packages/pydantic/_internal/_known_annotated_metadata.py", "Lib/site-packages/pydantic/_internal/_mock_val_ser.py", "Lib/site-packages/pydantic/_internal/_model_construction.py", "Lib/site-packages/pydantic/_internal/_namespace_utils.py", "Lib/site-packages/pydantic/_internal/_repr.py", "Lib/site-packages/pydantic/_internal/_schema_generation_shared.py", "Lib/site-packages/pydantic/_internal/_serializers.py", "Lib/site-packages/pydantic/_internal/_signature.py", "Lib/site-packages/pydantic/_internal/_std_types_schema.py", "Lib/site-packages/pydantic/_internal/_typing_extra.py", "Lib/site-packages/pydantic/_internal/_utils.py", "Lib/site-packages/pydantic/_internal/_validate_call.py", "Lib/site-packages/pydantic/_internal/_validators.py", "Lib/site-packages/pydantic/_migration.py", "Lib/site-packages/pydantic/alias_generators.py", "Lib/site-packages/pydantic/aliases.py", "Lib/site-packages/pydantic/annotated_handlers.py", "Lib/site-packages/pydantic/class_validators.py", "Lib/site-packages/pydantic/color.py", "Lib/site-packages/pydantic/config.py", "Lib/site-packages/pydantic/dataclasses.py", "Lib/site-packages/pydantic/datetime_parse.py", "Lib/site-packages/pydantic/decorator.py", "Lib/site-packages/pydantic/deprecated/__init__.py", "Lib/site-packages/pydantic/deprecated/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/__pycache__/class_validators.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/__pycache__/config.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/__pycache__/copy_internals.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/__pycache__/decorator.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/__pycache__/json.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/__pycache__/parse.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/__pycache__/tools.cpython-310.pyc", "Lib/site-packages/pydantic/deprecated/class_validators.py", "Lib/site-packages/pydantic/deprecated/config.py", "Lib/site-packages/pydantic/deprecated/copy_internals.py", "Lib/site-packages/pydantic/deprecated/decorator.py", "Lib/site-packages/pydantic/deprecated/json.py", "Lib/site-packages/pydantic/deprecated/parse.py", "Lib/site-packages/pydantic/deprecated/tools.py", "Lib/site-packages/pydantic/env_settings.py", "Lib/site-packages/pydantic/error_wrappers.py", "Lib/site-packages/pydantic/errors.py", "Lib/site-packages/pydantic/experimental/__init__.py", "Lib/site-packages/pydantic/experimental/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pydantic/experimental/__pycache__/pipeline.cpython-310.pyc", "Lib/site-packages/pydantic/experimental/pipeline.py", "Lib/site-packages/pydantic/fields.py", "Lib/site-packages/pydantic/functional_serializers.py", "Lib/site-packages/pydantic/functional_validators.py", "Lib/site-packages/pydantic/generics.py", "Lib/site-packages/pydantic/json.py", "Lib/site-packages/pydantic/json_schema.py", "Lib/site-packages/pydantic/main.py", "Lib/site-packages/pydantic/mypy.py", "Lib/site-packages/pydantic/networks.py", "Lib/site-packages/pydantic/parse.py", "Lib/site-packages/pydantic/plugin/__init__.py", "Lib/site-packages/pydantic/plugin/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pydantic/plugin/__pycache__/_loader.cpython-310.pyc", "Lib/site-packages/pydantic/plugin/__pycache__/_schema_validator.cpython-310.pyc", "Lib/site-packages/pydantic/plugin/_loader.py", "Lib/site-packages/pydantic/plugin/_schema_validator.py", "Lib/site-packages/pydantic/py.typed", "Lib/site-packages/pydantic/root_model.py", "Lib/site-packages/pydantic/schema.py", "Lib/site-packages/pydantic/tools.py", "Lib/site-packages/pydantic/type_adapter.py", "Lib/site-packages/pydantic/types.py", "Lib/site-packages/pydantic/typing.py", "Lib/site-packages/pydantic/utils.py", "Lib/site-packages/pydantic/v1/__init__.py", "Lib/site-packages/pydantic/v1/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/_hypothesis_plugin.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/annotated_types.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/class_validators.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/color.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/config.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/dataclasses.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/datetime_parse.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/decorator.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/env_settings.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/error_wrappers.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/fields.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/generics.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/json.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/main.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/mypy.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/networks.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/parse.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/schema.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/tools.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/types.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/typing.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/validators.cpython-310.pyc", "Lib/site-packages/pydantic/v1/__pycache__/version.cpython-310.pyc", "Lib/site-packages/pydantic/v1/_hypothesis_plugin.py", "Lib/site-packages/pydantic/v1/annotated_types.py", "Lib/site-packages/pydantic/v1/class_validators.py", "Lib/site-packages/pydantic/v1/color.py", "Lib/site-packages/pydantic/v1/config.py", "Lib/site-packages/pydantic/v1/dataclasses.py", "Lib/site-packages/pydantic/v1/datetime_parse.py", "Lib/site-packages/pydantic/v1/decorator.py", "Lib/site-packages/pydantic/v1/env_settings.py", "Lib/site-packages/pydantic/v1/error_wrappers.py", "Lib/site-packages/pydantic/v1/errors.py", "Lib/site-packages/pydantic/v1/fields.py", "Lib/site-packages/pydantic/v1/generics.py", "Lib/site-packages/pydantic/v1/json.py", "Lib/site-packages/pydantic/v1/main.py", "Lib/site-packages/pydantic/v1/mypy.py", "Lib/site-packages/pydantic/v1/networks.py", "Lib/site-packages/pydantic/v1/parse.py", "Lib/site-packages/pydantic/v1/py.typed", "Lib/site-packages/pydantic/v1/schema.py", "Lib/site-packages/pydantic/v1/tools.py", "Lib/site-packages/pydantic/v1/types.py", "Lib/site-packages/pydantic/v1/typing.py", "Lib/site-packages/pydantic/v1/utils.py", "Lib/site-packages/pydantic/v1/validators.py", "Lib/site-packages/pydantic/v1/version.py", "Lib/site-packages/pydantic/validate_call_decorator.py", "Lib/site-packages/pydantic/validators.py", "Lib/site-packages/pydantic/version.py", "Lib/site-packages/pydantic/warnings.py"], "fn": "pydantic-2.10.3-py310haa95532_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\pydantic-2.10.3-py310haa95532_0", "type": 1}, "md5": "c44ea686d6d24f5a1611d11fe08705cf", "name": "pydantic", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\pydantic-2.10.3-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::pydantic==2.10.3=py310haa95532_0[md5=c44ea686d6d24f5a1611d11fe08705cf]", "sha256": "b53e482d7e682ef8c19f68b0c4a9ee1ec9077bf0d446a725a894fbb3bcdde0d1", "size": 672508, "subdir": "win-64", "timestamp": 1734736304000, "url": "https://repo.anaconda.com/pkgs/main/win-64/pydantic-2.10.3-py310haa95532_0.conda", "version": "2.10.3"}