{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\boltons-24.1.0-py310haa95532_0", "files": ["Lib/site-packages/boltons-24.1.0.dist-info/INSTALLER", "Lib/site-packages/boltons-24.1.0.dist-info/LICENSE", "Lib/site-packages/boltons-24.1.0.dist-info/METADATA", "Lib/site-packages/boltons-24.1.0.dist-info/RECORD", "Lib/site-packages/boltons-24.1.0.dist-info/REQUESTED", "Lib/site-packages/boltons-24.1.0.dist-info/WHEEL", "Lib/site-packages/boltons-24.1.0.dist-info/direct_url.json", "Lib/site-packages/boltons-24.1.0.dist-info/top_level.txt", "Lib/site-packages/boltons/__init__.py", "Lib/site-packages/boltons/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/cacheutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/debugutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/deprutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/dictutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/easterutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/ecoutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/excutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/fileutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/formatutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/funcutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/gcutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/ioutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/iterutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/jsonutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/listutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/mathutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/mboxutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/namedutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/pathutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/queueutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/setutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/socketutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/statsutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/strutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/tableutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/tbutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/timeutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/typeutils.cpython-310.pyc", "Lib/site-packages/boltons/__pycache__/urlutils.cpython-310.pyc", "Lib/site-packages/boltons/cacheutils.py", "Lib/site-packages/boltons/debugutils.py", "Lib/site-packages/boltons/deprutils.py", "Lib/site-packages/boltons/dictutils.py", "Lib/site-packages/boltons/easterutils.py", "Lib/site-packages/boltons/ecoutils.py", "Lib/site-packages/boltons/excutils.py", "Lib/site-packages/boltons/fileutils.py", "Lib/site-packages/boltons/formatutils.py", "Lib/site-packages/boltons/funcutils.py", "Lib/site-packages/boltons/gcutils.py", "Lib/site-packages/boltons/ioutils.py", "Lib/site-packages/boltons/iterutils.py", "Lib/site-packages/boltons/jsonutils.py", "Lib/site-packages/boltons/listutils.py", "Lib/site-packages/boltons/mathutils.py", "Lib/site-packages/boltons/mboxutils.py", "Lib/site-packages/boltons/namedutils.py", "Lib/site-packages/boltons/pathutils.py", "Lib/site-packages/boltons/queueutils.py", "Lib/site-packages/boltons/setutils.py", "Lib/site-packages/boltons/socketutils.py", "Lib/site-packages/boltons/statsutils.py", "Lib/site-packages/boltons/strutils.py", "Lib/site-packages/boltons/tableutils.py", "Lib/site-packages/boltons/tbutils.py", "Lib/site-packages/boltons/timeutils.py", "Lib/site-packages/boltons/typeutils.py", "Lib/site-packages/boltons/urlutils.py"], "fn": "boltons-24.1.0-py310haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\boltons-24.1.0-py310haa95532_0", "type": 1}, "md5": "52f095149efb6994a921e709ce43d3ca", "name": "boltons", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\boltons-24.1.0-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::boltons==24.1.0=py310haa95532_0[md5=52f095149efb6994a921e709ce43d3ca]", "sha256": "d54eb4a2e2851fdd43662c6327458c828f59e825e7f757487d4ea1c4ea7ab4b9", "size": 430345, "subdir": "win-64", "timestamp": 1737061959000, "url": "https://repo.anaconda.com/pkgs/main/win-64/boltons-24.1.0-py310haa95532_0.conda", "version": "24.1.0"}