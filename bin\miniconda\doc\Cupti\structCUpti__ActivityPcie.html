<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityPcie Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityPcie Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityPcie" -->PCI devices information required to construct topology.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#0d4511d41ffd96ea9093296189db9883">attr</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#4f8690fd8762e05c417d33447da2258c">domain</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityPcie.html#3d4685de775cde6674c8ad076347de54">bridgeId</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;CUdevice&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityPcie.html#e6331359f8ab0d922e7de690532cdcdc">devId</a></td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#06d80135bf829251ed12dba9b042ce76">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#2d18c51ba371a359ab773c8f35c62f33">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#035f452cd8eccd4b70f0525b4b5106e3">linkRate</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#c69d0b3eaa88e7d37f05c74252ce8be1">linkWidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#c4f652d71bbefe5b764c33ffa508b3d0">pcieGeneration</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g21881d93eb4622a660f0ebd8e2fd2bab">CUpti_PcieDeviceType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#d08f77ef897e067279f406d2a9420427">type</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#72e5f6481018d566ed6d06815981e75a">upstreamBus</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#aad8a2569bf2d02fe0858c8debdf508d">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#24ad7e574c8887adc89d36b14db1b949">pad0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUdevice&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#ec71be9d1b09221e84a85f7c01d0eeca">peerDev</a> [CUPTI_MAX_GPUS]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#8976ff694e28acaadc04dadb09e75590">secondaryBus</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUuuid&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#2e6000a42f7999e0b50f23ae84a3dbb4">uuidDev</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityPcie.html#4218974c7a3b2c3e57ba7305d2b73e25">vendorId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure gives capabilities of GPU and PCI bridge connected to the PCIE bus which can be used to understand the topology. <hr><h2>Field Documentation</h2>
<a class="anchor" name="0d4511d41ffd96ea9093296189db9883"></a><!-- doxytag: member="CUpti_ActivityPcie::attr" ref="0d4511d41ffd96ea9093296189db9883" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityPcie.html#0d4511d41ffd96ea9093296189db9883">CUpti_ActivityPcie::attr</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Attributes for more information about GPU (gpuAttr) or PCI Bridge (bridgeAttr) 
</div>
</div><p>
<a class="anchor" name="3d4685de775cde6674c8ad076347de54"></a><!-- doxytag: member="CUpti_ActivityPcie::bridgeId" ref="3d4685de775cde6674c8ad076347de54" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPcie.html#3d4685de775cde6674c8ad076347de54">CUpti_ActivityPcie::bridgeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A unique identifier for Bridge in the Topology 
</div>
</div><p>
<a class="anchor" name="aad8a2569bf2d02fe0858c8debdf508d"></a><!-- doxytag: member="CUpti_ActivityPcie::deviceId" ref="aad8a2569bf2d02fe0858c8debdf508d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#aad8a2569bf2d02fe0858c8debdf508d">CUpti_ActivityPcie::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Device ID of the bridge 
</div>
</div><p>
<a class="anchor" name="e6331359f8ab0d922e7de690532cdcdc"></a><!-- doxytag: member="CUpti_ActivityPcie::devId" ref="e6331359f8ab0d922e7de690532cdcdc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="structCUpti__ActivityPcie.html#e6331359f8ab0d922e7de690532cdcdc">CUpti_ActivityPcie::devId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
GPU device ID 
</div>
</div><p>
<a class="anchor" name="4f8690fd8762e05c417d33447da2258c"></a><!-- doxytag: member="CUpti_ActivityPcie::domain" ref="4f8690fd8762e05c417d33447da2258c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityPcie.html#4f8690fd8762e05c417d33447da2258c">CUpti_ActivityPcie::domain</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Domain for the GPU or Bridge, required to identify which PCIE bus it belongs to in multiple NUMA systems. 
</div>
</div><p>
<a class="anchor" name="06d80135bf829251ed12dba9b042ce76"></a><!-- doxytag: member="CUpti_ActivityPcie::id" ref="06d80135bf829251ed12dba9b042ce76" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityPcie.html#06d80135bf829251ed12dba9b042ce76">CUpti_ActivityPcie::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
A unique identifier for GPU or Bridge in Topology 
</div>
</div><p>
<a class="anchor" name="2d18c51ba371a359ab773c8f35c62f33"></a><!-- doxytag: member="CUpti_ActivityPcie::kind" ref="2d18c51ba371a359ab773c8f35c62f33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityPcie.html#2d18c51ba371a359ab773c8f35c62f33">CUpti_ActivityPcie::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_PCIE. 
</div>
</div><p>
<a class="anchor" name="035f452cd8eccd4b70f0525b4b5106e3"></a><!-- doxytag: member="CUpti_ActivityPcie::linkRate" ref="035f452cd8eccd4b70f0525b4b5106e3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#035f452cd8eccd4b70f0525b4b5106e3">CUpti_ActivityPcie::linkRate</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Link rate of the GPU or bridge in gigatransfers per second (GT/s) 
</div>
</div><p>
<a class="anchor" name="c69d0b3eaa88e7d37f05c74252ce8be1"></a><!-- doxytag: member="CUpti_ActivityPcie::linkWidth" ref="c69d0b3eaa88e7d37f05c74252ce8be1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#c69d0b3eaa88e7d37f05c74252ce8be1">CUpti_ActivityPcie::linkWidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Link width of the GPU or bridge 
</div>
</div><p>
<a class="anchor" name="24ad7e574c8887adc89d36b14db1b949"></a><!-- doxytag: member="CUpti_ActivityPcie::pad0" ref="24ad7e574c8887adc89d36b14db1b949" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#24ad7e574c8887adc89d36b14db1b949">CUpti_ActivityPcie::pad0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Padding for alignment 
</div>
</div><p>
<a class="anchor" name="c4f652d71bbefe5b764c33ffa508b3d0"></a><!-- doxytag: member="CUpti_ActivityPcie::pcieGeneration" ref="c4f652d71bbefe5b764c33ffa508b3d0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#c4f652d71bbefe5b764c33ffa508b3d0">CUpti_ActivityPcie::pcieGeneration</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
PCIE Generation of GPU or Bridge. 
</div>
</div><p>
<a class="anchor" name="ec71be9d1b09221e84a85f7c01d0eeca"></a><!-- doxytag: member="CUpti_ActivityPcie::peerDev" ref="ec71be9d1b09221e84a85f7c01d0eeca" args="[CUPTI_MAX_GPUS]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUdevice <a class="el" href="structCUpti__ActivityPcie.html#ec71be9d1b09221e84a85f7c01d0eeca">CUpti_ActivityPcie::peerDev</a>[CUPTI_MAX_GPUS]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
CUdevice with which this device has P2P capability. This can also be obtained by querying cuDeviceCanAccessPeer or cudaDeviceCanAccessPeer APIs 
</div>
</div><p>
<a class="anchor" name="8976ff694e28acaadc04dadb09e75590"></a><!-- doxytag: member="CUpti_ActivityPcie::secondaryBus" ref="8976ff694e28acaadc04dadb09e75590" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#8976ff694e28acaadc04dadb09e75590">CUpti_ActivityPcie::secondaryBus</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The downstream bus number, used to search downstream devices/bridges connected to this bridge. 
</div>
</div><p>
<a class="anchor" name="d08f77ef897e067279f406d2a9420427"></a><!-- doxytag: member="CUpti_ActivityPcie::type" ref="d08f77ef897e067279f406d2a9420427" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g21881d93eb4622a660f0ebd8e2fd2bab">CUpti_PcieDeviceType</a> <a class="el" href="structCUpti__ActivityPcie.html#d08f77ef897e067279f406d2a9420427">CUpti_ActivityPcie::type</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device in topology, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g21881d93eb4622a660f0ebd8e2fd2bab">CUpti_PcieDeviceType</a>. If type is CUPTI_PCIE_DEVICE_TYPE_GPU use devId for id and gpuAttr and if type is CUPTI_PCIE_DEVICE_TYPE_BRIDGE use bridgeId for id and bridgeAttr. 
</div>
</div><p>
<a class="anchor" name="72e5f6481018d566ed6d06815981e75a"></a><!-- doxytag: member="CUpti_ActivityPcie::upstreamBus" ref="72e5f6481018d566ed6d06815981e75a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#72e5f6481018d566ed6d06815981e75a">CUpti_ActivityPcie::upstreamBus</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Upstream bus ID for the GPU or PCI bridge. Required to identify which bus it is connected to in the topology. 
</div>
</div><p>
<a class="anchor" name="2e6000a42f7999e0b50f23ae84a3dbb4"></a><!-- doxytag: member="CUpti_ActivityPcie::uuidDev" ref="2e6000a42f7999e0b50f23ae84a3dbb4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUuuid <a class="el" href="structCUpti__ActivityPcie.html#2e6000a42f7999e0b50f23ae84a3dbb4">CUpti_ActivityPcie::uuidDev</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
UUID for the device. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. 
</div>
</div><p>
<a class="anchor" name="4218974c7a3b2c3e57ba7305d2b73e25"></a><!-- doxytag: member="CUpti_ActivityPcie::vendorId" ref="4218974c7a3b2c3e57ba7305d2b73e25" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityPcie.html#4218974c7a3b2c3e57ba7305d2b73e25">CUpti_ActivityPcie::vendorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Vendor ID of the bridge 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
