{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["libnvjitlink >=12.1.55"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjitlink-dev-12.1.55-0", "features": "", "files": ["bin/nvJitLink_120_0.dll", "include/nvJitLink.h", "res/nvJitLink_win32.def"], "fn": "libnvjitlink-dev-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjitlink-dev-12.1.55-0", "type": 1}, "md5": "069c9a7cbc0b5b30f5aadc60e8c4fdba", "name": "libnvjitlink-dev", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libnvjitlink-dev-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/nvJitLink_120_0.dll", "path_type": "hardlink", "sha256": "7440ddbc5070b7518598dcea34db4c5e8329758f40d1424ff0ac180ecb960d31", "sha256_in_prefix": "7440ddbc5070b7518598dcea34db4c5e8329758f40d1424ff0ac180ecb960d31", "size_in_bytes": 34383360}, {"_path": "include/nvJitLink.h", "path_type": "hardlink", "sha256": "72c8ba357f419b7a8695238b6ad7fced7fe2a7d4733dd3aee92f277658203cd8", "sha256_in_prefix": "72c8ba357f419b7a8695238b6ad7fced7fe2a7d4733dd3aee92f277658203cd8", "size_in_bytes": 14104}, {"_path": "res/nvJitLink_win32.def", "path_type": "hardlink", "sha256": "b783d4a6785ff41365fe4bd7ef226adcbd55606db71c71bdc29febbf5d00652c", "sha256_in_prefix": "b783d4a6785ff41365fe4bd7ef226adcbd55606db71c71bdc29febbf5d00652c", "size_in_bytes": 769}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 14451243, "subdir": "win-64", "timestamp": 1674622744000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/libnvjitlink-dev-12.1.55-0.tar.bz2", "version": "12.1.55"}