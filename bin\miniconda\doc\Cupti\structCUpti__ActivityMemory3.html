<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemory3 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemory3 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemory3" -->The activity record for memory.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html">PACKED_ALIGNMENT</a></td></tr>

<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#13e46bbd66ce8af00aec688b1ec8791b">address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#3351348614cb5183a5a225eb01c9bd5f">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#ee22ff7491df85a7b2dcc03dcd35d36e">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#ac86e1ca86d56826fbfe2ce3d4bbda5e">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#b8f4ec99df4f9e3f75fcd7520ef5bf8a">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#64f98eab9137762aed6fa4f946fc693c">isAsync</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#ad766409b24cec785b83feba73dbfd0e">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#34f46f9937efc2906a6c822e15f4010f">memoryKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#c563b44178403adddff1f47e16a5ae55">memoryOperationType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">struct <br class="typebreak">
<a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html">CUpti_ActivityMemory3::PACKED_ALIGNMENT</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#d9450112dca9e61e1a34d3bbe2b35c85">memoryPoolConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#2a52532acd95190824fed56f76889853">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#be95bb903c9c74771055bf4a09870d4c">PC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#6a0a59696c57b1094892ba632b14b534">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#642cb198ed20485d9ddf3fe150d94c30">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory3.html#5c48ceb7af7d409584f1ec86fbc2c6da">timestamp</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory allocation and free operation (CUPTI_ACTIVITY_KIND_MEMORY2). This activity record provides separate records for memory allocation and memory release operations. This allows to correlate the corresponding driver and runtime API activity record with the memory operation.<p>
Note: This activity record is an upgrade over <a class="el" href="structCUpti__ActivityMemory.html">CUpti_ActivityMemory</a> enabled using the kind <a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3fee93ef030088d7a992a1abd6d6e53b0">CUPTI_ACTIVITY_KIND_MEMORY</a>. <a class="el" href="structCUpti__ActivityMemory.html">CUpti_ActivityMemory</a> provides a single record for the memory allocation and memory release operations. <hr><h2>Field Documentation</h2>
<a class="anchor" name="13e46bbd66ce8af00aec688b1ec8791b"></a><!-- doxytag: member="CUpti_ActivityMemory3::address" ref="13e46bbd66ce8af00aec688b1ec8791b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory3.html#13e46bbd66ce8af00aec688b1ec8791b">CUpti_ActivityMemory3::address</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The virtual address of the allocation. 
</div>
</div><p>
<a class="anchor" name="3351348614cb5183a5a225eb01c9bd5f"></a><!-- doxytag: member="CUpti_ActivityMemory3::bytes" ref="3351348614cb5183a5a225eb01c9bd5f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory3.html#3351348614cb5183a5a225eb01c9bd5f">CUpti_ActivityMemory3::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes of memory allocated. 
</div>
</div><p>
<a class="anchor" name="ee22ff7491df85a7b2dcc03dcd35d36e"></a><!-- doxytag: member="CUpti_ActivityMemory3::contextId" ref="ee22ff7491df85a7b2dcc03dcd35d36e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory3.html#ee22ff7491df85a7b2dcc03dcd35d36e">CUpti_ActivityMemory3::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context. If context is NULL, <code>contextId</code> is set to CUPTI_INVALID_CONTEXT_ID. 
</div>
</div><p>
<a class="anchor" name="ac86e1ca86d56826fbfe2ce3d4bbda5e"></a><!-- doxytag: member="CUpti_ActivityMemory3::correlationId" ref="ac86e1ca86d56826fbfe2ce3d4bbda5e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory3.html#ac86e1ca86d56826fbfe2ce3d4bbda5e">CUpti_ActivityMemory3::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory operation. Each memory operation is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory operation. 
</div>
</div><p>
<a class="anchor" name="b8f4ec99df4f9e3f75fcd7520ef5bf8a"></a><!-- doxytag: member="CUpti_ActivityMemory3::deviceId" ref="b8f4ec99df4f9e3f75fcd7520ef5bf8a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory3.html#b8f4ec99df4f9e3f75fcd7520ef5bf8a">CUpti_ActivityMemory3::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory operation is taking place. 
</div>
</div><p>
<a class="anchor" name="64f98eab9137762aed6fa4f946fc693c"></a><!-- doxytag: member="CUpti_ActivityMemory3::isAsync" ref="64f98eab9137762aed6fa4f946fc693c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory3.html#64f98eab9137762aed6fa4f946fc693c">CUpti_ActivityMemory3::isAsync</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<code>isAsync</code> is set if memory operation happens through async memory APIs. 
</div>
</div><p>
<a class="anchor" name="ad766409b24cec785b83feba73dbfd0e"></a><!-- doxytag: member="CUpti_ActivityMemory3::kind" ref="ad766409b24cec785b83feba73dbfd0e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemory3.html#ad766409b24cec785b83feba73dbfd0e">CUpti_ActivityMemory3::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMORY2 
</div>
</div><p>
<a class="anchor" name="34f46f9937efc2906a6c822e15f4010f"></a><!-- doxytag: member="CUpti_ActivityMemory3::memoryKind" ref="34f46f9937efc2906a6c822e15f4010f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a> <a class="el" href="structCUpti__ActivityMemory3.html#34f46f9937efc2906a6c822e15f4010f">CUpti_ActivityMemory3::memoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory kind requested by the user, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a>. 
</div>
</div><p>
<a class="anchor" name="c563b44178403adddff1f47e16a5ae55"></a><!-- doxytag: member="CUpti_ActivityMemory3::memoryOperationType" ref="c563b44178403adddff1f47e16a5ae55" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a> <a class="el" href="structCUpti__ActivityMemory3.html#c563b44178403adddff1f47e16a5ae55">CUpti_ActivityMemory3::memoryOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory operation requested by the user, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g6404b89cfbbd60e04204244d230b15c4">CUpti_ActivityMemoryOperationType</a>. 
</div>
</div><p>
<a class="anchor" name="d9450112dca9e61e1a34d3bbe2b35c85"></a><!-- doxytag: member="CUpti_ActivityMemory3::memoryPoolConfig" ref="d9450112dca9e61e1a34d3bbe2b35c85" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct <a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html">CUpti_ActivityMemory3::PACKED_ALIGNMENT</a>  <a class="el" href="structCUpti__ActivityMemory3.html#d9450112dca9e61e1a34d3bbe2b35c85">CUpti_ActivityMemory3::memoryPoolConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory pool configuration used for the memory operations. 
</div>
</div><p>
<a class="anchor" name="2a52532acd95190824fed56f76889853"></a><!-- doxytag: member="CUpti_ActivityMemory3::name" ref="2a52532acd95190824fed56f76889853" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityMemory3.html#2a52532acd95190824fed56f76889853">CUpti_ActivityMemory3::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Variable name. This name is shared across all activity records representing the same symbol, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="be95bb903c9c74771055bf4a09870d4c"></a><!-- doxytag: member="CUpti_ActivityMemory3::PC" ref="be95bb903c9c74771055bf4a09870d4c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory3.html#be95bb903c9c74771055bf4a09870d4c">CUpti_ActivityMemory3::PC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The program counter of the memory operation. 
</div>
</div><p>
<a class="anchor" name="6a0a59696c57b1094892ba632b14b534"></a><!-- doxytag: member="CUpti_ActivityMemory3::processId" ref="6a0a59696c57b1094892ba632b14b534" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory3.html#6a0a59696c57b1094892ba632b14b534">CUpti_ActivityMemory3::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process to which this record belongs to. 
</div>
</div><p>
<a class="anchor" name="642cb198ed20485d9ddf3fe150d94c30"></a><!-- doxytag: member="CUpti_ActivityMemory3::streamId" ref="642cb198ed20485d9ddf3fe150d94c30" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory3.html#642cb198ed20485d9ddf3fe150d94c30">CUpti_ActivityMemory3::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream. If memory operation is not async, <code>streamId</code> is set to CUPTI_INVALID_STREAM_ID. 
</div>
</div><p>
<a class="anchor" name="5c48ceb7af7d409584f1ec86fbc2c6da"></a><!-- doxytag: member="CUpti_ActivityMemory3::timestamp" ref="5c48ceb7af7d409584f1ec86fbc2c6da" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory3.html#5c48ceb7af7d409584f1ec86fbc2c6da">CUpti_ActivityMemory3::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory operation, in ns. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
