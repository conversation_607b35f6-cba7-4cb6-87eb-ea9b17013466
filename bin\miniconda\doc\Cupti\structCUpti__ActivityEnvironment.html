<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityEnvironment Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityEnvironment Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityEnvironment" -->The activity record for CUPTI environmental data.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#200295a5a961986e8db2a428d702fa92">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gea1ccba1baca62ba5d6919e5685bfedf">CUpti_ActivityEnvironmentKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#24a3b403bb0fc5bab993ae9189d9feee">environmentKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#18c9f42de8894c2b85b9680a3210828f">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#e015f7d1a880d8948df34c90e28e679a">timestamp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g3dcc6c3e9409a1183932febfdde58173">CUpti_EnvironmentClocksThrottleReason</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#40fcdb37ecf84c43601a6830c3104822">clocksThrottleReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#5941e586a8b85e49dcd20212df44c82d">fanSpeed</a></td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#ce7841a7064386ec4cac0a2e65952048">cooling</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#cb13f6306a8b9247090700ee208a6a4e">gpuTemperature</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#fe99db4bdefe7bb7a428b9b0173b649d">memoryClock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#65551f5708666db35d3fda6f851865b5">pcieLinkGen</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#dabf37883d378b25c8a56c1c765c98c9">pcieLinkWidth</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#035f3db14df036a2749d0c2d7413e624">power</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#aec43cd954ae85b91efb47ce5780e1bb">powerLimit</a></td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#6975324c822504d779a6f14a0888933f">power</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#8d27b0f9fc286bdf0907c24105630e9b">smClock</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;<a class="el" href="group__CUPTI__ACTIVITY__API.html#g3dcc6c3e9409a1183932febfdde58173">CUpti_EnvironmentClocksThrottleReason</a>&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#40fcdb37ecf84c43601a6830c3104822">clocksThrottleReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#fe99db4bdefe7bb7a428b9b0173b649d">memoryClock</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#65551f5708666db35d3fda6f851865b5">pcieLinkGen</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#dabf37883d378b25c8a56c1c765c98c9">pcieLinkWidth</a></td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#8d27b0f9fc286bdf0907c24105630e9b">smClock</a></td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#db88826b444ad2ac61555ee510662f28">speed</a></td></tr>

<tr><td class="memItemLeft" nowrap>struct {</td></tr>

<tr><td class="memItemLeft" nowrap>&nbsp;&nbsp;&nbsp;uint32_t&nbsp;&nbsp;&nbsp;<a class="el" href="structCUpti__ActivityEnvironment.html#cb13f6306a8b9247090700ee208a6a4e">gpuTemperature</a></td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityEnvironment.html#330d18924ce3e785806244fdb8f7fca9">temperature</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record provides CUPTI environmental data, include power, clocks, and thermals. This information is sampled at various rates and returned in this activity record. The consumer of the record needs to check the environmentKind field to figure out what kind of environmental record this is. <hr><h2>Field Documentation</h2>
<a class="anchor" name="40fcdb37ecf84c43601a6830c3104822"></a><!-- doxytag: member="CUpti_ActivityEnvironment::clocksThrottleReasons" ref="40fcdb37ecf84c43601a6830c3104822" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g3dcc6c3e9409a1183932febfdde58173">CUpti_EnvironmentClocksThrottleReason</a> <a class="el" href="structCUpti__ActivityEnvironment.html#40fcdb37ecf84c43601a6830c3104822">CUpti_ActivityEnvironment::clocksThrottleReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The clocks throttle reasons. 
</div>
</div><p>
<a class="anchor" name="ce7841a7064386ec4cac0a2e65952048"></a><!-- doxytag: member="CUpti_ActivityEnvironment::cooling" ref="ce7841a7064386ec4cac0a2e65952048" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__ActivityEnvironment.html#ce7841a7064386ec4cac0a2e65952048">CUpti_ActivityEnvironment::cooling</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Data returned for CUPTI_ACTIVITY_ENVIRONMENT_COOLING environment kind. 
</div>
</div><p>
<a class="anchor" name="200295a5a961986e8db2a428d702fa92"></a><!-- doxytag: member="CUpti_ActivityEnvironment::deviceId" ref="200295a5a961986e8db2a428d702fa92" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#200295a5a961986e8db2a428d702fa92">CUpti_ActivityEnvironment::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device 
</div>
</div><p>
<a class="anchor" name="24a3b403bb0fc5bab993ae9189d9feee"></a><!-- doxytag: member="CUpti_ActivityEnvironment::environmentKind" ref="24a3b403bb0fc5bab993ae9189d9feee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gea1ccba1baca62ba5d6919e5685bfedf">CUpti_ActivityEnvironmentKind</a> <a class="el" href="structCUpti__ActivityEnvironment.html#24a3b403bb0fc5bab993ae9189d9feee">CUpti_ActivityEnvironment::environmentKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of data reported in this record. 
</div>
</div><p>
<a class="anchor" name="5941e586a8b85e49dcd20212df44c82d"></a><!-- doxytag: member="CUpti_ActivityEnvironment::fanSpeed" ref="5941e586a8b85e49dcd20212df44c82d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#5941e586a8b85e49dcd20212df44c82d">CUpti_ActivityEnvironment::fanSpeed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The fan speed as percentage of maximum. 
</div>
</div><p>
<a class="anchor" name="cb13f6306a8b9247090700ee208a6a4e"></a><!-- doxytag: member="CUpti_ActivityEnvironment::gpuTemperature" ref="cb13f6306a8b9247090700ee208a6a4e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#cb13f6306a8b9247090700ee208a6a4e">CUpti_ActivityEnvironment::gpuTemperature</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The GPU temperature in degrees C. 
</div>
</div><p>
<a class="anchor" name="18c9f42de8894c2b85b9680a3210828f"></a><!-- doxytag: member="CUpti_ActivityEnvironment::kind" ref="18c9f42de8894c2b85b9680a3210828f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityEnvironment.html#18c9f42de8894c2b85b9680a3210828f">CUpti_ActivityEnvironment::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_ENVIRONMENT. 
</div>
</div><p>
<a class="anchor" name="fe99db4bdefe7bb7a428b9b0173b649d"></a><!-- doxytag: member="CUpti_ActivityEnvironment::memoryClock" ref="fe99db4bdefe7bb7a428b9b0173b649d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#fe99db4bdefe7bb7a428b9b0173b649d">CUpti_ActivityEnvironment::memoryClock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory frequency in MHz 
</div>
</div><p>
<a class="anchor" name="65551f5708666db35d3fda6f851865b5"></a><!-- doxytag: member="CUpti_ActivityEnvironment::pcieLinkGen" ref="65551f5708666db35d3fda6f851865b5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#65551f5708666db35d3fda6f851865b5">CUpti_ActivityEnvironment::pcieLinkGen</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The PCIe link generation. 
</div>
</div><p>
<a class="anchor" name="dabf37883d378b25c8a56c1c765c98c9"></a><!-- doxytag: member="CUpti_ActivityEnvironment::pcieLinkWidth" ref="dabf37883d378b25c8a56c1c765c98c9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#dabf37883d378b25c8a56c1c765c98c9">CUpti_ActivityEnvironment::pcieLinkWidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The PCIe link width. 
</div>
</div><p>
<a class="anchor" name="6975324c822504d779a6f14a0888933f"></a><!-- doxytag: member="CUpti_ActivityEnvironment::power" ref="6975324c822504d779a6f14a0888933f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__ActivityEnvironment.html#035f3db14df036a2749d0c2d7413e624">CUpti_ActivityEnvironment::power</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Data returned for CUPTI_ACTIVITY_ENVIRONMENT_POWER environment kind. 
</div>
</div><p>
<a class="anchor" name="035f3db14df036a2749d0c2d7413e624"></a><!-- doxytag: member="CUpti_ActivityEnvironment::power" ref="035f3db14df036a2749d0c2d7413e624" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#035f3db14df036a2749d0c2d7413e624">CUpti_ActivityEnvironment::power</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The power in milliwatts consumed by GPU and associated circuitry. 
</div>
</div><p>
<a class="anchor" name="aec43cd954ae85b91efb47ce5780e1bb"></a><!-- doxytag: member="CUpti_ActivityEnvironment::powerLimit" ref="aec43cd954ae85b91efb47ce5780e1bb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#aec43cd954ae85b91efb47ce5780e1bb">CUpti_ActivityEnvironment::powerLimit</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The power in milliwatts that will trigger power management algorithm. 
</div>
</div><p>
<a class="anchor" name="8d27b0f9fc286bdf0907c24105630e9b"></a><!-- doxytag: member="CUpti_ActivityEnvironment::smClock" ref="8d27b0f9fc286bdf0907c24105630e9b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityEnvironment.html#8d27b0f9fc286bdf0907c24105630e9b">CUpti_ActivityEnvironment::smClock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The SM frequency in MHz 
</div>
</div><p>
<a class="anchor" name="db88826b444ad2ac61555ee510662f28"></a><!-- doxytag: member="CUpti_ActivityEnvironment::speed" ref="db88826b444ad2ac61555ee510662f28" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__ActivityEnvironment.html#db88826b444ad2ac61555ee510662f28">CUpti_ActivityEnvironment::speed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Data returned for CUPTI_ACTIVITY_ENVIRONMENT_SPEED environment kind. 
</div>
</div><p>
<a class="anchor" name="330d18924ce3e785806244fdb8f7fca9"></a><!-- doxytag: member="CUpti_ActivityEnvironment::temperature" ref="330d18924ce3e785806244fdb8f7fca9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   <a class="el" href="structCUpti__ActivityEnvironment.html#330d18924ce3e785806244fdb8f7fca9">CUpti_ActivityEnvironment::temperature</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Data returned for CUPTI_ACTIVITY_ENVIRONMENT_TEMPERATURE environment kind. 
</div>
</div><p>
<a class="anchor" name="e015f7d1a880d8948df34c90e28e679a"></a><!-- doxytag: member="CUpti_ActivityEnvironment::timestamp" ref="e015f7d1a880d8948df34c90e28e679a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityEnvironment.html#e015f7d1a880d8948df34c90e28e679a">CUpti_ActivityEnvironment::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when this sample was retrieved, in ns. A value of 0 indicates that timestamp information could not be collected for the marker. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
