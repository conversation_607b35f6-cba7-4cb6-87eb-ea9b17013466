<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingEnableParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingEnableParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingEnableParams" -->Params for cuptiPCSamplingEnable.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingEnableParams.html#bec3da3a3f9d2b8e17d990f11861d1ca">ctx</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingEnableParams.html#a06792e6fb766237985fc7b0e4f23f8e">pPriv</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingEnableParams.html#ee915ff1cb4a32414e17cfd0cdb700ca">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="bec3da3a3f9d2b8e17d990f11861d1ca"></a><!-- doxytag: member="CUpti_PCSamplingEnableParams::ctx" ref="bec3da3a3f9d2b8e17d990f11861d1ca" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="structCUpti__PCSamplingEnableParams.html#bec3da3a3f9d2b8e17d990f11861d1ca">CUpti_PCSamplingEnableParams::ctx</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] CUcontext 
</div>
</div><p>
<a class="anchor" name="a06792e6fb766237985fc7b0e4f23f8e"></a><!-- doxytag: member="CUpti_PCSamplingEnableParams::pPriv" ref="a06792e6fb766237985fc7b0e4f23f8e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__PCSamplingEnableParams.html#a06792e6fb766237985fc7b0e4f23f8e">CUpti_PCSamplingEnableParams::pPriv</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Assign to NULL 
</div>
</div><p>
<a class="anchor" name="ee915ff1cb4a32414e17cfd0cdb700ca"></a><!-- doxytag: member="CUpti_PCSamplingEnableParams::size" ref="ee915ff1cb4a32414e17cfd0cdb700ca" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingEnableParams.html#ee915ff1cb4a32414e17cfd0cdb700ca">CUpti_PCSamplingEnableParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure i.e. CUpti_PCSamplingEnableParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
