<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>SanitizerApi: Sanitizer Result Codes</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Sanitizer Result Codes</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d80d35805bafd1be61967646e8fd225e6c">SANITIZER_SUCCESS</a> =  0, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d8cd4d5554b11e3a1a7319819984077edb">SANITIZER_ERROR_INVALID_PARAMETER</a> =  1, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d8ba2cb91f407560b1014cd67c4c24502b">SANITIZER_ERROR_INVALID_DEVICE</a> =  2, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d8099362bd6bf2807f3785cc2adeb0738f">SANITIZER_ERROR_INVALID_CONTEXT</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d805198bb6cc6d272e8557b8f872fe7519">SANITIZER_ERROR_INVALID_DOMAIN_ID</a> =  4, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d88036a4af0fc5e632471bb0dd25376748">SANITIZER_ERROR_INVALID_CALLBACK_ID</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d86d773b203970ec61a9d11926e3a430f5">SANITIZER_ERROR_INVALID_OPERATION</a> =  6, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d803d40e5d6c13e7f2e076373523d94db9">SANITIZER_ERROR_OUT_OF_MEMORY</a> =  7, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d881fd28c0b2fa0e100db9197e472a9273">SANITIZER_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</a> =  8, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d86f85fe9b42de6be2a8e252503f4bb610">SANITIZER_ERROR_API_NOT_IMPLEMENTED</a> =  9, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d89fa172f3d65a626b00de9aba23d2c28f">SANITIZER_ERROR_MAX_LIMIT_REACHED</a> =  10, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d85fbbbe4e2fb2e3f0c00ea1a295fa8b2a">SANITIZER_ERROR_NOT_READY</a> =  11, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d81a5d801b3160f678426b169f6810290f">SANITIZER_ERROR_NOT_COMPATIBLE</a> =  12, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d84f2507a514257be5dd447f8c659594c1">SANITIZER_ERROR_NOT_INITIALIZED</a> =  13, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d8e5c22c74893c834db40403e82703776e">SANITIZER_ERROR_NOT_SUPPORTED</a> =  14, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d853c91ef50d49c3a7e9432d5a572c461b">SANITIZER_ERROR_ADDRESS_NOT_IN_DEVICE_MEMORY</a> =  15, 
<br>
&nbsp;&nbsp;<a class="el" href="group__SANITIZER__RESULT__API.html#gg8edf13e06b1b4001d7577b07ddd575d8db0b62151593c6aff67023f6719ef6fd">SANITIZER_ERROR_UNKNOWN</a> =  999
<br>
 }</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Sanitizer result codes.  <a href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__RESULT__API.html#gcb5ad11b4de885309b8f5e9e999abfc4">sanitizerGetResultString</a> (<a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> result, const char **str)</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Error and result codes returned by Sanitizer functions. <hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g8edf13e06b1b4001d7577b07ddd575d8"></a><!-- doxytag: member="sanitizer_result.h::SanitizerResult" ref="g8edf13e06b1b4001d7577b07ddd575d8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Error and result codes returned by Sanitizer functions. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d80d35805bafd1be61967646e8fd225e6c"></a><!-- doxytag: member="SANITIZER_SUCCESS" ref="gg8edf13e06b1b4001d7577b07ddd575d80d35805bafd1be61967646e8fd225e6c" args="" -->SANITIZER_SUCCESS</em>&nbsp;</td><td>
No error. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d8cd4d5554b11e3a1a7319819984077edb"></a><!-- doxytag: member="SANITIZER_ERROR_INVALID_PARAMETER" ref="gg8edf13e06b1b4001d7577b07ddd575d8cd4d5554b11e3a1a7319819984077edb" args="" -->SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>
One or more of the parameters is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d8ba2cb91f407560b1014cd67c4c24502b"></a><!-- doxytag: member="SANITIZER_ERROR_INVALID_DEVICE" ref="gg8edf13e06b1b4001d7577b07ddd575d8ba2cb91f407560b1014cd67c4c24502b" args="" -->SANITIZER_ERROR_INVALID_DEVICE</em>&nbsp;</td><td>
The device does not correspond to a valid CUDA device. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d8099362bd6bf2807f3785cc2adeb0738f"></a><!-- doxytag: member="SANITIZER_ERROR_INVALID_CONTEXT" ref="gg8edf13e06b1b4001d7577b07ddd575d8099362bd6bf2807f3785cc2adeb0738f" args="" -->SANITIZER_ERROR_INVALID_CONTEXT</em>&nbsp;</td><td>
The context is NULL or not valid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d805198bb6cc6d272e8557b8f872fe7519"></a><!-- doxytag: member="SANITIZER_ERROR_INVALID_DOMAIN_ID" ref="gg8edf13e06b1b4001d7577b07ddd575d805198bb6cc6d272e8557b8f872fe7519" args="" -->SANITIZER_ERROR_INVALID_DOMAIN_ID</em>&nbsp;</td><td>
The domain ID is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d88036a4af0fc5e632471bb0dd25376748"></a><!-- doxytag: member="SANITIZER_ERROR_INVALID_CALLBACK_ID" ref="gg8edf13e06b1b4001d7577b07ddd575d88036a4af0fc5e632471bb0dd25376748" args="" -->SANITIZER_ERROR_INVALID_CALLBACK_ID</em>&nbsp;</td><td>
The callback ID is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d86d773b203970ec61a9d11926e3a430f5"></a><!-- doxytag: member="SANITIZER_ERROR_INVALID_OPERATION" ref="gg8edf13e06b1b4001d7577b07ddd575d86d773b203970ec61a9d11926e3a430f5" args="" -->SANITIZER_ERROR_INVALID_OPERATION</em>&nbsp;</td><td>
The current operation cannot be performed due to dependency on other factors. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d803d40e5d6c13e7f2e076373523d94db9"></a><!-- doxytag: member="SANITIZER_ERROR_OUT_OF_MEMORY" ref="gg8edf13e06b1b4001d7577b07ddd575d803d40e5d6c13e7f2e076373523d94db9" args="" -->SANITIZER_ERROR_OUT_OF_MEMORY</em>&nbsp;</td><td>
Unable to allocate enough memory to perform the requested operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d881fd28c0b2fa0e100db9197e472a9273"></a><!-- doxytag: member="SANITIZER_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT" ref="gg8edf13e06b1b4001d7577b07ddd575d881fd28c0b2fa0e100db9197e472a9273" args="" -->SANITIZER_ERROR_PARAMETER_SIZE_NOT_SUFFICIENT</em>&nbsp;</td><td>
The output buffer size is not sufficient to return all requested data. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d86f85fe9b42de6be2a8e252503f4bb610"></a><!-- doxytag: member="SANITIZER_ERROR_API_NOT_IMPLEMENTED" ref="gg8edf13e06b1b4001d7577b07ddd575d86f85fe9b42de6be2a8e252503f4bb610" args="" -->SANITIZER_ERROR_API_NOT_IMPLEMENTED</em>&nbsp;</td><td>
API is not implemented. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d89fa172f3d65a626b00de9aba23d2c28f"></a><!-- doxytag: member="SANITIZER_ERROR_MAX_LIMIT_REACHED" ref="gg8edf13e06b1b4001d7577b07ddd575d89fa172f3d65a626b00de9aba23d2c28f" args="" -->SANITIZER_ERROR_MAX_LIMIT_REACHED</em>&nbsp;</td><td>
The maximum limit is reached. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d85fbbbe4e2fb2e3f0c00ea1a295fa8b2a"></a><!-- doxytag: member="SANITIZER_ERROR_NOT_READY" ref="gg8edf13e06b1b4001d7577b07ddd575d85fbbbe4e2fb2e3f0c00ea1a295fa8b2a" args="" -->SANITIZER_ERROR_NOT_READY</em>&nbsp;</td><td>
The object is not ready to perform the requested operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d81a5d801b3160f678426b169f6810290f"></a><!-- doxytag: member="SANITIZER_ERROR_NOT_COMPATIBLE" ref="gg8edf13e06b1b4001d7577b07ddd575d81a5d801b3160f678426b169f6810290f" args="" -->SANITIZER_ERROR_NOT_COMPATIBLE</em>&nbsp;</td><td>
The current operation is not compatible with the current state of the object. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d84f2507a514257be5dd447f8c659594c1"></a><!-- doxytag: member="SANITIZER_ERROR_NOT_INITIALIZED" ref="gg8edf13e06b1b4001d7577b07ddd575d84f2507a514257be5dd447f8c659594c1" args="" -->SANITIZER_ERROR_NOT_INITIALIZED</em>&nbsp;</td><td>
Sanitizer is unable to initialize its connection to the CUDA driver. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d8e5c22c74893c834db40403e82703776e"></a><!-- doxytag: member="SANITIZER_ERROR_NOT_SUPPORTED" ref="gg8edf13e06b1b4001d7577b07ddd575d8e5c22c74893c834db40403e82703776e" args="" -->SANITIZER_ERROR_NOT_SUPPORTED</em>&nbsp;</td><td>
The attempted operation is not supported on the current system or device </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d853c91ef50d49c3a7e9432d5a572c461b"></a><!-- doxytag: member="SANITIZER_ERROR_ADDRESS_NOT_IN_DEVICE_MEMORY" ref="gg8edf13e06b1b4001d7577b07ddd575d853c91ef50d49c3a7e9432d5a572c461b" args="" -->SANITIZER_ERROR_ADDRESS_NOT_IN_DEVICE_MEMORY</em>&nbsp;</td><td>
The attempted device operation has a parameter not in device memory </td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg8edf13e06b1b4001d7577b07ddd575d8db0b62151593c6aff67023f6719ef6fd"></a><!-- doxytag: member="SANITIZER_ERROR_UNKNOWN" ref="gg8edf13e06b1b4001d7577b07ddd575d8db0b62151593c6aff67023f6719ef6fd" args="" -->SANITIZER_ERROR_UNKNOWN</em>&nbsp;</td><td>
An unknown internal error has occurred. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="gcb5ad11b4de885309b8f5e9e999abfc4"></a><!-- doxytag: member="sanitizer_result.h::sanitizerGetResultString" ref="gcb5ad11b4de885309b8f5e9e999abfc4" args="(SanitizerResult result, const char **str)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetResultString           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a>&nbsp;</td>
          <td class="paramname"> <em>result</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char **&nbsp;</td>
          <td class="paramname"> <em>str</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Get the descriptive string for a SanitizerResult.<p>
Return the descriptive string for a SanitizerResult in <code>*str</code>. <dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread-safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>result</em>&nbsp;</td><td>The result to get the string for </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>str</em>&nbsp;</td><td>Returns the string</td></tr>
  </table>
</dl>
<dl compact><dt><b>Return values:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_SUCCESS</em>&nbsp;</td><td>on success </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>SANITIZER_ERROR_INVALID_PARAMETER</em>&nbsp;</td><td>if <code>str</code> is NULL or <code>result</code> is not a valid SanitizerResult. </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Tue Jan 24 19:43:08 2023 for SanitizerApi by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
