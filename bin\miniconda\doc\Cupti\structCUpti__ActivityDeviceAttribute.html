<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityDeviceAttribute Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityDeviceAttribute Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityDeviceAttribute" -->The activity record for a device attribute.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDeviceAttribute.html#8f446483e9225ddc8201cef2ef0124d5">attribute</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDeviceAttribute.html#f7421077759b2031bff99f6456937e8c">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDeviceAttribute.html#296b9b6fed50288470026727b630e9c3">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDeviceAttribute.html#b88931484b80bb6cb871874a3bbc0d3c">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDeviceAttribute.html#c30c0591d8d5e598b33ff9dbc1d1a6be">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents information about a GPU device: either a CUpti_DeviceAttribute or CUdevice_attribute value (CUPTI_ACTIVITY_KIND_DEVICE_ATTRIBUTE). <hr><h2>Field Documentation</h2>
<a class="anchor" name="8f446483e9225ddc8201cef2ef0124d5"></a><!-- doxytag: member="CUpti_ActivityDeviceAttribute::attribute" ref="8f446483e9225ddc8201cef2ef0124d5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityDeviceAttribute.html#8f446483e9225ddc8201cef2ef0124d5">CUpti_ActivityDeviceAttribute::attribute</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The attribute, either a CUpti_DeviceAttribute or CUdevice_attribute. Flag CUPTI_ACTIVITY_FLAG_DEVICE_ATTRIBUTE_CUDEVICE is used to indicate what kind of attribute this is. If CUPTI_ACTIVITY_FLAG_DEVICE_ATTRIBUTE_CUDEVICE is 1 then CUdevice_attribute field is value, otherwise CUpti_DeviceAttribute field is valid. 
</div>
</div><p>
<a class="anchor" name="f7421077759b2031bff99f6456937e8c"></a><!-- doxytag: member="CUpti_ActivityDeviceAttribute::deviceId" ref="f7421077759b2031bff99f6456937e8c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDeviceAttribute.html#f7421077759b2031bff99f6456937e8c">CUpti_ActivityDeviceAttribute::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device that this attribute applies to. 
</div>
</div><p>
<a class="anchor" name="296b9b6fed50288470026727b630e9c3"></a><!-- doxytag: member="CUpti_ActivityDeviceAttribute::flags" ref="296b9b6fed50288470026727b630e9c3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityDeviceAttribute.html#296b9b6fed50288470026727b630e9c3">CUpti_ActivityDeviceAttribute::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the device. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="b88931484b80bb6cb871874a3bbc0d3c"></a><!-- doxytag: member="CUpti_ActivityDeviceAttribute::kind" ref="b88931484b80bb6cb871874a3bbc0d3c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityDeviceAttribute.html#b88931484b80bb6cb871874a3bbc0d3c">CUpti_ActivityDeviceAttribute::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_DEVICE_ATTRIBUTE. 
</div>
</div><p>
<a class="anchor" name="c30c0591d8d5e598b33ff9dbc1d1a6be"></a><!-- doxytag: member="CUpti_ActivityDeviceAttribute::value" ref="c30c0591d8d5e598b33ff9dbc1d1a6be" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityDeviceAttribute.html#c30c0591d8d5e598b33ff9dbc1d1a6be">CUpti_ActivityDeviceAttribute::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The value for the attribute. See CUpti_DeviceAttribute and CUdevice_attribute for the type of the value for a given attribute. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
