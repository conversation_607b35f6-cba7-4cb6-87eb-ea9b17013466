<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>CUPTI</b>::<b>PcSamplingUtil</b>::<a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html">CUptiUtil_GetBufferInfoParams</a>
  </div>
</div>
<div class="contents">
<h1>CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html">CUPTI PC Sampling Utility API</a>]</small>
</h1><!-- doxytag: class="CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams" -->Params for CuptiUtilGetBufferInfo.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structBufferInfo.html">BufferInfo</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#a96e3e7eaeced59974f9cf61fa1e8c16">bufferInfoData</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">std::ifstream *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#f34d532700a114abc5360396d1bd384c">fileHandler</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#d8878b11400c5f365488657206365866">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="a96e3e7eaeced59974f9cf61fa1e8c16"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams::bufferInfoData" ref="a96e3e7eaeced59974f9cf61fa1e8c16" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structBufferInfo.html">BufferInfo</a> <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#a96e3e7eaeced59974f9cf61fa1e8c16">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams::bufferInfoData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Buffer Info. 
</div>
</div><p>
<a class="anchor" name="f34d532700a114abc5360396d1bd384c"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams::fileHandler" ref="f34d532700a114abc5360396d1bd384c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::ifstream* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#f34d532700a114abc5360396d1bd384c">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams::fileHandler</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
File handle. 
</div>
</div><p>
<a class="anchor" name="d8878b11400c5f365488657206365866"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams::size" ref="d8878b11400c5f365488657206365866" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html#d8878b11400c5f365488657206365866">CUPTI::PcSamplingUtil::CUptiUtil_GetBufferInfoParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of the data structure i.e. CUpti_PCSamplingDisableParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:52 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
