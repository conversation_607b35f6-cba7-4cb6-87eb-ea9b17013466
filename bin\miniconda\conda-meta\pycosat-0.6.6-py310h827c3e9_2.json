{"arch": "x86_64", "build": "py310h827c3e9_2", "build_number": 2, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\pycosat-0.6.6-py310h827c3e9_2", "files": ["Lib/site-packages/__pycache__/test_pycosat.cpython-310.pyc", "Lib/site-packages/pycosat-0.6.6.dist-info/AUTHORS.md", "Lib/site-packages/pycosat-0.6.6.dist-info/INSTALLER", "Lib/site-packages/pycosat-0.6.6.dist-info/LICENSE", "Lib/site-packages/pycosat-0.6.6.dist-info/METADATA", "Lib/site-packages/pycosat-0.6.6.dist-info/RECORD", "Lib/site-packages/pycosat-0.6.6.dist-info/REQUESTED", "Lib/site-packages/pycosat-0.6.6.dist-info/WHEEL", "Lib/site-packages/pycosat-0.6.6.dist-info/direct_url.json", "Lib/site-packages/pycosat-0.6.6.dist-info/top_level.txt", "Lib/site-packages/pycosat.cp310-win_amd64.pyd", "Lib/site-packages/test_pycosat.py"], "fn": "pycosat-0.6.6-py310h827c3e9_2.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\pycosat-0.6.6-py310h827c3e9_2", "type": 1}, "md5": "91f606ab93b5e83b88411be5ffcec82f", "name": "pycosat", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\pycosat-0.6.6-py310h827c3e9_2.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::pycosat==0.6.6=py310h827c3e9_2[md5=91f606ab93b5e83b88411be5ffcec82f]", "sha256": "64c988487f7023ee2a4cf33d3ae0c2abee853cb101bfd265c56a8860fe965b72", "size": 89725, "subdir": "win-64", "timestamp": 1736868819000, "url": "https://repo.anaconda.com/pkgs/main/win-64/pycosat-0.6.6-py310h827c3e9_2.conda", "version": "0.6.6"}