<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>SanitizerApi: Sanitizer Barrier API</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Sanitizer Barrier API</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__SANITIZER__BARRIER__API.html#gc8aefc64ba0d4bb0d51330c3a27f7e02">sanitizerGetCudaBarrierCount</a> (CUfunction kernel, uint32_t *numBarriers)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get number of CUDA barriers used by a function.  <a href="#gc8aefc64ba0d4bb0d51330c3a27f7e02"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Functions, types, and enums that implement the Sanitizer Barrier API. <hr><h2>Function Documentation</h2>
<a class="anchor" name="gc8aefc64ba0d4bb0d51330c3a27f7e02"></a><!-- doxytag: member="sanitizer_barrier.h::sanitizerGetCudaBarrierCount" ref="gc8aefc64ba0d4bb0d51330c3a27f7e02" args="(CUfunction kernel, uint32_t *numBarriers)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__SANITIZER__RESULT__API.html#g8edf13e06b1b4001d7577b07ddd575d8">SanitizerResult</a> SANITIZERAPI sanitizerGetCudaBarrierCount           </td>
          <td>(</td>
          <td class="paramtype">CUfunction&nbsp;</td>
          <td class="paramname"> <em>kernel</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&nbsp;</td>
          <td class="paramname"> <em>numBarriers</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The module where <code>kernel</code> resides must have been instrumented using <a class="el" href="group__SANITIZER__PATCHING__API.html#g2e7500a0e365b2ddf6c273c87a34b22d">sanitizerPatchModule</a> prior to calling this function. This function is only available for modules built with nvcc 11.2 or newer, it will return 0 otherwise.<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd><b>Thread-safety</b>: this function is thread safe.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>kernel</em>&nbsp;</td><td>CUDA function </td></tr>
    <tr><td valign="top"><tt>[out]</tt>&nbsp;</td><td valign="top"><em>numBarriers</em>&nbsp;</td><td>Number of CUDA barriers in the input CUDA function </td></tr>
  </table>
</dl>

</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Tue Jan 24 19:43:08 2023 for SanitizerApi by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
