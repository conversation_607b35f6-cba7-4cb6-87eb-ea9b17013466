<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_FlushCounterData_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_FlushCounterData_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_FlushCounterData_Params" -->Params for cuptiProfilerFlushCounterData.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="cfe11e827beb601c75d2c05852b50a11"></a><!-- doxytag: member="CUpti_Profiler_FlushCounterData_Params::ctx" ref="cfe11e827beb601c75d2c05852b50a11" args="" -->
CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#cfe11e827beb601c75d2c05852b50a11">ctx</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] if NULL, the current CUcontext is used <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ea9923fecff1ec2eec224cd8516cd474"></a><!-- doxytag: member="CUpti_Profiler_FlushCounterData_Params::numRangesDropped" ref="ea9923fecff1ec2eec224cd8516cd474" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#ea9923fecff1ec2eec224cd8516cd474">numRangesDropped</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] number of ranges whose data was dropped in the processed passes <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ed6bc209584c89a19ca2dab4929da3eb"></a><!-- doxytag: member="CUpti_Profiler_FlushCounterData_Params::numTraceBytesDropped" ref="ed6bc209584c89a19ca2dab4929da3eb" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#ed6bc209584c89a19ca2dab4929da3eb">numTraceBytesDropped</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] number of bytes not written to TraceBuffer due to buffer full <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ea3f2437a4758ec15d67e48be03679fa"></a><!-- doxytag: member="CUpti_Profiler_FlushCounterData_Params::pPriv" ref="ea3f2437a4758ec15d67e48be03679fa" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#ea3f2437a4758ec15d67e48be03679fa">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="3e792ad6cddaad1013adbba15221a775"></a><!-- doxytag: member="CUpti_Profiler_FlushCounterData_Params::structSize" ref="3e792ad6cddaad1013adbba15221a775" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__FlushCounterData__Params.html#3e792ad6cddaad1013adbba15221a775">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_FlushCounterData_Params_STRUCT_SIZE <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
