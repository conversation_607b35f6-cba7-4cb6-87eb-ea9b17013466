<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemset4 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemset4 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemset4" -->The activity record for memset.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#5eb93979694d690dca7529eb58eea735">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#74834306366b5e925f5e9d0cf0ec28a1">channelID</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUpti_ChannelType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#c7686745e86c9ae6bbf9e94830e24cd1">channelType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#8e857e1dc2bbd7f76a8040ba2f0747e3">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#613ca4ac2e8b2152f800e43338a858ef">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#dcdafe0a786b1f7828a006b1f2018ecb">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#7fb783c0f89817f2532bcf2ebcdfef22">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#37150e683fdc637c337a63870fb480e9">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#4f3432b90fe2cbf664fd70aa61513915">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#cc6c09cb4c608a2a4565f115d64c9701">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#e6f965734765758685d09445a9e27a11">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#e623e7cd202366cb171734f16247eafc">memoryKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#ad64c4f5b576f7a813b2216795d4fb18">pad2</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#1fa538036ffcb4127260be49ef5293c1">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#8e8d739a94205fa8a063d6d667dae25f">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#c8f0a24899363870b6cfb1635f432a79">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset4.html#b6d130ed6f73f8f98f24a95a8318a4a3">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory set operation (CUPTI_ACTIVITY_KIND_MEMSET). <hr><h2>Field Documentation</h2>
<a class="anchor" name="5eb93979694d690dca7529eb58eea735"></a><!-- doxytag: member="CUpti_ActivityMemset4::bytes" ref="5eb93979694d690dca7529eb58eea735" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset4.html#5eb93979694d690dca7529eb58eea735">CUpti_ActivityMemset4::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes being set by the memory set. 
</div>
</div><p>
<a class="anchor" name="74834306366b5e925f5e9d0cf0ec28a1"></a><!-- doxytag: member="CUpti_ActivityMemset4::channelID" ref="74834306366b5e925f5e9d0cf0ec28a1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#74834306366b5e925f5e9d0cf0ec28a1">CUpti_ActivityMemset4::channelID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the HW channel on which the memory set is occuring. 
</div>
</div><p>
<a class="anchor" name="c7686745e86c9ae6bbf9e94830e24cd1"></a><!-- doxytag: member="CUpti_ActivityMemset4::channelType" ref="c7686745e86c9ae6bbf9e94830e24cd1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUpti_ChannelType <a class="el" href="structCUpti__ActivityMemset4.html#c7686745e86c9ae6bbf9e94830e24cd1">CUpti_ActivityMemset4::channelType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the channel 
</div>
</div><p>
<a class="anchor" name="8e857e1dc2bbd7f76a8040ba2f0747e3"></a><!-- doxytag: member="CUpti_ActivityMemset4::contextId" ref="8e857e1dc2bbd7f76a8040ba2f0747e3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#8e857e1dc2bbd7f76a8040ba2f0747e3">CUpti_ActivityMemset4::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="613ca4ac2e8b2152f800e43338a858ef"></a><!-- doxytag: member="CUpti_ActivityMemset4::correlationId" ref="613ca4ac2e8b2152f800e43338a858ef" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#613ca4ac2e8b2152f800e43338a858ef">CUpti_ActivityMemset4::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory set. Each memory set is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory set. 
</div>
</div><p>
<a class="anchor" name="dcdafe0a786b1f7828a006b1f2018ecb"></a><!-- doxytag: member="CUpti_ActivityMemset4::deviceId" ref="dcdafe0a786b1f7828a006b1f2018ecb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#dcdafe0a786b1f7828a006b1f2018ecb">CUpti_ActivityMemset4::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="7fb783c0f89817f2532bcf2ebcdfef22"></a><!-- doxytag: member="CUpti_ActivityMemset4::end" ref="7fb783c0f89817f2532bcf2ebcdfef22" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset4.html#7fb783c0f89817f2532bcf2ebcdfef22">CUpti_ActivityMemset4::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="37150e683fdc637c337a63870fb480e9"></a><!-- doxytag: member="CUpti_ActivityMemset4::flags" ref="37150e683fdc637c337a63870fb480e9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset4.html#37150e683fdc637c337a63870fb480e9">CUpti_ActivityMemset4::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memset. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="4f3432b90fe2cbf664fd70aa61513915"></a><!-- doxytag: member="CUpti_ActivityMemset4::graphId" ref="4f3432b90fe2cbf664fd70aa61513915" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#4f3432b90fe2cbf664fd70aa61513915">CUpti_ActivityMemset4::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that executed this memset through graph launch. This field will be 0 if the memset is not executed through graph launch. 
</div>
</div><p>
<a class="anchor" name="cc6c09cb4c608a2a4565f115d64c9701"></a><!-- doxytag: member="CUpti_ActivityMemset4::graphNodeId" ref="cc6c09cb4c608a2a4565f115d64c9701" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset4.html#cc6c09cb4c608a2a4565f115d64c9701">CUpti_ActivityMemset4::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed this memset through graph launch. This field will be 0 if the memset is not executed through graph launch. 
</div>
</div><p>
<a class="anchor" name="e6f965734765758685d09445a9e27a11"></a><!-- doxytag: member="CUpti_ActivityMemset4::kind" ref="e6f965734765758685d09445a9e27a11" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemset4.html#e6f965734765758685d09445a9e27a11">CUpti_ActivityMemset4::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMSET. 
</div>
</div><p>
<a class="anchor" name="e623e7cd202366cb171734f16247eafc"></a><!-- doxytag: member="CUpti_ActivityMemset4::memoryKind" ref="e623e7cd202366cb171734f16247eafc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset4.html#e623e7cd202366cb171734f16247eafc">CUpti_ActivityMemset4::memoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory kind of the memory set <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="ad64c4f5b576f7a813b2216795d4fb18"></a><!-- doxytag: member="CUpti_ActivityMemset4::pad2" ref="ad64c4f5b576f7a813b2216795d4fb18" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#ad64c4f5b576f7a813b2216795d4fb18">CUpti_ActivityMemset4::pad2</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use 
</div>
</div><p>
<a class="anchor" name="1fa538036ffcb4127260be49ef5293c1"></a><!-- doxytag: member="CUpti_ActivityMemset4::reserved0" ref="1fa538036ffcb4127260be49ef5293c1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemset4.html#1fa538036ffcb4127260be49ef5293c1">CUpti_ActivityMemset4::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="8e8d739a94205fa8a063d6d667dae25f"></a><!-- doxytag: member="CUpti_ActivityMemset4::start" ref="8e8d739a94205fa8a063d6d667dae25f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset4.html#8e8d739a94205fa8a063d6d667dae25f">CUpti_ActivityMemset4::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="c8f0a24899363870b6cfb1635f432a79"></a><!-- doxytag: member="CUpti_ActivityMemset4::streamId" ref="c8f0a24899363870b6cfb1635f432a79" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#c8f0a24899363870b6cfb1635f432a79">CUpti_ActivityMemset4::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="b6d130ed6f73f8f98f24a95a8318a4a3"></a><!-- doxytag: member="CUpti_ActivityMemset4::value" ref="b6d130ed6f73f8f98f24a95a8318a4a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset4.html#b6d130ed6f73f8f98f24a95a8318a4a3">CUpti_ActivityMemset4::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The value being assigned to memory by the memory set. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
