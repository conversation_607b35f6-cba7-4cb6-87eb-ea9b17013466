<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityNvLink4 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityNvLink4 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityNvLink4" -->NVLink information.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#7337ec9bd0628f8feeb14da5419c6fdb">bandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#41e344a24884357191daf2726c840681">flag</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#42f4991c32defb8dd85d73efc0069661">idDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#6f2cfb5493e4b010c72f41aa79c91552">idDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#782f9edb8c7f0336cddc2dae7b4288d1">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#c7984f64edaac13a46169e6703c72746">nvlinkVersion</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#945941009ff11011bccd4abfddbb2724">nvswitchConnected</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#e7330e44eea2b8aed71b1334884a3323">pad</a> [7]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#74731eaf480ee7cba996763720d1e469">physicalNvLinkCount</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#e2000b4e43a642fdf1f27ae31d0c17da">portDev0</a> [CUPTI_MAX_NVLINK_PORTS]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#cf91d7d9373ae84ad440401428f3156e">portDev1</a> [CUPTI_MAX_NVLINK_PORTS]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#045b345ced0bc553c82a7d48e994538c">typeDev0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#a0f6bf083a7f271984b7d1b9580144d8">typeDev1</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#948057b72263392a85b52e2780356335">domainId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityNvLink4.html#e06472f7c641d835ea89317bce872362">index</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This structure gives capabilities of each logical NVLink connection between two devices, gpu&lt;-&gt;gpu or gpu&lt;-&gt;CPU which can be used to understand the topology. <hr><h2>Field Documentation</h2>
<a class="anchor" name="7337ec9bd0628f8feeb14da5419c6fdb"></a><!-- doxytag: member="CUpti_ActivityNvLink4::bandwidth" ref="7337ec9bd0628f8feeb14da5419c6fdb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityNvLink4.html#7337ec9bd0628f8feeb14da5419c6fdb">CUpti_ActivityNvLink4::bandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Banwidth of NVLink in kbytes/sec 
</div>
</div><p>
<a class="anchor" name="948057b72263392a85b52e2780356335"></a><!-- doxytag: member="CUpti_ActivityNvLink4::domainId" ref="948057b72263392a85b52e2780356335" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink4.html#948057b72263392a85b52e2780356335">CUpti_ActivityNvLink4::domainId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Domain ID of NPU. On Linux, this can be queried using lspci. 
</div>
</div><p>
<a class="anchor" name="41e344a24884357191daf2726c840681"></a><!-- doxytag: member="CUpti_ActivityNvLink4::flag" ref="41e344a24884357191daf2726c840681" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink4.html#41e344a24884357191daf2726c840681">CUpti_ActivityNvLink4::flag</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flag gives capabilities of the link <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g819d686d23d2a3dfed9cc1d3c4ecb162" title="Link flags.">CUpti_LinkFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="42f4991c32defb8dd85d73efc0069661"></a><!-- doxytag: member="CUpti_ActivityNvLink4::idDev0" ref="42f4991c32defb8dd85d73efc0069661" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink4.html#42f4991c32defb8dd85d73efc0069661">CUpti_ActivityNvLink4::idDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev0 is CUPTI_DEV_TYPE_GPU, UUID for device 0. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev0 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="6f2cfb5493e4b010c72f41aa79c91552"></a><!-- doxytag: member="CUpti_ActivityNvLink4::idDev1" ref="6f2cfb5493e4b010c72f41aa79c91552" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityNvLink4.html#6f2cfb5493e4b010c72f41aa79c91552">CUpti_ActivityNvLink4::idDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
If typeDev1 is CUPTI_DEV_TYPE_GPU, UUID for device 1. <a class="el" href="structCUpti__ActivityDevice4.html">CUpti_ActivityDevice4</a>. If typeDev1 is CUPTI_DEV_TYPE_NPU, struct npu for NPU. 
</div>
</div><p>
<a class="anchor" name="e06472f7c641d835ea89317bce872362"></a><!-- doxytag: member="CUpti_ActivityNvLink4::index" ref="e06472f7c641d835ea89317bce872362" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink4.html#e06472f7c641d835ea89317bce872362">CUpti_ActivityNvLink4::index</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Index of the NPU. First index will always be zero. 
</div>
</div><p>
<a class="anchor" name="782f9edb8c7f0336cddc2dae7b4288d1"></a><!-- doxytag: member="CUpti_ActivityNvLink4::kind" ref="782f9edb8c7f0336cddc2dae7b4288d1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityNvLink4.html#782f9edb8c7f0336cddc2dae7b4288d1">CUpti_ActivityNvLink4::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_NVLINK. 
</div>
</div><p>
<a class="anchor" name="c7984f64edaac13a46169e6703c72746"></a><!-- doxytag: member="CUpti_ActivityNvLink4::nvlinkVersion" ref="c7984f64edaac13a46169e6703c72746" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink4.html#c7984f64edaac13a46169e6703c72746">CUpti_ActivityNvLink4::nvlinkVersion</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
NvLink version. 
</div>
</div><p>
<a class="anchor" name="945941009ff11011bccd4abfddbb2724"></a><!-- doxytag: member="CUpti_ActivityNvLink4::nvswitchConnected" ref="945941009ff11011bccd4abfddbb2724" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityNvLink4.html#945941009ff11011bccd4abfddbb2724">CUpti_ActivityNvLink4::nvswitchConnected</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
NVSwitch is connected as an intermediate node. 
</div>
</div><p>
<a class="anchor" name="e7330e44eea2b8aed71b1334884a3323"></a><!-- doxytag: member="CUpti_ActivityNvLink4::pad" ref="e7330e44eea2b8aed71b1334884a3323" args="[7]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityNvLink4.html#e7330e44eea2b8aed71b1334884a3323">CUpti_ActivityNvLink4::pad</a>[7]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. reserved for internal use 
</div>
</div><p>
<a class="anchor" name="74731eaf480ee7cba996763720d1e469"></a><!-- doxytag: member="CUpti_ActivityNvLink4::physicalNvLinkCount" ref="74731eaf480ee7cba996763720d1e469" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityNvLink4.html#74731eaf480ee7cba996763720d1e469">CUpti_ActivityNvLink4::physicalNvLinkCount</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of physical NVLinks present between two devices. 
</div>
</div><p>
<a class="anchor" name="e2000b4e43a642fdf1f27ae31d0c17da"></a><!-- doxytag: member="CUpti_ActivityNvLink4::portDev0" ref="e2000b4e43a642fdf1f27ae31d0c17da" args="[CUPTI_MAX_NVLINK_PORTS]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink4.html#e2000b4e43a642fdf1f27ae31d0c17da">CUpti_ActivityNvLink4::portDev0</a>[CUPTI_MAX_NVLINK_PORTS]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 32 NVLinks connected to device 0. If typeDev0 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="cf91d7d9373ae84ad440401428f3156e"></a><!-- doxytag: member="CUpti_ActivityNvLink4::portDev1" ref="cf91d7d9373ae84ad440401428f3156e" args="[CUPTI_MAX_NVLINK_PORTS]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int8_t <a class="el" href="structCUpti__ActivityNvLink4.html#cf91d7d9373ae84ad440401428f3156e">CUpti_ActivityNvLink4::portDev1</a>[CUPTI_MAX_NVLINK_PORTS]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Port numbers for maximum 32 NVLinks connected to device 1. If typeDev1 is CUPTI_DEV_TYPE_NPU, ignore this field. In case of invalid/unknown port number, this field will be set to value CUPTI_NVLINK_INVALID_PORT. This will be used to correlate the metric values to individual physical link and attribute traffic to the logical NVLink in the topology. 
</div>
</div><p>
<a class="anchor" name="045b345ced0bc553c82a7d48e994538c"></a><!-- doxytag: member="CUpti_ActivityNvLink4::typeDev0" ref="045b345ced0bc553c82a7d48e994538c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink4.html#045b345ced0bc553c82a7d48e994538c">CUpti_ActivityNvLink4::typeDev0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 0 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
<a class="anchor" name="a0f6bf083a7f271984b7d1b9580144d8"></a><!-- doxytag: member="CUpti_ActivityNvLink4::typeDev1" ref="a0f6bf083a7f271984b7d1b9580144d8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> <a class="el" href="structCUpti__ActivityNvLink4.html#a0f6bf083a7f271984b7d1b9580144d8">CUpti_ActivityNvLink4::typeDev1</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of device 1 <a class="el" href="group__CUPTI__ACTIVITY__API.html#gd67907716d28ba51253f08d3e0bd0dda">CUpti_DevType</a> 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
