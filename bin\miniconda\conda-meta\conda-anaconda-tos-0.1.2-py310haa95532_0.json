{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["conda >=24.11", "pydantic", "python >=3.10,<3.11.0a0", "rich"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-anaconda-tos-0.1.2-py310haa95532_0", "files": ["Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/INSTALLER", "Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/METADATA", "Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/RECORD", "Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/REQUESTED", "Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/WHEEL", "Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/direct_url.json", "Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/entry_points.txt", "Lib/site-packages/conda_anaconda_tos-0.1.2.dist-info/licenses/LICENSE", "Lib/site-packages/conda_anaconda_tos/__init__.py", "Lib/site-packages/conda_anaconda_tos/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/_version.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/api.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/local.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/models.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/path.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/plugin.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/__pycache__/remote.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/_version.py", "Lib/site-packages/conda_anaconda_tos/api.py", "Lib/site-packages/conda_anaconda_tos/console/__init__.py", "Lib/site-packages/conda_anaconda_tos/console/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/console/__pycache__/mappers.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/console/__pycache__/prompt.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/console/__pycache__/render.cpython-310.pyc", "Lib/site-packages/conda_anaconda_tos/console/mappers.py", "Lib/site-packages/conda_anaconda_tos/console/prompt.py", "Lib/site-packages/conda_anaconda_tos/console/render.py", "Lib/site-packages/conda_anaconda_tos/exceptions.py", "Lib/site-packages/conda_anaconda_tos/local.py", "Lib/site-packages/conda_anaconda_tos/models.py", "Lib/site-packages/conda_anaconda_tos/path.py", "Lib/site-packages/conda_anaconda_tos/plugin.py", "Lib/site-packages/conda_anaconda_tos/py.typed", "Lib/site-packages/conda_anaconda_tos/remote.py", "Scripts/.conda-anaconda-tos-pre-unlink.bat"], "fn": "conda-anaconda-tos-0.1.2-py310haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-anaconda-tos-0.1.2-py310haa95532_0", "type": 1}, "md5": "40f49add51f7faf0ef6ac18aa96c63d1", "name": "conda-anaconda-tos", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\conda-anaconda-tos-0.1.2-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::conda-anaconda-tos==0.1.2=py310haa95532_0[md5=40f49add51f7faf0ef6ac18aa96c63d1]", "sha256": "741bbf98c77bd242aaa941fef9630d1b2c58acb283a2681fe8511748f1b611ab", "size": 37194, "subdir": "win-64", "timestamp": 1739299095000, "url": "https://repo.anaconda.com/pkgs/main/win-64/conda-anaconda-tos-0.1.2-py310haa95532_0.conda", "version": "0.1.2"}