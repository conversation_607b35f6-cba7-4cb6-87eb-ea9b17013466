{"arch": "x86_64", "build": "h2bbff1b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\lz4-c-1.9.4-h2bbff1b_1", "files": ["Library/bin/liblz4.dll", "Library/bin/lz4.exe", "Library/include/lz4.h", "Library/include/lz4frame.h", "Library/include/lz4hc.h", "Library/lib/liblz4.lib"], "fn": "lz4-c-1.9.4-h2bbff1b_1.conda", "license": "BSD-2-<PERSON><PERSON>", "license_family": "BSD", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\lz4-c-1.9.4-h2bbff1b_1", "type": 1}, "md5": "723e3ccd4ef7c58ddbfeebe69abff662", "name": "lz4-c", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\lz4-c-1.9.4-h2bbff1b_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::lz4-c==1.9.4=h2bbff1b_1[md5=723e3ccd4ef7c58ddbfeebe69abff662]", "sha256": "0e848b5004a9dc7a06f459cc67c404d7fb0321bf134ec7512815afafe97960b4", "size": 155711, "subdir": "win-64", "timestamp": 1714512129000, "url": "https://repo.anaconda.com/pkgs/main/win-64/lz4-c-1.9.4-h2bbff1b_1.conda", "version": "1.9.4"}