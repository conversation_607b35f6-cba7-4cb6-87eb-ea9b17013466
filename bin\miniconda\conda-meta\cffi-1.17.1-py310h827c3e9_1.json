{"arch": "x86_64", "build": "py310h827c3e9_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["pyc<PERSON><PERSON>", "python >=3.10,<3.11.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cffi-1.17.1-py310h827c3e9_1", "files": ["Lib/site-packages/_cffi_backend.cp310-win_amd64.pyd", "Lib/site-packages/cffi-1.17.1.dist-info/INSTALLER", "Lib/site-packages/cffi-1.17.1.dist-info/LICENSE", "Lib/site-packages/cffi-1.17.1.dist-info/METADATA", "Lib/site-packages/cffi-1.17.1.dist-info/RECORD", "Lib/site-packages/cffi-1.17.1.dist-info/REQUESTED", "Lib/site-packages/cffi-1.17.1.dist-info/WHEEL", "Lib/site-packages/cffi-1.17.1.dist-info/direct_url.json", "Lib/site-packages/cffi-1.17.1.dist-info/entry_points.txt", "Lib/site-packages/cffi-1.17.1.dist-info/top_level.txt", "Lib/site-packages/cffi/__init__.py", "Lib/site-packages/cffi/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/_imp_emulation.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/_shimmed_dist_utils.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/api.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/backend_ctypes.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/cffi_opcode.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/commontypes.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/cparser.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/error.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/ffiplatform.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/lock.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/model.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/pkgconfig.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/recompiler.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/setuptools_ext.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/vengine_cpy.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/vengine_gen.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/verifier.cpython-310.pyc", "Lib/site-packages/cffi/_cffi_errors.h", "Lib/site-packages/cffi/_cffi_include.h", "Lib/site-packages/cffi/_embedding.h", "Lib/site-packages/cffi/_imp_emulation.py", "Lib/site-packages/cffi/_shimmed_dist_utils.py", "Lib/site-packages/cffi/api.py", "Lib/site-packages/cffi/backend_ctypes.py", "Lib/site-packages/cffi/cffi_opcode.py", "Lib/site-packages/cffi/commontypes.py", "Lib/site-packages/cffi/cparser.py", "Lib/site-packages/cffi/error.py", "Lib/site-packages/cffi/ffiplatform.py", "Lib/site-packages/cffi/lock.py", "Lib/site-packages/cffi/model.py", "Lib/site-packages/cffi/parse_c_type.h", "Lib/site-packages/cffi/pkgconfig.py", "Lib/site-packages/cffi/recompiler.py", "Lib/site-packages/cffi/setuptools_ext.py", "Lib/site-packages/cffi/vengine_cpy.py", "Lib/site-packages/cffi/vengine_gen.py", "Lib/site-packages/cffi/verifier.py"], "fn": "cffi-1.17.1-py310h827c3e9_1.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cffi-1.17.1-py310h827c3e9_1", "type": 1}, "md5": "3f38fd7bc085a8ea059461b6d7a77d9c", "name": "cffi", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cffi-1.17.1-py310h827c3e9_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::cffi==1.17.1=py310h827c3e9_1[md5=3f38fd7bc085a8ea059461b6d7a77d9c]", "sha256": "58336f1b6a7bb8109f12883de133e55e58fdceb76cc1b67a0c8e89ecc2013689", "size": 260472, "subdir": "win-64", "timestamp": 1736184518000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cffi-1.17.1-py310h827c3e9_1.conda", "version": "1.17.1"}