<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ModuleResourceData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ModuleResourceData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__CALLBACK__API.html">CUPTI Callback API</a>]</small>
</h1><!-- doxytag: class="CUpti_ModuleResourceData" -->Module data passed into a resource callback function.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ModuleResourceData.html#be33f366f20f4de770a8f71821c70175">cubinSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ModuleResourceData.html#783fac071da7924ac75739fec9a083c3">moduleId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ModuleResourceData.html#72a92bfe2127a8b0b245b41f5a85e9ff">pCubin</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
CUDA module data passed into a resource callback function as the <code>cbdata</code> argument to <a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a>. The <code>cbdata</code> will be this type for <code>domain</code> equal to CUPTI_CB_DOMAIN_RESOURCE. The module data is valid only within the invocation of the callback function that is passed the data. If you need to retain some data for use outside of the callback, you must make a copy of that data. <hr><h2>Field Documentation</h2>
<a class="anchor" name="be33f366f20f4de770a8f71821c70175"></a><!-- doxytag: member="CUpti_ModuleResourceData::cubinSize" ref="be33f366f20f4de770a8f71821c70175" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__ModuleResourceData.html#be33f366f20f4de770a8f71821c70175">CUpti_ModuleResourceData::cubinSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the cubin. 
</div>
</div><p>
<a class="anchor" name="783fac071da7924ac75739fec9a083c3"></a><!-- doxytag: member="CUpti_ModuleResourceData::moduleId" ref="783fac071da7924ac75739fec9a083c3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ModuleResourceData.html#783fac071da7924ac75739fec9a083c3">CUpti_ModuleResourceData::moduleId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Identifier to associate with the CUDA module. 
</div>
</div><p>
<a class="anchor" name="72a92bfe2127a8b0b245b41f5a85e9ff"></a><!-- doxytag: member="CUpti_ModuleResourceData::pCubin" ref="72a92bfe2127a8b0b245b41f5a85e9ff" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ModuleResourceData.html#72a92bfe2127a8b0b245b41f5a85e9ff">CUpti_ModuleResourceData::pCubin</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the associated cubin. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
