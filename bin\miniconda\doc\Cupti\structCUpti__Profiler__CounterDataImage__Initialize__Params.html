<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_CounterDataImage_Initialize_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_CounterDataImage_Initialize_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_CounterDataImage_Initialize_Params" -->Params for cuptiProfilerCounterDataImageInitialize.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="3134dc93a434d47f809effeeafd6cdb9"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_Initialize_Params::counterDataImageSize" ref="3134dc93a434d47f809effeeafd6cdb9" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#3134dc93a434d47f809effeeafd6cdb9">counterDataImageSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Size calculated from cuptiProfilerCounterDataImageCalculateSize <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="4b9e1301f3ea3313ba96a5b39eff7386"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_Initialize_Params::pCounterDataImage" ref="4b9e1301f3ea3313ba96a5b39eff7386" args="" -->
uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#4b9e1301f3ea3313ba96a5b39eff7386">pCounterDataImage</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] The buffer to be initialized. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="85695d03e22a0a634575d04d5d2b4556"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_Initialize_Params::pOptions" ref="85695d03e22a0a634575d04d5d2b4556" args="" -->
const <br class="typebreak">
<a class="el" href="structCUpti__Profiler__CounterDataImageOptions.html">CUpti_Profiler_CounterDataImageOptions</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#85695d03e22a0a634575d04d5d2b4556">pOptions</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] Pointer to Counter Data Image Options <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="fb9de5ab2e7fe1bef000b829e36963ae"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_Initialize_Params::pPriv" ref="fb9de5ab2e7fe1bef000b829e36963ae" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#fb9de5ab2e7fe1bef000b829e36963ae">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="f48cebdffa5473f56bd6ffaaf88e45e1"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_Initialize_Params::sizeofCounterDataImageOptions" ref="f48cebdffa5473f56bd6ffaaf88e45e1" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#f48cebdffa5473f56bd6ffaaf88e45e1">sizeofCounterDataImageOptions</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_CounterDataImageOptions_STRUCT_SIZE <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="1de1b7b170fb76a18674b900290a2e25"></a><!-- doxytag: member="CUpti_Profiler_CounterDataImage_Initialize_Params::structSize" ref="1de1b7b170fb76a18674b900290a2e25" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__CounterDataImage__Initialize__Params.html#1de1b7b170fb76a18674b900290a2e25">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_CounterDataImage_Initialize_Params_STRUCT_SIZE <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
