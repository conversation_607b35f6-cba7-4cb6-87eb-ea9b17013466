<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_PCSamplingGetNumStallReasonsParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_PCSamplingGetNumStallReasonsParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__API.html">CUPTI PC Sampling API</a>]</small>
</h1><!-- doxytag: class="CUpti_PCSamplingGetNumStallReasonsParams" -->Params for cuptiPCSamplingGetNumStallReasons.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#188817a75a763bcbd74c9b07520ca692">ctx</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#9f4cd2aac4b681aa3d3a9641fc916355">numStallReasons</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#f4cadb3a732398d544e27ae6a039c296">pPriv</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#b6b0ae8b6d0450e402b2d6e365e63a85">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="188817a75a763bcbd74c9b07520ca692"></a><!-- doxytag: member="CUpti_PCSamplingGetNumStallReasonsParams::ctx" ref="188817a75a763bcbd74c9b07520ca692" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#188817a75a763bcbd74c9b07520ca692">CUpti_PCSamplingGetNumStallReasonsParams::ctx</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] CUcontext 
</div>
</div><p>
<a class="anchor" name="9f4cd2aac4b681aa3d3a9641fc916355"></a><!-- doxytag: member="CUpti_PCSamplingGetNumStallReasonsParams::numStallReasons" ref="9f4cd2aac4b681aa3d3a9641fc916355" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t* <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#9f4cd2aac4b681aa3d3a9641fc916355">CUpti_PCSamplingGetNumStallReasonsParams::numStallReasons</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[r] Number of stall reasons 
</div>
</div><p>
<a class="anchor" name="f4cadb3a732398d544e27ae6a039c296"></a><!-- doxytag: member="CUpti_PCSamplingGetNumStallReasonsParams::pPriv" ref="f4cadb3a732398d544e27ae6a039c296" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#f4cadb3a732398d544e27ae6a039c296">CUpti_PCSamplingGetNumStallReasonsParams::pPriv</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Assign to NULL 
</div>
</div><p>
<a class="anchor" name="b6b0ae8b6d0450e402b2d6e365e63a85"></a><!-- doxytag: member="CUpti_PCSamplingGetNumStallReasonsParams::size" ref="b6b0ae8b6d0450e402b2d6e365e63a85" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__PCSamplingGetNumStallReasonsParams.html#b6b0ae8b6d0450e402b2d6e365e63a85">CUpti_PCSamplingGetNumStallReasonsParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[w] Size of the data structure i.e. CUpti_PCSamplingGetNumStallReasonsParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
