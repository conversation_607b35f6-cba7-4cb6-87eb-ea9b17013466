{"build": "pyhd3eb1b0_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.7"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\charset-normalizer-3.3.2-pyhd3eb1b0_0", "files": ["Lib/site-packages/charset_normalizer-3.3.2.dist-info/INSTALLER", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/LICENSE", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/METADATA", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/RECORD", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/REQUESTED", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/WHEEL", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/direct_url.json", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/entry_points.txt", "Lib/site-packages/charset_normalizer-3.3.2.dist-info/top_level.txt", "Lib/site-packages/charset_normalizer/__init__.py", "Lib/site-packages/charset_normalizer/__main__.py", "Lib/site-packages/charset_normalizer/api.py", "Lib/site-packages/charset_normalizer/cd.py", "Lib/site-packages/charset_normalizer/cli/__init__.py", "Lib/site-packages/charset_normalizer/cli/__main__.py", "Lib/site-packages/charset_normalizer/constant.py", "Lib/site-packages/charset_normalizer/legacy.py", "Lib/site-packages/charset_normalizer/md.py", "Lib/site-packages/charset_normalizer/models.py", "Lib/site-packages/charset_normalizer/py.typed", "Lib/site-packages/charset_normalizer/utils.py", "Lib/site-packages/charset_normalizer/version.py", "Lib/site-packages/charset_normalizer/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/api.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/cd.cpython-310.pyc", "Lib/site-packages/charset_normalizer/cli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/charset_normalizer/cli/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/constant.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/legacy.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/md.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/models.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/charset_normalizer/__pycache__/version.cpython-310.pyc", "Scripts/normalizer-script.py", "Scripts/normalizer.exe"], "fn": "charset-normalizer-3.3.2-pyhd3eb1b0_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\charset-normalizer-3.3.2-pyhd3eb1b0_0", "type": 1}, "md5": "c6fea3691e85cf7f568b0618ec29fc4f", "name": "charset-normalizer", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\charset-normalizer-3.3.2-pyhd3eb1b0_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "Lib/site-packages/charset_normalizer/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/cd.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/cli/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/cli/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/constant.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/legacy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/md.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/models.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/charset_normalizer/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Scripts/normalizer-script.py", "path_type": "windows_python_entry_point_script"}], "paths_version": 1}, "requested_spec": "defaults/noarch::charset-normalizer==3.3.2=pyhd3eb1b0_0[md5=c6fea3691e85cf7f568b0618ec29fc4f]", "sha256": "1cdc690b822b7518ab3b73c06115f6b95037ff111378e6d2e6508c4602df558e", "size": 45535, "subdir": "noarch", "timestamp": 1721748373000, "url": "https://repo.anaconda.com/pkgs/main/noarch/charset-normalizer-3.3.2-pyhd3eb1b0_0.conda", "version": "3.3.2"}