//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_CTIME
#define _CUDA_CTIME

#ifndef __CUDACC_RTC__
    #include <time.h>
#else
    typedef long long int time_t;
#endif

#include "detail/__config"

#include "detail/__pragma_push"

#include "detail/libcxx/include/ctime"

#include "detail/__pragma_pop"

#endif //_CUDA_CTIME
