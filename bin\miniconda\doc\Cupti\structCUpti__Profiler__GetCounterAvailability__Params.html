<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_GetCounterAvailability_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_GetCounterAvailability_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_GetCounterAvailability_Params" -->Params for cuptiProfilerGetCounterAvailability.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#dcbe4af6b1837d337ac83cc37798805f">counterAvailabilityImageSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="7f83c9050db37e01bd25ca3da63d5b69"></a><!-- doxytag: member="CUpti_Profiler_GetCounterAvailability_Params::ctx" ref="7f83c9050db37e01bd25ca3da63d5b69" args="" -->
CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#7f83c9050db37e01bd25ca3da63d5b69">ctx</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] if NULL, the current CUcontext is used <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ffdbb99799566a7989e07fa4bf2c19be"></a><!-- doxytag: member="CUpti_Profiler_GetCounterAvailability_Params::pCounterAvailabilityImage" ref="ffdbb99799566a7989e07fa4bf2c19be" args="" -->
uint8_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#ffdbb99799566a7989e07fa4bf2c19be">pCounterAvailabilityImage</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] buffer receiving counter availability image, may be NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="a222659b0936959904f70e3249aafb81"></a><!-- doxytag: member="CUpti_Profiler_GetCounterAvailability_Params::pPriv" ref="a222659b0936959904f70e3249aafb81" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#a222659b0936959904f70e3249aafb81">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="a23ec684bdbe398cc2d98fa082ba6fd9"></a><!-- doxytag: member="CUpti_Profiler_GetCounterAvailability_Params::structSize" ref="a23ec684bdbe398cc2d98fa082ba6fd9" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#a23ec684bdbe398cc2d98fa082ba6fd9">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_GetCounterAvailability_Params_STRUCT_SIZE <br></td></tr>
</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="dcbe4af6b1837d337ac83cc37798805f"></a><!-- doxytag: member="CUpti_Profiler_GetCounterAvailability_Params::counterAvailabilityImageSize" ref="dcbe4af6b1837d337ac83cc37798805f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__Profiler__GetCounterAvailability__Params.html#dcbe4af6b1837d337ac83cc37798805f">CUpti_Profiler_GetCounterAvailability_Params::counterAvailabilityImageSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
[in/out] If `pCounterAvailabilityImage` is NULL, then the required size is returned in `counterAvailabilityImageSize`, otherwise `counterAvailabilityImageSize` should be set to the size of `pCounterAvailabilityImage`, and on return it would be overwritten with number of actual bytes copied 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
