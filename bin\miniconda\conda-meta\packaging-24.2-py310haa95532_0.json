{"arch": "x86_64", "build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\packaging-24.2-py310haa95532_0", "files": ["Lib/site-packages/packaging-24.2.dist-info/INSTALLER", "Lib/site-packages/packaging-24.2.dist-info/LICENSE", "Lib/site-packages/packaging-24.2.dist-info/LICENSE.APACHE", "Lib/site-packages/packaging-24.2.dist-info/LICENSE.BSD", "Lib/site-packages/packaging-24.2.dist-info/METADATA", "Lib/site-packages/packaging-24.2.dist-info/RECORD", "Lib/site-packages/packaging-24.2.dist-info/REQUESTED", "Lib/site-packages/packaging-24.2.dist-info/WHEEL", "Lib/site-packages/packaging-24.2.dist-info/direct_url.json", "Lib/site-packages/packaging/__init__.py", "Lib/site-packages/packaging/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/_elffile.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/_manylinux.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/_musllinux.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/_structures.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/_tokenizer.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/specifiers.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/packaging/__pycache__/version.cpython-310.pyc", "Lib/site-packages/packaging/_elffile.py", "Lib/site-packages/packaging/_manylinux.py", "Lib/site-packages/packaging/_musllinux.py", "Lib/site-packages/packaging/_parser.py", "Lib/site-packages/packaging/_structures.py", "Lib/site-packages/packaging/_tokenizer.py", "Lib/site-packages/packaging/licenses/__init__.py", "Lib/site-packages/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "Lib/site-packages/packaging/licenses/_spdx.py", "Lib/site-packages/packaging/markers.py", "Lib/site-packages/packaging/metadata.py", "Lib/site-packages/packaging/py.typed", "Lib/site-packages/packaging/requirements.py", "Lib/site-packages/packaging/specifiers.py", "Lib/site-packages/packaging/tags.py", "Lib/site-packages/packaging/utils.py", "Lib/site-packages/packaging/version.py"], "fn": "packaging-24.2-py310haa95532_0.conda", "license": "Apache-2.0 or BSD-2-<PERSON><PERSON>", "license_family": "Apache", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\packaging-24.2-py310haa95532_0", "type": 1}, "md5": "bb570249fe927e5c102fdb16e82963b8", "name": "packaging", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\packaging-24.2-py310haa95532_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::packaging==24.2=py310haa95532_0[md5=bb570249fe927e5c102fdb16e82963b8]", "sha256": "c27e66487b8edecde7b962194a4a46bf110b8d0dbcb37e059a66eb9dee9b2312", "size": 178964, "subdir": "win-64", "timestamp": 1734473272000, "url": "https://repo.anaconda.com/pkgs/main/win-64/packaging-24.2-py310haa95532_0.conda", "version": "24.2"}