<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>SanitizerApi: Alphabetical List</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Data Structure Index</h1><p><div class="qindex"><a class="qindex" href="#letter_S">S</a></div><p>
<table align="center" width="95%" border="0" cellspacing="0" cellpadding="0">
<tr><td><a name="letter_S"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&nbsp;&nbsp;S&nbsp;&nbsp;</div></td></tr></table>
</td><td><a class="el" href="structSanitizer__GraphExecData.html">Sanitizer_GraphExecData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__MemcpyData.html">Sanitizer_MemcpyData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__ResourceFunctionsLazyLoadedData.html">Sanitizer_ResourceFunctionsLazyLoadedData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__ResourceStreamData.html">Sanitizer_ResourceStreamData</a>&nbsp;&nbsp;&nbsp;</td></tr><tr><td><a class="el" href="structSanitizer__BatchMemopData.html">Sanitizer_BatchMemopData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__GraphLaunchData.html">Sanitizer_GraphLaunchData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__MemsetData.html">Sanitizer_MemsetData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__ResourceMemoryData.html">Sanitizer_ResourceMemoryData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__SynchronizeData.html">Sanitizer_SynchronizeData</a>&nbsp;&nbsp;&nbsp;</td></tr><tr><td><a class="el" href="structSanitizer__CallbackData.html">Sanitizer_CallbackData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__GraphNodeLaunchData.html">Sanitizer_GraphNodeLaunchData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__ResourceArrayData.html">Sanitizer_ResourceArrayData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__ResourceMempoolData.html">Sanitizer_ResourceMempoolData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__UvmData.html">Sanitizer_UvmData</a>&nbsp;&nbsp;&nbsp;</td></tr><tr><td><a class="el" href="structSanitizer__EventData.html">Sanitizer_EventData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__LaunchData.html">Sanitizer_LaunchData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__ResourceContextData.html">Sanitizer_ResourceContextData</a>&nbsp;&nbsp;&nbsp;</td><td><a class="el" href="structSanitizer__ResourceModuleData.html">Sanitizer_ResourceModuleData</a>&nbsp;&nbsp;&nbsp;</td></tr></table><p><div class="qindex"><a class="qindex" href="#letter_S">S</a></div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Tue Jan 24 19:43:08 2023 for SanitizerApi by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
