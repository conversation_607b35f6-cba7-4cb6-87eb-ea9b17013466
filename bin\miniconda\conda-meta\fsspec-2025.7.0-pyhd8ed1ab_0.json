{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\fsspec-2025.7.0-pyhd8ed1ab_0", "features": "", "files": ["Lib/site-packages/fsspec-2025.7.0.dist-info/INSTALLER", "Lib/site-packages/fsspec-2025.7.0.dist-info/METADATA", "Lib/site-packages/fsspec-2025.7.0.dist-info/RECORD", "Lib/site-packages/fsspec-2025.7.0.dist-info/REQUESTED", "Lib/site-packages/fsspec-2025.7.0.dist-info/WHEEL", "Lib/site-packages/fsspec-2025.7.0.dist-info/direct_url.json", "Lib/site-packages/fsspec-2025.7.0.dist-info/licenses/LICENSE", "Lib/site-packages/fsspec/__init__.py", "Lib/site-packages/fsspec/_version.py", "Lib/site-packages/fsspec/archive.py", "Lib/site-packages/fsspec/asyn.py", "Lib/site-packages/fsspec/caching.py", "Lib/site-packages/fsspec/callbacks.py", "Lib/site-packages/fsspec/compression.py", "Lib/site-packages/fsspec/config.py", "Lib/site-packages/fsspec/conftest.py", "Lib/site-packages/fsspec/core.py", "Lib/site-packages/fsspec/dircache.py", "Lib/site-packages/fsspec/exceptions.py", "Lib/site-packages/fsspec/fuse.py", "Lib/site-packages/fsspec/generic.py", "Lib/site-packages/fsspec/gui.py", "Lib/site-packages/fsspec/implementations/__init__.py", "Lib/site-packages/fsspec/implementations/arrow.py", "Lib/site-packages/fsspec/implementations/asyn_wrapper.py", "Lib/site-packages/fsspec/implementations/cache_mapper.py", "Lib/site-packages/fsspec/implementations/cache_metadata.py", "Lib/site-packages/fsspec/implementations/cached.py", "Lib/site-packages/fsspec/implementations/dask.py", "Lib/site-packages/fsspec/implementations/data.py", "Lib/site-packages/fsspec/implementations/dbfs.py", "Lib/site-packages/fsspec/implementations/dirfs.py", "Lib/site-packages/fsspec/implementations/ftp.py", "Lib/site-packages/fsspec/implementations/gist.py", "Lib/site-packages/fsspec/implementations/git.py", "Lib/site-packages/fsspec/implementations/github.py", "Lib/site-packages/fsspec/implementations/http.py", "Lib/site-packages/fsspec/implementations/http_sync.py", "Lib/site-packages/fsspec/implementations/jupyter.py", "Lib/site-packages/fsspec/implementations/libarchive.py", "Lib/site-packages/fsspec/implementations/local.py", "Lib/site-packages/fsspec/implementations/memory.py", "Lib/site-packages/fsspec/implementations/reference.py", "Lib/site-packages/fsspec/implementations/sftp.py", "Lib/site-packages/fsspec/implementations/smb.py", "Lib/site-packages/fsspec/implementations/tar.py", "Lib/site-packages/fsspec/implementations/webhdfs.py", "Lib/site-packages/fsspec/implementations/zip.py", "Lib/site-packages/fsspec/json.py", "Lib/site-packages/fsspec/mapping.py", "Lib/site-packages/fsspec/parquet.py", "Lib/site-packages/fsspec/registry.py", "Lib/site-packages/fsspec/spec.py", "Lib/site-packages/fsspec/tests/abstract/__init__.py", "Lib/site-packages/fsspec/tests/abstract/common.py", "Lib/site-packages/fsspec/tests/abstract/copy.py", "Lib/site-packages/fsspec/tests/abstract/get.py", "Lib/site-packages/fsspec/tests/abstract/mv.py", "Lib/site-packages/fsspec/tests/abstract/open.py", "Lib/site-packages/fsspec/tests/abstract/pipe.py", "Lib/site-packages/fsspec/tests/abstract/put.py", "Lib/site-packages/fsspec/transaction.py", "Lib/site-packages/fsspec/utils.py", "Lib/site-packages/fsspec/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/_version.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/archive.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/asyn.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/caching.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/callbacks.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/compression.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/config.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/conftest.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/core.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/dircache.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/fuse.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/generic.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/gui.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/arrow.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/asyn_wrapper.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/cache_mapper.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/cache_metadata.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/cached.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/dask.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/data.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/dbfs.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/dirfs.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/ftp.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/gist.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/git.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/github.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/http.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/http_sync.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/jupyter.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/libarchive.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/local.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/memory.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/reference.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/sftp.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/smb.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/tar.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/webhdfs.cpython-310.pyc", "Lib/site-packages/fsspec/implementations/__pycache__/zip.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/json.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/mapping.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/parquet.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/registry.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/spec.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/common.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/copy.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/get.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/mv.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/open.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/pipe.cpython-310.pyc", "Lib/site-packages/fsspec/tests/abstract/__pycache__/put.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/transaction.cpython-310.pyc", "Lib/site-packages/fsspec/__pycache__/utils.cpython-310.pyc"], "fn": "fsspec-2025.7.0-pyhd8ed1ab_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\fsspec-2025.7.0-pyhd8ed1ab_0", "type": 1}, "md5": "a31ce802cd0ebfce298f342c02757019", "name": "fsspec", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\fsspec-2025.7.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/fsspec-2025.7.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/fsspec-2025.7.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "7cf1214db37ac22e8a3b3dc892225070e4d8bd05d5123cd3bfd833c97f8a4472", "sha256_in_prefix": "7cf1214db37ac22e8a3b3dc892225070e4d8bd05d5123cd3bfd833c97f8a4472", "size_in_bytes": 12161}, {"_path": "site-packages/fsspec-2025.7.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "a9f2ed8997df7b6adcbe8d575b077a38eaca7ef32263271f51e49a29f9ae8e82", "sha256_in_prefix": "a9f2ed8997df7b6adcbe8d575b077a38eaca7ef32263271f51e49a29f9ae8e82", "size_in_bytes": 8309}, {"_path": "site-packages/fsspec-2025.7.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/fsspec-2025.7.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/fsspec-2025.7.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "20a312f0ddfb18d240ad55f95bcc5e8c1d0dec3c5a9d86e0b1116c78eecaddc5", "sha256_in_prefix": "20a312f0ddfb18d240ad55f95bcc5e8c1d0dec3c5a9d86e0b1116c78eecaddc5", "size_in_bytes": 102}, {"_path": "site-packages/fsspec-2025.7.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "2dc35496ce53a7307915c008a844aad53e772b49b34cdd00445067691407ec94", "sha256_in_prefix": "2dc35496ce53a7307915c008a844aad53e772b49b34cdd00445067691407ec94", "size_in_bytes": 1513}, {"_path": "site-packages/fsspec/__init__.py", "path_type": "hardlink", "sha256": "2fbab034153588c35077c39ff3b1d848d153f605a534c48449a242f1f63401a4", "sha256_in_prefix": "2fbab034153588c35077c39ff3b1d848d153f605a534c48449a242f1f63401a4", "size_in_bytes": 2053}, {"_path": "site-packages/fsspec/_version.py", "path_type": "hardlink", "sha256": "071b6484149b3f603d67da4b0a835293f97a373698f520caf5d9a30e02173b9e", "sha256_in_prefix": "071b6484149b3f603d67da4b0a835293f97a373698f520caf5d9a30e02173b9e", "size_in_bytes": 517}, {"_path": "site-packages/fsspec/archive.py", "path_type": "hardlink", "sha256": "bcceadfe5815ea50566c1630a66dd2e287c140543150faf92a40d0aeb41c42ba", "sha256_in_prefix": "bcceadfe5815ea50566c1630a66dd2e287c140543150faf92a40d0aeb41c42ba", "size_in_bytes": 2411}, {"_path": "site-packages/fsspec/asyn.py", "path_type": "hardlink", "sha256": "984e79b4efcc9867310f5e1c52e6a24b7bde02aa3487a66a00d7e752e08ddec9", "sha256_in_prefix": "984e79b4efcc9867310f5e1c52e6a24b7bde02aa3487a66a00d7e752e08ddec9", "size_in_bytes": 36365}, {"_path": "site-packages/fsspec/caching.py", "path_type": "hardlink", "sha256": "f3ab9280f6b9139e5bdbc5c486e0be74c70a03126d6599e942a9c74f0685de12", "sha256_in_prefix": "f3ab9280f6b9139e5bdbc5c486e0be74c70a03126d6599e942a9c74f0685de12", "size_in_bytes": 34294}, {"_path": "site-packages/fsspec/callbacks.py", "path_type": "hardlink", "sha256": "0432302f32baaebff4579721e79edf4b3b22bc2125a5daa15ebe5d67d4def841", "sha256_in_prefix": "0432302f32baaebff4579721e79edf4b3b22bc2125a5daa15ebe5d67d4def841", "size_in_bytes": 9210}, {"_path": "site-packages/fsspec/compression.py", "path_type": "hardlink", "sha256": "8012b6315fe84c5556d970eaf1b6556d840a62b97a243528bbaffe932be6c6e9", "sha256_in_prefix": "8012b6315fe84c5556d970eaf1b6556d840a62b97a243528bbaffe932be6c6e9", "size_in_bytes": 5086}, {"_path": "site-packages/fsspec/config.py", "path_type": "hardlink", "sha256": "2c5e199aed6f8495bb25ef50f9cc2445cdf13fb461cb2ed79f08f6e99eacbf68", "sha256_in_prefix": "2c5e199aed6f8495bb25ef50f9cc2445cdf13fb461cb2ed79f08f6e99eacbf68", "size_in_bytes": 4279}, {"_path": "site-packages/fsspec/conftest.py", "path_type": "hardlink", "sha256": "7d57f1f8d2eb1ff3994b54c8a583683f333b79f11c3282fadab78739d61e1420", "sha256_in_prefix": "7d57f1f8d2eb1ff3994b54c8a583683f333b79f11c3282fadab78739d61e1420", "size_in_bytes": 1245}, {"_path": "site-packages/fsspec/core.py", "path_type": "hardlink", "sha256": "d6d2dcb70afbb05d553b776373f5248e127c200132d1350c1ff6dbd3b4b0d7b1", "sha256_in_prefix": "d6d2dcb70afbb05d553b776373f5248e127c200132d1350c1ff6dbd3b4b0d7b1", "size_in_bytes": 23828}, {"_path": "site-packages/fsspec/dircache.py", "path_type": "hardlink", "sha256": "633a20589ae111ab2d1d4eef5b3f9c26227bb1db4b5c55e112922718a77811c3", "sha256_in_prefix": "633a20589ae111ab2d1d4eef5b3f9c26227bb1db4b5c55e112922718a77811c3", "size_in_bytes": 2717}, {"_path": "site-packages/fsspec/exceptions.py", "path_type": "hardlink", "sha256": "a5ab922c3331cd324c3a3bd7d561142b470cc8592b171a5626cc85cb06afec0f", "sha256_in_prefix": "a5ab922c3331cd324c3a3bd7d561142b470cc8592b171a5626cc85cb06afec0f", "size_in_bytes": 331}, {"_path": "site-packages/fsspec/fuse.py", "path_type": "hardlink", "sha256": "43edcd38ec8ba817d86b80dbe44d7dcff658dfacf3607b48b3598e51ab08b414", "sha256_in_prefix": "43edcd38ec8ba817d86b80dbe44d7dcff658dfacf3607b48b3598e51ab08b414", "size_in_bytes": 10177}, {"_path": "site-packages/fsspec/generic.py", "path_type": "hardlink", "sha256": "2be6f4de27ca89d1d4a3df6bf27cf6a41ea81b27fcf11b5329a842b8117d6555", "sha256_in_prefix": "2be6f4de27ca89d1d4a3df6bf27cf6a41ea81b27fcf11b5329a842b8117d6555", "size_in_bytes": 13409}, {"_path": "site-packages/fsspec/gui.py", "path_type": "hardlink", "sha256": "090ed0b2b4e968395648b34ea7036825cee484e7185c86719ab00937d6c75905", "sha256_in_prefix": "090ed0b2b4e968395648b34ea7036825cee484e7185c86719ab00937d6c75905", "size_in_bytes": 14002}, {"_path": "site-packages/fsspec/implementations/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/fsspec/implementations/arrow.py", "path_type": "hardlink", "sha256": "ef6d438a49defe557fd2d96093d8f22a61cbe96fb9313d21d8b286bce6104cf2", "sha256_in_prefix": "ef6d438a49defe557fd2d96093d8f22a61cbe96fb9313d21d8b286bce6104cf2", "size_in_bytes": 8623}, {"_path": "site-packages/fsspec/implementations/asyn_wrapper.py", "path_type": "hardlink", "sha256": "e37e4d57f2f2ad189cdd6bd2c4c5aaec1f10195fcebf38bebd87765b5ff562d3", "sha256_in_prefix": "e37e4d57f2f2ad189cdd6bd2c4c5aaec1f10195fcebf38bebd87765b5ff562d3", "size_in_bytes": 3326}, {"_path": "site-packages/fsspec/implementations/cache_mapper.py", "path_type": "hardlink", "sha256": "5b8c25c723f165b4a9f48b49d29611541321e9bc3d785ca980fea4518bae888e", "sha256_in_prefix": "5b8c25c723f165b4a9f48b49d29611541321e9bc3d785ca980fea4518bae888e", "size_in_bytes": 2421}, {"_path": "site-packages/fsspec/implementations/cache_metadata.py", "path_type": "hardlink", "sha256": "add761e7ed125c87b25823e906938571a0325a83f27989857deb9b116b7e9d13", "sha256_in_prefix": "add761e7ed125c87b25823e906938571a0325a83f27989857deb9b116b7e9d13", "size_in_bytes": 8536}, {"_path": "site-packages/fsspec/implementations/cached.py", "path_type": "hardlink", "sha256": "e7d9725af6f3bd7ff2602f5c5404ab92d39d8e62bac3e7bb74db4d0491da38d4", "sha256_in_prefix": "e7d9725af6f3bd7ff2602f5c5404ab92d39d8e62bac3e7bb74db4d0491da38d4", "size_in_bytes": 35103}, {"_path": "site-packages/fsspec/implementations/dask.py", "path_type": "hardlink", "sha256": "09765b2732153a1295f082dcc6ecb76d3bdc69c0ae7806f24319af0246cf92b9", "sha256_in_prefix": "09765b2732153a1295f082dcc6ecb76d3bdc69c0ae7806f24319af0246cf92b9", "size_in_bytes": 4466}, {"_path": "site-packages/fsspec/implementations/data.py", "path_type": "hardlink", "sha256": "2c32dccf1461f21ef1dfd663addf868337501ebefcc98c4396bbf60bd5316f91", "sha256_in_prefix": "2c32dccf1461f21ef1dfd663addf868337501ebefcc98c4396bbf60bd5316f91", "size_in_bytes": 1658}, {"_path": "site-packages/fsspec/implementations/dbfs.py", "path_type": "hardlink", "sha256": "d81a7ed26f52aa56aba036b429b5f16f90686c2c8127bff961004849fddfc5b4", "sha256_in_prefix": "d81a7ed26f52aa56aba036b429b5f16f90686c2c8127bff961004849fddfc5b4", "size_in_bytes": 15145}, {"_path": "site-packages/fsspec/implementations/dirfs.py", "path_type": "hardlink", "sha256": "7f5b069d0f557f4c53c6b5e8e230de072e107eadd14ea0047a6a8149e6f48706", "sha256_in_prefix": "7f5b069d0f557f4c53c6b5e8e230de072e107eadd14ea0047a6a8149e6f48706", "size_in_bytes": 12108}, {"_path": "site-packages/fsspec/implementations/ftp.py", "path_type": "hardlink", "sha256": "6f32ff4e01fbee730cb5331ec111a46eae2239b4871aeed8a0c4425c11f89eb7", "sha256_in_prefix": "6f32ff4e01fbee730cb5331ec111a46eae2239b4871aeed8a0c4425c11f89eb7", "size_in_bytes": 11639}, {"_path": "site-packages/fsspec/implementations/gist.py", "path_type": "hardlink", "sha256": "3acb7df3986616be742ac03e403d2c858de13f8297e6a27dadbe42f97e1e84af", "sha256_in_prefix": "3acb7df3986616be742ac03e403d2c858de13f8297e6a27dadbe42f97e1e84af", "size_in_bytes": 8341}, {"_path": "site-packages/fsspec/implementations/git.py", "path_type": "hardlink", "sha256": "a810d6333e4b36594fa958ebe637ffd45b8d85ae0fe65c9023722585883ec141", "sha256_in_prefix": "a810d6333e4b36594fa958ebe637ffd45b8d85ae0fe65c9023722585883ec141", "size_in_bytes": 3731}, {"_path": "site-packages/fsspec/implementations/github.py", "path_type": "hardlink", "sha256": "682b192fc52f5d981d91c075454b370dd2de36b8cb29c16c15879014359b045a", "sha256_in_prefix": "682b192fc52f5d981d91c075454b370dd2de36b8cb29c16c15879014359b045a", "size_in_bytes": 11653}, {"_path": "site-packages/fsspec/implementations/http.py", "path_type": "hardlink", "sha256": "dcb858b91537cb0defded37c3aacfa11b25197769bd9283fcec18e22fd04da01", "sha256_in_prefix": "dcb858b91537cb0defded37c3aacfa11b25197769bd9283fcec18e22fd04da01", "size_in_bytes": 30418}, {"_path": "site-packages/fsspec/implementations/http_sync.py", "path_type": "hardlink", "sha256": "532743a9275405d8622752ae7f357caca1ab4dfb45478426355d2c69f20b6fc8", "sha256_in_prefix": "532743a9275405d8622752ae7f357caca1ab4dfb45478426355d2c69f20b6fc8", "size_in_bytes": 30133}, {"_path": "site-packages/fsspec/implementations/jupyter.py", "path_type": "hardlink", "sha256": "076ba3ece126ef2224faf452b0edfbfcd0f4b74101be7e01f92bb8de26cde0f8", "sha256_in_prefix": "076ba3ece126ef2224faf452b0edfbfcd0f4b74101be7e01f92bb8de26cde0f8", "size_in_bytes": 3811}, {"_path": "site-packages/fsspec/implementations/libarchive.py", "path_type": "hardlink", "sha256": "e7f2360e22d7c10d490bcc7e2bb8d7bbeb41c213bd763eed14b9dbd1b4e754c4", "sha256_in_prefix": "e7f2360e22d7c10d490bcc7e2bb8d7bbeb41c213bd763eed14b9dbd1b4e754c4", "size_in_bytes": 7102}, {"_path": "site-packages/fsspec/implementations/local.py", "path_type": "hardlink", "sha256": "0d078aee3446bf8fe6240c1e2ca00b3b95b32089235f167f8d1bf043fc5a7520", "sha256_in_prefix": "0d078aee3446bf8fe6240c1e2ca00b3b95b32089235f167f8d1bf043fc5a7520", "size_in_bytes": 16936}, {"_path": "site-packages/fsspec/implementations/memory.py", "path_type": "hardlink", "sha256": "29ce936526d9e2d762fba704e6db443c880ccaaf5a02de9c0dd54b1514c9bdff", "sha256_in_prefix": "29ce936526d9e2d762fba704e6db443c880ccaaf5a02de9c0dd54b1514c9bdff", "size_in_bytes": 10488}, {"_path": "site-packages/fsspec/implementations/reference.py", "path_type": "hardlink", "sha256": "9e9623e3d02647cae638df6dfc12e97c45ea860b1aadd51eca76a6ab2ada397a", "sha256_in_prefix": "9e9623e3d02647cae638df6dfc12e97c45ea860b1aadd51eca76a6ab2ada397a", "size_in_bytes": 48704}, {"_path": "site-packages/fsspec/implementations/sftp.py", "path_type": "hardlink", "sha256": "7cc63d5d9726a63b33436b42a8efd33da25eb1a783fc3bfba6d6338143c6a0ed", "sha256_in_prefix": "7cc63d5d9726a63b33436b42a8efd33da25eb1a783fc3bfba6d6338143c6a0ed", "size_in_bytes": 5631}, {"_path": "site-packages/fsspec/implementations/smb.py", "path_type": "hardlink", "sha256": "e5f86ef21d3a9ce2c13e1d9ce3c693ed6051aa1f5c11c6c8c2dcaed3ac138de7", "sha256_in_prefix": "e5f86ef21d3a9ce2c13e1d9ce3c693ed6051aa1f5c11c6c8c2dcaed3ac138de7", "size_in_bytes": 15236}, {"_path": "site-packages/fsspec/implementations/tar.py", "path_type": "hardlink", "sha256": "75a9bbf13a7f0a8cf26cda82636258806052dd473d16e254013f6807496894eb", "sha256_in_prefix": "75a9bbf13a7f0a8cf26cda82636258806052dd473d16e254013f6807496894eb", "size_in_bytes": 4111}, {"_path": "site-packages/fsspec/implementations/webhdfs.py", "path_type": "hardlink", "sha256": "1bdc06cb08fb064664e0cbbdcd7bba1da0e512a5f817c1b0d62e24e3a08fffea", "sha256_in_prefix": "1bdc06cb08fb064664e0cbbdcd7bba1da0e512a5f817c1b0d62e24e3a08fffea", "size_in_bytes": 16769}, {"_path": "site-packages/fsspec/implementations/zip.py", "path_type": "hardlink", "sha256": "f4b04c1cf7edd8ebad265d85b7eafdbbfcf71a9b4b91cda7f75babd80ddb09b8", "sha256_in_prefix": "f4b04c1cf7edd8ebad265d85b7eafdbbfcf71a9b4b91cda7f75babd80ddb09b8", "size_in_bytes": 6072}, {"_path": "site-packages/fsspec/json.py", "path_type": "hardlink", "sha256": "dc17cd490f7a301e176a8fe872385e20d7aa64cd9ebfba258d4cd1e5798d5eb1", "sha256_in_prefix": "dc17cd490f7a301e176a8fe872385e20d7aa64cd9ebfba258d4cd1e5798d5eb1", "size_in_bytes": 3814}, {"_path": "site-packages/fsspec/mapping.py", "path_type": "hardlink", "sha256": "9b69dd07f82d4415d898d260d087b5f81551ef94c595e1e62100730bec968635", "sha256_in_prefix": "9b69dd07f82d4415d898d260d087b5f81551ef94c595e1e62100730bec968635", "size_in_bytes": 8343}, {"_path": "site-packages/fsspec/parquet.py", "path_type": "hardlink", "sha256": "ea26c0986e76ecbe493454b454ef010cd9711dd0376d562a741c9e8858295153", "sha256_in_prefix": "ea26c0986e76ecbe493454b454ef010cd9711dd0376d562a741c9e8858295153", "size_in_bytes": 19448}, {"_path": "site-packages/fsspec/registry.py", "path_type": "hardlink", "sha256": "7a9a18af2145cc35a36e44097e1eb1905de712ef114e23b3577fafa22f0fb21b", "sha256_in_prefix": "7a9a18af2145cc35a36e44097e1eb1905de712ef114e23b3577fafa22f0fb21b", "size_in_bytes": 12048}, {"_path": "site-packages/fsspec/spec.py", "path_type": "hardlink", "sha256": "edc3947b93c2e54c9fe7a1ed181507128ca6f24b4f8fe048f06e0747c5ddfc2f", "sha256_in_prefix": "edc3947b93c2e54c9fe7a1ed181507128ca6f24b4f8fe048f06e0747c5ddfc2f", "size_in_bytes": 77298}, {"_path": "site-packages/fsspec/tests/abstract/__init__.py", "path_type": "hardlink", "sha256": "e31509aefee00e073ce7100ecf5a7e57f2b586bb1d3164d26b4aef8802e5264f", "sha256_in_prefix": "e31509aefee00e073ce7100ecf5a7e57f2b586bb1d3164d26b4aef8802e5264f", "size_in_bytes": 10181}, {"_path": "site-packages/fsspec/tests/abstract/common.py", "path_type": "hardlink", "sha256": "d46430368e4038dcc09f3663d1f5a09fc3493cb5c02de85bb2e1b14b71735955", "sha256_in_prefix": "d46430368e4038dcc09f3663d1f5a09fc3493cb5c02de85bb2e1b14b71735955", "size_in_bytes": 4973}, {"_path": "site-packages/fsspec/tests/abstract/copy.py", "path_type": "hardlink", "sha256": "814e7e77ded4dd149d7b7e55a784713d8e2b5b02fbe381e24ab27c2013a9f7ef", "sha256_in_prefix": "814e7e77ded4dd149d7b7e55a784713d8e2b5b02fbe381e24ab27c2013a9f7ef", "size_in_bytes": 19967}, {"_path": "site-packages/fsspec/tests/abstract/get.py", "path_type": "hardlink", "sha256": "bcd4781f3b6f4d1ec28f9e80328effb71ed3798cf5260affd966fc2eff948816", "sha256_in_prefix": "bcd4781f3b6f4d1ec28f9e80328effb71ed3798cf5260affd966fc2eff948816", "size_in_bytes": 20755}, {"_path": "site-packages/fsspec/tests/abstract/mv.py", "path_type": "hardlink", "sha256": "93c79410122b46b18cb0163938e6835dd1a7414286c0321f43276e07a603dcdb", "sha256_in_prefix": "93c79410122b46b18cb0163938e6835dd1a7414286c0321f43276e07a603dcdb", "size_in_bytes": 1982}, {"_path": "site-packages/fsspec/tests/abstract/open.py", "path_type": "hardlink", "sha256": "162d8f04f60b6d1ab2b05f1c166d2bc27078d6431d415623abc706c835e9dc15", "sha256_in_prefix": "162d8f04f60b6d1ab2b05f1c166d2bc27078d6431d415623abc706c835e9dc15", "size_in_bytes": 329}, {"_path": "site-packages/fsspec/tests/abstract/pipe.py", "path_type": "hardlink", "sha256": "2c5cc8acb081e462d77fdaf314a26613c01d1bb2d0fe1e1b268ef4afc14b3ea3", "sha256_in_prefix": "2c5cc8acb081e462d77fdaf314a26613c01d1bb2d0fe1e1b268ef4afc14b3ea3", "size_in_bytes": 402}, {"_path": "site-packages/fsspec/tests/abstract/put.py", "path_type": "hardlink", "sha256": "eda8a1d7b38a07f219661d4c92ad5e043223a1b86d310988f31f8fc3e4bf6999", "sha256_in_prefix": "eda8a1d7b38a07f219661d4c92ad5e043223a1b86d310988f31f8fc3e4bf6999", "size_in_bytes": 21201}, {"_path": "site-packages/fsspec/transaction.py", "path_type": "hardlink", "sha256": "c658911ba53665fde4846e31730f56881fb2028a894871062bf5631ce76d828d", "sha256_in_prefix": "c658911ba53665fde4846e31730f56881fb2028a894871062bf5631ce76d828d", "size_in_bytes": 2398}, {"_path": "site-packages/fsspec/utils.py", "path_type": "hardlink", "sha256": "1c2f1115b6fb2a910379db18c44c6f5afb1339b12e51cbae5b177407f3321a9a", "sha256_in_prefix": "1c2f1115b6fb2a910379db18c44c6f5afb1339b12e51cbae5b177407f3321a9a", "size_in_bytes": 22995}, {"_path": "Lib/site-packages/fsspec/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/_version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/archive.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/asyn.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/caching.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/callbacks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/compression.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/config.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/conftest.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/core.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/dircache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/fuse.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/generic.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/gui.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/arrow.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/asyn_wrapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/cache_mapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/cache_metadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/cached.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/dask.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/data.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/dbfs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/dirfs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/ftp.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/gist.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/git.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/github.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/http.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/http_sync.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/jupyter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/libarchive.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/local.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/memory.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/reference.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/sftp.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/smb.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/tar.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/webhdfs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/implementations/__pycache__/zip.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/json.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/mapping.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/parquet.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/registry.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/spec.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/common.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/copy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/get.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/mv.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/open.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/pipe.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/tests/abstract/__pycache__/put.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/transaction.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/fsspec/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "f734d98cd046392fbd9872df89ac043d72ac15f6a2529f129d912e28ab44609c", "size": 145357, "subdir": "noarch", "timestamp": 1752608821000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.7.0-pyhd8ed1ab_0.conda", "version": "2025.7.0"}