<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_vars_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x71.html#index_q"><span>q</span></a></li>
      <li class="current"><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_r">- r -</a></h3><ul>
<li>range
: <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#2762f636101d110ab5e40b0e78048372">CUpti_Profiler_BeginSession_Params</a>
<li>rangeId
: <a class="el" href="structCUpti__PCSamplingData.html#f8e5928e814b1697b3236f62c66591f0">CUpti_PCSamplingData</a>
<li>recordCount
: <a class="el" href="structBufferInfo.html#085c8c6c7565b438f2a5831915b4b07e">BufferInfo</a>
<li>registersPerThread
: <a class="el" href="structCUpti__ActivityKernel.html#b145d57e1a5c8ef0bd5bdc2ff1765041">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#c0d69567bba06869994e5c543af5e5ee">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#23657e8ff29782fd4434f391c2379b49">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel2.html#a4b039cdaf7e6d9bac28d6594d5fa156">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#d90ea5041a5d20cba239929aa4fe9873">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#96c92420111e41926ce014bae09b600c">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#d3f8a99006bdc0efd21ace4e0c8514e7">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#fc48a033c27e576d7181e866f8a1745f">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#73d9271315af6d4aac753507a4ed573c">CUpti_ActivityCdpKernel</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#e9573155f11831f3fcea6d960b5060f0">CUpti_ActivityKernel4</a>
<li>releaseThreshold
: <a class="el" href="structCUpti__ActivityMemoryPool.html#7e2918760a23740da9eef884d33f1ad9">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#8ffbcf073ce5c973cd5b06ab5358a1a4">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#dc0e926abd13ce22546621c6ff957c7e">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#a440564d6df427c241c6f93d2246187c">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a>
<li>remainingNumPcs
: <a class="el" href="structCUpti__PCSamplingData.html#bb938b9cff32663ea47e138ad76105f7">CUpti_PCSamplingData</a>
<li>replayMode
: <a class="el" href="structCUpti__Profiler__BeginSession__Params.html#7c368e06ef80b05b511897f437a8ea83">CUpti_Profiler_BeginSession_Params</a>
<li>requested
: <a class="el" href="structCUpti__ActivityKernel2.html#caa17e8a99abc44e2a82ce3563614f00">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#a1989ef0d58190bd5f34562bf46b00a9">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#127c7773b8a51866e08502def3be32e5">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#88fac5be5e7d2dc8c65bd617c08bc300">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#08605325dc3b97038fd31bd9d3b584b1">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#141f3146852da54f164003fa8c8195ab">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#7753011404c14094cc6b0535b2366016">CUpti_ActivityKernel8</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#0987b28950296d2989c521136e07c7ad">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityCdpKernel.html#a5a6ed25881e9bd1ed3203e4260426f2">CUpti_ActivityCdpKernel</a>
<li>reserved
: <a class="el" href="structCUpti__ActivityExternalCorrelation.html#0e201d0a36bec503c614a38e038ef7a1">CUpti_ActivityExternalCorrelation</a>
, <a class="el" href="structCUpti__ActivityInstantaneousEvent.html#4965924e17cec028c3b240b76c0770b1">CUpti_ActivityInstantaneousEvent</a>
, <a class="el" href="structCUpti__ActivityGraphTrace.html#68d4a64ceadf1a6234b79e8c6238c677">CUpti_ActivityGraphTrace</a>
<li>reserved0
: <a class="el" href="structCUpti__ActivityKernel2.html#7ce9734227bbb8f72f0e4ea5ee3d96c9">CUpti_ActivityKernel2</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#49cbe015c779a6cbdc2220bce339866a">CUpti_ActivityMemcpy</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#366d41146597b9ecdcf5fd85316fee55">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#7680663b2361acb6ee9d213981b3bb51">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP4.html#74a7e9eb58c5d138387b0e2480ae4ede">CUpti_ActivityMemcpyPtoP4</a>
, <a class="el" href="structCUpti__ActivityKernel9.html#af675ae91db73fd3a1205a64c0529976">CUpti_ActivityKernel9</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP.html#3b7b5615cbc5940dd7f918ace8931e12">CUpti_ActivityMemcpyPtoP</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#b9f63f0159ec44cf961c31c2b571be8d">CUpti_ActivityMemcpyPtoP2</a>
, <a class="el" href="structCUpti__ActivityMemset4.html#1fa538036ffcb4127260be49ef5293c1">CUpti_ActivityMemset4</a>
, <a class="el" href="structCUpti__ActivityKernel6.html#5f3e517d74c9f097befb461b0eec3f5a">CUpti_ActivityKernel6</a>
, <a class="el" href="structCUpti__ActivityMemset.html#4fdea6a2a1881b6c0ba640455546761a">CUpti_ActivityMemset</a>
, <a class="el" href="structCUpti__ActivityMemset2.html#17d899c9c24e31e6cb3c8b1e9f14df33">CUpti_ActivityMemset2</a>
, <a class="el" href="structCUpti__ActivityMemset3.html#c8c74336fabc54972fa21a7526d783dd">CUpti_ActivityMemset3</a>
, <a class="el" href="structCUpti__ActivityKernel.html#6effe828d4bef437197a5e0a8dac074f">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityKernel3.html#f72d27d8b8b706c3af8e7fd161cb1b88">CUpti_ActivityKernel3</a>
, <a class="el" href="structCUpti__ActivityKernel5.html#429dd00e64b2cb834dccdc76abccabac">CUpti_ActivityKernel5</a>
, <a class="el" href="structCUpti__ActivityMemcpy5.html#5e53427fc6a010d09a5219ce32518dab">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityKernel4.html#810aa4ec3ea45b137a75956b8ad26ffc">CUpti_ActivityKernel4</a>
, <a class="el" href="structCUpti__ActivityKernel7.html#e905e9efd17ee5b55500427caef7b95c">CUpti_ActivityKernel7</a>
, <a class="el" href="structCUpti__ActivityMemcpyPtoP3.html#ceb10f6ae885b88390ee4af67230d26f">CUpti_ActivityMemcpyPtoP3</a>
, <a class="el" href="structCUpti__ActivityKernel8.html#a7b6be273934adb0a1f8d9806c5b9fc1">CUpti_ActivityKernel8</a>
<li>reserveDeviceMB
: <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#a11b83be71c55353f2cc9dd4bb98ae57">NV::Cupti::Checkpoint::CUpti_Checkpoint</a>
<li>reserveHostMB
: <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#85bcb685c5358ea818f9c9766cfd9faf">NV::Cupti::Checkpoint::CUpti_Checkpoint</a>
<li>resourceDescriptor
: <a class="el" href="structCUpti__ResourceData.html#fb6ad0494c0d5b882236ab9eda3a1e9b">CUpti_ResourceData</a>
<li>returnValue
: <a class="el" href="structCUpti__ActivityAPI.html#096f5b78b6049d16812114f6af721b72">CUpti_ActivityAPI</a>
<li>runtimeCorrelationId
: <a class="el" href="structCUpti__ActivityMemcpy5.html#5b2bea88b5122860ce68713a831eecbf">CUpti_ActivityMemcpy5</a>
, <a class="el" href="structCUpti__ActivityKernel.html#6476274fc86dbc25c968ba5d8716ef92">CUpti_ActivityKernel</a>
, <a class="el" href="structCUpti__ActivityMemcpy4.html#a7c454e0bb17446cbda983ee6cf4920a">CUpti_ActivityMemcpy4</a>
, <a class="el" href="structCUpti__ActivityMemcpy3.html#3fcf8b9d4e65845da9f36a2528699a41">CUpti_ActivityMemcpy3</a>
, <a class="el" href="structCUpti__ActivityMemcpy.html#d9366d4d84c6a37aeda7632a91180644">CUpti_ActivityMemcpy</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
