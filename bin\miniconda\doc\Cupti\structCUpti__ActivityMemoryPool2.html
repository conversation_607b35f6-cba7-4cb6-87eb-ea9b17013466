<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemoryPool2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemoryPool2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemoryPool2" -->The activity record for memory pool.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#79960e2d874e5d1957c5ebd639e6feed">address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#7395211d58aafd2bb77d1d291db60b8f">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#84335784cfe88e1c6d4c310f215080aa">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#6e855f2f5d8ee15d1882f349d08f89dc">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#dd537ef336b06369505b407d9fb9a007">memoryPoolOperationType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#48ddeaffc29cb6f86f37a359fe211a85">memoryPoolType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#b3b1055f571a23a88223a998c7db4f12">minBytesToKeep</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#377cc6eb746e2ea942de3a07cddaacf2">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#4f0f99e31f2312ddd9eac5e9c97bf16a">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#8ffbcf073ce5c973cd5b06ab5358a1a4">releaseThreshold</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#f10a1e6649ab0e7466eb699999971974">size</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#dba2a2a9099ee6957de50f28b6a642cc">timestamp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemoryPool2.html#22105323f95af5512198a8c97bfaa06f">utilizedSize</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory pool creation, destruction and trimming (CUPTI_ACTIVITY_KIND_MEMORY_POOL). This activity record provides separate records for memory pool creation, destruction and triming operations. This allows to correlate the corresponding driver and runtime API activity record with the memory pool operation. <hr><h2>Field Documentation</h2>
<a class="anchor" name="79960e2d874e5d1957c5ebd639e6feed"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::address" ref="79960e2d874e5d1957c5ebd639e6feed" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#79960e2d874e5d1957c5ebd639e6feed">CUpti_ActivityMemoryPool2::address</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The virtual address of the allocation. 
</div>
</div><p>
<a class="anchor" name="7395211d58aafd2bb77d1d291db60b8f"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::correlationId" ref="7395211d58aafd2bb77d1d291db60b8f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#7395211d58aafd2bb77d1d291db60b8f">CUpti_ActivityMemoryPool2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory pool operation. Each memory pool operation is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory operation. 
</div>
</div><p>
<a class="anchor" name="84335784cfe88e1c6d4c310f215080aa"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::deviceId" ref="84335784cfe88e1c6d4c310f215080aa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#84335784cfe88e1c6d4c310f215080aa">CUpti_ActivityMemoryPool2::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory pool is created. 
</div>
</div><p>
<a class="anchor" name="6e855f2f5d8ee15d1882f349d08f89dc"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::kind" ref="6e855f2f5d8ee15d1882f349d08f89dc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemoryPool2.html#6e855f2f5d8ee15d1882f349d08f89dc">CUpti_ActivityMemoryPool2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMORY_POOL 
</div>
</div><p>
<a class="anchor" name="dd537ef336b06369505b407d9fb9a007"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::memoryPoolOperationType" ref="dd537ef336b06369505b407d9fb9a007" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a> <a class="el" href="structCUpti__ActivityMemoryPool2.html#dd537ef336b06369505b407d9fb9a007">CUpti_ActivityMemoryPool2::memoryPoolOperationType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory operation requested by the user, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a>. 
</div>
</div><p>
<a class="anchor" name="48ddeaffc29cb6f86f37a359fe211a85"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::memoryPoolType" ref="48ddeaffc29cb6f86f37a359fe211a85" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> <a class="el" href="structCUpti__ActivityMemoryPool2.html#48ddeaffc29cb6f86f37a359fe211a85">CUpti_ActivityMemoryPool2::memoryPoolType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the memory pool, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a> 
</div>
</div><p>
<a class="anchor" name="b3b1055f571a23a88223a998c7db4f12"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::minBytesToKeep" ref="b3b1055f571a23a88223a998c7db4f12" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#b3b1055f571a23a88223a998c7db4f12">CUpti_ActivityMemoryPool2::minBytesToKeep</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The minimum bytes to keep of the memory pool. <code>minBytesToKeep</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_OPERATION_TYPE_TRIMMED, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g878fb2c94e0051169fdf0ca7982612a2">CUpti_ActivityMemoryPoolOperationType</a> 
</div>
</div><p>
<a class="anchor" name="377cc6eb746e2ea942de3a07cddaacf2"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::pad" ref="377cc6eb746e2ea942de3a07cddaacf2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#377cc6eb746e2ea942de3a07cddaacf2">CUpti_ActivityMemoryPool2::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="4f0f99e31f2312ddd9eac5e9c97bf16a"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::processId" ref="4f0f99e31f2312ddd9eac5e9c97bf16a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#4f0f99e31f2312ddd9eac5e9c97bf16a">CUpti_ActivityMemoryPool2::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process to which this record belongs to. 
</div>
</div><p>
<a class="anchor" name="8ffbcf073ce5c973cd5b06ab5358a1a4"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::releaseThreshold" ref="8ffbcf073ce5c973cd5b06ab5358a1a4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#8ffbcf073ce5c973cd5b06ab5358a1a4">CUpti_ActivityMemoryPool2::releaseThreshold</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The release threshold of the memory pool. <code>releaseThreshold</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="f10a1e6649ab0e7466eb699999971974"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::size" ref="f10a1e6649ab0e7466eb699999971974" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#f10a1e6649ab0e7466eb699999971974">CUpti_ActivityMemoryPool2::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the memory pool operation in bytes. <code>size</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
<a class="anchor" name="dba2a2a9099ee6957de50f28b6a642cc"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::timestamp" ref="dba2a2a9099ee6957de50f28b6a642cc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#dba2a2a9099ee6957de50f28b6a642cc">CUpti_ActivityMemoryPool2::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory operation, in ns. 
</div>
</div><p>
<a class="anchor" name="22105323f95af5512198a8c97bfaa06f"></a><!-- doxytag: member="CUpti_ActivityMemoryPool2::utilizedSize" ref="22105323f95af5512198a8c97bfaa06f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemoryPool2.html#22105323f95af5512198a8c97bfaa06f">CUpti_ActivityMemoryPool2::utilizedSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The utilized size of the memory pool. <code>utilizedSize</code> is valid for CUPTI_ACTIVITY_MEMORY_POOL_TYPE_LOCAL, <a class="el" href="group__CUPTI__ACTIVITY__API.html#g8c40b23a5fe82862b18d60c5c42399a8">CUpti_ActivityMemoryPoolType</a>. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
