{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-documentation-12.1.55-0", "features": "", "files": ["cuda-doc/CUDA_Toolkit_Release_Notes.txt", "cuda-doc/DOCS", "cuda-doc/EULA.txt", "cuda-doc/LICENSE", "cuda-doc/README", "tools/CUDA_Occupancy_Calculator.xls"], "fn": "cuda-documentation-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-documentation-12.1.55-0", "type": 1}, "md5": "24d834c007322c61c4ec9543ec990aa2", "name": "cuda-documentation", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-documentation-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "cuda-doc/CUDA_Toolkit_Release_Notes.txt", "path_type": "hardlink", "sha256": "5e1ae9fa830c20db1198d798d6b279c6fd6a51304e2777abc191b64b037da42a", "sha256_in_prefix": "5e1ae9fa830c20db1198d798d6b279c6fd6a51304e2777abc191b64b037da42a", "size_in_bytes": 80660}, {"_path": "cuda-doc/DOCS", "path_type": "hardlink", "sha256": "4cb366220ae0b4e082e15e5599df992e1aae6e914fb442fb24c8a5909ac8809d", "sha256_in_prefix": "4cb366220ae0b4e082e15e5599df992e1aae6e914fb442fb24c8a5909ac8809d", "size_in_bytes": 160}, {"_path": "cuda-doc/EULA.txt", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "cuda-doc/LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "cuda-doc/README", "path_type": "hardlink", "sha256": "04932352f70876bffdf9faa4d997c9592dd7ede34f2798a22ca9b8ff3edc9e10", "sha256_in_prefix": "04932352f70876bffdf9faa4d997c9592dd7ede34f2798a22ca9b8ff3edc9e10", "size_in_bytes": 524}, {"_path": "tools/CUDA_Occupancy_Calculator.xls", "path_type": "hardlink", "sha256": "bb431220acf3158fbf77bb84dec3f8e89f3b5880350411dee113516d4e204037", "sha256_in_prefix": "bb431220acf3158fbf77bb84dec3f8e89f3b5880350411dee113516d4e204037", "size_in_bytes": 266240}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 91047, "subdir": "win-64", "timestamp": 1674618848000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-documentation-12.1.55-0.tar.bz2", "version": "12.1.55"}