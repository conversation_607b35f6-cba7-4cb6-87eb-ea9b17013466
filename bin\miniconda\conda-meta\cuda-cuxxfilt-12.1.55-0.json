{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cuxxfilt-12.1.55-0", "features": "", "files": ["LICENSE", "bin/cu++filt.exe", "include/nv_decode.h", "lib/x64/cufilt.lib"], "fn": "cuda-cuxxfilt-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cuxxfilt-12.1.55-0", "type": 1}, "md5": "a176b2fcc46248af61fae012e50def48", "name": "cuda-cuxxfilt", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-cuxxfilt-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "bin/cu++filt.exe", "path_type": "hardlink", "sha256": "11c301f4f94b7a2884291263a0439cca9850691e9b56b85bb0cda61145f4a31f", "sha256_in_prefix": "11c301f4f94b7a2884291263a0439cca9850691e9b56b85bb0cda61145f4a31f", "size_in_bytes": 201216}, {"_path": "include/nv_decode.h", "path_type": "hardlink", "sha256": "7a69f3d07f81ddc8d487d51a6348d8cdeec44c9be7e465d364401b7ebb621bb7", "sha256_in_prefix": "7a69f3d07f81ddc8d487d51a6348d8cdeec44c9be7e465d364401b7ebb621bb7", "size_in_bytes": 1970}, {"_path": "lib/x64/cufilt.lib", "path_type": "hardlink", "sha256": "3526c4adc24d5ca10e23e5634d7b0df6c1318c16e89f64d9b0cf99f1d3629f3f", "sha256_in_prefix": "3526c4adc24d5ca10e23e5634d7b0df6c1318c16e89f64d9b0cf99f1d3629f3f", "size_in_bytes": 153510}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 166522, "subdir": "win-64", "timestamp": 1674623019000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-cuxxfilt-12.1.55-0.tar.bz2", "version": "12.1.55"}