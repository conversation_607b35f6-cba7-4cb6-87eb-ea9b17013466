<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityUnifiedMemoryCounter2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityUnifiedMemoryCounter2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityUnifiedMemoryCounter2" -->The activity record for Unified Memory counters (CUDA 7.0 and beyond).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#2aae150a66b5187c1e60c775e665ee78">address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#995e75718002dd0073994d2ef3ece4fe">counterKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#303cfd2e8224e05f73675989310ff512">dstId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#b2911952d99de824524eca83a7f467dc">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#3095c670ddd8889a411179ffd7e3954a">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#4444bc2ffa7e464b23a16b5da35f03fd">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#771450d4e5cf20bffb0c02f7aa67eef1">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#2e18f2211c242e91197e027b29f0359f">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#d52654626d33fe90d1a86f3294dfd200">srcId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#9396ec87eb0e27c29664ea5e9ca3b099">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#bb8b65dc9a8de4edd50366ecdaa10ccc">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#3755fae68b31aa2e358b37521d68c05b">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a Unified Memory counter (CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER). <hr><h2>Field Documentation</h2>
<a class="anchor" name="2aae150a66b5187c1e60c775e665ee78"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::address" ref="2aae150a66b5187c1e60c775e665ee78" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#2aae150a66b5187c1e60c775e665ee78">CUpti_ActivityUnifiedMemoryCounter2::address</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This is the virtual base address of the page/s being transferred. For cpu and gpu faults, the virtual address for the page that faulted. 
</div>
</div><p>
<a class="anchor" name="995e75718002dd0073994d2ef3ece4fe"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::counterKind" ref="995e75718002dd0073994d2ef3ece4fe" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g601877eb6f7d248a5f538fd74b8fa782">CUpti_ActivityUnifiedMemoryCounterKind</a> <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#995e75718002dd0073994d2ef3ece4fe">CUpti_ActivityUnifiedMemoryCounter2::counterKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Unified Memory counter kind 
</div>
</div><p>
<a class="anchor" name="303cfd2e8224e05f73675989310ff512"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::dstId" ref="303cfd2e8224e05f73675989310ff512" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#303cfd2e8224e05f73675989310ff512">CUpti_ActivityUnifiedMemoryCounter2::dstId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the destination CPU/device involved in the memory transfer or remote map operation. Ignore this field if counterKind is CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT or CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT or CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING or CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING 
</div>
</div><p>
<a class="anchor" name="b2911952d99de824524eca83a7f467dc"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::end" ref="b2911952d99de824524eca83a7f467dc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#b2911952d99de824524eca83a7f467dc">CUpti_ActivityUnifiedMemoryCounter2::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp of the counter, in ns. Ignore this field if counterKind is CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT or CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING or CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD and CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOH, timestamp is captured when activity finishes on GPU. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT, timestamp is captured when CUDA driver queues the replay of faulting memory accesses on the GPU For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING, timestamp is captured when throttling operation was finished by CUDA driver 
</div>
</div><p>
<a class="anchor" name="3095c670ddd8889a411179ffd7e3954a"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::flags" ref="3095c670ddd8889a411179ffd7e3954a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#3095c670ddd8889a411179ffd7e3954a">CUpti_ActivityUnifiedMemoryCounter2::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with this record. See enums <a class="el" href="group__CUPTI__ACTIVITY__API.html#g58bc092ea88426687492fdea7b1c0ff3">CUpti_ActivityUnifiedMemoryAccessType</a> if counterKind is CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT and <a class="el" href="group__CUPTI__ACTIVITY__API.html#gc5e53db4204f170a6c31e520e080c1fc">CUpti_ActivityUnifiedMemoryMigrationCause</a> if counterKind is CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD or CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD and <a class="el" href="group__CUPTI__ACTIVITY__API.html#gc38ad61408d643a789562314cd18d6b7">CUpti_ActivityUnifiedMemoryRemoteMapCause</a> if counterKind is CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP and <a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> if counterKind is CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING or CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING 
</div>
</div><p>
<a class="anchor" name="4444bc2ffa7e464b23a16b5da35f03fd"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::kind" ref="4444bc2ffa7e464b23a16b5da35f03fd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#4444bc2ffa7e464b23a16b5da35f03fd">CUpti_ActivityUnifiedMemoryCounter2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_UNIFIED_MEMORY_COUNTER 
</div>
</div><p>
<a class="anchor" name="771450d4e5cf20bffb0c02f7aa67eef1"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::pad" ref="771450d4e5cf20bffb0c02f7aa67eef1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#771450d4e5cf20bffb0c02f7aa67eef1">CUpti_ActivityUnifiedMemoryCounter2::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="2e18f2211c242e91197e027b29f0359f"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::processId" ref="2e18f2211c242e91197e027b29f0359f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#2e18f2211c242e91197e027b29f0359f">CUpti_ActivityUnifiedMemoryCounter2::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process to which this record belongs to. 
</div>
</div><p>
<a class="anchor" name="d52654626d33fe90d1a86f3294dfd200"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::srcId" ref="d52654626d33fe90d1a86f3294dfd200" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#d52654626d33fe90d1a86f3294dfd200">CUpti_ActivityUnifiedMemoryCounter2::srcId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the source CPU/device involved in the memory transfer, page fault, thrashing, throttling or remote map operation. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING, it is a bitwise ORing of the device IDs fighting for the memory region. Ignore this field if counterKind is CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT 
</div>
</div><p>
<a class="anchor" name="9396ec87eb0e27c29664ea5e9ca3b099"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::start" ref="9396ec87eb0e27c29664ea5e9ca3b099" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#9396ec87eb0e27c29664ea5e9ca3b099">CUpti_ActivityUnifiedMemoryCounter2::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp of the counter, in ns. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD and CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOH, timestamp is captured when activity starts on GPU. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT and CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT, timestamp is captured when CUDA driver started processing the fault. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THRASHING, timestamp is captured when CUDA driver detected thrashing of memory region. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THROTTLING, timestamp is captured when throttling opeeration was started by CUDA driver. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP, timestamp is captured when CUDA driver has pushed all required operations to the processor specified by dstId. 
</div>
</div><p>
<a class="anchor" name="bb8b65dc9a8de4edd50366ecdaa10ccc"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::streamId" ref="bb8b65dc9a8de4edd50366ecdaa10ccc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#bb8b65dc9a8de4edd50366ecdaa10ccc">CUpti_ActivityUnifiedMemoryCounter2::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream causing the transfer. This value of this field is invalid. 
</div>
</div><p>
<a class="anchor" name="3755fae68b31aa2e358b37521d68c05b"></a><!-- doxytag: member="CUpti_ActivityUnifiedMemoryCounter2::value" ref="3755fae68b31aa2e358b37521d68c05b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#3755fae68b31aa2e358b37521d68c05b">CUpti_ActivityUnifiedMemoryCounter2::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Value of the counter For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_HTOD, CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_BYTES_TRANSFER_DTOH, CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_THREASHING and CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_REMOTE_MAP, it is the size of the memory region in bytes. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_GPU_PAGE_FAULT, it is the number of page fault groups for the same page. For counterKind CUPTI_ACTIVITY_UNIFIED_MEMORY_COUNTER_KIND_CPU_PAGE_FAULT_COUNT, it is the program counter for the instruction that caused fault. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
