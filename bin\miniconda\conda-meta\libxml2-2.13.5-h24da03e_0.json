{"arch": "x86_64", "build": "h24da03e_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["libiconv >=1.16,<2.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "zlib >=1.2.13,<1.3.0a0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libxml2-2.13.5-h24da03e_0", "files": ["Library/bin/libxml2.dll", "Library/bin/runxmlconf.exe", "Library/bin/xmlcatalog.exe", "Library/bin/xmllint.exe", "Library/include/libxml2/libxml/HTMLparser.h", "Library/include/libxml2/libxml/HTMLtree.h", "Library/include/libxml2/libxml/SAX.h", "Library/include/libxml2/libxml/SAX2.h", "Library/include/libxml2/libxml/c14n.h", "Library/include/libxml2/libxml/catalog.h", "Library/include/libxml2/libxml/chvalid.h", "Library/include/libxml2/libxml/debugXML.h", "Library/include/libxml2/libxml/dict.h", "Library/include/libxml2/libxml/encoding.h", "Library/include/libxml2/libxml/entities.h", "Library/include/libxml2/libxml/globals.h", "Library/include/libxml2/libxml/hash.h", "Library/include/libxml2/libxml/list.h", "Library/include/libxml2/libxml/nanoftp.h", "Library/include/libxml2/libxml/nanohttp.h", "Library/include/libxml2/libxml/parser.h", "Library/include/libxml2/libxml/parserInternals.h", "Library/include/libxml2/libxml/pattern.h", "Library/include/libxml2/libxml/relaxng.h", "Library/include/libxml2/libxml/schemasInternals.h", "Library/include/libxml2/libxml/schematron.h", "Library/include/libxml2/libxml/threads.h", "Library/include/libxml2/libxml/tree.h", "Library/include/libxml2/libxml/uri.h", "Library/include/libxml2/libxml/valid.h", "Library/include/libxml2/libxml/xinclude.h", "Library/include/libxml2/libxml/xlink.h", "Library/include/libxml2/libxml/xmlIO.h", "Library/include/libxml2/libxml/xmlautomata.h", "Library/include/libxml2/libxml/xmlerror.h", "Library/include/libxml2/libxml/xmlexports.h", "Library/include/libxml2/libxml/xmlmemory.h", "Library/include/libxml2/libxml/xmlmodule.h", "Library/include/libxml2/libxml/xmlreader.h", "Library/include/libxml2/libxml/xmlregexp.h", "Library/include/libxml2/libxml/xmlsave.h", "Library/include/libxml2/libxml/xmlschemas.h", "Library/include/libxml2/libxml/xmlschemastypes.h", "Library/include/libxml2/libxml/xmlstring.h", "Library/include/libxml2/libxml/xmlunicode.h", "Library/include/libxml2/libxml/xmlversion.h", "Library/include/libxml2/libxml/xmlwriter.h", "Library/include/libxml2/libxml/xpath.h", "Library/include/libxml2/libxml/xpathInternals.h", "Library/include/libxml2/libxml/xpointer.h", "Library/lib/libxml2.lib", "Library/lib/libxml2_a.lib", "Library/lib/libxml2_a_dll.lib", "Library/lib/pkgconfig/libxml-2.0.pc"], "fn": "libxml2-2.13.5-h24da03e_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libxml2-2.13.5-h24da03e_0", "type": 1}, "md5": "e51488cc55557e79a2a7d89fb6ea5d09", "name": "libxml2", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libxml2-2.13.5-h24da03e_0.conda", "paths_data": {"paths": [{"_path": "Library/lib/pkgconfig/libxml-2.0.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_ec39r2aui2/croot/libxml2_1732315999423/_h_env", "sha256": "b9ff97b5cb6d4d8821cefd98ed4607532b0360c5efd8ef4622065466999346e6", "sha256_in_prefix": "b57aaaf1041ab3673ca03ee267910460163c7186f6c11942dbaa1a6be6b8048b", "size_in_bytes": 666}], "paths_version": 1}, "platform": "win32", "requested_spec": "defaults/win-64::libxml2==2.13.5=h24da03e_0[md5=e51488cc55557e79a2a7d89fb6ea5d09]", "sha256": "221cb34819bee94b24c4e7001c4db7594fa2ccd165c8f74eea395845084e73bb", "size": 3029972, "subdir": "win-64", "timestamp": 1732316138000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libxml2-2.13.5-h24da03e_0.conda", "version": "2.13.5"}