/*<meta />*/

@import url('prettify/prettify.css');

/*! normalize.css v2.1.3 | MIT License | git.io/normalize */

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary
{
	display: block;
}

audio,
canvas,
video
{
	display: inline-block;
}

body
{
	font-family: Trebuchet, Helvetica, Arial, sans-serif;

}

p
{
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	margin-top: 8pt;
	margin-bottom: 8pt; 
}

h1
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 41px;
	font-weight: 600;
	line-height: 1.5;
}

h1 .small
{
	font-weight: 400;
	line-height: 1;
}

h1 small
{
	font-weight: 400;
	line-height: 1;
}

h2
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 34px;
	font-weight: 600;
	line-height: 1.5;
}

h2 .small
{
	font-weight: 400;
	line-height: 1;
}

h2 small
{
	font-weight: 400;
	line-height: 1;
}

h3
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 28px;
	font-weight: 600;
	line-height: 1.4;
}

h3 .small
{
	font-weight: 400;
	line-height: 1;
}

h3 small
{
	font-weight: 400;
	line-height: 1;
}

h4
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 20px;
	font-weight: 600;
	line-height: 1.4;
}

h4 .small
{
	font-weight: 400;
	line-height: 1;
}

h4 small
{
	font-weight: 400;
	line-height: 1;
}

h5
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 16px;
	font-weight: 600;
	line-height: 1.3;
}

h5 .small
{
	font-weight: 400;
	line-height: 1;
}

h5 small
{
	font-weight: 400;
	line-height: 1;
}

h6
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 14px;
	font-weight: 600;
	line-height: 1.3;
}

h6 .small
{
	font-weight: 400;
	line-height: 1;
}

h6 small
{
	font-weight: 400;
	line-height: 1;
}

.h1
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 41px;
	font-weight: 600;
	line-height: 1.5;
}

.h1 .small
{
	font-weight: 400;
	line-height: 1;
}

.h1 small
{
	font-weight: 400;
	line-height: 1;
}

.h2
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 34px;
	font-weight: 600;
	line-height: 1.5;
}

.h2 .small
{
	font-weight: 400;
	line-height: 1;
}

.h2 small
{
	font-weight: 400;
	line-height: 1;
}

.h3
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 28px;
	font-weight: 600;
	line-height: 1.4;
}

.h3 .small
{
	font-weight: 400;
	line-height: 1;
}

.h3 small
{
	font-weight: 400;
	line-height: 1;
}

.h4
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 20px;
	font-weight: 600;
	line-height: 1.4;
}

.h4 .small
{
	font-weight: 400;
	line-height: 1;
}

.h4 small
{
	font-weight: 400;
	line-height: 1;
}

.h5
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 16px;
	font-weight: 600;
	line-height: 1.3;
}

.h5 .small
{
	font-weight: 400;
	line-height: 1;
}

.h5 small
{
	font-weight: 400;
	line-height: 1;
}

.h6
{
	color: #76b900;
	font-family: Trebuchet, Helvetica, Arial, sans-serif;
	font-size: 14px;
	font-weight: 600;
	line-height: 1.3;
}

.h6 .small
{
	font-weight: 400;
	line-height: 1;
}

.h6 small
{
	font-weight: 400;
	line-height: 1;
}

.lead
{
	margin-bottom: 24px;
	font-size: 18px;
	font-weight: 200;
	line-height: 1.4;
}

table
{
	border-left-style: none;
	border-left-width: 1px;
	border-left-color: #696969;
	border-right-style: none;
	border-right-width: 1px;
	border-right-color: #696969;
	border-top-style: none;
	border-top-width: 1px;
	border-top-color: #696969;
	border-bottom-style: none;
	border-bottom-width: 1px;
	border-bottom-color: #696969;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 0px;
	padding-bottom: 0px;
}

li
{
	margin: 0.8em;
	margin-left: 0;
	margin-right: 0;
}

th
{
	vertical-align: middle;
	border-left-style: solid;
	border-left-width: 1px;
	border-left-color: #696969;
	border-right-style: solid;
	border-right-width: 1px;
	border-right-color: #696969;
	border-top-style: solid;
	border-top-width: 2px;
	border-top-color: #696969;
	border-bottom-style: solid;
	border-bottom-width: 2px;
	border-bottom-color: #696969;
	background-color: #b0c4de;
	margin-bottom: 12px;
	margin-top: 12px;
	margin-left: 3px;
	margin-right: 3px;
	padding-left: 2px;
	padding-right: 2px;
	padding-bottom: 8px;
	padding-top: 8px;
}

td
{
	border-left-style: solid;
	border-left-width: 1px;
	border-left-color: #696969;
	border-right-style: solid;
	border-right-width: 1px;
	border-right-color: #696969;
	border-top-style: solid;
	border-top-width: 1px;
	border-top-color: #696969;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	border-bottom-color: #696969;
	margin-top: 8px;
	margin-bottom: 8px;
	margin-left: 3px;
	margin-right: 3px;
	padding-left: 2px;
	padding-right: 2px;
	padding-bottom: 6px;
	padding-top: 6px;
}

td.notebox 
{
	background-color: #DDEEBF
	} 

a
{
	background: transparent;
	color: #76b900;
	text-decoration: none;
}

a.badge:focus
{
	color: #FFF;
	cursor: pointer;
	text-decoration: none;
}

a.badge:hover
{
	color: #FFF;
	cursor: pointer;
	text-decoration: none;
}

a.headerlink:hover
{
	background-color: #c60f0f;
	color: #FFF;
}

a.list-group-item
{
	color: #555;
}

a.list-group-item .list-group-item-heading
{
	color: #333;
}

a.list-group-item.active
{
	background-color: #76b900;
	border-color: #76b900;
	color: #FFF;
	z-index: 2;
}

a.list-group-item.active .list-group-item-heading
{
	color: inherit;
}

a.list-group-item.active .list-group-item-text
{
	color: #d3ff86;
}

a.list-group-item.active > .badge
{
	background-color: #FFF;
	color: #76b900;
}

a.list-group-item.active:focus
{
	background-color: #76b900;
	border-color: #76b900;
	color: #FFF;
	z-index: 2;
}

a.list-group-item.active:hover
{
	background-color: #76b900;
	border-color: #76b900;
	color: #FFF;
	z-index: 2;
}

a.list-group-item.active:focus .list-group-item-heading
{
	color: inherit;
}

a.list-group-item.active:focus .list-group-item-text
{
	color: #d3ff86;
}

a.list-group-item.active:hover .list-group-item-heading
{
	color: inherit;
}

a.list-group-item.active:hover .list-group-item-text
{
	color: #d3ff86;
}

a.list-group-item:focus
{
	background-color: #f5f5f5;
	text-decoration: none;
}

a.list-group-item:hover
{
	background-color: #f5f5f5;
	text-decoration: none;
}

a.thumbnail.active
{
	border-color: #76b900;
}

a.thumbnail:focus
{
	border-color: #76b900;
}

a.thumbnail:hover
{
	border-color: #76b900;
}

a:visited
{
	text-decoration: underline;
}

a:focus
{
	color: #76b900;
	outline: 5px auto 0;
	outline-offset: -2px;
	text-decoration: underline;
}

a:hover
{
	color: #76b900;
	outline: 0;
	text-decoration: underline;
}

a:active
{
	outline: 0;
}

.btn
{
	display: inline-block;
	margin-bottom: 0;
	font-weight: normal;
	text-align: center;
	vertical-align: middle;
	cursor: pointer;
	background-image: none;
	border: 1px solid transparent;
	white-space: nowrap;
	padding: 6px 12px;
	font-size: 16px;
	line-height: 1.5;
	border-radius: 0px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	-o-user-select: none;
	user-select: none;
}

.btn:focus
{
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px;
}

.btn:hover,
.btn:focus
{
	color: #333333;
	text-decoration: none;
}

.btn:active,
.btn.active
{
	outline: 0;
	background-image: none;
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn
{
	cursor: not-allowed;
	pointer-events: none;
	opacity: 0.65;
	filter: alpha(opacity=65);
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-default
{
	color: #333333;
	background-color: #ffffff;
	border-color: #cccccc;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default
{
	color: #333333;
	background-color: #ebebeb;
	border-color: #adadad;
}

.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default
{
	background-image: none;
}

.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default:hover[disabled],
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default:focus[disabled],
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default:active[disabled],
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default.active[disabled],
fieldset[disabled] .btn-default.active
{
	background-color: #ffffff;
	border-color: #cccccc;
}

.btn-default .badge
{
	color: #ffffff;
	background-color: #fff;
}

.btn-primary
{
	color: #ffffff;
	background-color: #76b900;
	border-color: #76b900;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary
{
	color: #ffffff;
	background-color: #5c9000;
	border-color: #4f7c00;
}

.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary
{
	background-image: none;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary:hover[disabled],
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary:focus[disabled],
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary:active[disabled],
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary.active[disabled],
fieldset[disabled] .btn-primary.active
{
	background-color: #76b900;
	border-color: #76b900;
}

.btn-primary .badge
{
	color: #76b900;
	background-color: #fff;
}

.btn-warning
{
	color: #ffffff;
	background-color: #f0ad4e;
	border-color: #f0ad4e;
}

.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning
{
	color: #ffffff;
	background-color: #ed9c28;
	border-color: #eb9316;
}

.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning
{
	background-image: none;
}

.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning:hover[disabled],
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning:focus[disabled],
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled:active,
.btn-warning:active[disabled],
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning.active[disabled],
fieldset[disabled] .btn-warning.active
{
	background-color: #f0ad4e;
	border-color: #f0ad4e;
}

.btn-warning .badge
{
	color: #f0ad4e;
	background-color: #fff;
}

.btn-danger
{
	color: #ffffff;
	background-color: #d9534f;
	border-color: #d9534f;
}

.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger
{
	color: #ffffff;
	background-color: #d2322d;
	border-color: #c12e2a;
}

.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger
{
	background-image: none;
}

.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger:hover[disabled],
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger:focus[disabled],
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled:active,
.btn-danger:active[disabled],
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger.active[disabled],
fieldset[disabled] .btn-danger.active
{
	background-color: #d9534f;
	border-color: #d9534f;
}

.btn-danger .badge
{
	color: #d9534f;
	background-color: #fff;
}

.btn-success
{
	color: #ffffff;
	background-color: #76b900;
	border-color: #76b900;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success
{
	color: #ffffff;
	background-color: #5c9000;
	border-color: #4f7c00;
}

.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success
{
	background-image: none;
}

.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success:hover[disabled],
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success:focus[disabled],
fieldset[disabled] .btn-success:focus,
.btn-success.disabled:active,
.btn-success:active[disabled],
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success.active[disabled],
fieldset[disabled] .btn-success.active
{
	background-color: #76b900;
	border-color: #76b900;
}

.btn-success .badge
{
	color: #76b900;
	background-color: #fff;
}

.btn-info
{
	color: #ffffff;
	background-color: #5bc0de;
	border-color: #5bc0de;
}

.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info
{
	color: #ffffff;
	background-color: #39b3d7;
	border-color: #2aabd2;
}

.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info
{
	background-image: none;
}

.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info:hover[disabled],
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info:focus[disabled],
fieldset[disabled] .btn-info:focus,
.btn-info.disabled:active,
.btn-info:active[disabled],
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info.active[disabled],
fieldset[disabled] .btn-info.active
{
	background-color: #5bc0de;
	border-color: #5bc0de;
}

.btn-info .badge
{
	color: #5bc0de;
	background-color: #fff;
}

.btn-link
{
	color: #76b900;
	font-weight: normal;
	cursor: pointer;
	border-radius: 0;
}

.btn-link,
.btn-link:active,
.btn-link[disabled],
fieldset[disabled] .btn-link
{
	background-color: transparent;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active
{
	border-color: transparent;
}

.btn-link:hover,
.btn-link:focus
{
	color: #76b900;
	text-decoration: underline;
	background-color: transparent;
}

.btn-link:hover[disabled],
fieldset[disabled] .btn-link:hover,
.btn-link:focus[disabled],
fieldset[disabled] .btn-link:focus
{
	color: #999999;
	text-decoration: none;
}

.btn-lg
{
	padding: 8px 16px;
	font-size: 20px;
	line-height: 1.33;
	border-radius: 0px;
}

.btn-sm
{
	padding: 5px 10px;
	font-size: 14px;
	line-height: 1.5;
	border-radius: 0px;
}

.btn-xs
{
	padding: 4px 6px;
	font-size: 14px;
	line-height: 1.5;
	border-radius: 0px;
}

.btn-block
{
	display: block;
	width: 100%;
	padding-left: 0;
	padding-right: 0;
}

.btn-block + .btn-block
{
	margin-top: 5px;
}

input.btn-block[type="submit"],
input.btn-block[type="reset"],
input.btn-block[type="button"]
{
	width: 100%;
}

.row
{
	margin-left: -10px;
	margin-right: -10px;
}

.row:before,
.row:after
{
	content: " ";
	display: table;
}

.row:after
{
	clear: both;
}

.row:before,
.row:after
{
	content: " ";
	display: table;
}

.row:after
{
	clear: both;
}

.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12
{
	position: relative;
	min-height: 1px;
	padding-left: 10px;
	padding-right: 10px;
}

.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12
{
	float: left;
}

.col-xs-12
{
	width: 100%;
}

.col-xs-11
{
	width: 91.66666666666666%;
}

.col-xs-10
{
	width: 83.33333333333334%;
}

.col-xs-9
{
	width: 75%;
}

.col-xs-8
{
	width: 66.66666666666666%;
}

.col-xs-7
{
	width: 58.333333333333336%;
}

.col-xs-6
{
	width: 50%;
}

.col-xs-5
{
	width: 41.66666666666667%;
}

.col-xs-4
{
	width: 33.33333333333333%;
}

.col-xs-3
{
	width: 25%;
}

.col-xs-2
{
	width: 16.666666666666664%;
}

.col-xs-1
{
	width: 8.333333333333332%;
}

.col-xs-pull-12
{
	right: 100%;
}

.col-xs-pull-11
{
	right: 91.66666666666666%;
}

.col-xs-pull-10
{
	right: 83.33333333333334%;
}

.col-xs-pull-9
{
	right: 75%;
}

.col-xs-pull-8
{
	right: 66.66666666666666%;
}

.col-xs-pull-7
{
	right: 58.333333333333336%;
}

.col-xs-pull-6
{
	right: 50%;
}

.col-xs-pull-5
{
	right: 41.66666666666667%;
}

.col-xs-pull-4
{
	right: 33.33333333333333%;
}

.col-xs-pull-3
{
	right: 25%;
}

.col-xs-pull-2
{
	right: 16.666666666666664%;
}

.col-xs-pull-1
{
	right: 8.333333333333332%;
}

.col-xs-pull-0
{
	right: 0%;
}

.col-xs-push-12
{
	left: 100%;
}

.col-xs-push-11
{
	left: 91.66666666666666%;
}

.col-xs-push-10
{
	left: 83.33333333333334%;
}

.col-xs-push-9
{
	left: 75%;
}

.col-xs-push-8
{
	left: 66.66666666666666%;
}

.col-xs-push-7
{
	left: 58.333333333333336%;
}

.col-xs-push-6
{
	left: 50%;
}

.col-xs-push-5
{
	left: 41.66666666666667%;
}

.col-xs-push-4
{
	left: 33.33333333333333%;
}

.col-xs-push-3
{
	left: 25%;
}

.col-xs-push-2
{
	left: 16.666666666666664%;
}

.col-xs-push-1
{
	left: 8.333333333333332%;
}

.col-xs-push-0
{
	left: 0%;
}

.col-xs-offset-12
{
	margin-left: 100%;
}

.col-xs-offset-11
{
	margin-left: 91.66666666666666%;
}

.col-xs-offset-10
{
	margin-left: 83.33333333333334%;
}

.col-xs-offset-9
{
	margin-left: 75%;
}

.col-xs-offset-8
{
	margin-left: 66.66666666666666%;
}

.col-xs-offset-7
{
	margin-left: 58.333333333333336%;
}

.col-xs-offset-6
{
	margin-left: 50%;
}

.col-xs-offset-5
{
	margin-left: 41.66666666666667%;
}

.col-xs-offset-4
{
	margin-left: 33.33333333333333%;
}

.col-xs-offset-3
{
	margin-left: 25%;
}

.col-xs-offset-2
{
	margin-left: 16.666666666666664%;
}

.col-xs-offset-1
{
	margin-left: 8.333333333333332%;
}

.col-xs-offset-0
{
	margin-left: 0%;
}

.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12
{
	float: left;
}

.col-sm-12
{
	width: 100%;
}

.col-sm-11
{
	width: 91.66666666666666%;
}

.col-sm-10
{
	width: 83.33333333333334%;
}

.col-sm-9
{
	width: 75%;
}

.col-sm-8
{
	width: 66.66666666666666%;
}

.col-sm-7
{
	width: 58.333333333333336%;
}

.col-sm-6
{
	width: 50%;
}

.col-sm-5
{
	width: 41.66666666666667%;
}

.col-sm-4
{
	width: 33.33333333333333%;
}

.col-sm-3
{
	width: 25%;
}

.col-sm-2
{
	width: 16.666666666666664%;
}

.col-sm-1
{
	width: 8.333333333333332%;
}

.col-sm-pull-12
{
	right: 100%;
}

.col-sm-pull-11
{
	right: 91.66666666666666%;
}

.col-sm-pull-10
{
	right: 83.33333333333334%;
}

.col-sm-pull-9
{
	right: 75%;
}

.col-sm-pull-8
{
	right: 66.66666666666666%;
}

.col-sm-pull-7
{
	right: 58.333333333333336%;
}

.col-sm-pull-6
{
	right: 50%;
}

.col-sm-pull-5
{
	right: 41.66666666666667%;
}

.col-sm-pull-4
{
	right: 33.33333333333333%;
}

.col-sm-pull-3
{
	right: 25%;
}

.col-sm-pull-2
{
	right: 16.666666666666664%;
}

.col-sm-pull-1
{
	right: 8.333333333333332%;
}

.col-sm-pull-0
{
	right: 0%;
}

.col-sm-push-12
{
	left: 100%;
}

.col-sm-push-11
{
	left: 91.66666666666666%;
}

.col-sm-push-10
{
	left: 83.33333333333334%;
}

.col-sm-push-9
{
	left: 75%;
}

.col-sm-push-8
{
	left: 66.66666666666666%;
}

.col-sm-push-7
{
	left: 58.333333333333336%;
}

.col-sm-push-6
{
	left: 50%;
}

.col-sm-push-5
{
	left: 41.66666666666667%;
}

.col-sm-push-4
{
	left: 33.33333333333333%;
}

.col-sm-push-3
{
	left: 25%;
}

.col-sm-push-2
{
	left: 16.666666666666664%;
}

.col-sm-push-1
{
	left: 8.333333333333332%;
}

.col-sm-push-0
{
	left: 0%;
}

.col-sm-offset-12
{
	margin-left: 100%;
}

.col-sm-offset-11
{
	margin-left: 91.66666666666666%;
}

.col-sm-offset-10
{
	margin-left: 83.33333333333334%;
}

.col-sm-offset-9
{
	margin-left: 75%;
}

.col-sm-offset-8
{
	margin-left: 66.66666666666666%;
}

.col-sm-offset-7
{
	margin-left: 58.333333333333336%;
}

.col-sm-offset-6
{
	margin-left: 50%;
}

.col-sm-offset-5
{
	margin-left: 41.66666666666667%;
}

.col-sm-offset-4
{
	margin-left: 33.33333333333333%;
}

.col-sm-offset-3
{
	margin-left: 25%;
}

.col-sm-offset-2
{
	margin-left: 16.666666666666664%;
}

.col-sm-offset-1
{
	margin-left: 8.333333333333332%;
}

.col-sm-offset-0
{
	margin-left: 0%;
}

.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12
{
	float: left;
}

.col-md-12
{
	width: 100%;
}

.col-md-11
{
	width: 91.66666666666666%;
}

.col-md-10
{
	width: 83.33333333333334%;
}

.col-md-9
{
	width: 75%;
}

.col-md-8
{
	width: 66.66666666666666%;
}

.col-md-7
{
	width: 58.333333333333336%;
}

.col-md-6
{
	width: 50%;
}

.col-md-5
{
	width: 41.66666666666667%;
}

.col-md-4
{
	width: 33.33333333333333%;
}

.col-md-3
{
	width: 25%;
}

.col-md-2
{
	width: 16.666666666666664%;
}

.col-md-1
{
	width: 8.333333333333332%;
}

.col-md-pull-12
{
	right: 100%;
}

.col-md-pull-11
{
	right: 91.66666666666666%;
}

.col-md-pull-10
{
	right: 83.33333333333334%;
}

.col-md-pull-9
{
	right: 75%;
}

.col-md-pull-8
{
	right: 66.66666666666666%;
}

.col-md-pull-7
{
	right: 58.333333333333336%;
}

.col-md-pull-6
{
	right: 50%;
}

.col-md-pull-5
{
	right: 41.66666666666667%;
}

.col-md-pull-4
{
	right: 33.33333333333333%;
}

.col-md-pull-3
{
	right: 25%;
}

.col-md-pull-2
{
	right: 16.666666666666664%;
}

.col-md-pull-1
{
	right: 8.333333333333332%;
}

.col-md-pull-0
{
	right: 0%;
}

.col-md-push-12
{
	left: 100%;
}

.col-md-push-11
{
	left: 91.66666666666666%;
}

.col-md-push-10
{
	left: 83.33333333333334%;
}

.col-md-push-9
{
	left: 75%;
}

.col-md-push-8
{
	left: 66.66666666666666%;
}

.col-md-push-7
{
	left: 58.333333333333336%;
}

.col-md-push-6
{
	left: 50%;
}

.col-md-push-5
{
	left: 41.66666666666667%;
}

.col-md-push-4
{
	left: 33.33333333333333%;
}

.col-md-push-3
{
	left: 25%;
}

.col-md-push-2
{
	left: 16.666666666666664%;
}

.col-md-push-1
{
	left: 8.333333333333332%;
}

.col-md-push-0
{
	left: 0%;
}

.col-md-offset-12
{
	margin-left: 100%;
}

.col-md-offset-11
{
	margin-left: 91.66666666666666%;
}

.col-md-offset-10
{
	margin-left: 83.33333333333334%;
}

.col-md-offset-9
{
	margin-left: 75%;
}

.col-md-offset-8
{
	margin-left: 66.66666666666666%;
}

.col-md-offset-7
{
	margin-left: 58.333333333333336%;
}

.col-md-offset-6
{
	margin-left: 50%;
}

.col-md-offset-5
{
	margin-left: 41.66666666666667%;
}

.col-md-offset-4
{
	margin-left: 33.33333333333333%;
}

.col-md-offset-3
{
	margin-left: 25%;
}

.col-md-offset-2
{
	margin-left: 16.666666666666664%;
}

.col-md-offset-1
{
	margin-left: 8.333333333333332%;
}

.col-md-offset-0
{
	margin-left: 0%;
}

.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12
{
	float: left;
}

.col-lg-12
{
	width: 100%;
}

.col-lg-11
{
	width: 91.66666666666666%;
}

.col-lg-10
{
	width: 83.33333333333334%;
}

.col-lg-9
{
	width: 75%;
}

.col-lg-8
{
	width: 66.66666666666666%;
}

.col-lg-7
{
	width: 58.333333333333336%;
}

.col-lg-6
{
	width: 50%;
}

.col-lg-5
{
	width: 41.66666666666667%;
}

.col-lg-4
{
	width: 33.33333333333333%;
}

.col-lg-3
{
	width: 25%;
}

.col-lg-2
{
	width: 16.666666666666664%;
}

.col-lg-1
{
	width: 8.333333333333332%;
}

.col-lg-pull-12
{
	right: 100%;
}

.col-lg-pull-11
{
	right: 91.66666666666666%;
}

.col-lg-pull-10
{
	right: 83.33333333333334%;
}

.col-lg-pull-9
{
	right: 75%;
}

.col-lg-pull-8
{
	right: 66.66666666666666%;
}

.col-lg-pull-7
{
	right: 58.333333333333336%;
}

.col-lg-pull-6
{
	right: 50%;
}

.col-lg-pull-5
{
	right: 41.66666666666667%;
}

.col-lg-pull-4
{
	right: 33.33333333333333%;
}

.col-lg-pull-3
{
	right: 25%;
}

.col-lg-pull-2
{
	right: 16.666666666666664%;
}

.col-lg-pull-1
{
	right: 8.333333333333332%;
}

.col-lg-pull-0
{
	right: 0%;
}

.col-lg-push-12
{
	left: 100%;
}

.col-lg-push-11
{
	left: 91.66666666666666%;
}

.col-lg-push-10
{
	left: 83.33333333333334%;
}

.col-lg-push-9
{
	left: 75%;
}

.col-lg-push-8
{
	left: 66.66666666666666%;
}

.col-lg-push-7
{
	left: 58.333333333333336%;
}

.col-lg-push-6
{
	left: 50%;
}

.col-lg-push-5
{
	left: 41.66666666666667%;
}

.col-lg-push-4
{
	left: 33.33333333333333%;
}

.col-lg-push-3
{
	left: 25%;
}

.col-lg-push-2
{
	left: 16.666666666666664%;
}

.col-lg-push-1
{
	left: 8.333333333333332%;
}

.col-lg-push-0
{
	left: 0%;
}

.col-lg-offset-12
{
	margin-left: 100%;
}

.col-lg-offset-11
{
	margin-left: 91.66666666666666%;
}

.col-lg-offset-10
{
	margin-left: 83.33333333333334%;
}

.col-lg-offset-9
{
	margin-left: 75%;
}

.col-lg-offset-8
{
	margin-left: 66.66666666666666%;
}

.col-lg-offset-7
{
	margin-left: 58.333333333333336%;
}

.col-lg-offset-6
{
	margin-left: 50%;
}

.col-lg-offset-5
{
	margin-left: 41.66666666666667%;
}

.col-lg-offset-4
{
	margin-left: 33.33333333333333%;
}

.col-lg-offset-3
{
	margin-left: 25%;
}

.col-lg-offset-2
{
	margin-left: 16.666666666666664%;
}

.col-lg-offset-1
{
	margin-left: 8.333333333333332%;
}

.col-lg-offset-0
{
	margin-left: 0%;
}

.panel
{
	margin-bottom: 24px;
	background-color: #ffffff;
	border: 1px solid transparent;
	border-radius: 0px;
	-webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.panel-body
{
	padding: 15px;
}

.panel-body:before,
.panel-body:after
{
	content: " ";
	display: table;
}

.panel-body:after
{
	clear: both;
}

.panel-body:before,
.panel-body:after
{
	content: " ";
	display: table;
}

.panel-body:after
{
	clear: both;
}

.panel > .list-group
{
	margin-bottom: 0;
}

.panel > .list-group .list-group-item
{
	border-width: 1px 0;
}

.panel > .list-group .list-group-item:first-child
{
	border-top-right-radius: 0;
	border-top-left-radius: 0;
}

.panel > .list-group .list-group-item:last-child
{
	border-bottom: 0;
}

.panel-heading + .list-group .list-group-item:first-child
{
	border-top-width: 0;
}

.panel > .table,
.panel > .table-responsive > .table
{
	margin-bottom: 0;
}

.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive
{
	border-top: 1px solid #dddddd;
}

.panel > .table > tbody:first-child th,
.panel > .table > tbody:first-child td
{
	border-top: 0;
}

.panel > .table-bordered,
.panel > .table-responsive > .table-bordered
{
	border: 0;
}

.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child
{
	border-left: 0;
}

.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child
{
	border-right: 0;
}

.panel > .table-bordered > thead > tr:last-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:last-child > th,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-bordered > thead > tr:last-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td
{
	border-bottom: 0;
}

.panel > .table-responsive
{
	border: 0;
	margin-bottom: 0;
}

.panel-heading
{
	padding: 10px 15px;
	border-bottom: 1px solid transparent;
	border-top-right-radius: -1px;
	border-top-left-radius: -1px;
}

.panel-heading > .dropdown .dropdown-toggle
{
	color: inherit;
}

.panel-title
{
	margin-top: 0;
	margin-bottom: 0;
	font-size: 18px;
	color: inherit;
}

.panel-title > a
{
	color: inherit;
}

.panel-footer
{
	padding: 10px 15px;
	background-color: #f5f5f5;
	border-top: 1px solid #dddddd;
	border-bottom-right-radius: -1px;
	border-bottom-left-radius: -1px;
}

.panel-group .panel
{
	margin-bottom: 0;
	border-radius: 0px;
	overflow: hidden;
}

.panel-group .panel + .panel
{
	margin-top: 5px;
}

.panel-group .panel-heading
{
	border-bottom: 0;
}

.panel-group .panel-heading + .panel-collapse .panel-body
{
	border-top: 1px solid #dddddd;
}

.panel-group .panel-footer
{
	border-top: 0;
}

.panel-group .panel-footer + .panel-collapse .panel-body
{
	border-bottom: 1px solid #dddddd;
}

.panel-default
{
	border-color: #dddddd;
}

.panel-default > .panel-heading
{
	color: #595959;
	background-color: #f5f5f5;
	border-color: #dddddd;
}

.panel-default > .panel-heading + .panel-collapse .panel-body
{
	border-top-color: #dddddd;
}

.panel-default > .panel-footer + .panel-collapse .panel-body
{
	border-bottom-color: #dddddd;
}

.panel-primary
{
	border-color: #76b900;
}

.panel-primary > .panel-heading
{
	color: #ffffff;
	background-color: #76b900;
	border-color: #76b900;
}

.panel-primary > .panel-heading + .panel-collapse .panel-body
{
	border-top-color: #76b900;
}

.panel-primary > .panel-footer + .panel-collapse .panel-body
{
	border-bottom-color: #76b900;
}

.panel-success
{
	border-color: #d6e9c6;
}

.panel-success > .panel-heading
{
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #d6e9c6;
}

.panel-success > .panel-heading + .panel-collapse .panel-body
{
	border-top-color: #d6e9c6;
}

.panel-success > .panel-footer + .panel-collapse .panel-body
{
	border-bottom-color: #d6e9c6;
}

.panel-warning
{
	border-color: #faebcc;
}

.panel-warning > .panel-heading
{
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #faebcc;
}

.panel-warning > .panel-heading + .panel-collapse .panel-body
{
	border-top-color: #faebcc;
}

.panel-warning > .panel-footer + .panel-collapse .panel-body
{
	border-bottom-color: #faebcc;
}

.panel-danger
{
	border-color: #ebccd1;
}

.panel-danger > .panel-heading
{
	color: #a94442;
	background-color: #f2dede;
	border-color: #ebccd1;
}

.panel-danger > .panel-heading + .panel-collapse .panel-body
{
	border-top-color: #ebccd1;
}

.panel-danger > .panel-footer + .panel-collapse .panel-body
{
	border-bottom-color: #ebccd1;
}

.panel-info
{
	border-color: #bce8f1;
}

.panel-info > .panel-heading
{
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1;
}

.panel-info > .panel-heading + .panel-collapse .panel-body
{
	border-top-color: #bce8f1;
}

.panel-info > .panel-footer + .panel-collapse .panel-body
{
	border-bottom-color: #bce8f1;
}

.content-sidebar .panel-heading
{
	text-transform: uppercase;
	font-weight: bold;
}

.panel-success
{
	border-color: #76b900;
}

.panel-success > .panel-heading
{
	color: #ffffff;
	background-color: #76b900;
	border-color: #76b900;
}

.panel-success > .panel-heading + .panel-collapse .panel-body
{
	border-top-color: #76b900;
}

.panel-success > .panel-footer + .panel-collapse .panel-body
{
	border-bottom-color: #76b900;
}

.panel-success .panel-heading a
{
	color: white;
}

*,
*:before,
*:after
{
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

