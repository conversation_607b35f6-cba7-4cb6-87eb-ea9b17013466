<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel9 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel9 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel9" -->The activity record for kernel.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#8324794b80bc7af2357b11bf98482563">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#e63b0aa5f53b77a726b2255ee04cdc38">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#fbb9f5ef358bc22707af93382d4917b2">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#484e2e6fee4b52ac80c8f82600b14c05">cacheConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#2701b9391c7df708cab1ace32f2f82c3">channelID</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUpti_ChannelType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#b2e006dae1847d08fe6383522965de17">channelType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#1627f9801e194c2953bb8cef9e0d2413">clusterSchedulingPolicy</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#c6cd17b05835250e63194e34cbc01ec0">clusterX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#f778d76a60d7644eacbffc751675e4fd">clusterY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#229d7bbb49b5969b41298b0729ac1cc4">clusterZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#f5ebb35b29cb59bbd816c07186eadd25">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#6dcfb7ced4d8cbe54ec02a140ab4820a">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#ceab5916ba047e3c97e104723e5510d7">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#29e448235bf3b29081011b5735e1f74d">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#bf4c9661e7605bac8b1e6711414e43e6">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#2bc2f653e4279eeb4e1542841848636c">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#4150c4de603a15606d9786f417e0f6ce">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#949121c74e9bd41ae3a20a22ab1fa58d">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#f7d35a11c8026e19123ae032d99f0b85">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#a8a2a26a73a7a72da59144250d19e7c2">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#d8998a1084c2be17faafd27d44d0ea4d">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#406407315ed8674d8ad307427b0dcc1d">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#fd77444d00d44ff97b47470d0879a2ee">isSharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#f66b2981ffeef483e90b0c889c4a9fee">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#f32b1561e7bf8757d0026aaa211171b0">launchType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#b0d7eb482d3c018abe19797f4a83e9b2">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#7913766967c1a846b0ec1c43eb6177f8">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#b3f7f2cb12fe06a5f696d11077ddf4f4">localMemoryTotal_v2</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#329bb107d58507c58c53f02b306d74cd">maxActiveClusters</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#324c0a262fdb886f2ca68bebe3839ea3">maxPotentialClusterSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#e2fa5eeb7e2b97e535cb744a5a82c8e7">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUaccessPolicyWindow *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#6e1f1e578bb63d068c7ba37641c08ed5">pAccessPolicyWindow</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#a474a5f04c0ccf8a82f37029faa171c5">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#d38470cc9c074c4f8f99473d968d3f44">partitionedGlobalCacheExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#067f0363d37d4997d5a3695f9ed383d9">partitionedGlobalCacheRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#7750d0b404e087c359ed7310360be96d">queued</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#fc48a033c27e576d7181e866f8a1745f">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#af675ae91db73fd3a1205a64c0529976">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#4063f9ca2d050f4aea14dc61663d64af">sharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#f58e06fed66f3626b8328f9caa5cc675">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#7a83af8f0143cdd49298eceda91ddf60">sharedMemoryExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#e894e90f1d092c40becb0f8e3b32ae10">shmemLimitConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#de576f758b208a441bf34736d2d5f848">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#79eca25f9d20c68c4195379378936cc1">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#c1a797c8b73f28e0a029bb56a463e149">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#2789727f1a83cf45a3230172887be4a4">submitted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#91844f8055d4b82dac07ddf422cda673">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel9.html#0987b28950296d2989c521136e07c7ad">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) <hr><h2>Field Documentation</h2>
<a class="anchor" name="8324794b80bc7af2357b11bf98482563"></a><!-- doxytag: member="CUpti_ActivityKernel9::blockX" ref="8324794b80bc7af2357b11bf98482563" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#8324794b80bc7af2357b11bf98482563">CUpti_ActivityKernel9::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="e63b0aa5f53b77a726b2255ee04cdc38"></a><!-- doxytag: member="CUpti_ActivityKernel9::blockY" ref="e63b0aa5f53b77a726b2255ee04cdc38" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#e63b0aa5f53b77a726b2255ee04cdc38">CUpti_ActivityKernel9::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="fbb9f5ef358bc22707af93382d4917b2"></a><!-- doxytag: member="CUpti_ActivityKernel9::blockZ" ref="fbb9f5ef358bc22707af93382d4917b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#fbb9f5ef358bc22707af93382d4917b2">CUpti_ActivityKernel9::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="484e2e6fee4b52ac80c8f82600b14c05"></a><!-- doxytag: member="CUpti_ActivityKernel9::cacheConfig" ref="484e2e6fee4b52ac80c8f82600b14c05" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityKernel9.html#484e2e6fee4b52ac80c8f82600b14c05">CUpti_ActivityKernel9::cacheConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For devices with compute capability 7.0+ cacheConfig values are not updated in case field isSharedMemoryCarveoutRequested is set 
</div>
</div><p>
<a class="anchor" name="2701b9391c7df708cab1ace32f2f82c3"></a><!-- doxytag: member="CUpti_ActivityKernel9::channelID" ref="2701b9391c7df708cab1ace32f2f82c3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#2701b9391c7df708cab1ace32f2f82c3">CUpti_ActivityKernel9::channelID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the HW channel on which the kernel is launched. 
</div>
</div><p>
<a class="anchor" name="b2e006dae1847d08fe6383522965de17"></a><!-- doxytag: member="CUpti_ActivityKernel9::channelType" ref="b2e006dae1847d08fe6383522965de17" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUpti_ChannelType <a class="el" href="structCUpti__ActivityKernel9.html#b2e006dae1847d08fe6383522965de17">CUpti_ActivityKernel9::channelType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the channel 
</div>
</div><p>
<a class="anchor" name="1627f9801e194c2953bb8cef9e0d2413"></a><!-- doxytag: member="CUpti_ActivityKernel9::clusterSchedulingPolicy" ref="1627f9801e194c2953bb8cef9e0d2413" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#1627f9801e194c2953bb8cef9e0d2413">CUpti_ActivityKernel9::clusterSchedulingPolicy</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cluster scheduling policy for the kernel. Refer CUclusterSchedulingPolicy Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="c6cd17b05835250e63194e34cbc01ec0"></a><!-- doxytag: member="CUpti_ActivityKernel9::clusterX" ref="c6cd17b05835250e63194e34cbc01ec0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#c6cd17b05835250e63194e34cbc01ec0">CUpti_ActivityKernel9::clusterX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension cluster size for the kernel. Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="f778d76a60d7644eacbffc751675e4fd"></a><!-- doxytag: member="CUpti_ActivityKernel9::clusterY" ref="f778d76a60d7644eacbffc751675e4fd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#f778d76a60d7644eacbffc751675e4fd">CUpti_ActivityKernel9::clusterY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension cluster size for the kernel. Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="229d7bbb49b5969b41298b0729ac1cc4"></a><!-- doxytag: member="CUpti_ActivityKernel9::clusterZ" ref="229d7bbb49b5969b41298b0729ac1cc4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#229d7bbb49b5969b41298b0729ac1cc4">CUpti_ActivityKernel9::clusterZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension cluster size for the kernel. Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="f5ebb35b29cb59bbd816c07186eadd25"></a><!-- doxytag: member="CUpti_ActivityKernel9::completed" ref="f5ebb35b29cb59bbd816c07186eadd25" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel9.html#f5ebb35b29cb59bbd816c07186eadd25">CUpti_ActivityKernel9::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="6dcfb7ced4d8cbe54ec02a140ab4820a"></a><!-- doxytag: member="CUpti_ActivityKernel9::contextId" ref="6dcfb7ced4d8cbe54ec02a140ab4820a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#6dcfb7ced4d8cbe54ec02a140ab4820a">CUpti_ActivityKernel9::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="ceab5916ba047e3c97e104723e5510d7"></a><!-- doxytag: member="CUpti_ActivityKernel9::correlationId" ref="ceab5916ba047e3c97e104723e5510d7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#ceab5916ba047e3c97e104723e5510d7">CUpti_ActivityKernel9::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="29e448235bf3b29081011b5735e1f74d"></a><!-- doxytag: member="CUpti_ActivityKernel9::deviceId" ref="29e448235bf3b29081011b5735e1f74d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#29e448235bf3b29081011b5735e1f74d">CUpti_ActivityKernel9::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="bf4c9661e7605bac8b1e6711414e43e6"></a><!-- doxytag: member="CUpti_ActivityKernel9::dynamicSharedMemory" ref="bf4c9661e7605bac8b1e6711414e43e6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#bf4c9661e7605bac8b1e6711414e43e6">CUpti_ActivityKernel9::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="2bc2f653e4279eeb4e1542841848636c"></a><!-- doxytag: member="CUpti_ActivityKernel9::end" ref="2bc2f653e4279eeb4e1542841848636c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel9.html#2bc2f653e4279eeb4e1542841848636c">CUpti_ActivityKernel9::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="91844f8055d4b82dac07ddf422cda673"></a><!-- doxytag: member="CUpti_ActivityKernel9::executed" ref="91844f8055d4b82dac07ddf422cda673" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel9.html#91844f8055d4b82dac07ddf422cda673">CUpti_ActivityKernel9::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="4150c4de603a15606d9786f417e0f6ce"></a><!-- doxytag: member="CUpti_ActivityKernel9::graphId" ref="4150c4de603a15606d9786f417e0f6ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#4150c4de603a15606d9786f417e0f6ce">CUpti_ActivityKernel9::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="949121c74e9bd41ae3a20a22ab1fa58d"></a><!-- doxytag: member="CUpti_ActivityKernel9::graphNodeId" ref="949121c74e9bd41ae3a20a22ab1fa58d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel9.html#949121c74e9bd41ae3a20a22ab1fa58d">CUpti_ActivityKernel9::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="f7d35a11c8026e19123ae032d99f0b85"></a><!-- doxytag: member="CUpti_ActivityKernel9::gridId" ref="f7d35a11c8026e19123ae032d99f0b85" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel9.html#f7d35a11c8026e19123ae032d99f0b85">CUpti_ActivityKernel9::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="a8a2a26a73a7a72da59144250d19e7c2"></a><!-- doxytag: member="CUpti_ActivityKernel9::gridX" ref="a8a2a26a73a7a72da59144250d19e7c2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#a8a2a26a73a7a72da59144250d19e7c2">CUpti_ActivityKernel9::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="d8998a1084c2be17faafd27d44d0ea4d"></a><!-- doxytag: member="CUpti_ActivityKernel9::gridY" ref="d8998a1084c2be17faafd27d44d0ea4d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#d8998a1084c2be17faafd27d44d0ea4d">CUpti_ActivityKernel9::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="406407315ed8674d8ad307427b0dcc1d"></a><!-- doxytag: member="CUpti_ActivityKernel9::gridZ" ref="406407315ed8674d8ad307427b0dcc1d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#406407315ed8674d8ad307427b0dcc1d">CUpti_ActivityKernel9::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="fd77444d00d44ff97b47470d0879a2ee"></a><!-- doxytag: member="CUpti_ActivityKernel9::isSharedMemoryCarveoutRequested" ref="fd77444d00d44ff97b47470d0879a2ee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel9.html#fd77444d00d44ff97b47470d0879a2ee">CUpti_ActivityKernel9::isSharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This indicates if CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT was updated for the kernel launch 
</div>
</div><p>
<a class="anchor" name="f66b2981ffeef483e90b0c889c4a9fee"></a><!-- doxytag: member="CUpti_ActivityKernel9::kind" ref="f66b2981ffeef483e90b0c889c4a9fee" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel9.html#f66b2981ffeef483e90b0c889c4a9fee">CUpti_ActivityKernel9::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="f32b1561e7bf8757d0026aaa211171b0"></a><!-- doxytag: member="CUpti_ActivityKernel9::launchType" ref="f32b1561e7bf8757d0026aaa211171b0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel9.html#f32b1561e7bf8757d0026aaa211171b0">CUpti_ActivityKernel9::launchType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The indicates if the kernel was executed via a regular launch or via a single/multi device cooperative launch. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c" title="The type of the CUDA kernel launch.">CUpti_ActivityLaunchType</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="b0d7eb482d3c018abe19797f4a83e9b2"></a><!-- doxytag: member="CUpti_ActivityKernel9::localMemoryPerThread" ref="b0d7eb482d3c018abe19797f4a83e9b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#b0d7eb482d3c018abe19797f4a83e9b2">CUpti_ActivityKernel9::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="7913766967c1a846b0ec1c43eb6177f8"></a><!-- doxytag: member="CUpti_ActivityKernel9::localMemoryTotal" ref="7913766967c1a846b0ec1c43eb6177f8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#7913766967c1a846b0ec1c43eb6177f8">CUpti_ActivityKernel9::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes (deprecated in CUDA 11.8). Refer field localMemoryTotal_v2 
</div>
</div><p>
<a class="anchor" name="b3f7f2cb12fe06a5f696d11077ddf4f4"></a><!-- doxytag: member="CUpti_ActivityKernel9::localMemoryTotal_v2" ref="b3f7f2cb12fe06a5f696d11077ddf4f4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel9.html#b3f7f2cb12fe06a5f696d11077ddf4f4">CUpti_ActivityKernel9::localMemoryTotal_v2</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="329bb107d58507c58c53f02b306d74cd"></a><!-- doxytag: member="CUpti_ActivityKernel9::maxActiveClusters" ref="329bb107d58507c58c53f02b306d74cd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#329bb107d58507c58c53f02b306d74cd">CUpti_ActivityKernel9::maxActiveClusters</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The maximum clusters that could co-exist on the target device for the kernel 
</div>
</div><p>
<a class="anchor" name="324c0a262fdb886f2ca68bebe3839ea3"></a><!-- doxytag: member="CUpti_ActivityKernel9::maxPotentialClusterSize" ref="324c0a262fdb886f2ca68bebe3839ea3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#324c0a262fdb886f2ca68bebe3839ea3">CUpti_ActivityKernel9::maxPotentialClusterSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The maximum cluster size for the kernel 
</div>
</div><p>
<a class="anchor" name="e2fa5eeb7e2b97e535cb744a5a82c8e7"></a><!-- doxytag: member="CUpti_ActivityKernel9::name" ref="e2fa5eeb7e2b97e535cb744a5a82c8e7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel9.html#e2fa5eeb7e2b97e535cb744a5a82c8e7">CUpti_ActivityKernel9::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="6e1f1e578bb63d068c7ba37641c08ed5"></a><!-- doxytag: member="CUpti_ActivityKernel9::pAccessPolicyWindow" ref="6e1f1e578bb63d068c7ba37641c08ed5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUaccessPolicyWindow* <a class="el" href="structCUpti__ActivityKernel9.html#6e1f1e578bb63d068c7ba37641c08ed5">CUpti_ActivityKernel9::pAccessPolicyWindow</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pointer to the access policy window. The structure CUaccessPolicyWindow is defined in cuda.h. 
</div>
</div><p>
<a class="anchor" name="a474a5f04c0ccf8a82f37029faa171c5"></a><!-- doxytag: member="CUpti_ActivityKernel9::padding" ref="a474a5f04c0ccf8a82f37029faa171c5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel9.html#a474a5f04c0ccf8a82f37029faa171c5">CUpti_ActivityKernel9::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="d38470cc9c074c4f8f99473d968d3f44"></a><!-- doxytag: member="CUpti_ActivityKernel9::partitionedGlobalCacheExecuted" ref="d38470cc9c074c4f8f99473d968d3f44" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel9.html#d38470cc9c074c4f8f99473d968d3f44">CUpti_ActivityKernel9::partitionedGlobalCacheExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching executed for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. Partitioned global caching can be automatically disabled if the occupancy requirement of the launch cannot support caching. 
</div>
</div><p>
<a class="anchor" name="067f0363d37d4997d5a3695f9ed383d9"></a><!-- doxytag: member="CUpti_ActivityKernel9::partitionedGlobalCacheRequested" ref="067f0363d37d4997d5a3695f9ed383d9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel9.html#067f0363d37d4997d5a3695f9ed383d9">CUpti_ActivityKernel9::partitionedGlobalCacheRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching requested for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. 
</div>
</div><p>
<a class="anchor" name="7750d0b404e087c359ed7310360be96d"></a><!-- doxytag: member="CUpti_ActivityKernel9::queued" ref="7750d0b404e087c359ed7310360be96d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel9.html#7750d0b404e087c359ed7310360be96d">CUpti_ActivityKernel9::queued</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the kernel is queued up in the command buffer, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the queued time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection.<p>
Command buffer is a buffer written by CUDA driver to send commands like kernel launch, memory copy etc to the GPU. All launches of CUDA kernels are asynchrnous with respect to the host, the host requests the launch by writing commands into the command buffer, then returns without checking the GPU's progress. 
</div>
</div><p>
<a class="anchor" name="fc48a033c27e576d7181e866f8a1745f"></a><!-- doxytag: member="CUpti_ActivityKernel9::registersPerThread" ref="fc48a033c27e576d7181e866f8a1745f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel9.html#fc48a033c27e576d7181e866f8a1745f">CUpti_ActivityKernel9::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="0987b28950296d2989c521136e07c7ad"></a><!-- doxytag: member="CUpti_ActivityKernel9::requested" ref="0987b28950296d2989c521136e07c7ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel9.html#0987b28950296d2989c521136e07c7ad">CUpti_ActivityKernel9::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="af675ae91db73fd3a1205a64c0529976"></a><!-- doxytag: member="CUpti_ActivityKernel9::reserved0" ref="af675ae91db73fd3a1205a64c0529976" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel9.html#af675ae91db73fd3a1205a64c0529976">CUpti_ActivityKernel9::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="4063f9ca2d050f4aea14dc61663d64af"></a><!-- doxytag: member="CUpti_ActivityKernel9::sharedMemoryCarveoutRequested" ref="4063f9ca2d050f4aea14dc61663d64af" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel9.html#4063f9ca2d050f4aea14dc61663d64af">CUpti_ActivityKernel9::sharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory carveout value requested for the function in percentage of the total resource. The value will be updated only if field isSharedMemoryCarveoutRequested is set. 
</div>
</div><p>
<a class="anchor" name="f58e06fed66f3626b8328f9caa5cc675"></a><!-- doxytag: member="CUpti_ActivityKernel9::sharedMemoryConfig" ref="f58e06fed66f3626b8328f9caa5cc675" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel9.html#f58e06fed66f3626b8328f9caa5cc675">CUpti_ActivityKernel9::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="7a83af8f0143cdd49298eceda91ddf60"></a><!-- doxytag: member="CUpti_ActivityKernel9::sharedMemoryExecuted" ref="7a83af8f0143cdd49298eceda91ddf60" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#7a83af8f0143cdd49298eceda91ddf60">CUpti_ActivityKernel9::sharedMemoryExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory size set by the driver. 
</div>
</div><p>
<a class="anchor" name="e894e90f1d092c40becb0f8e3b32ae10"></a><!-- doxytag: member="CUpti_ActivityKernel9::shmemLimitConfig" ref="e894e90f1d092c40becb0f8e3b32ae10" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a> <a class="el" href="structCUpti__ActivityKernel9.html#e894e90f1d092c40becb0f8e3b32ae10">CUpti_ActivityKernel9::shmemLimitConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory limit config for the kernel. This field shows whether user has opted for a higher per block limit of dynamic shared memory. 
</div>
</div><p>
<a class="anchor" name="de576f758b208a441bf34736d2d5f848"></a><!-- doxytag: member="CUpti_ActivityKernel9::start" ref="de576f758b208a441bf34736d2d5f848" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel9.html#de576f758b208a441bf34736d2d5f848">CUpti_ActivityKernel9::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="79eca25f9d20c68c4195379378936cc1"></a><!-- doxytag: member="CUpti_ActivityKernel9::staticSharedMemory" ref="79eca25f9d20c68c4195379378936cc1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel9.html#79eca25f9d20c68c4195379378936cc1">CUpti_ActivityKernel9::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="c1a797c8b73f28e0a029bb56a463e149"></a><!-- doxytag: member="CUpti_ActivityKernel9::streamId" ref="c1a797c8b73f28e0a029bb56a463e149" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel9.html#c1a797c8b73f28e0a029bb56a463e149">CUpti_ActivityKernel9::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="2789727f1a83cf45a3230172887be4a4"></a><!-- doxytag: member="CUpti_ActivityKernel9::submitted" ref="2789727f1a83cf45a3230172887be4a4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel9.html#2789727f1a83cf45a3230172887be4a4">CUpti_ActivityKernel9::submitted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the command buffer containing the kernel launch is submitted to the GPU, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the submitted time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
