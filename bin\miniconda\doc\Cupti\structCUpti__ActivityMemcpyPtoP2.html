<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpyPtoP2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpyPtoP2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpyPtoP2" -->The activity record for peer-to-peer memory copies. (deprecated in CUDA 11.1).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#8e42bb722578733594dfb27ff00456f1">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#a6c6d3a1c5d50d3248ff43dc401d6e6d">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#7a6e67c7b0e830508dcbc189ff1fd096">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#4319db4c6f7d6bd328cf1d4c6e69aa0b">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#272242233caa3fd910fb847a23475b93">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#831bb2dfd6b0f21a63e94c5e1f8d35a6">dstContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#5bda87a6c35d5bc3e34771b731756015">dstDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#7524b7a87642b2d4ca83dff31754bf09">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#96aff067a38adcd032bb1ac4b14a1805">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#01c6ab05b31cbffe53f0e1dfe93c25d6">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#53eb37cdc545024fe55a30e1bea8b94b">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#95c41198c6c95b726972dec8fe6a4eb8">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#27c8d2dec0c353073d65b938c73c6875">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#b9f63f0159ec44cf961c31c2b571be8d">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#629f91a401bdb94b3120da538a753e98">srcContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#52a2d35f9457468f5dc98ead20834916">srcDeviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#e011ef9aafabfd607faff943f22892f0">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#b0627b707c6df921c414e3c55c4995a9">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#a1db957966dabf0d20ec0bfc07fbd25f">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a peer-to-peer memory copy (CUPTI_ACTIVITY_KIND_MEMCPY2). <hr><h2>Field Documentation</h2>
<a class="anchor" name="8e42bb722578733594dfb27ff00456f1"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::bytes" ref="8e42bb722578733594dfb27ff00456f1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#8e42bb722578733594dfb27ff00456f1">CUpti_ActivityMemcpyPtoP2::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="a6c6d3a1c5d50d3248ff43dc401d6e6d"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::contextId" ref="a6c6d3a1c5d50d3248ff43dc401d6e6d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#a6c6d3a1c5d50d3248ff43dc401d6e6d">CUpti_ActivityMemcpyPtoP2::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="7a6e67c7b0e830508dcbc189ff1fd096"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::copyKind" ref="7a6e67c7b0e830508dcbc189ff1fd096" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#7a6e67c7b0e830508dcbc189ff1fd096">CUpti_ActivityMemcpyPtoP2::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="4319db4c6f7d6bd328cf1d4c6e69aa0b"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::correlationId" ref="4319db4c6f7d6bd328cf1d4c6e69aa0b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#4319db4c6f7d6bd328cf1d4c6e69aa0b">CUpti_ActivityMemcpyPtoP2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver and runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="272242233caa3fd910fb847a23475b93"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::deviceId" ref="272242233caa3fd910fb847a23475b93" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#272242233caa3fd910fb847a23475b93">CUpti_ActivityMemcpyPtoP2::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="831bb2dfd6b0f21a63e94c5e1f8d35a6"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::dstContextId" ref="831bb2dfd6b0f21a63e94c5e1f8d35a6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#831bb2dfd6b0f21a63e94c5e1f8d35a6">CUpti_ActivityMemcpyPtoP2::dstContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied to. 
</div>
</div><p>
<a class="anchor" name="5bda87a6c35d5bc3e34771b731756015"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::dstDeviceId" ref="5bda87a6c35d5bc3e34771b731756015" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#5bda87a6c35d5bc3e34771b731756015">CUpti_ActivityMemcpyPtoP2::dstDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied to. 
</div>
</div><p>
<a class="anchor" name="7524b7a87642b2d4ca83dff31754bf09"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::dstKind" ref="7524b7a87642b2d4ca83dff31754bf09" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#7524b7a87642b2d4ca83dff31754bf09">CUpti_ActivityMemcpyPtoP2::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="96aff067a38adcd032bb1ac4b14a1805"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::end" ref="96aff067a38adcd032bb1ac4b14a1805" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#96aff067a38adcd032bb1ac4b14a1805">CUpti_ActivityMemcpyPtoP2::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="01c6ab05b31cbffe53f0e1dfe93c25d6"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::flags" ref="01c6ab05b31cbffe53f0e1dfe93c25d6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#01c6ab05b31cbffe53f0e1dfe93c25d6">CUpti_ActivityMemcpyPtoP2::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="53eb37cdc545024fe55a30e1bea8b94b"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::graphNodeId" ref="53eb37cdc545024fe55a30e1bea8b94b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#53eb37cdc545024fe55a30e1bea8b94b">CUpti_ActivityMemcpyPtoP2::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed the memcpy through graph launch. This field will be 0 if memcpy is not done using graph launch. 
</div>
</div><p>
<a class="anchor" name="95c41198c6c95b726972dec8fe6a4eb8"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::kind" ref="95c41198c6c95b726972dec8fe6a4eb8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#95c41198c6c95b726972dec8fe6a4eb8">CUpti_ActivityMemcpyPtoP2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY2. 
</div>
</div><p>
<a class="anchor" name="27c8d2dec0c353073d65b938c73c6875"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::pad" ref="27c8d2dec0c353073d65b938c73c6875" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#27c8d2dec0c353073d65b938c73c6875">CUpti_ActivityMemcpyPtoP2::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="b9f63f0159ec44cf961c31c2b571be8d"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::reserved0" ref="b9f63f0159ec44cf961c31c2b571be8d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#b9f63f0159ec44cf961c31c2b571be8d">CUpti_ActivityMemcpyPtoP2::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="629f91a401bdb94b3120da538a753e98"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::srcContextId" ref="629f91a401bdb94b3120da538a753e98" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#629f91a401bdb94b3120da538a753e98">CUpti_ActivityMemcpyPtoP2::srcContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context owning the memory being copied from. 
</div>
</div><p>
<a class="anchor" name="52a2d35f9457468f5dc98ead20834916"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::srcDeviceId" ref="52a2d35f9457468f5dc98ead20834916" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#52a2d35f9457468f5dc98ead20834916">CUpti_ActivityMemcpyPtoP2::srcDeviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where memory is being copied from. 
</div>
</div><p>
<a class="anchor" name="e011ef9aafabfd607faff943f22892f0"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::srcKind" ref="e011ef9aafabfd607faff943f22892f0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#e011ef9aafabfd607faff943f22892f0">CUpti_ActivityMemcpyPtoP2::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="b0627b707c6df921c414e3c55c4995a9"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::start" ref="b0627b707c6df921c414e3c55c4995a9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#b0627b707c6df921c414e3c55c4995a9">CUpti_ActivityMemcpyPtoP2::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="a1db957966dabf0d20ec0bfc07fbd25f"></a><!-- doxytag: member="CUpti_ActivityMemcpyPtoP2::streamId" ref="a1db957966dabf0d20ec0bfc07fbd25f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpyPtoP2.html#a1db957966dabf0d20ec0bfc07fbd25f">CUpti_ActivityMemcpyPtoP2::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
