<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivitySharedAccess Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivitySharedAccess Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivitySharedAccess" -->The activity record for source-level shared access.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#68f4573a206d6e8fef2bb2268a12369b">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#a71e5396c4336e63ef2f6a135383fd2b">executed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#64d92d1f3a816f6e4c8194580b65c7cc">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#5a984df75a3ac618198eaed58b8e94de">functionId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#3dcbf0be670e04dddb9a208e5d322a0b">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#2581ac8c7f8d2d97f64eace29f888662">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#f9450a9c16d9c545a1cdb3b5909ab542">pcOffset</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#58ea172f2c23f9e2a5484d182f0f8439">sharedTransactions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#b4a566843c7034846d3dafcbebef51de">sourceLocatorId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#89882826e508fc592e58876ccc196b22">theoreticalSharedTransactions</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySharedAccess.html#8b227d1cbbb2c9eabcd037724f69a6a7">threadsExecuted</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity records the locations of the shared accesses in the source (CUPTI_ACTIVITY_KIND_SHARED_ACCESS). <hr><h2>Field Documentation</h2>
<a class="anchor" name="68f4573a206d6e8fef2bb2268a12369b"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::correlationId" ref="68f4573a206d6e8fef2bb2268a12369b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySharedAccess.html#68f4573a206d6e8fef2bb2268a12369b">CUpti_ActivitySharedAccess::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="a71e5396c4336e63ef2f6a135383fd2b"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::executed" ref="a71e5396c4336e63ef2f6a135383fd2b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySharedAccess.html#a71e5396c4336e63ef2f6a135383fd2b">CUpti_ActivitySharedAccess::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of times this instruction was executed per warp. It will be incremented when at least one of thread among warp is active with predicate and condition code evaluating to true. 
</div>
</div><p>
<a class="anchor" name="64d92d1f3a816f6e4c8194580b65c7cc"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::flags" ref="64d92d1f3a816f6e4c8194580b65c7cc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivitySharedAccess.html#64d92d1f3a816f6e4c8194580b65c7cc">CUpti_ActivitySharedAccess::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this shared access. 
</div>
</div><p>
<a class="anchor" name="5a984df75a3ac618198eaed58b8e94de"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::functionId" ref="5a984df75a3ac618198eaed58b8e94de" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySharedAccess.html#5a984df75a3ac618198eaed58b8e94de">CUpti_ActivitySharedAccess::functionId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Correlation ID with global/device function name 
</div>
</div><p>
<a class="anchor" name="3dcbf0be670e04dddb9a208e5d322a0b"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::kind" ref="3dcbf0be670e04dddb9a208e5d322a0b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivitySharedAccess.html#3dcbf0be670e04dddb9a208e5d322a0b">CUpti_ActivitySharedAccess::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_SHARED_ACCESS. 
</div>
</div><p>
<a class="anchor" name="2581ac8c7f8d2d97f64eace29f888662"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::pad" ref="2581ac8c7f8d2d97f64eace29f888662" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySharedAccess.html#2581ac8c7f8d2d97f64eace29f888662">CUpti_ActivitySharedAccess::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="f9450a9c16d9c545a1cdb3b5909ab542"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::pcOffset" ref="f9450a9c16d9c545a1cdb3b5909ab542" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySharedAccess.html#f9450a9c16d9c545a1cdb3b5909ab542">CUpti_ActivitySharedAccess::pcOffset</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pc offset for the access. 
</div>
</div><p>
<a class="anchor" name="58ea172f2c23f9e2a5484d182f0f8439"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::sharedTransactions" ref="58ea172f2c23f9e2a5484d182f0f8439" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivitySharedAccess.html#58ea172f2c23f9e2a5484d182f0f8439">CUpti_ActivitySharedAccess::sharedTransactions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total number of shared memory transactions generated by this access 
</div>
</div><p>
<a class="anchor" name="b4a566843c7034846d3dafcbebef51de"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::sourceLocatorId" ref="b4a566843c7034846d3dafcbebef51de" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySharedAccess.html#b4a566843c7034846d3dafcbebef51de">CUpti_ActivitySharedAccess::sourceLocatorId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID for source locator. 
</div>
</div><p>
<a class="anchor" name="89882826e508fc592e58876ccc196b22"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::theoreticalSharedTransactions" ref="89882826e508fc592e58876ccc196b22" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivitySharedAccess.html#89882826e508fc592e58876ccc196b22">CUpti_ActivitySharedAccess::theoreticalSharedTransactions</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The minimum number of shared memory transactions possible based on the access pattern. 
</div>
</div><p>
<a class="anchor" name="8b227d1cbbb2c9eabcd037724f69a6a7"></a><!-- doxytag: member="CUpti_ActivitySharedAccess::threadsExecuted" ref="8b227d1cbbb2c9eabcd037724f69a6a7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivitySharedAccess.html#8b227d1cbbb2c9eabcd037724f69a6a7">CUpti_ActivitySharedAccess::threadsExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This increments each time when this instruction is executed by number of threads that executed this instruction with predicate and condition code evaluating to true. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
