{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": ["cuda-demo-suite >=12.1.55", "cuda-runtime >=12.1.0", "cuda-toolkit >=12.1.0"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-12.1.0-0", "features": "", "files": [], "fn": "cuda-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-12.1.0-0", "type": 1}, "md5": "735eb91307a9f9279b3bf748737ae7b8", "name": "cuda", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "cuda", "sha256": "", "size": 1411, "subdir": "win-64", "timestamp": 1677130091000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-12.1.0-0.tar.bz2", "version": "12.1.0"}