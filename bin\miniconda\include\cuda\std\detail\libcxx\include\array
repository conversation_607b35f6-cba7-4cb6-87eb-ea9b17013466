// -*- C++ -*-
//===---------------------------- array -----------------------------------===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _LIBCUDACXX_ARRAY
#define _LIBCUDACXX_ARRAY

/*
    array synopsis

namespace std
{
template <class T, size_t N >
struct array
{
    // types:
    typedef T & reference;
    typedef const T & const_reference;
    typedef implementation defined iterator;
    typedef implementation defined const_iterator;
    typedef size_t size_type;
    typedef ptrdiff_t difference_type;
    typedef T value_type;
    typedef T* pointer;
    typedef const T* const_pointer;
    typedef std::reverse_iterator<iterator> reverse_iterator;
    typedef std::reverse_iterator<const_iterator> const_reverse_iterator;

    // No explicit construct/copy/destroy for aggregate type
    void fill(const T& u);
    void swap(array& a) noexcept(is_nothrow_swappable_v<T>);

    // iterators:
    iterator begin() noexcept;
    const_iterator begin() const noexcept;
    iterator end() noexcept;
    const_iterator end() const noexcept;

    reverse_iterator rbegin() noexcept;
    const_reverse_iterator rbegin() const noexcept;
    reverse_iterator rend() noexcept;
    const_reverse_iterator rend() const noexcept;

    const_iterator cbegin() const noexcept;
    const_iterator cend() const noexcept;
    const_reverse_iterator crbegin() const noexcept;
    const_reverse_iterator crend() const noexcept;

    // capacity:
    constexpr size_type size() const noexcept;
    constexpr size_type max_size() const noexcept;
    constexpr bool empty() const noexcept;

    // element access:
    reference operator[](size_type n);
    const_reference operator[](size_type n) const; // constexpr in C++14
    const_reference at(size_type n) const; // constexpr in C++14
    reference at(size_type n);

    reference front();
    const_reference front() const; // constexpr in C++14
    reference back();
    const_reference back() const; // constexpr in C++14

    T* data() noexcept;
    const T* data() const noexcept;
};

  template <class T, class... U>
    array(T, U...) -> array<T, 1 + sizeof...(U)>;

template <class T, size_t N>
  bool operator==(const array<T,N>& x, const array<T,N>& y);
template <class T, size_t N>
  bool operator!=(const array<T,N>& x, const array<T,N>& y);
template <class T, size_t N>
  bool operator<(const array<T,N>& x, const array<T,N>& y);
template <class T, size_t N>
  bool operator>(const array<T,N>& x, const array<T,N>& y);
template <class T, size_t N>
  bool operator<=(const array<T,N>& x, const array<T,N>& y);
template <class T, size_t N>
  bool operator>=(const array<T,N>& x, const array<T,N>& y);

template <class T, size_t N >
  void swap(array<T,N>& x, array<T,N>& y) noexcept(noexcept(x.swap(y))); // C++17

template <class T> struct tuple_size;
template <size_t I, class T> struct tuple_element;
template <class T, size_t N> struct tuple_size<array<T, N>>;
template <size_t I, class T, size_t N> struct tuple_element<I, array<T, N>>;
template <size_t I, class T, size_t N> T& get(array<T, N>&) noexcept; // constexpr in C++14
template <size_t I, class T, size_t N> const T& get(const array<T, N>&) noexcept; // constexpr in C++14
template <size_t I, class T, size_t N> T&& get(array<T, N>&&) noexcept; // constexpr in C++14
template <size_t I, class T, size_t N> const T&& get(const array<T, N>&&) noexcept; // constexpr in C++14

}  // std

*/

#ifndef __cuda_std__
#include <__config>
#include <__tuple>
#include <type_traits>
#include <utility>
#include <iterator>
#include <algorithm>
#include <stdexcept>
#include <cstdlib> // for _LIBCUDACXX_UNREACHABLE
#include <version>
#include <__debug>
#endif

#if defined(_LIBCUDACXX_USE_PRAGMA_GCC_SYSTEM_HEADER)
#pragma GCC system_header
#endif



_LIBCUDACXX_BEGIN_NAMESPACE_STD


template <class _Tp, size_t _Size>
struct _LIBCUDACXX_TEMPLATE_VIS array
{
    // types:
    typedef array __self;
    typedef _Tp                                          value_type;
    typedef value_type&                                  reference;
    typedef const value_type&                            const_reference;
    typedef value_type*                                  iterator;
    typedef const value_type*                            const_iterator;
    typedef value_type*                                  pointer;
    typedef const value_type*                            const_pointer;
    typedef size_t                                       size_type;
    typedef ptrdiff_t                                    difference_type;
    typedef _CUDA_VSTD::reverse_iterator<const_iterator> const_reverse_iterator;
    typedef _CUDA_VSTD::reverse_iterator<iterator>       reverse_iterator;

    _Tp __elems_[_Size];

    // No explicit construct/copy/destroy for aggregate type
    _LIBCUDACXX_INLINE_VISIBILITY void fill(const value_type& __u) {
      _CUDA_VSTD::fill_n(__elems_, _Size, __u);
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    void swap(array& __a) _NOEXCEPT_(__is_nothrow_swappable<_Tp>::value) {
      _CUDA_VSTD::swap_ranges(__elems_, __elems_ + _Size, __a.__elems_);
    }

    // iterators:
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    iterator begin() _NOEXCEPT {return iterator(data());}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_iterator begin() const _NOEXCEPT {return const_iterator(data());}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    iterator end() _NOEXCEPT {return iterator(data() + _Size);}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_iterator end() const _NOEXCEPT {return const_iterator(data() + _Size);}

    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    reverse_iterator rbegin() _NOEXCEPT {return reverse_iterator(end());}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_reverse_iterator rbegin() const _NOEXCEPT {return const_reverse_iterator(end());}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    reverse_iterator rend() _NOEXCEPT {return reverse_iterator(begin());}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_reverse_iterator rend() const _NOEXCEPT {return const_reverse_iterator(begin());}

    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_iterator cbegin() const _NOEXCEPT {return begin();}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_iterator cend() const _NOEXCEPT {return end();}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_reverse_iterator crbegin() const _NOEXCEPT {return rbegin();}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const_reverse_iterator crend() const _NOEXCEPT {return rend();}

    // capacity:
    _LIBCUDACXX_INLINE_VISIBILITY
    _LIBCUDACXX_CONSTEXPR size_type size() const _NOEXCEPT {return _Size;}
    _LIBCUDACXX_INLINE_VISIBILITY
    _LIBCUDACXX_CONSTEXPR size_type max_size() const _NOEXCEPT {return _Size;}
    _LIBCUDACXX_NODISCARD_AFTER_CXX17 _LIBCUDACXX_INLINE_VISIBILITY
    _LIBCUDACXX_CONSTEXPR bool empty() const _NOEXCEPT {return false; }

    // element access:
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    reference operator[](size_type __n)             _NOEXCEPT {return __elems_[__n];}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11
    const_reference operator[](size_type __n) const _NOEXCEPT {return __elems_[__n];}

    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14       reference at(size_type __n);
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 const_reference at(size_type __n) const;

    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14 reference front()             _NOEXCEPT {return __elems_[0];}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 const_reference front() const _NOEXCEPT {return __elems_[0];}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14 reference back()              _NOEXCEPT {return __elems_[_Size - 1];}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11 const_reference back() const  _NOEXCEPT {return __elems_[_Size - 1];}

    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    value_type* data() _NOEXCEPT {return __elems_;}
    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX14
    const value_type* data() const _NOEXCEPT {return __elems_;}
};


template <class _Tp, size_t _Size>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX14
typename array<_Tp, _Size>::reference
array<_Tp, _Size>::at(size_type __n)
{
    if (__n >= _Size)
        __throw_out_of_range("array::at");

    return __elems_[__n];
}

template <class _Tp, size_t _Size>
_LIBCUDACXX_CONSTEXPR_AFTER_CXX11
typename array<_Tp, _Size>::const_reference
array<_Tp, _Size>::at(size_type __n) const
{
    if (__n >= _Size)
        __throw_out_of_range("array::at");
    return __elems_[__n];
}

template <class _Tp>
struct _LIBCUDACXX_TEMPLATE_VIS array<_Tp, 0>
{
    // types:
    typedef array __self;
    typedef _Tp                                   value_type;
    typedef value_type&                           reference;
    typedef const value_type&                     const_reference;
    typedef value_type*                           iterator;
    typedef const value_type*                     const_iterator;
    typedef value_type*                           pointer;
    typedef const value_type*                     const_pointer;
    typedef size_t                                size_type;
    typedef ptrdiff_t                             difference_type;
    typedef _CUDA_VSTD::reverse_iterator<const_iterator>      const_reverse_iterator;
    typedef _CUDA_VSTD::reverse_iterator<iterator>            reverse_iterator;

    typedef typename conditional<is_const<_Tp>::value, const char,
                                char>::type _CharType;

    struct  _ArrayInStructT { _Tp __data_[1]; };
    _ALIGNAS_TYPE(_ArrayInStructT) _CharType __elems_[sizeof(_ArrayInStructT)];

    // No explicit construct/copy/destroy for aggregate type
    _LIBCUDACXX_INLINE_VISIBILITY void fill(const value_type&) {
      static_assert(!is_const<_Tp>::value,
                    "cannot fill zero-sized array of type 'const T'");
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    void swap(array&) _NOEXCEPT {
      static_assert(!is_const<_Tp>::value,
                    "cannot swap zero-sized array of type 'const T'");
    }

    // iterators:
    _LIBCUDACXX_INLINE_VISIBILITY
    iterator begin() _NOEXCEPT {return iterator(data());}
    _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator begin() const _NOEXCEPT {return const_iterator(data());}
    _LIBCUDACXX_INLINE_VISIBILITY
    iterator end() _NOEXCEPT {return iterator(data());}
    _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator end() const _NOEXCEPT {return const_iterator(data());}

    _LIBCUDACXX_INLINE_VISIBILITY
    reverse_iterator rbegin() _NOEXCEPT {return reverse_iterator(end());}
    _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator rbegin() const _NOEXCEPT {return const_reverse_iterator(end());}
    _LIBCUDACXX_INLINE_VISIBILITY
    reverse_iterator rend() _NOEXCEPT {return reverse_iterator(begin());}
    _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator rend() const _NOEXCEPT {return const_reverse_iterator(begin());}

    _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator cbegin() const _NOEXCEPT {return begin();}
    _LIBCUDACXX_INLINE_VISIBILITY
    const_iterator cend() const _NOEXCEPT {return end();}
    _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator crbegin() const _NOEXCEPT {return rbegin();}
    _LIBCUDACXX_INLINE_VISIBILITY
    const_reverse_iterator crend() const _NOEXCEPT {return rend();}

    // capacity:
    _LIBCUDACXX_INLINE_VISIBILITY
    _LIBCUDACXX_CONSTEXPR size_type size() const _NOEXCEPT {return 0; }
    _LIBCUDACXX_INLINE_VISIBILITY
    _LIBCUDACXX_CONSTEXPR size_type max_size() const _NOEXCEPT {return 0;}
    _LIBCUDACXX_NODISCARD_AFTER_CXX17 _LIBCUDACXX_INLINE_VISIBILITY
    _LIBCUDACXX_CONSTEXPR bool empty() const _NOEXCEPT {return true;}

    // element access:
    _LIBCUDACXX_INLINE_VISIBILITY
    reference operator[](size_type) _NOEXCEPT {
      _LIBCUDACXX_ASSERT(false, "cannot call array<T, 0>::operator[] on a zero-sized array");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11
    const_reference operator[](size_type) const _NOEXCEPT {
      _LIBCUDACXX_ASSERT(false, "cannot call array<T, 0>::operator[] on a zero-sized array");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    reference at(size_type) {
      __throw_out_of_range("array<T, 0>::at");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    const_reference at(size_type) const {
      __throw_out_of_range("array<T, 0>::at");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    reference front() _NOEXCEPT {
      _LIBCUDACXX_ASSERT(false, "cannot call array<T, 0>::front() on a zero-sized array");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    const_reference front() const _NOEXCEPT {
      _LIBCUDACXX_ASSERT(false, "cannot call array<T, 0>::front() on a zero-sized array");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    reference back() _NOEXCEPT {
      _LIBCUDACXX_ASSERT(false, "cannot call array<T, 0>::back() on a zero-sized array");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    const_reference back() const _NOEXCEPT {
      _LIBCUDACXX_ASSERT(false, "cannot call array<T, 0>::back() on a zero-sized array");
      _LIBCUDACXX_UNREACHABLE();
    }

    _LIBCUDACXX_INLINE_VISIBILITY
    value_type* data() _NOEXCEPT {return reinterpret_cast<value_type*>(__elems_);}
    _LIBCUDACXX_INLINE_VISIBILITY
    const value_type* data() const _NOEXCEPT {return reinterpret_cast<const value_type*>(__elems_);}
};


#ifndef _LIBCUDACXX_HAS_NO_DEDUCTION_GUIDES
template<class _Tp, class... _Args,
         class = typename enable_if<(is_same_v<_Tp, _Args> && ...), void>::type
         >
array(_Tp, _Args...)
  -> array<_Tp, 1 + sizeof...(_Args)>;
#endif

template <class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY
_LIBCUDACXX_CONSTEXPR_AFTER_CXX17 bool
operator==(const array<_Tp, _Size>& __x, const array<_Tp, _Size>& __y)
{
    return _CUDA_VSTD::equal(__x.begin(), __x.end(), __y.begin());
}

template <class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY
_LIBCUDACXX_CONSTEXPR_AFTER_CXX17 bool
operator!=(const array<_Tp, _Size>& __x, const array<_Tp, _Size>& __y)
{
    return !(__x == __y);
}

template <class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY
_LIBCUDACXX_CONSTEXPR_AFTER_CXX17 bool
operator<(const array<_Tp, _Size>& __x, const array<_Tp, _Size>& __y)
{
    return _CUDA_VSTD::lexicographical_compare(__x.begin(), __x.end(),
                                          __y.begin(), __y.end());
}

template <class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY
_LIBCUDACXX_CONSTEXPR_AFTER_CXX17 bool
operator>(const array<_Tp, _Size>& __x, const array<_Tp, _Size>& __y)
{
    return __y < __x;
}

template <class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY
_LIBCUDACXX_CONSTEXPR_AFTER_CXX17 bool
operator<=(const array<_Tp, _Size>& __x, const array<_Tp, _Size>& __y)
{
    return !(__y < __x);
}

template <class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY
_LIBCUDACXX_CONSTEXPR_AFTER_CXX17 bool
operator>=(const array<_Tp, _Size>& __x, const array<_Tp, _Size>& __y)
{
    return !(__x < __y);
}

template <class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY
typename enable_if
<
    _Size == 0 ||
    __is_swappable<_Tp>::value,
    void
>::type
swap(array<_Tp, _Size>& __x, array<_Tp, _Size>& __y)
                                  _NOEXCEPT_(noexcept(__x.swap(__y)))
{
    __x.swap(__y);
}

template <class _Tp, size_t _Size>
struct _LIBCUDACXX_TEMPLATE_VIS tuple_size<array<_Tp, _Size> >
    : public integral_constant<size_t, _Size> {};

template <size_t _Ip, class _Tp, size_t _Size>
struct _LIBCUDACXX_TEMPLATE_VIS tuple_element<_Ip, array<_Tp, _Size> >
{
    static_assert(_Ip < _Size, "Index out of bounds in std::tuple_element<> (std::array)");
    typedef _Tp type;
};

template <size_t _Ip, class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11
_Tp&
get(array<_Tp, _Size>& __a) _NOEXCEPT
{
    static_assert(_Ip < _Size, "Index out of bounds in std::get<> (std::array)");
    return __a.__elems_[_Ip];
}

template <size_t _Ip, class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11
const _Tp&
get(const array<_Tp, _Size>& __a) _NOEXCEPT
{
    static_assert(_Ip < _Size, "Index out of bounds in std::get<> (const std::array)");
    return __a.__elems_[_Ip];
}

#ifndef _LIBCUDACXX_CXX03_LANG

template <size_t _Ip, class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11
_Tp&&
get(array<_Tp, _Size>&& __a) _NOEXCEPT
{
    static_assert(_Ip < _Size, "Index out of bounds in std::get<> (std::array &&)");
    return _CUDA_VSTD::move(__a.__elems_[_Ip]);
}

template <size_t _Ip, class _Tp, size_t _Size>
inline _LIBCUDACXX_INLINE_VISIBILITY _LIBCUDACXX_CONSTEXPR_AFTER_CXX11
const _Tp&&
get(const array<_Tp, _Size>&& __a) _NOEXCEPT
{
    static_assert(_Ip < _Size, "Index out of bounds in std::get<> (const std::array &&)");
    return _CUDA_VSTD::move(__a.__elems_[_Ip]);
}

#endif  // !_LIBCUDACXX_CXX03_LANG

_LIBCUDACXX_END_NAMESPACE_STD

#endif  // _LIBCUDACXX_ARRAY
