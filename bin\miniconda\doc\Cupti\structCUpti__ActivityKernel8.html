<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel8 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel8 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel8" -->The activity record for kernel.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#13ebab2313546ef43865378625fdf80c">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#fa9c09c410ce8481ab69b722bb7e44e3">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#b787ec99aae1db911446cc214f1a80dd">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#6394ab3ec7641b21d715fe4ed12cc04e">cacheConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#ee43d05e2fba6a8b7f3be1dc35179392">channelID</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUpti_ChannelType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#239b1c98181f664f25d5c5636be3f84d">channelType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#1edd454aa50166f8d4959e7b3ff2d7b0">clusterSchedulingPolicy</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#79cee473cbbf21a019ad3d92c195740a">clusterX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#acfe25401868454b7e48555e41cf2dac">clusterY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#52cc4fa87852c16ed1400c5263936550">clusterZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#f4a5b9bfa8a477d391c6ac1e17803963">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#7ab3d6908500e5998d253e3e5ff91c7c">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#6452f4580f8ff6dda02ad598cd70ec5c">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#f8e416fedd118d5bd1be2b3149f1fde8">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#9e7bc838f9cbae0d1acedcc0ddba9a72">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#c0f55394ac1ca5ff5f14ab0e14616914">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#5cfd288cfc1422fa0acbb628332241a3">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#987e277903421b688ab58438a06e3e1b">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#57e3aaa514f508f9c347754c186bb084">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#c6a7a746112f86ff9cf0fe822e16a49d">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#79e101760cbc88ea76f446e2f750cc80">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#f47a18dbcfaa44cfbe6d1e301238f288">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#cdfc6be84faf239bb9a9d2d2985342da">isSharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#d7ed798a57e097150492d11e7ee0da1b">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#d2a138322da251d4d9468ce2dbe8c8ad">launchType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#de57e9ec6f18d39472d9d24bcc6f4bd7">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#37e98f953cc4b384e9bb383042843ccf">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#ba7834abc71391760bff30d7b4182ac9">localMemoryTotal_v2</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#74adea6e1869f5acdf9e87d3fc36c38c">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUaccessPolicyWindow *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#342ccf5385daa6c45083cc1e02de6393">pAccessPolicyWindow</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#7a2b34a8e0f3871b7896292ae88a4e0e">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#600b93250df7721993f66b45b8d67271">partitionedGlobalCacheExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#9feb7964a4dfd1b9ec2bd124f4aa5f50">partitionedGlobalCacheRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#214dc9f88e09f2f0ed174faf9cc82247">queued</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#96c92420111e41926ce014bae09b600c">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#a7b6be273934adb0a1f8d9806c5b9fc1">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#a79c3959b204eff4b2ecb9fa2d820571">sharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#98cfa5371e39324a62ea6451e5dd5367">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#3dd049ef7a44c5a1ac03e0f8d92eb51f">sharedMemoryExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#cca157ea7cb025d5607d3407cfc53fec">shmemLimitConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#3ef92fa20916173624e5c5133571dc35">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#b788fe8577f4c6b07b709e0d5ee6c767">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#6018be7cf08d0daa5a92a5b31f895d7f">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#13656d1e9a0b4e83754f78a26f0156d8">submitted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#25cbbe93c18dce9315afa389602bf12d">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel8.html#7753011404c14094cc6b0535b2366016">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) <hr><h2>Field Documentation</h2>
<a class="anchor" name="13ebab2313546ef43865378625fdf80c"></a><!-- doxytag: member="CUpti_ActivityKernel8::blockX" ref="13ebab2313546ef43865378625fdf80c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#13ebab2313546ef43865378625fdf80c">CUpti_ActivityKernel8::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="fa9c09c410ce8481ab69b722bb7e44e3"></a><!-- doxytag: member="CUpti_ActivityKernel8::blockY" ref="fa9c09c410ce8481ab69b722bb7e44e3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#fa9c09c410ce8481ab69b722bb7e44e3">CUpti_ActivityKernel8::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="b787ec99aae1db911446cc214f1a80dd"></a><!-- doxytag: member="CUpti_ActivityKernel8::blockZ" ref="b787ec99aae1db911446cc214f1a80dd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#b787ec99aae1db911446cc214f1a80dd">CUpti_ActivityKernel8::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="6394ab3ec7641b21d715fe4ed12cc04e"></a><!-- doxytag: member="CUpti_ActivityKernel8::cacheConfig" ref="6394ab3ec7641b21d715fe4ed12cc04e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityKernel8.html#6394ab3ec7641b21d715fe4ed12cc04e">CUpti_ActivityKernel8::cacheConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For devices with compute capability 7.0+ cacheConfig values are not updated in case field isSharedMemoryCarveoutRequested is set 
</div>
</div><p>
<a class="anchor" name="ee43d05e2fba6a8b7f3be1dc35179392"></a><!-- doxytag: member="CUpti_ActivityKernel8::channelID" ref="ee43d05e2fba6a8b7f3be1dc35179392" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#ee43d05e2fba6a8b7f3be1dc35179392">CUpti_ActivityKernel8::channelID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the HW channel on which the kernel is launched. 
</div>
</div><p>
<a class="anchor" name="239b1c98181f664f25d5c5636be3f84d"></a><!-- doxytag: member="CUpti_ActivityKernel8::channelType" ref="239b1c98181f664f25d5c5636be3f84d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUpti_ChannelType <a class="el" href="structCUpti__ActivityKernel8.html#239b1c98181f664f25d5c5636be3f84d">CUpti_ActivityKernel8::channelType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the channel 
</div>
</div><p>
<a class="anchor" name="1edd454aa50166f8d4959e7b3ff2d7b0"></a><!-- doxytag: member="CUpti_ActivityKernel8::clusterSchedulingPolicy" ref="1edd454aa50166f8d4959e7b3ff2d7b0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#1edd454aa50166f8d4959e7b3ff2d7b0">CUpti_ActivityKernel8::clusterSchedulingPolicy</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cluster scheduling policy for the kernel. Refer CUclusterSchedulingPolicy Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="79cee473cbbf21a019ad3d92c195740a"></a><!-- doxytag: member="CUpti_ActivityKernel8::clusterX" ref="79cee473cbbf21a019ad3d92c195740a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#79cee473cbbf21a019ad3d92c195740a">CUpti_ActivityKernel8::clusterX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension cluster size for the kernel. Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="acfe25401868454b7e48555e41cf2dac"></a><!-- doxytag: member="CUpti_ActivityKernel8::clusterY" ref="acfe25401868454b7e48555e41cf2dac" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#acfe25401868454b7e48555e41cf2dac">CUpti_ActivityKernel8::clusterY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension cluster size for the kernel. Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="52cc4fa87852c16ed1400c5263936550"></a><!-- doxytag: member="CUpti_ActivityKernel8::clusterZ" ref="52cc4fa87852c16ed1400c5263936550" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#52cc4fa87852c16ed1400c5263936550">CUpti_ActivityKernel8::clusterZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension cluster size for the kernel. Field is valid for devices with compute capability 9.0 and higher 
</div>
</div><p>
<a class="anchor" name="f4a5b9bfa8a477d391c6ac1e17803963"></a><!-- doxytag: member="CUpti_ActivityKernel8::completed" ref="f4a5b9bfa8a477d391c6ac1e17803963" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel8.html#f4a5b9bfa8a477d391c6ac1e17803963">CUpti_ActivityKernel8::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="7ab3d6908500e5998d253e3e5ff91c7c"></a><!-- doxytag: member="CUpti_ActivityKernel8::contextId" ref="7ab3d6908500e5998d253e3e5ff91c7c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#7ab3d6908500e5998d253e3e5ff91c7c">CUpti_ActivityKernel8::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="6452f4580f8ff6dda02ad598cd70ec5c"></a><!-- doxytag: member="CUpti_ActivityKernel8::correlationId" ref="6452f4580f8ff6dda02ad598cd70ec5c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#6452f4580f8ff6dda02ad598cd70ec5c">CUpti_ActivityKernel8::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="f8e416fedd118d5bd1be2b3149f1fde8"></a><!-- doxytag: member="CUpti_ActivityKernel8::deviceId" ref="f8e416fedd118d5bd1be2b3149f1fde8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#f8e416fedd118d5bd1be2b3149f1fde8">CUpti_ActivityKernel8::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="9e7bc838f9cbae0d1acedcc0ddba9a72"></a><!-- doxytag: member="CUpti_ActivityKernel8::dynamicSharedMemory" ref="9e7bc838f9cbae0d1acedcc0ddba9a72" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#9e7bc838f9cbae0d1acedcc0ddba9a72">CUpti_ActivityKernel8::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="c0f55394ac1ca5ff5f14ab0e14616914"></a><!-- doxytag: member="CUpti_ActivityKernel8::end" ref="c0f55394ac1ca5ff5f14ab0e14616914" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel8.html#c0f55394ac1ca5ff5f14ab0e14616914">CUpti_ActivityKernel8::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="25cbbe93c18dce9315afa389602bf12d"></a><!-- doxytag: member="CUpti_ActivityKernel8::executed" ref="25cbbe93c18dce9315afa389602bf12d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel8.html#25cbbe93c18dce9315afa389602bf12d">CUpti_ActivityKernel8::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="5cfd288cfc1422fa0acbb628332241a3"></a><!-- doxytag: member="CUpti_ActivityKernel8::graphId" ref="5cfd288cfc1422fa0acbb628332241a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#5cfd288cfc1422fa0acbb628332241a3">CUpti_ActivityKernel8::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="987e277903421b688ab58438a06e3e1b"></a><!-- doxytag: member="CUpti_ActivityKernel8::graphNodeId" ref="987e277903421b688ab58438a06e3e1b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel8.html#987e277903421b688ab58438a06e3e1b">CUpti_ActivityKernel8::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="57e3aaa514f508f9c347754c186bb084"></a><!-- doxytag: member="CUpti_ActivityKernel8::gridId" ref="57e3aaa514f508f9c347754c186bb084" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel8.html#57e3aaa514f508f9c347754c186bb084">CUpti_ActivityKernel8::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="c6a7a746112f86ff9cf0fe822e16a49d"></a><!-- doxytag: member="CUpti_ActivityKernel8::gridX" ref="c6a7a746112f86ff9cf0fe822e16a49d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#c6a7a746112f86ff9cf0fe822e16a49d">CUpti_ActivityKernel8::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="79e101760cbc88ea76f446e2f750cc80"></a><!-- doxytag: member="CUpti_ActivityKernel8::gridY" ref="79e101760cbc88ea76f446e2f750cc80" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#79e101760cbc88ea76f446e2f750cc80">CUpti_ActivityKernel8::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="f47a18dbcfaa44cfbe6d1e301238f288"></a><!-- doxytag: member="CUpti_ActivityKernel8::gridZ" ref="f47a18dbcfaa44cfbe6d1e301238f288" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#f47a18dbcfaa44cfbe6d1e301238f288">CUpti_ActivityKernel8::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="cdfc6be84faf239bb9a9d2d2985342da"></a><!-- doxytag: member="CUpti_ActivityKernel8::isSharedMemoryCarveoutRequested" ref="cdfc6be84faf239bb9a9d2d2985342da" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel8.html#cdfc6be84faf239bb9a9d2d2985342da">CUpti_ActivityKernel8::isSharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This indicates if CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT was updated for the kernel launch 
</div>
</div><p>
<a class="anchor" name="d7ed798a57e097150492d11e7ee0da1b"></a><!-- doxytag: member="CUpti_ActivityKernel8::kind" ref="d7ed798a57e097150492d11e7ee0da1b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel8.html#d7ed798a57e097150492d11e7ee0da1b">CUpti_ActivityKernel8::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="d2a138322da251d4d9468ce2dbe8c8ad"></a><!-- doxytag: member="CUpti_ActivityKernel8::launchType" ref="d2a138322da251d4d9468ce2dbe8c8ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel8.html#d2a138322da251d4d9468ce2dbe8c8ad">CUpti_ActivityKernel8::launchType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The indicates if the kernel was executed via a regular launch or via a single/multi device cooperative launch. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c" title="The type of the CUDA kernel launch.">CUpti_ActivityLaunchType</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="de57e9ec6f18d39472d9d24bcc6f4bd7"></a><!-- doxytag: member="CUpti_ActivityKernel8::localMemoryPerThread" ref="de57e9ec6f18d39472d9d24bcc6f4bd7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#de57e9ec6f18d39472d9d24bcc6f4bd7">CUpti_ActivityKernel8::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="37e98f953cc4b384e9bb383042843ccf"></a><!-- doxytag: member="CUpti_ActivityKernel8::localMemoryTotal" ref="37e98f953cc4b384e9bb383042843ccf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#37e98f953cc4b384e9bb383042843ccf">CUpti_ActivityKernel8::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes (deprecated in CUDA 11.8). Refer field localMemoryTotal_v2 
</div>
</div><p>
<a class="anchor" name="ba7834abc71391760bff30d7b4182ac9"></a><!-- doxytag: member="CUpti_ActivityKernel8::localMemoryTotal_v2" ref="ba7834abc71391760bff30d7b4182ac9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel8.html#ba7834abc71391760bff30d7b4182ac9">CUpti_ActivityKernel8::localMemoryTotal_v2</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="74adea6e1869f5acdf9e87d3fc36c38c"></a><!-- doxytag: member="CUpti_ActivityKernel8::name" ref="74adea6e1869f5acdf9e87d3fc36c38c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel8.html#74adea6e1869f5acdf9e87d3fc36c38c">CUpti_ActivityKernel8::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="342ccf5385daa6c45083cc1e02de6393"></a><!-- doxytag: member="CUpti_ActivityKernel8::pAccessPolicyWindow" ref="342ccf5385daa6c45083cc1e02de6393" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUaccessPolicyWindow* <a class="el" href="structCUpti__ActivityKernel8.html#342ccf5385daa6c45083cc1e02de6393">CUpti_ActivityKernel8::pAccessPolicyWindow</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pointer to the access policy window. The structure CUaccessPolicyWindow is defined in cuda.h. 
</div>
</div><p>
<a class="anchor" name="7a2b34a8e0f3871b7896292ae88a4e0e"></a><!-- doxytag: member="CUpti_ActivityKernel8::padding" ref="7a2b34a8e0f3871b7896292ae88a4e0e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel8.html#7a2b34a8e0f3871b7896292ae88a4e0e">CUpti_ActivityKernel8::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="600b93250df7721993f66b45b8d67271"></a><!-- doxytag: member="CUpti_ActivityKernel8::partitionedGlobalCacheExecuted" ref="600b93250df7721993f66b45b8d67271" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel8.html#600b93250df7721993f66b45b8d67271">CUpti_ActivityKernel8::partitionedGlobalCacheExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching executed for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. Partitioned global caching can be automatically disabled if the occupancy requirement of the launch cannot support caching. 
</div>
</div><p>
<a class="anchor" name="9feb7964a4dfd1b9ec2bd124f4aa5f50"></a><!-- doxytag: member="CUpti_ActivityKernel8::partitionedGlobalCacheRequested" ref="9feb7964a4dfd1b9ec2bd124f4aa5f50" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel8.html#9feb7964a4dfd1b9ec2bd124f4aa5f50">CUpti_ActivityKernel8::partitionedGlobalCacheRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching requested for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. 
</div>
</div><p>
<a class="anchor" name="214dc9f88e09f2f0ed174faf9cc82247"></a><!-- doxytag: member="CUpti_ActivityKernel8::queued" ref="214dc9f88e09f2f0ed174faf9cc82247" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel8.html#214dc9f88e09f2f0ed174faf9cc82247">CUpti_ActivityKernel8::queued</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the kernel is queued up in the command buffer, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the queued time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection.<p>
Command buffer is a buffer written by CUDA driver to send commands like kernel launch, memory copy etc to the GPU. All launches of CUDA kernels are asynchrnous with respect to the host, the host requests the launch by writing commands into the command buffer, then returns without checking the GPU's progress. 
</div>
</div><p>
<a class="anchor" name="96c92420111e41926ce014bae09b600c"></a><!-- doxytag: member="CUpti_ActivityKernel8::registersPerThread" ref="96c92420111e41926ce014bae09b600c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel8.html#96c92420111e41926ce014bae09b600c">CUpti_ActivityKernel8::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="7753011404c14094cc6b0535b2366016"></a><!-- doxytag: member="CUpti_ActivityKernel8::requested" ref="7753011404c14094cc6b0535b2366016" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel8.html#7753011404c14094cc6b0535b2366016">CUpti_ActivityKernel8::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="a7b6be273934adb0a1f8d9806c5b9fc1"></a><!-- doxytag: member="CUpti_ActivityKernel8::reserved0" ref="a7b6be273934adb0a1f8d9806c5b9fc1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel8.html#a7b6be273934adb0a1f8d9806c5b9fc1">CUpti_ActivityKernel8::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="a79c3959b204eff4b2ecb9fa2d820571"></a><!-- doxytag: member="CUpti_ActivityKernel8::sharedMemoryCarveoutRequested" ref="a79c3959b204eff4b2ecb9fa2d820571" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel8.html#a79c3959b204eff4b2ecb9fa2d820571">CUpti_ActivityKernel8::sharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory carveout value requested for the function in percentage of the total resource. The value will be updated only if field isSharedMemoryCarveoutRequested is set. 
</div>
</div><p>
<a class="anchor" name="98cfa5371e39324a62ea6451e5dd5367"></a><!-- doxytag: member="CUpti_ActivityKernel8::sharedMemoryConfig" ref="98cfa5371e39324a62ea6451e5dd5367" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel8.html#98cfa5371e39324a62ea6451e5dd5367">CUpti_ActivityKernel8::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="3dd049ef7a44c5a1ac03e0f8d92eb51f"></a><!-- doxytag: member="CUpti_ActivityKernel8::sharedMemoryExecuted" ref="3dd049ef7a44c5a1ac03e0f8d92eb51f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#3dd049ef7a44c5a1ac03e0f8d92eb51f">CUpti_ActivityKernel8::sharedMemoryExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory size set by the driver. 
</div>
</div><p>
<a class="anchor" name="cca157ea7cb025d5607d3407cfc53fec"></a><!-- doxytag: member="CUpti_ActivityKernel8::shmemLimitConfig" ref="cca157ea7cb025d5607d3407cfc53fec" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a> <a class="el" href="structCUpti__ActivityKernel8.html#cca157ea7cb025d5607d3407cfc53fec">CUpti_ActivityKernel8::shmemLimitConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory limit config for the kernel. This field shows whether user has opted for a higher per block limit of dynamic shared memory. 
</div>
</div><p>
<a class="anchor" name="3ef92fa20916173624e5c5133571dc35"></a><!-- doxytag: member="CUpti_ActivityKernel8::start" ref="3ef92fa20916173624e5c5133571dc35" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel8.html#3ef92fa20916173624e5c5133571dc35">CUpti_ActivityKernel8::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="b788fe8577f4c6b07b709e0d5ee6c767"></a><!-- doxytag: member="CUpti_ActivityKernel8::staticSharedMemory" ref="b788fe8577f4c6b07b709e0d5ee6c767" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel8.html#b788fe8577f4c6b07b709e0d5ee6c767">CUpti_ActivityKernel8::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="6018be7cf08d0daa5a92a5b31f895d7f"></a><!-- doxytag: member="CUpti_ActivityKernel8::streamId" ref="6018be7cf08d0daa5a92a5b31f895d7f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel8.html#6018be7cf08d0daa5a92a5b31f895d7f">CUpti_ActivityKernel8::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="13656d1e9a0b4e83754f78a26f0156d8"></a><!-- doxytag: member="CUpti_ActivityKernel8::submitted" ref="13656d1e9a0b4e83754f78a26f0156d8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel8.html#13656d1e9a0b4e83754f78a26f0156d8">CUpti_ActivityKernel8::submitted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the command buffer containing the kernel launch is submitted to the GPU, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the submitted time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
