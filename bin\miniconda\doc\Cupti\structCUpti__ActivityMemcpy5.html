<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemcpy5 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemcpy5 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemcpy5" -->The activity record for memory copies.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#01567f6643ea926d59531fcd23452796">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#f0589495f2a300dc86df57dafa718fe4">channelID</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUpti_ChannelType&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#4b57159d552af325a7d1e7ddd0faf2dc">channelType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#48824985ad20df1d0560522476dae349">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#f475f4ca796c8d69bc718bbf7d415b0a">copyKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#7007a4b690ac5f19dc1115acbb735a1b">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#62c2a48a80e9f0a7ec9a19cf75851a76">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#cd3f1391749b89c9c34b04c71bde6453">dstKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#ef8025a3827b17b90a4cc99bb11e56ff">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#818723ed5e9038d1c1540f53068bbb29">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#d37fff48635a50196a2acdb8cc178440">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#63f9610801a6247d24726c49f71fd589">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#73a02977087615b6ffa6d11e42f02fba">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#f86a13a423086976dbd2b5bdbb200f16">pad2</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#5e53427fc6a010d09a5219ce32518dab">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#5b2bea88b5122860ce68713a831eecbf">runtimeCorrelationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#939744fdb08ae4ecb349324b919ed712">srcKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#8da8117c4e6d0d4488be1507e7f66a11">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemcpy5.html#23cb65383137c8be145ce99f6255f6ad">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory copy (CUPTI_ACTIVITY_KIND_MEMCPY). <hr><h2>Field Documentation</h2>
<a class="anchor" name="01567f6643ea926d59531fcd23452796"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::bytes" ref="01567f6643ea926d59531fcd23452796" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy5.html#01567f6643ea926d59531fcd23452796">CUpti_ActivityMemcpy5::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes transferred by the memory copy. 
</div>
</div><p>
<a class="anchor" name="f0589495f2a300dc86df57dafa718fe4"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::channelID" ref="f0589495f2a300dc86df57dafa718fe4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#f0589495f2a300dc86df57dafa718fe4">CUpti_ActivityMemcpy5::channelID</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the HW channel on which the memory copy is occuring. 
</div>
</div><p>
<a class="anchor" name="4b57159d552af325a7d1e7ddd0faf2dc"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::channelType" ref="4b57159d552af325a7d1e7ddd0faf2dc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUpti_ChannelType <a class="el" href="structCUpti__ActivityMemcpy5.html#4b57159d552af325a7d1e7ddd0faf2dc">CUpti_ActivityMemcpy5::channelType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of the channel 
</div>
</div><p>
<a class="anchor" name="48824985ad20df1d0560522476dae349"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::contextId" ref="48824985ad20df1d0560522476dae349" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#48824985ad20df1d0560522476dae349">CUpti_ActivityMemcpy5::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="f475f4ca796c8d69bc718bbf7d415b0a"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::copyKind" ref="f475f4ca796c8d69bc718bbf7d415b0a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy5.html#f475f4ca796c8d69bc718bbf7d415b0a">CUpti_ActivityMemcpy5::copyKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g10056d66c2ee966fc5cde439eb0a3661" title="The kind of a memory copy, indicating the source and destination targets of the copy...">CUpti_ActivityMemcpyKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="7007a4b690ac5f19dc1115acbb735a1b"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::correlationId" ref="7007a4b690ac5f19dc1115acbb735a1b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#7007a4b690ac5f19dc1115acbb735a1b">CUpti_ActivityMemcpy5::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory copy. Each memory copy is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="62c2a48a80e9f0a7ec9a19cf75851a76"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::deviceId" ref="62c2a48a80e9f0a7ec9a19cf75851a76" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#62c2a48a80e9f0a7ec9a19cf75851a76">CUpti_ActivityMemcpy5::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory copy is occurring. 
</div>
</div><p>
<a class="anchor" name="cd3f1391749b89c9c34b04c71bde6453"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::dstKind" ref="cd3f1391749b89c9c34b04c71bde6453" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy5.html#cd3f1391749b89c9c34b04c71bde6453">CUpti_ActivityMemcpy5::dstKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The destination memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="ef8025a3827b17b90a4cc99bb11e56ff"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::end" ref="ef8025a3827b17b90a4cc99bb11e56ff" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy5.html#ef8025a3827b17b90a4cc99bb11e56ff">CUpti_ActivityMemcpy5::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="818723ed5e9038d1c1540f53068bbb29"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::flags" ref="818723ed5e9038d1c1540f53068bbb29" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy5.html#818723ed5e9038d1c1540f53068bbb29">CUpti_ActivityMemcpy5::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memory copy. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="d37fff48635a50196a2acdb8cc178440"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::graphId" ref="d37fff48635a50196a2acdb8cc178440" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#d37fff48635a50196a2acdb8cc178440">CUpti_ActivityMemcpy5::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that executed this memcpy through graph launch. This field will be 0 if the memcpy is not done through graph launch. 
</div>
</div><p>
<a class="anchor" name="63f9610801a6247d24726c49f71fd589"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::graphNodeId" ref="63f9610801a6247d24726c49f71fd589" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy5.html#63f9610801a6247d24726c49f71fd589">CUpti_ActivityMemcpy5::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed this memcpy through graph launch. This field will be 0 if the memcpy is not done through graph launch. 
</div>
</div><p>
<a class="anchor" name="73a02977087615b6ffa6d11e42f02fba"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::kind" ref="73a02977087615b6ffa6d11e42f02fba" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemcpy5.html#73a02977087615b6ffa6d11e42f02fba">CUpti_ActivityMemcpy5::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMCPY. 
</div>
</div><p>
<a class="anchor" name="f86a13a423086976dbd2b5bdbb200f16"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::pad2" ref="f86a13a423086976dbd2b5bdbb200f16" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#f86a13a423086976dbd2b5bdbb200f16">CUpti_ActivityMemcpy5::pad2</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="5e53427fc6a010d09a5219ce32518dab"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::reserved0" ref="5e53427fc6a010d09a5219ce32518dab" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemcpy5.html#5e53427fc6a010d09a5219ce32518dab">CUpti_ActivityMemcpy5::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="5b2bea88b5122860ce68713a831eecbf"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::runtimeCorrelationId" ref="5b2bea88b5122860ce68713a831eecbf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#5b2bea88b5122860ce68713a831eecbf">CUpti_ActivityMemcpy5::runtimeCorrelationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The runtime correlation ID of the memory copy. Each memory copy is assigned a unique runtime correlation ID that is identical to the correlation ID in the runtime API activity record that launched the memory copy. 
</div>
</div><p>
<a class="anchor" name="939744fdb08ae4ecb349324b919ed712"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::srcKind" ref="939744fdb08ae4ecb349324b919ed712" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityMemcpy5.html#939744fdb08ae4ecb349324b919ed712">CUpti_ActivityMemcpy5::srcKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The source memory kind read by the memory copy, stored as a byte to reduce record size. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="8da8117c4e6d0d4488be1507e7f66a11"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::start" ref="8da8117c4e6d0d4488be1507e7f66a11" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemcpy5.html#8da8117c4e6d0d4488be1507e7f66a11">CUpti_ActivityMemcpy5::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory copy, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory copy. 
</div>
</div><p>
<a class="anchor" name="23cb65383137c8be145ce99f6255f6ad"></a><!-- doxytag: member="CUpti_ActivityMemcpy5::streamId" ref="23cb65383137c8be145ce99f6255f6ad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemcpy5.html#23cb65383137c8be145ce99f6255f6ad">CUpti_ActivityMemcpy5::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory copy is occurring. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
