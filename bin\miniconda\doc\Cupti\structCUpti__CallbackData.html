<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_CallbackData Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_CallbackData Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__CALLBACK__API.html">CUPTI Callback API</a>]</small>
</h1><!-- doxytag: class="CUpti_CallbackData" -->Data passed into a runtime or driver API callback function.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7bd557c9b3084014c680b9925842be24">CUpti_ApiCallbackSite</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#01337ce329bea0e08d803cb99c1f1f01">callbackSite</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#536ba0e56c569cba46be71ff06d34927">context</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#49f73c9d3877114e592f3266224062ed">contextUid</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#4a1f2884b8d54d9771d6ee32df82c9f7">correlationData</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#49f5003be9fb00a0593fdf7160c7beb4">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#45423011db4b6b3078f96380e7c43076">functionName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#0498588b68ecc63a07b30d88d1d7171c">functionParams</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#659cca9a4d4498b5da937e5a65e0bf13">functionReturnValue</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__CallbackData.html#6e5af5a34bb534f64ad8f968d780b63f">symbolName</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Data passed into a runtime or driver API callback function as the <code>cbdata</code> argument to <a class="el" href="group__CUPTI__CALLBACK__API.html#g84b7295694fda2bbfda931682a07bf4f">CUpti_CallbackFunc</a>. The <code>cbdata</code> will be this type for <code>domain</code> equal to CUPTI_CB_DOMAIN_DRIVER_API or CUPTI_CB_DOMAIN_RUNTIME_API. The callback data is valid only within the invocation of the callback function that is passed the data. If you need to retain some data for use outside of the callback, you must make a copy of that data. For example, if you make a shallow copy of <a class="el" href="structCUpti__CallbackData.html" title="Data passed into a runtime or driver API callback function.">CUpti_CallbackData</a> within a callback, you cannot dereference <code>functionParams</code> outside of that callback to access the function parameters. <code>functionName</code> is an exception: the string pointed to by <code>functionName</code> is a global constant and so may be accessed outside of the callback. <hr><h2>Field Documentation</h2>
<a class="anchor" name="01337ce329bea0e08d803cb99c1f1f01"></a><!-- doxytag: member="CUpti_CallbackData::callbackSite" ref="01337ce329bea0e08d803cb99c1f1f01" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__CALLBACK__API.html#g7bd557c9b3084014c680b9925842be24">CUpti_ApiCallbackSite</a> <a class="el" href="structCUpti__CallbackData.html#01337ce329bea0e08d803cb99c1f1f01">CUpti_CallbackData::callbackSite</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Point in the runtime or driver function from where the callback was issued. 
</div>
</div><p>
<a class="anchor" name="536ba0e56c569cba46be71ff06d34927"></a><!-- doxytag: member="CUpti_CallbackData::context" ref="536ba0e56c569cba46be71ff06d34927" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUcontext <a class="el" href="structCUpti__CallbackData.html#536ba0e56c569cba46be71ff06d34927">CUpti_CallbackData::context</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Driver context current to the thread, or null if no context is current. This value can change from the entry to exit callback of a runtime API function if the runtime initializes a context. 
</div>
</div><p>
<a class="anchor" name="49f73c9d3877114e592f3266224062ed"></a><!-- doxytag: member="CUpti_CallbackData::contextUid" ref="49f73c9d3877114e592f3266224062ed" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__CallbackData.html#49f73c9d3877114e592f3266224062ed">CUpti_CallbackData::contextUid</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Unique ID for the CUDA context associated with the thread. The UIDs are assigned sequentially as contexts are created and are unique within a process. 
</div>
</div><p>
<a class="anchor" name="4a1f2884b8d54d9771d6ee32df82c9f7"></a><!-- doxytag: member="CUpti_CallbackData::correlationData" ref="4a1f2884b8d54d9771d6ee32df82c9f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t* <a class="el" href="structCUpti__CallbackData.html#4a1f2884b8d54d9771d6ee32df82c9f7">CUpti_CallbackData::correlationData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to data shared between the entry and exit callbacks of a given runtime or drive API function invocation. This field can be used to pass 64-bit values from the entry callback to the corresponding exit callback. 
</div>
</div><p>
<a class="anchor" name="49f5003be9fb00a0593fdf7160c7beb4"></a><!-- doxytag: member="CUpti_CallbackData::correlationId" ref="49f5003be9fb00a0593fdf7160c7beb4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__CallbackData.html#49f5003be9fb00a0593fdf7160c7beb4">CUpti_CallbackData::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record correlation ID for this callback. For a driver domain callback (i.e. <code>domain</code> CUPTI_CB_DOMAIN_DRIVER_API) this ID will equal the correlation ID in the <a class="el" href="structCUpti__ActivityAPI.html" title="The activity record for a driver or runtime API invocation.">CUpti_ActivityAPI</a> record corresponding to the CUDA driver function call. For a runtime domain callback (i.e. <code>domain</code> CUPTI_CB_DOMAIN_RUNTIME_API) this ID will equal the correlation ID in the <a class="el" href="structCUpti__ActivityAPI.html" title="The activity record for a driver or runtime API invocation.">CUpti_ActivityAPI</a> record corresponding to the CUDA runtime function call. Within the callback, this ID can be recorded to correlate user data with the activity record. This field is new in 4.1. 
</div>
</div><p>
<a class="anchor" name="45423011db4b6b3078f96380e7c43076"></a><!-- doxytag: member="CUpti_CallbackData::functionName" ref="45423011db4b6b3078f96380e7c43076" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__CallbackData.html#45423011db4b6b3078f96380e7c43076">CUpti_CallbackData::functionName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Name of the runtime or driver API function which issued the callback. This string is a global constant and so may be accessed outside of the callback. 
</div>
</div><p>
<a class="anchor" name="0498588b68ecc63a07b30d88d1d7171c"></a><!-- doxytag: member="CUpti_CallbackData::functionParams" ref="0498588b68ecc63a07b30d88d1d7171c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="structCUpti__CallbackData.html#0498588b68ecc63a07b30d88d1d7171c">CUpti_CallbackData::functionParams</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the arguments passed to the runtime or driver API call. See generated_cuda_runtime_api_meta.h and generated_cuda_meta.h for structure definitions for the parameters for each runtime and driver API function. 
</div>
</div><p>
<a class="anchor" name="659cca9a4d4498b5da937e5a65e0bf13"></a><!-- doxytag: member="CUpti_CallbackData::functionReturnValue" ref="659cca9a4d4498b5da937e5a65e0bf13" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__CallbackData.html#659cca9a4d4498b5da937e5a65e0bf13">CUpti_CallbackData::functionReturnValue</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the return value of the runtime or driver API call. This field is only valid within the <a class="el" href="group__CUPTI__CALLBACK__API.html#gg7bd557c9b3084014c680b9925842be241df739da617612b774bb0a8895c15fab">exit::CUPTI_API_EXIT</a> callback. For a runtime API <code>functionReturnValue</code> points to a <code>cudaError_t</code>. For a driver API <code>functionReturnValue</code> points to a <code>CUresult</code>. 
</div>
</div><p>
<a class="anchor" name="6e5af5a34bb534f64ad8f968d780b63f"></a><!-- doxytag: member="CUpti_CallbackData::symbolName" ref="6e5af5a34bb534f64ad8f968d780b63f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__CallbackData.html#6e5af5a34bb534f64ad8f968d780b63f">CUpti_CallbackData::symbolName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Name of the symbol operated on by the runtime or driver API function which issued the callback. This entry is valid only for driver and runtime launch callbacks, where it returns the name of the kernel. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
