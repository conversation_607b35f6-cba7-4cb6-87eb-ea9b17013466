<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemory Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemory Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemory" -->The activity record for memory.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#741c9a69102c01843912c9b6b3dcfebf">address</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#50f11689280a94237d9cc939331aaf3e">allocPC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#6e134910fdbf47aeeb8f3caa3b13801c">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#cc2706a3423d065de869b589f705d759">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#0e507d23c333e45003468152cd3f91d9">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#c65778c1dbe448c2e20d88bb8c25aedd">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#e8d5833b4227fe9350cb35e0176c1f99">freePC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#856d16536d63ed913277388cb49baa60">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#665383a70b3dcfa9ff3feb81737dfaac">memoryKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#f6a4df9e6e990204a80a7fce37d95ac2">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#57c6f4268958946c3384b43deab7f88f">processId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemory.html#dfa91d9df7f8defade47a033974a1f14">start</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory allocation and free operation (CUPTI_ACTIVITY_KIND_MEMORY). This activity record provides a single record for the memory allocation and memory release operations.<p>
Note: It is recommended to move to the new activity record <a class="el" href="structCUpti__ActivityMemory3.html">CUpti_ActivityMemory3</a> enabled using the kind <a class="el" href="group__CUPTI__ACTIVITY__API.html#ggefed720d5a60c3e8b286cd386c4913e3fc702c2e896e657930de3685d4ea3233">CUPTI_ACTIVITY_KIND_MEMORY2</a>. <a class="el" href="structCUpti__ActivityMemory3.html">CUpti_ActivityMemory3</a> provides separate records for memory allocation and memory release operations. This allows to correlate the corresponding driver and runtime API activity record with the memory operation. <hr><h2>Field Documentation</h2>
<a class="anchor" name="741c9a69102c01843912c9b6b3dcfebf"></a><!-- doxytag: member="CUpti_ActivityMemory::address" ref="741c9a69102c01843912c9b6b3dcfebf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory.html#741c9a69102c01843912c9b6b3dcfebf">CUpti_ActivityMemory::address</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The virtual address of the allocation 
</div>
</div><p>
<a class="anchor" name="50f11689280a94237d9cc939331aaf3e"></a><!-- doxytag: member="CUpti_ActivityMemory::allocPC" ref="50f11689280a94237d9cc939331aaf3e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory.html#50f11689280a94237d9cc939331aaf3e">CUpti_ActivityMemory::allocPC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The program counter of the allocation of memory 
</div>
</div><p>
<a class="anchor" name="6e134910fdbf47aeeb8f3caa3b13801c"></a><!-- doxytag: member="CUpti_ActivityMemory::bytes" ref="6e134910fdbf47aeeb8f3caa3b13801c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory.html#6e134910fdbf47aeeb8f3caa3b13801c">CUpti_ActivityMemory::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes of memory allocated. 
</div>
</div><p>
<a class="anchor" name="cc2706a3423d065de869b589f705d759"></a><!-- doxytag: member="CUpti_ActivityMemory::contextId" ref="cc2706a3423d065de869b589f705d759" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory.html#cc2706a3423d065de869b589f705d759">CUpti_ActivityMemory::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context. If context is NULL, <code>contextId</code> is set to CUPTI_INVALID_CONTEXT_ID. 
</div>
</div><p>
<a class="anchor" name="0e507d23c333e45003468152cd3f91d9"></a><!-- doxytag: member="CUpti_ActivityMemory::deviceId" ref="0e507d23c333e45003468152cd3f91d9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory.html#0e507d23c333e45003468152cd3f91d9">CUpti_ActivityMemory::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory allocation is taking place. 
</div>
</div><p>
<a class="anchor" name="c65778c1dbe448c2e20d88bb8c25aedd"></a><!-- doxytag: member="CUpti_ActivityMemory::end" ref="c65778c1dbe448c2e20d88bb8c25aedd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory.html#c65778c1dbe448c2e20d88bb8c25aedd">CUpti_ActivityMemory::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory operation, i.e. the time when memory was freed, in ns. This will be 0 if memory is not freed in the application 
</div>
</div><p>
<a class="anchor" name="e8d5833b4227fe9350cb35e0176c1f99"></a><!-- doxytag: member="CUpti_ActivityMemory::freePC" ref="e8d5833b4227fe9350cb35e0176c1f99" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory.html#e8d5833b4227fe9350cb35e0176c1f99">CUpti_ActivityMemory::freePC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The program counter of the freeing of memory. This will be 0 if memory is not freed in the application 
</div>
</div><p>
<a class="anchor" name="856d16536d63ed913277388cb49baa60"></a><!-- doxytag: member="CUpti_ActivityMemory::kind" ref="856d16536d63ed913277388cb49baa60" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemory.html#856d16536d63ed913277388cb49baa60">CUpti_ActivityMemory::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMORY 
</div>
</div><p>
<a class="anchor" name="665383a70b3dcfa9ff3feb81737dfaac"></a><!-- doxytag: member="CUpti_ActivityMemory::memoryKind" ref="665383a70b3dcfa9ff3feb81737dfaac" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc">CUpti_ActivityMemoryKind</a> <a class="el" href="structCUpti__ActivityMemory.html#665383a70b3dcfa9ff3feb81737dfaac">CUpti_ActivityMemory::memoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory kind requested by the user 
</div>
</div><p>
<a class="anchor" name="f6a4df9e6e990204a80a7fce37d95ac2"></a><!-- doxytag: member="CUpti_ActivityMemory::name" ref="f6a4df9e6e990204a80a7fce37d95ac2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityMemory.html#f6a4df9e6e990204a80a7fce37d95ac2">CUpti_ActivityMemory::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Variable name. This name is shared across all activity records representing the same symbol, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="57c6f4268958946c3384b43deab7f88f"></a><!-- doxytag: member="CUpti_ActivityMemory::processId" ref="57c6f4268958946c3384b43deab7f88f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemory.html#57c6f4268958946c3384b43deab7f88f">CUpti_ActivityMemory::processId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the process to which this record belongs to. 
</div>
</div><p>
<a class="anchor" name="dfa91d9df7f8defade47a033974a1f14"></a><!-- doxytag: member="CUpti_ActivityMemory::start" ref="dfa91d9df7f8defade47a033974a1f14" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemory.html#dfa91d9df7f8defade47a033974a1f14">CUpti_ActivityMemory::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory operation, i.e. the time when memory was allocated, in ns. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
