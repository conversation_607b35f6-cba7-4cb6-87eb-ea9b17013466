{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvprof-12.1.55-0", "features": "", "files": ["LICENSE", "bin/cuinj64_121.dll", "bin/nvprof.exe"], "fn": "cuda-nvprof-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvprof-12.1.55-0", "type": 1}, "md5": "cd54dee939278e72a6ac28d67054643d", "name": "cuda-nvprof", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvprof-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "bin/cuinj64_121.dll", "path_type": "hardlink", "sha256": "467097fdacb708c9d1e4849d16704d1083409244cbdf24377974497290b02bce", "sha256_in_prefix": "467097fdacb708c9d1e4849d16704d1083409244cbdf24377974497290b02bce", "size_in_bytes": 1510912}, {"_path": "bin/nvprof.exe", "path_type": "hardlink", "sha256": "13c807f53ccfc1cd5b271d34c045a782f83742239c36e5a1e611df7c846a9fd6", "sha256_in_prefix": "13c807f53ccfc1cd5b271d34c045a782f83742239c36e5a1e611df7c846a9fd6", "size_in_bytes": 2201600}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 1702345, "subdir": "win-64", "timestamp": 1674625635000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-nvprof-12.1.55-0.tar.bz2", "version": "12.1.55"}