<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel" -->The activity record for kernel. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#cbb9bac29ee4ab936299bf572ca0e866">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#6af1a44732c5f34db22a6d83703a2e21">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#bcd200d948ddea33f583829aaf2e83d6">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#48c622133fb1572c4f5772c613d12cdc">cacheConfigExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#613844ef0471a6e4bf8ff990840d2eb4">cacheConfigRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#45e5c6ebf342ea9973d5440d873f362d">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#2636d706fa71c3fe58f9448a6e2147f1">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#1e74daf552a5184da76defff2345e45e">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#421a3a0cc5f9d88f4f08f8499d9e5c63">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#bf78c6db9c33dfd79974957d089290de">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#ddf2226768621c2104aed248ce8c2504">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#28099c5428e2d74037597ff3b76397b4">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#c679d4c1a156bcdec2b0b55505b7672c">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#e5e6b9020dfbc214f57f309d309ad769">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#9e97d54c211fb928e3f35e14d872f86f">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#559eb15429cd3009d59a01e778406ca8">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#2ed011696782a9fb7f07279dfbfe9751">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#3619848c88e34930c0c92c1bbb2109aa">pad</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#b145d57e1a5c8ef0bd5bdc2ff1765041">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#6effe828d4bef437197a5e0a8dac074f">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#6476274fc86dbc25c968ba5d8716ef92">runtimeCorrelationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#9d9ad4235c7ef9b1cff6cb8241cf78b5">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#390d6d8ed2caec3f408ed3eeac4cefd8">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel.html#cf1a4bc3d0e7791aadefbe8749ce2c82">streamId</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="cbb9bac29ee4ab936299bf572ca0e866"></a><!-- doxytag: member="CUpti_ActivityKernel::blockX" ref="cbb9bac29ee4ab936299bf572ca0e866" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#cbb9bac29ee4ab936299bf572ca0e866">CUpti_ActivityKernel::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="6af1a44732c5f34db22a6d83703a2e21"></a><!-- doxytag: member="CUpti_ActivityKernel::blockY" ref="6af1a44732c5f34db22a6d83703a2e21" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#6af1a44732c5f34db22a6d83703a2e21">CUpti_ActivityKernel::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="bcd200d948ddea33f583829aaf2e83d6"></a><!-- doxytag: member="CUpti_ActivityKernel::blockZ" ref="bcd200d948ddea33f583829aaf2e83d6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#bcd200d948ddea33f583829aaf2e83d6">CUpti_ActivityKernel::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="48c622133fb1572c4f5772c613d12cdc"></a><!-- doxytag: member="CUpti_ActivityKernel::cacheConfigExecuted" ref="48c622133fb1572c4f5772c613d12cdc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel.html#48c622133fb1572c4f5772c613d12cdc">CUpti_ActivityKernel::cacheConfigExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="613844ef0471a6e4bf8ff990840d2eb4"></a><!-- doxytag: member="CUpti_ActivityKernel::cacheConfigRequested" ref="613844ef0471a6e4bf8ff990840d2eb4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel.html#613844ef0471a6e4bf8ff990840d2eb4">CUpti_ActivityKernel::cacheConfigRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="45e5c6ebf342ea9973d5440d873f362d"></a><!-- doxytag: member="CUpti_ActivityKernel::contextId" ref="45e5c6ebf342ea9973d5440d873f362d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#45e5c6ebf342ea9973d5440d873f362d">CUpti_ActivityKernel::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="2636d706fa71c3fe58f9448a6e2147f1"></a><!-- doxytag: member="CUpti_ActivityKernel::correlationId" ref="2636d706fa71c3fe58f9448a6e2147f1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#2636d706fa71c3fe58f9448a6e2147f1">CUpti_ActivityKernel::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="1e74daf552a5184da76defff2345e45e"></a><!-- doxytag: member="CUpti_ActivityKernel::deviceId" ref="1e74daf552a5184da76defff2345e45e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#1e74daf552a5184da76defff2345e45e">CUpti_ActivityKernel::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="421a3a0cc5f9d88f4f08f8499d9e5c63"></a><!-- doxytag: member="CUpti_ActivityKernel::dynamicSharedMemory" ref="421a3a0cc5f9d88f4f08f8499d9e5c63" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#421a3a0cc5f9d88f4f08f8499d9e5c63">CUpti_ActivityKernel::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="bf78c6db9c33dfd79974957d089290de"></a><!-- doxytag: member="CUpti_ActivityKernel::end" ref="bf78c6db9c33dfd79974957d089290de" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel.html#bf78c6db9c33dfd79974957d089290de">CUpti_ActivityKernel::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="ddf2226768621c2104aed248ce8c2504"></a><!-- doxytag: member="CUpti_ActivityKernel::gridX" ref="ddf2226768621c2104aed248ce8c2504" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#ddf2226768621c2104aed248ce8c2504">CUpti_ActivityKernel::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="28099c5428e2d74037597ff3b76397b4"></a><!-- doxytag: member="CUpti_ActivityKernel::gridY" ref="28099c5428e2d74037597ff3b76397b4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#28099c5428e2d74037597ff3b76397b4">CUpti_ActivityKernel::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="c679d4c1a156bcdec2b0b55505b7672c"></a><!-- doxytag: member="CUpti_ActivityKernel::gridZ" ref="c679d4c1a156bcdec2b0b55505b7672c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#c679d4c1a156bcdec2b0b55505b7672c">CUpti_ActivityKernel::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="e5e6b9020dfbc214f57f309d309ad769"></a><!-- doxytag: member="CUpti_ActivityKernel::kind" ref="e5e6b9020dfbc214f57f309d309ad769" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel.html#e5e6b9020dfbc214f57f309d309ad769">CUpti_ActivityKernel::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="9e97d54c211fb928e3f35e14d872f86f"></a><!-- doxytag: member="CUpti_ActivityKernel::localMemoryPerThread" ref="9e97d54c211fb928e3f35e14d872f86f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#9e97d54c211fb928e3f35e14d872f86f">CUpti_ActivityKernel::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="559eb15429cd3009d59a01e778406ca8"></a><!-- doxytag: member="CUpti_ActivityKernel::localMemoryTotal" ref="559eb15429cd3009d59a01e778406ca8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#559eb15429cd3009d59a01e778406ca8">CUpti_ActivityKernel::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="2ed011696782a9fb7f07279dfbfe9751"></a><!-- doxytag: member="CUpti_ActivityKernel::name" ref="2ed011696782a9fb7f07279dfbfe9751" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel.html#2ed011696782a9fb7f07279dfbfe9751">CUpti_ActivityKernel::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="3619848c88e34930c0c92c1bbb2109aa"></a><!-- doxytag: member="CUpti_ActivityKernel::pad" ref="3619848c88e34930c0c92c1bbb2109aa" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#3619848c88e34930c0c92c1bbb2109aa">CUpti_ActivityKernel::pad</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="b145d57e1a5c8ef0bd5bdc2ff1765041"></a><!-- doxytag: member="CUpti_ActivityKernel::registersPerThread" ref="b145d57e1a5c8ef0bd5bdc2ff1765041" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel.html#b145d57e1a5c8ef0bd5bdc2ff1765041">CUpti_ActivityKernel::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="6effe828d4bef437197a5e0a8dac074f"></a><!-- doxytag: member="CUpti_ActivityKernel::reserved0" ref="6effe828d4bef437197a5e0a8dac074f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel.html#6effe828d4bef437197a5e0a8dac074f">CUpti_ActivityKernel::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="6476274fc86dbc25c968ba5d8716ef92"></a><!-- doxytag: member="CUpti_ActivityKernel::runtimeCorrelationId" ref="6476274fc86dbc25c968ba5d8716ef92" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#6476274fc86dbc25c968ba5d8716ef92">CUpti_ActivityKernel::runtimeCorrelationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The runtime correlation ID of the kernel. Each kernel execution is assigned a unique runtime correlation ID that is identical to the correlation ID in the runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="9d9ad4235c7ef9b1cff6cb8241cf78b5"></a><!-- doxytag: member="CUpti_ActivityKernel::start" ref="9d9ad4235c7ef9b1cff6cb8241cf78b5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel.html#9d9ad4235c7ef9b1cff6cb8241cf78b5">CUpti_ActivityKernel::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="390d6d8ed2caec3f408ed3eeac4cefd8"></a><!-- doxytag: member="CUpti_ActivityKernel::staticSharedMemory" ref="390d6d8ed2caec3f408ed3eeac4cefd8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel.html#390d6d8ed2caec3f408ed3eeac4cefd8">CUpti_ActivityKernel::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="cf1a4bc3d0e7791aadefbe8749ce2c82"></a><!-- doxytag: member="CUpti_ActivityKernel::streamId" ref="cf1a4bc3d0e7791aadefbe8749ce2c82" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel.html#cf1a4bc3d0e7791aadefbe8749ce2c82">CUpti_ActivityKernel::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
