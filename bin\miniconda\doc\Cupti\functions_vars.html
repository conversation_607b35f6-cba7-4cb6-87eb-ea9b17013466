<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6a.html#index_j"><span>j</span></a></li>
      <li><a href="functions_vars_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_a">- a -</a></h3><ul>
<li>address
: <a class="el" href="structCUpti__ActivityMemory.html#741c9a69102c01843912c9b6b3dcfebf">CUpti_ActivityMemory</a>
, <a class="el" href="structCUpti__ActivityMemory2.html#091f2f757e1efd329dc1673c721a894e">CUpti_ActivityMemory2</a>
, <a class="el" href="structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html#c9d2edeeaa902737723687409b8063c7">CUpti_ActivityMemory3::CUpti_ActivityMemory3::PACKED_ALIGNMENT</a>
, <a class="el" href="structCUpti__ActivityMemoryPool.html#3afd484b38b277c0c316bdc63908d234">CUpti_ActivityMemoryPool</a>
, <a class="el" href="structCUpti__ActivityMemory3.html#13e46bbd66ce8af00aec688b1ec8791b">CUpti_ActivityMemory3</a>
, <a class="el" href="structCUpti__ActivityMemoryPool2.html#79960e2d874e5d1957c5ebd639e6feed">CUpti_ActivityMemoryPool2</a>
, <a class="el" href="structCUpti__ActivityUnifiedMemoryCounter2.html#2aae150a66b5187c1e60c775e665ee78">CUpti_ActivityUnifiedMemoryCounter2</a>
<li>allocPC
: <a class="el" href="structCUpti__ActivityMemory.html#50f11689280a94237d9cc939331aaf3e">CUpti_ActivityMemory</a>
<li>allowOverwrite
: <a class="el" href="structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html#0105b536c10c73c2423351628dd6fc3a">NV::Cupti::Checkpoint::CUpti_Checkpoint</a>
<li>allPassesCollected
: <a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#3937b85465b4ed80780a331dd1babbcc">CUpti_Profiler_IsPassCollected_Params</a>
<li>allPassesSubmitted
: <a class="el" href="structCUpti__Profiler__EndPass__Params.html#690480d5291940ff7d4def7e7f3b50b3">CUpti_Profiler_EndPass_Params</a>
<li>architecture
: <a class="el" href="structCUpti__Profiler__DeviceSupported__Params.html#d68ca321150e7bc3c84db9c907f7355a">CUpti_Profiler_DeviceSupported_Params</a>
<li>async
: <a class="el" href="structCUpti__ActivityOpenAccOther.html#703828877b868e7068d7b902b4b5a5a7">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenAcc.html#7f2f1ba8677db32397be3d1bcbf69d8d">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#9a88f776fbccfc4f9660636a9d8cc46e">CUpti_ActivityOpenAccLaunch</a>
<li>asyncMap
: <a class="el" href="structCUpti__ActivityOpenAcc.html#fe0f9caadc974e006e594986cab625ba">CUpti_ActivityOpenAcc</a>
, <a class="el" href="structCUpti__ActivityOpenAccOther.html#3e6779087d5cf058d4d7b771de3c8550">CUpti_ActivityOpenAccOther</a>
, <a class="el" href="structCUpti__ActivityOpenAccLaunch.html#5d9139164b4acee503d8f8d9837251f1">CUpti_ActivityOpenAccLaunch</a>
<li>attr
: <a class="el" href="structCUpti__ActivityPcie.html#0d4511d41ffd96ea9093296189db9883">CUpti_ActivityPcie</a>
<li>attribute
: <a class="el" href="structCUpti__ActivityDeviceAttribute.html#8f446483e9225ddc8201cef2ef0124d5">CUpti_ActivityDeviceAttribute</a>
<li>attributeType
: <a class="el" href="structCUpti__PCSamplingConfigurationInfo.html#61f71926fc2dd6186d2d6759b80c9446">CUpti_PCSamplingConfigurationInfo</a>
</ul>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
