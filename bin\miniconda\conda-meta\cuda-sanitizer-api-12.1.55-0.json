{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-sanitizer-api-12.1.55-0", "features": "", "files": ["LICENSE", "compute-sanitizer/EnableDebuggerInterface.bat", "compute-sanitizer/InterceptorInjectionTarget.dll", "compute-sanitizer/TreeLauncherTargetInjection.dll", "compute-sanitizer/compute-sanitizer.exe", "compute-sanitizer/docs/ComputeSanitizer/graphics/no-padding.png", "compute-sanitizer/docs/ComputeSanitizer/graphics/padding.png", "compute-sanitizer/docs/ComputeSanitizer/graphics/use-after-free.png", "compute-sanitizer/docs/ComputeSanitizer/graphics/use-before-alloc.png", "compute-sanitizer/docs/ComputeSanitizer/index.html", "compute-sanitizer/docs/CopyrightAndLicenses/index.html", "compute-sanitizer/docs/ReleaseNotes/index.html", "compute-sanitizer/docs/SanitizerApi/annotated.html", "compute-sanitizer/docs/SanitizerApi/classes.html", "compute-sanitizer/docs/SanitizerApi/doxygen.css", "compute-sanitizer/docs/SanitizerApi/doxygen.png", "compute-sanitizer/docs/SanitizerApi/ftv2blank.png", "compute-sanitizer/docs/SanitizerApi/ftv2doc.png", "compute-sanitizer/docs/SanitizerApi/ftv2folderclosed.png", "compute-sanitizer/docs/SanitizerApi/ftv2folderopen.png", "compute-sanitizer/docs/SanitizerApi/ftv2lastnode.png", "compute-sanitizer/docs/SanitizerApi/ftv2link.png", "compute-sanitizer/docs/SanitizerApi/ftv2mlastnode.png", "compute-sanitizer/docs/SanitizerApi/ftv2mnode.png", "compute-sanitizer/docs/SanitizerApi/ftv2node.png", "compute-sanitizer/docs/SanitizerApi/ftv2plastnode.png", "compute-sanitizer/docs/SanitizerApi/ftv2pnode.png", "compute-sanitizer/docs/SanitizerApi/ftv2vertline.png", "compute-sanitizer/docs/SanitizerApi/functions.html", "compute-sanitizer/docs/SanitizerApi/functions_vars.html", "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__BARRIER__API.html", "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__CALLBACK__API.html", "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__MEMORY__API.html", "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__PATCHING__API.html", "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__RESULT__API.html", "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__STREAM__API.html", "compute-sanitizer/docs/SanitizerApi/index.html", "compute-sanitizer/docs/SanitizerApi/modules.html", "compute-sanitizer/docs/SanitizerApi/notices-header.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__BatchMemopData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__CallbackData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__EventData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphExecData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphLaunchData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphNodeLaunchData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__LaunchData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__MemcpyData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__MemsetData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceArrayData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceContextData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceFunctionsLazyLoadedData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMemoryData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMempoolData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceModuleData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceStreamData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__SynchronizeData.html", "compute-sanitizer/docs/SanitizerApi/structSanitizer__UvmData.html", "compute-sanitizer/docs/SanitizerApi/tab_b.gif", "compute-sanitizer/docs/SanitizerApi/tab_l.gif", "compute-sanitizer/docs/SanitizerApi/tab_r.gif", "compute-sanitizer/docs/SanitizerApi/tabs.css", "compute-sanitizer/docs/SanitizerApiGuide/index.html", "compute-sanitizer/docs/SanitizerNvtxGuide/index.html", "compute-sanitizer/docs/common/formatting/bg-head.png", "compute-sanitizer/docs/common/formatting/bg-horiz.png", "compute-sanitizer/docs/common/formatting/bg-left.png", "compute-sanitizer/docs/common/formatting/bg-right.png", "compute-sanitizer/docs/common/formatting/bg-sidehead-glow.png", "compute-sanitizer/docs/common/formatting/bg-sidehead.png", "compute-sanitizer/docs/common/formatting/bg-vert.png", "compute-sanitizer/docs/common/formatting/common.min.js", "compute-sanitizer/docs/common/formatting/commonltr.css", "compute-sanitizer/docs/common/formatting/cppapiref.css", "compute-sanitizer/docs/common/formatting/cuda-toolkit-documentation.png", "compute-sanitizer/docs/common/formatting/devtools-documentation.png", "compute-sanitizer/docs/common/formatting/devzone.png", "compute-sanitizer/docs/common/formatting/dita.style.css", "compute-sanitizer/docs/common/formatting/html5shiv-printshiv.min.js", "compute-sanitizer/docs/common/formatting/jquery.ba-hashchange.min.js", "compute-sanitizer/docs/common/formatting/jquery.min.js", "compute-sanitizer/docs/common/formatting/jquery.scrollintoview.min.js", "compute-sanitizer/docs/common/formatting/magnify-dropdown.png", "compute-sanitizer/docs/common/formatting/magnify.png", "compute-sanitizer/docs/common/formatting/nvidia.png", "compute-sanitizer/docs/common/formatting/prettify/lang-Splus.js", "compute-sanitizer/docs/common/formatting/prettify/lang-aea.js", "compute-sanitizer/docs/common/formatting/prettify/lang-agc.js", "compute-sanitizer/docs/common/formatting/prettify/lang-apollo.js", "compute-sanitizer/docs/common/formatting/prettify/lang-basic.js", "compute-sanitizer/docs/common/formatting/prettify/lang-cbm.js", "compute-sanitizer/docs/common/formatting/prettify/lang-cl.js", "compute-sanitizer/docs/common/formatting/prettify/lang-clj.js", "compute-sanitizer/docs/common/formatting/prettify/lang-css.js", "compute-sanitizer/docs/common/formatting/prettify/lang-dart.js", "compute-sanitizer/docs/common/formatting/prettify/lang-el.js", "compute-sanitizer/docs/common/formatting/prettify/lang-erl.js", "compute-sanitizer/docs/common/formatting/prettify/lang-erlang.js", "compute-sanitizer/docs/common/formatting/prettify/lang-fs.js", "compute-sanitizer/docs/common/formatting/prettify/lang-go.js", "compute-sanitizer/docs/common/formatting/prettify/lang-hs.js", "compute-sanitizer/docs/common/formatting/prettify/lang-lasso.js", "compute-sanitizer/docs/common/formatting/prettify/lang-lassoscript.js", "compute-sanitizer/docs/common/formatting/prettify/lang-latex.js", "compute-sanitizer/docs/common/formatting/prettify/lang-lgt.js", "compute-sanitizer/docs/common/formatting/prettify/lang-lisp.js", "compute-sanitizer/docs/common/formatting/prettify/lang-ll.js", "compute-sanitizer/docs/common/formatting/prettify/lang-llvm.js", "compute-sanitizer/docs/common/formatting/prettify/lang-logtalk.js", "compute-sanitizer/docs/common/formatting/prettify/lang-ls.js", "compute-sanitizer/docs/common/formatting/prettify/lang-lsp.js", "compute-sanitizer/docs/common/formatting/prettify/lang-lua.js", "compute-sanitizer/docs/common/formatting/prettify/lang-matlab.js", "compute-sanitizer/docs/common/formatting/prettify/lang-ml.js", "compute-sanitizer/docs/common/formatting/prettify/lang-mumps.js", "compute-sanitizer/docs/common/formatting/prettify/lang-n.js", "compute-sanitizer/docs/common/formatting/prettify/lang-nemerle.js", "compute-sanitizer/docs/common/formatting/prettify/lang-pascal.js", "compute-sanitizer/docs/common/formatting/prettify/lang-proto.js", "compute-sanitizer/docs/common/formatting/prettify/lang-r.js", "compute-sanitizer/docs/common/formatting/prettify/lang-rd.js", "compute-sanitizer/docs/common/formatting/prettify/lang-rkt.js", "compute-sanitizer/docs/common/formatting/prettify/lang-rust.js", "compute-sanitizer/docs/common/formatting/prettify/lang-s.js", "compute-sanitizer/docs/common/formatting/prettify/lang-scala.js", "compute-sanitizer/docs/common/formatting/prettify/lang-scm.js", "compute-sanitizer/docs/common/formatting/prettify/lang-sql.js", "compute-sanitizer/docs/common/formatting/prettify/lang-ss.js", "compute-sanitizer/docs/common/formatting/prettify/lang-swift.js", "compute-sanitizer/docs/common/formatting/prettify/lang-tcl.js", "compute-sanitizer/docs/common/formatting/prettify/lang-tex.js", "compute-sanitizer/docs/common/formatting/prettify/lang-vb.js", "compute-sanitizer/docs/common/formatting/prettify/lang-vbs.js", "compute-sanitizer/docs/common/formatting/prettify/lang-vhd.js", "compute-sanitizer/docs/common/formatting/prettify/lang-vhdl.js", "compute-sanitizer/docs/common/formatting/prettify/lang-wiki.js", "compute-sanitizer/docs/common/formatting/prettify/lang-xq.js", "compute-sanitizer/docs/common/formatting/prettify/lang-xquery.js", "compute-sanitizer/docs/common/formatting/prettify/lang-yaml.js", "compute-sanitizer/docs/common/formatting/prettify/lang-yml.js", "compute-sanitizer/docs/common/formatting/prettify/onLoad.png", "compute-sanitizer/docs/common/formatting/prettify/prettify.css", "compute-sanitizer/docs/common/formatting/prettify/prettify.js", "compute-sanitizer/docs/common/formatting/prettify/run_prettify.js", "compute-sanitizer/docs/common/formatting/qwcode.highlight.css", "compute-sanitizer/docs/common/formatting/search-clear.png", "compute-sanitizer/docs/common/formatting/site.css", "compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-tracker.js", "compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-write.js", "compute-sanitizer/docs/common/scripts/tynt/tynt.js", "compute-sanitizer/docs/index.html", "compute-sanitizer/docs/pdf/ComputeSanitizer.pdf", "compute-sanitizer/docs/pdf/CopyrightAndLicenses.pdf", "compute-sanitizer/docs/pdf/ReleaseNotes.pdf", "compute-sanitizer/docs/pdf/SanitizerApiGuide.pdf", "compute-sanitizer/docs/pdf/SanitizerNvtxGuide.pdf", "compute-sanitizer/docs/search/check.html", "compute-sanitizer/docs/search/files.js", "compute-sanitizer/docs/search/htmlFileInfoList.js", "compute-sanitizer/docs/search/htmlFileList.js", "compute-sanitizer/docs/search/index-1.js", "compute-sanitizer/docs/search/index-2.js", "compute-sanitizer/docs/search/index-3.js", "compute-sanitizer/docs/search/nwSearchFnt.min.js", "compute-sanitizer/docs/search/stemmers/en_stemmer.min.js", "compute-sanitizer/include/generated_cudaD3D10_meta.h", "compute-sanitizer/include/generated_cudaD3D11_meta.h", "compute-sanitizer/include/generated_cudaD3D9_meta.h", "compute-sanitizer/include/generated_cudaGL_meta.h", "compute-sanitizer/include/generated_cuda_d3d10_interop_meta.h", "compute-sanitizer/include/generated_cuda_d3d11_interop_meta.h", "compute-sanitizer/include/generated_cuda_d3d9_interop_meta.h", "compute-sanitizer/include/generated_cuda_gl_interop_meta.h", "compute-sanitizer/include/generated_cuda_meta.h", "compute-sanitizer/include/generated_cuda_profiler_api_meta.h", "compute-sanitizer/include/generated_cuda_runtime_api_meta.h", "compute-sanitizer/include/sanitizer.h", "compute-sanitizer/include/sanitizer_barrier.h", "compute-sanitizer/include/sanitizer_callbacks.h", "compute-sanitizer/include/sanitizer_driver_cbid.h", "compute-sanitizer/include/sanitizer_memory.h", "compute-sanitizer/include/sanitizer_patching.h", "compute-sanitizer/include/sanitizer_result.h", "compute-sanitizer/include/sanitizer_runtime_cbid.h", "compute-sanitizer/include/sanitizer_stream.h", "compute-sanitizer/sanitizer-collection.dll", "compute-sanitizer/sanitizer-public.dll", "compute-sanitizer/sanitizer-public.lib", "compute-sanitizer/x86/InterceptorInjectionTarget.dll", "compute-sanitizer/x86/TreeLauncherTargetInjection.dll"], "fn": "cuda-sanitizer-api-12.1.55-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-sanitizer-api-12.1.55-0", "type": 1}, "md5": "68b9dfefd9432a503482b6b55290cfa2", "name": "cuda-sanitizer-api", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-sanitizer-api-12.1.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "compute-sanitizer/EnableDebuggerInterface.bat", "path_type": "hardlink", "sha256": "68c5f577c737a6784c2f25a79b37a7d08557a51c8cedc5cc89c20eaee4b5fe06", "sha256_in_prefix": "68c5f577c737a6784c2f25a79b37a7d08557a51c8cedc5cc89c20eaee4b5fe06", "size_in_bytes": 97}, {"_path": "compute-sanitizer/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "2048ee0a763d963d9f07c0132e26632b9fe4579f77519b93c3a8dc6f84c5eda1", "sha256_in_prefix": "2048ee0a763d963d9f07c0132e26632b9fe4579f77519b93c3a8dc6f84c5eda1", "size_in_bytes": 1092160}, {"_path": "compute-sanitizer/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "d9f9ae8804bc58037ca2b7740809c1f52eae5951e8cc5a49b7afdccfa9d80696", "sha256_in_prefix": "d9f9ae8804bc58037ca2b7740809c1f52eae5951e8cc5a49b7afdccfa9d80696", "size_in_bytes": 1612872}, {"_path": "compute-sanitizer/compute-sanitizer.exe", "path_type": "hardlink", "sha256": "216c1608ba381e46062714d5f1c88c2a8d3c5b4cbf9b49902e51da4e06d12933", "sha256_in_prefix": "216c1608ba381e46062714d5f1c88c2a8d3c5b4cbf9b49902e51da4e06d12933", "size_in_bytes": 3717728}, {"_path": "compute-sanitizer/docs/ComputeSanitizer/graphics/no-padding.png", "path_type": "hardlink", "sha256": "aeb8c47be35e661ac11ff85fa78bcf06a3bb3b234f76485c2c8d52c06a531fd9", "sha256_in_prefix": "aeb8c47be35e661ac11ff85fa78bcf06a3bb3b234f76485c2c8d52c06a531fd9", "size_in_bytes": 4055}, {"_path": "compute-sanitizer/docs/ComputeSanitizer/graphics/padding.png", "path_type": "hardlink", "sha256": "5160e3867cad691809961c68fa90b83022f3e20d6f2410176f910b85256956c2", "sha256_in_prefix": "5160e3867cad691809961c68fa90b83022f3e20d6f2410176f910b85256956c2", "size_in_bytes": 3753}, {"_path": "compute-sanitizer/docs/ComputeSanitizer/graphics/use-after-free.png", "path_type": "hardlink", "sha256": "03cbd67be9b6a84bfb132765dc1c622d7055de94aeb55efc804dc9a95f9eade4", "sha256_in_prefix": "03cbd67be9b6a84bfb132765dc1c622d7055de94aeb55efc804dc9a95f9eade4", "size_in_bytes": 73869}, {"_path": "compute-sanitizer/docs/ComputeSanitizer/graphics/use-before-alloc.png", "path_type": "hardlink", "sha256": "7bf5f067837b00c4963a61bca2c59b62c4af4996ba2dd288fd46d2fb8439e69d", "sha256_in_prefix": "7bf5f067837b00c4963a61bca2c59b62c4af4996ba2dd288fd46d2fb8439e69d", "size_in_bytes": 73157}, {"_path": "compute-sanitizer/docs/ComputeSanitizer/index.html", "path_type": "hardlink", "sha256": "c153866e1ba7ed17548ec0c5845054eb7dde24b50b9dfea6f2a07b7708f0e269", "sha256_in_prefix": "c153866e1ba7ed17548ec0c5845054eb7dde24b50b9dfea6f2a07b7708f0e269", "size_in_bytes": 287123}, {"_path": "compute-sanitizer/docs/CopyrightAndLicenses/index.html", "path_type": "hardlink", "sha256": "ca6bd15aed1d3acf718cf98d47b5fe7c286da2642ff6b865d017326f621d91af", "sha256_in_prefix": "ca6bd15aed1d3acf718cf98d47b5fe7c286da2642ff6b865d017326f621d91af", "size_in_bytes": 65756}, {"_path": "compute-sanitizer/docs/ReleaseNotes/index.html", "path_type": "hardlink", "sha256": "215b69eef353c9b1c765f616de21258ad630f8565a5eeca977929290bd65f5d2", "sha256_in_prefix": "215b69eef353c9b1c765f616de21258ad630f8565a5eeca977929290bd65f5d2", "size_in_bytes": 59175}, {"_path": "compute-sanitizer/docs/SanitizerApi/annotated.html", "path_type": "hardlink", "sha256": "f9b6cfbd6c136fb60e27c02b7bd632c1f8c93772d66ccc9cbd2de0b25955a0ae", "sha256_in_prefix": "f9b6cfbd6c136fb60e27c02b7bd632c1f8c93772d66ccc9cbd2de0b25955a0ae", "size_in_bytes": 191641}, {"_path": "compute-sanitizer/docs/SanitizerApi/classes.html", "path_type": "hardlink", "sha256": "27376ef0dd03c6755e8c7b11a0a7bd6f59cc24c4fe1fe9880c0b45e1def165ec", "sha256_in_prefix": "27376ef0dd03c6755e8c7b11a0a7bd6f59cc24c4fe1fe9880c0b45e1def165ec", "size_in_bytes": 3705}, {"_path": "compute-sanitizer/docs/SanitizerApi/doxygen.css", "path_type": "hardlink", "sha256": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "sha256_in_prefix": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "size_in_bytes": 5701}, {"_path": "compute-sanitizer/docs/SanitizerApi/doxygen.png", "path_type": "hardlink", "sha256": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "sha256_in_prefix": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "size_in_bytes": 1281}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2blank.png", "path_type": "hardlink", "sha256": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "sha256_in_prefix": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "size_in_bytes": 174}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2doc.png", "path_type": "hardlink", "sha256": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "sha256_in_prefix": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "size_in_bytes": 255}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2folderclosed.png", "path_type": "hardlink", "sha256": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "sha256_in_prefix": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "size_in_bytes": 259}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2folderopen.png", "path_type": "hardlink", "sha256": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "sha256_in_prefix": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "size_in_bytes": 261}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2lastnode.png", "path_type": "hardlink", "sha256": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "sha256_in_prefix": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "size_in_bytes": 233}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2link.png", "path_type": "hardlink", "sha256": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "sha256_in_prefix": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "size_in_bytes": 358}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2mlastnode.png", "path_type": "hardlink", "sha256": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "sha256_in_prefix": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "size_in_bytes": 160}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2mnode.png", "path_type": "hardlink", "sha256": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "sha256_in_prefix": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "size_in_bytes": 194}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2node.png", "path_type": "hardlink", "sha256": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "sha256_in_prefix": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "size_in_bytes": 235}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2plastnode.png", "path_type": "hardlink", "sha256": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "sha256_in_prefix": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "size_in_bytes": 165}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2pnode.png", "path_type": "hardlink", "sha256": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "sha256_in_prefix": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "size_in_bytes": 200}, {"_path": "compute-sanitizer/docs/SanitizerApi/ftv2vertline.png", "path_type": "hardlink", "sha256": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "sha256_in_prefix": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "size_in_bytes": 229}, {"_path": "compute-sanitizer/docs/SanitizerApi/functions.html", "path_type": "hardlink", "sha256": "3150eeac9c3b7469d9c140fc6b7b23edb0f898e01e85fbf7e6297eeb92323dbd", "sha256_in_prefix": "3150eeac9c3b7469d9c140fc6b7b23edb0f898e01e85fbf7e6297eeb92323dbd", "size_in_bytes": 37691}, {"_path": "compute-sanitizer/docs/SanitizerApi/functions_vars.html", "path_type": "hardlink", "sha256": "e845fc7ae5b88ec70d30eb76a33ad583b0ee5739ff6d3d07fc7cfe8e7e44394b", "sha256_in_prefix": "e845fc7ae5b88ec70d30eb76a33ad583b0ee5739ff6d3d07fc7cfe8e7e44394b", "size_in_bytes": 18501}, {"_path": "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__BARRIER__API.html", "path_type": "hardlink", "sha256": "303abe74f53a2ef53f1233ae85ccfbddeaf1eeb256ab0d70956995a9217cc82d", "sha256_in_prefix": "303abe74f53a2ef53f1233ae85ccfbddeaf1eeb256ab0d70956995a9217cc82d", "size_in_bytes": 3763}, {"_path": "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__CALLBACK__API.html", "path_type": "hardlink", "sha256": "0a0290bfc5feda684f26fd1db5e584f266f3648847f413d842f3af07766cbcd5", "sha256_in_prefix": "0a0290bfc5feda684f26fd1db5e584f266f3648847f413d842f3af07766cbcd5", "size_in_bytes": 201259}, {"_path": "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__MEMORY__API.html", "path_type": "hardlink", "sha256": "458cb3e28a9d227986cfb0863cddd7dc1095b5dbc3396ff6b660500c8dd41ee1", "sha256_in_prefix": "458cb3e28a9d227986cfb0863cddd7dc1095b5dbc3396ff6b660500c8dd41ee1", "size_in_bytes": 19236}, {"_path": "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__PATCHING__API.html", "path_type": "hardlink", "sha256": "b39380c4402e8584fc0f54367dd29c2c4bd99c3f8e53081d5cc0b3c2530a9a20", "sha256_in_prefix": "b39380c4402e8584fc0f54367dd29c2c4bd99c3f8e53081d5cc0b3c2530a9a20", "size_in_bytes": 112837}, {"_path": "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__RESULT__API.html", "path_type": "hardlink", "sha256": "2a217a17dde3ac2aa770956b4f22b2b4c6c92e065134a9259b3dddcf05e65d8d", "sha256_in_prefix": "2a217a17dde3ac2aa770956b4f22b2b4c6c92e065134a9259b3dddcf05e65d8d", "size_in_bytes": 14608}, {"_path": "compute-sanitizer/docs/SanitizerApi/group__SANITIZER__STREAM__API.html", "path_type": "hardlink", "sha256": "9627b253e60cf0c34ec32895149a7cd46c7aa9f130da2c66dfebf258154026d1", "sha256_in_prefix": "9627b253e60cf0c34ec32895149a7cd46c7aa9f130da2c66dfebf258154026d1", "size_in_bytes": 8659}, {"_path": "compute-sanitizer/docs/SanitizerApi/index.html", "path_type": "hardlink", "sha256": "6100d2cfef0cc50cd8ad8a1f8a7a7b284da78a6acf92c8e45adf086289539ec4", "sha256_in_prefix": "6100d2cfef0cc50cd8ad8a1f8a7a7b284da78a6acf92c8e45adf086289539ec4", "size_in_bytes": 16632}, {"_path": "compute-sanitizer/docs/SanitizerApi/modules.html", "path_type": "hardlink", "sha256": "e78362f9f708a09a94794442087708678c5d36010600e5b2fc912b649b32d2cc", "sha256_in_prefix": "e78362f9f708a09a94794442087708678c5d36010600e5b2fc912b649b32d2cc", "size_in_bytes": 398790}, {"_path": "compute-sanitizer/docs/SanitizerApi/notices-header.html", "path_type": "hardlink", "sha256": "e2bdc0ef9776ab5e98eb2ff66e413dbe8435c62aa2607a4bdf96b4055be2cd12", "sha256_in_prefix": "e2bdc0ef9776ab5e98eb2ff66e413dbe8435c62aa2607a4bdf96b4055be2cd12", "size_in_bytes": 14556}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__BatchMemopData.html", "path_type": "hardlink", "sha256": "a992551df9620441492c101e158318440faf4d04ea68c508979b0c74a71ee9dc", "sha256_in_prefix": "a992551df9620441492c101e158318440faf4d04ea68c508979b0c74a71ee9dc", "size_in_bytes": 4339}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__CallbackData.html", "path_type": "hardlink", "sha256": "3fffc11c7c1c39a89e29583aae49c17e7262d0c251cb07e576f21bec15405ef6", "sha256_in_prefix": "3fffc11c7c1c39a89e29583aae49c17e7262d0c251cb07e576f21bec15405ef6", "size_in_bytes": 5071}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__EventData.html", "path_type": "hardlink", "sha256": "966a1fa665fcd8c4a114dd16bfd0474039514c5c0742e632b6d1e7a72652cad2", "sha256_in_prefix": "966a1fa665fcd8c4a114dd16bfd0474039514c5c0742e632b6d1e7a72652cad2", "size_in_bytes": 3692}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphExecData.html", "path_type": "hardlink", "sha256": "30ac1351176ab6288d48a9f49fec1febbdc4c33811b021a8563aac2a66f00213", "sha256_in_prefix": "30ac1351176ab6288d48a9f49fec1febbdc4c33811b021a8563aac2a66f00213", "size_in_bytes": 3559}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphLaunchData.html", "path_type": "hardlink", "sha256": "faaff8f4e94c500a993cc057ab3678d977f4b24d11ff857c511cf2d5ffae2bc6", "sha256_in_prefix": "faaff8f4e94c500a993cc057ab3678d977f4b24d11ff857c511cf2d5ffae2bc6", "size_in_bytes": 4103}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphNodeLaunchData.html", "path_type": "hardlink", "sha256": "12db1d4eba62a97bf2aad66efad2ccdba133754c587cf8ed950ce571d1babe55", "sha256_in_prefix": "12db1d4eba62a97bf2aad66efad2ccdba133754c587cf8ed950ce571d1babe55", "size_in_bytes": 6405}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__LaunchData.html", "path_type": "hardlink", "sha256": "818d909b4c599b2952243fe2a5272c32d2edd9e75b5e7864c44fe3b0eea21f79", "sha256_in_prefix": "818d909b4c599b2952243fe2a5272c32d2edd9e75b5e7864c44fe3b0eea21f79", "size_in_bytes": 7953}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__MemcpyData.html", "path_type": "hardlink", "sha256": "a273b924acfc6d8ed04f01284d5da73c70fc82ad16721878c94600bf3bbd9320", "sha256_in_prefix": "a273b924acfc6d8ed04f01284d5da73c70fc82ad16721878c94600bf3bbd9320", "size_in_bytes": 6248}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__MemsetData.html", "path_type": "hardlink", "sha256": "11ad19b2966325506c967cf8dd523dd17bfd26f63480f8eafa70ae9ec3da9e14", "sha256_in_prefix": "11ad19b2966325506c967cf8dd523dd17bfd26f63480f8eafa70ae9ec3da9e14", "size_in_bytes": 4423}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceArrayData.html", "path_type": "hardlink", "sha256": "518f94bb035bbb8c668e449979976b8010af43bd6dc72e0304284b80978c6756", "sha256_in_prefix": "518f94bb035bbb8c668e449979976b8010af43bd6dc72e0304284b80978c6756", "size_in_bytes": 3649}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceContextData.html", "path_type": "hardlink", "sha256": "ce16589a7548568f76e651aa9849a69d7e6e47ac4231b6985aeca15b4daba91a", "sha256_in_prefix": "ce16589a7548568f76e651aa9849a69d7e6e47ac4231b6985aeca15b4daba91a", "size_in_bytes": 3542}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceFunctionsLazyLoadedData.html", "path_type": "hardlink", "sha256": "31bcd6f3039a1d2e91af88cafd0f23841036697211b3e59b78673735de904540", "sha256_in_prefix": "31bcd6f3039a1d2e91af88cafd0f23841036697211b3e59b78673735de904540", "size_in_bytes": 3952}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMemoryData.html", "path_type": "hardlink", "sha256": "bfb2535794fd2882fa02d7858249c537f2086cf74be5e7b6a421108af629bb8c", "sha256_in_prefix": "bfb2535794fd2882fa02d7858249c537f2086cf74be5e7b6a421108af629bb8c", "size_in_bytes": 5927}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMempoolData.html", "path_type": "hardlink", "sha256": "a46b1dcfc43cb06ca0a95ff4585fedaa7ddd4553b035c61c11b46adea84b854a", "sha256_in_prefix": "a46b1dcfc43cb06ca0a95ff4585fedaa7ddd4553b035c61c11b46adea84b854a", "size_in_bytes": 3788}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceModuleData.html", "path_type": "hardlink", "sha256": "070d717d6c62d552bde91e348864b2f088e24aa84a10d6db5794d315124f832f", "sha256_in_prefix": "070d717d6c62d552bde91e348864b2f088e24aa84a10d6db5794d315124f832f", "size_in_bytes": 4153}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceStreamData.html", "path_type": "hardlink", "sha256": "990b4163de3685da7d997f6423640d91faee757d6adb68f688e3b23a92e246d9", "sha256_in_prefix": "990b4163de3685da7d997f6423640d91faee757d6adb68f688e3b23a92e246d9", "size_in_bytes": 3742}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__SynchronizeData.html", "path_type": "hardlink", "sha256": "5f5755f424915d6865b933f5bc247c10f9752d8833b1c80dba644c60396a8905", "sha256_in_prefix": "5f5755f424915d6865b933f5bc247c10f9752d8833b1c80dba644c60396a8905", "size_in_bytes": 3691}, {"_path": "compute-sanitizer/docs/SanitizerApi/structSanitizer__UvmData.html", "path_type": "hardlink", "sha256": "c49776dcced707bf6fd6360bd29038256c3762de0f3632a90a3eae7629289d40", "sha256_in_prefix": "c49776dcced707bf6fd6360bd29038256c3762de0f3632a90a3eae7629289d40", "size_in_bytes": 4062}, {"_path": "compute-sanitizer/docs/SanitizerApi/tab_b.gif", "path_type": "hardlink", "sha256": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "sha256_in_prefix": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "size_in_bytes": 35}, {"_path": "compute-sanitizer/docs/SanitizerApi/tab_l.gif", "path_type": "hardlink", "sha256": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "sha256_in_prefix": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "size_in_bytes": 706}, {"_path": "compute-sanitizer/docs/SanitizerApi/tab_r.gif", "path_type": "hardlink", "sha256": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "sha256_in_prefix": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "size_in_bytes": 2585}, {"_path": "compute-sanitizer/docs/SanitizerApi/tabs.css", "path_type": "hardlink", "sha256": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "sha256_in_prefix": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "size_in_bytes": 1838}, {"_path": "compute-sanitizer/docs/SanitizerApiGuide/index.html", "path_type": "hardlink", "sha256": "35481da6338e88bb6153cc517081ae2b766137cc19f636ac72bfaaa2ac593564", "sha256_in_prefix": "35481da6338e88bb6153cc517081ae2b766137cc19f636ac72bfaaa2ac593564", "size_in_bytes": 46821}, {"_path": "compute-sanitizer/docs/SanitizerNvtxGuide/index.html", "path_type": "hardlink", "sha256": "e4ffc7a4eaa44afefae30f151439e06d682b58f8d31a6d0ee1bd8c688077cffd", "sha256_in_prefix": "e4ffc7a4eaa44afefae30f151439e06d682b58f8d31a6d0ee1bd8c688077cffd", "size_in_bytes": 47882}, {"_path": "compute-sanitizer/docs/common/formatting/bg-head.png", "path_type": "hardlink", "sha256": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "sha256_in_prefix": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "size_in_bytes": 230}, {"_path": "compute-sanitizer/docs/common/formatting/bg-horiz.png", "path_type": "hardlink", "sha256": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "sha256_in_prefix": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "size_in_bytes": 331}, {"_path": "compute-sanitizer/docs/common/formatting/bg-left.png", "path_type": "hardlink", "sha256": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "sha256_in_prefix": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "size_in_bytes": 132}, {"_path": "compute-sanitizer/docs/common/formatting/bg-right.png", "path_type": "hardlink", "sha256": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "sha256_in_prefix": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "size_in_bytes": 131}, {"_path": "compute-sanitizer/docs/common/formatting/bg-sidehead-glow.png", "path_type": "hardlink", "sha256": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "sha256_in_prefix": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "size_in_bytes": 153}, {"_path": "compute-sanitizer/docs/common/formatting/bg-sidehead.png", "path_type": "hardlink", "sha256": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "sha256_in_prefix": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "size_in_bytes": 2827}, {"_path": "compute-sanitizer/docs/common/formatting/bg-vert.png", "path_type": "hardlink", "sha256": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "sha256_in_prefix": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "size_in_bytes": 152}, {"_path": "compute-sanitizer/docs/common/formatting/common.min.js", "path_type": "hardlink", "sha256": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "sha256_in_prefix": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "size_in_bytes": 10628}, {"_path": "compute-sanitizer/docs/common/formatting/commonltr.css", "path_type": "hardlink", "sha256": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "sha256_in_prefix": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "size_in_bytes": 6097}, {"_path": "compute-sanitizer/docs/common/formatting/cppapiref.css", "path_type": "hardlink", "sha256": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "sha256_in_prefix": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "size_in_bytes": 8927}, {"_path": "compute-sanitizer/docs/common/formatting/cuda-toolkit-documentation.png", "path_type": "hardlink", "sha256": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "sha256_in_prefix": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "size_in_bytes": 9129}, {"_path": "compute-sanitizer/docs/common/formatting/devtools-documentation.png", "path_type": "hardlink", "sha256": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "sha256_in_prefix": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "size_in_bytes": 4359}, {"_path": "compute-sanitizer/docs/common/formatting/devzone.png", "path_type": "hardlink", "sha256": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "sha256_in_prefix": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "size_in_bytes": 10349}, {"_path": "compute-sanitizer/docs/common/formatting/dita.style.css", "path_type": "hardlink", "sha256": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "sha256_in_prefix": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "size_in_bytes": 34852}, {"_path": "compute-sanitizer/docs/common/formatting/html5shiv-printshiv.min.js", "path_type": "hardlink", "sha256": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "sha256_in_prefix": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "size_in_bytes": 3989}, {"_path": "compute-sanitizer/docs/common/formatting/jquery.ba-hashchange.min.js", "path_type": "hardlink", "sha256": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "sha256_in_prefix": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "size_in_bytes": 1604}, {"_path": "compute-sanitizer/docs/common/formatting/jquery.min.js", "path_type": "hardlink", "sha256": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "sha256_in_prefix": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "size_in_bytes": 92633}, {"_path": "compute-sanitizer/docs/common/formatting/jquery.scrollintoview.min.js", "path_type": "hardlink", "sha256": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "sha256_in_prefix": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "size_in_bytes": 3501}, {"_path": "compute-sanitizer/docs/common/formatting/magnify-dropdown.png", "path_type": "hardlink", "sha256": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "sha256_in_prefix": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "size_in_bytes": 1139}, {"_path": "compute-sanitizer/docs/common/formatting/magnify.png", "path_type": "hardlink", "sha256": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "sha256_in_prefix": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "size_in_bytes": 1100}, {"_path": "compute-sanitizer/docs/common/formatting/nvidia.png", "path_type": "hardlink", "sha256": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "sha256_in_prefix": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "size_in_bytes": 4442}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-Splus.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-aea.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-agc.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-apollo.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-basic.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-cbm.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-cl.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-clj.js", "path_type": "hardlink", "sha256": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "sha256_in_prefix": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "size_in_bytes": 1484}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-css.js", "path_type": "hardlink", "sha256": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "sha256_in_prefix": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "size_in_bytes": 1525}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-dart.js", "path_type": "hardlink", "sha256": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "sha256_in_prefix": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "size_in_bytes": 1626}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-el.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-erl.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-erlang.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-fs.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-go.js", "path_type": "hardlink", "sha256": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "sha256_in_prefix": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "size_in_bytes": 884}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-hs.js", "path_type": "hardlink", "sha256": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "sha256_in_prefix": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "size_in_bytes": 1217}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-lasso.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-lassoscript.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-latex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-lgt.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-lisp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-ll.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-llvm.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-logtalk.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-ls.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-lsp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-lua.js", "path_type": "hardlink", "sha256": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "sha256_in_prefix": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "size_in_bytes": 1162}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-matlab.js", "path_type": "hardlink", "sha256": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "sha256_in_prefix": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "size_in_bytes": 21092}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-ml.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-mumps.js", "path_type": "hardlink", "sha256": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "sha256_in_prefix": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "size_in_bytes": 1500}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-n.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-nemerle.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-pascal.js", "path_type": "hardlink", "sha256": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "sha256_in_prefix": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "size_in_bytes": 1332}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-proto.js", "path_type": "hardlink", "sha256": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "sha256_in_prefix": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "size_in_bytes": 891}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-r.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-rd.js", "path_type": "hardlink", "sha256": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "sha256_in_prefix": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "size_in_bytes": 862}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-rkt.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-rust.js", "path_type": "hardlink", "sha256": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "sha256_in_prefix": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "size_in_bytes": 2254}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-s.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-scala.js", "path_type": "hardlink", "sha256": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "sha256_in_prefix": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "size_in_bytes": 1554}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-scm.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-sql.js", "path_type": "hardlink", "sha256": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "sha256_in_prefix": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "size_in_bytes": 2404}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-ss.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-swift.js", "path_type": "hardlink", "sha256": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "sha256_in_prefix": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "size_in_bytes": 2050}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-tcl.js", "path_type": "hardlink", "sha256": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "sha256_in_prefix": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "size_in_bytes": 1261}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-tex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-vb.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-vbs.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-vhd.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-vhdl.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-wiki.js", "path_type": "hardlink", "sha256": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "sha256_in_prefix": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "size_in_bytes": 1157}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-xq.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-xquery.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-yaml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/lang-yml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/onLoad.png", "path_type": "hardlink", "sha256": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "sha256_in_prefix": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "size_in_bytes": 110}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/prettify.css", "path_type": "hardlink", "sha256": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "sha256_in_prefix": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "size_in_bytes": 675}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/prettify.js", "path_type": "hardlink", "sha256": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "sha256_in_prefix": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "size_in_bytes": 15307}, {"_path": "compute-sanitizer/docs/common/formatting/prettify/run_prettify.js", "path_type": "hardlink", "sha256": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "sha256_in_prefix": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "size_in_bytes": 18100}, {"_path": "compute-sanitizer/docs/common/formatting/qwcode.highlight.css", "path_type": "hardlink", "sha256": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "sha256_in_prefix": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "size_in_bytes": 908}, {"_path": "compute-sanitizer/docs/common/formatting/search-clear.png", "path_type": "hardlink", "sha256": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "sha256_in_prefix": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "size_in_bytes": 3638}, {"_path": "compute-sanitizer/docs/common/formatting/site.css", "path_type": "hardlink", "sha256": "dfa996ee28d5b956e18054960dd079dc7f53b6ee6a458afaee4b8d4b9bb5fb50", "sha256_in_prefix": "dfa996ee28d5b956e18054960dd079dc7f53b6ee6a458afaee4b8d4b9bb5fb50", "size_in_bytes": 12469}, {"_path": "compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-tracker.js", "path_type": "hardlink", "sha256": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "sha256_in_prefix": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "size_in_bytes": 117}, {"_path": "compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-write.js", "path_type": "hardlink", "sha256": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "sha256_in_prefix": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "size_in_bytes": 221}, {"_path": "compute-sanitizer/docs/common/scripts/tynt/tynt.js", "path_type": "hardlink", "sha256": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "sha256_in_prefix": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "size_in_bytes": 316}, {"_path": "compute-sanitizer/docs/index.html", "path_type": "hardlink", "sha256": "7dee5c227de0ae49bcfb277a0d3326adaa418a3bc3666cf3bb14655e4e005f34", "sha256_in_prefix": "7dee5c227de0ae49bcfb277a0d3326adaa418a3bc3666cf3bb14655e4e005f34", "size_in_bytes": 6400}, {"_path": "compute-sanitizer/docs/pdf/ComputeSanitizer.pdf", "path_type": "hardlink", "sha256": "9b8aeff4a0950bc2708242a70b9ce55dcc6972f21ea533e8898c442138477f13", "sha256_in_prefix": "9b8aeff4a0950bc2708242a70b9ce55dcc6972f21ea533e8898c442138477f13", "size_in_bytes": 1902357}, {"_path": "compute-sanitizer/docs/pdf/CopyrightAndLicenses.pdf", "path_type": "hardlink", "sha256": "25726e6485dc232de70c46f1f4bb8ffbcf3be1518e6b40c51cd41b55cfd8ce07", "sha256_in_prefix": "25726e6485dc232de70c46f1f4bb8ffbcf3be1518e6b40c51cd41b55cfd8ce07", "size_in_bytes": 1492251}, {"_path": "compute-sanitizer/docs/pdf/ReleaseNotes.pdf", "path_type": "hardlink", "sha256": "e8b12d760c35107f8432ab181f0d16094ae87ddc5f9d09795ebf625ba2151020", "sha256_in_prefix": "e8b12d760c35107f8432ab181f0d16094ae87ddc5f9d09795ebf625ba2151020", "size_in_bytes": 1503766}, {"_path": "compute-sanitizer/docs/pdf/SanitizerApiGuide.pdf", "path_type": "hardlink", "sha256": "68cbf53aed2baf47cc5061fb554c6edda100b54fdf62b8f98efd2edc17e0512f", "sha256_in_prefix": "68cbf53aed2baf47cc5061fb554c6edda100b54fdf62b8f98efd2edc17e0512f", "size_in_bytes": 1527533}, {"_path": "compute-sanitizer/docs/pdf/SanitizerNvtxGuide.pdf", "path_type": "hardlink", "sha256": "e5a3b3d212ee9da901a34f941ce49e720d9d3be97d6e2e45d0494b6f0887ab89", "sha256_in_prefix": "e5a3b3d212ee9da901a34f941ce49e720d9d3be97d6e2e45d0494b6f0887ab89", "size_in_bytes": 1514281}, {"_path": "compute-sanitizer/docs/search/check.html", "path_type": "hardlink", "sha256": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "sha256_in_prefix": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "size_in_bytes": 1252}, {"_path": "compute-sanitizer/docs/search/files.js", "path_type": "hardlink", "sha256": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "sha256_in_prefix": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "size_in_bytes": 99}, {"_path": "compute-sanitizer/docs/search/htmlFileInfoList.js", "path_type": "hardlink", "sha256": "a6ed9528e0168320d25bc6247105e8c03a9a36147b9d72a79503e9009b55a2e4", "sha256_in_prefix": "a6ed9528e0168320d25bc6247105e8c03a9a36147b9d72a79503e9009b55a2e4", "size_in_bytes": 3325}, {"_path": "compute-sanitizer/docs/search/htmlFileList.js", "path_type": "hardlink", "sha256": "98f2b532f3f8eb5f72a9e69d694f70679a976f961820eba42528659777d1048c", "sha256_in_prefix": "98f2b532f3f8eb5f72a9e69d694f70679a976f961820eba42528659777d1048c", "size_in_bytes": 2084}, {"_path": "compute-sanitizer/docs/search/index-1.js", "path_type": "hardlink", "sha256": "d359194d4d4fa5d6502d6a8311ab47d1f4c41b78de2a54e5006efb266542eba6", "sha256_in_prefix": "d359194d4d4fa5d6502d6a8311ab47d1f4c41b78de2a54e5006efb266542eba6", "size_in_bytes": 26912}, {"_path": "compute-sanitizer/docs/search/index-2.js", "path_type": "hardlink", "sha256": "65ef03e436f8d17e37f908e419366ebebe514352540e1f901e9fe5b8552eb699", "sha256_in_prefix": "65ef03e436f8d17e37f908e419366ebebe514352540e1f901e9fe5b8552eb699", "size_in_bytes": 30608}, {"_path": "compute-sanitizer/docs/search/index-3.js", "path_type": "hardlink", "sha256": "37d6424864e37d799e3a1e182c3e547cd4dbcc3ac21eecb3fa3562aadc5c322b", "sha256_in_prefix": "37d6424864e37d799e3a1e182c3e547cd4dbcc3ac21eecb3fa3562aadc5c322b", "size_in_bytes": 36823}, {"_path": "compute-sanitizer/docs/search/nwSearchFnt.min.js", "path_type": "hardlink", "sha256": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "sha256_in_prefix": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "size_in_bytes": 12073}, {"_path": "compute-sanitizer/docs/search/stemmers/en_stemmer.min.js", "path_type": "hardlink", "sha256": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "sha256_in_prefix": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "size_in_bytes": 3531}, {"_path": "compute-sanitizer/include/generated_cudaD3D10_meta.h", "path_type": "hardlink", "sha256": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "sha256_in_prefix": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "size_in_bytes": 4439}, {"_path": "compute-sanitizer/include/generated_cudaD3D11_meta.h", "path_type": "hardlink", "sha256": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "sha256_in_prefix": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "size_in_bytes": 1823}, {"_path": "compute-sanitizer/include/generated_cudaD3D9_meta.h", "path_type": "hardlink", "sha256": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "sha256_in_prefix": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "size_in_bytes": 5379}, {"_path": "compute-sanitizer/include/generated_cudaGL_meta.h", "path_type": "hardlink", "sha256": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "sha256_in_prefix": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "size_in_bytes": 3344}, {"_path": "compute-sanitizer/include/generated_cuda_d3d10_interop_meta.h", "path_type": "hardlink", "sha256": "f5f27e2aaff104e93316f7c11f158a65bd358b982952be13e25bde0cb7238ca0", "sha256_in_prefix": "f5f27e2aaff104e93316f7c11f158a65bd358b982952be13e25bde0cb7238ca0", "size_in_bytes": 3366}, {"_path": "compute-sanitizer/include/generated_cuda_d3d11_interop_meta.h", "path_type": "hardlink", "sha256": "36cb61c510245590b0cb862997d405d69714b48ff8bdc85ddf7c9a7aedfad637", "sha256_in_prefix": "36cb61c510245590b0cb862997d405d69714b48ff8bdc85ddf7c9a7aedfad637", "size_in_bytes": 1502}, {"_path": "compute-sanitizer/include/generated_cuda_d3d9_interop_meta.h", "path_type": "hardlink", "sha256": "b57f91a7968819fc04f271af446ee2fc58dcc1624d623cc69fa0262e4a9c117f", "sha256_in_prefix": "b57f91a7968819fc04f271af446ee2fc58dcc1624d623cc69fa0262e4a9c117f", "size_in_bytes": 4180}, {"_path": "compute-sanitizer/include/generated_cuda_gl_interop_meta.h", "path_type": "hardlink", "sha256": "e78afc412e339da36d5e3fb66250f9a0c97eef86eee10fa3dffbbc7a1cd3ee4d", "sha256_in_prefix": "e78afc412e339da36d5e3fb66250f9a0c97eef86eee10fa3dffbbc7a1cd3ee4d", "size_in_bytes": 2494}, {"_path": "compute-sanitizer/include/generated_cuda_meta.h", "path_type": "hardlink", "sha256": "8dbedd3a439c47f4a47d179fc47d53f991c7257aa1a6b054ac51ba737c0a820e", "sha256_in_prefix": "8dbedd3a439c47f4a47d179fc47d53f991c7257aa1a6b054ac51ba737c0a820e", "size_in_bytes": 82552}, {"_path": "compute-sanitizer/include/generated_cuda_profiler_api_meta.h", "path_type": "hardlink", "sha256": "9fc1cd434c21fc94b3a710dfbcd5c2c67aa82982c83b86e017f95e4cccaa2645", "sha256_in_prefix": "9fc1cd434c21fc94b3a710dfbcd5c2c67aa82982c83b86e017f95e4cccaa2645", "size_in_bytes": 578}, {"_path": "compute-sanitizer/include/generated_cuda_runtime_api_meta.h", "path_type": "hardlink", "sha256": "da763565763b294569c29e5999a15527c5772e7b86308e1a1a478c870ae58dba", "sha256_in_prefix": "da763565763b294569c29e5999a15527c5772e7b86308e1a1a478c870ae58dba", "size_in_bytes": 66908}, {"_path": "compute-sanitizer/include/sanitizer.h", "path_type": "hardlink", "sha256": "d5147fade6811b6076fdedfcccf95248dc6badc9eb9fc80cf2732835a98b4fab", "sha256_in_prefix": "d5147fade6811b6076fdedfcccf95248dc6badc9eb9fc80cf2732835a98b4fab", "size_in_bytes": 3525}, {"_path": "compute-sanitizer/include/sanitizer_barrier.h", "path_type": "hardlink", "sha256": "cac8dc73573f111e845fe55b008dbedad79eef7ecd242e4e3a9b4f7ed4bb8f80", "sha256_in_prefix": "cac8dc73573f111e845fe55b008dbedad79eef7ecd242e4e3a9b4f7ed4bb8f80", "size_in_bytes": 3864}, {"_path": "compute-sanitizer/include/sanitizer_callbacks.h", "path_type": "hardlink", "sha256": "0c17ed3a3a19c45162f88b56b17fec3fb1b5316bc956b199a8c9cfbd755cb1d7", "sha256_in_prefix": "0c17ed3a3a19c45162f88b56b17fec3fb1b5316bc956b199a8c9cfbd755cb1d7", "size_in_bytes": 55005}, {"_path": "compute-sanitizer/include/sanitizer_driver_cbid.h", "path_type": "hardlink", "sha256": "877570f6b51cd9f9cbb711638d85bf6c1d648f725ae1446a832ae88c127f6a39", "sha256_in_prefix": "877570f6b51cd9f9cbb711638d85bf6c1d648f725ae1446a832ae88c127f6a39", "size_in_bytes": 70444}, {"_path": "compute-sanitizer/include/sanitizer_memory.h", "path_type": "hardlink", "sha256": "4152a34839450967cbc2ec5bed15947e549246bca3ed455eb5e0444ec7aef555", "sha256_in_prefix": "4152a34839450967cbc2ec5bed15947e549246bca3ed455eb5e0444ec7aef555", "size_in_bytes": 7757}, {"_path": "compute-sanitizer/include/sanitizer_patching.h", "path_type": "hardlink", "sha256": "bc830ac404e3d6d3d183bf7105cd6ff6b0ea4cd419df694779a0fc7c35b369cc", "sha256_in_prefix": "bc830ac404e3d6d3d183bf7105cd6ff6b0ea4cd419df694779a0fc7c35b369cc", "size_in_bytes": 36220}, {"_path": "compute-sanitizer/include/sanitizer_result.h", "path_type": "hardlink", "sha256": "db16edef044109e819618c4c06fd337433d44d228a2540c38fb35d5dbd596194", "sha256_in_prefix": "db16edef044109e819618c4c06fd337433d44d228a2540c38fb35d5dbd596194", "size_in_bytes": 6251}, {"_path": "compute-sanitizer/include/sanitizer_runtime_cbid.h", "path_type": "hardlink", "sha256": "e5dd8d4d10df1cc6a193e5c5ec2c110e279423674c1857427c30486a5d4983c9", "sha256_in_prefix": "e5dd8d4d10df1cc6a193e5c5ec2c110e279423674c1857427c30486a5d4983c9", "size_in_bytes": 43240}, {"_path": "compute-sanitizer/include/sanitizer_stream.h", "path_type": "hardlink", "sha256": "b78b84bad0d90f6d62961b4d15c151e2d7f15822c72b4469841d2b60c97a1503", "sha256_in_prefix": "b78b84bad0d90f6d62961b4d15c151e2d7f15822c72b4469841d2b60c97a1503", "size_in_bytes": 4965}, {"_path": "compute-sanitizer/sanitizer-collection.dll", "path_type": "hardlink", "sha256": "2a8353bdd86a289ad4748bf9f3f17c562677a3f944edfa3732a5b89f53b11793", "sha256_in_prefix": "2a8353bdd86a289ad4748bf9f3f17c562677a3f944edfa3732a5b89f53b11793", "size_in_bytes": 4978264}, {"_path": "compute-sanitizer/sanitizer-public.dll", "path_type": "hardlink", "sha256": "9411239134dfee2d8b1e4bbe0344fcec17d15fe8e30e94ffaa4ddebb96b972d3", "sha256_in_prefix": "9411239134dfee2d8b1e4bbe0344fcec17d15fe8e30e94ffaa4ddebb96b972d3", "size_in_bytes": 822344}, {"_path": "compute-sanitizer/sanitizer-public.lib", "path_type": "hardlink", "sha256": "61e9cec071f64971a8edeed4811df5b6fbde592352fd128ab40dadce4c224f2c", "sha256_in_prefix": "61e9cec071f64971a8edeed4811df5b6fbde592352fd128ab40dadce4c224f2c", "size_in_bytes": 9422}, {"_path": "compute-sanitizer/x86/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "f78362c8f21834007d1aa037eea13be1a3e3cebdeca8461758ea943ed8caf25c", "sha256_in_prefix": "f78362c8f21834007d1aa037eea13be1a3e3cebdeca8461758ea943ed8caf25c", "size_in_bytes": 897120}, {"_path": "compute-sanitizer/x86/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "f3a9b0a7231b8b9bbc8e7d324ded5f7983130f2b042a4397b6454c5fbfcdabc8", "sha256_in_prefix": "f3a9b0a7231b8b9bbc8e7d324ded5f7983130f2b042a4397b6454c5fbfcdabc8", "size_in_bytes": 1402984}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 13541186, "subdir": "win-64", "timestamp": 1674621353000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-sanitizer-api-12.1.55-0.tar.bz2", "version": "12.1.55"}