<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityInstantaneousMetric Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityInstantaneousMetric Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityInstantaneousMetric" -->The activity record for an instantaneous CUPTI metric.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html#511641c14fa7a3036115db84f95f718d">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html#5998404f431736262d803dfe7e582de2">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html#5b5400eac530fb39a3c629f115353c9a">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html#79c981d76070b10c79d051325ccc1dda">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html#2db3f914716449a1b321511903e519cc">pad</a> [3]</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html#03e0fe94bfb2add6b6aa68ac9163d4d3">timestamp</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityInstantaneousMetric.html#f137ea2441d6b8328c35dd99dcdba9a5">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents the collection of a CUPTI metric value (CUPTI_ACTIVITY_KIND_METRIC) at a particular instance. This activity record kind is not produced by the activity API but is included for completeness and ease-of-use. Profiler frameworks built on top of CUPTI that collect metric data may choose to use this type to store the collected metric data. <hr><h2>Field Documentation</h2>
<a class="anchor" name="511641c14fa7a3036115db84f95f718d"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetric::deviceId" ref="511641c14fa7a3036115db84f95f718d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#511641c14fa7a3036115db84f95f718d">CUpti_ActivityInstantaneousMetric::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device id 
</div>
</div><p>
<a class="anchor" name="5998404f431736262d803dfe7e582de2"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetric::flags" ref="5998404f431736262d803dfe7e582de2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#5998404f431736262d803dfe7e582de2">CUpti_ActivityInstantaneousMetric::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The properties of this metric. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="5b5400eac530fb39a3c629f115353c9a"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetric::id" ref="5b5400eac530fb39a3c629f115353c9a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__METRIC__API.html#g0d2977d7b73cd78f216d5526998a92d4">CUpti_MetricID</a> <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#5b5400eac530fb39a3c629f115353c9a">CUpti_ActivityInstantaneousMetric::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The metric ID. 
</div>
</div><p>
<a class="anchor" name="79c981d76070b10c79d051325ccc1dda"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetric::kind" ref="79c981d76070b10c79d051325ccc1dda" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#79c981d76070b10c79d051325ccc1dda">CUpti_ActivityInstantaneousMetric::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_INSTANTANEOUS_METRIC. 
</div>
</div><p>
<a class="anchor" name="2db3f914716449a1b321511903e519cc"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetric::pad" ref="2db3f914716449a1b321511903e519cc" args="[3]" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#2db3f914716449a1b321511903e519cc">CUpti_ActivityInstantaneousMetric::pad</a>[3]          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. reserved for internal use 
</div>
</div><p>
<a class="anchor" name="03e0fe94bfb2add6b6aa68ac9163d4d3"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetric::timestamp" ref="03e0fe94bfb2add6b6aa68ac9163d4d3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#03e0fe94bfb2add6b6aa68ac9163d4d3">CUpti_ActivityInstantaneousMetric::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp at which metric is sampled 
</div>
</div><p>
<a class="anchor" name="f137ea2441d6b8328c35dd99dcdba9a5"></a><!-- doxytag: member="CUpti_ActivityInstantaneousMetric::value" ref="f137ea2441d6b8328c35dd99dcdba9a5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionCUpti__MetricValue.html">CUpti_MetricValue</a> <a class="el" href="structCUpti__ActivityInstantaneousMetric.html#f137ea2441d6b8328c35dd99dcdba9a5">CUpti_ActivityInstantaneousMetric::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The metric value. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
