<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivitySynchronization Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivitySynchronization Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivitySynchronization" -->The activity record for synchronization management.  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#fc87e818eb828419da1c69e8f62d3426">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#8cd8e5f8bd4f08fa554f968d58e95312">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#083e4344decfea8b7d8b1ee45c822c52">cudaEventId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#c4a1752012e7275052cddb462afc2e3a">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#edbd1b06b503775c07c8667447aecaf5">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#ab12527761f1e688558d6eca39722ff9">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#f8c4a5d97a842dbedf012993923e21e3">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g80e1eb47615e31021f574df8ebbe5d9a">CUpti_ActivitySynchronizationType</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivitySynchronization.html#394d7c722338b1fb8c425efe79e8f1b2">type</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity is used to track various CUDA synchronization APIs. (CUPTI_ACTIVITY_KIND_SYNCHRONIZATION). <hr><h2>Field Documentation</h2>
<a class="anchor" name="fc87e818eb828419da1c69e8f62d3426"></a><!-- doxytag: member="CUpti_ActivitySynchronization::contextId" ref="fc87e818eb828419da1c69e8f62d3426" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySynchronization.html#fc87e818eb828419da1c69e8f62d3426">CUpti_ActivitySynchronization::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context for which the synchronization API is called. In case of context synchronization API it is the context id for which the API is called. In case of stream/event synchronization it is the ID of the context where the stream/event was created. 
</div>
</div><p>
<a class="anchor" name="8cd8e5f8bd4f08fa554f968d58e95312"></a><!-- doxytag: member="CUpti_ActivitySynchronization::correlationId" ref="8cd8e5f8bd4f08fa554f968d58e95312" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySynchronization.html#8cd8e5f8bd4f08fa554f968d58e95312">CUpti_ActivitySynchronization::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the API to which this result is associated. 
</div>
</div><p>
<a class="anchor" name="083e4344decfea8b7d8b1ee45c822c52"></a><!-- doxytag: member="CUpti_ActivitySynchronization::cudaEventId" ref="083e4344decfea8b7d8b1ee45c822c52" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySynchronization.html#083e4344decfea8b7d8b1ee45c822c52">CUpti_ActivitySynchronization::cudaEventId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The event ID for which the synchronization API is called. A CUPTI_SYNCHRONIZATION_INVALID_VALUE value indicate the field is not applicable for this record. Not valid for cuCtxSynchronize, cuStreamSynchronize. 
</div>
</div><p>
<a class="anchor" name="c4a1752012e7275052cddb462afc2e3a"></a><!-- doxytag: member="CUpti_ActivitySynchronization::end" ref="c4a1752012e7275052cddb462afc2e3a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivitySynchronization.html#c4a1752012e7275052cddb462afc2e3a">CUpti_ActivitySynchronization::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the function, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the function. 
</div>
</div><p>
<a class="anchor" name="edbd1b06b503775c07c8667447aecaf5"></a><!-- doxytag: member="CUpti_ActivitySynchronization::kind" ref="edbd1b06b503775c07c8667447aecaf5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivitySynchronization.html#edbd1b06b503775c07c8667447aecaf5">CUpti_ActivitySynchronization::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_SYNCHRONIZATION. 
</div>
</div><p>
<a class="anchor" name="ab12527761f1e688558d6eca39722ff9"></a><!-- doxytag: member="CUpti_ActivitySynchronization::start" ref="ab12527761f1e688558d6eca39722ff9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivitySynchronization.html#ab12527761f1e688558d6eca39722ff9">CUpti_ActivitySynchronization::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the function, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the function. 
</div>
</div><p>
<a class="anchor" name="f8c4a5d97a842dbedf012993923e21e3"></a><!-- doxytag: member="CUpti_ActivitySynchronization::streamId" ref="f8c4a5d97a842dbedf012993923e21e3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivitySynchronization.html#f8c4a5d97a842dbedf012993923e21e3">CUpti_ActivitySynchronization::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The compute stream for which the synchronization API is called. A CUPTI_SYNCHRONIZATION_INVALID_VALUE value indicate the field is not applicable for this record. Not valid for cuCtxSynchronize, cuEventSynchronize. 
</div>
</div><p>
<a class="anchor" name="394d7c722338b1fb8c425efe79e8f1b2"></a><!-- doxytag: member="CUpti_ActivitySynchronization::type" ref="394d7c722338b1fb8c425efe79e8f1b2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g80e1eb47615e31021f574df8ebbe5d9a">CUpti_ActivitySynchronizationType</a> <a class="el" href="structCUpti__ActivitySynchronization.html#394d7c722338b1fb8c425efe79e8f1b2">CUpti_ActivitySynchronization::type</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The type of record. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
