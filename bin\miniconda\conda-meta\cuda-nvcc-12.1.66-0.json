{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0", "constrains": [], "depends": [], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvcc-12.1.66-0", "features": "", "files": ["LICENSE", "bin/__nvcc_device_query.exe", "bin/bin2c.exe", "bin/crt/link.stub", "bin/crt/prelink.stub", "bin/cudafe++.exe", "bin/fatbinary.exe", "bin/nvcc.exe", "bin/nvcc.profile", "bin/nvlink.exe", "bin/ptxas.exe", "include/crt/common_functions.h", "include/crt/cudacc_ext.h", "include/crt/device_double_functions.h", "include/crt/device_double_functions.hpp", "include/crt/device_functions.h", "include/crt/device_functions.hpp", "include/crt/func_macro.h", "include/crt/host_config.h", "include/crt/host_defines.h", "include/crt/host_runtime.h", "include/crt/math_functions.h", "include/crt/math_functions.hpp", "include/crt/mma.h", "include/crt/mma.hpp", "include/crt/nvfunctional", "include/crt/sm_70_rt.h", "include/crt/sm_70_rt.hpp", "include/crt/sm_80_rt.h", "include/crt/sm_80_rt.hpp", "include/crt/sm_90_rt.h", "include/crt/sm_90_rt.hpp", "include/crt/storage_class.h", "include/fatbinary_section.h", "include/nvPTXCompiler.h", "lib/x64/nvptxcompiler_static.lib", "nvvm/bin/cicc.exe", "nvvm/bin/nvvm64_40_0.dll", "nvvm/include/nvvm.h", "nvvm/lib/x64/nvvm.lib", "nvvm/libdevice/libdevice.10.bc"], "fn": "cuda-nvcc-12.1.66-0.tar.bz2", "license": "", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvcc-12.1.66-0", "type": 1}, "md5": "087bb866d98d29d2290aa9f36ef0d2f7", "name": "cuda-nvcc", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\cuda-nvcc-12.1.66-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "bin/__nvcc_device_query.exe", "path_type": "hardlink", "sha256": "8b171032bf855aa0d77d36705427439198c66583a22430841e9a2d18dbf2d342", "sha256_in_prefix": "8b171032bf855aa0d77d36705427439198c66583a22430841e9a2d18dbf2d342", "size_in_bytes": 84480}, {"_path": "bin/bin2c.exe", "path_type": "hardlink", "sha256": "241d9b7a029fa3e0f9f5cf676054b9f3d6764729c1d0faf5f4bdf6464cd8ed3d", "sha256_in_prefix": "241d9b7a029fa3e0f9f5cf676054b9f3d6764729c1d0faf5f4bdf6464cd8ed3d", "size_in_bytes": 227840}, {"_path": "bin/crt/link.stub", "path_type": "hardlink", "sha256": "dc9358c079768152f52489a92a1c33025ad1aada31cb63c5185d276fb48d19c4", "sha256_in_prefix": "dc9358c079768152f52489a92a1c33025ad1aada31cb63c5185d276fb48d19c4", "size_in_bytes": 6485}, {"_path": "bin/crt/prelink.stub", "path_type": "hardlink", "sha256": "f91db9f00463d557ceb1fbd4ce05b6d1257677dd449cfb5f02244e2a51183f13", "sha256_in_prefix": "f91db9f00463d557ceb1fbd4ce05b6d1257677dd449cfb5f02244e2a51183f13", "size_in_bytes": 4069}, {"_path": "bin/cudafe++.exe", "path_type": "hardlink", "sha256": "6cc6a2f76b4ec5bc9ba9ad9f70e0573bd401d626a657abcf3ee4890ce64f928d", "sha256_in_prefix": "6cc6a2f76b4ec5bc9ba9ad9f70e0573bd401d626a657abcf3ee4890ce64f928d", "size_in_bytes": 6239744}, {"_path": "bin/fatbinary.exe", "path_type": "hardlink", "sha256": "583946cda2bfcdc2c65370f5da99ba5d34d736d4101e315e0a5fbc69d50ab402", "sha256_in_prefix": "583946cda2bfcdc2c65370f5da99ba5d34d736d4101e315e0a5fbc69d50ab402", "size_in_bytes": 374272}, {"_path": "bin/nvcc.exe", "path_type": "hardlink", "sha256": "909e1865cc6a2f2e477f8f7ce0409817d424459076fcae85768ccd7ea9048cbf", "sha256_in_prefix": "909e1865cc6a2f2e477f8f7ce0409817d424459076fcae85768ccd7ea9048cbf", "size_in_bytes": 12105216}, {"_path": "bin/nvcc.profile", "path_type": "hardlink", "sha256": "da1b3c8e3cecaf06ef7432dd23958ad59c6e45fe9463d56d1177c108434c537e", "sha256_in_prefix": "da1b3c8e3cecaf06ef7432dd23958ad59c6e45fe9463d56d1177c108434c537e", "size_in_bytes": 334}, {"_path": "bin/nvlink.exe", "path_type": "hardlink", "sha256": "cf2bbae1dcc6f224085f4b299a15b40a2cca8f7c5bf434822bfd161ec4e5865b", "sha256_in_prefix": "cf2bbae1dcc6f224085f4b299a15b40a2cca8f7c5bf434822bfd161ec4e5865b", "size_in_bytes": 16673792}, {"_path": "bin/ptxas.exe", "path_type": "hardlink", "sha256": "69b75cdcf3f7c8ad08df3457427fa6b4fa4f6986d9bf0323c12ab10b11bf8528", "sha256_in_prefix": "69b75cdcf3f7c8ad08df3457427fa6b4fa4f6986d9bf0323c12ab10b11bf8528", "size_in_bytes": 16302592}, {"_path": "include/crt/common_functions.h", "path_type": "hardlink", "sha256": "8123e7afb0ddc439d531a25a1bc59bfb4117ffc8648199f8c32931910b81e3db", "sha256_in_prefix": "8123e7afb0ddc439d531a25a1bc59bfb4117ffc8648199f8c32931910b81e3db", "size_in_bytes": 13869}, {"_path": "include/crt/cudacc_ext.h", "path_type": "hardlink", "sha256": "10b085d6b1fcbe650a75de7455e0f5d38d1be221bacddb3318144b88bc72c5a5", "sha256_in_prefix": "10b085d6b1fcbe650a75de7455e0f5d38d1be221bacddb3318144b88bc72c5a5", "size_in_bytes": 3288}, {"_path": "include/crt/device_double_functions.h", "path_type": "hardlink", "sha256": "eae92999e3fee4e050e54d93d20cede69ce857f02f73f471866a8ab65bb1069c", "sha256_in_prefix": "eae92999e3fee4e050e54d93d20cede69ce857f02f73f471866a8ab65bb1069c", "size_in_bytes": 40975}, {"_path": "include/crt/device_double_functions.hpp", "path_type": "hardlink", "sha256": "80d741d9fa10a3c8a212f2e9e8bc23306dc30f88d4b2a6016491f872386dc760", "sha256_in_prefix": "80d741d9fa10a3c8a212f2e9e8bc23306dc30f88d4b2a6016491f872386dc760", "size_in_bytes": 8765}, {"_path": "include/crt/device_functions.h", "path_type": "hardlink", "sha256": "c148fc8d8e74b07c5eabf5daa5a1a0e49d0165dc0cb7b35343d4bc75ad14426d", "sha256_in_prefix": "c148fc8d8e74b07c5eabf5daa5a1a0e49d0165dc0cb7b35343d4bc75ad14426d", "size_in_bytes": 138358}, {"_path": "include/crt/device_functions.hpp", "path_type": "hardlink", "sha256": "f547362f34bdcfbc0d580d4df560a672fdecd356b91651b915824c3cd676b1d4", "sha256_in_prefix": "f547362f34bdcfbc0d580d4df560a672fdecd356b91651b915824c3cd676b1d4", "size_in_bytes": 39183}, {"_path": "include/crt/func_macro.h", "path_type": "hardlink", "sha256": "1bb685c45385895321df70d19248a734e13225f4fb5efbfc28fcd21a93b5930d", "sha256_in_prefix": "1bb685c45385895321df70d19248a734e13225f4fb5efbfc28fcd21a93b5930d", "size_in_bytes": 1812}, {"_path": "include/crt/host_config.h", "path_type": "hardlink", "sha256": "6e5023e554b52e76d1f2c2ed5ad916b8921ba29842cbbd9c451afd1fcd1d3edc", "sha256_in_prefix": "6e5023e554b52e76d1f2c2ed5ad916b8921ba29842cbbd9c451afd1fcd1d3edc", "size_in_bytes": 10899}, {"_path": "include/crt/host_defines.h", "path_type": "hardlink", "sha256": "b5c6230db0d261b24a9121b37d2ed9af6e208ab7da63b41a9261ac20fdc6c33c", "sha256_in_prefix": "b5c6230db0d261b24a9121b37d2ed9af6e208ab7da63b41a9261ac20fdc6c33c", "size_in_bytes": 9525}, {"_path": "include/crt/host_runtime.h", "path_type": "hardlink", "sha256": "0eee347bf357fdd3c2ef5f712bde9211b75bd5acaddeaf2b365b0da53c3ca5e4", "sha256_in_prefix": "0eee347bf357fdd3c2ef5f712bde9211b75bd5acaddeaf2b365b0da53c3ca5e4", "size_in_bytes": 9634}, {"_path": "include/crt/math_functions.h", "path_type": "hardlink", "sha256": "62fe00d953f6f77b3f2b2e2a781ece1b1697c51283c0930eaee0e2b070c155f8", "sha256_in_prefix": "62fe00d953f6f77b3f2b2e2a781ece1b1697c51283c0930eaee0e2b070c155f8", "size_in_bytes": 408145}, {"_path": "include/crt/math_functions.hpp", "path_type": "hardlink", "sha256": "63c278eca238126ce6bff5128da5ced2ae97cd0f7a527e20fe05a41b7b5af972", "sha256_in_prefix": "63c278eca238126ce6bff5128da5ced2ae97cd0f7a527e20fe05a41b7b5af972", "size_in_bytes": 103445}, {"_path": "include/crt/mma.h", "path_type": "hardlink", "sha256": "9397c7a1f11a29404d1bf5c89a1fe8a1651fce1b64b4f884ff05f11aaaa4e511", "sha256_in_prefix": "9397c7a1f11a29404d1bf5c89a1fe8a1651fce1b64b4f884ff05f11aaaa4e511", "size_in_bytes": 63256}, {"_path": "include/crt/mma.hpp", "path_type": "hardlink", "sha256": "6f0b9c7425503672edc809b9d1584fe7d80219889375c6da8c15eca69812ac5b", "sha256_in_prefix": "6f0b9c7425503672edc809b9d1584fe7d80219889375c6da8c15eca69812ac5b", "size_in_bytes": 67727}, {"_path": "include/crt/nvfunctional", "path_type": "hardlink", "sha256": "f6bd186addac0bcbe6dfeab969ab3cbd744c2553d3f6287739e72b04f207bfd8", "sha256_in_prefix": "f6bd186addac0bcbe6dfeab969ab3cbd744c2553d3f6287739e72b04f207bfd8", "size_in_bytes": 17521}, {"_path": "include/crt/sm_70_rt.h", "path_type": "hardlink", "sha256": "04d86eb0237d7b5104e9cc853fa315f2de33f1d524c62b7a123463b6d9aa81c7", "sha256_in_prefix": "04d86eb0237d7b5104e9cc853fa315f2de33f1d524c62b7a123463b6d9aa81c7", "size_in_bytes": 6637}, {"_path": "include/crt/sm_70_rt.hpp", "path_type": "hardlink", "sha256": "d0d13085e4671530e4f41f81ecf1d4e2101d2b3b3206aae7229b8e013ff55273", "sha256_in_prefix": "d0d13085e4671530e4f41f81ecf1d4e2101d2b3b3206aae7229b8e013ff55273", "size_in_bytes": 8029}, {"_path": "include/crt/sm_80_rt.h", "path_type": "hardlink", "sha256": "fec4f1eacc4f113aa04bafa006a2b4115842d3c64000c8ee405a3ea1bf0e47a1", "sha256_in_prefix": "fec4f1eacc4f113aa04bafa006a2b4115842d3c64000c8ee405a3ea1bf0e47a1", "size_in_bytes": 7607}, {"_path": "include/crt/sm_80_rt.hpp", "path_type": "hardlink", "sha256": "9224440b554c8ecf1357af8f6559a834c26137c4277abaa5948d968a5b233acf", "sha256_in_prefix": "9224440b554c8ecf1357af8f6559a834c26137c4277abaa5948d968a5b233acf", "size_in_bytes": 6853}, {"_path": "include/crt/sm_90_rt.h", "path_type": "hardlink", "sha256": "9ec65af581cd8ff13126f2b329eb73e2ca7e4c5f1a0a1233ec753d108a1b06d7", "sha256_in_prefix": "9ec65af581cd8ff13126f2b329eb73e2ca7e4c5f1a0a1233ec753d108a1b06d7", "size_in_bytes": 6246}, {"_path": "include/crt/sm_90_rt.hpp", "path_type": "hardlink", "sha256": "461b2c314cf5f1d2bd1782f22fa30882f135c68c8615935eb5360eec39ddc6fc", "sha256_in_prefix": "461b2c314cf5f1d2bd1782f22fa30882f135c68c8615935eb5360eec39ddc6fc", "size_in_bytes": 9476}, {"_path": "include/crt/storage_class.h", "path_type": "hardlink", "sha256": "14b8ddc03a85d579cbc0cb3f1f888c3affa82fad960a1ad01ae689ce6ca008db", "sha256_in_prefix": "14b8ddc03a85d579cbc0cb3f1f888c3affa82fad960a1ad01ae689ce6ca008db", "size_in_bytes": 4933}, {"_path": "include/fatbinary_section.h", "path_type": "hardlink", "sha256": "4ebcd4d30c74ee5c5798ee8a8856a7cf709e940e6beea73364b47792035bd0a8", "sha256_in_prefix": "4ebcd4d30c74ee5c5798ee8a8856a7cf709e940e6beea73364b47792035bd0a8", "size_in_bytes": 1870}, {"_path": "include/nvPTXCompiler.h", "path_type": "hardlink", "sha256": "e4890be0b464d15733e5d31071058e3b847ffb903cbf918821a56168fdcc22c9", "sha256_in_prefix": "e4890be0b464d15733e5d31071058e3b847ffb903cbf918821a56168fdcc22c9", "size_in_bytes": 12362}, {"_path": "lib/x64/nvptxcompiler_static.lib", "path_type": "hardlink", "sha256": "e41afc949ab95f81a2ba47e909db52a07992ecc70271667fdc4a24c98ad61516", "sha256_in_prefix": "e41afc949ab95f81a2ba47e909db52a07992ecc70271667fdc4a24c98ad61516", "size_in_bytes": 78291750}, {"_path": "nvvm/bin/cicc.exe", "path_type": "hardlink", "sha256": "6275d08890b2caf04aaae4dad56997b6d7c8635bdae544a0a88c3db40bf37825", "sha256_in_prefix": "6275d08890b2caf04aaae4dad56997b6d7c8635bdae544a0a88c3db40bf37825", "size_in_bytes": 29341696}, {"_path": "nvvm/bin/nvvm64_40_0.dll", "path_type": "hardlink", "sha256": "e0b4e1b67faf1e6a54d9a9b644106d7e929f0d70014a1bbdf3e2b7b511cf8f46", "sha256_in_prefix": "e0b4e1b67faf1e6a54d9a9b644106d7e929f0d70014a1bbdf3e2b7b511cf8f46", "size_in_bytes": 18210816}, {"_path": "nvvm/include/nvvm.h", "path_type": "hardlink", "sha256": "daa32e68128b348087a400b47a177f603614c03978de4b7f2ca468bf458ccc36", "sha256_in_prefix": "daa32e68128b348087a400b47a177f603614c03978de4b7f2ca468bf458ccc36", "size_in_bytes": 11576}, {"_path": "nvvm/lib/x64/nvvm.lib", "path_type": "hardlink", "sha256": "5b938605805e142ec7a526c5255d9f3536ce9b606b148200d48f97a8015373e5", "sha256_in_prefix": "5b938605805e142ec7a526c5255d9f3536ce9b606b148200d48f97a8015373e5", "size_in_bytes": 4672}, {"_path": "nvvm/libdevice/libdevice.10.bc", "path_type": "hardlink", "sha256": "b5460c8b86f453f8e3f5a61fa00f2d8972ff7ab49899399ec10e1015594af25c", "sha256_in_prefix": "b5460c8b86f453f8e3f5a61fa00f2d8972ff7ab49899399ec10e1015594af25c", "size_in_bytes": 483660}], "paths_version": 1}, "requested_spec": "None", "sha256": "", "size": 57960310, "subdir": "win-64", "timestamp": 1675849822000, "track_features": "", "url": "https://conda.anaconda.org/nvidia/label/cuda-12.1.0/win-64/cuda-nvcc-12.1.66-0.tar.bz2", "version": "12.1.66"}