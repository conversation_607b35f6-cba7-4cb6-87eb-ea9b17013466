<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMarker Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMarker Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMarker" -->The activity record providing a marker which is an instantaneous point in time. (deprecated in CUDA 8.0).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html#2b83a27fe4d92c89b951f6d004b84b8f">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html#1e1dc438685a30b3adc372cbc331b09e">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html#d2ce59b034314ad39d02a7097f07eaf5">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html#c3967aa4171fa1e30be9c31f16bd0369">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="unionCUpti__ActivityObjectKindId.html">CUpti_ActivityObjectKindId</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html#e81855f68e5c9f80bacd0ec66b0b3613">objectId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html#c1b912d09e94461e8dc8b06aa1e8a0ff">objectKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMarker.html#af62263cba8b33e679ecc5e3afae3ef1">timestamp</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
The marker is specified with a descriptive name and unique id (CUPTI_ACTIVITY_KIND_MARKER). Marker activity is now reported using the <a class="el" href="structCUpti__ActivityMarker2.html" title="The activity record providing a marker which is an instantaneous point in time.">CUpti_ActivityMarker2</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="2b83a27fe4d92c89b951f6d004b84b8f"></a><!-- doxytag: member="CUpti_ActivityMarker::flags" ref="2b83a27fe4d92c89b951f6d004b84b8f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityMarker.html#2b83a27fe4d92c89b951f6d004b84b8f">CUpti_ActivityMarker::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the marker. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="1e1dc438685a30b3adc372cbc331b09e"></a><!-- doxytag: member="CUpti_ActivityMarker::id" ref="1e1dc438685a30b3adc372cbc331b09e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMarker.html#1e1dc438685a30b3adc372cbc331b09e">CUpti_ActivityMarker::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The marker ID. 
</div>
</div><p>
<a class="anchor" name="d2ce59b034314ad39d02a7097f07eaf5"></a><!-- doxytag: member="CUpti_ActivityMarker::kind" ref="d2ce59b034314ad39d02a7097f07eaf5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMarker.html#d2ce59b034314ad39d02a7097f07eaf5">CUpti_ActivityMarker::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MARKER. 
</div>
</div><p>
<a class="anchor" name="c3967aa4171fa1e30be9c31f16bd0369"></a><!-- doxytag: member="CUpti_ActivityMarker::name" ref="c3967aa4171fa1e30be9c31f16bd0369" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityMarker.html#c3967aa4171fa1e30be9c31f16bd0369">CUpti_ActivityMarker::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The marker name for an instantaneous or start marker. This will be NULL for an end marker. 
</div>
</div><p>
<a class="anchor" name="e81855f68e5c9f80bacd0ec66b0b3613"></a><!-- doxytag: member="CUpti_ActivityMarker::objectId" ref="e81855f68e5c9f80bacd0ec66b0b3613" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionCUpti__ActivityObjectKindId.html">CUpti_ActivityObjectKindId</a> <a class="el" href="structCUpti__ActivityMarker.html#e81855f68e5c9f80bacd0ec66b0b3613">CUpti_ActivityMarker::objectId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The identifier for the activity object associated with this marker. 'objectKind' indicates which ID is valid for this record. 
</div>
</div><p>
<a class="anchor" name="c1b912d09e94461e8dc8b06aa1e8a0ff"></a><!-- doxytag: member="CUpti_ActivityMarker::objectKind" ref="c1b912d09e94461e8dc8b06aa1e8a0ff" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9b6136c1123883722ded735eb52cf270">CUpti_ActivityObjectKind</a> <a class="el" href="structCUpti__ActivityMarker.html#c1b912d09e94461e8dc8b06aa1e8a0ff">CUpti_ActivityMarker::objectKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The kind of activity object associated with this marker. 
</div>
</div><p>
<a class="anchor" name="af62263cba8b33e679ecc5e3afae3ef1"></a><!-- doxytag: member="CUpti_ActivityMarker::timestamp" ref="af62263cba8b33e679ecc5e3afae3ef1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMarker.html#af62263cba8b33e679ecc5e3afae3ef1">CUpti_ActivityMarker::timestamp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp for the marker, in ns. A value of 0 indicates that timestamp information could not be collected for the marker. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
