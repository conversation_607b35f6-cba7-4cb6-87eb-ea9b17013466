<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_Profiler_IsPassCollected_Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_Profiler_IsPassCollected_Params Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PROFILER__API.html">CUPTI Profiling API</a>]</small>
</h1><!-- doxytag: class="CUpti_Profiler_IsPassCollected_Params" -->Params for cuptiProfilerIsPassCollected.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="3937b85465b4ed80780a331dd1babbcc"></a><!-- doxytag: member="CUpti_Profiler_IsPassCollected_Params::allPassesCollected" ref="3937b85465b4ed80780a331dd1babbcc" args="" -->
uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#3937b85465b4ed80780a331dd1babbcc">allPassesCollected</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] becomes true when the last pass has been decoded <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="731255ee7f1855d8e4af7d57f49536f1"></a><!-- doxytag: member="CUpti_Profiler_IsPassCollected_Params::ctx" ref="731255ee7f1855d8e4af7d57f49536f1" args="" -->
CUcontext&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#731255ee7f1855d8e4af7d57f49536f1">ctx</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] if NULL, the current CUcontext is used <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="c2a4281719c88dcdf4f105d516ab0030"></a><!-- doxytag: member="CUpti_Profiler_IsPassCollected_Params::numRangesDropped" ref="c2a4281719c88dcdf4f105d516ab0030" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#c2a4281719c88dcdf4f105d516ab0030">numRangesDropped</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] number of ranges whose data was dropped in the processed pass <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="a259efdbb47b23017ba4d55b8c772ec1"></a><!-- doxytag: member="CUpti_Profiler_IsPassCollected_Params::numTraceBytesDropped" ref="a259efdbb47b23017ba4d55b8c772ec1" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#a259efdbb47b23017ba4d55b8c772ec1">numTraceBytesDropped</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] number of bytes not written to TraceBuffer due to buffer full <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="e38c86e191d4c3ba0036e56ec0e8ca6f"></a><!-- doxytag: member="CUpti_Profiler_IsPassCollected_Params::onePassCollected" ref="e38c86e191d4c3ba0036e56ec0e8ca6f" args="" -->
uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#e38c86e191d4c3ba0036e56ec0e8ca6f">onePassCollected</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[out] true if a pass was successfully decoded <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ad37da287c2ebf6bedaa25659a04ae36"></a><!-- doxytag: member="CUpti_Profiler_IsPassCollected_Params::pPriv" ref="ad37da287c2ebf6bedaa25659a04ae36" args="" -->
void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#ad37da287c2ebf6bedaa25659a04ae36">pPriv</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] assign to NULL <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="a2b6ab8636ac6afb4f0d9e56e9c23e18"></a><!-- doxytag: member="CUpti_Profiler_IsPassCollected_Params::structSize" ref="a2b6ab8636ac6afb4f0d9e56e9c23e18" args="" -->
size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__Profiler__IsPassCollected__Params.html#a2b6ab8636ac6afb4f0d9e56e9c23e18">structSize</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">[in] CUpti_Profiler_IsPassCollected_Params_STRUCT_SIZE <br></td></tr>
</table>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:51 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
