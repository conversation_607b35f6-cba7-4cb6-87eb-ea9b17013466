<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityMemset2 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityMemset2 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityMemset2" -->The activity record for memset. (deprecated in CUDA 11.1).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#f19fb3c26cfbafe25cf79c1738b4edbe">bytes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#5eab4e4796b95a2290ee535f40c5df18">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#910198afe3cf3846e048b1d5219f19c7">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#bb3711869b5db640b526d4fff08208e1">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#cf3e11b13fe2b518aae0b13fc401db74">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#f97b22e7b2aceb126e32d09e83fd318b">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#853ebb5e2fc8a4b22835265801654fd2">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#12913c55dc794d33aebab9619c9e0779">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#601cc83c4fec414e90e6fa03420f5e87">memoryKind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#17d899c9c24e31e6cb3c8b1e9f14df33">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#05fe7da921c7b348be2fc856a9480e85">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#4cfbe1f2b00a5afc528c846aaef2ee45">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityMemset2.html#7f11ea562c586fcba8801b509464800a">value</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a memory set operation (CUPTI_ACTIVITY_KIND_MEMSET). <hr><h2>Field Documentation</h2>
<a class="anchor" name="f19fb3c26cfbafe25cf79c1738b4edbe"></a><!-- doxytag: member="CUpti_ActivityMemset2::bytes" ref="f19fb3c26cfbafe25cf79c1738b4edbe" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset2.html#f19fb3c26cfbafe25cf79c1738b4edbe">CUpti_ActivityMemset2::bytes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of bytes being set by the memory set. 
</div>
</div><p>
<a class="anchor" name="5eab4e4796b95a2290ee535f40c5df18"></a><!-- doxytag: member="CUpti_ActivityMemset2::contextId" ref="5eab4e4796b95a2290ee535f40c5df18" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset2.html#5eab4e4796b95a2290ee535f40c5df18">CUpti_ActivityMemset2::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="910198afe3cf3846e048b1d5219f19c7"></a><!-- doxytag: member="CUpti_ActivityMemset2::correlationId" ref="910198afe3cf3846e048b1d5219f19c7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset2.html#910198afe3cf3846e048b1d5219f19c7">CUpti_ActivityMemset2::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the memory set. Each memory set is assigned a unique correlation ID that is identical to the correlation ID in the driver API activity record that launched the memory set. 
</div>
</div><p>
<a class="anchor" name="bb3711869b5db640b526d4fff08208e1"></a><!-- doxytag: member="CUpti_ActivityMemset2::deviceId" ref="bb3711869b5db640b526d4fff08208e1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset2.html#bb3711869b5db640b526d4fff08208e1">CUpti_ActivityMemset2::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="cf3e11b13fe2b518aae0b13fc401db74"></a><!-- doxytag: member="CUpti_ActivityMemset2::end" ref="cf3e11b13fe2b518aae0b13fc401db74" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset2.html#cf3e11b13fe2b518aae0b13fc401db74">CUpti_ActivityMemset2::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="f97b22e7b2aceb126e32d09e83fd318b"></a><!-- doxytag: member="CUpti_ActivityMemset2::flags" ref="f97b22e7b2aceb126e32d09e83fd318b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset2.html#f97b22e7b2aceb126e32d09e83fd318b">CUpti_ActivityMemset2::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the memset. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="853ebb5e2fc8a4b22835265801654fd2"></a><!-- doxytag: member="CUpti_ActivityMemset2::graphNodeId" ref="853ebb5e2fc8a4b22835265801654fd2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset2.html#853ebb5e2fc8a4b22835265801654fd2">CUpti_ActivityMemset2::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that executed this memset through graph launch. This field will be 0 if the memset is not executed through graph launch. 
</div>
</div><p>
<a class="anchor" name="12913c55dc794d33aebab9619c9e0779"></a><!-- doxytag: member="CUpti_ActivityMemset2::kind" ref="12913c55dc794d33aebab9619c9e0779" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityMemset2.html#12913c55dc794d33aebab9619c9e0779">CUpti_ActivityMemset2::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_MEMSET. 
</div>
</div><p>
<a class="anchor" name="601cc83c4fec414e90e6fa03420f5e87"></a><!-- doxytag: member="CUpti_ActivityMemset2::memoryKind" ref="601cc83c4fec414e90e6fa03420f5e87" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityMemset2.html#601cc83c4fec414e90e6fa03420f5e87">CUpti_ActivityMemset2::memoryKind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The memory kind of the memory set <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9969b86f0e54989b27080dc6083263bc" title="The kinds of memory accessed by a memory operation/copy.">CUpti_ActivityMemoryKind</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="17d899c9c24e31e6cb3c8b1e9f14df33"></a><!-- doxytag: member="CUpti_ActivityMemset2::reserved0" ref="17d899c9c24e31e6cb3c8b1e9f14df33" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityMemset2.html#17d899c9c24e31e6cb3c8b1e9f14df33">CUpti_ActivityMemset2::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="05fe7da921c7b348be2fc856a9480e85"></a><!-- doxytag: member="CUpti_ActivityMemset2::start" ref="05fe7da921c7b348be2fc856a9480e85" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityMemset2.html#05fe7da921c7b348be2fc856a9480e85">CUpti_ActivityMemset2::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the memory set, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the memory set. 
</div>
</div><p>
<a class="anchor" name="4cfbe1f2b00a5afc528c846aaef2ee45"></a><!-- doxytag: member="CUpti_ActivityMemset2::streamId" ref="4cfbe1f2b00a5afc528c846aaef2ee45" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset2.html#4cfbe1f2b00a5afc528c846aaef2ee45">CUpti_ActivityMemset2::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the memory set is occurring. 
</div>
</div><p>
<a class="anchor" name="7f11ea562c586fcba8801b509464800a"></a><!-- doxytag: member="CUpti_ActivityMemset2::value" ref="7f11ea562c586fcba8801b509464800a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityMemset2.html#7f11ea562c586fcba8801b509464800a">CUpti_ActivityMemset2::value</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The value being assigned to memory by the memory set. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:50 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
