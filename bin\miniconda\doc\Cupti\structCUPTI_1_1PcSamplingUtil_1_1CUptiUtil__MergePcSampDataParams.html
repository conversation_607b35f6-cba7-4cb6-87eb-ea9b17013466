<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>CUPTI</b>::<b>PcSamplingUtil</b>::<a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html">CUptiUtil_MergePcSampDataParams</a>
  </div>
</div>
<div class="contents">
<h1>CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__PCSAMPLING__UTILITY.html">CUPTI PC Sampling Utility API</a>]</small>
</h1><!-- doxytag: class="CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams" -->Params for CuptiUtilMergePcSampData.  

<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structCUpti__PCSamplingData.html">CUpti_PCSamplingData</a> **&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#a959dc172662357324cc09d3be1e914b">MergedPcSampDataBuffers</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#c29d94c02d27b7e7564512afbb89776b">numberOfBuffers</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#e079332020b290e992840b0f60b00cd0">numMergedBuffer</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structCUpti__PCSamplingData.html">CUpti_PCSamplingData</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#17737240fde681d90ca46f3e2162a279">PcSampDataBuffer</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">size_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#808596a0967b302067db7339c8edc097">size</a></td></tr>

</table>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="a959dc172662357324cc09d3be1e914b"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::MergedPcSampDataBuffers" ref="a959dc172662357324cc09d3be1e914b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structCUpti__PCSamplingData.html">CUpti_PCSamplingData</a>** <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#a959dc172662357324cc09d3be1e914b">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::MergedPcSampDataBuffers</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to array of merged buffers as per the range id. 
</div>
</div><p>
<a class="anchor" name="c29d94c02d27b7e7564512afbb89776b"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::numberOfBuffers" ref="c29d94c02d27b7e7564512afbb89776b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#c29d94c02d27b7e7564512afbb89776b">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::numberOfBuffers</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of buffers to merge. 
</div>
</div><p>
<a class="anchor" name="e079332020b290e992840b0f60b00cd0"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::numMergedBuffer" ref="e079332020b290e992840b0f60b00cd0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#e079332020b290e992840b0f60b00cd0">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::numMergedBuffer</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of merged buffers. 
</div>
</div><p>
<a class="anchor" name="17737240fde681d90ca46f3e2162a279"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::PcSampDataBuffer" ref="17737240fde681d90ca46f3e2162a279" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structCUpti__PCSamplingData.html">CUpti_PCSamplingData</a>* <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#17737240fde681d90ca46f3e2162a279">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::PcSampDataBuffer</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to array of buffers to merge 
</div>
</div><p>
<a class="anchor" name="808596a0967b302067db7339c8edc097"></a><!-- doxytag: member="CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::size" ref="808596a0967b302067db7339c8edc097" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html#808596a0967b302067db7339c8edc097">CUPTI::PcSamplingUtil::CUptiUtil_MergePcSampDataParams::size</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of the data structure i.e. CUpti_PCSamplingDisableParamsSize CUPTI client should set the size of the structure. It will be used in CUPTI to check what fields are available in the structure. Used to preserve backward compatibility. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:52 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
