<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityDevice Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityDevice Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityDevice" -->The activity record for a device. (deprecated).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#86f3de26fc67e56f6c518cb9a18890ce">computeCapabilityMajor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#d2cab4240a6b1efa050041398801337c">computeCapabilityMinor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#d5ef8f094da64ffcc3117369c31a4e8c">constantMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#9f9cc4a4e357ee6ab879545ed89194e3">coreClockRate</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#de7e83e5e5b6391e5cc5f0fbd165a19c">flags</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#a9b77917ca7ec7d84928a42588908c69">globalMemoryBandwidth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#23d1ddc8fb6d1cc3c5d51505d8950fda">globalMemorySize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#3da00a5266b174dd6a7b39b5037cca09">id</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#679307df3d2646d57311e04a1d0a229b">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#ad3a569e4232a91f063e4f8a487caddf">l2CacheSize</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#1155a0e8c8c1b6a5759c6d0b96159965">maxBlockDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#802490bb5d76fb092717376300d44756">maxBlockDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#9e49d79190baba5dfd96d215d39bdd26">maxBlockDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#169bcfc973aa5e5fe96790582635eeb1">maxBlocksPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#70ab083c749ba036bb328519db0c283a">maxGridDimX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#5bf6166aaaf908f752352383ffad1650">maxGridDimY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#047c4a3075cf74d5f8eda66deb698ab7">maxGridDimZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#3a0fa19d37bcd0780bb1ff64321f3655">maxIPC</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#88012896e26d4ae2f72318b75ff23725">maxRegistersPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#fbc51a5e5b66e7e0e11bb09602c9d717">maxSharedMemoryPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#6fa193349a4614bdee39236295679980">maxThreadsPerBlock</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#76b73260d48ecbe671f791ab82c5d15b">maxWarpsPerMultiprocessor</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#634327dd4c2979d083e97b5d701a4245">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#5793ec045865bde52ec2b6b20aa8ae3e">numMemcpyEngines</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#0a8d0f23f95aa19c84950a121bdc6230">numMultiprocessors</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityDevice.html#c9a763efd5c2f3d250a615935484ece3">numThreadsPerWarp</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents information about a GPU device (CUPTI_ACTIVITY_KIND_DEVICE). Device activity is now reported using the <a class="el" href="structCUpti__ActivityDevice4.html" title="The activity record for a device. (CUDA 11.6 onwards).">CUpti_ActivityDevice4</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="86f3de26fc67e56f6c518cb9a18890ce"></a><!-- doxytag: member="CUpti_ActivityDevice::computeCapabilityMajor" ref="86f3de26fc67e56f6c518cb9a18890ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#86f3de26fc67e56f6c518cb9a18890ce">CUpti_ActivityDevice::computeCapabilityMajor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, major number. 
</div>
</div><p>
<a class="anchor" name="d2cab4240a6b1efa050041398801337c"></a><!-- doxytag: member="CUpti_ActivityDevice::computeCapabilityMinor" ref="d2cab4240a6b1efa050041398801337c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#d2cab4240a6b1efa050041398801337c">CUpti_ActivityDevice::computeCapabilityMinor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute capability for the device, minor number. 
</div>
</div><p>
<a class="anchor" name="d5ef8f094da64ffcc3117369c31a4e8c"></a><!-- doxytag: member="CUpti_ActivityDevice::constantMemorySize" ref="d5ef8f094da64ffcc3117369c31a4e8c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#d5ef8f094da64ffcc3117369c31a4e8c">CUpti_ActivityDevice::constantMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of constant memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="9f9cc4a4e357ee6ab879545ed89194e3"></a><!-- doxytag: member="CUpti_ActivityDevice::coreClockRate" ref="9f9cc4a4e357ee6ab879545ed89194e3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#9f9cc4a4e357ee6ab879545ed89194e3">CUpti_ActivityDevice::coreClockRate</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The core clock rate of the device, in kHz. 
</div>
</div><p>
<a class="anchor" name="de7e83e5e5b6391e5cc5f0fbd165a19c"></a><!-- doxytag: member="CUpti_ActivityDevice::flags" ref="de7e83e5e5b6391e5cc5f0fbd165a19c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639">CUpti_ActivityFlag</a> <a class="el" href="structCUpti__ActivityDevice.html#de7e83e5e5b6391e5cc5f0fbd165a19c">CUpti_ActivityDevice::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The flags associated with the device. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gaef8ced52897c4377b0aee6196c37639" title="Flags associated with activity records.">CUpti_ActivityFlag</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="a9b77917ca7ec7d84928a42588908c69"></a><!-- doxytag: member="CUpti_ActivityDevice::globalMemoryBandwidth" ref="a9b77917ca7ec7d84928a42588908c69" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice.html#a9b77917ca7ec7d84928a42588908c69">CUpti_ActivityDevice::globalMemoryBandwidth</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The global memory bandwidth available on the device, in kBytes/sec. 
</div>
</div><p>
<a class="anchor" name="23d1ddc8fb6d1cc3c5d51505d8950fda"></a><!-- doxytag: member="CUpti_ActivityDevice::globalMemorySize" ref="23d1ddc8fb6d1cc3c5d51505d8950fda" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityDevice.html#23d1ddc8fb6d1cc3c5d51505d8950fda">CUpti_ActivityDevice::globalMemorySize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of global memory on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="3da00a5266b174dd6a7b39b5037cca09"></a><!-- doxytag: member="CUpti_ActivityDevice::id" ref="3da00a5266b174dd6a7b39b5037cca09" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#3da00a5266b174dd6a7b39b5037cca09">CUpti_ActivityDevice::id</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device ID. 
</div>
</div><p>
<a class="anchor" name="679307df3d2646d57311e04a1d0a229b"></a><!-- doxytag: member="CUpti_ActivityDevice::kind" ref="679307df3d2646d57311e04a1d0a229b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityDevice.html#679307df3d2646d57311e04a1d0a229b">CUpti_ActivityDevice::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_DEVICE. 
</div>
</div><p>
<a class="anchor" name="ad3a569e4232a91f063e4f8a487caddf"></a><!-- doxytag: member="CUpti_ActivityDevice::l2CacheSize" ref="ad3a569e4232a91f063e4f8a487caddf" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#ad3a569e4232a91f063e4f8a487caddf">CUpti_ActivityDevice::l2CacheSize</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The size of the L2 cache on the device, in bytes. 
</div>
</div><p>
<a class="anchor" name="1155a0e8c8c1b6a5759c6d0b96159965"></a><!-- doxytag: member="CUpti_ActivityDevice::maxBlockDimX" ref="1155a0e8c8c1b6a5759c6d0b96159965" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#1155a0e8c8c1b6a5759c6d0b96159965">CUpti_ActivityDevice::maxBlockDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a block. 
</div>
</div><p>
<a class="anchor" name="802490bb5d76fb092717376300d44756"></a><!-- doxytag: member="CUpti_ActivityDevice::maxBlockDimY" ref="802490bb5d76fb092717376300d44756" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#802490bb5d76fb092717376300d44756">CUpti_ActivityDevice::maxBlockDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a block. 
</div>
</div><p>
<a class="anchor" name="9e49d79190baba5dfd96d215d39bdd26"></a><!-- doxytag: member="CUpti_ActivityDevice::maxBlockDimZ" ref="9e49d79190baba5dfd96d215d39bdd26" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#9e49d79190baba5dfd96d215d39bdd26">CUpti_ActivityDevice::maxBlockDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a block. 
</div>
</div><p>
<a class="anchor" name="169bcfc973aa5e5fe96790582635eeb1"></a><!-- doxytag: member="CUpti_ActivityDevice::maxBlocksPerMultiprocessor" ref="169bcfc973aa5e5fe96790582635eeb1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#169bcfc973aa5e5fe96790582635eeb1">CUpti_ActivityDevice::maxBlocksPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of blocks that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="70ab083c749ba036bb328519db0c283a"></a><!-- doxytag: member="CUpti_ActivityDevice::maxGridDimX" ref="70ab083c749ba036bb328519db0c283a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#70ab083c749ba036bb328519db0c283a">CUpti_ActivityDevice::maxGridDimX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed X dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="5bf6166aaaf908f752352383ffad1650"></a><!-- doxytag: member="CUpti_ActivityDevice::maxGridDimY" ref="5bf6166aaaf908f752352383ffad1650" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#5bf6166aaaf908f752352383ffad1650">CUpti_ActivityDevice::maxGridDimY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Y dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="047c4a3075cf74d5f8eda66deb698ab7"></a><!-- doxytag: member="CUpti_ActivityDevice::maxGridDimZ" ref="047c4a3075cf74d5f8eda66deb698ab7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#047c4a3075cf74d5f8eda66deb698ab7">CUpti_ActivityDevice::maxGridDimZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum allowed Z dimension for a grid. 
</div>
</div><p>
<a class="anchor" name="3a0fa19d37bcd0780bb1ff64321f3655"></a><!-- doxytag: member="CUpti_ActivityDevice::maxIPC" ref="3a0fa19d37bcd0780bb1ff64321f3655" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#3a0fa19d37bcd0780bb1ff64321f3655">CUpti_ActivityDevice::maxIPC</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The maximum "instructions per cycle" possible on each device multiprocessor. 
</div>
</div><p>
<a class="anchor" name="88012896e26d4ae2f72318b75ff23725"></a><!-- doxytag: member="CUpti_ActivityDevice::maxRegistersPerBlock" ref="88012896e26d4ae2f72318b75ff23725" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#88012896e26d4ae2f72318b75ff23725">CUpti_ActivityDevice::maxRegistersPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of registers that can be allocated to a block. 
</div>
</div><p>
<a class="anchor" name="fbc51a5e5b66e7e0e11bb09602c9d717"></a><!-- doxytag: member="CUpti_ActivityDevice::maxSharedMemoryPerBlock" ref="fbc51a5e5b66e7e0e11bb09602c9d717" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#fbc51a5e5b66e7e0e11bb09602c9d717">CUpti_ActivityDevice::maxSharedMemoryPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum amount of shared memory that can be assigned to a block, in bytes. 
</div>
</div><p>
<a class="anchor" name="6fa193349a4614bdee39236295679980"></a><!-- doxytag: member="CUpti_ActivityDevice::maxThreadsPerBlock" ref="6fa193349a4614bdee39236295679980" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#6fa193349a4614bdee39236295679980">CUpti_ActivityDevice::maxThreadsPerBlock</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of threads allowed in a block. 
</div>
</div><p>
<a class="anchor" name="76b73260d48ecbe671f791ab82c5d15b"></a><!-- doxytag: member="CUpti_ActivityDevice::maxWarpsPerMultiprocessor" ref="76b73260d48ecbe671f791ab82c5d15b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#76b73260d48ecbe671f791ab82c5d15b">CUpti_ActivityDevice::maxWarpsPerMultiprocessor</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Maximum number of warps that can be present on a multiprocessor at any given time. 
</div>
</div><p>
<a class="anchor" name="634327dd4c2979d083e97b5d701a4245"></a><!-- doxytag: member="CUpti_ActivityDevice::name" ref="634327dd4c2979d083e97b5d701a4245" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityDevice.html#634327dd4c2979d083e97b5d701a4245">CUpti_ActivityDevice::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The device name. This name is shared across all activity records representing instances of the device, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="5793ec045865bde52ec2b6b20aa8ae3e"></a><!-- doxytag: member="CUpti_ActivityDevice::numMemcpyEngines" ref="5793ec045865bde52ec2b6b20aa8ae3e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#5793ec045865bde52ec2b6b20aa8ae3e">CUpti_ActivityDevice::numMemcpyEngines</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of memory copy engines on the device. 
</div>
</div><p>
<a class="anchor" name="0a8d0f23f95aa19c84950a121bdc6230"></a><!-- doxytag: member="CUpti_ActivityDevice::numMultiprocessors" ref="0a8d0f23f95aa19c84950a121bdc6230" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#0a8d0f23f95aa19c84950a121bdc6230">CUpti_ActivityDevice::numMultiprocessors</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Number of multiprocessors on the device. 
</div>
</div><p>
<a class="anchor" name="c9a763efd5c2f3d250a615935484ece3"></a><!-- doxytag: member="CUpti_ActivityDevice::numThreadsPerWarp" ref="c9a763efd5c2f3d250a615935484ece3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityDevice.html#c9a763efd5c2f3d250a615935484ece3">CUpti_ActivityDevice::numThreadsPerWarp</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of threads per warp on the device. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:48 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
