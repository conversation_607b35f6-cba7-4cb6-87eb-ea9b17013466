<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<title>Cupti: CUpti_ActivityKernel6 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css">
<link href="doxygen.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>CUpti_ActivityKernel6 Struct Reference<br>
<small>
[<a class="el" href="group__CUPTI__ACTIVITY__API.html">CUPTI Activity API</a>]</small>
</h1><!-- doxytag: class="CUpti_ActivityKernel6" -->The activity record for kernel. (deprecated in CUDA 11.6).  
<a href="#_details">More...</a>
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#bcb75b544f458747c8c48dc95cc7ffa2">blockX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#c520a9a82295ffb2e0bc14069609681f">blockY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#775076cf28b6b200d2bd90d1a0e4933d">blockZ</a></td></tr>

<tr><td class="memItemLeft" nowrap>union {</td></tr>

<tr><td class="memItemLeft" nowrap valign="top">}&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#8df69227d19afd04d9f87ca8c394ba0b">cacheConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#10217cdc4f154b8ec276101a1975644f">completed</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#89a181dc4733a4c3bfc74f1eaedd111e">contextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#5bb21c9050a325332bad376f935c6f6c">correlationId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#5e473a0a5a4bcd2957b52b18ccde7443">deviceId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#a7a71b7d6f48d851bc09b2aec98ded8a">dynamicSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#4f5c7fc3ffb9c39e579017e9ff2801c5">end</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#b9e197904881320995715b431b187f99">graphId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#c4729b41416c2598b353cba2c1fbf1dc">graphNodeId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#6becaf7c86dcde7d294f143faf9bbf72">gridId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#d0b5fcdb54b15ed29188b9da4e45ccf3">gridX</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#3710a3c079cf1eaab3e179bee4586120">gridY</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#b366ba101f4214ec034798d675137723">gridZ</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#9553e33a377998a9809007a8ff3ed5e5">isSharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#3147903a6e3f7c7d3278be85851087cc">kind</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#cd98f6540868e1044d32182d97b8fbfc">launchType</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#385047d9da74891e88cf20c1a7ea6b90">localMemoryPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#7df5b16b858e0857de7509de0c8a5d22">localMemoryTotal</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#e5958d24f048e60c4e740be144fe409c">name</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">CUaccessPolicyWindow *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#ff1fdd768e3fb91fff3badefe7610190">pAccessPolicyWindow</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#eaaaf2c9006e5559a7aac13dd84c0486">padding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#1914bb9c82257e19a7cc627f5c413799">partitionedGlobalCacheExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#a96e3194cd271a1af3658c3bc0921db3">partitionedGlobalCacheRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#93591cbf8b30ba46e5fe8aade23a181a">queued</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#23657e8ff29782fd4434f391c2379b49">registersPerThread</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#5f3e517d74c9f097befb461b0eec3f5a">reserved0</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#3b5ab3e19ad21b0ed686bad8ebdcd9d0">sharedMemoryCarveoutRequested</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#8999a401981ec6f71d55ae3b1d3fa14c">sharedMemoryConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#2a978ce99b3f1068cc41fabe2d20fb57">sharedMemoryExecuted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#a8323e003d914ae0f26679bc40bec056">shmemLimitConfig</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#809fd89a377f0af29ea9781e7984fdb5">start</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#8ab62b9c269425d61454e83c5042008c">staticSharedMemory</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#ae5ccf7a09ea26295fd7fe5737be5258">streamId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#a90bb0179c75aeb63a92d3c97c1e210a">submitted</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#38754cadc9f22e58b3611be3e90362b7">executed</a>:4</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint8_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structCUpti__ActivityKernel6.html#08605325dc3b97038fd31bd9d3b584b1">requested</a>:4</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This activity record represents a kernel execution (CUPTI_ACTIVITY_KIND_KERNEL and CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL) but is no longer generated by CUPTI. Kernel activities are now reported using the <a class="el" href="structCUpti__ActivityKernel9.html" title="The activity record for kernel.">CUpti_ActivityKernel9</a> activity record. <hr><h2>Field Documentation</h2>
<a class="anchor" name="bcb75b544f458747c8c48dc95cc7ffa2"></a><!-- doxytag: member="CUpti_ActivityKernel6::blockX" ref="bcb75b544f458747c8c48dc95cc7ffa2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#bcb75b544f458747c8c48dc95cc7ffa2">CUpti_ActivityKernel6::blockX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="c520a9a82295ffb2e0bc14069609681f"></a><!-- doxytag: member="CUpti_ActivityKernel6::blockY" ref="c520a9a82295ffb2e0bc14069609681f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#c520a9a82295ffb2e0bc14069609681f">CUpti_ActivityKernel6::blockY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension block size for the kernel. 
</div>
</div><p>
<a class="anchor" name="775076cf28b6b200d2bd90d1a0e4933d"></a><!-- doxytag: member="CUpti_ActivityKernel6::blockZ" ref="775076cf28b6b200d2bd90d1a0e4933d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#775076cf28b6b200d2bd90d1a0e4933d">CUpti_ActivityKernel6::blockZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="8df69227d19afd04d9f87ca8c394ba0b"></a><!-- doxytag: member="CUpti_ActivityKernel6::cacheConfig" ref="8df69227d19afd04d9f87ca8c394ba0b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... }   <a class="el" href="structCUpti__ActivityKernel6.html#8df69227d19afd04d9f87ca8c394ba0b">CUpti_ActivityKernel6::cacheConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
For devices with compute capability 7.0+ cacheConfig values are not updated in case field isSharedMemoryCarveoutRequested is set 
</div>
</div><p>
<a class="anchor" name="10217cdc4f154b8ec276101a1975644f"></a><!-- doxytag: member="CUpti_ActivityKernel6::completed" ref="10217cdc4f154b8ec276101a1975644f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel6.html#10217cdc4f154b8ec276101a1975644f">CUpti_ActivityKernel6::completed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The completed timestamp for the kernel execution, in ns. It represents the completion of all it's child kernels and the kernel itself. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the completion time is unknown. 
</div>
</div><p>
<a class="anchor" name="89a181dc4733a4c3bfc74f1eaedd111e"></a><!-- doxytag: member="CUpti_ActivityKernel6::contextId" ref="89a181dc4733a4c3bfc74f1eaedd111e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#89a181dc4733a4c3bfc74f1eaedd111e">CUpti_ActivityKernel6::contextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the context where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="5bb21c9050a325332bad376f935c6f6c"></a><!-- doxytag: member="CUpti_ActivityKernel6::correlationId" ref="5bb21c9050a325332bad376f935c6f6c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#5bb21c9050a325332bad376f935c6f6c">CUpti_ActivityKernel6::correlationId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The correlation ID of the kernel. Each kernel execution is assigned a unique correlation ID that is identical to the correlation ID in the driver or runtime API activity record that launched the kernel. 
</div>
</div><p>
<a class="anchor" name="5e473a0a5a4bcd2957b52b18ccde7443"></a><!-- doxytag: member="CUpti_ActivityKernel6::deviceId" ref="5e473a0a5a4bcd2957b52b18ccde7443" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#5e473a0a5a4bcd2957b52b18ccde7443">CUpti_ActivityKernel6::deviceId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the device where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="a7a71b7d6f48d851bc09b2aec98ded8a"></a><!-- doxytag: member="CUpti_ActivityKernel6::dynamicSharedMemory" ref="a7a71b7d6f48d851bc09b2aec98ded8a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#a7a71b7d6f48d851bc09b2aec98ded8a">CUpti_ActivityKernel6::dynamicSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The dynamic shared memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="4f5c7fc3ffb9c39e579017e9ff2801c5"></a><!-- doxytag: member="CUpti_ActivityKernel6::end" ref="4f5c7fc3ffb9c39e579017e9ff2801c5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel6.html#4f5c7fc3ffb9c39e579017e9ff2801c5">CUpti_ActivityKernel6::end</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The end timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="38754cadc9f22e58b3611be3e90362b7"></a><!-- doxytag: member="CUpti_ActivityKernel6::executed" ref="38754cadc9f22e58b3611be3e90362b7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel6.html#38754cadc9f22e58b3611be3e90362b7">CUpti_ActivityKernel6::executed</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration used for the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="b9e197904881320995715b431b187f99"></a><!-- doxytag: member="CUpti_ActivityKernel6::graphId" ref="b9e197904881320995715b431b187f99" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#b9e197904881320995715b431b187f99">CUpti_ActivityKernel6::graphId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="c4729b41416c2598b353cba2c1fbf1dc"></a><!-- doxytag: member="CUpti_ActivityKernel6::graphNodeId" ref="c4729b41416c2598b353cba2c1fbf1dc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel6.html#c4729b41416c2598b353cba2c1fbf1dc">CUpti_ActivityKernel6::graphNodeId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The unique ID of the graph node that launched this kernel through graph launch APIs. This field will be 0 if the kernel is not launched through graph launch APIs. 
</div>
</div><p>
<a class="anchor" name="6becaf7c86dcde7d294f143faf9bbf72"></a><!-- doxytag: member="CUpti_ActivityKernel6::gridId" ref="6becaf7c86dcde7d294f143faf9bbf72" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t <a class="el" href="structCUpti__ActivityKernel6.html#6becaf7c86dcde7d294f143faf9bbf72">CUpti_ActivityKernel6::gridId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The grid ID of the kernel. Each kernel is assigned a unique grid ID at runtime. 
</div>
</div><p>
<a class="anchor" name="d0b5fcdb54b15ed29188b9da4e45ccf3"></a><!-- doxytag: member="CUpti_ActivityKernel6::gridX" ref="d0b5fcdb54b15ed29188b9da4e45ccf3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#d0b5fcdb54b15ed29188b9da4e45ccf3">CUpti_ActivityKernel6::gridX</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The X-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="3710a3c079cf1eaab3e179bee4586120"></a><!-- doxytag: member="CUpti_ActivityKernel6::gridY" ref="3710a3c079cf1eaab3e179bee4586120" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#3710a3c079cf1eaab3e179bee4586120">CUpti_ActivityKernel6::gridY</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Y-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="b366ba101f4214ec034798d675137723"></a><!-- doxytag: member="CUpti_ActivityKernel6::gridZ" ref="b366ba101f4214ec034798d675137723" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#b366ba101f4214ec034798d675137723">CUpti_ActivityKernel6::gridZ</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The Z-dimension grid size for the kernel. 
</div>
</div><p>
<a class="anchor" name="9553e33a377998a9809007a8ff3ed5e5"></a><!-- doxytag: member="CUpti_ActivityKernel6::isSharedMemoryCarveoutRequested" ref="9553e33a377998a9809007a8ff3ed5e5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel6.html#9553e33a377998a9809007a8ff3ed5e5">CUpti_ActivityKernel6::isSharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
This indicates if CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT was updated for the kernel launch 
</div>
</div><p>
<a class="anchor" name="3147903a6e3f7c7d3278be85851087cc"></a><!-- doxytag: member="CUpti_ActivityKernel6::kind" ref="3147903a6e3f7c7d3278be85851087cc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#gefed720d5a60c3e8b286cd386c4913e3">CUpti_ActivityKind</a> <a class="el" href="structCUpti__ActivityKernel6.html#3147903a6e3f7c7d3278be85851087cc">CUpti_ActivityKernel6::kind</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The activity record kind, must be CUPTI_ACTIVITY_KIND_KERNEL or CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL. 
</div>
</div><p>
<a class="anchor" name="cd98f6540868e1044d32182d97b8fbfc"></a><!-- doxytag: member="CUpti_ActivityKernel6::launchType" ref="cd98f6540868e1044d32182d97b8fbfc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel6.html#cd98f6540868e1044d32182d97b8fbfc">CUpti_ActivityKernel6::launchType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The indicates if the kernel was executed via a regular launch or via a single/multi device cooperative launch. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="group__CUPTI__ACTIVITY__API.html#gd054e12847a4a5a0ece53d2cb6dc6d8c" title="The type of the CUDA kernel launch.">CUpti_ActivityLaunchType</a> </dd></dl>

</div>
</div><p>
<a class="anchor" name="385047d9da74891e88cf20c1a7ea6b90"></a><!-- doxytag: member="CUpti_ActivityKernel6::localMemoryPerThread" ref="385047d9da74891e88cf20c1a7ea6b90" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#385047d9da74891e88cf20c1a7ea6b90">CUpti_ActivityKernel6::localMemoryPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The amount of local memory reserved for each thread, in bytes. 
</div>
</div><p>
<a class="anchor" name="7df5b16b858e0857de7509de0c8a5d22"></a><!-- doxytag: member="CUpti_ActivityKernel6::localMemoryTotal" ref="7df5b16b858e0857de7509de0c8a5d22" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#7df5b16b858e0857de7509de0c8a5d22">CUpti_ActivityKernel6::localMemoryTotal</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The total amount of local memory reserved for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="e5958d24f048e60c4e740be144fe409c"></a><!-- doxytag: member="CUpti_ActivityKernel6::name" ref="e5958d24f048e60c4e740be144fe409c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structCUpti__ActivityKernel6.html#e5958d24f048e60c4e740be144fe409c">CUpti_ActivityKernel6::name</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The name of the kernel. This name is shared across all activity records representing the same kernel, and so should not be modified. 
</div>
</div><p>
<a class="anchor" name="ff1fdd768e3fb91fff3badefe7610190"></a><!-- doxytag: member="CUpti_ActivityKernel6::pAccessPolicyWindow" ref="ff1fdd768e3fb91fff3badefe7610190" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CUaccessPolicyWindow* <a class="el" href="structCUpti__ActivityKernel6.html#ff1fdd768e3fb91fff3badefe7610190">CUpti_ActivityKernel6::pAccessPolicyWindow</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The pointer to the access policy window. The structure CUaccessPolicyWindow is defined in cuda.h. 
</div>
</div><p>
<a class="anchor" name="eaaaf2c9006e5559a7aac13dd84c0486"></a><!-- doxytag: member="CUpti_ActivityKernel6::padding" ref="eaaaf2c9006e5559a7aac13dd84c0486" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel6.html#eaaaf2c9006e5559a7aac13dd84c0486">CUpti_ActivityKernel6::padding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="1914bb9c82257e19a7cc627f5c413799"></a><!-- doxytag: member="CUpti_ActivityKernel6::partitionedGlobalCacheExecuted" ref="1914bb9c82257e19a7cc627f5c413799" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel6.html#1914bb9c82257e19a7cc627f5c413799">CUpti_ActivityKernel6::partitionedGlobalCacheExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching executed for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. Partitioned global caching can be automatically disabled if the occupancy requirement of the launch cannot support caching. 
</div>
</div><p>
<a class="anchor" name="a96e3194cd271a1af3658c3bc0921db3"></a><!-- doxytag: member="CUpti_ActivityKernel6::partitionedGlobalCacheRequested" ref="a96e3194cd271a1af3658c3bc0921db3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g9d3467131ce6c87aa85f3a5a43f83484">CUpti_ActivityPartitionedGlobalCacheConfig</a> <a class="el" href="structCUpti__ActivityKernel6.html#a96e3194cd271a1af3658c3bc0921db3">CUpti_ActivityKernel6::partitionedGlobalCacheRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The partitioned global caching requested for the kernel. Partitioned global caching is required to enable caching on certain chips, such as devices with compute capability 5.2. 
</div>
</div><p>
<a class="anchor" name="93591cbf8b30ba46e5fe8aade23a181a"></a><!-- doxytag: member="CUpti_ActivityKernel6::queued" ref="93591cbf8b30ba46e5fe8aade23a181a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel6.html#93591cbf8b30ba46e5fe8aade23a181a">CUpti_ActivityKernel6::queued</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the kernel is queued up in the command buffer, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the queued time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection.<p>
Command buffer is a buffer written by CUDA driver to send commands like kernel launch, memory copy etc to the GPU. All launches of CUDA kernels are asynchrnous with respect to the host, the host requests the launch by writing commands into the command buffer, then returns without checking the GPU's progress. 
</div>
</div><p>
<a class="anchor" name="23657e8ff29782fd4434f391c2379b49"></a><!-- doxytag: member="CUpti_ActivityKernel6::registersPerThread" ref="23657e8ff29782fd4434f391c2379b49" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structCUpti__ActivityKernel6.html#23657e8ff29782fd4434f391c2379b49">CUpti_ActivityKernel6::registersPerThread</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of registers required for each thread executing the kernel. 
</div>
</div><p>
<a class="anchor" name="08605325dc3b97038fd31bd9d3b584b1"></a><!-- doxytag: member="CUpti_ActivityKernel6::requested" ref="08605325dc3b97038fd31bd9d3b584b1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel6.html#08605325dc3b97038fd31bd9d3b584b1">CUpti_ActivityKernel6::requested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The cache configuration requested by the kernel. The value is one of the CUfunc_cache enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="5f3e517d74c9f097befb461b0eec3f5a"></a><!-- doxytag: member="CUpti_ActivityKernel6::reserved0" ref="5f3e517d74c9f097befb461b0eec3f5a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="structCUpti__ActivityKernel6.html#5f3e517d74c9f097befb461b0eec3f5a">CUpti_ActivityKernel6::reserved0</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Undefined. Reserved for internal use. 
</div>
</div><p>
<a class="anchor" name="3b5ab3e19ad21b0ed686bad8ebdcd9d0"></a><!-- doxytag: member="CUpti_ActivityKernel6::sharedMemoryCarveoutRequested" ref="3b5ab3e19ad21b0ed686bad8ebdcd9d0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel6.html#3b5ab3e19ad21b0ed686bad8ebdcd9d0">CUpti_ActivityKernel6::sharedMemoryCarveoutRequested</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory carveout value requested for the function in percentage of the total resource. The value will be updated only if field isSharedMemoryCarveoutRequested is set. 
</div>
</div><p>
<a class="anchor" name="8999a401981ec6f71d55ae3b1d3fa14c"></a><!-- doxytag: member="CUpti_ActivityKernel6::sharedMemoryConfig" ref="8999a401981ec6f71d55ae3b1d3fa14c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t <a class="el" href="structCUpti__ActivityKernel6.html#8999a401981ec6f71d55ae3b1d3fa14c">CUpti_ActivityKernel6::sharedMemoryConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory configuration used for the kernel. The value is one of the CUsharedconfig enumeration values from cuda.h. 
</div>
</div><p>
<a class="anchor" name="2a978ce99b3f1068cc41fabe2d20fb57"></a><!-- doxytag: member="CUpti_ActivityKernel6::sharedMemoryExecuted" ref="2a978ce99b3f1068cc41fabe2d20fb57" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#2a978ce99b3f1068cc41fabe2d20fb57">CUpti_ActivityKernel6::sharedMemoryExecuted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Shared memory size set by the driver. 
</div>
</div><p>
<a class="anchor" name="a8323e003d914ae0f26679bc40bec056"></a><!-- doxytag: member="CUpti_ActivityKernel6::shmemLimitConfig" ref="a8323e003d914ae0f26679bc40bec056" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__CUPTI__ACTIVITY__API.html#g6662b8786c121eed35068b4c1cc931ab">CUpti_FuncShmemLimitConfig</a> <a class="el" href="structCUpti__ActivityKernel6.html#a8323e003d914ae0f26679bc40bec056">CUpti_ActivityKernel6::shmemLimitConfig</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The shared memory limit config for the kernel. This field shows whether user has opted for a higher per block limit of dynamic shared memory. 
</div>
</div><p>
<a class="anchor" name="809fd89a377f0af29ea9781e7984fdb5"></a><!-- doxytag: member="CUpti_ActivityKernel6::start" ref="809fd89a377f0af29ea9781e7984fdb5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel6.html#809fd89a377f0af29ea9781e7984fdb5">CUpti_ActivityKernel6::start</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The start timestamp for the kernel execution, in ns. A value of 0 for both the start and end timestamps indicates that timestamp information could not be collected for the kernel. 
</div>
</div><p>
<a class="anchor" name="8ab62b9c269425d61454e83c5042008c"></a><!-- doxytag: member="CUpti_ActivityKernel6::staticSharedMemory" ref="8ab62b9c269425d61454e83c5042008c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="structCUpti__ActivityKernel6.html#8ab62b9c269425d61454e83c5042008c">CUpti_ActivityKernel6::staticSharedMemory</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The static shared memory allocated for the kernel, in bytes. 
</div>
</div><p>
<a class="anchor" name="ae5ccf7a09ea26295fd7fe5737be5258"></a><!-- doxytag: member="CUpti_ActivityKernel6::streamId" ref="ae5ccf7a09ea26295fd7fe5737be5258" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structCUpti__ActivityKernel6.html#ae5ccf7a09ea26295fd7fe5737be5258">CUpti_ActivityKernel6::streamId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The ID of the stream where the kernel is executing. 
</div>
</div><p>
<a class="anchor" name="a90bb0179c75aeb63a92d3c97c1e210a"></a><!-- doxytag: member="CUpti_ActivityKernel6::submitted" ref="a90bb0179c75aeb63a92d3c97c1e210a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structCUpti__ActivityKernel6.html#a90bb0179c75aeb63a92d3c97c1e210a">CUpti_ActivityKernel6::submitted</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The timestamp when the command buffer containing the kernel launch is submitted to the GPU, in ns. A value of CUPTI_TIMESTAMP_UNKNOWN indicates that the submitted time could not be collected for the kernel. This timestamp is not collected by default. Use API <a class="el" href="group__CUPTI__ACTIVITY__API.html#g4b50c0c1634913f8157cfcfe84f19fa9">cuptiActivityEnableLatencyTimestamps()</a> to enable collection. 
</div>
</div><p>
</div>
<hr size="1"><address style="text-align: right;"><small>Generated on Fri Feb 3 03:33:49 2023 for Cupti by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.5.8 </small></address>
</body>
</html>
