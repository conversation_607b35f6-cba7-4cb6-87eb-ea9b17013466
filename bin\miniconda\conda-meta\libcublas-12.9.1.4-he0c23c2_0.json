{"build": "he0c23c2_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["cuda-nvrtc", "cuda-version >=12.9,<12.10.0a0", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcublas-12.9.1.4-he0c23c2_0", "features": "", "files": ["Library/bin/cublas64_12.dll", "Library/bin/cublasLt64_12.dll", "Library/bin/nvblas64_12.dll"], "fn": "libcublas-12.9.1.4-he0c23c2_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcublas-12.9.1.4-he0c23c2_0", "type": 1}, "md5": "8af1f3e0e73db074ce3eb843a86e0e56", "name": "libcublas", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\libcublas-12.9.1.4-he0c23c2_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/cublas64_12.dll", "path_type": "hardlink", "sha256": "90052a83efd1b57a8e3616a6590b335855f81b814a4f16eecb7b5bf6d1b1d4eb", "sha256_in_prefix": "90052a83efd1b57a8e3616a6590b335855f81b814a4f16eecb7b5bf6d1b1d4eb", "size_in_bytes": 102518272}, {"_path": "Library/bin/cublasLt64_12.dll", "path_type": "hardlink", "sha256": "c3a05ea244c937314afec09f87b91f814c7e27977681f6c67eb51bb06ced3a4a", "sha256_in_prefix": "c3a05ea244c937314afec09f87b91f814c7e27977681f6c67eb51bb06ced3a4a", "size_in_bytes": 668669952}, {"_path": "Library/bin/nvblas64_12.dll", "path_type": "hardlink", "sha256": "8849585e69ab5f98dfa3574d67e737d62579324ed9e39dd66043e3601d89a371", "sha256_in_prefix": "8849585e69ab5f98dfa3574d67e737d62579324ed9e39dd66043e3601d89a371", "size_in_bytes": 334848}], "paths_version": 1}, "requested_spec": "None", "sha256": "f0f1fddb9ec6c56b09897d55d75c2e167519199896fe96d60d1316a2d575d302", "size": 461266290, "subdir": "win-64", "timestamp": 1749228024000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/libcublas-12.9.1.4-he0c23c2_0.conda", "version": "12.9.1.4"}