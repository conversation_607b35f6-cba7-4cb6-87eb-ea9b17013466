{"build": "py39h17685eb_3", "build_number": 3, "channel": "https://conda.anaconda.org/conda-forge", "constrains": [], "depends": ["python", "vc >=14.3,<15", "vc14_runtime >=14.44.35208", "ucrt >=10.0.20348.0", "openssl >=3.5.0,<4.0a0", "_python_abi3_support 1.*", "cpython >=3.9"], "extracted_package_dir": "C:\\pinokio\\bin\\miniconda\\pkgs\\hf-xet-1.1.5-py39h17685eb_3", "features": "", "files": ["Lib/site-packages/hf_xet/__init__.py", "Lib/site-packages/hf_xet/hf_xet.pyd", "Lib/site-packages/hf_xet-1.1.5.dist-info/INSTALLER", "Lib/site-packages/hf_xet-1.1.5.dist-info/METADATA", "Lib/site-packages/hf_xet-1.1.5.dist-info/RECORD", "Lib/site-packages/hf_xet-1.1.5.dist-info/REQUESTED", "Lib/site-packages/hf_xet-1.1.5.dist-info/WHEEL", "Lib/site-packages/hf_xet-1.1.5.dist-info/direct_url.json", "Lib/site-packages/hf_xet-1.1.5.dist-info/licenses/LICENSE", "Lib/site-packages/hf_xet/__pycache__/__init__.cpython-310.pyc"], "fn": "hf-xet-1.1.5-py39h17685eb_3.conda", "license": "Apache-2.0", "link": {"source": "C:\\pinokio\\bin\\miniconda\\pkgs\\hf-xet-1.1.5-py39h17685eb_3", "type": 1}, "md5": "20950324bae25480c2f8001190a6c91d", "name": "hf-xet", "noarch": "python", "package_tarball_full_path": "C:\\pinokio\\bin\\miniconda\\pkgs\\hf-xet-1.1.5-py39h17685eb_3.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/hf_xet/__init__.py", "path_type": "hardlink", "sha256": "13c50377243c82567f9ef7bd84711fdb66cf6a783cf912b1e15b80a5761e414a", "sha256_in_prefix": "13c50377243c82567f9ef7bd84711fdb66cf6a783cf912b1e15b80a5761e414a", "size_in_bytes": 107}, {"_path": "site-packages/hf_xet/hf_xet.pyd", "path_type": "hardlink", "sha256": "9e4e32ebca1dfd3f4cbd86cc181fb496e89faff9802e10b914b7a1b663658e7b", "sha256_in_prefix": "9e4e32ebca1dfd3f4cbd86cc181fb496e89faff9802e10b914b7a1b663658e7b", "size_in_bytes": 6625280}, {"_path": "site-packages/hf_xet-1.1.5.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "sha256_in_prefix": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "size_in_bytes": 6}, {"_path": "site-packages/hf_xet-1.1.5.dist-info/METADATA", "path_type": "hardlink", "sha256": "8c556c22ba57f646ecfd56cc07dfc626d59f8803d2980075991ff4e66242410f", "sha256_in_prefix": "8c556c22ba57f646ecfd56cc07dfc626d59f8803d2980075991ff4e66242410f", "size_in_bytes": 879}, {"_path": "site-packages/hf_xet-1.1.5.dist-info/RECORD", "path_type": "hardlink", "sha256": "820828e1d496ff6c99b957bdae1b47398a992ef1f52ff90d52a8be55bf71f5ed", "sha256_in_prefix": "820828e1d496ff6c99b957bdae1b47398a992ef1f52ff90d52a8be55bf71f5ed", "size_in_bytes": 770}, {"_path": "site-packages/hf_xet-1.1.5.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/hf_xet-1.1.5.dist-info/WHEEL", "path_type": "hardlink", "sha256": "d31901e94a105f1494d86f07b536ea64313e0c1aeb2543950bfcf6138f7bdafc", "sha256_in_prefix": "d31901e94a105f1494d86f07b536ea64313e0c1aeb2543950bfcf6138f7bdafc", "size_in_bytes": 94}, {"_path": "site-packages/hf_xet-1.1.5.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "87830c799822729628f6450b0cbde55000cacd87561119282c05f9849544a420", "sha256_in_prefix": "87830c799822729628f6450b0cbde55000cacd87561119282c05f9849544a420", "size_in_bytes": 82}, {"_path": "site-packages/hf_xet-1.1.5.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "Lib/site-packages/hf_xet/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "c5a6a4991595d970aca049daab9f917a59094106693039eccce953f6ad2a6721", "size": 2462378, "subdir": "win-64", "timestamp": 1750541236000, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/win-64/hf-xet-1.1.5-py39h17685eb_3.conda", "version": "1.1.5"}